version: '3.3'

# WSL环境下的简化Docker Compose配置
# 兼容旧版本docker-compose

services:
  # Redis - 内存数据库
  redis:
    image: redis:7-alpine
    container_name: financial-redis-wsl
    ports:
      - "6379:6379"
    volumes:
      - redis_data_wsl:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    restart: unless-stopped

  # ClickHouse - 时序数据库 (轻量配置)
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: financial-clickhouse-wsl
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse_data_wsl:/var/lib/clickhouse
    environment:
      CLICKHOUSE_DB: financial_data
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: password123
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    restart: unless-stopped

  # MinIO - 对象存储 (可选)
  minio:
    image: minio/minio:latest
    container_name: financial-minio-wsl
    ports:
      - "9001:9001"
      - "9002:9002"
    volumes:
      - minio_data_wsl:/data
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password123
    command: server /data --console-address ":9001" --address ":9002"
    restart: unless-stopped

volumes:
  redis_data_wsl:
    driver: local
  clickhouse_data_wsl:
    driver: local
  minio_data_wsl:
    driver: local

networks:
  default:
    driver: bridge