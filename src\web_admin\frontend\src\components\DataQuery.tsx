import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Table,
  message,
  Space,
  Statistic,
  Row,
  Col,
  Tag,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  FileTextOutlined,
  FileExcelOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import axios from 'axios';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

interface QueryResult {
  timestamp: string;
  symbol: string;
  last_price: number;
  volume: number;
  turnover: number;
}

const DataQuery: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [queryResults, setQueryResults] = useState<QueryResult[]>([]);
  const [queryStats, setQueryStats] = useState({
    total: 0,
    queryTime: 0,
    dataSize: 0
  });

  const handleQuery = async (values: any) => {
    setLoading(true);
    try {
      const startTime = Date.now();
      
      const params = {
        symbol: values.symbol,
        start_time: values.timeRange[0].toISOString(),
        end_time: values.timeRange[1].toISOString(),
        data_type: values.dataType,
        limit: values.limit || 1000
      };

      const response = await axios.get('/api/data/query', { params });
      
      const queryTime = Date.now() - startTime;
      
      setQueryResults(response.data.data);
      setQueryStats({
        total: response.data.total,
        queryTime,
        dataSize: JSON.stringify(response.data.data).length
      });
      
      message.success(`查询完成，共找到 ${response.data.total} 条记录`);
    } catch (error) {
      message.error('查询失败');
      console.error('Error querying data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: string) => {
    if (queryResults.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    try {
      const values = form.getFieldsValue();
      const params = {
        symbol: values.symbol,
        start_time: values.timeRange[0].toISOString(),
        end_time: values.timeRange[1].toISOString(),
        data_type: values.dataType,
        format,
        limit: 10000 // 导出时增加限制
      };

      const response = await axios.get('/api/data/query', {
        params,
        responseType: format === 'csv' ? 'blob' : 'json'
      });

      if (format === 'csv') {
        // 下载CSV文件
        const blob = new Blob([response.data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${values.symbol}_${values.dataType}_${dayjs().format('YYYYMMDD_HHmmss')}.csv`;
        link.click();
        window.URL.revokeObjectURL(url);
      } else {
        // 下载JSON文件
        const dataStr = JSON.stringify(response.data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${values.symbol}_${values.dataType}_${dayjs().format('YYYYMMDD_HHmmss')}.json`;
        link.click();
        window.URL.revokeObjectURL(url);
      }

      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
      console.error('Error exporting data:', error);
    }
  };

  const columns = [
    {
      title: '时间戳',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: string) => dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss.SSS'),
      width: 180
    },
    {
      title: '合约代码',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (symbol: string) => <Tag color="blue">{symbol}</Tag>,
      width: 120
    },
    {
      title: '最新价',
      dataIndex: 'last_price',
      key: 'last_price',
      render: (price: number) => price.toFixed(2),
      align: 'right' as const,
      width: 100
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      render: (volume: number) => volume.toLocaleString(),
      align: 'right' as const,
      width: 100
    },
    {
      title: '成交额',
      dataIndex: 'turnover',
      key: 'turnover',
      render: (turnover: number) => turnover.toLocaleString(),
      align: 'right' as const,
      width: 120
    }
  ];

  const symbolOptions = [
    { value: 'CU2409', label: 'CU2409 - 沪铜2409' },
    { value: 'AL2409', label: 'AL2409 - 沪铝2409' },
    { value: 'ZN2409', label: 'ZN2409 - 沪锌2409' },
    { value: 'AU2412', label: 'AU2412 - 沪金2412' },
    { value: 'AG2412', label: 'AG2412 - 沪银2412' },
    { value: 'RB2410', label: 'RB2410 - 螺纹钢2410' },
    { value: 'I2409', label: 'I2409 - 铁矿石2409' },
    { value: 'J2409', label: 'J2409 - 焦炭2409' }
  ];

  const dataTypeOptions = [
    { value: 'tick', label: 'Tick数据' },
    { value: 'kline_1m', label: '1分钟K线' },
    { value: 'kline_5m', label: '5分钟K线' },
    { value: 'kline_15m', label: '15分钟K线' },
    { value: 'kline_1h', label: '1小时K线' },
    { value: 'kline_1d', label: '日K线' },
    { value: 'level2', label: 'Level2深度数据' }
  ];

  return (
    <div>
      <Card title="数据查询" style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleQuery}
          initialValues={{
            dataType: 'tick',
            timeRange: [dayjs().subtract(1, 'hour'), dayjs()],
            limit: 1000
          }}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="symbol"
                label="合约代码"
                rules={[{ required: true, message: '请选择合约代码' }]}
              >
                <Select
                  placeholder="请选择合约代码"
                  options={symbolOptions}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="dataType"
                label="数据类型"
                rules={[{ required: true, message: '请选择数据类型' }]}
              >
                <Select placeholder="请选择数据类型" options={dataTypeOptions} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="timeRange"
                label="时间范围"
                rules={[{ required: true, message: '请选择时间范围' }]}
              >
                <RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="limit" label="查询限制">
                <Select placeholder="查询限制" defaultValue={1000}>
                  <Select.Option value={100}>100条</Select.Option>
                  <Select.Option value={500}>500条</Select.Option>
                  <Select.Option value={1000}>1000条</Select.Option>
                  <Select.Option value={5000}>5000条</Select.Option>
                  <Select.Option value={10000}>10000条</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
                loading={loading}
              >
                查询数据
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={() => handleExport('json')}
                disabled={queryResults.length === 0}
              >
                导出JSON
              </Button>
              <Button
                icon={<FileExcelOutlined />}
                onClick={() => handleExport('csv')}
                disabled={queryResults.length === 0}
              >
                导出CSV
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {queryResults.length > 0 && (
        <>
          <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="查询结果"
                  value={queryStats.total}
                  suffix="条"
                  prefix={<DatabaseOutlined />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="查询耗时"
                  value={queryStats.queryTime}
                  suffix="ms"
                  valueStyle={{ color: queryStats.queryTime < 1000 ? '#52c41a' : '#faad14' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="数据大小"
                  value={(queryStats.dataSize / 1024).toFixed(1)}
                  suffix="KB"
                  prefix={<FileTextOutlined />}
                />
              </Card>
            </Col>
          </Row>

          <Card title="查询结果">
            <Table
              columns={columns}
              dataSource={queryResults}
              rowKey={(record, index) => `${record.symbol}_${record.timestamp}_${index}`}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
                pageSizeOptions: ['10', '20', '50', '100']
              }}
              scroll={{ x: 800 }}
              size="small"
            />
          </Card>
        </>
      )}

      {queryResults.length === 0 && !loading && (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <DatabaseOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <p>请输入查询条件并点击"查询数据"按钮</p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default DataQuery;