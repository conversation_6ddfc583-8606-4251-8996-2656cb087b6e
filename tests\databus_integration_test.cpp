#include <gtest/gtest.h>
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>

#include "../src/databus/data_bus.h"
#include "../src/proto/data_types.h"

using namespace financial_data;
using namespace financial_data::databus;

class DataBusIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建默认配置的数据总线（禁用Kafka以简化测试）
        auto config = DataBusConfig{};
        config.enable_kafka = false;  // 禁用Kafka
        config.worker_thread_count = 2;
        config.enable_backpressure = true;
        config.enable_batching = true;
        
        data_bus_ = std::make_unique<DataBus>(config);
        ASSERT_TRUE(data_bus_->Start());
    }
    
    void TearDown() override {
        if (data_bus_) {
            data_bus_->Stop();
        }
    }
    
    StandardTick CreateTestTick(const std::string& symbol = "CU2409", 
                               const std::string& exchange = "SHFE",
                               uint32_t sequence = 0) {
        StandardTick tick;
        tick.timestamp_ns = StandardTick::GetCurrentTimestampNs();
        tick.symbol = symbol;
        tick.exchange = exchange;
        tick.last_price = 78560.0 + sequence * 10.0;
        tick.volume = 1000 + sequence;
        tick.turnover = tick.last_price * tick.volume;
        tick.sequence = sequence;
        tick.trade_flag = "buy_open";
        
        // 设置买卖盘数据
        for (int i = 0; i < 5; ++i) {
            tick.bids[i] = PriceLevel(tick.last_price - (i + 1) * 10.0, 100 + i * 10, i + 1);
            tick.asks[i] = PriceLevel(tick.last_price + (i + 1) * 10.0, 100 + i * 10, i + 1);
        }
        
        return tick;
    }
    
    Level2Data CreateTestLevel2(const std::string& symbol = "AL2409", 
                               const std::string& exchange = "SHFE",
                               uint32_t sequence = 0) {
        Level2Data level2;
        level2.timestamp_ns = StandardTick::GetCurrentTimestampNs();
        level2.symbol = symbol;
        level2.exchange = exchange;
        level2.sequence = sequence;
        
        // 添加10档买卖盘数据
        for (int i = 0; i < 10; ++i) {
            level2.bids.emplace_back(78000.0 - i * 10.0, 100 + i * 10, i + 1);
            level2.asks.emplace_back(78100.0 + i * 10.0, 100 + i * 10, i + 1);
        }
        
        return level2;
    }
    
    std::unique_ptr<DataBus> data_bus_;
};

/**
 * @brief 测试基本的数据推送和接收功能
 */
TEST_F(DataBusIntegrationTest, BasicDataPushAndReceive) {
    std::atomic<int> tick_count{0};
    std::atomic<int> level2_count{0};
    std::vector<StandardTick> received_ticks;
    std::vector<Level2Data> received_level2;
    std::mutex data_mutex;
    
    // 注册客户端
    std::string client_id = "test_client";
    bool registered = data_bus_->RegisterClient(
        client_id,
        "integration_test",
        [&](const MarketDataWrapper& data) -> bool {
            std::lock_guard<std::mutex> lock(data_mutex);
            
            if (data.type == MarketDataWrapper::DataType::TICK) {
                received_ticks.push_back(data.tick_data);
                tick_count.fetch_add(1);
            } else if (data.type == MarketDataWrapper::DataType::LEVEL2) {
                received_level2.push_back(data.level2_data);
                level2_count.fetch_add(1);
            }
            
            return true;
        }
    );
    
    ASSERT_TRUE(registered);
    
    // 订阅合约
    ASSERT_TRUE(data_bus_->Subscribe(client_id, {"CU2409", "AL2409"}, {"SHFE"}));
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 发送测试数据
    const int test_tick_count = 10;
    const int test_level2_count = 5;
    
    for (int i = 0; i < test_tick_count; ++i) {
        StandardTick tick = CreateTestTick("CU2409", "SHFE", i);
        ASSERT_TRUE(data_bus_->PushTick(tick));
    }
    
    for (int i = 0; i < test_level2_count; ++i) {
        Level2Data level2 = CreateTestLevel2("AL2409", "SHFE", i);
        ASSERT_TRUE(data_bus_->PushLevel2(level2));
    }
    
    // 等待数据处理完成
    auto timeout = std::chrono::steady_clock::now() + std::chrono::seconds(5);
    while ((tick_count.load() < test_tick_count || level2_count.load() < test_level2_count) &&
           std::chrono::steady_clock::now() < timeout) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 验证结果
    EXPECT_EQ(tick_count.load(), test_tick_count);
    EXPECT_EQ(level2_count.load(), test_level2_count);
    
    {
        std::lock_guard<std::mutex> lock(data_mutex);
        EXPECT_EQ(received_ticks.size(), test_tick_count);
        EXPECT_EQ(received_level2.size(), test_level2_count);
        
        // 验证数据完整性
        for (int i = 0; i < test_tick_count; ++i) {
            EXPECT_EQ(received_ticks[i].symbol, "CU2409");
            EXPECT_EQ(received_ticks[i].exchange, "SHFE");
            EXPECT_EQ(received_ticks[i].sequence, i);
        }
        
        for (int i = 0; i < test_level2_count; ++i) {
            EXPECT_EQ(received_level2[i].symbol, "AL2409");
            EXPECT_EQ(received_level2[i].exchange, "SHFE");
            EXPECT_EQ(received_level2[i].sequence, i);
        }
    }
    
    // 清理
    data_bus_->UnregisterClient(client_id);
}

/**
 * @brief 测试多客户端订阅不同合约
 */
TEST_F(DataBusIntegrationTest, MultiClientDifferentSubscriptions) {
    std::atomic<int> cu_messages{0};
    std::atomic<int> al_messages{0};
    std::atomic<int> all_messages{0};
    
    // 注册CU合约专用客户端
    std::string cu_client = "cu_client";
    ASSERT_TRUE(data_bus_->RegisterClient(
        cu_client,
        "test",
        [&](const MarketDataWrapper& data) -> bool {
            if (data.type == MarketDataWrapper::DataType::TICK && 
                data.tick_data.symbol == "CU2409") {
                cu_messages.fetch_add(1);
            }
            return true;
        }
    ));
    ASSERT_TRUE(data_bus_->Subscribe(cu_client, {"CU2409"}, {}));
    
    // 注册AL合约专用客户端
    std::string al_client = "al_client";
    ASSERT_TRUE(data_bus_->RegisterClient(
        al_client,
        "test",
        [&](const MarketDataWrapper& data) -> bool {
            if (data.type == MarketDataWrapper::DataType::LEVEL2 && 
                data.level2_data.symbol == "AL2409") {
                al_messages.fetch_add(1);
            }
            return true;
        }
    ));
    ASSERT_TRUE(data_bus_->Subscribe(al_client, {"AL2409"}, {}));
    
    // 注册全市场客户端
    std::string all_client = "all_client";
    ASSERT_TRUE(data_bus_->RegisterClient(
        all_client,
        "test",
        [&](const MarketDataWrapper& data) -> bool {
            all_messages.fetch_add(1);
            return true;
        }
    ));
    ASSERT_TRUE(data_bus_->Subscribe(all_client, {"CU2409", "AL2409"}, {}));
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 发送测试数据
    const int cu_count = 5;
    const int al_count = 3;
    
    for (int i = 0; i < cu_count; ++i) {
        StandardTick tick = CreateTestTick("CU2409", "SHFE", i);
        ASSERT_TRUE(data_bus_->PushTick(tick));
    }
    
    for (int i = 0; i < al_count; ++i) {
        Level2Data level2 = CreateTestLevel2("AL2409", "SHFE", i);
        ASSERT_TRUE(data_bus_->PushLevel2(level2));
    }
    
    // 等待数据处理完成
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    // 验证结果
    EXPECT_EQ(cu_messages.load(), cu_count) << "CU client should receive only CU messages";
    EXPECT_EQ(al_messages.load(), al_count) << "AL client should receive only AL messages";
    EXPECT_EQ(all_messages.load(), cu_count + al_count) << "All client should receive all messages";
    
    // 清理
    data_bus_->UnregisterClient(cu_client);
    data_bus_->UnregisterClient(al_client);
    data_bus_->UnregisterClient(all_client);
}

/**
 * @brief 测试路由规则功能
 */
TEST_F(DataBusIntegrationTest, RoutingRules) {
    std::atomic<int> high_price_messages{0};
    std::atomic<int> all_messages{0};
    
    // 注册高价格消息客户端
    std::string high_price_client = "high_price_client";
    ASSERT_TRUE(data_bus_->RegisterClient(
        high_price_client,
        "test",
        [&](const MarketDataWrapper& data) -> bool {
            high_price_messages.fetch_add(1);
            return true;
        }
    ));
    ASSERT_TRUE(data_bus_->Subscribe(high_price_client, {"CU2409"}, {}));
    
    // 注册全消息客户端
    std::string all_client = "all_client";
    ASSERT_TRUE(data_bus_->RegisterClient(
        all_client,
        "test",
        [&](const MarketDataWrapper& data) -> bool {
            all_messages.fetch_add(1);
            return true;
        }
    ));
    ASSERT_TRUE(data_bus_->Subscribe(all_client, {"CU2409"}, {}));
    
    // 添加路由规则：只有价格大于80000的消息才发送给高价格客户端
    RoutingRule high_price_rule;
    high_price_rule.rule_id = "high_price_filter";
    high_price_rule.symbol_pattern = "CU2409";
    high_price_rule.exchange_pattern = "*";
    high_price_rule.client_pattern = "high_price_*";
    high_price_rule.min_price = 80000.0;
    high_price_rule.priority = 100;
    high_price_rule.enabled = true;
    
    ASSERT_TRUE(data_bus_->AddRoutingRule(high_price_rule));
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 发送测试数据
    const int total_messages = 10;
    int expected_high_price = 0;
    
    for (int i = 0; i < total_messages; ++i) {
        StandardTick tick = CreateTestTick("CU2409", "SHFE", i);
        tick.last_price = 79000.0 + i * 500.0;  // 价格从79000到83500
        
        if (tick.last_price >= 80000.0) {
            expected_high_price++;
        }
        
        ASSERT_TRUE(data_bus_->PushTick(tick));
    }
    
    // 等待数据处理完成
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    // 验证结果
    EXPECT_EQ(all_messages.load(), total_messages) << "All client should receive all messages";
    EXPECT_EQ(high_price_messages.load(), expected_high_price) 
        << "High price client should only receive high price messages";
    
    // 清理
    data_bus_->RemoveRoutingRule("high_price_filter");
    data_bus_->UnregisterClient(high_price_client);
    data_bus_->UnregisterClient(all_client);
}

/**
 * @brief 测试客户端心跳和超时清理
 */
TEST_F(DataBusIntegrationTest, ClientHeartbeatAndTimeout) {
    // 注册测试客户端
    std::string client_id = "heartbeat_test_client";
    ASSERT_TRUE(data_bus_->RegisterClient(
        client_id,
        "test",
        [](const MarketDataWrapper& data) -> bool { return true; }
    ));
    
    // 验证客户端已注册
    auto active_clients = data_bus_->GetActiveClients();
    EXPECT_TRUE(std::find(active_clients.begin(), active_clients.end(), client_id) != active_clients.end());
    
    // 发送心跳
    data_bus_->ClientHeartbeat(client_id);
    
    // 获取客户端信息
    auto client_info = data_bus_->GetClientInfo(client_id);
    ASSERT_NE(client_info, nullptr);
    EXPECT_TRUE(client_info->active.load());
    
    // 验证心跳时间更新
    int64_t first_heartbeat = client_info->last_heartbeat_ns;
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    data_bus_->ClientHeartbeat(client_id);
    EXPECT_GT(client_info->last_heartbeat_ns, first_heartbeat);
    
    // 清理
    data_bus_->UnregisterClient(client_id);
    
    // 验证客户端已注销
    active_clients = data_bus_->GetActiveClients();
    EXPECT_TRUE(std::find(active_clients.begin(), active_clients.end(), client_id) == active_clients.end());
}

/**
 * @brief 测试系统统计信息
 */
TEST_F(DataBusIntegrationTest, SystemStatistics) {
    // 重置统计信息
    data_bus_->ResetStatistics();
    
    // 注册测试客户端
    std::string client_id = "stats_test_client";
    ASSERT_TRUE(data_bus_->RegisterClient(
        client_id,
        "test",
        [](const MarketDataWrapper& data) -> bool { return true; }
    ));
    ASSERT_TRUE(data_bus_->Subscribe(client_id, {"CU2409"}, {"SHFE"}));
    
    // 发送测试数据
    const int message_count = 100;
    for (int i = 0; i < message_count; ++i) {
        StandardTick tick = CreateTestTick("CU2409", "SHFE", i);
        ASSERT_TRUE(data_bus_->PushTick(tick));
    }
    
    // 等待数据处理完成
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    // 获取统计信息
    auto stats = data_bus_->GetStatistics();
    
    EXPECT_EQ(stats.total_messages_received.load(), message_count);
    EXPECT_GT(stats.total_messages_processed.load(), 0);
    EXPECT_GT(stats.total_messages_sent.load(), 0);
    EXPECT_EQ(stats.total_messages_dropped.load(), 0);  // 没有背压，不应该有丢弃
    
    // 获取队列状态
    auto queue_status = data_bus_->GetQueueStatus();
    EXPECT_GE(queue_status.input_queue_usage, 0.0);
    EXPECT_LE(queue_status.input_queue_usage, 100.0);
    
    // 获取路由统计
    auto routing_stats = data_bus_->GetRoutingStatistics();
    EXPECT_GT(routing_stats.total_messages_routed.load(), 0);
    
    // 获取背压统计
    auto bp_stats = data_bus_->GetBackpressureStatistics();
    EXPECT_EQ(bp_stats.current_state, BackpressureState::NORMAL);
    
    // 清理
    data_bus_->UnregisterClient(client_id);
}

/**
 * @brief 测试健康检查功能
 */
TEST_F(DataBusIntegrationTest, HealthCheck) {
    // 获取健康状态
    auto health = data_bus_->GetHealthStatus();
    
    EXPECT_TRUE(health.overall_healthy) << "System should be healthy initially";
    EXPECT_TRUE(health.queues_healthy) << "Queues should be healthy";
    EXPECT_TRUE(health.kafka_healthy) << "Kafka should be healthy (disabled)";
    EXPECT_TRUE(health.router_healthy) << "Router should be healthy";
    EXPECT_TRUE(health.backpressure_healthy) << "Backpressure should be healthy";
    
    if (!health.overall_healthy) {
        std::cout << "Health check error: " << health.error_message << std::endl;
    }
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}