#pragma once

#include <string>
#include <memory>
#include <functional>
#include <vector>
#include <mutex>
#include <atomic>
#include <unordered_map>

// CTP API头文件
#include "ctp_headers.h"

namespace financial_data {

// CTP API包装器，用于封装CTP原生API
class CTPApiWrapper : public CThostFtdcMdSpi {
private:
    CThostFtdcMdApi* md_api_;
    std::string flow_path_;
    std::atomic<bool> connected_;
    std::atomic<bool> logged_in_;
    std::atomic<int> request_id_;
    
    // 回调函数类型定义
    using ConnectedCallback = std::function<void()>;
    using DisconnectedCallback = std::function<void(int reason)>;
    using LoginCallback = std::function<void(bool success, const std::string& error_msg)>;
    using MarketDataCallback = std::function<void(CThostFtdcDepthMarketDataField* data)>;
    using ErrorCallback = std::function<void(int error_id, const std::string& error_msg)>;
    
    // 回调函数
    ConnectedCallback on_connected_;
    DisconnectedCallback on_disconnected_;
    LoginCallback on_login_;
    MarketDataCallback on_market_data_;
    ErrorCallback on_error_;
    
    // 订阅状态管理
    std::unordered_map<std::string, bool> subscription_status_;
    std::mutex subscription_mutex_;

public:
    CTPApiWrapper();
    ~CTPApiWrapper();
    
    // 禁用拷贝和移动
    CTPApiWrapper(const CTPApiWrapper&) = delete;
    CTPApiWrapper& operator=(const CTPApiWrapper&) = delete;
    
    // 初始化和连接
    bool Initialize(const std::string& flow_path);
    bool RegisterFront(const std::string& front_address);
    void Init();
    void Release();
    
    // 登录
    bool Login(const std::string& broker_id, const std::string& user_id, const std::string& password);
    void Logout();
    
    // 订阅管理
    bool SubscribeMarketData(const std::vector<std::string>& symbols);
    bool UnsubscribeMarketData(const std::vector<std::string>& symbols);
    
    // 状态查询
    bool IsConnected() const { return connected_.load(); }
    bool IsLoggedIn() const { return logged_in_.load(); }
    
    // 回调设置
    void SetConnectedCallback(const ConnectedCallback& callback) { on_connected_ = callback; }
    void SetDisconnectedCallback(const DisconnectedCallback& callback) { on_disconnected_ = callback; }
    void SetLoginCallback(const LoginCallback& callback) { on_login_ = callback; }
    void SetMarketDataCallback(const MarketDataCallback& callback) { on_market_data_ = callback; }
    void SetErrorCallback(const ErrorCallback& callback) { on_error_ = callback; }

protected:
    // CTP API回调方法重写
    void OnFrontConnected() override;
    void OnFrontDisconnected(int nReason) override;
    void OnRspUserLogin(CThostFtdcRspUserLoginField* pRspUserLogin, 
                       CThostFtdcRspInfoField* pRspInfo, 
                       int nRequestID, 
                       bool bIsLast) override;
    void OnRspUserLogout(CThostFtdcUserLogoutField* pUserLogout, 
                        CThostFtdcRspInfoField* pRspInfo, 
                        int nRequestID, 
                        bool bIsLast) override;
    void OnRspSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, 
                           CThostFtdcRspInfoField* pRspInfo, 
                           int nRequestID, 
                           bool bIsLast) override;
    void OnRspUnSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, 
                             CThostFtdcRspInfoField* pRspInfo, 
                             int nRequestID, 
                             bool bIsLast) override;
    void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* pDepthMarketData) override;
    void OnRspError(CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) override;

private:
    int GetNextRequestId() { return ++request_id_; }
    bool IsErrorRspInfo(CThostFtdcRspInfoField* pRspInfo);
    std::string GetErrorMessage(CThostFtdcRspInfoField* pRspInfo);
};

} // namespace financial_data