#!/bin/bash

echo "检查开发环境服务状态..."
echo

echo "========================================"
echo "1. Docker 容器状态"
echo "========================================"
docker-compose -f docker-compose.dev.yml ps

echo
echo "========================================"
echo "2. Redis 连接测试"
echo "========================================"
if docker exec financial-redis-dev redis-cli ping >/dev/null 2>&1; then
    echo "Redis: 连接正常 ✓"
else
    echo "Redis: 连接失败 ✗"
fi

echo
echo "========================================"
echo "3. ClickHouse 连接测试"
echo "========================================"
if curl -s "http://localhost:8123/?query=SELECT version()" >/dev/null 2>&1; then
    echo "ClickHouse: 连接正常 ✓"
    echo "数据库列表:"
    curl -s "http://localhost:8123/?query=SHOW DATABASES"
else
    echo "ClickHouse: 连接失败 ✗"
fi

echo
echo "========================================"
echo "4. Kafka 连接测试"
echo "========================================"
if docker exec financial-kafka-dev kafka-topics --bootstrap-server localhost:9092 --list >/dev/null 2>&1; then
    echo "Kafka: 连接正常 ✓"
else
    echo "Kafka: 连接失败 ✗"
fi

echo
echo "========================================"
echo "5. MinIO 连接测试"
echo "========================================"
if curl -s -I http://localhost:9002/minio/health/live 2>/dev/null | grep -q "200 OK"; then
    echo "MinIO: 连接正常 ✓"
else
    echo "MinIO: 连接失败 ✗"
fi

echo
echo "========================================"
echo "服务访问地址:"
echo "========================================"
echo "- Redis:      localhost:6379"
echo "- ClickHouse: http://localhost:8123"
echo "- Kafka:      localhost:9092"
echo "- MinIO:      http://localhost:9001"
echo