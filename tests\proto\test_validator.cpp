#include <gtest/gtest.h>
#include "proto/validator.h"
#include "proto/data_types.h"
#include <chrono>

using namespace financial_data;

class ValidatorTest : public ::testing::Test {
protected:
    void SetUp() override {
        validator_ = std::make_unique<DataValidator>();
        
        // 设置测试配置
        PriceAnomalyConfig price_config;
        price_config.max_price_change_ratio = 0.1;
        price_config.min_price = 0.01;
        price_config.max_price = 10000.0;
        price_config.max_spread_ratio = 0.05;
        validator_->SetPriceConfig(price_config);
        
        TimestampConfig timestamp_config;
        timestamp_config.max_future_offset_ns = 1000000000LL; // 1秒
        timestamp_config.max_past_offset_ns = 86400000000000LL; // 1天
        validator_->SetTimestampConfig(timestamp_config);
        
        SequenceConfig sequence_config;
        sequence_config.max_sequence_gap = 100;
        sequence_config.allow_sequence_reset = true;
        validator_->SetSequenceConfig(sequence_config);
    }
    
    StandardTick CreateValidTick() {
        StandardTick tick;
        tick.symbol = "AAPL";
        tick.exchange = "NASDAQ";
        tick.timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();
        tick.last_price = 150.0;
        tick.volume = 1000;
        tick.turnover = 150000.0;
        tick.sequence = 1;
        tick.trade_flag = "0";
        return tick;
    }
    
    Level2Data CreateValidLevel2() {
        Level2Data level2;
        level2.symbol = "AAPL";
        level2.exchange = "NASDAQ";
        level2.timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();
        level2.sequence = 1;
        
        // 添加买盘档位
        level2.bids.emplace_back(149.95, 100, 5);
        level2.bids.emplace_back(149.90, 200, 8);
        level2.bids.emplace_back(149.85, 150, 3);
        
        // 添加卖盘档位
        level2.asks.emplace_back(150.05, 120, 6);
        level2.asks.emplace_back(150.10, 180, 4);
        level2.asks.emplace_back(150.15, 90, 2);
        
        return level2;
    }
    
    std::unique_ptr<DataValidator> validator_;
};

// 测试有效的Tick数据验证
TEST_F(ValidatorTest, ValidTickData) {
    StandardTick tick = CreateValidTick();
    ValidationResult result = validator_->ValidateTick(tick);
    EXPECT_EQ(result, ValidationResult::VALID);
}

// 测试无效符号
TEST_F(ValidatorTest, InvalidSymbol) {
    StandardTick tick = CreateValidTick();
    tick.symbol = "";
    ValidationResult result = validator_->ValidateTick(tick);
    EXPECT_EQ(result, ValidationResult::INVALID_SYMBOL);
}

// 测试无效时间戳
TEST_F(ValidatorTest, InvalidTimestamp) {
    StandardTick tick = CreateValidTick();
    tick.timestamp_ns = 0;
    ValidationResult result = validator_->ValidateTick(tick);
    EXPECT_EQ(result, ValidationResult::INVALID_TIMESTAMP);
}

// 测试无效价格
TEST_F(ValidatorTest, InvalidPrice) {
    StandardTick tick = CreateValidTick();
    tick.last_price = 0.0;
    ValidationResult result = validator_->ValidateTick(tick);
    EXPECT_EQ(result, ValidationResult::INVALID_PRICE);
    
    tick.last_price = -10.0;
    result = validator_->ValidateTick(tick);
    EXPECT_EQ(result, ValidationResult::INVALID_PRICE);
}

// 测试价格范围检查
TEST_F(ValidatorTest, PriceRangeCheck) {
    StandardTick tick = CreateValidTick();
    
    // 价格过低
    tick.last_price = 0.005;
    ValidationResult result = validator_->ValidateTick(tick);
    EXPECT_EQ(result, ValidationResult::INVALID_PRICE);
    
    // 价格过高
    tick.last_price = 20000.0;
    result = validator_->ValidateTick(tick);
    EXPECT_EQ(result, ValidationResult::INVALID_PRICE);
}

// 测试价格异常检测
TEST_F(ValidatorTest, PriceAnomalyDetection) {
    StandardTick tick1 = CreateValidTick();
    tick1.last_price = 100.0;
    tick1.sequence = 1;
    
    // 第一个数据应该通过
    ValidationResult result = validator_->ValidateTick(tick1);
    EXPECT_EQ(result, ValidationResult::VALID);
    
    // 价格跳跃过大
    StandardTick tick2 = CreateValidTick();
    tick2.last_price = 150.0; // 50%的价格跳跃
    tick2.sequence = 2;
    
    result = validator_->ValidateTick(tick2);
    EXPECT_EQ(result, ValidationResult::PRICE_ANOMALY);
}

// 测试序列号验证
TEST_F(ValidatorTest, SequenceValidation) {
    StandardTick tick1 = CreateValidTick();
    tick1.sequence = 1;
    
    ValidationResult result = validator_->ValidateTick(tick1);
    EXPECT_EQ(result, ValidationResult::VALID);
    
    // 正常递增序列号
    StandardTick tick2 = CreateValidTick();
    tick2.sequence = 2;
    result = validator_->ValidateTick(tick2);
    EXPECT_EQ(result, ValidationResult::VALID);
    
    // 重复序列号
    StandardTick tick3 = CreateValidTick();
    tick3.sequence = 2;
    result = validator_->ValidateTick(tick3);
    EXPECT_EQ(result, ValidationResult::SEQUENCE_GAP);
    
    // 序列号间隔过大
    StandardTick tick4 = CreateValidTick();
    tick4.sequence = 200; // 间隔过大
    result = validator_->ValidateTick(tick4);
    EXPECT_EQ(result, ValidationResult::SEQUENCE_GAP);
}

// 测试Level2数据验证
TEST_F(ValidatorTest, ValidLevel2Data) {
    Level2Data level2 = CreateValidLevel2();
    ValidationResult result = validator_->ValidateLevel2(level2);
    EXPECT_EQ(result, ValidationResult::VALID);
}

// 测试Level2买卖价差验证
TEST_F(ValidatorTest, Level2SpreadValidation) {
    Level2Data level2 = CreateValidLevel2();
    
    // 买价高于卖价（异常情况）
    level2.bids[0].price = 150.10;
    level2.asks[0].price = 150.05;
    
    ValidationResult result = validator_->ValidateLevel2(level2);
    EXPECT_EQ(result, ValidationResult::PRICE_ANOMALY);
}

// 测试Level2价格顺序验证
TEST_F(ValidatorTest, Level2PriceOrderValidation) {
    Level2Data level2 = CreateValidLevel2();
    
    // 买盘价格顺序错误（应该递减）
    level2.bids[1].price = 150.00; // 比第一档更高
    
    ValidationResult result = validator_->ValidateLevel2(level2);
    EXPECT_EQ(result, ValidationResult::INVALID_PRICE);
}

// 测试数据质量监控器
TEST_F(ValidatorTest, DataQualityMonitor) {
    DataQualityMonitor monitor;
    
    // 测试有效数据
    StandardTick valid_tick = CreateValidTick();
    MarketDataWrapper wrapper(valid_tick);
    
    ValidationResult result = monitor.MonitorData(wrapper);
    EXPECT_EQ(result, ValidationResult::VALID);
    
    auto metrics = monitor.GetGlobalMetrics();
    EXPECT_EQ(metrics.total_records, 1);
    EXPECT_EQ(metrics.valid_records, 1);
    EXPECT_DOUBLE_EQ(metrics.GetValidityRate(), 1.0);
    
    // 测试无效数据
    StandardTick invalid_tick = CreateValidTick();
    invalid_tick.symbol = "";
    MarketDataWrapper invalid_wrapper(invalid_tick);
    
    result = monitor.MonitorData(invalid_wrapper);
    EXPECT_EQ(result, ValidationResult::INVALID_SYMBOL);
    
    metrics = monitor.GetGlobalMetrics();
    EXPECT_EQ(metrics.total_records, 2);
    EXPECT_EQ(metrics.valid_records, 1);
    EXPECT_DOUBLE_EQ(metrics.GetValidityRate(), 0.5);
}

// 测试数据完整性检查器
TEST_F(ValidatorTest, DataIntegrityChecker) {
    DataIntegrityChecker checker;
    
    StandardTick tick = CreateValidTick();
    EXPECT_TRUE(checker.CheckIntegrity(tick));
    
    // 测试字段完整性
    tick.symbol = "";
    EXPECT_FALSE(checker.CheckFieldCompleteness(tick));
    
    // 测试价格范围
    tick = CreateValidTick();
    checker.SetPriceRange("AAPL", 100.0, 200.0);
    EXPECT_TRUE(checker.CheckValueRanges(tick));
    
    tick.last_price = 50.0; // 超出范围
    EXPECT_FALSE(checker.CheckValueRanges(tick));
    
    // 测试逻辑一致性
    tick = CreateValidTick();
    tick.turnover = tick.last_price * tick.volume * 2; // 不一致的成交额
    EXPECT_FALSE(checker.CheckLogicalConsistency(tick));
}

// 测试批量数据验证
TEST_F(ValidatorTest, BatchValidation) {
    MarketDataBatch batch;
    
    // 添加有效数据
    StandardTick tick1 = CreateValidTick();
    tick1.sequence = 1;
    batch.AddTick(tick1);
    
    StandardTick tick2 = CreateValidTick();
    tick2.sequence = 2;
    batch.AddTick(tick2);
    
    // 添加无效数据
    StandardTick tick3 = CreateValidTick();
    tick3.symbol = "";
    batch.AddTick(tick3);
    
    std::vector<ValidationResult> results = validator_->ValidateBatch(batch);
    
    EXPECT_EQ(results.size(), 3);
    EXPECT_EQ(results[0], ValidationResult::VALID);
    EXPECT_EQ(results[1], ValidationResult::VALID);
    EXPECT_EQ(results[2], ValidationResult::INVALID_SYMBOL);
}

// 测试时间戳验证
TEST_F(ValidatorTest, TimestampValidation) {
    auto now = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
    
    // 当前时间应该有效
    EXPECT_TRUE(validator_->ValidateTimestamp(now));
    
    // 未来时间（超出允许范围）
    EXPECT_FALSE(validator_->ValidateTimestamp(now + 2000000000LL)); // 2秒后
    
    // 过去时间（超出允许范围）
    EXPECT_FALSE(validator_->ValidateTimestamp(now - 90000000000000LL)); // 25小时前
}

// 测试内存池
TEST_F(ValidatorTest, MemoryPool) {
    MemoryPool<StandardTick> pool(10);
    
    EXPECT_EQ(pool.TotalSize(), 10);
    EXPECT_EQ(pool.AvailableSize(), 10);
    EXPECT_EQ(pool.UsedSize(), 0);
    
    // 获取对象
    StandardTick* tick1 = pool.Acquire();
    StandardTick* tick2 = pool.Acquire();
    
    EXPECT_NE(tick1, nullptr);
    EXPECT_NE(tick2, nullptr);
    EXPECT_NE(tick1, tick2);
    EXPECT_EQ(pool.AvailableSize(), 8);
    EXPECT_EQ(pool.UsedSize(), 2);
    
    // 释放对象
    pool.Release(tick1);
    EXPECT_EQ(pool.AvailableSize(), 9);
    EXPECT_EQ(pool.UsedSize(), 1);
    
    // 测试池扩展
    std::vector<StandardTick*> ticks;
    for (int i = 0; i < 15; ++i) {
        ticks.push_back(pool.Acquire());
    }
    
    EXPECT_EQ(pool.TotalSize(), 20); // 应该扩展到20
    
    for (auto* tick : ticks) {
        pool.Release(tick);
    }
    pool.Release(tick2);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}