{"aws_config": {"region": "us-east-1", "access_key_id": "${AWS_ACCESS_KEY_ID}", "secret_access_key": "${AWS_SECRET_ACCESS_KEY}", "bucket_name": "financial-data-backup", "storage_class": "STANDARD_IA", "encryption": {"type": "AES256", "kms_key_id": ""}}, "backup_policy": {"enable_automatic_backup": true, "backup_schedule": "0 3 * * *", "retention_policy": {"delete_after_days": 2555, "transition_to_glacier_after_days": 365, "transition_to_deep_archive_after_days": 1095}, "backup_verification": {"enable_checksum_verification": true, "enable_restore_test": false, "test_frequency_days": 30}}, "sync_settings": {"max_concurrent_uploads": 10, "multipart_threshold_mb": 100, "multipart_chunk_size_mb": 50, "retry_attempts": 3, "retry_delay_seconds": 5, "bandwidth_limit_mbps": 0}, "monitoring": {"enable_cloudwatch_metrics": true, "enable_sns_notifications": true, "sns_topic_arn": "arn:aws:sns:us-east-1:123456789012:financial-data-backup-alerts", "notification_events": ["backup_completed", "backup_failed", "restore_completed", "restore_failed"]}, "disaster_recovery": {"enable_cross_region_replication": true, "backup_regions": ["us-west-2", "eu-west-1"], "enable_versioning": true, "mfa_delete": false}}