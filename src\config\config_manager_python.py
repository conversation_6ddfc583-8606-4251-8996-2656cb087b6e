"""
Python wrapper for the C++ ConfigManager
Provides a Pythonic interface to the unified configuration management system
"""

import json
import os
import threading
import time
import hashlib
import shutil
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum
import logging
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

logger = logging.getLogger(__name__)


class ConfigChangeType(Enum):
    """配置变更事件类型"""
    ADDED = "added"
    MODIFIED = "modified"
    DELETED = "deleted"
    RELOADED = "reloaded"


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    type: ConfigChangeType
    section: Optional[str] = None
    key: Optional[str] = None
    old_value: Any = None
    new_value: Any = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class ValidationResult:
    """配置验证结果"""
    is_valid: bool = True
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
    
    def add_error(self, error: str):
        """添加错误"""
        self.is_valid = False
        self.errors.append(error)
    
    def add_warning(self, warning: str):
        """添加警告"""
        self.warnings.append(warning)


class ConfigValidator:
    """配置验证器基类"""
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        """验证配置"""
        raise NotImplementedError
    
    def get_validator_name(self) -> str:
        """获取验证器名称"""
        return self.__class__.__name__


class ConfigChangeListener:
    """配置变更监听器基类"""
    
    def on_config_changed(self, event: ConfigChangeEvent):
        """配置变更回调"""
        raise NotImplementedError


class ConfigFileWatcher(FileSystemEventHandler):
    """配置文件监控器"""
    
    def __init__(self, config_manager, config_file_path: str):
        super().__init__()
        self.config_manager = config_manager
        self.config_file_path = os.path.abspath(config_file_path)
        self.last_modified = 0
        self.debounce_delay = 0.5  # 防抖延迟500ms
        self.pending_reload = False
        self._lock = threading.Lock()
        
    def on_modified(self, event):
        """文件修改事件处理"""
        if event.is_directory:
            return
            
        # 检查是否是我们监控的配置文件
        if os.path.abspath(event.src_path) != self.config_file_path:
            return
            
        current_time = time.time()
        
        with self._lock:
            # 防抖处理：如果在短时间内多次修改，只处理最后一次
            if current_time - self.last_modified < self.debounce_delay:
                if not self.pending_reload:
                    self.pending_reload = True
                    # 延迟执行重新加载
                    threading.Timer(self.debounce_delay, self._delayed_reload).start()
                return
            
            self.last_modified = current_time
            self._reload_config()
    
    def _delayed_reload(self):
        """延迟重新加载配置"""
        with self._lock:
            self.pending_reload = False
            self._reload_config()
    
    def _reload_config(self):
        """重新加载配置"""
        try:
            logger.info(f"Detected config file change: {self.config_file_path}")
            
            # 验证文件完整性
            if not self._validate_file_integrity():
                logger.warning("Config file integrity check failed, skipping reload")
                return
            
            # 创建备份（在重新加载之前）
            backup_path = self.config_manager._create_backup()
            
            try:
                # 重新加载配置
                success = self.config_manager.load_config()
                if success:
                    logger.info("Config hot reload successful")
                else:
                    logger.error("Config hot reload failed, restoring from backup")
                    if backup_path:
                        self.config_manager._restore_from_backup(backup_path)
            except Exception as e:
                logger.error(f"Error during config hot reload: {e}")
                if backup_path:
                    self.config_manager._restore_from_backup(backup_path)
                    
        except Exception as e:
            logger.error(f"Error in config file watcher: {e}")
    
    def _validate_file_integrity(self) -> bool:
        """验证文件完整性"""
        try:
            # 检查文件是否存在且可读
            if not os.path.exists(self.config_file_path):
                return False
            
            # 尝试解析JSON以验证格式
            with open(self.config_file_path, 'r', encoding='utf-8') as f:
                json.load(f)
            
            return True
        except (json.JSONDecodeError, IOError, OSError):
            return False


class PythonConfigManager:
    """Python配置管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._config = {}
        self._config_file_path = ""
        self._validators = {}
        self._listeners = []
        self._hot_reload_enabled = False
        self._file_watcher_thread = None
        self._file_watcher_running = False
        self._watch_interval = 1.0
        self._last_file_time = 0
        self._version_history = []
        self._max_version_history = 10
        self._env_vars_enabled = False
        self._env_prefix = "MARKET_DATA_"
        self._config_lock = threading.RLock()
        
        # 增强的热更新功能
        self._file_observer = None
        self._file_watcher = None
        self._backup_enabled = True
        self._backup_dir = "config/backups"
        self._max_backups = 5
        self._reload_callbacks = []
        self._config_checksum = ""
        self._safe_reload_enabled = True
        self._stats = {
            'total_keys': 0,
            'total_sections': 0,
            'last_loaded': 0,
            'last_modified': 0,
            'change_count': 0,
            'validation_count': 0,
            'error_count': 0
        }
        
        self._initialized = True
    
    def initialize(self, config_file_path: str) -> bool:
        """初始化配置管理器"""
        try:
            with self._config_lock:
                self._config_file_path = config_file_path
                self._stats = {
                    'total_keys': 0,
                    'total_sections': 0,
                    'last_loaded': 0,
                    'last_modified': 0,
                    'change_count': 0,
                    'validation_count': 0,
                    'error_count': 0
                }
                return self.load_config()
        except Exception as e:
            logger.error(f"Failed to initialize config manager: {e}")
            return False
    
    def shutdown(self):
        """关闭配置管理器"""
        self._stop_enhanced_file_watcher()
        self.stop_file_watcher()
        with self._config_lock:
            self._listeners.clear()
            self._validators.clear()
            self._version_history.clear()
            self._reload_callbacks.clear()
    
    def load_config(self) -> bool:
        """加载配置"""
        return self.load_from_file(self._config_file_path)
    
    def save_config(self) -> bool:
        """保存配置"""
        return self.save_to_file(self._config_file_path)
    
    def load_from_file(self, file_path: str) -> bool:
        """从文件加载配置"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"Config file not found: {file_path}")
                self._notify_reload_callbacks(False, f"Config file not found: {file_path}")
                return False
            
            # 检查文件校验和，避免重复加载
            new_checksum = self._calculate_config_checksum()
            if (hasattr(self, '_config_checksum') and 
                new_checksum == self._config_checksum and 
                new_checksum and 
                self._config_checksum):
                logger.debug("Config file unchanged, skipping reload")
                return True
            
            with open(file_path, 'r', encoding='utf-8') as f:
                new_config = json.load(f)
            
            # 处理环境变量
            if self._env_vars_enabled:
                new_config = self._process_environment_variables(new_config)
            
            # 安全重载：先验证新配置
            if self._safe_reload_enabled:
                temp_config = self._config
                self._config = new_config
                
                validation_result = self.validate_config()
                
                # 恢复原配置用于验证
                self._config = temp_config
                
                if not validation_result.is_valid:
                    error_msg = f"Config validation failed: {validation_result.errors}"
                    logger.error(error_msg)
                    self._stats['error_count'] += 1
                    self._notify_reload_callbacks(False, error_msg)
                    return False
            else:
                # 非安全模式：加载后验证
                validation_result = self.validate_config()
                if not validation_result.is_valid:
                    logger.warning(f"Config validation failed: {validation_result.errors}")
                    self._stats['error_count'] += 1
            
            # 原子性更新配置
            with self._config_lock:
                old_config = self._config.copy() if self._config else {}
                self._config = new_config
                
                # 更新文件时间戳
                if os.path.exists(file_path):
                    self._last_file_time = os.path.getmtime(file_path)
            
            # 更新校验和（在锁外面）
            self._config_checksum = new_checksum
            
            # 更新统计信息
            self._stats['last_loaded'] = time.time()
            self._stats['total_keys'] = self._count_keys(new_config)
            self._stats['total_sections'] = self._count_sections(new_config)
            
            # 通知监听器
            event = ConfigChangeEvent(
                type=ConfigChangeType.RELOADED,
                timestamp=time.time()
            )
            self._notify_listeners(event)
            
            # 通知重载回调
            self._notify_reload_callbacks(True, "Config reloaded successfully")
            
            logger.info(f"Config loaded successfully from {file_path}")
            return True
            
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON in config file {file_path}: {e}"
            logger.error(error_msg)
            self._stats['error_count'] += 1
            self._notify_reload_callbacks(False, error_msg)
            return False
        except Exception as e:
            error_msg = f"Failed to load config from {file_path}: {e}"
            logger.error(error_msg)
            self._stats['error_count'] += 1
            self._notify_reload_callbacks(False, error_msg)
            return False
    
    def save_to_file(self, file_path: str) -> bool:
        """保存配置到文件"""
        try:
            with self._config_lock:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self._config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Failed to save config to {file_path}: {e}")
            self._stats['error_count'] += 1
            return False
    
    def get_value(self, key: str, default_value: Any = None) -> Any:
        """获取配置值"""
        try:
            with self._config_lock:
                current = self._config
                for part in key.split('.'):
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        return default_value
                
                # 处理环境变量
                if isinstance(current, str) and self._env_vars_enabled:
                    return self._resolve_environment_variables(current)
                
                return current
        except Exception:
            return default_value
    
    def set_value(self, key: str, value: Any) -> bool:
        """设置配置值"""
        try:
            with self._config_lock:
                parts = key.split('.')
                current = self._config
                
                # 保存旧值
                old_value = self.get_value(key)
                
                # 导航到父节点
                for part in parts[:-1]:
                    if part not in current:
                        current[part] = {}
                    current = current[part]
                
                # 设置新值
                final_key = parts[-1]
                current[final_key] = value
                
                # 更新统计信息
                self._stats['change_count'] += 1
                self._stats['last_modified'] = time.time()
                
                # 通知监听器
                event = ConfigChangeEvent(
                    type=ConfigChangeType.ADDED if old_value is None else ConfigChangeType.MODIFIED,
                    key=key,
                    old_value=old_value,
                    new_value=value
                )
                self._notify_listeners(event)
                
                return True
        except Exception as e:
            logger.error(f"Failed to set value for key {key}: {e}")
            return False
    
    def has_key(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            with self._config_lock:
                current = self._config
                for part in key.split('.'):
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        return False
                return True
        except Exception:
            return False
    
    def remove_key(self, key: str) -> bool:
        """删除键"""
        try:
            with self._config_lock:
                parts = key.split('.')
                current = self._config
                
                # 导航到父节点
                for part in parts[:-1]:
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        return False
                
                final_key = parts[-1]
                if isinstance(current, dict) and final_key in current:
                    old_value = current[final_key]
                    del current[final_key]
                    
                    # 通知监听器
                    event = ConfigChangeEvent(
                        type=ConfigChangeType.DELETED,
                        key=key,
                        old_value=old_value
                    )
                    self._notify_listeners(event)
                    
                    return True
                
                return False
        except Exception as e:
            logger.error(f"Failed to remove key {key}: {e}")
            return False
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取配置节"""
        with self._config_lock:
            if section in self._config and isinstance(self._config[section], dict):
                return self._config[section].copy()
            return {}
    
    def set_section(self, section: str, config: Dict[str, Any]) -> bool:
        """设置配置节"""
        try:
            with self._config_lock:
                old_value = self._config.get(section)
                self._config[section] = config
                
                # 通知监听器
                event = ConfigChangeEvent(
                    type=ConfigChangeType.ADDED if old_value is None else ConfigChangeType.MODIFIED,
                    section=section,
                    old_value=old_value,
                    new_value=config
                )
                self._notify_listeners(event)
                
                return True
        except Exception as e:
            logger.error(f"Failed to set section {section}: {e}")
            return False
    
    def remove_section(self, section: str) -> bool:
        """删除配置节"""
        try:
            with self._config_lock:
                if section in self._config:
                    old_value = self._config[section]
                    del self._config[section]
                    
                    # 通知监听器
                    event = ConfigChangeEvent(
                        type=ConfigChangeType.DELETED,
                        section=section,
                        old_value=old_value
                    )
                    self._notify_listeners(event)
                    
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to remove section {section}: {e}")
            return False
    
    def get_section_names(self) -> List[str]:
        """获取所有配置节名称"""
        with self._config_lock:
            return [key for key, value in self._config.items() if isinstance(value, dict)]
    
    def validate_config(self) -> ValidationResult:
        """验证整个配置"""
        result = ValidationResult()
        
        with self._config_lock:
            for section, validator in self._validators.items():
                if section in self._config:
                    section_result = validator.validate(self._config[section])
                    
                    if not section_result.is_valid:
                        result.is_valid = False
                    
                    for error in section_result.errors:
                        result.errors.append(f"[{section}] {error}")
                    
                    for warning in section_result.warnings:
                        result.warnings.append(f"[{section}] {warning}")
        
        self._stats['validation_count'] += 1
        if not result.is_valid:
            self._stats['error_count'] += 1
        
        return result
    
    def validate_section(self, section: str) -> ValidationResult:
        """验证特定配置节"""
        result = ValidationResult()
        
        if section in self._validators and section in self._config:
            result = self._validators[section].validate(self._config[section])
        
        return result
    
    def register_validator(self, section: str, validator: ConfigValidator) -> bool:
        """注册配置验证器"""
        try:
            self._validators[section] = validator
            return True
        except Exception as e:
            logger.error(f"Failed to register validator for section {section}: {e}")
            return False
    
    def unregister_validator(self, section: str):
        """注销配置验证器"""
        if section in self._validators:
            del self._validators[section]
    
    def enable_hot_reload(self, enable: bool = True):
        """启用/禁用热更新"""
        if enable and not self._hot_reload_enabled:
            self._hot_reload_enabled = True
            self._start_enhanced_file_watcher()
        elif not enable and self._hot_reload_enabled:
            self._hot_reload_enabled = False
            self._stop_enhanced_file_watcher()
    
    def is_hot_reload_enabled(self) -> bool:
        """检查热更新是否启用"""
        return self._hot_reload_enabled
    
    def set_file_watch_interval(self, interval: float):
        """设置文件监控间隔"""
        self._watch_interval = interval
    
    def register_change_listener(self, listener: ConfigChangeListener):
        """注册配置变更监听器"""
        if listener not in self._listeners:
            self._listeners.append(listener)
    
    def unregister_change_listener(self, listener: ConfigChangeListener):
        """注销配置变更监听器"""
        if listener in self._listeners:
            self._listeners.remove(listener)
    
    def create_snapshot(self, description: str = "") -> str:
        """创建配置快照"""
        import copy
        with self._config_lock:
            version_id = self._generate_version_id()
            snapshot = {
                'version_id': version_id,
                'timestamp': time.time(),
                'description': description,
                'config_snapshot': copy.deepcopy(self._config),
                'checksum': self._calculate_checksum(self._config)
            }
            
            self._version_history.append(snapshot)
            self._cleanup_old_versions()
            
            return version_id
    
    def restore_from_snapshot(self, version_id: str) -> bool:
        """从快照恢复配置"""
        try:
            snapshot = None
            for version in self._version_history:
                if version['version_id'] == version_id:
                    snapshot = version
                    break
            
            if snapshot is None:
                return False
            
            import copy
            with self._config_lock:
                self._config = copy.deepcopy(snapshot['config_snapshot'])
            
            # 通知监听器
            event = ConfigChangeEvent(
                type=ConfigChangeType.RELOADED,
                timestamp=time.time()
            )
            
            # 在锁外通知监听器
            self._notify_listeners(event)
            
            return True
        except Exception as e:
            logger.error(f"Failed to restore from snapshot {version_id}: {e}")
            return False
    
    def get_version_history(self) -> List[Dict[str, Any]]:
        """获取版本历史"""
        return self._version_history.copy()
    
    def delete_snapshot(self, version_id: str) -> bool:
        """删除快照"""
        try:
            for i, version in enumerate(self._version_history):
                if version['version_id'] == version_id:
                    del self._version_history[i]
                    return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete snapshot {version_id}: {e}")
            return False
    
    def set_max_version_history(self, max_versions: int):
        """设置最大版本历史数量"""
        self._max_version_history = max_versions
        self._cleanup_old_versions()
    
    def merge_config(self, other_config: Dict[str, Any], overwrite: bool = True) -> bool:
        """合并配置"""
        try:
            with self._config_lock:
                if overwrite:
                    self._deep_merge(self._config, other_config)
                else:
                    # 只添加不存在的键
                    for key, value in other_config.items():
                        if key not in self._config:
                            self._config[key] = value
                return True
        except Exception as e:
            logger.error(f"Failed to merge config: {e}")
            return False
    
    def merge_from_file(self, file_path: str, overwrite: bool = True) -> bool:
        """从文件合并配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                other_config = json.load(f)
            return self.merge_config(other_config, overwrite)
        except Exception as e:
            logger.error(f"Failed to merge config from file {file_path}: {e}")
            return False
    
    def enable_environment_variables(self, enable: bool = True):
        """启用/禁用环境变量支持"""
        self._env_vars_enabled = enable
    
    def set_environment_prefix(self, prefix: str):
        """设置环境变量前缀"""
        self._env_prefix = prefix
    
    def export_to_string(self, pretty_print: bool = True) -> str:
        """导出配置为字符串"""
        with self._config_lock:
            if pretty_print:
                return json.dumps(self._config, indent=4, ensure_ascii=False)
            else:
                return json.dumps(self._config, ensure_ascii=False)
    
    def export_to_file(self, file_path: str, pretty_print: bool = True) -> bool:
        """导出配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.export_to_string(pretty_print))
            return True
        except Exception as e:
            logger.error(f"Failed to export config to file {file_path}: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()
    
    # 私有方法
    def _notify_listeners(self, event: ConfigChangeEvent):
        """通知监听器"""
        for listener in self._listeners[:]:  # 创建副本避免并发修改
            try:
                listener.on_config_changed(event)
            except Exception as e:
                logger.error(f"Error in config change listener: {e}")
                # 移除出错的监听器
                if listener in self._listeners:
                    self._listeners.remove(listener)
    
    def _start_file_watcher(self):
        """启动文件监控"""
        if self._file_watcher_running:
            return
        
        self._file_watcher_running = True
        self._file_watcher_thread = threading.Thread(target=self._file_watcher_loop)
        self._file_watcher_thread.daemon = True
        self._file_watcher_thread.start()
    
    def stop_file_watcher(self):
        """停止文件监控"""
        if not self._file_watcher_running:
            return
        
        self._file_watcher_running = False
        if self._file_watcher_thread and self._file_watcher_thread.is_alive():
            self._file_watcher_thread.join(timeout=2.0)
    
    def _start_enhanced_file_watcher(self):
        """启动增强的文件监控"""
        if self._file_observer is not None:
            return
        
        try:
            from watchdog.observers import Observer
            
            self._file_observer = Observer()
            self._file_watcher = ConfigFileWatcher(self, self._config_file_path)
            
            # 监控配置文件所在目录
            config_dir = os.path.dirname(os.path.abspath(self._config_file_path))
            self._file_observer.schedule(self._file_watcher, config_dir, recursive=False)
            
            self._file_observer.start()
            logger.info(f"Enhanced file watcher started for: {self._config_file_path}")
            
            # 同时启动轮询监控作为备份（在Windows上watchdog可能不够可靠）
            self._start_file_watcher()
            
        except ImportError:
            logger.warning("watchdog not available, falling back to polling-based file watcher")
            self._start_file_watcher()
        except Exception as e:
            logger.error(f"Failed to start enhanced file watcher: {e}")
            self._start_file_watcher()
    
    def _stop_enhanced_file_watcher(self):
        """停止增强的文件监控"""
        if self._file_observer is not None:
            try:
                self._file_observer.stop()
                self._file_observer.join(timeout=2.0)
                self._file_observer = None
                self._file_watcher = None
                logger.info("Enhanced file watcher stopped")
            except Exception as e:
                logger.error(f"Error stopping enhanced file watcher: {e}")
        
        # 同时停止传统的文件监控
        self.stop_file_watcher()
    
    def set_backup_enabled(self, enabled: bool):
        """设置是否启用配置备份"""
        self._backup_enabled = enabled
    
    def set_backup_directory(self, backup_dir: str):
        """设置备份目录"""
        self._backup_dir = backup_dir
        os.makedirs(backup_dir, exist_ok=True)
    
    def set_max_backups(self, max_backups: int):
        """设置最大备份数量"""
        self._max_backups = max_backups
    
    def set_safe_reload_enabled(self, enabled: bool):
        """设置是否启用安全重载（验证后再应用）"""
        self._safe_reload_enabled = enabled
    
    def register_reload_callback(self, callback: Callable[[bool, str], None]):
        """注册重载回调函数
        
        Args:
            callback: 回调函数，参数为 (success: bool, message: str)
        """
        if callback not in self._reload_callbacks:
            self._reload_callbacks.append(callback)
    
    def unregister_reload_callback(self, callback: Callable[[bool, str], None]):
        """注销重载回调函数"""
        if callback in self._reload_callbacks:
            self._reload_callbacks.remove(callback)
    
    def _create_backup(self) -> Optional[str]:
        """创建配置备份"""
        if not self._backup_enabled:
            return None
        
        try:
            os.makedirs(self._backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            # 添加微秒确保唯一性
            import random
            microseconds = random.randint(100000, 999999)
            backup_filename = f"config_backup_{timestamp}_{microseconds}.json"
            backup_path = os.path.join(self._backup_dir, backup_filename)
            
            # 备份当前内存中的配置（而不是文件）
            with self._config_lock:
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(self._config, f, indent=4, ensure_ascii=False)
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            logger.info(f"Config backup created: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Failed to create config backup: {e}")
            return None
    
    def _restore_from_backup(self, backup_path: str) -> bool:
        """从备份恢复配置"""
        try:
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, self._config_file_path)
                logger.info(f"Config restored from backup: {backup_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to restore from backup: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """清理旧备份文件"""
        try:
            if not os.path.exists(self._backup_dir):
                return
            
            # 获取所有备份文件
            backup_files = []
            for filename in os.listdir(self._backup_dir):
                if filename.startswith("config_backup_") and filename.endswith(".json"):
                    filepath = os.path.join(self._backup_dir, filename)
                    backup_files.append((filepath, os.path.getmtime(filepath)))
            
            # 按修改时间排序，保留最新的
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除超出数量限制的备份
            for filepath, _ in backup_files[self._max_backups:]:
                try:
                    os.remove(filepath)
                    logger.debug(f"Removed old backup: {filepath}")
                except Exception as e:
                    logger.warning(f"Failed to remove old backup {filepath}: {e}")
                    
        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")
    
    def _calculate_config_checksum(self) -> str:
        """计算配置文件校验和"""
        try:
            with open(self._config_file_path, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception:
            return ""
    
    def _notify_reload_callbacks(self, success: bool, message: str):
        """通知重载回调函数"""
        for callback in self._reload_callbacks[:]:  # 创建副本避免并发修改
            try:
                callback(success, message)
            except Exception as e:
                logger.error(f"Error in reload callback: {e}")
                # 移除出错的回调
                if callback in self._reload_callbacks:
                    self._reload_callbacks.remove(callback)
    
    def _file_watcher_loop(self):
        """文件监控循环"""
        while self._file_watcher_running:
            try:
                if os.path.exists(self._config_file_path):
                    current_time = os.path.getmtime(self._config_file_path)
                    
                    if current_time != self._last_file_time:
                        self._last_file_time = current_time
                        self.load_config()
            except Exception as e:
                logger.error(f"Error in file watcher: {e}")
            
            time.sleep(self._watch_interval)
    
    def _calculate_checksum(self, config: Dict[str, Any]) -> str:
        """计算配置校验和"""
        import hashlib
        config_str = json.dumps(config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def _generate_version_id(self) -> str:
        """生成版本ID"""
        import datetime
        import random
        now = datetime.datetime.now()
        # 添加随机数确保唯一性
        random_suffix = random.randint(100, 999)
        return f"v{now.strftime('%Y%m%d_%H%M%S')}_{now.microsecond:06d}_{random_suffix}"
    
    def _cleanup_old_versions(self):
        """清理旧版本"""
        while len(self._version_history) > self._max_version_history:
            self._version_history.pop(0)
    
    def _resolve_environment_variables(self, value: str) -> str:
        """解析环境变量"""
        import re
        
        def replace_env_var(match):
            env_var = match.group(1)
            env_value = os.getenv(env_var)
            
            if env_value is not None:
                return env_value
            
            # 尝试使用前缀
            prefixed_var = self._env_prefix + env_var
            prefixed_value = os.getenv(prefixed_var)
            
            if prefixed_value is not None:
                return prefixed_value
            
            # 保留原始值
            return match.group(0)
        
        return re.sub(r'\$\{([^}]+)\}', replace_env_var, value)
    
    def _process_environment_variables(self, config: Any) -> Any:
        """处理环境变量"""
        if isinstance(config, str):
            return self._resolve_environment_variables(config)
        elif isinstance(config, dict):
            return {key: self._process_environment_variables(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._process_environment_variables(item) for item in config]
        else:
            return config
    
    def _count_keys(self, config: Dict[str, Any]) -> int:
        """计算键数量"""
        count = 0
        
        def count_recursive(obj):
            nonlocal count
            if isinstance(obj, dict):
                count += len(obj)
                for value in obj.values():
                    if isinstance(value, dict):
                        count_recursive(value)
        
        count_recursive(config)
        return count
    
    def _count_sections(self, config: Dict[str, Any]) -> int:
        """计算配置节数量"""
        return sum(1 for value in config.values() if isinstance(value, dict))
    
    def _deep_merge(self, target: Dict[str, Any], source: Dict[str, Any]):
        """深度合并字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value


# 全局配置管理器实例
config_manager = PythonConfigManager()


# 便捷函数
def get_config(key: str, default_value: Any = None) -> Any:
    """获取配置值"""
    return config_manager.get_value(key, default_value)


def set_config(key: str, value: Any) -> bool:
    """设置配置值"""
    return config_manager.set_value(key, value)


def has_config(key: str) -> bool:
    """检查配置键是否存在"""
    return config_manager.has_key(key)


def get_section(section: str) -> Dict[str, Any]:
    """获取配置节"""
    return config_manager.get_section(section)


def initialize_config(config_file_path: str) -> bool:
    """初始化配置管理器"""
    return config_manager.initialize(config_file_path)