#!/bin/bash
# 金融数据服务 - 任务调度器启动脚本 (Linux/macOS)
# Financial Data Service - Scheduler Startup Script (Linux/macOS)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
金融数据服务 - 任务调度器
Financial Data Service - Task Scheduler

用法: $0 [选项]
Usage: $0 [options]

选项 Options:
  --config, -c FILE     配置文件路径 Configuration file path
  --log-level, -l LEVEL 日志级别 Log level (DEBUG, INFO, WARNING, ERROR)
  --daemon, -d          后台运行模式 Daemon mode
  --status              显示服务状态 Show service status
  --stop                停止服务 Stop service
  --restart             重启服务 Restart service
  --help, -h            显示帮助 Show help

示例 Examples:
  $0                           使用默认配置启动 Start with default config
  $0 --config custom.json     使用自定义配置 Start with custom config
  $0 --log-level DEBUG        设置日志级别 Set log level
  $0 --daemon                 后台运行 Run as daemon
  $0 --status                 显示状态 Show status
  $0 --stop                   停止服务 Stop service
  $0 --restart                重启服务 Restart service

EOF
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 默认参数
CONFIG_FILE="config/scheduler_config.json"
LOG_LEVEL="INFO"
DAEMON_MODE=false
SHOW_STATUS=false
STOP_SERVICE=false
RESTART_SERVICE=false
PID_FILE="logs/scheduler.pid"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --config|-c)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --log-level|-l)
            LOG_LEVEL="$2"
            shift 2
            ;;
        --daemon|-d)
            DAEMON_MODE=true
            shift
            ;;
        --status)
            SHOW_STATUS=true
            shift
            ;;
        --stop)
            STOP_SERVICE=true
            shift
            ;;
        --restart)
            RESTART_SERVICE=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3未安装或不在PATH中"
        print_error "Python3 is not installed or not in PATH"
        exit 1
    fi
    
    # 检查Python版本
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if [[ $(echo "$python_version < 3.8" | bc -l) -eq 1 ]]; then
        print_error "需要Python 3.8或更高版本，当前版本: $python_version"
        print_error "Python 3.8 or higher required, current version: $python_version"
        exit 1
    fi
    
    print_info "Python版本: $python_version"
}

# 检查依赖包
check_dependencies() {
    print_info "检查Python依赖包..."
    
    if ! python3 -c "import asyncio, logging, json, croniter" &> /dev/null; then
        print_error "缺少必要的Python包"
        print_error "Missing required Python packages"
        print_info "请运行: pip3 install -r requirements.txt"
        print_info "Please run: pip3 install -r requirements.txt"
        exit 1
    fi
    
    print_success "依赖包检查通过"
}

# 创建必要的目录
create_directories() {
    mkdir -p logs
    mkdir -p config
    print_info "已创建必要目录"
}

# 检查配置文件
check_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        print_warning "配置文件不存在: $CONFIG_FILE"
        print_warning "Configuration file not found: $CONFIG_FILE"
        print_info "将使用默认配置"
        print_info "Will use default configuration"
    else
        print_info "使用配置文件: $CONFIG_FILE"
    fi
}

# 检查服务状态
check_service_status() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE" 2>/dev/null)
        if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
            print_success "服务正在运行 (PID: $pid)"
            return 0
        else
            print_warning "服务未运行 (PID文件存在但进程不存在)"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        print_info "服务未运行 (PID文件不存在)"
        return 1
    fi
}

# 停止服务
stop_service() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE" 2>/dev/null)
        if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
            print_info "正在停止服务 (PID: $pid)..."
            
            # 发送TERM信号
            kill -TERM "$pid"
            
            # 等待进程结束
            local count=0
            while kill -0 "$pid" 2>/dev/null && [[ $count -lt 30 ]]; do
                sleep 1
                ((count++))
            done
            
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "正常停止超时，强制终止进程"
                kill -KILL "$pid"
            fi
            
            rm -f "$PID_FILE"
            print_success "服务已停止"
        else
            print_info "服务未运行"
            rm -f "$PID_FILE"
        fi
    else
        print_info "服务未运行"
    fi
}

# 启动服务
start_service() {
    # 检查是否已经在运行
    if check_service_status &>/dev/null; then
        print_error "服务已在运行"
        exit 1
    fi
    
    print_info "启动调度器服务..."
    print_info "配置文件: $CONFIG_FILE"
    print_info "日志级别: $LOG_LEVEL"
    print_info "后台模式: $DAEMON_MODE"
    
    local cmd_args="--config $CONFIG_FILE --log-level $LOG_LEVEL"
    
    if [[ "$DAEMON_MODE" == true ]]; then
        cmd_args="$cmd_args --daemon --pid-file $PID_FILE"
        python3 scripts/start_scheduler.py $cmd_args
        
        # 等待一下确保服务启动
        sleep 2
        
        if check_service_status &>/dev/null; then
            print_success "调度器服务已在后台启动"
        else
            print_error "服务启动失败"
            exit 1
        fi
    else
        cmd_args="$cmd_args --pid-file $PID_FILE"
        print_info "前台模式启动，按 Ctrl+C 停止服务"
        
        # 设置信号处理
        trap 'print_info "收到中断信号，正在停止服务..."; exit 0' INT TERM
        
        python3 scripts/start_scheduler.py $cmd_args
    fi
}

# 重启服务
restart_service() {
    print_info "重启调度器服务..."
    stop_service
    sleep 2
    start_service
}

# 主函数
main() {
    echo "========================================"
    echo "金融数据服务 - 任务调度器"
    echo "Financial Data Service - Task Scheduler"
    echo "========================================"
    echo
    
    # 基础检查
    check_python
    check_dependencies
    create_directories
    check_config
    
    # 处理不同的命令
    if [[ "$SHOW_STATUS" == true ]]; then
        check_service_status
        exit $?
    fi
    
    if [[ "$STOP_SERVICE" == true ]]; then
        stop_service
        exit 0
    fi
    
    if [[ "$RESTART_SERVICE" == true ]]; then
        restart_service
        exit 0
    fi
    
    # 启动服务
    start_service
}

# 运行主函数
main "$@"