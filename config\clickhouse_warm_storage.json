{"clickhouse": {"cluster": {"name": "financial_cluster", "nodes": [{"host": "clickhouse-1", "port": 9000, "http_port": 8123, "role": "primary"}, {"host": "clickhouse-2", "port": 9000, "http_port": 8123, "role": "replica"}, {"host": "clickhouse-3", "port": 9000, "http_port": 8123, "role": "replica"}]}, "connection": {"database": "market_data", "username": "admin", "password": "password123", "max_connections": 20, "connection_timeout_seconds": 30, "query_timeout_seconds": 300, "compression": "lz4"}, "performance": {"batch_size": 10000, "batch_timeout_seconds": 5, "max_memory_usage_gb": 10, "max_concurrent_queries": 10, "background_pool_size": 16, "merge_tree_max_rows_to_use_cache": 1048576}, "storage": {"data_retention_days": 730, "partition_by": "month", "ttl_to_cold_storage_days": 90, "compression_codec": "ZSTD(3)", "index_granularity": 8192}}, "data_migration": {"schedule": {"enabled": true, "interval_hours": 1, "start_hour": 2, "data_age_threshold_hours": 168}, "batch_processing": {"batch_size": 10000, "max_concurrent_migrations": 4, "parallel_symbol_processing": true}, "validation": {"enabled": true, "data_loss_threshold_percent": 0.1, "validate_sequences": true, "validate_timestamps": true, "validate_prices": true}, "retry": {"max_retries": 3, "retry_delay_seconds": 30, "exponential_backoff": true}, "cleanup": {"auto_cleanup_redis": true, "cleanup_delay_hours": 24, "keep_backup_days": 7}}, "table_schemas": {"futures_tick": {"partition_by": "toYYYYMM(timestamp)", "order_by": "(symbol, timestamp)", "primary_key": "(symbol, timestamp)", "ttl": "timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'", "indexes": [{"name": "idx_symbol", "expression": "symbol", "type": "bloom_filter(0.01)"}, {"name": "idx_timestamp", "expression": "timestamp", "type": "minmax", "granularity": 3}, {"name": "idx_exchange", "expression": "exchange", "type": "set(10)", "granularity": 1}]}, "stock_tick": {"partition_by": "toYYYYMM(timestamp)", "order_by": "(symbol, timestamp)", "primary_key": "(symbol, timestamp)", "ttl": "timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'", "indexes": [{"name": "idx_symbol", "expression": "symbol", "type": "bloom_filter(0.01)"}, {"name": "idx_timestamp", "expression": "timestamp", "type": "minmax", "granularity": 3}]}, "options_tick": {"partition_by": "(toYYYYMM(timestamp), underlying_symbol)", "order_by": "(symbol, timestamp)", "primary_key": "(symbol, timestamp)", "ttl": "timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'", "indexes": [{"name": "idx_symbol", "expression": "symbol", "type": "bloom_filter(0.01)"}, {"name": "idx_underlying", "expression": "underlying_symbol", "type": "bloom_filter(0.01)"}, {"name": "idx_expiry", "expression": "expiry_date", "type": "minmax", "granularity": 1}]}, "forex_tick": {"partition_by": "toYYYYMM(timestamp)", "order_by": "(symbol, timestamp)", "primary_key": "(symbol, timestamp)", "ttl": "timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'", "indexes": [{"name": "idx_symbol", "expression": "symbol", "type": "bloom_filter(0.01)"}, {"name": "idx_base_currency", "expression": "base_currency", "type": "set(50)", "granularity": 1}]}}, "kline_tables": {"periods": ["1m", "5m", "15m", "1h", "1d"], "retention_policies": {"1m": "2 YEAR", "5m": "2 YEAR", "15m": "2 YEAR", "1h": "5 YEAR", "1d": "10 YEAR"}, "partition_strategies": {"1m": "toYYYYMM(timestamp)", "5m": "toYYYYMM(timestamp)", "15m": "toYYYYMM(timestamp)", "1h": "toYYYYMM(timestamp)", "1d": "toYYYY(timestamp)"}}, "monitoring": {"metrics_collection": {"enabled": true, "collection_interval_seconds": 60, "retention_days": 30}, "alerts": {"migration_failure_threshold": 5, "query_latency_threshold_ms": 1000, "disk_usage_threshold_percent": 85, "memory_usage_threshold_percent": 90}, "health_checks": {"cluster_health_check_interval_seconds": 30, "node_health_check_interval_seconds": 10, "replication_lag_threshold_seconds": 60}}, "security": {"encryption": {"enable_tls": true, "tls_version": "1.3", "certificate_path": "/etc/clickhouse-server/certs/", "verify_certificates": true}, "authentication": {"enable_rbac": true, "session_timeout_minutes": 60, "max_failed_attempts": 5, "lockout_duration_minutes": 15}, "audit": {"enable_query_log": true, "log_retention_days": 90, "log_sensitive_data": false}}}