#!/bin/bash

echo "启动金融数据服务开发环境..."
echo

# 检查 Docker 是否运行
if ! docker info >/dev/null 2>&1; then
    echo "错误: Docker 未运行，请先启动 Docker"
    exit 1
fi

echo "1. 停止现有容器..."
docker-compose -f docker-compose.dev.yml down

echo
echo "2. 拉取最新镜像..."
docker-compose -f docker-compose.dev.yml pull

echo
echo "3. 启动开发环境服务..."
docker-compose -f docker-compose.dev.yml up -d

echo
echo "4. 等待服务启动..."
sleep 10

echo
echo "5. 检查服务状态..."
docker-compose -f docker-compose.dev.yml ps

echo
echo "========================================"
echo "开发环境服务已启动！"
echo "========================================"
echo
echo "服务访问地址:"
echo "- Redis:      localhost:6379"
echo "- ClickHouse: localhost:8123 (HTTP), localhost:9000 (Native)"
echo "- Kafka:      localhost:9092"
echo "- MinIO:      http://localhost:9001 (用户名: admin, 密码: password123)"
echo
echo "使用以下命令查看日志:"
echo "  docker-compose -f docker-compose.dev.yml logs -f [service_name]"
echo
echo "使用以下命令停止环境:"
echo "  docker-compose -f docker-compose.dev.yml down"
echo