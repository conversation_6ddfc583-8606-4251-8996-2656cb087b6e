#include <gtest/gtest.h>
#include <thread>
#include <chrono>
#include <vector>
#include <memory>
#include <atomic>
#include <future>

#include "../../src/collectors/ctp_collector.h"
#include "../../src/databus/data_bus.h"
#include "../../src/storage/redis_storage.h"
#include "../../src/storage/clickhouse_storage.h"
#include "../../src/interfaces/websocket_server.h"
#include "../../src/interfaces/grpc_server.h"
#include "../../src/monitoring/metrics_collector.h"
#include "../../src/security/security_manager.h"

class SystemIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize all system components
        data_bus_ = std::make_shared<DataBus>();
        redis_storage_ = std::make_shared<RedisStorage>();
        clickhouse_storage_ = std::make_shared<ClickHouseStorage>();
        websocket_server_ = std::make_shared<WebSocketServer>();
        grpc_server_ = std::make_shared<GrpcServer>();
        metrics_collector_ = std::make_shared<MetricsCollector>();
        security_manager_ = std::make_shared<SecurityManager>();
        
        // Start all services
        StartAllServices();
        
        // Wait for services to be ready
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
    
    void TearDown() override {
        StopAllServices();
    }
    
    void StartAllServices() {
        // Start data bus
        data_bus_->Start();
        
        // Start storage services
        redis_storage_->Initialize();
        clickhouse_storage_->Initialize();
        
        // Start interface services
        websocket_server_->Start(8080);
        grpc_server_->Start(50051);
        
        // Start monitoring
        metrics_collector_->Start();
        
        // Initialize security
        security_manager_->Initialize();
    }
    
    void StopAllServices() {
        websocket_server_->Stop();
        grpc_server_->Stop();
        metrics_collector_->Stop();
        data_bus_->Stop();
    }
    
    std::shared_ptr<DataBus> data_bus_;
    std::shared_ptr<RedisStorage> redis_storage_;
    std::shared_ptr<ClickHouseStorage> clickhouse_storage_;
    std::shared_ptr<WebSocketServer> websocket_server_;
    std::shared_ptr<GrpcServer> grpc_server_;
    std::shared_ptr<MetricsCollector> metrics_collector_;
    std::shared_ptr<SecurityManager> security_manager_;
};

// Test 1: End-to-End Data Flow
TEST_F(SystemIntegrationTest, EndToEndDataFlow) {
    // Generate test market data
    MarketData test_data;
    test_data.symbol = "CU2409";
    test_data.exchange = "SHFE";
    test_data.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    test_data.last_price = 78560.0;
    test_data.volume = 12580;
    test_data.sequence = 123456;
    
    // Publish data to data bus
    auto start_time = std::chrono::high_resolution_clock::now();
    data_bus_->Publish(test_data);
    
    // Wait for data to propagate through system
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Verify data in Redis hot storage
    auto redis_data = redis_storage_->GetLatestTick("CU2409");
    ASSERT_TRUE(redis_data.has_value());
    EXPECT_EQ(redis_data->symbol, "CU2409");
    EXPECT_EQ(redis_data->last_price, 78560.0);
    
    // Verify data can be queried via gRPC
    // (This would require a gRPC client implementation)
    
    // Measure end-to-end latency
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - start_time).count();
    
    EXPECT_LT(latency, 50); // Less than 50 microseconds
}

// Test 2: High Throughput Processing
TEST_F(SystemIntegrationTest, HighThroughputProcessing) {
    const int MESSAGE_COUNT = 100000;
    std::atomic<int> processed_count{0};
    
    // Set up data processing callback
    data_bus_->SetCallback([&](const MarketData& data) {
        processed_count++;
    });
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Generate and publish test data
    for (int i = 0; i < MESSAGE_COUNT; ++i) {
        MarketData test_data;
        test_data.symbol = "TEST" + std::to_string(i % 100);
        test_data.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        test_data.last_price = 1000.0 + i;
        test_data.sequence = i;
        
        data_bus_->Publish(test_data);
    }
    
    // Wait for processing to complete
    while (processed_count < MESSAGE_COUNT) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time).count();
    
    double throughput = static_cast<double>(MESSAGE_COUNT) / duration;
    EXPECT_GT(throughput, 1000000); // Greater than 1M messages/second
}

// Test 3: Concurrent Client Connections
TEST_F(SystemIntegrationTest, ConcurrentClientConnections) {
    const int CLIENT_COUNT = 1000;
    std::vector<std::future<bool>> client_futures;
    std::atomic<int> successful_connections{0};
    
    // Simulate concurrent client connections
    for (int i = 0; i < CLIENT_COUNT; ++i) {
        auto future = std::async(std::launch::async, [this, i, &successful_connections]() {
            try {
                // Simulate WebSocket client connection
                // (This would require actual WebSocket client implementation)
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                successful_connections++;
                return true;
            } catch (...) {
                return false;
            }
        });
        client_futures.push_back(std::move(future));
    }
    
    // Wait for all connections to complete
    int successful_count = 0;
    for (auto& future : client_futures) {
        if (future.get()) {
            successful_count++;
        }
    }
    
    EXPECT_EQ(successful_count, CLIENT_COUNT);
    EXPECT_EQ(successful_connections.load(), CLIENT_COUNT);
}

// Test 4: Data Integrity and Consistency
TEST_F(SystemIntegrationTest, DataIntegrityAndConsistency) {
    const int SEQUENCE_COUNT = 10000;
    std::vector<MarketData> test_data;
    
    // Generate sequential test data
    for (int i = 0; i < SEQUENCE_COUNT; ++i) {
        MarketData data;
        data.symbol = "INTEGRITY_TEST";
        data.sequence = i;
        data.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count() + i;
        data.last_price = 1000.0 + i * 0.1;
        test_data.push_back(data);
    }
    
    // Publish all data
    for (const auto& data : test_data) {
        data_bus_->Publish(data);
    }
    
    // Wait for processing
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Verify data integrity in storage
    auto stored_data = redis_storage_->GetTicksBySequenceRange("INTEGRITY_TEST", 0, SEQUENCE_COUNT - 1);
    EXPECT_EQ(stored_data.size(), SEQUENCE_COUNT);
    
    // Verify sequence continuity
    for (size_t i = 0; i < stored_data.size(); ++i) {
        EXPECT_EQ(stored_data[i].sequence, i);
    }
}

// Test 5: System Recovery and Failover
TEST_F(SystemIntegrationTest, SystemRecoveryAndFailover) {
    // Generate initial data
    MarketData test_data;
    test_data.symbol = "FAILOVER_TEST";
    test_data.sequence = 1;
    test_data.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    data_bus_->Publish(test_data);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Simulate service failure and recovery
    websocket_server_->Stop();
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    auto recovery_start = std::chrono::high_resolution_clock::now();
    websocket_server_->Start(8080);
    
    // Wait for service to be ready
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    auto recovery_end = std::chrono::high_resolution_clock::now();
    auto recovery_time = std::chrono::duration_cast<std::chrono::seconds>(
        recovery_end - recovery_start).count();
    
    EXPECT_LT(recovery_time, 5); // Recovery within 5 seconds
    
    // Verify system continues to work after recovery
    test_data.sequence = 2;
    data_bus_->Publish(test_data);
    
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    auto recovered_data = redis_storage_->GetLatestTick("FAILOVER_TEST");
    ASSERT_TRUE(recovered_data.has_value());
    EXPECT_EQ(recovered_data->sequence, 2);
}

// Test 6: Security and Authentication
TEST_F(SystemIntegrationTest, SecurityAndAuthentication) {
    // Test JWT authentication
    std::string valid_token = security_manager_->GenerateJWT("test_user", {"read", "write"});
    EXPECT_TRUE(security_manager_->ValidateJWT(valid_token));
    
    std::string invalid_token = "invalid.jwt.token";
    EXPECT_FALSE(security_manager_->ValidateJWT(invalid_token));
    
    // Test role-based access control
    EXPECT_TRUE(security_manager_->HasPermission("test_user", "read"));
    EXPECT_FALSE(security_manager_->HasPermission("test_user", "admin"));
    
    // Test TLS encryption (would require actual TLS implementation)
    EXPECT_TRUE(security_manager_->IsTLSEnabled());
}

// Test 7: Monitoring and Alerting
TEST_F(SystemIntegrationTest, MonitoringAndAlerting) {
    // Generate high latency scenario
    auto start_time = std::chrono::high_resolution_clock::now();
    std::this_thread::sleep_for(std::chrono::microseconds(100)); // Simulate processing delay
    auto end_time = std::chrono::high_resolution_clock::now();
    
    auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - start_time).count();
    
    metrics_collector_->RecordLatency(latency);
    
    // Check if alert is triggered for high latency
    if (latency > 50) {
        EXPECT_TRUE(metrics_collector_->HasAlert("HIGH_LATENCY"));
    }
    
    // Test resource monitoring
    metrics_collector_->RecordCPUUsage(90.0); // High CPU usage
    EXPECT_TRUE(metrics_collector_->HasAlert("HIGH_CPU_USAGE"));
    
    // Test data integrity monitoring
    metrics_collector_->RecordDataLoss(1); // Data loss detected
    EXPECT_TRUE(metrics_collector_->HasAlert("DATA_LOSS"));
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}