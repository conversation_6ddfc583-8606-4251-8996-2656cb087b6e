#!/usr/bin/env python3
"""
金融数据服务系统 - 统一测试运行器
Financial Data Service - Unified Test Runner

这是一个统一的测试运行器，可以运行所有类型的测试：
- 单元测试 (Unit Tests)
- 集成测试 (Integration Tests)  
- 性能测试 (Performance Tests)
- 部署测试 (Deployment Tests)
- 端到端测试 (End-to-End Tests)

使用方法:
    python test_runner.py --all                    # 运行所有测试
    python test_runner.py --unit                   # 只运行单元测试
    python test_runner.py --integration            # 只运行集成测试
    python test_runner.py --performance            # 只运行性能测试
    python test_runner.py --deployment             # 只运行部署测试
    python test_runner.py --e2e                    # 只运行端到端测试
    python test_runner.py --quick                  # 快速测试（基础功能）
    python test_runner.py --report                 # 生成详细报告
"""

import os
import sys
import time
import json
import argparse
import subprocess
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import concurrent.futures
from enum import Enum

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestType(Enum):
    """测试类型枚举"""
    UNIT = "unit"
    INTEGRATION = "integration"
    PERFORMANCE = "performance"
    DEPLOYMENT = "deployment"
    E2E = "e2e"
    QUICK = "quick"


@dataclass
class TestResult:
    """测试结果数据类"""
    name: str
    test_type: TestType
    status: str  # "PASS", "FAIL", "SKIP", "ERROR"
    duration: float
    output: str = ""
    error: str = ""
    details: Dict = None


@dataclass
class TestSuite:
    """测试套件数据类"""
    name: str
    test_type: TestType
    command: str
    working_dir: str = "."
    timeout: int = 300
    required_services: List[str] = None
    environment: Dict[str, str] = None


class UnifiedTestRunner:
    """统一测试运行器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root or os.getcwd())
        self.results: List[TestResult] = []
        self.start_time = None
        self.end_time = None
        
        # 定义测试套件
        self.test_suites = self._define_test_suites()
        
    def _define_test_suites(self) -> Dict[TestType, List[TestSuite]]:
        """定义所有测试套件"""
        return {
            TestType.UNIT: [
                TestSuite(
                    name="Python单元测试",
                    test_type=TestType.UNIT,
                    command="python -m pytest tests/test_*.py -v --tb=short",
                    timeout=120
                ),
                TestSuite(
                    name="C++单元测试",
                    test_type=TestType.UNIT,
                    command="cd build && ctest --output-on-failure",
                    timeout=180
                ),
                TestSuite(
                    name="配置管理测试",
                    test_type=TestType.UNIT,
                    command="python -m pytest tests/test_config_*.py -v",
                    timeout=60
                )
            ],
            TestType.INTEGRATION: [
                TestSuite(
                    name="存储集成测试",
                    test_type=TestType.INTEGRATION,
                    command="python -m pytest tests/test_*_integration.py -v",
                    required_services=["redis", "clickhouse"],
                    timeout=300
                ),
                TestSuite(
                    name="数据采集集成测试",
                    test_type=TestType.INTEGRATION,
                    command="python -m pytest tests/test_pytdx_*.py -v",
                    timeout=180
                ),
                TestSuite(
                    name="端到端集成测试",
                    test_type=TestType.INTEGRATION,
                    command="python tests/integration/end_to_end_integration_test.py",
                    required_services=["redis", "clickhouse"],
                    timeout=600
                )
            ],
            TestType.PERFORMANCE: [
                TestSuite(
                    name="数据库性能测试",
                    test_type=TestType.PERFORMANCE,
                    command="cd build && ./databus_performance_test",
                    required_services=["redis", "clickhouse"],
                    timeout=300
                ),
                TestSuite(
                    name="网络性能测试",
                    test_type=TestType.PERFORMANCE,
                    command="python tests/performance/network_performance_test.py",
                    timeout=180
                )
            ],
            TestType.DEPLOYMENT: [
                TestSuite(
                    name="Docker部署测试",
                    test_type=TestType.DEPLOYMENT,
                    command="python test_docker_deployment.py",
                    required_services=["docker"],
                    timeout=600
                ),
                TestSuite(
                    name="服务健康检查",
                    test_type=TestType.DEPLOYMENT,
                    command="python scripts/check_dev_services.py",
                    timeout=120
                )
            ],
            TestType.E2E: [
                TestSuite(
                    name="完整数据流测试",
                    test_type=TestType.E2E,
                    command="python tests/integration/end_to_end_integration_test.py",
                    required_services=["redis", "clickhouse", "kafka"],
                    timeout=900
                )
            ],
            TestType.QUICK: [
                TestSuite(
                    name="Python环境检查",
                    test_type=TestType.QUICK,
                    command="python -c \"import sys; print(f'Python {sys.version}'); import json, os, pathlib; print('OK Python环境正常')\"",
                    timeout=30
                ),
                TestSuite(
                    name="配置文件验证",
                    test_type=TestType.QUICK,
                    command="python -c \"import json; [json.load(open(f, encoding='utf-8')) for f in ['config/app.json', 'config/unified_config.json', 'config/environments.json']]; print('OK 配置文件格式正确')\"",
                    timeout=30
                ),
                TestSuite(
                    name="项目结构检查",
                    test_type=TestType.QUICK,
                    command="python -c \"import os; dirs=['src','config','tests']; [print(f'OK {d}') if os.path.exists(d) else print(f'FAIL {d}') for d in dirs]; print('OK 项目结构检查完成')\"",
                    timeout=30
                )
            ]
        }
    
    def check_prerequisites(self, test_types: List[TestType]) -> bool:
        """检查测试前提条件"""
        logger.info("检查测试前提条件...")
        
        # 检查Python环境
        try:
            import pytest
            import redis
            import pandas
        except ImportError as e:
            logger.error(f"缺少必要的Python包: {e}")
            return False
        
        # 检查项目结构
        required_dirs = ["src", "tests", "config"]
        for dir_name in required_dirs:
            if not (self.project_root / dir_name).exists():
                logger.error(f"缺少必要目录: {dir_name}")
                return False
        
        # 检查必要的服务（根据测试类型）
        required_services = set()
        for test_type in test_types:
            for suite in self.test_suites.get(test_type, []):
                if suite.required_services:
                    required_services.update(suite.required_services)
        
        if required_services:
            logger.info(f"需要的服务: {', '.join(required_services)}")
            # 这里可以添加服务检查逻辑
        
        return True
    
    def run_test_suite(self, suite: TestSuite) -> TestResult:
        """运行单个测试套件"""
        logger.info(f"运行测试: {suite.name}")
        start_time = time.time()
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            if suite.environment:
                env.update(suite.environment)
            
            # 运行测试命令
            result = subprocess.run(
                suite.command,
                shell=True,
                cwd=self.project_root / suite.working_dir,
                capture_output=True,
                text=True,
                timeout=suite.timeout,
                env=env
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                status = "PASS"
                logger.info(f"✅ {suite.name} - 通过 ({duration:.2f}s)")
            else:
                status = "FAIL"
                logger.error(f"❌ {suite.name} - 失败 ({duration:.2f}s)")
            
            return TestResult(
                name=suite.name,
                test_type=suite.test_type,
                status=status,
                duration=duration,
                output=result.stdout,
                error=result.stderr
            )
            
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            logger.error(f"⏰ {suite.name} - 超时 ({duration:.2f}s)")
            return TestResult(
                name=suite.name,
                test_type=suite.test_type,
                status="ERROR",
                duration=duration,
                error=f"测试超时 ({suite.timeout}s)"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"💥 {suite.name} - 错误: {e}")
            return TestResult(
                name=suite.name,
                test_type=suite.test_type,
                status="ERROR",
                duration=duration,
                error=str(e)
            )
    
    def run_tests(self, test_types: List[TestType], parallel: bool = False) -> bool:
        """运行指定类型的测试"""
        self.start_time = datetime.now()
        logger.info(f"开始运行测试: {[t.value for t in test_types]}")
        
        # 检查前提条件
        if not self.check_prerequisites(test_types):
            return False
        
        # 收集要运行的测试套件
        suites_to_run = []
        for test_type in test_types:
            suites_to_run.extend(self.test_suites.get(test_type, []))
        
        if not suites_to_run:
            logger.warning("没有找到要运行的测试套件")
            return False
        
        logger.info(f"将运行 {len(suites_to_run)} 个测试套件")
        
        # 运行测试
        if parallel and len(suites_to_run) > 1:
            # 并行运行
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                future_to_suite = {
                    executor.submit(self.run_test_suite, suite): suite 
                    for suite in suites_to_run
                }
                
                for future in concurrent.futures.as_completed(future_to_suite):
                    result = future.result()
                    self.results.append(result)
        else:
            # 串行运行
            for suite in suites_to_run:
                result = self.run_test_suite(suite)
                self.results.append(result)
        
        self.end_time = datetime.now()
        return self._analyze_results()
    
    def _analyze_results(self) -> bool:
        """分析测试结果"""
        total = len(self.results)
        passed = sum(1 for r in self.results if r.status == "PASS")
        failed = sum(1 for r in self.results if r.status == "FAIL")
        errors = sum(1 for r in self.results if r.status == "ERROR")
        
        logger.info("\n" + "="*60)
        logger.info("测试结果汇总")
        logger.info("="*60)
        logger.info(f"总计: {total} 个测试")
        logger.info(f"通过: {passed} 个 ✅")
        logger.info(f"失败: {failed} 个 ❌")
        logger.info(f"错误: {errors} 个 💥")
        
        if self.start_time and self.end_time:
            duration = (self.end_time - self.start_time).total_seconds()
            logger.info(f"总耗时: {duration:.2f} 秒")
        
        # 显示失败的测试
        if failed > 0 or errors > 0:
            logger.info("\n失败的测试:")
            for result in self.results:
                if result.status in ["FAIL", "ERROR"]:
                    logger.error(f"  - {result.name}: {result.status}")
                    if result.error:
                        logger.error(f"    错误: {result.error[:200]}...")
        
        return failed == 0 and errors == 0
    
    def generate_report(self, output_file: str = None) -> str:
        """生成详细的测试报告"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"test_report_{timestamp}.json"
        
        report = {
            "summary": {
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "end_time": self.end_time.isoformat() if self.end_time else None,
                "duration": (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else 0,
                "total_tests": len(self.results),
                "passed": sum(1 for r in self.results if r.status == "PASS"),
                "failed": sum(1 for r in self.results if r.status == "FAIL"),
                "errors": sum(1 for r in self.results if r.status == "ERROR")
            },
            "results": [asdict(result) for result in self.results]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"测试报告已生成: {output_file}")
        return output_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="金融数据服务系统统一测试运行器")
    
    # 测试类型选项
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    parser.add_argument("--unit", action="store_true", help="运行单元测试")
    parser.add_argument("--integration", action="store_true", help="运行集成测试")
    parser.add_argument("--performance", action="store_true", help="运行性能测试")
    parser.add_argument("--deployment", action="store_true", help="运行部署测试")
    parser.add_argument("--e2e", action="store_true", help="运行端到端测试")
    parser.add_argument("--quick", action="store_true", help="运行快速测试")
    
    # 其他选项
    parser.add_argument("--parallel", action="store_true", help="并行运行测试")
    parser.add_argument("--report", action="store_true", help="生成详细报告")
    parser.add_argument("--output", help="报告输出文件")
    
    args = parser.parse_args()
    
    # 确定要运行的测试类型
    test_types = []
    if args.all:
        test_types = list(TestType)
    else:
        if args.unit:
            test_types.append(TestType.UNIT)
        if args.integration:
            test_types.append(TestType.INTEGRATION)
        if args.performance:
            test_types.append(TestType.PERFORMANCE)
        if args.deployment:
            test_types.append(TestType.DEPLOYMENT)
        if args.e2e:
            test_types.append(TestType.E2E)
        if args.quick:
            test_types.append(TestType.QUICK)
    
    # 如果没有指定测试类型，默认运行快速测试
    if not test_types:
        test_types = [TestType.QUICK]
    
    # 创建测试运行器并运行测试
    runner = UnifiedTestRunner()
    success = runner.run_tests(test_types, parallel=args.parallel)
    
    # 生成报告
    if args.report:
        runner.generate_report(args.output)
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
