{"scheduler": {"max_concurrent_tasks": 5, "task_timeout_seconds": 3600, "health_check_interval": 30, "log_level": "INFO"}, "tasks": {"update_symbol_lists": {"enabled": true, "cron": "0 8 * * 1-5", "description": "每日更新所有代码表", "priority": "CRITICAL", "max_retries": 3, "timeout_seconds": 600, "parameters": {"force_update": false, "symbol_types": ["stock", "index", "futures", "fund", "bond"]}}, "stock_data_update": {"enabled": true, "cron": "0 18 * * 1-5", "description": "股票数据全量更新", "symbols": "auto_from_list", "priority": "HIGH", "max_retries": 3, "timeout_seconds": 3600, "parameters": {"data_types": ["kline_D", "kline_60", "kline_30"], "update_days": 30, "batch_size": 50, "symbol_source": "stock_list"}}, "index_data_update": {"enabled": true, "cron": "5 18 * * 1-5", "description": "指数数据全量更新", "symbols": "auto_from_list", "priority": "HIGH", "max_retries": 3, "timeout_seconds": 1800, "parameters": {"data_types": ["kline_D", "kline_60", "kline_30"], "update_days": 30, "batch_size": 20, "symbol_source": "index_list"}}, "futures_data_update": {"enabled": true, "cron": "10 18 * * 1-5", "description": "期货数据全量更新", "symbols": "auto_from_list", "priority": "HIGH", "max_retries": 3, "timeout_seconds": 2400, "parameters": {"data_types": ["kline_D", "kline_60", "kline_30"], "update_days": 30, "batch_size": 30, "symbol_source": "futures_list"}}, "fund_data_update": {"enabled": true, "cron": "15 18 * * 1-5", "description": "基金数据全量更新", "symbols": "auto_from_list", "priority": "NORMAL", "max_retries": 3, "timeout_seconds": 1800, "parameters": {"data_types": ["kline_D"], "update_days": 30, "batch_size": 40, "symbol_source": "fund_list"}}, "weekend_history_backfill": {"enabled": true, "cron": "0 2 * * 6", "description": "周末历史数据补全", "symbols": ["000001", "000002", "000300", "399001", "399006"], "priority": "NORMAL", "max_retries": 5, "timeout_seconds": 7200, "parameters": {"backfill_days": 30, "data_types": ["kline_D", "kline_60", "kline_30", "kline_15", "kline_5"]}}, "realtime_stock_update": {"enabled": true, "cron": "*/5 9-15 * * 1-5", "description": "交易时间内股票实时数据更新", "symbols": "auto_from_list", "priority": "HIGH", "max_retries": 2, "timeout_seconds": 300, "parameters": {"data_types": ["realtime", "kline_5"], "symbol_source": "stock_list", "batch_size": 100, "sample_size": 500}}, "realtime_index_update": {"enabled": true, "cron": "*/5 9-15 * * 1-5", "description": "交易时间内指数实时数据更新", "symbols": "auto_from_list", "priority": "HIGH", "max_retries": 2, "timeout_seconds": 180, "parameters": {"data_types": ["realtime", "kline_5"], "symbol_source": "index_list", "batch_size": 50}}, "minute_data_update": {"enabled": true, "cron": "*/15 9-15 * * 1-5", "description": "交易时间内分钟线数据更新", "symbols": "auto_from_list", "priority": "NORMAL", "max_retries": 2, "timeout_seconds": 600, "parameters": {"data_types": ["kline_15", "kline_30"], "symbol_source": "stock_list", "batch_size": 50, "sample_size": 1000}}, "data_quality_check": {"enabled": true, "cron": "0 2 * * *", "description": "每日数据质量检查", "priority": "NORMAL", "max_retries": 3, "timeout_seconds": 1800, "parameters": {"check_days": 7, "quality_threshold": 0.95, "generate_report": true}}, "weekly_data_cleanup": {"enabled": true, "cron": "0 3 * * 0", "description": "每周数据清理", "priority": "LOW", "max_retries": 2, "timeout_seconds": 3600, "parameters": {"cleanup_types": ["expired_cache", "duplicate_data", "invalid_records"], "retention_days": 90}}, "system_health_check": {"enabled": true, "cron": "*/5 * * * *", "description": "系统健康检查", "priority": "CRITICAL", "max_retries": 1, "timeout_seconds": 60, "parameters": {"check_components": ["pytdx", "storage", "scheduler"], "alert_threshold": 0.8}}, "monthly_data_archive": {"enabled": true, "cron": "0 1 1 * *", "description": "每月数据归档", "priority": "LOW", "max_retries": 3, "timeout_seconds": 7200, "parameters": {"archive_months": 3, "compress": true, "verify_integrity": true}}}, "symbol_management": {"auto_update_enabled": true, "cache_expire_hours": 24, "supported_types": {"stock": {"enabled": true, "markets": [0, 1], "filters": [], "description": "股票代码表"}, "index": {"enabled": true, "markets": [0, 1], "filters": ["000", "399", "880"], "description": "指数代码表"}, "futures": {"enabled": true, "markets": [28, 29, 30, 31], "filters": [], "description": "期货代码表"}, "fund": {"enabled": true, "markets": [0, 1], "filters": ["15", "16", "18", "50", "51"], "description": "基金代码表"}, "bond": {"enabled": true, "markets": [0, 1], "filters": ["10", "11", "12", "13"], "description": "债券代码表"}}}, "data_types": {"kline_D": {"enabled": true, "priority": 9, "description": "日线数据"}, "kline_60": {"enabled": true, "priority": 8, "description": "60分钟线"}, "kline_30": {"enabled": true, "priority": 7, "description": "30分钟线"}, "kline_15": {"enabled": true, "priority": 6, "description": "15分钟线"}, "kline_5": {"enabled": true, "priority": 5, "description": "5分钟线"}, "kline_1": {"enabled": false, "priority": 4, "description": "1分钟线(数据量大)"}, "tick": {"enabled": false, "priority": 3, "description": "tick数据(数据量极大)"}, "realtime": {"enabled": true, "priority": 10, "description": "实时行情"}, "financial": {"enabled": true, "priority": 4, "description": "财务数据"}, "company_info": {"enabled": true, "priority": 3, "description": "公司信息"}}, "pytdx": {"batch_size": 800, "concurrent_requests": 5, "request_interval": 0.1, "connect_timeout": 10, "max_retries": 3, "archive_enabled": true, "cache_enabled": true, "cache_expire_minutes": 60, "hq_servers": [{"ip": "*************", "port": 7709}, {"ip": "***************", "port": 7709}, {"ip": "*************", "port": 7709}, {"ip": "*************", "port": 7709}, {"ip": "**************", "port": 7709}, {"ip": "*************", "port": 7709}]}, "storage": {"type": "default", "redis_enabled": true, "clickhouse_enabled": true, "batch_size": 1000, "max_retries": 3, "timeout_seconds": 30}, "failure_handling": {"max_retries": 3, "base_delay_seconds": 60, "max_delay_seconds": 3600, "exponential_backoff": true, "jitter": true, "alert_on_failure": true, "failure_threshold": 5}, "monitoring": {"enabled": true, "metrics_interval": 60, "log_performance": true, "alert_channels": ["log", "email"], "performance_thresholds": {"task_duration_warning": 1800, "task_duration_critical": 3600, "failure_rate_warning": 0.1, "failure_rate_critical": 0.2}}, "logging": {"level": "INFO", "file": "logs/scheduler_service.log", "max_size_mb": 100, "backup_count": 10, "format": "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s"}}