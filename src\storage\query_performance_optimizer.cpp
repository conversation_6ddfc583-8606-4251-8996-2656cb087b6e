#include "query_performance_optimizer.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <random>

namespace financial_data {

QueryPerformanceOptimizer::QueryPerformanceOptimizer(const QueryOptimizationConfig& config)
    : config_(config) {
    InitializeLogger();
}

QueryPerformanceOptimizer::~QueryPerformanceOptimizer() {
    Shutdown();
}

void QueryPerformanceOptimizer::InitializeLogger() {
    logger_ = spdlog::get("query_performance_optimizer");
    if (!logger_) {
        logger_ = spdlog::stdout_color_mt("query_performance_optimizer");
        logger_->set_level(spdlog::level::info);
    }
}

bool QueryPerformanceOptimizer::Initialize(std::shared_ptr<UnifiedDataAccessInterface> data_access) {
    if (running_.load()) {
        logger_->warn("QueryPerformanceOptimizer already initialized");
        return true;
    }
    
    if (!data_access) {
        logger_->error("Invalid data access interface provided");
        return false;
    }
    
    data_access_ = data_access;
    
    // 启动工作线程
    StartWorkerThreads();
    
    running_.store(true);
    logger_->info("QueryPerformanceOptimizer initialized successfully");
    return true;
}

void QueryPerformanceOptimizer::Shutdown() {
    if (!running_.load()) {
        return;
    }
    
    shutdown_requested_.store(true);
    
    // 停止工作线程
    StopWorkerThreads();
    
    // 清理缓存
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        advanced_cache_.clear();
    }
    
    // 清理分页游标
    {
        std::lock_guard<std::mutex> lock(cursor_mutex_);
        pagination_cursors_.clear();
    }
    
    running_.store(false);
    logger_->info("QueryPerformanceOptimizer shutdown completed");
}

void QueryPerformanceOptimizer::StartWorkerThreads() {
    // 启动批量查询工作线程
    if (config_.enable_batch_optimization) {
        for (size_t i = 0; i < config_.max_concurrent_batches; ++i) {
            batch_workers_.emplace_back([this]() {
                ProcessBatchQueries();
            });
        }
    }
    
    // 启动预取工作线程
    if (config_.enable_prefetching) {
        prefetch_worker_ = std::thread([this]() {
            ProcessPrefetching();
        });
    }
    
    // 启动查询合并工作线程
    if (config_.enable_query_coalescing) {
        coalescing_worker_ = std::thread([this]() {
            ProcessQueryCoalescing();
        });
    }
    
    // 启动性能监控线程
    if (config_.enable_performance_monitoring) {
        monitoring_worker_ = std::thread([this]() {
            while (!shutdown_requested_.load()) {
                MonitorPerformance();
                std::this_thread::sleep_for(config_.monitoring_interval);
            }
        });
    }
}

void QueryPerformanceOptimizer::StopWorkerThreads() {
    // 通知所有工作线程停止
    batch_cv_.notify_all();
    
    // 等待批量查询工作线程结束
    for (auto& worker : batch_workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    batch_workers_.clear();
    
    // 等待预取工作线程结束
    if (prefetch_worker_.joinable()) {
        prefetch_worker_.join();
    }
    
    // 等待查询合并工作线程结束
    if (coalescing_worker_.joinable()) {
        coalescing_worker_.join();
    }
    
    // 等待性能监控线程结束
    if (monitoring_worker_.joinable()) {
        monitoring_worker_.join();
    }
}

std::future<QueryResponse> QueryPerformanceOptimizer::OptimizedQuery(const QueryRequest& request) {
    auto promise = std::make_shared<std::promise<QueryResponse>>();
    auto future = promise->get_future();
    
    if (!running_.load()) {
        QueryResponse error_response;
        error_response.storage_source = "optimizer_error";
        promise->set_value(std::move(error_response));
        return future;
    }
    
    // 异步处理优化查询
    std::thread([this, request, promise]() {
        auto start_time = std::chrono::steady_clock::now();
        
        try {
            // 生成查询哈希
            std::string query_hash = GenerateQueryHash(request);
            
            // 尝试从缓存获取
            QueryResponse cached_response;
            auto cache_start = std::chrono::steady_clock::now();
            bool cache_hit = GetFromCache(query_hash, cached_response);
            auto cache_end = std::chrono::steady_clock::now();
            
            double cache_lookup_time = std::chrono::duration<double, std::milli>(
                cache_end - cache_start).count();
            metrics_.UpdateCacheMetrics(cache_hit, cache_lookup_time);
            
            if (cache_hit) {
                promise->set_value(std::move(cached_response));
                return;
            }
            
            // 缓存未命中，执行实际查询
            auto query_future = data_access_->QueryData(request);
            QueryResponse response = query_future.get();
            
            // 将结果放入缓存
            if (!response.IsEmpty() && response.storage_source != "error") {
                PutToCache(query_hash, response);
            }
            
            // 分析查询模式
            AnalyzeQueryPattern(request);
            
            // 检查是否需要预取
            if (config_.enable_prefetching && ShouldPrefetch(request.symbol)) {
                TriggerPrefetch(request);
            }
            
            promise->set_value(std::move(response));
            
        } catch (const std::exception& e) {
            logger_->error("Optimized query failed: {}", e.what());
            QueryResponse error_response;
            error_response.storage_source = "optimizer_error";
            promise->set_value(std::move(error_response));
        }
        
        // 更新性能指标
        auto end_time = std::chrono::steady_clock::now();
        double query_time = std::chrono::duration<double, std::milli>(
            end_time - start_time).count();
        bool is_slow = query_time > config_.slow_query_threshold_ms;
        
        metrics_.UpdateQueryMetrics(query_time, is_slow);
        
        if (is_slow) {
            RecordSlowQuery(request, query_time);
        }
        
    }).detach();
    
    return future;
}

std::future<std::vector<QueryResponse>> QueryPerformanceOptimizer::BatchQuery(
    const std::vector<QueryRequest>& requests) {
    
    auto promise = std::make_shared<std::promise<std::vector<QueryResponse>>>();
    auto future = promise->get_future();
    
    if (!running_.load() || !config_.enable_batch_optimization) {
        std::vector<QueryResponse> error_responses(requests.size());
        for (auto& response : error_responses) {
            response.storage_source = "batch_error";
        }
        promise->set_value(std::move(error_responses));
        return future;
    }
    
    // 创建批量查询请求
    BatchQueryRequest batch_request;
    batch_request.requests = requests;
    batch_request.promise = std::move(*promise);
    
    // 添加到批量查询队列
    {
        std::lock_guard<std::mutex> lock(batch_mutex_);
        batch_queue_.push(std::move(batch_request));
    }
    batch_cv_.notify_one();
    
    metrics_.batch_queries++;
    
    return future;
}

std::future<QueryResponse> QueryPerformanceOptimizer::PaginatedQuery(
    const QueryRequest& request, const std::string& cursor) {
    
    auto promise = std::make_shared<std::promise<QueryResponse>>();
    auto future = promise->get_future();
    
    if (!running_.load() || !config_.enable_pagination) {
        QueryResponse error_response;
        error_response.storage_source = "pagination_error";
        promise->set_value(std::move(error_response));
        return future;
    }
    
    // 异步处理分页查询
    std::thread([this, request, cursor, promise]() {
        try {
            QueryResponse response = ProcessPaginatedQuery(request, cursor);
            promise->set_value(std::move(response));
            metrics_.paginated_queries++;
        } catch (const std::exception& e) {
            logger_->error("Paginated query failed: {}", e.what());
            QueryResponse error_response;
            error_response.storage_source = "pagination_error";
            promise->set_value(std::move(error_response));
        }
    }).detach();
    
    return future;
}

std::string QueryPerformanceOptimizer::CreatePaginationCursor(const QueryRequest& request) {
    std::lock_guard<std::mutex> lock(cursor_mutex_);
    
    PaginationCursor cursor;
    cursor.original_request = request;
    
    std::string cursor_id = cursor.cursor_id;
    pagination_cursors_[cursor_id] = std::move(cursor);
    
    return cursor_id;
}

bool QueryPerformanceOptimizer::ValidateCursor(const std::string& cursor) {
    std::lock_guard<std::mutex> lock(cursor_mutex_);
    
    auto it = pagination_cursors_.find(cursor);
    if (it != pagination_cursors_.end()) {
        if (!it->second.IsExpired(config_.cursor_ttl)) {
            it->second.UpdateAccess();
            return true;
        } else {
            // 删除过期游标
            pagination_cursors_.erase(it);
        }
    }
    
    return false;
}

QueryResponse QueryPerformanceOptimizer::ProcessPaginatedQuery(
    const QueryRequest& request, const std::string& cursor) {
    
    QueryRequest actual_request = request;
    size_t current_offset = 0;
    
    // 如果提供了游标，使用游标信息
    if (!cursor.empty()) {
        std::lock_guard<std::mutex> lock(cursor_mutex_);
        auto it = pagination_cursors_.find(cursor);
        if (it != pagination_cursors_.end() && !it->second.IsExpired(config_.cursor_ttl)) {
            actual_request = it->second.original_request;
            current_offset = it->second.current_offset;
            it->second.UpdateAccess();
        } else {
            // 游标无效或过期
            QueryResponse error_response;
            error_response.storage_source = "invalid_cursor";
            return error_response;
        }
    }
    
    // 调整查询参数以实现分页
    size_t page_size = std::min(static_cast<size_t>(actual_request.limit), config_.max_page_size);
    actual_request.limit = static_cast<int>(page_size + current_offset);
    
    // 执行查询
    auto query_future = data_access_->QueryData(actual_request);
    QueryResponse response = query_future.get();
    
    // 处理分页结果
    if (!response.IsEmpty() && response.ticks.size() > current_offset) {
        // 提取当前页的数据
        std::vector<StandardTick> page_ticks;
        size_t start_idx = current_offset;
        size_t end_idx = std::min(start_idx + page_size, response.ticks.size());
        
        page_ticks.assign(response.ticks.begin() + start_idx, 
                         response.ticks.begin() + end_idx);
        
        response.ticks = std::move(page_ticks);
        
        // 更新游标信息
        if (end_idx < response.total_records) {
            std::lock_guard<std::mutex> lock(cursor_mutex_);
            if (!cursor.empty()) {
                auto it = pagination_cursors_.find(cursor);
                if (it != pagination_cursors_.end()) {
                    it->second.current_offset = end_idx;
                }
            }
            
            // 创建下一页游标
            PaginationCursor next_cursor;
            next_cursor.original_request = actual_request;
            next_cursor.current_offset = end_idx;
            next_cursor.total_estimated = response.total_records;
            
            response.next_cursor = next_cursor.cursor_id;
            pagination_cursors_[next_cursor.cursor_id] = std::move(next_cursor);
            response.has_more = true;
        } else {
            response.has_more = false;
        }
    }
    
    return response;
}

void QueryPerformanceOptimizer::ProcessBatchQueries() {
    while (!shutdown_requested_.load()) {
        std::unique_lock<std::mutex> lock(batch_mutex_);
        batch_cv_.wait(lock, [this]() {
            return !batch_queue_.empty() || shutdown_requested_.load();
        });
        
        if (shutdown_requested_.load()) {
            break;
        }
        
        if (!batch_queue_.empty()) {
            BatchQueryRequest batch_request = std::move(batch_queue_.front());
            batch_queue_.pop();
            lock.unlock();
            
            ExecuteBatchQuery(batch_request);
        }
    }
}

void QueryPerformanceOptimizer::ExecuteBatchQuery(const BatchQueryRequest& batch_request) {
    try {
        std::vector<QueryResponse> responses;
        responses.reserve(batch_request.requests.size());
        
        // 检查缓存中的查询
        std::vector<std::future<QueryResponse>> futures;
        std::vector<bool> cache_hits(batch_request.requests.size(), false);
        
        for (size_t i = 0; i < batch_request.requests.size(); ++i) {
            const auto& request = batch_request.requests[i];
            std::string query_hash = GenerateQueryHash(request);
            
            QueryResponse cached_response;
            if (GetFromCache(query_hash, cached_response)) {
                responses.push_back(std::move(cached_response));
                cache_hits[i] = true;
            } else {
                // 缓存未命中，添加到待查询列表
                futures.push_back(data_access_->QueryData(request));
                responses.emplace_back(); // 占位符
            }
        }
        
        // 等待所有查询完成
        size_t future_idx = 0;
        for (size_t i = 0; i < batch_request.requests.size(); ++i) {
            if (!cache_hits[i]) {
                responses[i] = futures[future_idx].get();
                
                // 将结果放入缓存
                if (!responses[i].IsEmpty() && responses[i].storage_source != "error") {
                    std::string query_hash = GenerateQueryHash(batch_request.requests[i]);
                    PutToCache(query_hash, responses[i]);
                }
                
                future_idx++;
            }
        }
        
        // 计算批量效率
        size_t cache_hit_count = std::count(cache_hits.begin(), cache_hits.end(), true);
        double efficiency = double(cache_hit_count) / batch_request.requests.size();
        metrics_.batch_efficiency.store(efficiency);
        
        // 返回结果
        batch_request.promise.set_value(std::move(responses));
        
    } catch (const std::exception& e) {
        logger_->error("Batch query execution failed: {}", e.what());
        std::vector<QueryResponse> error_responses(batch_request.requests.size());
        for (auto& response : error_responses) {
            response.storage_source = "batch_error";
        }
        batch_request.promise.set_value(std::move(error_responses));
    }
}

std::future<void> QueryPerformanceOptimizer::TriggerPrefetch(const QueryRequest& base_request) {
    auto promise = std::make_shared<std::promise<void>>();
    auto future = promise->get_future();
    
    if (!config_.enable_prefetching) {
        promise->set_value();
        return future;
    }
    
    // 异步执行预取
    std::thread([this, base_request, promise]() {
        try {
            auto prefetch_requests = GeneratePrefetchRequests(base_request);
            
            for (const auto& request : prefetch_requests) {
                std::string query_hash = GenerateQueryHash(request);
                
                // 检查是否已在缓存中
                QueryResponse cached_response;
                if (!GetFromCache(query_hash, cached_response)) {
                    // 执行预取查询
                    auto query_future = data_access_->QueryData(request);
                    QueryResponse response = query_future.get();
                    
                    if (!response.IsEmpty() && response.storage_source != "error") {
                        PutToCache(query_hash, response);
                        metrics_.prefetch_queries++;
                    }
                }
            }
            
            promise->set_value();
            
        } catch (const std::exception& e) {
            logger_->error("Prefetch failed: {}", e.what());
            promise->set_value();
        }
    }).detach();
    
    return future;
}

std::vector<QueryRequest> QueryPerformanceOptimizer::GeneratePrefetchRequests(
    const QueryRequest& base_request) {
    
    std::vector<QueryRequest> prefetch_requests;
    
    // 基于时间的预取：预取相邻时间段的数据
    int64_t time_range = base_request.end_timestamp_ns - base_request.start_timestamp_ns;
    int64_t prefetch_range = static_cast<int64_t>(time_range * 0.5); // 预取50%的时间范围
    
    // 预取之前的时间段
    QueryRequest prev_request = base_request;
    prev_request.end_timestamp_ns = base_request.start_timestamp_ns;
    prev_request.start_timestamp_ns = base_request.start_timestamp_ns - prefetch_range;
    prev_request.limit = static_cast<int>(config_.prefetch_size);
    prefetch_requests.push_back(prev_request);
    
    // 预取之后的时间段
    QueryRequest next_request = base_request;
    next_request.start_timestamp_ns = base_request.end_timestamp_ns;
    next_request.end_timestamp_ns = base_request.end_timestamp_ns + prefetch_range;
    next_request.limit = static_cast<int>(config_.prefetch_size);
    prefetch_requests.push_back(next_request);
    
    return prefetch_requests;
}

bool QueryPerformanceOptimizer::ShouldPrefetch(const std::string& symbol) const {
    // 检查预取是否启用
    std::lock_guard<std::mutex> lock(prefetch_mutex_);
    auto it = prefetch_enabled_.find(symbol);
    if (it != prefetch_enabled_.end()) {
        return it->second;
    }
    
    // 默认基于缓存命中率决定是否预取
    return metrics_.cache_hit_ratio.load() < config_.prefetch_threshold;
}

void QueryPerformanceOptimizer::ProcessPrefetching() {
    while (!shutdown_requested_.load()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // 处理预取队列
        std::lock_guard<std::mutex> lock(prefetch_mutex_);
        while (!prefetch_queue_.empty()) {
            QueryRequest request = prefetch_queue_.front();
            prefetch_queue_.pop();
            
            // 异步执行预取
            TriggerPrefetch(request);
        }
    }
}

void QueryPerformanceOptimizer::ProcessQueryCoalescing() {
    while (!shutdown_requested_.load()) {
        std::this_thread::sleep_for(config_.coalescing_window);
        
        std::lock_guard<std::mutex> lock(coalescing_mutex_);
        
        // 处理待合并的查询
        for (auto& [pattern, queries] : pending_queries_) {
            if (queries.size() > 1) {
                // 合并相似查询
                QueryRequest merged_request = MergeQueryRequests(queries);
                
                // 执行合并后的查询
                auto query_future = data_access_->QueryData(merged_request);
                QueryResponse response = query_future.get();
                
                // 将结果分发给所有原始查询
                for (const auto& original_request : queries) {
                    std::string query_hash = GenerateQueryHash(original_request);
                    PutToCache(query_hash, response);
                }
                
                metrics_.coalesced_queries += queries.size();
            }
        }
        
        pending_queries_.clear();
    }
}

QueryRequest QueryPerformanceOptimizer::MergeQueryRequests(
    const std::vector<QueryRequest>& requests) const {
    
    if (requests.empty()) {
        return QueryRequest{};
    }
    
    QueryRequest merged = requests[0];
    
    // 合并时间范围
    for (const auto& request : requests) {
        merged.start_timestamp_ns = std::min(merged.start_timestamp_ns, request.start_timestamp_ns);
        merged.end_timestamp_ns = std::max(merged.end_timestamp_ns, request.end_timestamp_ns);
        merged.limit = std::max(merged.limit, request.limit);
    }
    
    return merged;
}

std::string QueryPerformanceOptimizer::GenerateQueryHash(const QueryRequest& request) const {
    std::ostringstream oss;
    oss << request.symbol << "|" << request.exchange << "|" << request.data_type << "|"
        << request.start_timestamp_ns << "|" << request.end_timestamp_ns << "|"
        << request.limit << "|" << request.cursor << "|" << request.include_level2;
    
    // 简单哈希
    std::hash<std::string> hasher;
    return std::to_string(hasher(oss.str()));
}

bool QueryPerformanceOptimizer::GetFromCache(const std::string& query_hash, QueryResponse& response) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    auto it = advanced_cache_.find(query_hash);
    if (it != advanced_cache_.end()) {
        if (!it->second.IsExpired(config_.cache_ttl)) {
            response = it->second.response;
            it->second.UpdateAccess();
            return true;
        } else {
            // 删除过期缓存
            advanced_cache_.erase(it);
        }
    }
    
    return false;
}

void QueryPerformanceOptimizer::PutToCache(const std::string& query_hash, const QueryResponse& response) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    // 检查缓存大小限制
    if (advanced_cache_.size() >= config_.max_cache_size) {
        ApplyCacheStrategy();
    }
    
    AdvancedCacheItem item;
    item.response = response;
    item.timestamp = std::chrono::steady_clock::now();
    item.last_access = item.timestamp;
    item.query_hash = query_hash;
    item.priority_score = item.CalculatePriorityScore();
    
    advanced_cache_[query_hash] = std::move(item);
}

void QueryPerformanceOptimizer::ApplyCacheStrategy() {
    // 根据配置的缓存策略进行缓存淘汰
    size_t target_size = config_.max_cache_size * 0.8; // 淘汰到80%容量
    
    switch (config_.cache_strategy) {
        case CacheStrategy::LRU: {
            // 最近最少使用策略
            std::vector<std::pair<std::string, std::chrono::steady_clock::time_point>> items;
            for (const auto& [hash, item] : advanced_cache_) {
                items.emplace_back(hash, item.last_access);
            }
            
            std::sort(items.begin(), items.end(),
                     [](const auto& a, const auto& b) {
                         return a.second < b.second;
                     });
            
            size_t to_remove = advanced_cache_.size() - target_size;
            for (size_t i = 0; i < to_remove && i < items.size(); ++i) {
                advanced_cache_.erase(items[i].first);
            }
            break;
        }
        
        case CacheStrategy::LFU: {
            // 最少使用频率策略
            std::vector<std::pair<std::string, uint32_t>> items;
            for (const auto& [hash, item] : advanced_cache_) {
                items.emplace_back(hash, item.access_count.load());
            }
            
            std::sort(items.begin(), items.end(),
                     [](const auto& a, const auto& b) {
                         return a.second < b.second;
                     });
            
            size_t to_remove = advanced_cache_.size() - target_size;
            for (size_t i = 0; i < to_remove && i < items.size(); ++i) {
                advanced_cache_.erase(items[i].first);
            }
            break;
        }
        
        case CacheStrategy::TTL: {
            // 基于时间过期策略
            auto now = std::chrono::steady_clock::now();
            auto it = advanced_cache_.begin();
            while (it != advanced_cache_.end()) {
                if (it->second.IsExpired(config_.cache_ttl)) {
                    it = advanced_cache_.erase(it);
                } else {
                    ++it;
                }
            }
            break;
        }
        
        case CacheStrategy::ADAPTIVE: {
            // 自适应策略：基于优先级分数
            std::vector<std::pair<std::string, double>> items;
            for (auto& [hash, item] : advanced_cache_) {
                item.priority_score = item.CalculatePriorityScore();
                items.emplace_back(hash, item.priority_score);
            }
            
            std::sort(items.begin(), items.end(),
                     [](const auto& a, const auto& b) {
                         return a.second < b.second;
                     });
            
            size_t to_remove = advanced_cache_.size() - target_size;
            for (size_t i = 0; i < to_remove && i < items.size(); ++i) {
                advanced_cache_.erase(items[i].first);
            }
            break;
        }
    }
}

void QueryPerformanceOptimizer::MonitorPerformance() {
    UpdatePerformanceMetrics();
    CleanupExpiredCache();
    CleanupExpiredCursors();
    
    // 记录性能指标
    logger_->info("Performance metrics - Total queries: {}, Cache hit ratio: {:.2f}%, "
                 "Avg query time: {:.2f}ms, Slow queries: {}",
                 metrics_.total_queries.load(),
                 metrics_.cache_hit_ratio.load() * 100,
                 metrics_.avg_query_time_ms.load(),
                 metrics_.slow_queries.load());
}

void QueryPerformanceOptimizer::UpdatePerformanceMetrics() {
    // 更新缓存命中率
    uint64_t hits = metrics_.cache_hits.load();
    uint64_t misses = metrics_.cache_misses.load();
    uint64_t total = hits + misses;
    
    if (total > 0) {
        double hit_ratio = double(hits) / total;
        metrics_.cache_hit_ratio.store(hit_ratio);
    }
}

void QueryPerformanceOptimizer::CleanupExpiredCache() {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    auto it = advanced_cache_.begin();
    while (it != advanced_cache_.end()) {
        if (it->second.IsExpired(config_.cache_ttl)) {
            it = advanced_cache_.erase(it);
        } else {
            ++it;
        }
    }
}

void QueryPerformanceOptimizer::CleanupExpiredCursors() {
    std::lock_guard<std::mutex> lock(cursor_mutex_);
    
    auto it = pagination_cursors_.begin();
    while (it != pagination_cursors_.end()) {
        if (it->second.IsExpired(config_.cursor_ttl)) {
            it = pagination_cursors_.erase(it);
        } else {
            ++it;
        }
    }
}

void QueryPerformanceOptimizer::RecordSlowQuery(const QueryRequest& request, double response_time_ms) {
    std::lock_guard<std::mutex> lock(slow_queries_mutex_);
    
    slow_queries_.emplace_back(request, response_time_ms);
    
    // 限制慢查询记录数量
    if (slow_queries_.size() > 1000) {
        slow_queries_.erase(slow_queries_.begin(), slow_queries_.begin() + 100);
    }
}

void QueryPerformanceOptimizer::AnalyzeQueryPattern(const QueryRequest& request) {
    std::string pattern = ExtractQueryPattern(request);
    
    std::lock_guard<std::mutex> lock(pattern_mutex_);
    auto& analysis = query_patterns_[pattern];
    analysis.query_pattern = pattern;
    analysis.frequency++;
}

std::string QueryPerformanceOptimizer::ExtractQueryPattern(const QueryRequest& request) const {
    std::ostringstream oss;
    oss << request.symbol << "|" << request.data_type << "|" << request.exchange;
    return oss.str();
}

QueryPerformanceOptimizer::QueryPerformanceMetrics QueryPerformanceOptimizer::GetMetrics() const {
    return metrics_;
}

void QueryPerformanceOptimizer::ResetMetrics() {
    metrics_.Reset();
}

size_t QueryPerformanceOptimizer::GetCacheSize() const {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    return advanced_cache_.size();
}

double QueryPerformanceOptimizer::GetCacheHitRatio() const {
    return metrics_.cache_hit_ratio.load();
}

void QueryPerformanceOptimizer::InvalidateCache(const std::string& pattern) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    if (pattern.empty()) {
        // 清空所有缓存
        advanced_cache_.clear();
    } else {
        // 根据模式删除缓存
        auto it = advanced_cache_.begin();
        while (it != advanced_cache_.end()) {
            if (it->first.find(pattern) != std::string::npos) {
                it = advanced_cache_.erase(it);
            } else {
                ++it;
            }
        }
    }
}

std::vector<QueryPerformanceOptimizer::QueryAnalysis> 
QueryPerformanceOptimizer::AnalyzeQueryPatterns() const {
    std::lock_guard<std::mutex> lock(pattern_mutex_);
    
    std::vector<QueryAnalysis> analyses;
    for (const auto& [pattern, analysis] : query_patterns_) {
        analyses.push_back(analysis);
    }
    
    // 按频率排序
    std::sort(analyses.begin(), analyses.end(),
             [](const QueryAnalysis& a, const QueryAnalysis& b) {
                 return a.frequency > b.frequency;
             });
    
    return analyses;
}

// 工厂方法实现
std::unique_ptr<QueryPerformanceOptimizer> QueryPerformanceOptimizerFactory::CreateDefault() {
    QueryOptimizationConfig config;
    return std::make_unique<QueryPerformanceOptimizer>(config);
}

std::unique_ptr<QueryPerformanceOptimizer> QueryPerformanceOptimizerFactory::CreateHighThroughput() {
    QueryOptimizationConfig config;
    config.cache_strategy = CacheStrategy::ADAPTIVE;
    config.max_cache_size = 50000;
    config.enable_batch_optimization = true;
    config.batch_size = 2000;
    config.max_concurrent_batches = 20;
    config.enable_query_coalescing = true;
    return std::make_unique<QueryPerformanceOptimizer>(config);
}

std::unique_ptr<QueryPerformanceOptimizer> QueryPerformanceOptimizerFactory::CreateLowLatency() {
    QueryOptimizationConfig config;
    config.cache_strategy = CacheStrategy::LRU;
    config.max_cache_size = 100000;
    config.cache_ttl = std::chrono::minutes(15);
    config.enable_prefetching = true;
    config.prefetch_size = 1000;
    config.batch_timeout = std::chrono::milliseconds(10);
    return std::make_unique<QueryPerformanceOptimizer>(config);
}

} // namespace financial_data