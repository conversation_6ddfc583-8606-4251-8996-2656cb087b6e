@echo off
REM 金融数据服务系统Web管理界面启动脚本
REM Financial Data Service System Web Admin Startup Script

echo === 金融数据服务系统Web管理界面启动 ===
echo === Financial Data Service Web Admin Startup ===

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker未安装，请先安装Docker
    echo Error: Docker is not installed
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker Compose未安装，请先安装Docker Compose
    echo Error: Docker Compose is not installed
    pause
    exit /b 1
)

REM 创建必要的目录
echo 创建必要的目录...
if not exist ssl mkdir ssl
if not exist logs mkdir logs

REM 设置环境变量
if not defined JWT_SECRET_KEY (
    set JWT_SECRET_KEY=your-secret-key-change-in-production
)
if not defined DB_PASSWORD (
    set DB_PASSWORD=password123
)

REM 启动服务
echo 启动Docker服务...
docker-compose up -d

REM 等待服务启动
echo 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 检查服务状态...
docker-compose ps

REM 显示访问信息
echo.
echo === 服务启动完成 ===
echo === Services Started Successfully ===
echo.
echo Web管理界面访问地址:
echo HTTP:  http://localhost
echo HTTPS: https://localhost
echo.
echo 默认登录信息:
echo 用户名: admin
echo 密码:   admin123
echo.
echo API文档地址:
echo http://localhost/api/docs
echo.
echo 查看日志:
echo docker-compose logs -f
echo.
echo 停止服务:
echo stop.bat
echo.

REM 显示重要安全提示
echo === 安全提示 ===
echo 1. 请立即修改默认管理员密码
echo 2. 在生产环境中使用有效的SSL证书
echo 3. 配置防火墙规则限制访问
echo 4. 定期备份数据库
echo 5. 监控系统日志

pause