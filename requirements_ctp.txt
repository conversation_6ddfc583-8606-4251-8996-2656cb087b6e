# CTP数据采集器专用依赖包

# 核心数据采集
pytdx>=1.72                    # 通达信数据接口
pandas>=2.0.0                  # 数据处理
numpy>=1.20.0                  # 数值计算

# 异步和并发
asyncio-mqtt>=0.13.0           # MQTT异步客户端
aiohttp>=3.8.0                 # 异步HTTP客户端
aiofiles>=23.0.0               # 异步文件操作

# 数据存储
redis>=4.5.0                   # Redis客户端
clickhouse-driver>=0.2.6       # ClickHouse客户端
sqlalchemy>=2.0.0              # SQL ORM
asyncpg>=0.28.0                # PostgreSQL异步客户端

# 消息队列
pika>=1.3.0                    # RabbitMQ客户端
aiokafka>=0.8.0                # Kafka异步客户端

# 配置和日志
pydantic>=2.0.0                # 数据验证
python-dotenv>=1.0.0           # 环境变量
loguru>=0.7.0                  # 高级日志

# 时间和调度
croniter>=1.4.0                # Cron表达式解析
schedule>=1.2.0                # 任务调度
pytz>=2023.3                   # 时区处理

# 网络和安全
requests>=2.31.0               # HTTP请求
cryptography>=41.0.0           # 加密库
certifi>=2023.7.0              # SSL证书

# 监控和指标
prometheus-client>=0.17.0      # Prometheus指标
psutil>=5.9.0                  # 系统监控

# 开发和测试
pytest>=7.4.0                 # 测试框架
pytest-asyncio>=0.21.0        # 异步测试
black>=23.0.0                  # 代码格式化
flake8>=6.0.0                  # 代码检查

# 数据序列化
msgpack>=1.0.0                 # 高效序列化
orjson>=3.9.0                  # 快速JSON

# 工具库
click>=8.1.0                   # 命令行工具
tqdm>=4.66.0                   # 进度条
rich>=13.0.0                   # 富文本输出