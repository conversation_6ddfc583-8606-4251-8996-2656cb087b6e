# Market Data Collection Enhancement - API Documentation

## Overview

This document provides comprehensive API documentation for the Market Data Collection Enhancement system. The system provides both HTTP REST API and gRPC API for accessing market data and managing the collection system.

## Base URLs

- **HTTP API**: `http://localhost:8080` (or your deployed endpoint)
- **gRPC API**: `localhost:8081` (or your deployed endpoint)
- **Metrics**: `http://localhost:9090/metrics` (Prometheus format)

## Authentication

Currently, the system uses basic authentication. Include the following header in your requests:

```
Authorization: Bearer <your-api-token>
```

## HTTP REST API

### Health Check Endpoints

#### GET /health
Returns the overall health status of the system.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "components": {
    "redis": "healthy",
    "clickhouse": "healthy",
    "minio": "healthy",
    "pytdx_collector": "healthy",
    "ctp_collector": "healthy"
  },
  "uptime_seconds": 3600
}
```

#### GET /ready
Returns readiness status for load balancer health checks.

**Response:**
```json
{
  "ready": true,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Market Data Endpoints

#### GET /api/v1/ticks/{symbol}
Retrieve tick data for a specific symbol.

**Parameters:**
- `symbol` (path): Stock symbol (e.g., "000001.SZ")
- `start_time` (query): Start timestamp in ISO 8601 format
- `end_time` (query): End timestamp in ISO 8601 format
- `limit` (query): Maximum number of records (default: 1000, max: 10000)
- `cursor` (query): Pagination cursor for next page

**Example Request:**
```bash
curl "http://localhost:8080/api/v1/ticks/000001.SZ?start_time=2024-01-15T09:00:00Z&end_time=2024-01-15T15:00:00Z&limit=1000"
```

**Response:**
```json
{
  "symbol": "000001.SZ",
  "data": [
    {
      "timestamp_ns": 1705312800000000000,
      "price": 12.34,
      "volume": 1000,
      "bid_price": 12.33,
      "ask_price": 12.35,
      "bid_volume": 500,
      "ask_volume": 800,
      "data_source": "ctp",
      "collection_timestamp_ns": 1705312800100000000
    }
  ],
  "pagination": {
    "has_more": true,
    "next_cursor": "eyJ0aW1lc3RhbXAiOjE3MDUzMTI4MDB9",
    "total_count": 5000
  },
  "storage_source": "hot",
  "query_time_ms": 15
}
```

#### GET /api/v1/ticks/{symbol}/latest
Get the latest tick data for a symbol.

**Response:**
```json
{
  "symbol": "000001.SZ",
  "timestamp_ns": 1705312800000000000,
  "price": 12.34,
  "volume": 1000,
  "bid_price": 12.33,
  "ask_price": 12.35,
  "bid_volume": 500,
  "ask_volume": 800,
  "data_source": "ctp",
  "collection_timestamp_ns": 1705312800100000000,
  "storage_source": "hot"
}
```

#### POST /api/v1/ticks/batch
Retrieve tick data for multiple symbols.

**Request Body:**
```json
{
  "symbols": ["000001.SZ", "000002.SZ", "600000.SH"],
  "start_time": "2024-01-15T09:00:00Z",
  "end_time": "2024-01-15T15:00:00Z",
  "limit": 1000
}
```

**Response:**
```json
{
  "data": {
    "000001.SZ": [
      {
        "timestamp_ns": 1705312800000000000,
        "price": 12.34,
        "volume": 1000
      }
    ],
    "000002.SZ": [
      {
        "timestamp_ns": 1705312800000000000,
        "price": 23.45,
        "volume": 2000
      }
    ]
  },
  "query_time_ms": 45
}
```

#### GET /api/v1/level2/{symbol}
Retrieve Level 2 market data (order book).

**Parameters:**
- `symbol` (path): Stock symbol
- `start_time` (query): Start timestamp
- `end_time` (query): End timestamp
- `limit` (query): Maximum number of records

**Response:**
```json
{
  "symbol": "000001.SZ",
  "data": [
    {
      "timestamp_ns": 1705312800000000000,
      "bid_prices": [12.33, 12.32, 12.31, 12.30, 12.29],
      "bid_volumes": [1000, 2000, 1500, 3000, 2500],
      "ask_prices": [12.34, 12.35, 12.36, 12.37, 12.38],
      "ask_volumes": [800, 1200, 1800, 2200, 1600],
      "data_source": "ctp"
    }
  ]
}
```

### Collection Management Endpoints

#### GET /api/v1/collectors/status
Get status of all data collectors.

**Response:**
```json
{
  "collectors": {
    "pytdx": {
      "status": "running",
      "last_update": "2024-01-15T10:29:00Z",
      "symbols_count": 4000,
      "data_points_collected": 1500000,
      "error_count": 5
    },
    "ctp": {
      "status": "running",
      "last_update": "2024-01-15T10:30:00Z",
      "symbols_count": 500,
      "data_points_collected": 50000,
      "error_count": 0
    }
  },
  "coordination": {
    "active": true,
    "duplicate_data_points": 1250,
    "conflict_resolution_count": 45
  }
}
```

#### POST /api/v1/collectors/{collector_type}/start
Start a specific collector.

**Parameters:**
- `collector_type` (path): "pytdx" or "ctp"

**Request Body:**
```json
{
  "symbols": ["000001.SZ", "000002.SZ"],
  "config": {
    "batch_size": 1000,
    "concurrent_requests": 5
  }
}
```

#### POST /api/v1/collectors/{collector_type}/stop
Stop a specific collector.

#### GET /api/v1/tasks
Get scheduled task status.

**Response:**
```json
{
  "tasks": [
    {
      "task_id": "historical_update_daily",
      "type": "HISTORICAL_UPDATE",
      "status": "scheduled",
      "next_run": "2024-01-16T02:00:00Z",
      "last_run": "2024-01-15T02:00:00Z",
      "last_result": "success",
      "retry_count": 0
    }
  ]
}
```

### Configuration Endpoints

#### GET /api/v1/config
Get current system configuration.

**Response:**
```json
{
  "collection": {
    "pytdx": {
      "enabled": true,
      "batch_size": 1000
    }
  },
  "storage": {
    "hot_storage": {
      "retention_days": 7
    }
  }
}
```

#### PUT /api/v1/config
Update system configuration (hot reload).

**Request Body:**
```json
{
  "collection": {
    "pytdx": {
      "batch_size": 2000
    }
  }
}
```

### Monitoring Endpoints

#### GET /api/v1/metrics/summary
Get system metrics summary.

**Response:**
```json
{
  "data_collection": {
    "total_data_points": 2000000,
    "data_points_per_second": 150,
    "error_rate_percent": 0.1
  },
  "storage": {
    "hot_storage_usage_percent": 45,
    "warm_storage_usage_percent": 30,
    "cold_storage_usage_gb": 500
  },
  "performance": {
    "avg_query_time_ms": 25,
    "cache_hit_rate_percent": 85
  }
}
```

## gRPC API

### Service Definition

```protobuf
service MarketDataService {
  // Data retrieval
  rpc GetTicks(GetTicksRequest) returns (GetTicksResponse);
  rpc GetLatestTick(GetLatestTickRequest) returns (StandardTick);
  rpc GetLevel2Data(GetLevel2Request) returns (GetLevel2Response);
  
  // Collection management
  rpc StartCollector(StartCollectorRequest) returns (CollectorResponse);
  rpc StopCollector(StopCollectorRequest) returns (CollectorResponse);
  rpc GetCollectorStatus(CollectorStatusRequest) returns (CollectorStatusResponse);
  
  // Configuration
  rpc GetConfig(GetConfigRequest) returns (ConfigResponse);
  rpc UpdateConfig(UpdateConfigRequest) returns (ConfigResponse);
  
  // Streaming
  rpc StreamTicks(StreamTicksRequest) returns (stream StandardTick);
}
```

### Message Types

```protobuf
message StandardTick {
  string symbol = 1;
  int64 timestamp_ns = 2;
  double price = 3;
  uint64 volume = 4;
  double bid_price = 5;
  double ask_price = 6;
  uint64 bid_volume = 7;
  uint64 ask_volume = 8;
  string data_source = 9;
  int64 collection_timestamp_ns = 10;
}

message GetTicksRequest {
  string symbol = 1;
  int64 start_timestamp_ns = 2;
  int64 end_timestamp_ns = 3;
  int32 limit = 4;
  string cursor = 5;
}

message GetTicksResponse {
  repeated StandardTick ticks = 1;
  string next_cursor = 2;
  bool has_more = 3;
  string storage_source = 4;
  int32 query_time_ms = 5;
}
```

### Client Examples

#### Python Client
```python
import grpc
from market_data_pb2 import GetTicksRequest
from market_data_pb2_grpc import MarketDataServiceStub

# Create channel and stub
channel = grpc.insecure_channel('localhost:8081')
stub = MarketDataServiceStub(channel)

# Get tick data
request = GetTicksRequest(
    symbol="000001.SZ",
    start_timestamp_ns=1705312800000000000,
    end_timestamp_ns=1705316400000000000,
    limit=1000
)

response = stub.GetTicks(request)
print(f"Retrieved {len(response.ticks)} ticks")
```

#### Go Client
```go
package main

import (
    "context"
    "log"
    
    "google.golang.org/grpc"
    pb "your-module/market_data"
)

func main() {
    conn, err := grpc.Dial("localhost:8081", grpc.WithInsecure())
    if err != nil {
        log.Fatal(err)
    }
    defer conn.Close()
    
    client := pb.NewMarketDataServiceClient(conn)
    
    req := &pb.GetTicksRequest{
        Symbol: "000001.SZ",
        StartTimestampNs: 1705312800000000000,
        EndTimestampNs: 1705316400000000000,
        Limit: 1000,
    }
    
    resp, err := client.GetTicks(context.Background(), req)
    if err != nil {
        log.Fatal(err)
    }
    
    log.Printf("Retrieved %d ticks", len(resp.Ticks))
}
```

## Error Handling

### HTTP Error Codes

- `200 OK`: Request successful
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Service temporarily unavailable

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_SYMBOL",
    "message": "Symbol '000001.XX' is not valid",
    "details": {
      "field": "symbol",
      "value": "000001.XX"
    }
  },
  "request_id": "req_123456789"
}
```

### gRPC Error Codes

- `OK`: Success
- `INVALID_ARGUMENT`: Invalid request parameters
- `NOT_FOUND`: Resource not found
- `PERMISSION_DENIED`: Insufficient permissions
- `RESOURCE_EXHAUSTED`: Rate limit exceeded
- `INTERNAL`: Internal server error
- `UNAVAILABLE`: Service unavailable

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Default limit**: 1000 requests per minute per API key
- **Burst limit**: 100 requests per second
- **Data retrieval limit**: 100,000 data points per request

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 950
X-RateLimit-Reset: 1705312860
```

## Pagination

Large result sets are paginated using cursor-based pagination:

1. Initial request returns data and a `next_cursor`
2. Use the `next_cursor` in subsequent requests
3. Continue until `has_more` is `false`

## Data Formats

### Timestamps
All timestamps are in nanoseconds since Unix epoch (UTC).

### Symbols
Stock symbols follow the format: `{code}.{exchange}`
- Shanghai: `.SH` (e.g., "600000.SH")
- Shenzhen: `.SZ` (e.g., "000001.SZ")

### Prices
All prices are in the original currency (CNY for Chinese stocks).

### Volumes
Volumes are in shares (not lots).

## WebSocket API (Real-time Streaming)

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8080/ws/ticks');
```

### Subscribe to Symbols
```json
{
  "action": "subscribe",
  "symbols": ["000001.SZ", "000002.SZ"]
}
```

### Receive Data
```json
{
  "type": "tick",
  "data": {
    "symbol": "000001.SZ",
    "timestamp_ns": 1705312800000000000,
    "price": 12.34,
    "volume": 1000
  }
}
```

### Unsubscribe
```json
{
  "action": "unsubscribe",
  "symbols": ["000001.SZ"]
}
```