# Security module CMakeLists.txt

# Find required packages
find_package(OpenSSL REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(<PERSON><PERSON><PERSON><PERSON> jsoncpp)
pkg_check_modules(UUID uuid)

# Security library sources
set(SECURITY_SOURCES
    tls_manager.cpp
    encryption_manager.cpp
    jwt_auth.cpp
    rbac_manager.cpp
    audit_logger.cpp
    security_manager.cpp
)

set(SECURITY_HEADERS
    security_config.h
    tls_manager.h
    encryption_manager.h
    jwt_auth.h
    rbac_manager.h
    audit_logger.h
    security_manager.h
)

# Create security library
add_library(financial_data_security STATIC ${SECURITY_SOURCES} ${SECURITY_HEADERS})

# Include directories
target_include_directories(financial_data_security PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${OPENSSL_INCLUDE_DIR}
    ${JSO<PERSON><PERSON>_INCLUDE_DIRS}
    ${UUID_INCLUDE_DIRS}
)

# Link libraries
target_link_libraries(financial_data_security
    ${OPENSSL_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    ${UUID_LIBRARIES}
    pthread
)

# Compiler definitions
target_compile_definitions(financial_data_security PRIVATE
    ${JSONCPP_CFLAGS_OTHER}
    ${UUID_CFLAGS_OTHER}
)

# Set C++ standard
set_target_properties(financial_data_security PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# Install targets
install(TARGETS financial_data_security
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${SECURITY_HEADERS}
    DESTINATION include/financial_data/security
)