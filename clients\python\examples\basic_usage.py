#!/usr/bin/env python3
"""
Basic usage example for Financial Data SDK
"""

import asyncio
import time
from financial_data_sdk import FinancialDataClient, AsyncFinancialDataClient
from financial_data_sdk.indicators import TechnicalIndicators, calculate_all_indicators
import pandas as pd
import numpy as np


def sync_client_example():
    """Example using synchronous client"""
    print("=== Synchronous Client Example ===")
    
    # Initialize client with multiple servers for load balancing
    servers = ["localhost:50051", "localhost:50052", "localhost:50053"]
    client = FinancialDataClient(servers, enable_cache=True)
    
    try:
        # Health check
        if client.health_check():
            print("✓ Service is healthy")
        else:
            print("✗ Service health check failed")
            return
        
        # Get historical tick data
        print("\n1. Getting historical tick data...")
        tick_df = client.get_tick_data(
            symbol="AAPL",
            exchange="NASDAQ",
            start_time="2024-01-01",
            end_time="2024-01-02",
            limit=1000,
            as_dataframe=True
        )
        print(f"Retrieved {len(tick_df)} tick records")
        if not tick_df.empty:
            print(tick_df.head())
        
        # Get historical K-line data
        print("\n2. Getting historical K-line data...")
        kline_df = client.get_kline_data(
            symbol="AAPL",
            period="1m",
            exchange="NASDAQ",
            start_time="2024-01-01",
            end_time="2024-01-02",
            limit=500,
            as_dataframe=True
        )
        print(f"Retrieved {len(kline_df)} K-line records")
        if not kline_df.empty:
            print(kline_df.head())
        
        # Batch data requests
        print("\n3. Batch data requests...")
        batch_requests = [
            {
                'id': 'aapl_tick',
                'type': 'tick',
                'symbol': 'AAPL',
                'exchange': 'NASDAQ',
                'limit': 100
            },
            {
                'id': 'googl_kline',
                'type': 'kline',
                'symbol': 'GOOGL',
                'period': '5m',
                'exchange': 'NASDAQ',
                'limit': 50
            }
        ]
        
        batch_results = client.get_batch_data(batch_requests, parallel=True)
        for req_id, result in batch_results.items():
            if result is not None:
                print(f"✓ {req_id}: {len(result)} records")
            else:
                print(f"✗ {req_id}: Failed")
        
        # Stream real-time data (example)
        print("\n4. Streaming real-time data...")
        
        def tick_handler(tick_data):
            print(f"Tick: {tick_data.symbol} @ {tick_data.last_price} vol:{tick_data.volume}")
        
        # Start streaming (this would run in background)
        stream_future = client.stream_tick_data(
            symbols=["AAPL", "GOOGL"],
            exchange="NASDAQ",
            callback=tick_handler
        )
        
        # Let it run for a few seconds
        time.sleep(3)
        
        # Get cache statistics
        print("\n5. Cache statistics:")
        print(client.cache.stats() if client.cache else "Cache disabled")
        
        # Get server statistics
        print("\n6. Server statistics:")
        print(client.get_server_stats())
        
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        client.close()
        print("\n✓ Client closed")


async def async_client_example():
    """Example using asynchronous client"""
    print("\n=== Asynchronous Client Example ===")
    
    servers = ["localhost:50051", "localhost:50052"]
    
    async with AsyncFinancialDataClient(servers, enable_cache=True) as client:
        try:
            # Health check
            if await client.health_check():
                print("✓ Async service is healthy")
            else:
                print("✗ Async service health check failed")
                return
            
            # Get data asynchronously
            print("\n1. Getting data asynchronously...")
            tick_data = await client.get_tick_data(
                symbol="MSFT",
                exchange="NASDAQ",
                limit=100
            )
            print(f"Retrieved {len(tick_data)} tick records")
            
            # Concurrent data requests
            print("\n2. Concurrent data requests...")
            requests = [
                {'id': 'req1', 'type': 'tick', 'symbol': 'AAPL', 'limit': 50},
                {'id': 'req2', 'type': 'tick', 'symbol': 'GOOGL', 'limit': 50},
                {'id': 'req3', 'type': 'kline', 'symbol': 'MSFT', 'period': '1m', 'limit': 30}
            ]
            
            concurrent_results = await client.get_concurrent_data(
                requests, 
                max_concurrent=3
            )
            
            for req_id, result in concurrent_results.items():
                if result is not None:
                    print(f"✓ {req_id}: {len(result)} records")
                else:
                    print(f"✗ {req_id}: Failed")
            
            # Async streaming
            print("\n3. Async streaming...")
            count = 0
            async for tick in client.stream_tick_data(["AAPL"], exchange="NASDAQ"):
                print(f"Async Tick: {tick.symbol} @ {tick.last_price}")
                count += 1
                if count >= 5:  # Limit for demo
                    break
            
        except Exception as e:
            print(f"Async error: {e}")


def technical_indicators_example():
    """Example using technical indicators"""
    print("\n=== Technical Indicators Example ===")
    
    # Create sample OHLCV data
    dates = pd.date_range('2024-01-01', periods=100, freq='1H')
    np.random.seed(42)
    
    # Generate realistic price data
    base_price = 100.0
    returns = np.random.normal(0, 0.02, 100)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV DataFrame
    df = pd.DataFrame({
        'datetime': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100)
    })
    df.set_index('datetime', inplace=True)
    
    print(f"Sample data shape: {df.shape}")
    print(df.head())
    
    # Calculate individual indicators
    print("\n1. Individual indicators:")
    
    # Simple Moving Average
    sma_20 = TechnicalIndicators.sma(df['close'], 20)
    print(f"SMA(20) latest: {sma_20.iloc[-1]:.2f}")
    
    # RSI
    rsi = TechnicalIndicators.rsi(df['close'])
    print(f"RSI latest: {rsi.iloc[-1]:.2f}")
    
    # MACD
    macd_line, signal_line, histogram = TechnicalIndicators.macd(df['close'])
    print(f"MACD latest: {macd_line.iloc[-1]:.4f}")
    
    # Bollinger Bands
    bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['close'])
    print(f"BB Upper: {bb_upper.iloc[-1]:.2f}, Lower: {bb_lower.iloc[-1]:.2f}")
    
    # Calculate all indicators at once
    print("\n2. All indicators:")
    df_with_indicators = calculate_all_indicators(df)
    print(f"DataFrame with indicators shape: {df_with_indicators.shape}")
    print("Available indicators:", [col for col in df_with_indicators.columns if col not in ['open', 'high', 'low', 'close', 'volume']])
    
    # Generate trading signals
    print("\n3. Trading signals:")
    from financial_data_sdk.indicators import IndicatorAnalyzer
    
    signals_df = IndicatorAnalyzer.generate_signals(df)
    
    # Count signals
    buy_signals = signals_df[['rsi_buy', 'macd_buy', 'bb_buy']].any(axis=1).sum()
    sell_signals = signals_df[['rsi_sell', 'macd_sell', 'bb_sell']].any(axis=1).sum()
    
    print(f"Buy signals: {buy_signals}")
    print(f"Sell signals: {sell_signals}")
    
    # Simple backtest
    print("\n4. Simple backtest:")
    backtest_results = IndicatorAnalyzer.backtest_strategy(
        df,
        signals_df['rsi_buy'],
        signals_df['rsi_sell'],
        initial_capital=100000
    )
    
    print(f"Initial capital: ${backtest_results['initial_capital']:,.2f}")
    print(f"Final value: ${backtest_results['final_value']:,.2f}")
    print(f"Total return: {backtest_results['total_return']:.2%}")
    print(f"Number of trades: {backtest_results['num_trades']}")


def data_conversion_example():
    """Example of data conversion utilities"""
    print("\n=== Data Conversion Example ===")
    
    from financial_data_sdk.utils import DataConverter, DataValidator
    
    converter = DataConverter()
    
    # Timestamp conversions
    print("1. Timestamp conversions:")
    
    # Various timestamp formats
    timestamps = [
        "2024-01-01 10:30:00",
        1704110200,  # Unix timestamp
        pd.Timestamp("2024-01-01 10:30:00"),
        1704110200000000000  # Nanoseconds
    ]
    
    for ts in timestamps:
        ns_timestamp = converter.to_nanoseconds(ts)
        dt = converter.from_nanoseconds(ns_timestamp)
        print(f"  {ts} -> {ns_timestamp} -> {dt}")
    
    # Symbol normalization
    print("\n2. Symbol normalization:")
    symbols = ["aapl", "GOOGL.NASDAQ", "msft ", "TSLA.US"]
    for symbol in symbols:
        normalized = converter.normalize_symbol(symbol, "NASDAQ")
        print(f"  {symbol} -> {normalized}")
    
    # Price precision
    print("\n3. Price precision:")
    prices = [123.456789, 99.999, 100.001]
    tick_size = 0.01
    for price in prices:
        rounded = converter.convert_price_precision(price, tick_size)
        print(f"  {price} -> {rounded} (tick size: {tick_size})")
    
    # Data validation
    print("\n4. Data validation:")
    
    # Valid tick data
    valid_tick = {
        'timestamp': int(time.time() * 1_000_000_000),
        'symbol': 'AAPL',
        'last_price': 150.25,
        'volume': 1000,
        'bid_prices': [150.24, 150.23],
        'ask_prices': [150.26, 150.27]
    }
    
    errors = DataValidator.validate_tick_data(valid_tick)
    print(f"  Valid tick errors: {errors}")
    
    # Invalid tick data
    invalid_tick = {
        'timestamp': 'invalid',
        'symbol': 'AAPL',
        'last_price': -10,  # Invalid negative price
        'volume': -100,     # Invalid negative volume
        'bid_prices': [150.30],  # Bid higher than ask
        'ask_prices': [150.25]
    }
    
    errors = DataValidator.validate_tick_data(invalid_tick)
    print(f"  Invalid tick errors: {errors}")


def main():
    """Run all examples"""
    print("Financial Data SDK Examples")
    print("=" * 50)
    
    # Run synchronous examples
    sync_client_example()
    
    # Run asynchronous examples
    asyncio.run(async_client_example())
    
    # Run technical indicators examples
    technical_indicators_example()
    
    # Run data conversion examples
    data_conversion_example()
    
    print("\n" + "=" * 50)
    print("All examples completed!")


if __name__ == "__main__":
    main()