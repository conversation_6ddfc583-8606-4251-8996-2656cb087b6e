#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>

#include "collectors/ctp_collector.h"

using namespace financial_data;

// 全局变量用于优雅退出
volatile bool g_running = true;

void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
    g_running = false;
}

int main() {
    try {
        // 设置信号处理
        signal(SIGINT, signal_handler);
        signal(SIGTERM, signal_handler);
        
        // 初始化日志
        auto console = spdlog::stdout_color_mt("console");
        spdlog::set_default_logger(console);
        spdlog::set_level(spdlog::level::info);
        
        std::cout << "=== CTP Market Data Collector Demo ===" << std::endl;
        std::cout << "Press Ctrl+C to exit" << std::endl;
        
        // 创建CTP配置
        CTPConfig config;
        config.front_address = "tcp://***************:10131";  // SimNow模拟环境
        config.broker_id = "9999";
        config.user_id = "demo_user";      // 请替换为实际用户名
        config.password = "demo_password"; // 请替换为实际密码
        config.flow_path = "./flow/";
        config.heartbeat_interval = 30;
        config.reconnect_interval = 5;
        config.max_reconnect_attempts = 3;
        config.enable_level2 = true;
        
        std::cout << "\nConfiguration:" << std::endl;
        std::cout << "  Front Address: " << config.front_address << std::endl;
        std::cout << "  Broker ID: " << config.broker_id << std::endl;
        std::cout << "  User ID: " << config.user_id << std::endl;
        std::cout << "  Flow Path: " << config.flow_path << std::endl;
        
        // 创建采集器
        auto collector = std::make_unique<CTPMarketDataCollector>();
        
        // 统计变量
        std::atomic<int> tick_count{0};
        std::atomic<int> level2_count{0};
        
        // 设置数据回调
        collector->SetDataCallback([&](const MarketDataWrapper& data) {
            if (data.type == MarketDataWrapper::DataType::TICK) {
                tick_count++;
                const auto& tick = data.tick_data;
                
                if (tick_count % 10 == 1) { // 每10条显示一次
                    std::cout << "TICK [" << tick_count << "] " 
                             << tick.symbol << "@" << tick.exchange 
                             << " Price:" << tick.last_price 
                             << " Vol:" << tick.volume 
                             << " Bid:" << tick.GetBidPrice() 
                             << " Ask:" << tick.GetAskPrice() << std::endl;
                }
            } else if (data.type == MarketDataWrapper::DataType::LEVEL2) {
                level2_count++;
                const auto& level2 = data.level2_data;
                
                if (level2_count % 5 == 1) { // 每5条显示一次
                    std::cout << "L2 [" << level2_count << "] " 
                             << level2.symbol << "@" << level2.exchange 
                             << " Bids:" << level2.bids.size() 
                             << " Asks:" << level2.asks.size() << std::endl;
                }
            }
        });
        
        // 设置状态回调
        collector->SetStatusCallback([](ConnectionStatus status, const std::string& message) {
            std::string status_str;
            switch (status) {
                case ConnectionStatus::DISCONNECTED: status_str = "DISCONNECTED"; break;
                case ConnectionStatus::CONNECTING: status_str = "CONNECTING"; break;
                case ConnectionStatus::CONNECTED: status_str = "CONNECTED"; break;
                case ConnectionStatus::RECONNECTING: status_str = "RECONNECTING"; break;
                case ConnectionStatus::ERROR: status_str = "ERROR"; break;
            }
            std::cout << ">>> Status: " << status_str << " - " << message << std::endl;
        });
        
        // 初始化采集器
        if (!collector->Initialize(config)) {
            std::cerr << "Failed to initialize CTP collector" << std::endl;
            return 1;
        }
        std::cout << "✓ CTP collector initialized" << std::endl;
        
        // 启动采集器
        collector->Start();
        std::cout << "✓ CTP collector started" << std::endl;
        
        // 尝试连接
        std::cout << "Connecting to CTP server..." << std::endl;
        if (collector->Connect()) {
            std::cout << "✓ Connection initiated" << std::endl;
        } else {
            std::cout << "✗ Failed to initiate connection" << std::endl;
        }
        
        // 等待连接建立
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        // 订阅一些热门合约
        std::vector<std::string> symbols = {
            "CU2409",   // 沪铜
            "AL2409",   // 沪铝
            "ZN2409",   // 沪锌
            "AU2412",   // 沪金
            "AG2412",   // 沪银
            "RB2410",   // 螺纹钢
            "I2409",    // 铁矿石
            "J2409",    // 焦炭
            "M2409",    // 豆粕
            "Y2409"     // 豆油
        };
        
        std::cout << "Subscribing to " << symbols.size() << " symbols..." << std::endl;
        if (collector->Subscribe(symbols)) {
            std::cout << "✓ Subscription successful" << std::endl;
            for (const auto& symbol : symbols) {
                std::cout << "  - " << symbol << std::endl;
            }
        } else {
            std::cout << "✗ Subscription failed" << std::endl;
        }
        
        // 主循环
        std::cout << "\n=== Market Data Stream (Press Ctrl+C to exit) ===" << std::endl;
        auto start_time = std::chrono::steady_clock::now();
        
        while (g_running) {
            std::this_thread::sleep_for(std::chrono::seconds(5));
            
            // 显示统计信息
            auto stats = collector->GetStatistics();
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration<double>(now - start_time).count();
            
            std::cout << "\n--- Statistics (Running " << std::fixed << std::setprecision(1) 
                     << elapsed << "s) ---" << std::endl;
            std::cout << "Status: " << static_cast<int>(stats.status) 
                     << " | Received: " << stats.total_received 
                     << " | Processed: " << stats.total_processed 
                     << " | Errors: " << stats.total_errors << std::endl;
            std::cout << "Tick Count: " << tick_count 
                     << " | Level2 Count: " << level2_count << std::endl;
            std::cout << "Health: " << (collector->IsHealthy() ? "OK" : "WARN") 
                     << " | " << collector->GetHealthStatus() << std::endl;
            
            if (stats.messages_per_second > 0) {
                std::cout << "Throughput: " << std::fixed << std::setprecision(2) 
                         << stats.messages_per_second << " msg/s" << std::endl;
            }
        }
        
        // 优雅关闭
        std::cout << "\nShutting down..." << std::endl;
        collector->Stop();
        collector->Disconnect();
        
        // 最终统计
        auto final_stats = collector->GetStatistics();
        std::cout << "\n=== Final Statistics ===" << std::endl;
        std::cout << "Total Received: " << final_stats.total_received << std::endl;
        std::cout << "Total Processed: " << final_stats.total_processed << std::endl;
        std::cout << "Total Errors: " << final_stats.total_errors << std::endl;
        std::cout << "Tick Messages: " << tick_count << std::endl;
        std::cout << "Level2 Messages: " << level2_count << std::endl;
        
        std::cout << "\n=== Demo completed successfully! ===" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}