#!/bin/bash
# WSL环境下金融数据服务部署测试脚本
# Financial Data Service Deployment Test Script for WSL

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 项目信息
PROJECT_NAME="金融数据服务系统"
PROJECT_DIR=$(pwd)
VENV_DIR="$PROJECT_DIR/venv"
LOG_DIR="$PROJECT_DIR/logs"

print_header() {
    echo "========================================"
    echo "    $PROJECT_NAME - WSL部署测试"
    echo "========================================"
    echo
}

# 检查WSL环境
check_wsl_environment() {
    print_info "检查WSL环境..."
    
    if ! grep -q Microsoft /proc/version 2>/dev/null; then
        print_warning "当前不在WSL环境中，但可以继续部署"
    else
        print_success "检测到WSL环境"
    fi
    
    # 检查操作系统
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        print_info "操作系统: $NAME $VERSION_ID"
    fi
}

# 安装系统依赖
install_system_dependencies() {
    print_info "安装系统依赖..."
    
    # 更新包管理器
    sudo apt-get update -y
    
    # 安装基础依赖
    sudo apt-get install -y \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev \
        build-essential \
        curl \
        wget \
        git \
        redis-server \
        docker.io \
        docker-compose
    
    # 启动Redis服务
    sudo service redis-server start
    
    print_success "系统依赖安装完成"
}

# 创建Python虚拟环境
setup_python_environment() {
    print_info "设置Python虚拟环境..."
    
    # 创建虚拟环境
    if [[ ! -d "$VENV_DIR" ]]; then
        python3 -m venv "$VENV_DIR"
        print_info "创建虚拟环境: $VENV_DIR"
    fi
    
    # 激活虚拟环境
    source "$VENV_DIR/bin/activate"
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装项目依赖
    if [[ -f "requirements.txt" ]]; then
        print_info "安装Python依赖包..."
        pip install -r requirements.txt
        print_success "Python依赖安装完成"
    else
        print_warning "未找到requirements.txt文件"
    fi
}

# 创建必要目录
create_directories() {
    print_info "创建必要目录..."
    
    mkdir -p "$LOG_DIR"
    mkdir -p config
    mkdir -p data
    
    print_success "目录创建完成"
}

# 启动Docker服务
start_docker_services() {
    print_info "启动Docker服务..."
    
    # 启动Docker守护进程
    sudo service docker start
    
    # 检查Docker是否正常运行
    if ! sudo docker info >/dev/null 2>&1; then
        print_error "Docker服务启动失败"
        return 1
    fi
    
    # 启动基础服务
    if [[ -f "docker-compose.yml" ]]; then
        print_info "启动基础服务容器..."
        sudo docker-compose up -d redis clickhouse
        
        # 等待服务启动
        print_info "等待服务启动..."
        sleep 10
        
        # 检查服务状态
        if sudo docker-compose ps | grep -q "Up"; then
            print_success "Docker服务启动成功"
        else
            print_warning "部分Docker服务可能未正常启动"
        fi
    fi
}

# 测试基础功能
test_basic_functionality() {
    print_info "测试基础功能..."
    
    # 激活虚拟环境
    source "$VENV_DIR/bin/activate"
    
    # 测试Redis连接
    print_info "测试Redis连接..."
    if redis-cli ping | grep -q "PONG"; then
        print_success "Redis连接正常"
    else
        print_error "Redis连接失败"
        return 1
    fi
    
    # 测试Python模块导入
    print_info "测试Python模块..."
    if python3 -c "import asyncio, redis, pandas, pytdx" 2>/dev/null; then
        print_success "Python模块导入正常"
    else
        print_error "Python模块导入失败"
        return 1
    fi
    
    # 运行增强功能测试
    if [[ -f "test_enhanced_features.py" ]]; then
        print_info "运行增强功能测试..."
        python3 test_enhanced_features.py
    fi
}

# 启动调度器服务
start_scheduler_service() {
    print_info "启动调度器服务..."
    
    # 激活虚拟环境
    source "$VENV_DIR/bin/activate"
    
    # 检查启动脚本
    if [[ -f "start_enhanced_scheduler.py" ]]; then
        print_info "使用增强版调度器..."
        python3 start_enhanced_scheduler.py &
        SCHEDULER_PID=$!
        echo $SCHEDULER_PID > "$LOG_DIR/scheduler.pid"
        print_success "调度器服务已启动 (PID: $SCHEDULER_PID)"
    elif [[ -f "scripts/start_scheduler.py" ]]; then
        print_info "使用标准调度器..."
        python3 scripts/start_scheduler.py --daemon --config config/scheduler_config.json
        print_success "调度器服务已启动"
    else
        print_warning "未找到调度器启动脚本"
    fi
}

# 显示服务状态
show_service_status() {
    print_info "服务状态检查..."
    
    echo
    echo "=== 系统服务状态 ==="
    
    # Redis状态
    if pgrep redis-server >/dev/null; then
        print_success "Redis服务: 运行中"
    else
        print_error "Redis服务: 未运行"
    fi
    
    # Docker服务状态
    if sudo docker info >/dev/null 2>&1; then
        print_success "Docker服务: 运行中"
        echo "Docker容器状态:"
        sudo docker-compose ps 2>/dev/null || echo "  无Docker Compose服务"
    else
        print_error "Docker服务: 未运行"
    fi
    
    # 调度器状态
    if [[ -f "$LOG_DIR/scheduler.pid" ]]; then
        local pid=$(cat "$LOG_DIR/scheduler.pid")
        if kill -0 "$pid" 2>/dev/null; then
            print_success "调度器服务: 运行中 (PID: $pid)"
        else
            print_error "调度器服务: 未运行"
        fi
    else
        print_warning "调度器服务: 状态未知"
    fi
    
    echo
    echo "=== 网络端口状态 ==="
    echo "Redis (6379): $(netstat -ln | grep :6379 >/dev/null && echo '监听中' || echo '未监听')"
    echo "ClickHouse HTTP (8123): $(netstat -ln | grep :8123 >/dev/null && echo '监听中' || echo '未监听')"
    echo "ClickHouse Native (9000): $(netstat -ln | grep :9000 >/dev/null && echo '监听中' || echo '未监听')"
}

# 显示使用说明
show_usage_info() {
    echo
    echo "=== 使用说明 ==="
    echo
    echo "1. 查看日志:"
    echo "   tail -f $LOG_DIR/scheduler_service.log"
    echo
    echo "2. 停止服务:"
    echo "   ./deploy_wsl_test.sh --stop"
    echo
    echo "3. 重启服务:"
    echo "   ./deploy_wsl_test.sh --restart"
    echo
    echo "4. 查看状态:"
    echo "   ./deploy_wsl_test.sh --status"
    echo
    echo "5. 手动测试:"
    echo "   source venv/bin/activate"
    echo "   python3 test_enhanced_features.py"
    echo
    echo "6. 访问服务:"
    echo "   Redis: localhost:6379"
    echo "   ClickHouse: http://localhost:8123"
    echo
}

# 停止服务
stop_services() {
    print_info "停止服务..."
    
    # 停止调度器
    if [[ -f "$LOG_DIR/scheduler.pid" ]]; then
        local pid=$(cat "$LOG_DIR/scheduler.pid")
        if kill -0 "$pid" 2>/dev/null; then
            kill -TERM "$pid"
            print_info "调度器服务已停止"
        fi
        rm -f "$LOG_DIR/scheduler.pid"
    fi
    
    # 停止Docker服务
    if [[ -f "docker-compose.yml" ]]; then
        sudo docker-compose down
        print_info "Docker服务已停止"
    fi
    
    print_success "所有服务已停止"
}

# 重启服务
restart_services() {
    print_info "重启服务..."
    stop_services
    sleep 3
    start_docker_services
    start_scheduler_service
    print_success "服务重启完成"
}

# 主函数
main() {
    case "${1:-deploy}" in
        --stop)
            print_header
            stop_services
            ;;
        --restart)
            print_header
            restart_services
            show_service_status
            ;;
        --status)
            print_header
            show_service_status
            ;;
        deploy|--deploy)
            print_header
            check_wsl_environment
            install_system_dependencies
            setup_python_environment
            create_directories
            start_docker_services
            test_basic_functionality
            start_scheduler_service
            show_service_status
            show_usage_info
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  deploy, --deploy    部署服务 (默认)"
            echo "  --stop              停止服务"
            echo "  --restart           重启服务"
            echo "  --status            查看状态"
            echo "  --help, -h          显示帮助"
            ;;
        *)
            print_error "未知选项: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"