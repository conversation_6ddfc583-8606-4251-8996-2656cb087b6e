@echo off
echo 测试本地服务连接...
echo.

echo ========================================
echo 1. 测试 Redis 连接 (端口 6379)
echo ========================================
netstat -an | findstr :6379
if %errorlevel% equ 0 (
    echo Redis 端口已开放
) else (
    echo Redis 端口未开放
)

echo.
echo ========================================
echo 2. 测试 ClickHouse 连接 (端口 8123)
echo ========================================
netstat -an | findstr :8123
if %errorlevel% equ 0 (
    echo ClickHouse HTTP 端口已开放
) else (
    echo ClickHouse HTTP 端口未开放
)

echo.
echo ========================================
echo 3. 测试 ClickHouse Native 连接 (端口 9000)
echo ========================================
netstat -an | findstr :9000
if %errorlevel% equ 0 (
    echo ClickHouse Native 端口已开放
) else (
    echo ClickHouse Native 端口未开放
)

echo.
echo ========================================
echo 4. 测试 Kafka 连接 (端口 9092)
echo ========================================
netstat -an | findstr :9092
if %errorlevel% equ 0 (
    echo Kafka 端口已开放
) else (
    echo Kafka 端口未开放
)

echo.
echo ========================================
echo 5. 测试 MinIO 连接 (端口 9001, 9002)
echo ========================================
netstat -an | findstr :9001
if %errorlevel% equ 0 (
    echo MinIO 控制台端口已开放
) else (
    echo MinIO 控制台端口未开放
)

netstat -an | findstr :9002
if %errorlevel% equ 0 (
    echo MinIO API 端口已开放
) else (
    echo MinIO API 端口未开放
)

echo.
echo ========================================
echo 6. 尝试 HTTP 连接测试
echo ========================================

echo 测试 ClickHouse HTTP 接口...
curl -s --connect-timeout 3 "http://localhost:8123/?query=SELECT 1" 2>nul
if %errorlevel% equ 0 (
    echo ClickHouse HTTP: 连接成功 ✓
) else (
    echo ClickHouse HTTP: 连接失败 ✗
)

echo 测试 MinIO 健康检查...
curl -s --connect-timeout 3 -I "http://localhost:9002/minio/health/live" 2>nul | find "200 OK" >nul
if %errorlevel% equ 0 (
    echo MinIO: 连接成功 ✓
) else (
    echo MinIO: 连接失败 ✗
)

echo.
pause