#include <gtest/gtest.h>
#include "../src/monitoring/metrics_collector.h"
#include "../src/monitoring/latency_monitor.h"
#include "../src/monitoring/data_integrity_checker.h"
#include "../src/monitoring/resource_monitor.h"
#include "../src/monitoring/alert_manager.h"
#include <thread>
#include <chrono>

using namespace monitoring;

class MonitoringTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.prometheus_bind_address = "0.0.0.0:9091"; // Different port for testing
        config_.latency_threshold_microseconds = 100.0; // Higher threshold for testing
        config_.missing_data_timeout = std::chrono::seconds(2);
        config_.resource_monitoring_interval = std::chrono::seconds(1);
    }
    
    void TearDown() override {
        // Cleanup
    }
    
    MonitoringConfig config_;
};

TEST_F(MonitoringTest, MetricsCollectorInitialization) {
    MetricsCollector collector(config_);
    
    EXPECT_TRUE(collector.initialize());
    EXPECT_TRUE(collector.start());
    EXPECT_TRUE(collector.isRunning());
    
    collector.stop();
    EXPECT_FALSE(collector.isRunning());
}

TEST_F(MonitoringTest, LatencyMonitoring) {
    MetricsCollector collector(config_);
    ASSERT_TRUE(collector.initialize());
    ASSERT_TRUE(collector.start());
    
    // Test latency recording
    auto measurement_id = collector.startLatencyMeasurement("test_operation", "TEST_SYMBOL");
    std::this_thread::sleep_for(std::chrono::microseconds(50));
    collector.endLatencyMeasurement(measurement_id);
    
    // Test direct latency recording
    collector.recordLatency("direct_test", 75.0, "TEST_SYMBOL");
    
    // Give some time for processing
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto health = collector.getSystemHealth();
    EXPECT_GT(health.average_latency_microseconds, 0.0);
    
    collector.stop();
}

TEST_F(MonitoringTest, DataIntegrityChecking) {
    MetricsCollector collector(config_);
    ASSERT_TRUE(collector.initialize());
    ASSERT_TRUE(collector.start());
    
    // Send sequential messages
    for (uint64_t i = 1; i <= 10; ++i) {
        collector.recordDataMessage("TEST_SYMBOL", i, "test_source", "tick");
    }
    
    // Send message with gap (simulate data loss)
    collector.recordDataMessage("TEST_SYMBOL", 15, "test_source", "tick");
    
    // Give time for processing
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    auto health = collector.getSystemHealth();
    EXPECT_GT(health.total_messages_received, 0);
    EXPECT_GT(health.total_messages_lost, 0); // Should detect the gap
    
    collector.stop();
}

TEST_F(MonitoringTest, AlertManager) {
    auto alert_manager = std::make_shared<AlertManager>();
    ASSERT_TRUE(alert_manager->start());
    
    // Send test alert
    alert_manager->sendAlert("test_alert", "WARNING", "This is a test alert", 
                           {{"test_key", "test_value"}});
    
    // Give time for processing
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto stats = alert_manager->getStatistics();
    EXPECT_GT(stats.total_alerts_sent, 0);
    
    auto recent_alerts = alert_manager->getRecentAlerts(5);
    EXPECT_FALSE(recent_alerts.empty());
    EXPECT_EQ(recent_alerts.back().type, "test_alert");
    
    alert_manager->stop();
}

TEST_F(MonitoringTest, ResourceMonitoring) {
    auto alert_manager = std::make_shared<AlertManager>();
    ASSERT_TRUE(alert_manager->start());
    
    ResourceMonitor resource_monitor(alert_manager);
    resource_monitor.setMonitoringInterval(std::chrono::seconds(1));
    resource_monitor.setCpuThreshold(50.0); // Low threshold to trigger alerts
    resource_monitor.setMemoryThreshold(50.0);
    
    ASSERT_TRUE(resource_monitor.start());
    
    // Let it run for a few seconds to collect data
    std::this_thread::sleep_for(std::chrono::seconds(3));
    
    auto current_usage = resource_monitor.getCurrentUsage();
    EXPECT_GE(current_usage.cpu_percentage, 0.0);
    EXPECT_GE(current_usage.memory_percentage, 0.0);
    EXPECT_GE(current_usage.disk_percentage, 0.0);
    
    resource_monitor.stop();
    alert_manager->stop();
}

TEST_F(MonitoringTest, PrometheusMetrics) {
    auto& metrics = PrometheusMetrics::getInstance();
    ASSERT_TRUE(metrics.initialize("0.0.0.0:9092")); // Different port for testing
    
    // Test various metric types
    metrics.recordLatency("test_operation", 45.0);
    metrics.recordEndToEndLatency(55.0);
    metrics.incrementDataReceived("TEST_SYMBOL");
    metrics.incrementDataLoss("TEST_SYMBOL");
    metrics.updateCpuUsage(75.0);
    metrics.updateMemoryUsage(60.0);
    metrics.incrementConnectionCount();
    metrics.updateQueueSize("test_queue", 100);
    metrics.recordThroughput("test_component", 1000.0);
    metrics.incrementAlert("test_alert", "warning");
    
    // Metrics are recorded asynchronously, so we just verify no exceptions
    SUCCEED();
}

TEST_F(MonitoringTest, SystemHealthCheck) {
    MetricsCollector collector(config_);
    ASSERT_TRUE(collector.initialize());
    ASSERT_TRUE(collector.start());
    
    // Generate some activity
    collector.recordLatency("health_test", 25.0);
    collector.recordDataMessage("HEALTH_SYMBOL", 1);
    
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto health = collector.getSystemHealth();
    
    // Verify health structure
    EXPECT_TRUE(health.prometheus_healthy);
    EXPECT_TRUE(health.latency_monitor_healthy);
    EXPECT_TRUE(health.data_integrity_healthy);
    EXPECT_TRUE(health.resource_monitor_healthy);
    EXPECT_TRUE(health.alert_manager_healthy);
    
    collector.stop();
}

TEST_F(MonitoringTest, ConfigurationUpdate) {
    MetricsCollector collector(config_);
    ASSERT_TRUE(collector.initialize());
    ASSERT_TRUE(collector.start());
    
    // Update configuration
    MonitoringConfig new_config = config_;
    new_config.latency_threshold_microseconds = 200.0;
    new_config.cpu_threshold = 90.0;
    
    collector.updateConfig(new_config);
    
    // Verify configuration was applied
    const auto& current_config = collector.getConfig();
    EXPECT_EQ(current_config.latency_threshold_microseconds, 200.0);
    EXPECT_EQ(current_config.cpu_threshold, 90.0);
    
    collector.stop();
}

// Performance test to ensure monitoring doesn't significantly impact performance
TEST_F(MonitoringTest, PerformanceImpact) {
    MetricsCollector collector(config_);
    ASSERT_TRUE(collector.initialize());
    ASSERT_TRUE(collector.start());
    
    const int num_operations = 10000;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Simulate high-frequency operations
    for (int i = 0; i < num_operations; ++i) {
        auto measurement_id = collector.startLatencyMeasurement("perf_test");
        collector.endLatencyMeasurement(measurement_id);
        collector.recordDataMessage("PERF_SYMBOL", i);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    // Should be able to handle 10k operations in reasonable time (< 1 second)
    EXPECT_LT(duration.count(), 1000000); // Less than 1 second
    
    std::cout << "Performance test: " << num_operations << " operations in " 
              << duration.count() << " microseconds" << std::endl;
    
    collector.stop();
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}