"""
Configuration settings for the API service
"""

import os
from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class RedisConfig:
    """Redis configuration"""
    host: str = "localhost"
    port: int = 6379
    password: str = ""
    db: int = 0
    max_connections: int = 20


@dataclass
class ClickHouseConfig:
    """ClickHouse configuration"""
    host: str = "localhost"
    port: int = 9000
    user: str = "default"
    password: str = ""
    database: str = "market_data"


@dataclass
class S3Config:
    """S3/MinIO configuration"""
    endpoint: str = "http://localhost:9000"
    access_key: str = "minioadmin"
    secret_key: str = "minioadmin"
    bucket: str = "market-data"
    region: str = "us-east-1"


class APIConfig:
    """Main API configuration"""
    
    def __init__(self):
        # Redis configuration
        self.redis_url = os.getenv(
            "REDIS_URL", 
            "redis://localhost:6379/0"
        )
        self.redis_config = RedisConfig(
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", "6379")),
            password=os.getenv("REDIS_PASSWORD", ""),
            db=int(os.getenv("REDIS_DB", "0")),
            max_connections=int(os.getenv("REDIS_MAX_CONNECTIONS", "20"))
        )
        
        # ClickHouse configuration
        self.clickhouse_host = os.getenv("CLICKHOUSE_HOST", "localhost")
        self.clickhouse_port = int(os.getenv("CLICKHOUSE_PORT", "9000"))
        self.clickhouse_user = os.getenv("CLICKHOUSE_USER", "default")
        self.clickhouse_password = os.getenv("CLICKHOUSE_PASSWORD", "")
        self.clickhouse_database = os.getenv("CLICKHOUSE_DATABASE", "market_data")
        
        # S3/MinIO configuration
        self.s3_endpoint = os.getenv("S3_ENDPOINT", "http://localhost:9000")
        self.s3_access_key = os.getenv("S3_ACCESS_KEY", "minioadmin")
        self.s3_secret_key = os.getenv("S3_SECRET_KEY", "minioadmin")
        self.s3_bucket = os.getenv("S3_BUCKET", "market-data")
        self.s3_region = os.getenv("S3_REGION", "us-east-1")
        
        # API configuration
        self.api_host = os.getenv("API_HOST", "0.0.0.0")
        self.api_port = int(os.getenv("API_PORT", "8000"))
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # Cache configuration
        self.cache_ttl_tick = int(os.getenv("CACHE_TTL_TICK", "300"))  # 5 minutes
        self.cache_ttl_kline = int(os.getenv("CACHE_TTL_KLINE", "600"))  # 10 minutes
        self.cache_ttl_level2 = int(os.getenv("CACHE_TTL_LEVEL2", "120"))  # 2 minutes
        
        # Query limits
        self.max_query_limit = int(os.getenv("MAX_QUERY_LIMIT", "10000"))
        self.default_query_limit = int(os.getenv("DEFAULT_QUERY_LIMIT", "1000"))
        
        # Performance settings
        self.query_timeout = int(os.getenv("QUERY_TIMEOUT", "30"))  # seconds
        self.connection_pool_size = int(os.getenv("CONNECTION_POOL_SIZE", "20"))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "redis": {
                "url": self.redis_url,
                "host": self.redis_config.host,
                "port": self.redis_config.port,
                "db": self.redis_config.db
            },
            "clickhouse": {
                "host": self.clickhouse_host,
                "port": self.clickhouse_port,
                "user": self.clickhouse_user,
                "database": self.clickhouse_database
            },
            "s3": {
                "endpoint": self.s3_endpoint,
                "bucket": self.s3_bucket,
                "region": self.s3_region
            },
            "api": {
                "host": self.api_host,
                "port": self.api_port,
                "debug": self.debug
            },
            "cache": {
                "ttl_tick": self.cache_ttl_tick,
                "ttl_kline": self.cache_ttl_kline,
                "ttl_level2": self.cache_ttl_level2
            },
            "limits": {
                "max_query_limit": self.max_query_limit,
                "default_query_limit": self.default_query_limit,
                "query_timeout": self.query_timeout
            }
        }