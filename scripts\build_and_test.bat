@echo off
REM Build and test script for Redis Hot Storage

echo Building Redis Hot Storage implementation...

REM Create build directory
if not exist "build" mkdir build
cd build

REM Configure with CMake (假设已安装)
echo Configuring project...
cmake .. -G "Visual Studio 16 2019" -A x64

REM Build the project
echo Building project...
cmake --build . --config Release

REM Run tests if build successful
if %ERRORLEVEL% EQU 0 (
    echo Build successful! Running tests...
    
    REM Run mock tests (不需要Redis连接)
    echo Running mock tests...
    ctest -C Release --verbose
    
    echo Tests completed!
) else (
    echo Build failed!
    exit /b 1
)

cd ..
pause