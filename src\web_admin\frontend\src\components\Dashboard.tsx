import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Progress, Alert, Table, Tag } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import axios from 'axios';
import dayjs from 'dayjs';

interface SystemMetrics {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_io: {
    rx_bytes: number;
    tx_bytes: number;
  };
  active_connections: number;
  data_throughput: number;
  latency_p99: number;
  error_rate: number;
}

const Dashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [alerts, setAlerts] = useState<any[]>([]);

  useEffect(() => {
    fetchMetrics();
    fetchRecentAlerts();
    
    // 每5秒刷新一次数据
    const interval = setInterval(fetchMetrics, 5000);
    return () => clearInterval(interval);
  }, []);

  const fetchMetrics = async () => {
    try {
      const response = await axios.get('/api/system/metrics');
      setMetrics(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching metrics:', error);
      setLoading(false);
    }
  };

  const fetchRecentAlerts = async () => {
    try {
      // 模拟最近告警数据
      setAlerts([
        {
          id: '1',
          level: 'warning',
          message: 'CPU使用率达到82%',
          timestamp: dayjs().subtract(5, 'minute').format('YYYY-MM-DD HH:mm:ss'),
          status: 'active'
        },
        {
          id: '2',
          level: 'info',
          message: '数据采集器重连成功',
          timestamp: dayjs().subtract(15, 'minute').format('YYYY-MM-DD HH:mm:ss'),
          status: 'resolved'
        }
      ]);
    } catch (error) {
      console.error('Error fetching alerts:', error);
    }
  };

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return '#f5222d';
    if (value >= thresholds.warning) return '#faad14';
    return '#52c41a';
  };

  const getLatencyChartOption = () => {
    // 模拟延迟数据
    const data = Array.from({ length: 20 }, (_, i) => ({
      time: dayjs().subtract(20 - i, 'minute').format('HH:mm'),
      latency: 30 + Math.random() * 20
    }));

    return {
      title: {
        text: '延迟趋势 (微秒)',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}μs'
      },
      xAxis: {
        type: 'category',
        data: data.map(d => d.time)
      },
      yAxis: {
        type: 'value',
        name: '延迟 (μs)'
      },
      series: [{
        data: data.map(d => d.latency),
        type: 'line',
        smooth: true,
        itemStyle: { color: '#1890ff' }
      }]
    };
  };

  const getThroughputChartOption = () => {
    // 模拟吞吐量数据
    const data = Array.from({ length: 20 }, (_, i) => ({
      time: dayjs().subtract(20 - i, 'minute').format('HH:mm'),
      throughput: 800000 + Math.random() * 200000
    }));

    return {
      title: {
        text: '数据吞吐量 (条/秒)',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c} 条/秒'
      },
      xAxis: {
        type: 'category',
        data: data.map(d => d.time)
      },
      yAxis: {
        type: 'value',
        name: '吞吐量'
      },
      series: [{
        data: data.map(d => d.throughput),
        type: 'bar',
        itemStyle: { color: '#52c41a' }
      }]
    };
  };

  const alertColumns = [
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      render: (level: string) => {
        const config = {
          critical: { color: 'red', icon: <CloseCircleOutlined /> },
          warning: { color: 'orange', icon: <ExclamationCircleOutlined /> },
          info: { color: 'blue', icon: <CheckCircleOutlined /> }
        };
        const { color, icon } = config[level as keyof typeof config] || config.info;
        return <Tag color={color} icon={icon}>{level.toUpperCase()}</Tag>;
      }
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message'
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'red' : 'green'}>
          {status === 'active' ? '活跃' : '已解决'}
        </Tag>
      )
    }
  ];

  if (loading) {
    return <div>加载中...</div>;
  }

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic
              title="CPU使用率"
              value={metrics?.cpu_usage || 0}
              precision={1}
              suffix="%"
              valueStyle={{ color: getStatusColor(metrics?.cpu_usage || 0, { warning: 70, critical: 85 }) }}
            />
            <Progress
              percent={metrics?.cpu_usage || 0}
              strokeColor={getStatusColor(metrics?.cpu_usage || 0, { warning: 70, critical: 85 })}
              showInfo={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="内存使用率"
              value={metrics?.memory_usage || 0}
              precision={1}
              suffix="%"
              valueStyle={{ color: getStatusColor(metrics?.memory_usage || 0, { warning: 70, critical: 85 }) }}
            />
            <Progress
              percent={metrics?.memory_usage || 0}
              strokeColor={getStatusColor(metrics?.memory_usage || 0, { warning: 70, critical: 85 })}
              showInfo={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃连接数"
              value={metrics?.active_connections || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据吞吐量"
              value={metrics?.data_throughput || 0}
              precision={0}
              suffix="条/秒"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="P99延迟"
              value={metrics?.latency_p99 || 0}
              precision={1}
              suffix="μs"
              valueStyle={{ 
                color: getStatusColor(metrics?.latency_p99 || 0, { warning: 40, critical: 50 })
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="错误率"
              value={(metrics?.error_rate || 0) * 100}
              precision={3}
              suffix="%"
              valueStyle={{ 
                color: getStatusColor((metrics?.error_rate || 0) * 100, { warning: 0.1, critical: 1 })
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="磁盘使用率"
              value={metrics?.disk_usage || 0}
              precision={1}
              suffix="%"
              valueStyle={{ color: getStatusColor(metrics?.disk_usage || 0, { warning: 80, critical: 90 }) }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="网络IO"
              value={(metrics?.network_io?.rx_bytes || 0) / 1024 / 1024}
              precision={1}
              suffix="MB/s"
              prefix={<ArrowDownOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <Card title="延迟趋势">
            <ReactECharts option={getLatencyChartOption()} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="吞吐量趋势">
            <ReactECharts option={getThroughputChartOption()} style={{ height: '300px' }} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card title="最近告警">
            <Table
              columns={alertColumns}
              dataSource={alerts}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {metrics?.latency_p99 && metrics.latency_p99 > 50 && (
        <Alert
          message="性能告警"
          description="当前P99延迟超过50微秒阈值，请检查系统负载"
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  );
};

export default Dashboard;