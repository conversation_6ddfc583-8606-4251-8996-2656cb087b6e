version: '3.8'

services:
  # Load Balancer (HAProxy)
  load-balancer:
    image: haproxy:2.8
    container_name: financial-lb
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
      - "50051:50051"
    volumes:
      - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - financial-app-1
      - financial-app-2
    networks:
      - financial-network
    restart: unless-stopped

  # Primary Application Instance
  financial-app-1:
    build:
      context: ../../
      dockerfile: Dockerfile
    container_name: financial-app-1
    environment:
      - NODE_ID=1
      - ROLE=primary
      - REDIS_CLUSTER_NODES=redis-cluster-1:7000,redis-cluster-2:7000,redis-cluster-3:7000
      - CLICKHOUSE_CLUSTER_NODES=clickhouse-1:9000,clickhouse-2:9000,clickhouse-3:9000
      - KAFKA_BROKERS=kafka-1:9092,kafka-2:9092,kafka-3:9092
    volumes:
      - ./config:/app/config:ro
      - app_logs_1:/app/logs
      - app_data_1:/app/data
    depends_on:
      - redis-cluster-1
      - redis-cluster-2
      - redis-cluster-3
      - clickhouse-1
      - clickhouse-2
      - clickhouse-3
      - kafka-1
    networks:
      - financial-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G

  # Secondary Application Instance
  financial-app-2:
    build:
      context: ../../
      dockerfile: Dockerfile
    container_name: financial-app-2
    environment:
      - NODE_ID=2
      - ROLE=secondary
      - REDIS_CLUSTER_NODES=redis-cluster-1:7000,redis-cluster-2:7000,redis-cluster-3:7000
      - CLICKHOUSE_CLUSTER_NODES=clickhouse-1:9000,clickhouse-2:9000,clickhouse-3:9000
      - KAFKA_BROKERS=kafka-1:9092,kafka-2:9092,kafka-3:9092
    volumes:
      - ./config:/app/config:ro
      - app_logs_2:/app/logs
      - app_data_2:/app/data
    depends_on:
      - redis-cluster-1
      - redis-cluster-2
      - redis-cluster-3
      - clickhouse-1
      - clickhouse-2
      - clickhouse-3
      - kafka-1
    networks:
      - financial-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G

  # Redis Cluster (6 nodes for high availability)
  redis-cluster-1:
    image: redis:7-alpine
    container_name: financial-redis-cluster-1
    command: redis-server /usr/local/etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7001:7000"
      - "17001:17000"
    volumes:
      - ./redis-cluster.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_cluster_1_data:/data
    networks:
      - financial-network
    restart: unless-stopped

  redis-cluster-2:
    image: redis:7-alpine
    container_name: financial-redis-cluster-2
    command: redis-server /usr/local/etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7002:7000"
      - "17002:17000"
    volumes:
      - ./redis-cluster.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_cluster_2_data:/data
    networks:
      - financial-network
    restart: unless-stopped

  redis-cluster-3:
    image: redis:7-alpine
    container_name: financial-redis-cluster-3
    command: redis-server /usr/local/etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7003:7000"
      - "17003:17000"
    volumes:
      - ./redis-cluster.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_cluster_3_data:/data
    networks:
      - financial-network
    restart: unless-stopped

  redis-cluster-4:
    image: redis:7-alpine
    container_name: financial-redis-cluster-4
    command: redis-server /usr/local/etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7004:7000"
      - "17004:17000"
    volumes:
      - ./redis-cluster.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_cluster_4_data:/data
    networks:
      - financial-network
    restart: unless-stopped

  redis-cluster-5:
    image: redis:7-alpine
    container_name: financial-redis-cluster-5
    command: redis-server /usr/local/etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7005:7000"
      - "17005:17000"
    volumes:
      - ./redis-cluster.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_cluster_5_data:/data
    networks:
      - financial-network
    restart: unless-stopped

  redis-cluster-6:
    image: redis:7-alpine
    container_name: financial-redis-cluster-6
    command: redis-server /usr/local/etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7006:7000"
      - "17006:17000"
    volumes:
      - ./redis-cluster.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_cluster_6_data:/data
    networks:
      - financial-network
    restart: unless-stopped

  # ClickHouse Cluster (3 nodes)
  clickhouse-1:
    image: clickhouse/clickhouse-server:23.8
    container_name: financial-clickhouse-1
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - ./clickhouse-cluster.xml:/etc/clickhouse-server/config.xml:ro
      - ./clickhouse-users.xml:/etc/clickhouse-server/users.xml:ro
      - clickhouse_1_data:/var/lib/clickhouse
      - clickhouse_1_logs:/var/log/clickhouse-server
    environment:
      CLICKHOUSE_DB: market_data
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: ${CLICKHOUSE_PASSWORD}
    networks:
      - financial-network
    restart: unless-stopped
    ulimits:
      nofile:
        soft: 262144
        hard: 262144

  clickhouse-2:
    image: clickhouse/clickhouse-server:23.8
    container_name: financial-clickhouse-2
    ports:
      - "8124:8123"
      - "9001:9000"
    volumes:
      - ./clickhouse-cluster.xml:/etc/clickhouse-server/config.xml:ro
      - ./clickhouse-users.xml:/etc/clickhouse-server/users.xml:ro
      - clickhouse_2_data:/var/lib/clickhouse
      - clickhouse_2_logs:/var/log/clickhouse-server
    environment:
      CLICKHOUSE_DB: market_data
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: ${CLICKHOUSE_PASSWORD}
    networks:
      - financial-network
    restart: unless-stopped
    ulimits:
      nofile:
        soft: 262144
        hard: 262144

  clickhouse-3:
    image: clickhouse/clickhouse-server:23.8
    container_name: financial-clickhouse-3
    ports:
      - "8125:8123"
      - "9002:9000"
    volumes:
      - ./clickhouse-cluster.xml:/etc/clickhouse-server/config.xml:ro
      - ./clickhouse-users.xml:/etc/clickhouse-server/users.xml:ro
      - clickhouse_3_data:/var/lib/clickhouse
      - clickhouse_3_logs:/var/log/clickhouse-server
    environment:
      CLICKHOUSE_DB: market_data
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: ${CLICKHOUSE_PASSWORD}
    networks:
      - financial-network
    restart: unless-stopped
    ulimits:
      nofile:
        soft: 262144
        hard: 262144

  # Kafka Cluster (3 brokers)
  kafka-1:
    image: confluentinc/cp-kafka:7.4.0
    container_name: financial-kafka-1
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-1:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_MIN_INSYNC_REPLICAS: 2
    volumes:
      - kafka_1_data:/var/lib/kafka/data
    depends_on:
      - zookeeper-1
      - zookeeper-2
      - zookeeper-3
    networks:
      - financial-network
    restart: unless-stopped

  kafka-2:
    image: confluentinc/cp-kafka:7.4.0
    container_name: financial-kafka-2
    ports:
      - "9093:9092"
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-2:29092,PLAINTEXT_HOST://localhost:9093
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_MIN_INSYNC_REPLICAS: 2
    volumes:
      - kafka_2_data:/var/lib/kafka/data
    depends_on:
      - zookeeper-1
      - zookeeper-2
      - zookeeper-3
    networks:
      - financial-network
    restart: unless-stopped

  kafka-3:
    image: confluentinc/cp-kafka:7.4.0
    container_name: financial-kafka-3
    ports:
      - "9094:9092"
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-3:29092,PLAINTEXT_HOST://localhost:9094
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_MIN_INSYNC_REPLICAS: 2
    volumes:
      - kafka_3_data:/var/lib/kafka/data
    depends_on:
      - zookeeper-1
      - zookeeper-2
      - zookeeper-3
    networks:
      - financial-network
    restart: unless-stopped

  # Zookeeper Cluster (3 nodes)
  zookeeper-1:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: financial-zookeeper-1
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_SERVERS: zookeeper-1:2888:3888;zookeeper-2:2888:3888;zookeeper-3:2888:3888
    volumes:
      - zookeeper_1_data:/var/lib/zookeeper/data
      - zookeeper_1_logs:/var/lib/zookeeper/log
    networks:
      - financial-network
    restart: unless-stopped

  zookeeper-2:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: financial-zookeeper-2
    ports:
      - "2182:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SERVER_ID: 2
      ZOOKEEPER_SERVERS: zookeeper-1:2888:3888;zookeeper-2:2888:3888;zookeeper-3:2888:3888
    volumes:
      - zookeeper_2_data:/var/lib/zookeeper/data
      - zookeeper_2_logs:/var/lib/zookeeper/log
    networks:
      - financial-network
    restart: unless-stopped

  zookeeper-3:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: financial-zookeeper-3
    ports:
      - "2183:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SERVER_ID: 3
      ZOOKEEPER_SERVERS: zookeeper-1:2888:3888;zookeeper-2:2888:3888;zookeeper-3:2888:3888
    volumes:
      - zookeeper_3_data:/var/lib/zookeeper/data
      - zookeeper_3_logs:/var/lib/zookeeper/log
    networks:
      - financial-network
    restart: unless-stopped

  # MinIO Cluster (4 nodes)
  minio-1:
    image: minio/minio:latest
    container_name: financial-minio-1
    ports:
      - "9001:9000"
      - "9011:9001"
    volumes:
      - minio_1_data:/data
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    command: server http://minio-{1...4}/data --console-address ":9001"
    networks:
      - financial-network
    restart: unless-stopped

  minio-2:
    image: minio/minio:latest
    container_name: financial-minio-2
    ports:
      - "9002:9000"
      - "9012:9001"
    volumes:
      - minio_2_data:/data
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    command: server http://minio-{1...4}/data --console-address ":9001"
    networks:
      - financial-network
    restart: unless-stopped

  minio-3:
    image: minio/minio:latest
    container_name: financial-minio-3
    ports:
      - "9003:9000"
      - "9013:9001"
    volumes:
      - minio_3_data:/data
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    command: server http://minio-{1...4}/data --console-address ":9001"
    networks:
      - financial-network
    restart: unless-stopped

  minio-4:
    image: minio/minio:latest
    container_name: financial-minio-4
    ports:
      - "9004:9000"
      - "9014:9001"
    volumes:
      - minio_4_data:/data
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    command: server http://minio-{1...4}/data --console-address ":9001"
    networks:
      - financial-network
    restart: unless-stopped

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: financial-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - financial-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: financial-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    networks:
      - financial-network
    restart: unless-stopped

  # Log Aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: financial-elasticsearch
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - financial-network
    restart: unless-stopped

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: financial-logstash
    ports:
      - "5044:5044"
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
    depends_on:
      - elasticsearch
    networks:
      - financial-network
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: financial-kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - financial-network
    restart: unless-stopped

volumes:
  app_logs_1:
  app_logs_2:
  app_data_1:
  app_data_2:
  redis_cluster_1_data:
  redis_cluster_2_data:
  redis_cluster_3_data:
  redis_cluster_4_data:
  redis_cluster_5_data:
  redis_cluster_6_data:
  clickhouse_1_data:
  clickhouse_1_logs:
  clickhouse_2_data:
  clickhouse_2_logs:
  clickhouse_3_data:
  clickhouse_3_logs:
  kafka_1_data:
  kafka_2_data:
  kafka_3_data:
  zookeeper_1_data:
  zookeeper_1_logs:
  zookeeper_2_data:
  zookeeper_2_logs:
  zookeeper_3_data:
  zookeeper_3_logs:
  minio_1_data:
  minio_2_data:
  minio_3_data:
  minio_4_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  financial-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16