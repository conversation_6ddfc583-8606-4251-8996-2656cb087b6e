#!/usr/bin/env python3
"""
CTP采集器Docker部署测试脚本
测试CTP数据采集系统的Docker容器化部署
"""

import asyncio
import subprocess
import time
import json
import sys
import os
import requests
from datetime import datetime
from typing import Dict, List, Optional

def print_header():
    print("=" * 70)
    print("    CTP数据采集器 - Docker部署测试")
    print("=" * 70)
    print()

def print_info(msg):
    print(f"[INFO] {msg}")

def print_success(msg):
    print(f"[SUCCESS] ✅ {msg}")

def print_error(msg):
    print(f"[ERROR] ❌ {msg}")

def print_warning(msg):
    print(f"[WARNING] ⚠️  {msg}")

class CTPDockerTester:
    def __init__(self):
        self.compose_file = "docker-compose.ctp.yml"
        self.services = ["redis", "clickhouse", "ctp-collector"]
        self.test_results = []
    
    def check_docker_environment(self):
        """检查Docker环境"""
        print_info("检查Docker环境...")
        
        try:
            # 检查Docker
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print_success(f"Docker: {result.stdout.strip()}")
            else:
                print_error("Docker未安装或不可用")
                return False
            
            # 检查Docker Compose
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print_success(f"Docker Compose: {result.stdout.strip()}")
            else:
                print_error("Docker Compose未安装或不可用")
                return False
            
            # 检查Docker服务
            result = subprocess.run(['docker', 'info'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print_success("Docker服务运行正常")
                return True
            else:
                print_error("Docker服务未运行")
                return False
                
        except Exception as e:
            print_error(f"Docker环境检查失败: {e}")
            return False
    
    def build_ctp_image(self):
        """构建CTP采集器镜像"""
        print_info("构建CTP采集器Docker镜像...")
        
        try:
            # 检查Dockerfile是否存在
            if not os.path.exists("docker/ctp-collector.Dockerfile"):
                print_error("CTP Dockerfile不存在")
                return False
            
            # 构建镜像
            cmd = [
                'docker', 'build',
                '-f', 'docker/ctp-collector.Dockerfile',
                '-t', 'ctp-data-collector:latest',
                '.'
            ]
            
            print_info("开始构建镜像 (可能需要几分钟)...")
            start_time = time.time()
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            build_time = time.time() - start_time
            
            if result.returncode == 0:
                print_success(f"镜像构建成功 ({build_time:.1f}秒)")
                return True
            else:
                print_error("镜像构建失败")
                print_error(f"错误信息: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print_error("镜像构建超时")
            return False
        except Exception as e:
            print_error(f"镜像构建异常: {e}")
            return False
    
    def start_services(self):
        """启动服务"""
        print_info("启动CTP采集服务...")
        
        try:
            # 停止可能存在的服务
            subprocess.run(['docker-compose', '-f', self.compose_file, 'down'], 
                          capture_output=True, timeout=30)
            
            # 启动基础服务
            cmd = ['docker-compose', '-f', self.compose_file, 'up', '-d'] + self.services
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print_success("服务启动成功")
                
                # 等待服务就绪
                print_info("等待服务就绪...")
                time.sleep(30)
                
                return True
            else:
                print_error("服务启动失败")
                print_error(f"错误信息: {result.stderr}")
                return False
                
        except Exception as e:
            print_error(f"服务启动异常: {e}")
            return False
    
    def check_service_status(self):
        """检查服务状态"""
        print_info("检查服务状态...")
        
        try:
            result = subprocess.run(['docker-compose', '-f', self.compose_file, 'ps'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                running_services = []
                
                for line in lines[2:]:  # 跳过标题行
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            name = parts[0]
                            state = parts[2]
                            if 'Up' in state:
                                running_services.append(name)
                                print_success(f"✅ {name}: {state}")
                            else:
                                print_error(f"❌ {name}: {state}")
                
                return len(running_services) >= len(self.services) * 0.8  # 80%服务运行即可
            else:
                print_error("无法获取服务状态")
                return False
                
        except Exception as e:
            print_error(f"服务状态检查异常: {e}")
            return False
    
    def test_redis_connection(self):
        """测试Redis连接"""
        print_info("测试Redis连接...")
        
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
            
            # 测试连接
            response = r.ping()
            if response:
                print_success("Redis连接正常")
                
                # 测试基本操作
                test_key = f"ctp_test_{int(time.time())}"
                r.set(test_key, "CTP Docker测试", ex=60)
                value = r.get(test_key)
                
                if value and value.decode('utf-8') == "CTP Docker测试":
                    print_success("Redis读写操作正常")
                    r.delete(test_key)
                    return True
                else:
                    print_error("Redis读写操作失败")
                    return False
            else:
                print_error("Redis连接失败")
                return False
                
        except ImportError:
            print_warning("Redis模块未安装，跳过连接测试")
            return True
        except Exception as e:
            print_error(f"Redis连接异常: {e}")
            return False
    
    def test_clickhouse_connection(self):
        """测试ClickHouse连接"""
        print_info("测试ClickHouse连接...")
        
        try:
            # 测试HTTP接口
            response = requests.get('http://localhost:8123/ping', timeout=10)
            
            if response.status_code == 200 and 'Ok' in response.text:
                print_success("ClickHouse HTTP接口正常")
                
                # 测试查询
                query_response = requests.post('http://localhost:8123/', 
                                             data='SELECT version()',
                                             timeout=10)
                
                if query_response.status_code == 200:
                    version = query_response.text.strip()
                    print_success(f"ClickHouse查询正常 - 版本: {version}")
                    return True
                else:
                    print_warning("ClickHouse查询测试失败")
                    return False
            else:
                print_error("ClickHouse连接失败")
                return False
                
        except requests.RequestException as e:
            print_error(f"ClickHouse连接异常: {e}")
            return False
        except Exception as e:
            print_error(f"ClickHouse测试异常: {e}")
            return False
    
    def test_ctp_collector_health(self):
        """测试CTP采集器健康状态"""
        print_info("测试CTP采集器健康状态...")
        
        try:
            # 检查容器状态
            result = subprocess.run(['docker', 'ps', '--filter', 'name=ctp-data-collector', 
                                   '--format', '{{.Status}}'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                status = result.stdout.strip()
                if 'Up' in status:
                    print_success(f"CTP采集器容器运行正常: {status}")
                else:
                    print_error(f"CTP采集器容器状态异常: {status}")
                    return False
            else:
                print_error("CTP采集器容器未运行")
                return False
            
            # 检查容器日志
            result = subprocess.run(['docker', 'logs', '--tail', '10', 'ctp-data-collector'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logs = result.stdout.strip()
                if logs:
                    print_info("CTP采集器最新日志:")
                    for line in logs.split('\n')[-3:]:  # 显示最后3行
                        print(f"  {line}")
                    
                    # 检查是否有错误
                    if 'ERROR' in logs.upper() or 'EXCEPTION' in logs.upper():
                        print_warning("发现错误日志，请检查")
                    else:
                        print_success("日志正常")
                
                return True
            else:
                print_warning("无法获取容器日志")
                return True  # 不影响整体测试
                
        except Exception as e:
            print_error(f"CTP采集器健康检查异常: {e}")
            return False
    
    def test_data_collection_simulation(self):
        """测试数据采集模拟"""
        print_info("测试数据采集模拟...")
        
        try:
            # 模拟向Redis写入测试数据
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
            
            # 模拟股票数据
            test_data = {
                'symbol': '000001',
                'name': '平安银行',
                'price': 12.34,
                'volume': 1000000,
                'timestamp': datetime.now().isoformat(),
                'source': 'ctp_docker_test'
            }
            
            # 写入不同类型的测试数据
            data_types = ['stock', 'futures', 'index']
            for data_type in data_types:
                key = f"ctp_test:{data_type}:{test_data['symbol']}"
                r.setex(key, 300, json.dumps(test_data))
                print_success(f"✅ {data_type}数据写入成功")
            
            # 验证数据读取
            for data_type in data_types:
                key = f"ctp_test:{data_type}:{test_data['symbol']}"
                stored_data = r.get(key)
                if stored_data:
                    parsed_data = json.loads(stored_data.decode('utf-8'))
                    if parsed_data['source'] == 'ctp_docker_test':
                        print_success(f"✅ {data_type}数据读取验证成功")
                    else:
                        print_error(f"❌ {data_type}数据验证失败")
                        return False
                else:
                    print_error(f"❌ {data_type}数据读取失败")
                    return False
            
            # 清理测试数据
            for data_type in data_types:
                key = f"ctp_test:{data_type}:{test_data['symbol']}"
                r.delete(key)
            
            print_success("数据采集模拟测试通过")
            return True
            
        except ImportError:
            print_warning("Redis模块未安装，跳过数据采集测试")
            return True
        except Exception as e:
            print_error(f"数据采集模拟测试异常: {e}")
            return False
    
    def test_performance_metrics(self):
        """测试性能指标"""
        print_info("测试性能指标...")
        
        try:
            # 检查容器资源使用
            result = subprocess.run(['docker', 'stats', '--no-stream', '--format', 
                                   'table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                print_info("容器资源使用情况:")
                for line in lines:
                    if 'ctp-' in line:
                        print(f"  {line}")
                
                print_success("性能指标获取成功")
                return True
            else:
                print_warning("无法获取性能指标")
                return True  # 不影响整体测试
                
        except Exception as e:
            print_error(f"性能指标测试异常: {e}")
            return False
    
    def cleanup_services(self):
        """清理服务"""
        print_info("清理测试服务...")
        
        try:
            result = subprocess.run(['docker-compose', '-f', self.compose_file, 'down', '-v'], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print_success("服务清理完成")
            else:
                print_warning("服务清理可能不完整")
                
        except Exception as e:
            print_warning(f"服务清理异常: {e}")
    
    def show_deployment_summary(self):
        """显示部署总结"""
        print()
        print("=" * 70)
        print("CTP采集器Docker部署总结")
        print("=" * 70)
        
        # 显示测试结果
        passed = sum(1 for _, result in self.test_results if result)
        total = len(self.test_results)
        
        print(f"\n📊 测试结果: {passed}/{total} 项通过")
        print("-" * 50)
        
        for test_name, result in self.test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:25} : {status}")
        
        # 显示服务信息
        print(f"\n🚀 部署的服务:")
        print("  • CTP数据采集器: ctp-data-collector")
        print("  • Redis缓存: ctp-redis (localhost:6379)")
        print("  • ClickHouse数据库: ctp-clickhouse (localhost:8123)")
        
        # 显示使用建议
        print(f"\n💡 使用建议:")
        print("  • 查看日志: docker logs ctp-data-collector")
        print("  • 服务管理: docker-compose -f docker-compose.ctp.yml [up|down|ps]")
        print("  • 数据访问: redis-cli 或 curl http://localhost:8123/")
        print("  • 监控面板: docker-compose -f docker-compose.ctp.yml --profile monitoring up -d")
        
        return passed >= total * 0.8  # 80%通过率
    
    async def run_full_test(self):
        """运行完整测试"""
        print_header()
        
        # 测试项目列表
        tests = [
            ("Docker环境检查", self.check_docker_environment),
            ("构建CTP镜像", self.build_ctp_image),
            ("启动服务", self.start_services),
            ("服务状态检查", self.check_service_status),
            ("Redis连接测试", self.test_redis_connection),
            ("ClickHouse连接测试", self.test_clickhouse_connection),
            ("CTP采集器健康检查", self.test_ctp_collector_health),
            ("数据采集模拟", self.test_data_collection_simulation),
            ("性能指标测试", self.test_performance_metrics)
        ]
        
        # 执行测试
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                result = test_func()
                self.test_results.append((test_name, result))
                
                if not result:
                    print_error(f"{test_name} 失败，继续其他测试...")
                
            except Exception as e:
                print_error(f"{test_name} 异常: {e}")
                self.test_results.append((test_name, False))
        
        # 显示总结
        success = self.show_deployment_summary()
        
        # 清理资源
        if input("\n是否清理测试服务? (y/N): ").lower() == 'y':
            self.cleanup_services()
        
        return success

async def main():
    """主函数"""
    tester = CTPDockerTester()
    
    try:
        success = await tester.run_full_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        tester.cleanup_services()
        sys.exit(1)
    except Exception as e:
        print_error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())