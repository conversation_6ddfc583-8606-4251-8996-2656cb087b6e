#include "financial_data_sdk.h"
#include "market_data_service.grpc.pb.h"
#include <grpcpp/grpcpp.h>
#include <thread>
#include <queue>
#include <unordered_map>

namespace financial_data {
namespace sdk {

// Async streaming client with proper event handling
class StreamingAsyncClient {
public:
    explicit StreamingAsyncClient(std::shared_ptr<ConnectionPool> pool)
        : pool_(pool), running_(false), next_call_id_(1) {}

    ~StreamingAsyncClient() {
        Stop();
    }

    void Start() {
        if (running_.exchange(true)) {
            return;
        }
        
        worker_thread_ = std::thread(&StreamingAsyncClient::ProcessEvents, this);
    }

    void Stop() {
        if (!running_.exchange(false)) {
            return;
        }
        
        completion_queue_.Shutdown();
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        
        // Clean up active calls
        std::lock_guard<std::mutex> lock(calls_mutex_);
        active_calls_.clear();
    }

    // Start streaming tick data
    uint64_t StartTickDataStream(const SubscriptionConfig& config,
                                TickDataCallback callback,
                                ErrorCallback error_callback) {
        auto call_id = next_call_id_++;
        auto call = std::make_unique<TickDataCall>(call_id, callback, error_callback);
        
        auto channel = pool_->GetChannel();
        call->stub = MarketDataService::NewStub(channel);
        call->context.set_deadline(std::chrono::system_clock::now() + std::chrono::hours(24));
        
        // Prepare request
        for (const auto& symbol : config.symbols) {
            call->request.add_symbols(symbol);
        }
        call->request.set_exchange(config.exchange);
        call->request.set_include_level2(config.include_level2);
        call->request.set_buffer_size(config.buffer_size);
        
        // Start the stream
        call->reader = call->stub->AsyncStreamTickData(&call->context, call->request, &completion_queue_);
        
        // Store the call
        {
            std::lock_guard<std::mutex> lock(calls_mutex_);
            active_calls_[call_id] = std::move(call);
        }
        
        // Start reading
        StartRead(call_id);
        
        return call_id;
    }

    void StopStream(uint64_t call_id) {
        std::lock_guard<std::mutex> lock(calls_mutex_);
        auto it = active_calls_.find(call_id);
        if (it != active_calls_.end()) {
            it->second->context.TryCancel();
            active_calls_.erase(it);
        }
    }

private:
    struct TickDataCall {
        uint64_t call_id;
        TickDataCallback callback;
        ErrorCallback error_callback;
        
        grpc::ClientContext context;
        TickDataRequest request;
        TickDataResponse response;
        std::unique_ptr<MarketDataService::Stub> stub;
        std::unique_ptr<grpc::ClientAsyncReader<TickDataResponse>> reader;
        grpc::Status status;
        
        enum State {
            READING,
            FINISHED
        } state = READING;
        
        TickDataCall(uint64_t id, TickDataCallback cb, ErrorCallback err_cb)
            : call_id(id), callback(cb), error_callback(err_cb) {}
    };

    void ProcessEvents() {
        void* tag;
        bool ok;
        
        while (completion_queue_.Next(&tag, &ok)) {
            auto call_id = reinterpret_cast<uint64_t>(tag);
            
            std::lock_guard<std::mutex> lock(calls_mutex_);
            auto it = active_calls_.find(call_id);
            if (it == active_calls_.end()) {
                continue; // Call was cancelled
            }
            
            auto& call = it->second;
            
            if (!ok) {
                // Stream ended or error occurred
                HandleStreamEnd(call_id);
                continue;
            }
            
            switch (call->state) {
                case TickDataCall::READING:
                    HandleRead(call_id);
                    break;
                case TickDataCall::FINISHED:
                    HandleFinish(call_id);
                    break;
            }
        }
    }

    void StartRead(uint64_t call_id) {
        std::lock_guard<std::mutex> lock(calls_mutex_);
        auto it = active_calls_.find(call_id);
        if (it != active_calls_.end()) {
            auto& call = it->second;
            call->reader->Read(&call->response, reinterpret_cast<void*>(call_id));
        }
    }

    void HandleRead(uint64_t call_id) {
        auto it = active_calls_.find(call_id);
        if (it == active_calls_.end()) {
            return;
        }
        
        auto& call = it->second;
        
        // Process received data
        for (const auto& tick_proto : call->response.ticks()) {
            StandardTick tick;
            ConvertFromProto(tick_proto, tick);
            
            try {
                call->callback(tick);
            } catch (const std::exception& e) {
                if (call->error_callback) {
                    call->error_callback(ErrorInfo(ErrorCode::UNKNOWN_ERROR, 
                                                 "Callback error: " + std::string(e.what())));
                }
            }
        }
        
        // Continue reading
        StartRead(call_id);
    }

    void HandleStreamEnd(uint64_t call_id) {
        auto it = active_calls_.find(call_id);
        if (it == active_calls_.end()) {
            return;
        }
        
        auto& call = it->second;
        call->state = TickDataCall::FINISHED;
        
        // Get final status
        call->reader->Finish(&call->status, reinterpret_cast<void*>(call_id));
    }

    void HandleFinish(uint64_t call_id) {
        auto it = active_calls_.find(call_id);
        if (it == active_calls_.end()) {
            return;
        }
        
        auto& call = it->second;
        
        if (!call->status.ok() && call->error_callback) {
            ErrorCode error_code = ErrorCode::NETWORK_ERROR;
            if (call->status.error_code() == grpc::StatusCode::DEADLINE_EXCEEDED) {
                error_code = ErrorCode::TIMEOUT;
            } else if (call->status.error_code() == grpc::StatusCode::UNAUTHENTICATED) {
                error_code = ErrorCode::AUTHENTICATION_FAILED;
            }
            
            call->error_callback(ErrorInfo(error_code, call->status.error_message()));
        }
        
        // Remove the call
        active_calls_.erase(it);
    }

    void ConvertFromProto(const TickData& proto, StandardTick& tick) {
        tick.timestamp_ns = proto.timestamp();
        tick.symbol = proto.symbol();
        tick.exchange = proto.exchange();
        tick.last_price = proto.last_price();
        tick.volume = proto.volume();
        tick.turnover = proto.turnover();
        tick.open_interest = proto.open_interest();
        tick.sequence = proto.sequence();
        tick.trade_flag = proto.trade_flag();
        
        // Convert bid/ask levels
        size_t bid_count = std::min(static_cast<size_t>(proto.bids_size()), tick.bids.size());
        for (size_t i = 0; i < bid_count; ++i) {
            const auto& bid_proto = proto.bids(static_cast<int>(i));
            tick.bids[i] = PriceLevel(bid_proto.price(), 
                                    static_cast<uint32_t>(bid_proto.volume()),
                                    static_cast<uint32_t>(bid_proto.order_count()));
        }
        
        size_t ask_count = std::min(static_cast<size_t>(proto.asks_size()), tick.asks.size());
        for (size_t i = 0; i < ask_count; ++i) {
            const auto& ask_proto = proto.asks(static_cast<int>(i));
            tick.asks[i] = PriceLevel(ask_proto.price(),
                                    static_cast<uint32_t>(ask_proto.volume()),
                                    static_cast<uint32_t>(ask_proto.order_count()));
        }
    }

    std::shared_ptr<ConnectionPool> pool_;
    grpc::CompletionQueue completion_queue_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    
    std::atomic<uint64_t> next_call_id_;
    std::unordered_map<uint64_t, std::unique_ptr<TickDataCall>> active_calls_;
    std::mutex calls_mutex_;
};

} // namespace sdk
} // namespace financial_data