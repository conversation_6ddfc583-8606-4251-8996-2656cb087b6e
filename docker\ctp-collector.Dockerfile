# CTP数据采集器 Docker镜像
# 基于Python 3.11，包含PyTDX和CTP相关依赖

FROM python:3.11-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # 基础工具
    curl \
    wget \
    git \
    # 编译工具
    build-essential \
    gcc \
    g++ \
    # Python开发依赖
    python3-dev \
    # 网络和加密库
    libssl-dev \
    libffi-dev \
    # 数据库客户端
    libpq-dev \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建应用用户
RUN useradd -m -s /bin/bash -u 1000 ctpuser

# 复制requirements文件
COPY requirements_ctp.txt /app/

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements_ctp.txt

# 复制应用代码
COPY src/ /app/src/
COPY config/ /app/config/
COPY scripts/ /app/scripts/

# 创建必要目录
RUN mkdir -p /app/logs /app/data /app/cache

# 设置权限
RUN chown -R ctpuser:ctpuser /app

# 切换到应用用户
USER ctpuser

# 设置Python路径
ENV PYTHONPATH=/app/src

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python3 -c "import sys; sys.path.insert(0, '/app/src'); from collectors.pytdx_collector import PyTDXCollector; print('Health check passed')" || exit 1

# 暴露端口 (如果需要API服务)
EXPOSE 8080

# 启动命令
CMD ["python3", "-m", "collectors.pytdx_collector"]