#!/bin/bash

# Financial Data Service - Production Deployment Script
# This script handles the complete deployment process for production environment

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-production}"
BACKUP_DIR="/backup/financial-data-service"
LOG_FILE="/var/log/financial-data-service-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root or with sudo"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
    fi
    
    # Check available disk space (minimum 50GB)
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 52428800 ]]; then  # 50GB in KB
        error "Insufficient disk space. At least 50GB required."
    fi
    
    # Check available memory (minimum 16GB)
    available_memory=$(free -m | awk 'NR==2{print $2}')
    if [[ $available_memory -lt 16384 ]]; then  # 16GB in MB
        warning "Less than 16GB RAM available. Performance may be affected."
    fi
    
    success "Prerequisites check completed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "/var/log/financial-data-service"
    mkdir -p "/opt/financial-data-service/data"
    mkdir -p "/opt/financial-data-service/config"
    mkdir -p "/etc/financial-data-service"
    
    # Set proper permissions
    chown -R 1000:1000 "/opt/financial-data-service"
    chmod -R 755 "/opt/financial-data-service"
    
    success "Directories created successfully"
}

# Generate SSL certificates
generate_ssl_certificates() {
    log "Generating SSL certificates..."
    
    SSL_DIR="/etc/financial-data-service/ssl"
    mkdir -p "$SSL_DIR"
    
    if [[ ! -f "$SSL_DIR/financial-data-service.pem" ]]; then
        # Generate self-signed certificate for development/testing
        # In production, replace with proper certificates from CA
        openssl req -x509 -newkey rsa:4096 -keyout "$SSL_DIR/financial-data-service.key" \
                    -out "$SSL_DIR/financial-data-service.crt" -days 365 -nodes \
                    -subj "/C=CN/ST=Shanghai/L=Shanghai/O=Financial Data Service/CN=financial-data-service.local"
        
        # Combine certificate and key for HAProxy
        cat "$SSL_DIR/financial-data-service.crt" "$SSL_DIR/financial-data-service.key" > "$SSL_DIR/financial-data-service.pem"
        
        # Set proper permissions
        chmod 600 "$SSL_DIR"/*
        chown root:root "$SSL_DIR"/*
        
        success "SSL certificates generated"
    else
        log "SSL certificates already exist"
    fi
}

# Setup environment variables
setup_environment() {
    log "Setting up environment variables..."
    
    ENV_FILE="/etc/financial-data-service/.env"
    
    cat > "$ENV_FILE" << EOF
# Financial Data Service Environment Configuration
DEPLOYMENT_ENV=$DEPLOYMENT_ENV
NODE_ENV=production

# Database Passwords
REDIS_PASSWORD=$(openssl rand -base64 32)
CLICKHOUSE_PASSWORD=$(openssl rand -base64 32)
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=$(openssl rand -base64 32)
GRAFANA_ADMIN_PASSWORD=$(openssl rand -base64 32)

# JWT Secret
JWT_SECRET=$(openssl rand -base64 64)

# Encryption Keys
AES_ENCRYPTION_KEY=$(openssl rand -hex 32)

# Network Configuration
CLUSTER_NETWORK=172.20.0.0/16

# Performance Tuning
MAX_CONNECTIONS=10000
WORKER_PROCESSES=auto
BUFFER_SIZE=64k

# Monitoring
PROMETHEUS_RETENTION=30d
GRAFANA_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"
EOF
    
    chmod 600 "$ENV_FILE"
    chown root:root "$ENV_FILE"
    
    success "Environment variables configured"
}

# Optimize system parameters
optimize_system() {
    log "Optimizing system parameters..."
    
    # Network optimizations for high-frequency trading
    cat >> /etc/sysctl.conf << EOF

# Financial Data Service Optimizations
# Network optimizations
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.core.rmem_default = 65536
net.core.wmem_default = 65536
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_rmem = 4096 65536 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_slow_start_after_idle = 0

# Memory optimizations
vm.swappiness = 1
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# Kernel optimizations
kernel.sched_migration_cost_ns = 5000000
kernel.sched_autogroup_enabled = 0

# File descriptor limits
fs.file-max = 2097152
EOF
    
    # Apply sysctl changes
    sysctl -p
    
    # Set ulimits
    cat >> /etc/security/limits.conf << EOF

# Financial Data Service limits
* soft nofile 1048576
* hard nofile 1048576
* soft nproc 1048576
* hard nproc 1048576
root soft nofile 1048576
root hard nofile 1048576
EOF
    
    success "System parameters optimized"
}

# Setup monitoring and logging
setup_monitoring() {
    log "Setting up monitoring and logging..."
    
    # Create Prometheus configuration
    mkdir -p "/opt/financial-data-service/monitoring"
    
    cat > "/opt/financial-data-service/monitoring/prometheus.yml" << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'financial-data-service'
    static_configs:
      - targets: ['financial-app-1:8081', 'financial-app-2:8081']
    scrape_interval: 5s
    metrics_path: /metrics

  - job_name: 'redis-cluster'
    static_configs:
      - targets: ['redis-cluster-1:6379', 'redis-cluster-2:6379', 'redis-cluster-3:6379']

  - job_name: 'clickhouse-cluster'
    static_configs:
      - targets: ['clickhouse-1:8123', 'clickhouse-2:8123', 'clickhouse-3:8123']

  - job_name: 'kafka-cluster'
    static_configs:
      - targets: ['kafka-1:9092', 'kafka-2:9092', 'kafka-3:9092']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
EOF
    
    # Create alert rules
    cat > "/opt/financial-data-service/monitoring/alert_rules.yml" << EOF
groups:
  - name: financial_data_service_alerts
    rules:
      - alert: HighLatency
        expr: financial_data_latency_microseconds > 50
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "High latency detected"
          description: "End-to-end latency is {{ \$value }}μs, exceeding 50μs threshold"

      - alert: DataLoss
        expr: financial_data_loss_total > 0
        for: 0s
        labels:
          severity: critical
        annotations:
          summary: "Data loss detected"
          description: "{{ \$value }} data points lost"

      - alert: HighCPUUsage
        expr: cpu_usage_percent > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ \$value }}%"

      - alert: HighMemoryUsage
        expr: memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ \$value }}%"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ \$labels.job }} service is down"
EOF
    
    success "Monitoring and logging configured"
}

# Deploy application
deploy_application() {
    log "Deploying Financial Data Service application..."
    
    cd "$SCRIPT_DIR/production"
    
    # Pull latest images
    docker-compose -f docker-compose.prod.yml pull
    
    # Start infrastructure services first
    log "Starting infrastructure services..."
    docker-compose -f docker-compose.prod.yml up -d \
        zookeeper-1 zookeeper-2 zookeeper-3 \
        kafka-1 kafka-2 kafka-3 \
        redis-cluster-1 redis-cluster-2 redis-cluster-3 \
        redis-cluster-4 redis-cluster-5 redis-cluster-6 \
        clickhouse-1 clickhouse-2 clickhouse-3 \
        minio-1 minio-2 minio-3 minio-4
    
    # Wait for infrastructure to be ready
    log "Waiting for infrastructure services to be ready..."
    sleep 60
    
    # Initialize Redis cluster
    log "Initializing Redis cluster..."
    docker exec financial-redis-cluster-1 redis-cli --cluster create \
        redis-cluster-1:7000 redis-cluster-2:7000 redis-cluster-3:7000 \
        redis-cluster-4:7000 redis-cluster-5:7000 redis-cluster-6:7000 \
        --cluster-replicas 1 --cluster-yes
    
    # Initialize ClickHouse cluster
    log "Initializing ClickHouse cluster..."
    docker exec financial-clickhouse-1 clickhouse-client --query "
        CREATE DATABASE IF NOT EXISTS market_data ON CLUSTER financial_cluster;
        CREATE TABLE IF NOT EXISTS market_data.tick_data ON CLUSTER financial_cluster (
            timestamp DateTime64(9),
            symbol LowCardinality(String),
            exchange LowCardinality(String),
            last_price Float64,
            volume UInt64,
            turnover Float64,
            open_interest UInt64,
            bid_prices Array(Float64),
            bid_volumes Array(UInt32),
            ask_prices Array(Float64),
            ask_volumes Array(UInt32),
            sequence UInt32
        ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/tick_data', '{replica}')
        PARTITION BY toYYYYMM(timestamp)
        ORDER BY (symbol, timestamp);
    "
    
    # Start application services
    log "Starting application services..."
    docker-compose -f docker-compose.prod.yml up -d \
        financial-app-1 financial-app-2 \
        load-balancer
    
    # Start monitoring services
    log "Starting monitoring services..."
    docker-compose -f docker-compose.prod.yml up -d \
        prometheus grafana \
        elasticsearch logstash kibana
    
    success "Application deployed successfully"
}

# Run health checks
run_health_checks() {
    log "Running health checks..."
    
    # Wait for services to be fully ready
    sleep 30
    
    # Check load balancer
    if curl -f -s http://localhost/health > /dev/null; then
        success "Load balancer health check passed"
    else
        error "Load balancer health check failed"
    fi
    
    # Check WebSocket endpoint
    if curl -f -s -H "Upgrade: websocket" -H "Connection: Upgrade" \
            -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" \
            http://localhost:8080/ws > /dev/null; then
        success "WebSocket endpoint health check passed"
    else
        warning "WebSocket endpoint health check failed"
    fi
    
    # Check gRPC endpoint
    if nc -z localhost 50051; then
        success "gRPC endpoint health check passed"
    else
        warning "gRPC endpoint health check failed"
    fi
    
    # Check monitoring endpoints
    if curl -f -s http://localhost:9090/-/healthy > /dev/null; then
        success "Prometheus health check passed"
    else
        warning "Prometheus health check failed"
    fi
    
    if curl -f -s http://localhost:3000/api/health > /dev/null; then
        success "Grafana health check passed"
    else
        warning "Grafana health check failed"
    fi
    
    success "Health checks completed"
}

# Setup backup system
setup_backup() {
    log "Setting up backup system..."
    
    # Create backup script
    cat > "/usr/local/bin/financial-data-backup.sh" << 'EOF'
#!/bin/bash

BACKUP_DIR="/backup/financial-data-service"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_PATH="$BACKUP_DIR/$DATE"

mkdir -p "$BACKUP_PATH"

# Backup Redis data
docker exec financial-redis-cluster-1 redis-cli --cluster backup "$BACKUP_PATH/redis"

# Backup ClickHouse data
docker exec financial-clickhouse-1 clickhouse-client --query "BACKUP DATABASE market_data TO '$BACKUP_PATH/clickhouse'"

# Backup MinIO data
docker exec financial-minio-1 mc mirror /data "$BACKUP_PATH/minio"

# Backup configuration
cp -r /opt/financial-data-service/config "$BACKUP_PATH/"
cp /etc/financial-data-service/.env "$BACKUP_PATH/"

# Compress backup
tar -czf "$BACKUP_PATH.tar.gz" -C "$BACKUP_DIR" "$DATE"
rm -rf "$BACKUP_PATH"

# Clean old backups (keep 30 days)
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_PATH.tar.gz"
EOF
    
    chmod +x "/usr/local/bin/financial-data-backup.sh"
    
    # Setup cron job for automated backups
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/financial-data-backup.sh") | crontab -
    
    success "Backup system configured"
}

# Setup log rotation
setup_log_rotation() {
    log "Setting up log rotation..."
    
    cat > "/etc/logrotate.d/financial-data-service" << EOF
/var/log/financial-data-service/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    postrotate
        docker kill -s USR1 \$(docker ps -q --filter name=financial-app)
    endscript
}
EOF
    
    success "Log rotation configured"
}

# Create systemd service
create_systemd_service() {
    log "Creating systemd service..."
    
    cat > "/etc/systemd/system/financial-data-service.service" << EOF
[Unit]
Description=Financial Data Service
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$SCRIPT_DIR/production
ExecStart=/usr/bin/docker-compose -f docker-compose.prod.yml up -d
ExecStop=/usr/bin/docker-compose -f docker-compose.prod.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable financial-data-service
    
    success "Systemd service created and enabled"
}

# Print deployment summary
print_summary() {
    log "Deployment Summary"
    echo "===========================================" | tee -a "$LOG_FILE"
    echo "Financial Data Service has been deployed successfully!" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    echo "Service Endpoints:" | tee -a "$LOG_FILE"
    echo "  - Load Balancer: http://localhost" | tee -a "$LOG_FILE"
    echo "  - WebSocket: ws://localhost:8080/ws" | tee -a "$LOG_FILE"
    echo "  - gRPC: localhost:50051" | tee -a "$LOG_FILE"
    echo "  - Monitoring: http://localhost:3000 (admin/\$GRAFANA_ADMIN_PASSWORD)" | tee -a "$LOG_FILE"
    echo "  - Prometheus: http://localhost:9090" | tee -a "$LOG_FILE"
    echo "  - Kibana: http://localhost:5601" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    echo "Configuration:" | tee -a "$LOG_FILE"
    echo "  - Environment file: /etc/financial-data-service/.env" | tee -a "$LOG_FILE"
    echo "  - SSL certificates: /etc/financial-data-service/ssl/" | tee -a "$LOG_FILE"
    echo "  - Data directory: /opt/financial-data-service/data/" | tee -a "$LOG_FILE"
    echo "  - Backup directory: $BACKUP_DIR" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    echo "Management Commands:" | tee -a "$LOG_FILE"
    echo "  - Start service: systemctl start financial-data-service" | tee -a "$LOG_FILE"
    echo "  - Stop service: systemctl stop financial-data-service" | tee -a "$LOG_FILE"
    echo "  - View logs: journalctl -u financial-data-service" | tee -a "$LOG_FILE"
    echo "  - Manual backup: /usr/local/bin/financial-data-backup.sh" | tee -a "$LOG_FILE"
    echo "===========================================" | tee -a "$LOG_FILE"
}

# Main deployment function
main() {
    log "Starting Financial Data Service deployment..."
    
    check_prerequisites
    create_directories
    generate_ssl_certificates
    setup_environment
    optimize_system
    setup_monitoring
    deploy_application
    run_health_checks
    setup_backup
    setup_log_rotation
    create_systemd_service
    
    success "Deployment completed successfully!"
    print_summary
}

# Handle script arguments
case "${1:-}" in
    "health-check")
        run_health_checks
        ;;
    "backup")
        /usr/local/bin/financial-data-backup.sh
        ;;
    "optimize")
        optimize_system
        ;;
    *)
        main
        ;;
esac