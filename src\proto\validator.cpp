#include "validator.h"
#include <sstream>
#include <algorithm>
#include <chrono>

namespace financial_data {

// DataValidator implementation
ValidationResult DataValidator::ValidateTick(const StandardTick& tick) {
    // 基本字段验证
    if (tick.symbol.empty()) {
        return ValidationResult::INVALID_SYMBOL;
    }
    
    if (tick.timestamp_ns <= 0) {
        return ValidationResult::INVALID_TIMESTAMP;
    }
    
    if (tick.last_price <= 0.0) {
        return ValidationResult::INVALID_PRICE;
    }
    
    // 时间戳验证
    if (!ValidateTimestamp(tick.timestamp_ns, tick.symbol)) {
        return ValidationResult::TIMESTAMP_ANOMALY;
    }
    
    // 序列号验证
    if (!ValidateSequence(tick.symbol, tick.sequence)) {
        return ValidationResult::SEQUENCE_GAP;
    }
    
    // 价格异常检测
    if (price_config_.enable_price_jump_detection && 
        !DetectPriceAnomaly(tick.symbol, tick.last_price)) {
        return ValidationResult::PRICE_ANOMALY;
    }
    
    // 价格范围检查
    if (tick.last_price < price_config_.min_price || 
        tick.last_price > price_config_.max_price) {
        return ValidationResult::INVALID_PRICE;
    }
    
    // 更新缓存
    last_tick_cache_[tick.symbol] = tick;
    last_sequence_cache_[tick.symbol] = tick.sequence;
    last_timestamp_cache_[tick.symbol] = tick.timestamp_ns;
    
    // 更新价格统计
    price_stats_cache_[tick.symbol].Update(tick.last_price);
    
    return ValidationResult::VALID;
}

ValidationResult DataValidator::ValidateLevel2(const Level2Data& level2) {
    // 基本字段验证
    if (level2.symbol.empty()) {
        return ValidationResult::INVALID_SYMBOL;
    }
    
    if (level2.timestamp_ns <= 0) {
        return ValidationResult::INVALID_TIMESTAMP;
    }
    
    if (level2.bids.empty() || level2.asks.empty()) {
        return ValidationResult::INVALID_PRICE;
    }
    
    // 时间戳验证
    if (!ValidateTimestamp(level2.timestamp_ns, level2.symbol)) {
        return ValidationResult::TIMESTAMP_ANOMALY;
    }
    
    // 序列号验证
    if (!ValidateSequence(level2.symbol, level2.sequence)) {
        return ValidationResult::SEQUENCE_GAP;
    }
    
    // 买卖价差验证
    if (!ValidateSpread(level2.bids, level2.asks)) {
        return ValidationResult::PRICE_ANOMALY;
    }
    
    // 档位价格验证
    for (const auto& bid : level2.bids) {
        if (bid.price <= 0.0 || bid.volume == 0) {
            return ValidationResult::INVALID_PRICE;
        }
        if (bid.price < price_config_.min_price || bid.price > price_config_.max_price) {
            return ValidationResult::INVALID_PRICE;
        }
    }
    
    for (const auto& ask : level2.asks) {
        if (ask.price <= 0.0 || ask.volume == 0) {
            return ValidationResult::INVALID_PRICE;
        }
        if (ask.price < price_config_.min_price || ask.price > price_config_.max_price) {
            return ValidationResult::INVALID_PRICE;
        }
    }
    
    // 买盘价格递减检查
    for (size_t i = 1; i < level2.bids.size(); ++i) {
        if (level2.bids[i].price > level2.bids[i-1].price) {
            return ValidationResult::INVALID_PRICE;
        }
    }
    
    // 卖盘价格递增检查
    for (size_t i = 1; i < level2.asks.size(); ++i) {
        if (level2.asks[i].price < level2.asks[i-1].price) {
            return ValidationResult::INVALID_PRICE;
        }
    }
    
    // 更新缓存
    last_level2_cache_[level2.symbol] = level2;
    last_sequence_cache_[level2.symbol] = level2.sequence;
    last_timestamp_cache_[level2.symbol] = level2.timestamp_ns;
    
    return ValidationResult::VALID;
}

ValidationResult DataValidator::ValidateMarketData(const MarketDataWrapper& wrapper) {
    switch (wrapper.type) {
        case MarketDataWrapper::DataType::TICK:
            return ValidateTick(wrapper.tick_data);
        case MarketDataWrapper::DataType::LEVEL2:
            return ValidateLevel2(wrapper.level2_data);
        default:
            return ValidationResult::INVALID_PRICE;
    }
}

std::vector<ValidationResult> DataValidator::ValidateBatch(const MarketDataBatch& batch) {
    std::vector<ValidationResult> results;
    results.reserve(batch.data.size());
    
    for (const auto& data : batch.data) {
        results.push_back(ValidateMarketData(data));
    }
    
    return results;
}

bool DataValidator::DetectPriceAnomaly(const std::string& symbol, double current_price) {
    auto it = last_tick_cache_.find(symbol);
    if (it == last_tick_cache_.end()) {
        return true; // 第一次数据，认为正常
    }
    
    double last_price = it->second.last_price;
    double price_change_ratio = std::abs(current_price - last_price) / last_price;
    
    if (price_change_ratio > price_config_.max_price_change_ratio) {
        return false; // 价格变动过大
    }
    
    // 使用统计方法检测异常
    auto stats_it = price_stats_cache_.find(symbol);
    if (stats_it != price_stats_cache_.end() && stats_it->second.count >= 10) {
        const auto& stats = stats_it->second;
        double mean = stats.GetMean();
        double std_dev = stats.GetStdDev();
        
        // 3-sigma规则检测异常
        if (std_dev > 0 && std::abs(current_price - mean) > 3 * std_dev) {
            return false;
        }
    }
    
    return true;
}

bool DataValidator::ValidateTimestamp(int64_t timestamp_ns, const std::string& symbol) {
    auto now = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
    
    // 检查未来时间
    if (timestamp_ns > now + timestamp_config_.max_future_offset_ns) {
        return false;
    }
    
    // 检查过去时间
    if (timestamp_ns < now - timestamp_config_.max_past_offset_ns) {
        return false;
    }
    
    // 单调性检查
    if (timestamp_config_.enable_monotonic_check && !symbol.empty()) {
        auto it = last_timestamp_cache_.find(symbol);
        if (it != last_timestamp_cache_.end() && timestamp_ns < it->second) {
            return false; // 时间戳倒退
        }
    }
    
    return true;
}

bool DataValidator::ValidateSequence(const std::string& symbol, uint32_t sequence) {
    auto it = last_sequence_cache_.find(symbol);
    if (it == last_sequence_cache_.end()) {
        return true; // 第一次数据
    }
    
    uint32_t last_sequence = it->second;
    
    // 检查重复序列号
    if (sequence == last_sequence) {
        return false;
    }
    
    // 检查序列号重置
    if (sequence_config_.allow_sequence_reset && 
        sequence < last_sequence && 
        last_sequence > sequence_config_.sequence_reset_threshold) {
        return true; // 允许序列号重置
    }
    
    // 检查序列号间隔
    if (sequence > last_sequence) {
        uint32_t gap = sequence - last_sequence;
        if (gap > sequence_config_.max_sequence_gap) {
            return false; // 序列号间隔过大
        }
    } else {
        return false; // 序列号倒退且不是重置
    }
    
    return true;
}

bool DataValidator::ValidateSpread(const std::vector<PriceLevel>& bids, const std::vector<PriceLevel>& asks) {
    if (bids.empty() || asks.empty()) {
        return false;
    }
    
    double best_bid = bids[0].price;
    double best_ask = asks[0].price;
    
    if (best_bid >= best_ask) {
        return false; // 买价不能大于等于卖价
    }
    
    double spread = best_ask - best_bid;
    double spread_ratio = spread / best_bid;
    
    return spread_ratio <= price_config_.max_spread_ratio;
}

void DataValidator::ClearCache() {
    last_tick_cache_.clear();
    last_level2_cache_.clear();
    last_sequence_cache_.clear();
    last_timestamp_cache_.clear();
    price_stats_cache_.clear();
}

void DataValidator::ClearSymbolCache(const std::string& symbol) {
    last_tick_cache_.erase(symbol);
    last_level2_cache_.erase(symbol);
    last_sequence_cache_.erase(symbol);
    last_timestamp_cache_.erase(symbol);
    price_stats_cache_.erase(symbol);
}

size_t DataValidator::GetCacheSize() const {
    return last_tick_cache_.size() + last_level2_cache_.size() + 
           last_sequence_cache_.size() + last_timestamp_cache_.size();
}

DataValidator::PriceStats DataValidator::GetPriceStats(const std::string& symbol) const {
    auto it = price_stats_cache_.find(symbol);
    return it != price_stats_cache_.end() ? it->second : PriceStats{};
}

std::string DataValidator::ValidationResultToString(ValidationResult result) {
    switch (result) {
        case ValidationResult::VALID: return "VALID";
        case ValidationResult::INVALID_TIMESTAMP: return "INVALID_TIMESTAMP";
        case ValidationResult::INVALID_PRICE: return "INVALID_PRICE";
        case ValidationResult::INVALID_SEQUENCE: return "INVALID_SEQUENCE";
        case ValidationResult::PRICE_ANOMALY: return "PRICE_ANOMALY";
        case ValidationResult::TIMESTAMP_ANOMALY: return "TIMESTAMP_ANOMALY";
        case ValidationResult::SEQUENCE_GAP: return "SEQUENCE_GAP";
        case ValidationResult::DUPLICATE_SEQUENCE: return "DUPLICATE_SEQUENCE";
        case ValidationResult::INVALID_SYMBOL: return "INVALID_SYMBOL";
        case ValidationResult::INVALID_VOLUME: return "INVALID_VOLUME";
        default: return "UNKNOWN";
    }
}

// DataQualityMonitor implementation
ValidationResult DataQualityMonitor::MonitorData(const MarketDataWrapper& data) {
    ValidationResult result = validator_.ValidateMarketData(data);
    
    // 更新全局统计
    global_metrics_.total_records++;
    
    // 更新符号统计
    std::string symbol;
    switch (data.type) {
        case MarketDataWrapper::DataType::TICK:
            symbol = data.tick_data.symbol;
            break;
        case MarketDataWrapper::DataType::LEVEL2:
            symbol = data.level2_data.symbol;
            break;
        default:
            symbol = "unknown";
    }
    
    auto& symbol_metrics = symbol_metrics_[symbol];
    symbol_metrics.total_records++;
    
    // 根据验证结果更新统计
    if (result == ValidationResult::VALID) {
        global_metrics_.valid_records++;
        symbol_metrics.valid_records++;
    } else {
        switch (result) {
            case ValidationResult::INVALID_TIMESTAMP:
                global_metrics_.invalid_timestamp++;
                symbol_metrics.invalid_timestamp++;
                break;
            case ValidationResult::INVALID_PRICE:
                global_metrics_.invalid_price++;
                symbol_metrics.invalid_price++;
                break;
            case ValidationResult::INVALID_SEQUENCE:
                global_metrics_.invalid_sequence++;
                symbol_metrics.invalid_sequence++;
                break;
            case ValidationResult::PRICE_ANOMALY:
                global_metrics_.price_anomalies++;
                symbol_metrics.price_anomalies++;
                break;
            case ValidationResult::TIMESTAMP_ANOMALY:
                global_metrics_.timestamp_anomalies++;
                symbol_metrics.timestamp_anomalies++;
                break;
            case ValidationResult::SEQUENCE_GAP:
                global_metrics_.sequence_gaps++;
                symbol_metrics.sequence_gaps++;
                break;
            case ValidationResult::DUPLICATE_SEQUENCE:
                global_metrics_.duplicate_sequences++;
                symbol_metrics.duplicate_sequences++;
                break;
            default:
                break;
        }
    }
    
    return result;
}

std::vector<ValidationResult> DataQualityMonitor::MonitorBatch(const MarketDataBatch& batch) {
    std::vector<ValidationResult> results;
    results.reserve(batch.data.size());
    
    for (const auto& data : batch.data) {
        results.push_back(MonitorData(data));
    }
    
    return results;
}

DataQualityMonitor::QualityMetrics DataQualityMonitor::GetSymbolMetrics(const std::string& symbol) const {
    auto it = symbol_metrics_.find(symbol);
    return it != symbol_metrics_.end() ? it->second : QualityMetrics{};
}

std::string DataQualityMonitor::GenerateQualityReport() const {
    std::ostringstream oss;
    
    oss << "=== Data Quality Report ===\n";
    oss << "Global Metrics:\n";
    oss << "  Total Records: " << global_metrics_.total_records << "\n";
    oss << "  Valid Records: " << global_metrics_.valid_records << "\n";
    oss << "  Validity Rate: " << (global_metrics_.GetValidityRate() * 100) << "%\n";
    oss << "  Invalid Timestamp: " << global_metrics_.invalid_timestamp << "\n";
    oss << "  Invalid Price: " << global_metrics_.invalid_price << "\n";
    oss << "  Invalid Sequence: " << global_metrics_.invalid_sequence << "\n";
    oss << "  Price Anomalies: " << global_metrics_.price_anomalies << "\n";
    oss << "  Timestamp Anomalies: " << global_metrics_.timestamp_anomalies << "\n";
    oss << "  Sequence Gaps: " << global_metrics_.sequence_gaps << "\n";
    oss << "  Duplicate Sequences: " << global_metrics_.duplicate_sequences << "\n\n";
    
    oss << "Symbol Metrics:\n";
    for (const auto& [symbol, metrics] : symbol_metrics_) {
        oss << "  " << symbol << ":\n";
        oss << "    Total: " << metrics.total_records;
        oss << ", Valid: " << metrics.valid_records;
        oss << ", Rate: " << (metrics.GetValidityRate() * 100) << "%\n";
    }
    
    return oss.str();
}

void DataQualityMonitor::ResetMetrics() {
    global_metrics_ = QualityMetrics{};
    symbol_metrics_.clear();
    window_start_time_ = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
}

void DataQualityMonitor::ResetSymbolMetrics(const std::string& symbol) {
    symbol_metrics_.erase(symbol);
}

bool DataQualityMonitor::ShouldResetWindow() const {
    auto now = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
    return (now - window_start_time_) >= window_duration_ns_;
}

// DataIntegrityChecker implementation
DataIntegrityChecker::DataIntegrityChecker() {
    // 设置默认必需字段
    required_fields_ = {
        "symbol", "timestamp_ns", "last_price", "volume"
    };
}

void DataIntegrityChecker::SetRequiredFields(const std::vector<std::string>& fields) {
    required_fields_.clear();
    for (const auto& field : fields) {
        required_fields_.insert(field);
    }
}

void DataIntegrityChecker::SetPriceRange(const std::string& symbol, double min_price, double max_price) {
    price_ranges_[symbol] = {min_price, max_price};
}

void DataIntegrityChecker::SetVolumeRange(const std::string& symbol, uint64_t min_volume, uint64_t max_volume) {
    volume_ranges_[symbol] = {min_volume, max_volume};
}

bool DataIntegrityChecker::CheckIntegrity(const StandardTick& tick) const {
    return CheckFieldCompleteness(tick) && 
           CheckValueRanges(tick) && 
           CheckLogicalConsistency(tick);
}

bool DataIntegrityChecker::CheckIntegrity(const Level2Data& level2) const {
    return CheckFieldCompleteness(level2) && 
           CheckValueRanges(level2) && 
           CheckLogicalConsistency(level2);
}

bool DataIntegrityChecker::CheckIntegrity(const MarketDataWrapper& wrapper) const {
    switch (wrapper.type) {
        case MarketDataWrapper::DataType::TICK:
            return CheckIntegrity(wrapper.tick_data);
        case MarketDataWrapper::DataType::LEVEL2:
            return CheckIntegrity(wrapper.level2_data);
        default:
            return false;
    }
}

bool DataIntegrityChecker::CheckFieldCompleteness(const StandardTick& tick) const {
    if (required_fields_.count("symbol") && tick.symbol.empty()) return false;
    if (required_fields_.count("timestamp_ns") && tick.timestamp_ns <= 0) return false;
    if (required_fields_.count("last_price") && tick.last_price <= 0.0) return false;
    if (required_fields_.count("volume") && tick.volume == 0) return false;
    return true;
}

bool DataIntegrityChecker::CheckFieldCompleteness(const Level2Data& level2) const {
    if (required_fields_.count("symbol") && level2.symbol.empty()) return false;
    if (required_fields_.count("timestamp_ns") && level2.timestamp_ns <= 0) return false;
    if (level2.bids.empty() || level2.asks.empty()) return false;
    return true;
}

bool DataIntegrityChecker::CheckValueRanges(const StandardTick& tick) const {
    // 检查价格范围
    auto price_it = price_ranges_.find(tick.symbol);
    if (price_it != price_ranges_.end()) {
        if (tick.last_price < price_it->second.first || 
            tick.last_price > price_it->second.second) {
            return false;
        }
    }
    
    // 检查成交量范围
    auto volume_it = volume_ranges_.find(tick.symbol);
    if (volume_it != volume_ranges_.end()) {
        if (tick.volume < volume_it->second.first || 
            tick.volume > volume_it->second.second) {
            return false;
        }
    }
    
    return true;
}

bool DataIntegrityChecker::CheckValueRanges(const Level2Data& level2) const {
    // 检查买卖盘价格范围
    auto price_it = price_ranges_.find(level2.symbol);
    if (price_it != price_ranges_.end()) {
        for (const auto& bid : level2.bids) {
            if (bid.price < price_it->second.first || 
                bid.price > price_it->second.second) {
                return false;
            }
        }
        for (const auto& ask : level2.asks) {
            if (ask.price < price_it->second.first || 
                ask.price > price_it->second.second) {
                return false;
            }
        }
    }
    
    return true;
}

bool DataIntegrityChecker::CheckLogicalConsistency(const StandardTick& tick) const {
    // 检查成交额与价格、成交量的一致性
    if (tick.turnover > 0.0 && tick.volume > 0 && tick.last_price > 0.0) {
        double expected_turnover = tick.last_price * tick.volume;
        double turnover_diff = std::abs(tick.turnover - expected_turnover) / expected_turnover;
        if (turnover_diff > 0.01) { // 允许1%的误差
            return false;
        }
    }
    
    return true;
}

bool DataIntegrityChecker::CheckLogicalConsistency(const Level2Data& level2) const {
    // 检查买卖盘价格顺序
    if (!level2.bids.empty()) {
        for (size_t i = 1; i < level2.bids.size(); ++i) {
            if (level2.bids[i].price > level2.bids[i-1].price) {
                return false; // 买盘价格应该递减
            }
        }
    }
    
    if (!level2.asks.empty()) {
        for (size_t i = 1; i < level2.asks.size(); ++i) {
            if (level2.asks[i].price < level2.asks[i-1].price) {
                return false; // 卖盘价格应该递增
            }
        }
    }
    
    // 检查买卖价差
    if (!level2.bids.empty() && !level2.asks.empty()) {
        if (level2.bids[0].price >= level2.asks[0].price) {
            return false; // 最优买价应该小于最优卖价
        }
    }
    
    return true;
}

} // namespace financial_data