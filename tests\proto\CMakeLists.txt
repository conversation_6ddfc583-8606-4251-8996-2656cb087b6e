# Protocol tests

add_executable(proto_tests
    test_validator.cpp
    test_serializer.cpp
)

target_link_libraries(proto_tests
    proto
    gtest
    gtest_main
    gmock
    gmock_main
)

target_include_directories(proto_tests PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Add tests to CTest
add_test(NAME ProtoValidatorTests COMMAND proto_tests --gtest_filter="ValidatorTest.*")
add_test(NAME ProtoSerializerTests COMMAND proto_tests --gtest_filter="SerializerTest.*")