#include "serializer.h"
#include <algorithm>
#include <cstring>

namespace financial_data {

// Serializer implementation
bool Serializer::SerializeTick(const StandardTick& tick, TickData* pb_tick) {
    if (!pb_tick) return false;
    
    pb_tick->set_timestamp_ns(tick.timestamp_ns);
    pb_tick->set_symbol(tick.symbol);
    pb_tick->set_exchange(tick.exchange);
    pb_tick->set_last_price(tick.last_price);
    pb_tick->set_volume(tick.volume);
    pb_tick->set_turnover(tick.turnover);
    pb_tick->set_open_interest(tick.open_interest);
    pb_tick->set_sequence(tick.sequence);
    pb_tick->set_trade_flag(tick.trade_flag);
    
    return true;
}

bool Serializer::SerializeLevel2(const Level2Data& level2, Level2Data* pb_level2) {
    if (!pb_level2) return false;
    
    pb_level2->set_timestamp_ns(level2.timestamp_ns);
    pb_level2->set_symbol(level2.symbol);
    pb_level2->set_exchange(level2.exchange);
    pb_level2->set_sequence(level2.sequence);
    
    // 序列化买盘档位
    for (const auto& bid : level2.bids) {
        auto* pb_bid = pb_level2->add_bids();
        pb_bid->set_price(bid.price);
        pb_bid->set_volume(bid.volume);
        pb_bid->set_order_count(bid.order_count);
    }
    
    // 序列化卖盘档位
    for (const auto& ask : level2.asks) {
        auto* pb_ask = pb_level2->add_asks();
        pb_ask->set_price(ask.price);
        pb_ask->set_volume(ask.volume);
        pb_ask->set_order_count(ask.order_count);
    }
    
    return true;
}

bool Serializer::SerializeMarketData(const MarketDataWrapper& wrapper, MarketData* pb_data) {
    if (!pb_data) return false;
    
    pb_data->set_receive_time_ns(wrapper.receive_time_ns);
    pb_data->set_source(wrapper.source);
    
    switch (wrapper.type) {
        case MarketDataWrapper::DataType::TICK: {
            auto* pb_tick = pb_data->mutable_tick();
            return SerializeTick(wrapper.tick_data, pb_tick);
        }
        case MarketDataWrapper::DataType::LEVEL2: {
            auto* pb_level2 = pb_data->mutable_level2();
            return SerializeLevel2(wrapper.level2_data, pb_level2);
        }
        default:
            return false;
    }
}

bool Serializer::SerializeBatch(const MarketDataBatch& batch, MarketDataBatch* pb_batch) {
    if (!pb_batch) return false;
    
    pb_batch->set_batch_timestamp_ns(batch.batch_timestamp_ns);
    pb_batch->set_batch_sequence(batch.batch_sequence);
    
    for (const auto& data : batch.data) {
        auto* pb_data = pb_batch->add_data();
        if (!SerializeMarketData(data, pb_data)) {
            return false;
        }
    }
    
    return true;
}

bool Serializer::DeserializeTick(const TickData& pb_tick, StandardTick* tick) {
    if (!tick) return false;
    
    tick->timestamp_ns = pb_tick.timestamp_ns();
    tick->symbol = pb_tick.symbol();
    tick->exchange = pb_tick.exchange();
    tick->last_price = pb_tick.last_price();
    tick->volume = pb_tick.volume();
    tick->turnover = pb_tick.turnover();
    tick->open_interest = pb_tick.open_interest();
    tick->sequence = pb_tick.sequence();
    tick->trade_flag = pb_tick.trade_flag();
    
    return true;
}

bool Serializer::DeserializeLevel2(const Level2Data& pb_level2, Level2Data* level2) {
    if (!level2) return false;
    
    level2->timestamp_ns = pb_level2.timestamp_ns();
    level2->symbol = pb_level2.symbol();
    level2->exchange = pb_level2.exchange();
    level2->sequence = pb_level2.sequence();
    
    // 反序列化买盘档位
    level2->bids.clear();
    level2->bids.reserve(pb_level2.bids_size());
    for (const auto& pb_bid : pb_level2.bids()) {
        level2->bids.emplace_back(pb_bid.price(), pb_bid.volume(), pb_bid.order_count());
    }
    
    // 反序列化卖盘档位
    level2->asks.clear();
    level2->asks.reserve(pb_level2.asks_size());
    for (const auto& pb_ask : pb_level2.asks()) {
        level2->asks.emplace_back(pb_ask.price(), pb_ask.volume(), pb_ask.order_count());
    }
    
    return true;
}

bool Serializer::DeserializeMarketData(const MarketData& pb_data, MarketDataWrapper* wrapper) {
    if (!wrapper) return false;
    
    wrapper->receive_time_ns = pb_data.receive_time_ns();
    wrapper->source = pb_data.source();
    
    if (pb_data.has_tick()) {
        wrapper->type = MarketDataWrapper::DataType::TICK;
        return DeserializeTick(pb_data.tick(), &wrapper->tick_data);
    } else if (pb_data.has_level2()) {
        wrapper->type = MarketDataWrapper::DataType::LEVEL2;
        return DeserializeLevel2(pb_data.level2(), &wrapper->level2_data);
    }
    
    return false;
}

bool Serializer::DeserializeBatch(const MarketDataBatch& pb_batch, MarketDataBatch* batch) {
    if (!batch) return false;
    
    batch->batch_timestamp_ns = pb_batch.batch_timestamp_ns();
    batch->batch_sequence = pb_batch.batch_sequence();
    
    batch->data.clear();
    batch->data.reserve(pb_batch.data_size());
    
    for (const auto& pb_data : pb_batch.data()) {
        MarketDataWrapper wrapper;
        if (DeserializeMarketData(pb_data, &wrapper)) {
            batch->data.push_back(std::move(wrapper));
        } else {
            return false;
        }
    }
    
    return true;
}

// ZeroCopyBuffer implementation
ZeroCopyBuffer::ZeroCopyBuffer(size_t initial_capacity) 
    : capacity_(initial_capacity), size_(0) {
    buffer_ = std::make_unique<char[]>(capacity_);
}

bool ZeroCopyBuffer::EnsureCapacity(size_t required_size) {
    if (required_size <= capacity_) {
        return true;
    }
    
    // 扩展到2倍大小或所需大小，取较大者
    size_t new_capacity = std::max(capacity_ * 2, required_size);
    auto new_buffer = std::make_unique<char[]>(new_capacity);
    
    // 拷贝现有数据
    if (size_ > 0) {
        std::memcpy(new_buffer.get(), buffer_.get(), size_);
    }
    
    buffer_ = std::move(new_buffer);
    capacity_ = new_capacity;
    return true;
}

// FastSerializer implementation
bool FastSerializer::SerializeToBuffer(const MarketDataWrapper& data) {
    MarketData pb_data;
    if (!Serializer::SerializeMarketData(data, &pb_data)) {
        return false;
    }
    
    size_t required_size = pb_data.ByteSizeLong();
    if (!buffer_.EnsureCapacity(required_size)) {
        return false;
    }
    
    bool success = pb_data.SerializeToArray(buffer_.Data(), static_cast<int>(required_size));
    if (success) {
        buffer_.SetSize(required_size);
    }
    
    return success;
}

bool FastSerializer::SerializeToBuffer(const MarketDataBatch& batch) {
    MarketDataBatch pb_batch;
    if (!Serializer::SerializeBatch(batch, &pb_batch)) {
        return false;
    }
    
    size_t required_size = pb_batch.ByteSizeLong();
    if (!buffer_.EnsureCapacity(required_size)) {
        return false;
    }
    
    bool success = pb_batch.SerializeToArray(buffer_.Data(), static_cast<int>(required_size));
    if (success) {
        buffer_.SetSize(required_size);
    }
    
    return success;
}

bool FastSerializer::DeserializeFromBuffer(std::string_view data, MarketDataWrapper* wrapper) {
    if (!wrapper) return false;
    
    MarketData pb_data;
    if (!pb_data.ParseFromArray(data.data(), static_cast<int>(data.size()))) {
        return false;
    }
    
    return Serializer::DeserializeMarketData(pb_data, wrapper);
}

bool FastSerializer::DeserializeFromBuffer(std::string_view data, MarketDataBatch* batch) {
    if (!batch) return false;
    
    MarketDataBatch pb_batch;
    if (!pb_batch.ParseFromArray(data.data(), static_cast<int>(data.size()))) {
        return false;
    }
    
    return Serializer::DeserializeBatch(pb_batch, batch);
}

} // namespace financial_data