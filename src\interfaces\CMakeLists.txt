# WebSocket and gRPC interfaces library

# WebSocket interface sources
set(WEBSOCKET_SOURCES
    websocket_server.cpp
    websocket_handler.cpp
    subscription_manager.cpp
    message_compressor.cpp
    heartbeat_manager.cpp
)

set(WEBSOCKET_HEADERS
    websocket_server.h
    websocket_handler.h
    subscription_manager.h
    message_compressor.h
    heartbeat_manager.h
    websocket_types.h
)

# gRPC interface sources
set(GRPC_SOURCES
    grpc_server.cpp
)

set(GRPC_HEADERS
    grpc_server.h
)

# Create interfaces library
add_library(interfaces ${WEBSOCKET_SOURCES} ${WEBSOCKET_HEADERS} ${GRPC_SOURCES} ${GRPC_HEADERS})

# Find gRPC
find_package(gRPC CONFIG REQUIRED)
find_package(protobuf CONFIG REQUIRED)

# Link required libraries
target_link_libraries(interfaces
    proto
    databus
    Threads::Threads
    spdlog::spdlog
    nlohmann_json::nlohmann_json
    websocketpp::websocketpp
    asio::asio
    zlib::zlib
    gRPC::grpc++
    gRPC::grpc++_reflection
    protobuf::libprotobuf
)

# Include directories
target_include_directories(interfaces PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})