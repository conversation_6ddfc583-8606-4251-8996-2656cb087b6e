# 金融数据服务 - 任务调度器容器
# Financial Data Service - Task Scheduler Container

FROM python:3.11-slim

# 设置标签
LABEL maintainer="Financial Data Service Team"
LABEL description="Task Scheduler for Financial Data Service"
LABEL version="1.0.0"

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV PYTHONUNBUFFERED=1
ENV TZ=Asia/Shanghai

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    curl \
    procps \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r scheduler && useradd -r -g scheduler scheduler

# 设置工作目录
WORKDIR /app

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY config/ ./config/

# 创建必要的目录
RUN mkdir -p logs data && \
    chown -R scheduler:scheduler /app

# 切换到应用用户
USER scheduler

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python3 scripts/start_scheduler.py --status || exit 1

# 暴露端口（如果需要监控接口）
EXPOSE 8080

# 设置启动命令
CMD ["python3", "scripts/start_scheduler.py", "--config", "config/scheduler_config.json"]