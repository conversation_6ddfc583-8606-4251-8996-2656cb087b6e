@echo off
echo Setting up Financial Data Service development environment...

echo Checking Docker installation...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Docker is not installed. Please install Docker Desktop first.
    exit /b 1
)

echo Checking Docker Compose installation...
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Docker Compose is not installed. Please install Docker Compose first.
    exit /b 1
)

echo Starting development services...
docker-compose up -d

echo Waiting for services to start...
timeout /t 30 /nobreak >nul

echo Checking service health...
docker-compose ps

echo Setting up ClickHouse database...
docker exec financial-clickhouse clickhouse-client --query "CREATE DATABASE IF NOT EXISTS market_data"

echo Setting up Redis test data...
docker exec financial-redis redis-cli SET test:key "Financial Data Service Ready"

echo.
echo Development environment setup complete!
echo Services available at:
echo   - Redis: localhost:6379
echo   - ClickHouse: localhost:8123 (HTTP), localhost:9000 (TCP)
echo   - Kafka: localhost:9092
echo   - MinIO: localhost:9001 (Console), localhost:9002 (API)
echo   - Prometheus: localhost:9090
echo   - Grafana: localhost:3000 (admin/admin123)