#!/bin/bash

# 金融数据服务系统Web管理界面启动脚本
# Financial Data Service System Web Admin Startup Script

set -e

echo "=== 金融数据服务系统Web管理界面启动 ==="
echo "=== Financial Data Service Web Admin Startup ==="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    echo "Error: Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装，请先安装Docker Compose"
    echo "Error: Docker Compose is not installed"
    exit 1
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p ssl
mkdir -p logs

# 生成自签名SSL证书（仅用于开发环境）
if [ ! -f ssl/cert.pem ] || [ ! -f ssl/key.pem ]; then
    echo "生成SSL证书..."
    openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=Financial Data Service/CN=localhost"
fi

# 设置环境变量
export JWT_SECRET_KEY=${JWT_SECRET_KEY:-$(openssl rand -hex 32)}
export DB_PASSWORD=${DB_PASSWORD:-$(openssl rand -base64 32)}

echo "JWT_SECRET_KEY=$JWT_SECRET_KEY" > .env
echo "DB_PASSWORD=$DB_PASSWORD" >> .env

# 启动服务
echo "启动Docker服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "=== 服务启动完成 ==="
echo "=== Services Started Successfully ==="
echo ""
echo "Web管理界面访问地址:"
echo "HTTP:  http://localhost"
echo "HTTPS: https://localhost"
echo ""
echo "默认登录信息:"
echo "用户名: admin"
echo "密码:   admin123"
echo ""
echo "API文档地址:"
echo "http://localhost/api/docs"
echo ""
echo "查看日志:"
echo "docker-compose logs -f"
echo ""
echo "停止服务:"
echo "./stop.sh"
echo ""

# 显示重要安全提示
echo "=== 安全提示 ==="
echo "1. 请立即修改默认管理员密码"
echo "2. 在生产环境中使用有效的SSL证书"
echo "3. 配置防火墙规则限制访问"
echo "4. 定期备份数据库"
echo "5. 监控系统日志"