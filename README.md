# 金融数据服务系统 (Financial Data Service)

专业的量化投资高频交易行情数据服务平台，提供微秒级延迟、零数据丢失、全市场覆盖的实时和历史行情数据服务。

## 🚀 快速开始

### 环境要求

- **操作系统**: Windows 10+, Ubuntu 20.04+, macOS 10.15+
- **编译器**: C++17 支持 (MSVC 2019+, GCC 8+, Clang 7+)
- **构建工具**: CMake 3.15+
- **容器**: Docker Desktop 或 Docker + Docker Compose
- **内存**: 最少 8GB RAM
- **存储**: 最少 20GB 可用空间

### 开发环境设置

#### Windows

```cmd
# 设置开发环境
scripts\setup-dev.bat

# 构建项目
scripts\build.bat
```

#### Linux/macOS

```bash
# 设置开发环境
chmod +x scripts/setup-dev.sh
./scripts/setup-dev.sh

# 构建项目
mkdir build && cd build
cmake .. && make -j$(nproc)
```

## 📁 项目结构

```
financial-data-service/
├── src/                    # 源代码
│   ├── collectors/         # 数据采集模块
│   ├── storage/           # 存储模块
│   └── main.cpp           # 主程序入口
├── tests/                 # 测试代码
├── config/                # 配置文件
├── docs/                  # 文档
├── scripts/               # 构建和部署脚本
├── .github/workflows/     # CI/CD 流水线
├── docker-compose.yml     # 开发环境服务编排
├── Dockerfile            # 容器镜像构建
└── CMakeLists.txt        # 构建配置
```

## 🏗️ 系统架构

- **数据采集层**: CTP、股票、期权、外汇等多数据源支持
- **数据处理层**: 实时数据标准化、校验和路由
- **存储层**: Redis(热) + ClickHouse(温) + MinIO(冷)
- **接口层**: WebSocket、REST API、gRPC 多协议

## 📊 性能指标

- **延迟**: < 50微秒 (端到端)
- **吞吐量**: 100万条/秒
- **并发**: 1000个客户端
- **可用性**: 99.99%

## 🔧 开发服务

开发环境启动后可用的服务：

| 服务 | 地址 | 用途 |
|------|------|------|
| Redis | localhost:6379 | 实时数据缓存 |
| ClickHouse | localhost:8123/9000 | 历史数据存储 |
| Kafka | localhost:9092 | 消息队列 |
| MinIO | localhost:9001/9002 | 对象存储 |
| Prometheus | localhost:9090 | 监控指标 |
| Grafana | localhost:3000 | 监控面板 |

## 🧪 测试

```bash
# 运行所有测试
cd build && ctest

# 运行特定测试
./financial_data_tests --gtest_filter="CTPCollectorTest.*"
```

## 📚 文档

详细文档请参考 [docs/README.md](docs/README.md)

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如有问题或建议，请提交 [Issue](https://github.com/your-org/financial-data-service/issues)