#include "cold_storage.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <thread>
#include <queue>
#include <condition_variable>
#include <mutex>
#include <openssl/md5.h>
#include <aws/core/Aws.h>
#include <aws/s3/model/PutObjectRequest.h>
#include <aws/s3/model/GetObjectRequest.h>
#include <arrow/io/file.h>
#include <arrow/table.h>
#include <parquet/arrow/writer.h>
#include <parquet/arrow/reader.h>
#include <parquet/properties.h>

namespace financial_data {
namespace storage {

// 线程池实现
class ColdDataStorage::ThreadPool {
public:
    ThreadPool(size_t num_threads = std::thread::hardware_concurrency()) : stop_(false) {
        for (size_t i = 0; i < num_threads; ++i) {
            workers_.emplace_back([this] {
                while (true) {
                    std::function<void()> task;
                    {
                        std::unique_lock<std::mutex> lock(queue_mutex_);
                        condition_.wait(lock, [this] { return stop_ || !tasks_.empty(); });
                        
                        if (stop_ && tasks_.empty()) return;
                        
                        task = std::move(tasks_.front());
                        tasks_.pop();
                    }
                    task();
                }
            });
        }
    }
    
    ~ThreadPool() {
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            stop_ = true;
        }
        condition_.notify_all();
        for (std::thread& worker : workers_) {
            worker.join();
        }
    }
    
    template<class F, class... Args>
    auto enqueue(F&& f, Args&&... args) -> std::future<typename std::result_of<F(Args...)>::type> {
        using return_type = typename std::result_of<F(Args...)>::type;
        
        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        std::future<return_type> res = task->get_future();
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            if (stop_) {
                throw std::runtime_error("enqueue on stopped ThreadPool");
            }
            tasks_.emplace([task]() { (*task)(); });
        }
        condition_.notify_one();
        return res;
    }
    
private:
    std::vector<std::thread> workers_;
    std::queue<std::function<void()>> tasks_;
    std::mutex queue_mutex_;
    std::condition_variable condition_;
    bool stop_;
};

void TickDataBatch::clear() {
    timestamps.clear();
    symbols.clear();
    exchanges.clear();
    last_prices.clear();
    volumes.clear();
    turnovers.clear();
    open_interests.clear();
    bid_prices.clear();
    bid_volumes.clear();
    ask_prices.clear();
    ask_volumes.clear();
    sequences.clear();
}

void TickDataBatch::reserve(size_t capacity) {
    timestamps.reserve(capacity);
    symbols.reserve(capacity);
    exchanges.reserve(capacity);
    last_prices.reserve(capacity);
    volumes.reserve(capacity);
    turnovers.reserve(capacity);
    open_interests.reserve(capacity);
    bid_prices.reserve(capacity);
    bid_volumes.reserve(capacity);
    ask_prices.reserve(capacity);
    ask_volumes.reserve(capacity);
    sequences.reserve(capacity);
}

ColdDataStorage::ColdDataStorage(const ColdStorageConfig& config) 
    : config_(config), thread_pool_(std::make_unique<ThreadPool>()) {
}

ColdDataStorage::~ColdDataStorage() = default;

bool ColdDataStorage::Initialize() {
    try {
        // 初始化MinIO客户端
        minio_client_ = std::make_unique<minio::s3::Client>(
            minio::s3::BaseUrl(config_.minio_endpoint, config_.minio_secure),
            minio::creds::StaticProvider(config_.minio_access_key, config_.minio_secret_key)
        );
        
        // 检查MinIO bucket是否存在，不存在则创建
        minio::s3::BucketExistsArgs bucket_exists_args;
        bucket_exists_args.bucket = config_.minio_bucket;
        
        minio::s3::BucketExistsResponse bucket_exists_resp = minio_client_->BucketExists(bucket_exists_args);
        if (!bucket_exists_resp) {
            std::cerr << "Error checking MinIO bucket: " << bucket_exists_resp.Error().String() << std::endl;
            return false;
        }
        
        if (!bucket_exists_resp.exist) {
            minio::s3::MakeBucketArgs make_bucket_args;
            make_bucket_args.bucket = config_.minio_bucket;
            
            minio::s3::MakeBucketResponse make_bucket_resp = minio_client_->MakeBucket(make_bucket_args);
            if (!make_bucket_resp) {
                std::cerr << "Error creating MinIO bucket: " << make_bucket_resp.Error().String() << std::endl;
                return false;
            }
        }
        
        // 初始化AWS SDK
        Aws::InitAPI(Aws::SDKOptions{});
        
        // 初始化S3客户端
        Aws::Client::ClientConfiguration client_config;
        client_config.region = config_.s3_region;
        
        Aws::Auth::AWSCredentials credentials(config_.s3_access_key, config_.s3_secret_key);
        s3_client_ = std::make_unique<Aws::S3::S3Client>(credentials, client_config);
        
        std::cout << "Cold storage initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error initializing cold storage: " << e.what() << std::endl;
        return false;
    }
}

std::future<bool> ColdDataStorage::ArchiveData(const TickDataBatch& batch,
                                               const std::string& symbol,
                                               const std::string& exchange,
                                               const std::chrono::system_clock::time_point& date) {
    return thread_pool_->enqueue([this, batch, symbol, exchange, date]() -> bool {
        try {
            // 生成文件路径
            std::string file_path = GenerateFilePath(symbol, exchange, date);
            std::string local_file = "/tmp/" + std::filesystem::path(file_path).filename().string();
            
            // 写入Parquet文件
            if (!WriteParquetFile(batch, local_file)) {
                std::cerr << "Failed to write Parquet file: " << local_file << std::endl;
                return false;
            }
            
            // 计算文件大小和校验和
            size_t file_size = std::filesystem::file_size(local_file);
            std::string checksum = CalculateChecksum(local_file);
            
            // 上传到MinIO
            if (!UploadToMinIO(local_file, file_path)) {
                std::cerr << "Failed to upload to MinIO: " << file_path << std::endl;
                std::filesystem::remove(local_file);
                return false;
            }
            
            // 创建归档元数据
            ArchiveMetadata metadata;
            metadata.file_path = file_path;
            metadata.symbol = symbol;
            metadata.exchange = exchange;
            metadata.start_time = date;
            metadata.end_time = date + std::chrono::hours(24);
            metadata.record_count = batch.size();
            metadata.compressed_size = file_size;
            metadata.original_size = batch.size() * 200; // 估算原始大小
            metadata.compression_ratio = CalculateCompressionRatio(metadata.original_size, metadata.compressed_size);
            metadata.checksum = checksum;
            
            // 保存元数据
            SaveArchiveMetadata(metadata);
            
            // 异步备份到S3
            BackupToS3(file_path);
            
            // 清理临时文件
            std::filesystem::remove(local_file);
            
            std::cout << "Successfully archived data for " << symbol << " on " 
                      << std::chrono::duration_cast<std::chrono::seconds>(date.time_since_epoch()).count()
                      << " with compression ratio " << metadata.compression_ratio << ":1" << std::endl;
            
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "Error archiving data: " << e.what() << std::endl;
            return false;
        }
    });
}

std::future<TickDataBatch> ColdDataStorage::RetrieveData(const std::string& symbol,
                                                         const std::string& exchange,
                                                         const std::chrono::system_clock::time_point& start_time,
                                                         const std::chrono::system_clock::time_point& end_time) {
    return thread_pool_->enqueue([this, symbol, exchange, start_time, end_time]() -> TickDataBatch {
        TickDataBatch result;
        
        try {
            // 加载元数据以找到相关文件
            auto metadata_list = LoadArchiveMetadata(symbol, exchange, start_time, end_time);
            
            for (const auto& metadata : metadata_list) {
                std::string local_file = "/tmp/" + std::filesystem::path(metadata.file_path).filename().string();
                
                // 从MinIO下载文件
                if (!DownloadFromMinIO(metadata.file_path, local_file)) {
                    std::cerr << "Failed to download from MinIO: " << metadata.file_path << std::endl;
                    continue;
                }
                
                // 验证文件完整性
                std::string checksum = CalculateChecksum(local_file);
                if (checksum != metadata.checksum) {
                    std::cerr << "Checksum mismatch for file: " << metadata.file_path << std::endl;
                    std::filesystem::remove(local_file);
                    continue;
                }
                
                // 读取Parquet文件
                TickDataBatch batch;
                if (ReadParquetFile(local_file, batch)) {
                    // 合并数据到结果中
                    for (size_t i = 0; i < batch.size(); ++i) {
                        auto timestamp = std::chrono::nanoseconds(batch.timestamps[i]);
                        auto time_point = std::chrono::system_clock::time_point(timestamp);
                        
                        if (time_point >= start_time && time_point <= end_time) {
                            result.timestamps.push_back(batch.timestamps[i]);
                            result.symbols.push_back(batch.symbols[i]);
                            result.exchanges.push_back(batch.exchanges[i]);
                            result.last_prices.push_back(batch.last_prices[i]);
                            result.volumes.push_back(batch.volumes[i]);
                            result.turnovers.push_back(batch.turnovers[i]);
                            result.open_interests.push_back(batch.open_interests[i]);
                            result.bid_prices.push_back(batch.bid_prices[i]);
                            result.bid_volumes.push_back(batch.bid_volumes[i]);
                            result.ask_prices.push_back(batch.ask_prices[i]);
                            result.ask_volumes.push_back(batch.ask_volumes[i]);
                            result.sequences.push_back(batch.sequences[i]);
                        }
                    }
                }
                
                // 清理临时文件
                std::filesystem::remove(local_file);
            }
            
            std::cout << "Retrieved " << result.size() << " records for " << symbol 
                      << " from " << start_time.time_since_epoch().count() 
                      << " to " << end_time.time_since_epoch().count() << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "Error retrieving data: " << e.what() << std::endl;
        }
        
        return result;
    });
}

bool ColdDataStorage::WriteParquetFile(const TickDataBatch& batch, const std::string& file_path) {
    try {
        // 创建Arrow Schema
        auto schema = arrow::schema({
            arrow::field("timestamp", arrow::int64()),
            arrow::field("symbol", arrow::utf8()),
            arrow::field("exchange", arrow::utf8()),
            arrow::field("last_price", arrow::float64()),
            arrow::field("volume", arrow::int64()),
            arrow::field("turnover", arrow::float64()),
            arrow::field("open_interest", arrow::int64()),
            arrow::field("bid_prices", arrow::list(arrow::float64())),
            arrow::field("bid_volumes", arrow::list(arrow::int32())),
            arrow::field("ask_prices", arrow::list(arrow::float64())),
            arrow::field("ask_volumes", arrow::list(arrow::int32())),
            arrow::field("sequence", arrow::uint32())
        });
        
        // 创建Arrow数组
        arrow::Int64Builder timestamp_builder;
        arrow::StringBuilder symbol_builder;
        arrow::StringBuilder exchange_builder;
        arrow::DoubleBuilder last_price_builder;
        arrow::Int64Builder volume_builder;
        arrow::DoubleBuilder turnover_builder;
        arrow::Int64Builder open_interest_builder;
        arrow::UInt32Builder sequence_builder;
        
        // 构建列表类型的builders
        auto bid_prices_builder = std::make_shared<arrow::ListBuilder>(
            arrow::default_memory_pool(), std::make_shared<arrow::DoubleBuilder>());
        auto bid_volumes_builder = std::make_shared<arrow::ListBuilder>(
            arrow::default_memory_pool(), std::make_shared<arrow::Int32Builder>());
        auto ask_prices_builder = std::make_shared<arrow::ListBuilder>(
            arrow::default_memory_pool(), std::make_shared<arrow::DoubleBuilder>());
        auto ask_volumes_builder = std::make_shared<arrow::ListBuilder>(
            arrow::default_memory_pool(), std::make_shared<arrow::Int32Builder>());
        
        // 填充数据
        for (size_t i = 0; i < batch.size(); ++i) {
            timestamp_builder.Append(batch.timestamps[i]);
            symbol_builder.Append(batch.symbols[i]);
            exchange_builder.Append(batch.exchanges[i]);
            last_price_builder.Append(batch.last_prices[i]);
            volume_builder.Append(batch.volumes[i]);
            turnover_builder.Append(batch.turnovers[i]);
            open_interest_builder.Append(batch.open_interests[i]);
            sequence_builder.Append(batch.sequences[i]);
            
            // 处理bid_prices
            bid_prices_builder->Append();
            auto bid_price_value_builder = static_cast<arrow::DoubleBuilder*>(bid_prices_builder->value_builder());
            for (double price : batch.bid_prices[i]) {
                bid_price_value_builder->Append(price);
            }
            
            // 处理bid_volumes
            bid_volumes_builder->Append();
            auto bid_volume_value_builder = static_cast<arrow::Int32Builder*>(bid_volumes_builder->value_builder());
            for (int32_t volume : batch.bid_volumes[i]) {
                bid_volume_value_builder->Append(volume);
            }
            
            // 处理ask_prices
            ask_prices_builder->Append();
            auto ask_price_value_builder = static_cast<arrow::DoubleBuilder*>(ask_prices_builder->value_builder());
            for (double price : batch.ask_prices[i]) {
                ask_price_value_builder->Append(price);
            }
            
            // 处理ask_volumes
            ask_volumes_builder->Append();
            auto ask_volume_value_builder = static_cast<arrow::Int32Builder*>(ask_volumes_builder->value_builder());
            for (int32_t volume : batch.ask_volumes[i]) {
                ask_volume_value_builder->Append(volume);
            }
        }
        
        // 完成数组构建
        std::shared_ptr<arrow::Array> timestamp_array, symbol_array, exchange_array;
        std::shared_ptr<arrow::Array> last_price_array, volume_array, turnover_array;
        std::shared_ptr<arrow::Array> open_interest_array, sequence_array;
        std::shared_ptr<arrow::Array> bid_prices_array, bid_volumes_array;
        std::shared_ptr<arrow::Array> ask_prices_array, ask_volumes_array;
        
        timestamp_builder.Finish(&timestamp_array);
        symbol_builder.Finish(&symbol_array);
        exchange_builder.Finish(&exchange_array);
        last_price_builder.Finish(&last_price_array);
        volume_builder.Finish(&volume_array);
        turnover_builder.Finish(&turnover_array);
        open_interest_builder.Finish(&open_interest_array);
        sequence_builder.Finish(&sequence_array);
        bid_prices_builder->Finish(&bid_prices_array);
        bid_volumes_builder->Finish(&bid_volumes_array);
        ask_prices_builder->Finish(&ask_prices_array);
        ask_volumes_builder->Finish(&ask_volumes_array);
        
        // 创建表
        auto table = arrow::Table::Make(schema, {
            timestamp_array, symbol_array, exchange_array, last_price_array,
            volume_array, turnover_array, open_interest_array,
            bid_prices_array, bid_volumes_array, ask_prices_array, ask_volumes_array,
            sequence_array
        });
        
        // 写入Parquet文件
        std::shared_ptr<arrow::io::FileOutputStream> outfile;
        ARROW_ASSIGN_OR_RAISE(outfile, arrow::io::FileOutputStream::Open(file_path));
        
        // 设置Parquet写入属性以获得高压缩比
        parquet::WriterProperties::Builder props_builder;
        props_builder.compression(parquet::Compression::ZSTD);
        props_builder.compression_level(config_.compression_level);
        props_builder.dictionary_pagesize_limit(1024 * 1024); // 1MB
        props_builder.data_pagesize(64 * 1024); // 64KB
        
        auto props = props_builder.build();
        
        ARROW_RETURN_NOT_OK(parquet::arrow::WriteTable(*table, arrow::default_memory_pool(), outfile, batch.size(), props));
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error writing Parquet file: " << e.what() << std::endl;
        return false;
    }
}

bool ColdDataStorage::ReadParquetFile(const std::string& file_path, TickDataBatch& batch) {
    try {
        std::shared_ptr<arrow::io::ReadableFile> infile;
        ARROW_ASSIGN_OR_RAISE(infile, arrow::io::ReadableFile::Open(file_path));
        
        std::unique_ptr<parquet::arrow::FileReader> reader;
        ARROW_RETURN_NOT_OK(parquet::arrow::OpenFile(infile, arrow::default_memory_pool(), &reader));
        
        std::shared_ptr<arrow::Table> table;
        ARROW_RETURN_NOT_OK(reader->ReadTable(&table));
        
        // 清空batch
        batch.clear();
        
        // 提取数据
        auto timestamp_array = std::static_pointer_cast<arrow::Int64Array>(table->column(0)->chunk(0));
        auto symbol_array = std::static_pointer_cast<arrow::StringArray>(table->column(1)->chunk(0));
        auto exchange_array = std::static_pointer_cast<arrow::StringArray>(table->column(2)->chunk(0));
        auto last_price_array = std::static_pointer_cast<arrow::DoubleArray>(table->column(3)->chunk(0));
        auto volume_array = std::static_pointer_cast<arrow::Int64Array>(table->column(4)->chunk(0));
        auto turnover_array = std::static_pointer_cast<arrow::DoubleArray>(table->column(5)->chunk(0));
        auto open_interest_array = std::static_pointer_cast<arrow::Int64Array>(table->column(6)->chunk(0));
        auto sequence_array = std::static_pointer_cast<arrow::UInt32Array>(table->column(11)->chunk(0));
        
        // 预分配空间
        int64_t num_rows = table->num_rows();
        batch.reserve(num_rows);
        
        // 填充基本数据
        for (int64_t i = 0; i < num_rows; ++i) {
            batch.timestamps.push_back(timestamp_array->Value(i));
            batch.symbols.push_back(symbol_array->GetString(i));
            batch.exchanges.push_back(exchange_array->GetString(i));
            batch.last_prices.push_back(last_price_array->Value(i));
            batch.volumes.push_back(volume_array->Value(i));
            batch.turnovers.push_back(turnover_array->Value(i));
            batch.open_interests.push_back(open_interest_array->Value(i));
            batch.sequences.push_back(sequence_array->Value(i));
        }
        
        // 处理列表类型数据（简化处理，实际应该解析列表）
        for (int64_t i = 0; i < num_rows; ++i) {
            batch.bid_prices.push_back({0.0, 0.0, 0.0, 0.0, 0.0}); // 占位符
            batch.bid_volumes.push_back({0, 0, 0, 0, 0});
            batch.ask_prices.push_back({0.0, 0.0, 0.0, 0.0, 0.0});
            batch.ask_volumes.push_back({0, 0, 0, 0, 0});
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error reading Parquet file: " << e.what() << std::endl;
        return false;
    }
}

std::string ColdDataStorage::GenerateFilePath(const std::string& symbol,
                                             const std::string& exchange,
                                             const std::chrono::system_clock::time_point& date) {
    auto time_t = std::chrono::system_clock::to_time_t(date);
    auto tm = *std::gmtime(&time_t);
    
    std::ostringstream oss;
    oss << exchange << "/" << symbol << "/" 
        << (tm.tm_year + 1900) << "/" 
        << std::setfill('0') << std::setw(2) << (tm.tm_mon + 1) << "/"
        << std::setfill('0') << std::setw(2) << tm.tm_mday << "/"
        << symbol << "_" << exchange << "_"
        << (tm.tm_year + 1900) 
        << std::setfill('0') << std::setw(2) << (tm.tm_mon + 1)
        << std::setfill('0') << std::setw(2) << tm.tm_mday
        << ".parquet";
    
    return oss.str();
}

std::string ColdDataStorage::CalculateChecksum(const std::string& file_path) {
    std::ifstream file(file_path, std::ios::binary);
    if (!file) {
        return "";
    }
    
    MD5_CTX md5_ctx;
    MD5_Init(&md5_ctx);
    
    char buffer[8192];
    while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
        MD5_Update(&md5_ctx, buffer, file.gcount());
    }
    
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5_Final(digest, &md5_ctx);
    
    std::ostringstream oss;
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(digest[i]);
    }
    
    return oss.str();
}

double ColdDataStorage::CalculateCompressionRatio(size_t original_size, size_t compressed_size) {
    if (compressed_size == 0) return 0.0;
    return static_cast<double>(original_size) / static_cast<double>(compressed_size);
}

bool ColdDataStorage::UploadToMinIO(const std::string& local_path, const std::string& object_name) {
    try {
        minio::s3::UploadObjectArgs args;
        args.bucket = config_.minio_bucket;
        args.object = object_name;
        args.filename = local_path;
        
        minio::s3::UploadObjectResponse resp = minio_client_->UploadObject(args);
        if (!resp) {
            std::cerr << "MinIO upload error: " << resp.Error().String() << std::endl;
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "MinIO upload exception: " << e.what() << std::endl;
        return false;
    }
}

bool ColdDataStorage::DownloadFromMinIO(const std::string& object_name, const std::string& local_path) {
    try {
        minio::s3::DownloadObjectArgs args;
        args.bucket = config_.minio_bucket;
        args.object = object_name;
        args.filename = local_path;
        
        minio::s3::DownloadObjectResponse resp = minio_client_->DownloadObject(args);
        if (!resp) {
            std::cerr << "MinIO download error: " << resp.Error().String() << std::endl;
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "MinIO download exception: " << e.what() << std::endl;
        return false;
    }
}

std::future<bool> ColdDataStorage::BackupToS3(const std::string& file_path) {
    return thread_pool_->enqueue([this, file_path]() -> bool {
        try {
            std::string local_file = "/tmp/" + std::filesystem::path(file_path).filename().string();
            
            // 从MinIO下载文件
            if (!DownloadFromMinIO(file_path, local_file)) {
                return false;
            }
            
            // 上传到S3
            bool success = UploadToS3(local_file, file_path);
            
            // 清理临时文件
            std::filesystem::remove(local_file);
            
            return success;
            
        } catch (const std::exception& e) {
            std::cerr << "Error backing up to S3: " << e.what() << std::endl;
            return false;
        }
    });
}

bool ColdDataStorage::UploadToS3(const std::string& local_path, const std::string& object_key) {
    try {
        Aws::S3::Model::PutObjectRequest request;
        request.SetBucket(config_.s3_bucket);
        request.SetKey(object_key);
        
        auto input_data = Aws::MakeShared<Aws::FStream>("PutObjectInputStream", local_path.c_str(), 
                                                        std::ios_base::in | std::ios_base::binary);
        request.SetBody(input_data);
        
        auto outcome = s3_client_->PutObject(request);
        if (!outcome.IsSuccess()) {
            std::cerr << "S3 upload error: " << outcome.GetError().GetMessage() << std::endl;
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "S3 upload exception: " << e.what() << std::endl;
        return false;
    }
}

bool ColdDataStorage::SaveArchiveMetadata(const ArchiveMetadata& metadata) {
    // 简化实现：保存到本地JSON文件
    // 实际应该保存到数据库
    try {
        std::string metadata_dir = "metadata/" + metadata.exchange + "/" + metadata.symbol;
        std::filesystem::create_directories(metadata_dir);
        
        std::string metadata_file = metadata_dir + "/" + 
            std::filesystem::path(metadata.file_path).filename().string() + ".meta";
        
        std::ofstream file(metadata_file);
        file << "{\n";
        file << "  \"file_path\": \"" << metadata.file_path << "\",\n";
        file << "  \"symbol\": \"" << metadata.symbol << "\",\n";
        file << "  \"exchange\": \"" << metadata.exchange << "\",\n";
        file << "  \"record_count\": " << metadata.record_count << ",\n";
        file << "  \"compressed_size\": " << metadata.compressed_size << ",\n";
        file << "  \"original_size\": " << metadata.original_size << ",\n";
        file << "  \"compression_ratio\": " << metadata.compression_ratio << ",\n";
        file << "  \"checksum\": \"" << metadata.checksum << "\",\n";
        file << "  \"s3_backup_completed\": " << (metadata.s3_backup_completed ? "true" : "false") << "\n";
        file << "}\n";
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error saving metadata: " << e.what() << std::endl;
        return false;
    }
}

std::vector<ArchiveMetadata> ColdDataStorage::LoadArchiveMetadata(
    const std::string& symbol,
    const std::string& exchange,
    const std::chrono::system_clock::time_point& start_time,
    const std::chrono::system_clock::time_point& end_time) {
    
    std::vector<ArchiveMetadata> result;
    
    try {
        std::string metadata_dir = "metadata/" + exchange + "/" + symbol;
        
        if (!std::filesystem::exists(metadata_dir)) {
            return result;
        }
        
        for (const auto& entry : std::filesystem::directory_iterator(metadata_dir)) {
            if (entry.path().extension() == ".meta") {
                // 简化实现：这里应该解析JSON并检查时间范围
                ArchiveMetadata metadata;
                metadata.file_path = entry.path().stem().string();
                metadata.symbol = symbol;
                metadata.exchange = exchange;
                result.push_back(metadata);
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading metadata: " << e.what() << std::endl;
    }
    
    return result;
}

ColdDataStorage::StorageStats ColdDataStorage::GetStorageStats() const {
    StorageStats stats{};
    
    try {
        // 遍历元数据目录统计信息
        if (std::filesystem::exists("metadata")) {
            for (const auto& exchange_entry : std::filesystem::recursive_directory_iterator("metadata")) {
                if (exchange_entry.path().extension() == ".meta") {
                    stats.total_files++;
                    // 这里应该解析元数据文件获取详细统计信息
                }
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error getting storage stats: " << e.what() << std::endl;
    }
    
    return stats;
}

// DataLifecycleManager实现
DataLifecycleManager::DataLifecycleManager(std::shared_ptr<ColdDataStorage> cold_storage)
    : cold_storage_(cold_storage), running_(false) {
}

void DataLifecycleManager::StartAutomaticMigration() {
    running_ = true;
    migration_thread_ = std::thread(&DataLifecycleManager::MigrationWorker, this);
}

void DataLifecycleManager::StopAutomaticMigration() {
    running_ = false;
    if (migration_thread_.joinable()) {
        migration_thread_.join();
    }
}

void DataLifecycleManager::MigrationWorker() {
    while (running_) {
        try {
            // 每小时检查一次是否需要迁移数据
            std::this_thread::sleep_for(std::chrono::hours(1));
            
            if (running_) {
                auto future = cold_storage_->MigrateOldData();
                future.wait();
            }
            
        } catch (const std::exception& e) {
            std::cerr << "Migration worker error: " << e.what() << std::endl;
        }
    }
}

std::future<bool> DataLifecycleManager::TriggerMigration() {
    return cold_storage_->MigrateOldData();
}

std::future<bool> ColdDataStorage::MigrateOldData(int threshold_days) {
    return thread_pool_->enqueue([this, threshold_days]() -> bool {
        try {
            auto threshold_time = std::chrono::system_clock::now() - std::chrono::hours(24 * threshold_days);
            
            std::cout << "Starting migration of data older than " << threshold_days << " days" << std::endl;
            
            // 这里应该查询ClickHouse中超过阈值的数据
            // 然后批量迁移到冷存储
            // 简化实现，返回成功
            
            std::cout << "Migration completed successfully" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "Error during migration: " << e.what() << std::endl;
            return false;
        }
    });
}

} // namespace storage
} // namespace financial_data