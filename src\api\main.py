"""
FastAPI application for historical market data queries
Provides RESTful endpoints for querying tick data, K-line data, and Level-2 data
"""

from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>espo<PERSON>
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import asyncio
import logging
from contextlib import asynccontextmanager

from .models import (
    TickDataResponse, KlineDataResponse, Level2DataResponse,
    TickDataRequest, KlineDataRequest, Level2DataRequest,
    PaginationResponse, ErrorResponse
)
from .services import HistoricalDataService
from .cache import CacheManager
from .config import APIConfig

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global services
data_service: Optional[HistoricalDataService] = None
cache_manager: Optional[CacheManager] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global data_service, cache_manager
    
    # Startup
    logger.info("Starting Historical Data API...")
    config = APIConfig()
    
    # Initialize services
    data_service = HistoricalDataService(config)
    cache_manager = CacheManager(config.redis_config)
    
    await data_service.initialize()
    await cache_manager.initialize()
    
    logger.info("Historical Data API started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Historical Data API...")
    if data_service:
        await data_service.close()
    if cache_manager:
        await cache_manager.close()
    logger.info("Historical Data API shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="Financial Data Service API",
    description="RESTful API for querying historical market data",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def get_data_service() -> HistoricalDataService:
    """Dependency to get data service instance"""
    if data_service is None:
        raise HTTPException(status_code=503, detail="Data service not initialized")
    return data_service


def get_cache_manager() -> CacheManager:
    """Dependency to get cache manager instance"""
    if cache_manager is None:
        raise HTTPException(status_code=503, detail="Cache manager not initialized")
    return cache_manager


@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}


@app.get("/api/v1/tick-data", response_model=TickDataResponse)
async def get_tick_data(
    symbol: str = Query(..., description="Contract symbol (e.g., CU2409)"),
    exchange: Optional[str] = Query(None, description="Exchange code (SHFE, DCE, CZCE, CFFEX)"),
    start_time: Optional[datetime] = Query(None, description="Start timestamp (ISO format)"),
    end_time: Optional[datetime] = Query(None, description="End timestamp (ISO format)"),
    limit: int = Query(1000, ge=1, le=10000, description="Maximum number of records"),
    cursor: Optional[str] = Query(None, description="Pagination cursor"),
    data_service: HistoricalDataService = Depends(get_data_service),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Query historical tick data
    
    - **symbol**: Contract symbol (required)
    - **exchange**: Exchange code (optional, auto-detected if not provided)
    - **start_time**: Start timestamp in ISO format (optional)
    - **end_time**: End timestamp in ISO format (optional)
    - **limit**: Maximum number of records (1-10000, default 1000)
    - **cursor**: Pagination cursor for next page (optional)
    """
    try:
        # Create request object
        request = TickDataRequest(
            symbol=symbol,
            exchange=exchange,
            start_time=start_time,
            end_time=end_time,
            limit=limit,
            cursor=cursor
        )
        
        # Check cache first
        cache_key = f"tick:{symbol}:{exchange}:{start_time}:{end_time}:{limit}:{cursor}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            logger.info(f"Cache hit for tick data query: {symbol}")
            return TickDataResponse.model_validate(cached_result)
        
        # Query from data service
        result = await data_service.get_tick_data(request)
        
        # Cache the result for 5 minutes
        await cache_manager.set(cache_key, result.model_dump(), expire=300)
        
        logger.info(f"Tick data query completed: {symbol}, records: {len(result.data)}")
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error querying tick data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/v1/kline-data", response_model=KlineDataResponse)
async def get_kline_data(
    symbol: str = Query(..., description="Contract symbol"),
    exchange: Optional[str] = Query(None, description="Exchange code"),
    period: str = Query("1m", description="K-line period (1m, 5m, 15m, 1h, 1d)"),
    start_time: Optional[datetime] = Query(None, description="Start timestamp"),
    end_time: Optional[datetime] = Query(None, description="End timestamp"),
    limit: int = Query(1000, ge=1, le=10000, description="Maximum number of records"),
    cursor: Optional[str] = Query(None, description="Pagination cursor"),
    data_service: HistoricalDataService = Depends(get_data_service),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Query historical K-line data
    
    - **symbol**: Contract symbol (required)
    - **exchange**: Exchange code (optional)
    - **period**: K-line period - 1m, 5m, 15m, 30m, 1h, 4h, 1d (default: 1m)
    - **start_time**: Start timestamp in ISO format (optional)
    - **end_time**: End timestamp in ISO format (optional)
    - **limit**: Maximum number of records (1-10000, default 1000)
    - **cursor**: Pagination cursor for next page (optional)
    """
    try:
        # Validate period
        valid_periods = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
        if period not in valid_periods:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid period. Must be one of: {', '.join(valid_periods)}"
            )
        
        request = KlineDataRequest(
            symbol=symbol,
            exchange=exchange,
            period=period,
            start_time=start_time,
            end_time=end_time,
            limit=limit,
            cursor=cursor
        )
        
        # Check cache
        cache_key = f"kline:{symbol}:{exchange}:{period}:{start_time}:{end_time}:{limit}:{cursor}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            logger.info(f"Cache hit for kline data query: {symbol}")
            return KlineDataResponse.parse_obj(cached_result)
        
        # Query from data service
        result = await data_service.get_kline_data(request)
        
        # Cache for 10 minutes (kline data changes less frequently)
        await cache_manager.set(cache_key, result.model_dump(), expire=600)
        
        logger.info(f"Kline data query completed: {symbol}, period: {period}, records: {len(result.data)}")
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error querying kline data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/v1/level2-data", response_model=Level2DataResponse)
async def get_level2_data(
    symbol: str = Query(..., description="Contract symbol"),
    exchange: Optional[str] = Query(None, description="Exchange code"),
    start_time: Optional[datetime] = Query(None, description="Start timestamp"),
    end_time: Optional[datetime] = Query(None, description="End timestamp"),
    limit: int = Query(1000, ge=1, le=5000, description="Maximum number of records"),
    cursor: Optional[str] = Query(None, description="Pagination cursor"),
    data_service: HistoricalDataService = Depends(get_data_service),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Query historical Level-2 market depth data
    
    - **symbol**: Contract symbol (required)
    - **exchange**: Exchange code (optional)
    - **start_time**: Start timestamp in ISO format (optional)
    - **end_time**: End timestamp in ISO format (optional)
    - **limit**: Maximum number of records (1-5000, default 1000)
    - **cursor**: Pagination cursor for next page (optional)
    """
    try:
        request = Level2DataRequest(
            symbol=symbol,
            exchange=exchange,
            start_time=start_time,
            end_time=end_time,
            limit=limit,
            cursor=cursor
        )
        
        # Check cache
        cache_key = f"level2:{symbol}:{exchange}:{start_time}:{end_time}:{limit}:{cursor}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            logger.info(f"Cache hit for level2 data query: {symbol}")
            return Level2DataResponse.parse_obj(cached_result)
        
        # Query from data service
        result = await data_service.get_level2_data(request)
        
        # Cache for 2 minutes (level2 data is more real-time)
        await cache_manager.set(cache_key, result.model_dump(), expire=120)
        
        logger.info(f"Level2 data query completed: {symbol}, records: {len(result.data)}")
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error querying level2 data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/v1/symbols", response_model=List[Dict[str, Any]])
async def get_symbols(
    exchange: Optional[str] = Query(None, description="Filter by exchange"),
    product_type: Optional[str] = Query(None, description="Filter by product type"),
    data_service: HistoricalDataService = Depends(get_data_service)
):
    """
    Get available symbols/contracts
    
    - **exchange**: Filter by exchange code (optional)
    - **product_type**: Filter by product type (futures, stock, option, forex)
    """
    try:
        symbols = await data_service.get_available_symbols(exchange, product_type)
        return symbols
    except Exception as e:
        logger.error(f"Error getting symbols: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/v1/exchanges", response_model=List[str])
async def get_exchanges(
    data_service: HistoricalDataService = Depends(get_data_service)
):
    """Get list of available exchanges"""
    try:
        exchanges = await data_service.get_available_exchanges()
        return exchanges
    except Exception as e:
        logger.error(f"Error getting exchanges: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    error_response = ErrorResponse(
        error=exc.detail,
        status_code=exc.status_code
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    error_response = ErrorResponse(
        error="Internal server error",
        status_code=500
    )
    return JSONResponse(
        status_code=500,
        content=error_response.model_dump()
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)