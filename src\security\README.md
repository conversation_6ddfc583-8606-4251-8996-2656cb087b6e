# 数据安全和访问控制模块

本模块实现了金融数据服务系统的安全和访问控制功能，包括：

## 功能特性

1. **TLS 1.3加密传输** - 保护数据传输安全
2. **AES-256磁盘加密** - 保护存储数据安全
3. **JWT认证系统** - 支持多因素身份认证
4. **RBAC访问控制** - 基于角色的权限管理
5. **审计日志系统** - 完整的操作记录和查询

## 组件结构

- `tls_manager.h/cpp` - TLS 1.3加密传输管理
- `encryption_manager.h/cpp` - AES-256磁盘加密管理
- `jwt_auth.h/cpp` - JWT认证系统
- `rbac_manager.h/cpp` - 基于角色的访问控制
- `audit_logger.h/cpp` - 审计日志记录系统
- `security_config.h` - 安全配置定义

## 使用示例

参见 `examples/security_demo.cpp` 中的完整使用示例。