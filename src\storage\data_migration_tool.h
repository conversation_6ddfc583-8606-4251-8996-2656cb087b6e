#pragma once

#include "clickhouse_storage.h"
#include "redis_storage.h"
#include <memory>
#include <string>
#include <vector>
#include <chrono>
#include <future>
#include <atomic>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>

namespace financial_data {

/**
 * @brief Configuration for data migration from Redis to ClickHouse
 */
struct MigrationConfig {
    // Migration schedule
    std::chrono::seconds migration_interval{3600}; // 1 hour
    std::chrono::hours data_retention_hours{168}; // 7 days
    
    // Batch processing
    size_t batch_size = 10000;
    size_t max_concurrent_migrations = 4;
    
    // Retry settings
    int max_retries = 3;
    std::chrono::seconds retry_delay{30};
    
    // Data validation
    bool validate_migrated_data = true;
    double data_loss_threshold = 0.001; // 0.1% acceptable loss
    
    // Performance settings
    bool compress_during_migration = true;
    bool parallel_symbol_processing = true;
};

/**
 * @brief Statistics for migration operations
 */
struct MigrationStats {
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    
    size_t total_symbols_processed = 0;
    size_t total_records_migrated = 0;
    size_t total_records_failed = 0;
    size_t total_bytes_migrated = 0;
    
    std::chrono::milliseconds total_redis_read_time{0};
    std::chrono::milliseconds total_clickhouse_write_time{0};
    std::chrono::milliseconds total_validation_time{0};
    
    double compression_ratio = 0.0;
    double migration_rate_records_per_sec = 0.0;
    double migration_rate_mb_per_sec = 0.0;
    
    std::vector<std::string> failed_symbols;
    std::vector<std::string> error_messages;
};

/**
 * @brief Automated data migration tool for moving data from Redis hot storage to ClickHouse warm storage
 */
class DataMigrationTool {
public:
    DataMigrationTool(
        std::shared_ptr<RedisStorage> redis_storage,
        std::shared_ptr<ClickHouseStorage> clickhouse_storage,
        const MigrationConfig& config = MigrationConfig{}
    );
    
    ~DataMigrationTool();
    
    // Migration control
    bool StartScheduledMigration();
    void StopScheduledMigration();
    bool IsRunning() const;
    
    // Manual migration operations
    std::future<MigrationStats> MigrateSymbolData(
        const std::string& symbol,
        const std::string& exchange,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    std::future<MigrationStats> MigrateAllExpiredData();
    
    std::future<MigrationStats> MigrateDateRange(
        const std::vector<std::string>& symbols,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    // Status and monitoring
    MigrationStats GetCurrentStats() const;
    MigrationStats GetLastMigrationStats() const;
    std::vector<std::string> GetActiveMigrations() const;
    
    // Configuration
    void UpdateConfig(const MigrationConfig& config);
    MigrationConfig GetConfig() const;
    
    // Data validation
    bool ValidateMigratedData(
        const std::string& symbol,
        const std::string& exchange,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    // Cleanup operations
    bool CleanupRedisData(int64_t cutoff_timestamp);
    bool ArchiveOldClickHouseData(int64_t cutoff_timestamp);
    
private:
    // Core migration logic
    MigrationStats MigrateSymbolDataInternal(
        const std::string& symbol,
        const std::string& exchange,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    bool MigrateBatch(
        const std::vector<StandardizedTick>& batch,
        const std::string& symbol,
        const std::string& exchange
    );
    
    // Scheduled migration worker
    void ScheduledMigrationWorker();
    void ProcessMigrationQueue();
    
    // Data retrieval and conversion
    std::vector<StandardizedTick> FetchRedisData(
        const std::string& symbol,
        const std::string& exchange,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    StandardizedTick ConvertRedisToStandardTick(const RedisTickData& redis_tick);
    
    // Validation helpers
    bool ValidateDataIntegrity(
        const std::vector<StandardizedTick>& original_data,
        const std::vector<StandardizedTick>& migrated_data
    );
    
    size_t CountMissingSequences(const std::vector<StandardizedTick>& data);
    
    // Error handling and retry
    bool RetryOperation(std::function<bool()> operation, const std::string& operation_name);
    void HandleMigrationError(const std::string& error, const std::string& symbol);
    
    // Statistics and monitoring
    void UpdateStats(const MigrationStats& batch_stats);
    void ResetCurrentStats();
    
    // Configuration and dependencies
    std::shared_ptr<RedisStorage> redis_storage_;
    std::shared_ptr<ClickHouseStorage> clickhouse_storage_;
    MigrationConfig config_;
    
    // Threading and synchronization
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_requested_{false};
    std::thread migration_thread_;
    std::thread queue_processor_thread_;
    
    // Migration queue
    struct MigrationTask {
        std::string symbol;
        std::string exchange;
        int64_t start_timestamp;
        int64_t end_timestamp;
        std::promise<MigrationStats> promise;
    };
    
    std::queue<MigrationTask> migration_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // Statistics tracking
    mutable std::mutex stats_mutex_;
    MigrationStats current_stats_;
    MigrationStats last_completed_stats_;
    
    // Active migrations tracking
    mutable std::mutex active_migrations_mutex_;
    std::set<std::string> active_migrations_;
};

/**
 * @brief Utility class for data compression during migration
 */
class MigrationCompressor {
public:
    static std::vector<uint8_t> CompressTickData(const std::vector<StandardizedTick>& data);
    static std::vector<StandardizedTick> DecompressTickData(const std::vector<uint8_t>& compressed_data);
    static double CalculateCompressionRatio(size_t original_size, size_t compressed_size);
};

/**
 * @brief Data validation utilities for ensuring migration integrity
 */
class MigrationValidator {
public:
    struct ValidationResult {
        bool is_valid = true;
        size_t total_records = 0;
        size_t missing_records = 0;
        size_t corrupted_records = 0;
        std::vector<uint32_t> missing_sequences;
        std::vector<std::string> validation_errors;
        double data_loss_percentage = 0.0;
    };
    
    static ValidationResult ValidateTickDataMigration(
        const std::vector<StandardizedTick>& source_data,
        const std::vector<StandardizedTick>& target_data
    );
    
    static bool ValidateSequenceContinuity(const std::vector<StandardizedTick>& data);
    static bool ValidateTimestampOrdering(const std::vector<StandardizedTick>& data);
    static bool ValidatePriceData(const std::vector<StandardizedTick>& data);
    
private:
    static constexpr double MAX_PRICE_DEVIATION = 0.5; // 50% max price change between ticks
    static constexpr int64_t MAX_TIMESTAMP_GAP_NS = 60000000000LL; // 60 seconds max gap
};

} // namespace financial_data