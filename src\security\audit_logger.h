#pragma once

#include "security_config.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <memory>
#include <mutex>
#include <fstream>

namespace financial_data {
namespace security {

// 审计事件类型
enum class AuditEventType {
    LOGIN,
    LOGOUT,
    DATA_ACCESS,
    CONFIG_CHANGE,
    USER_MANAGEMENT,
    ROLE_MANAGEMENT,
    PERMISSION_CHECK,
    API_CALL,
    WEBSOCKET_CONNECTION,
    GRPC_CALL,
    ERROR_EVENT,
    SECURITY_VIOLATION
};

// 审计事件严重级别
enum class AuditLevel {
    INFO,
    WARNING,
    ERROR,
    CRITICAL
};

// 审计事件
struct AuditEvent {
    std::string event_id;
    AuditEventType event_type;
    AuditLevel level;
    std::string user_id;
    std::string session_id;
    std::string source_ip;
    std::string user_agent;
    std::string resource;
    std::string action;
    std::string description;
    std::unordered_map<std::string, std::string> details;
    std::chrono::system_clock::time_point timestamp;
    bool success;
    std::string error_message;
};

// 审计查询条件
struct AuditQuery {
    std::string user_id;
    AuditEventType event_type = static_cast<AuditEventType>(-1);
    AuditLevel min_level = AuditLevel::INFO;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    std::string source_ip;
    std::string resource;
    std::string action;
    int limit = 1000;
    int offset = 0;
};

class AuditLogger {
public:
    explicit AuditLogger(const AuditConfig& config);
    ~AuditLogger();

    // 初始化审计日志系统
    bool Initialize();
    
    // 记录审计事件
    void LogEvent(const AuditEvent& event);
    
    // 便捷方法记录不同类型的事件
    void LogLogin(const std::string& user_id, const std::string& source_ip, 
                  bool success, const std::string& error_message = "");
    void LogLogout(const std::string& user_id, const std::string& session_id);
    void LogDataAccess(const std::string& user_id, const std::string& resource,
                      const std::string& action, bool success);
    void LogConfigChange(const std::string& user_id, const std::string& config_item,
                        const std::string& old_value, const std::string& new_value);
    void LogUserManagement(const std::string& admin_user_id, const std::string& target_user_id,
                          const std::string& action, bool success);
    void LogPermissionCheck(const std::string& user_id, const std::string& permission,
                           const std::string& resource, bool granted);
    void LogAPICall(const std::string& user_id, const std::string& endpoint,
                   const std::string& method, int status_code);
    void LogSecurityViolation(const std::string& user_id, const std::string& violation_type,
                             const std::string& description, const std::string& source_ip);
    
    // 查询审计日志
    std::vector<AuditEvent> QueryEvents(const AuditQuery& query);
    
    // 统计信息
    struct AuditStatistics {
        int total_events;
        int login_attempts;
        int failed_logins;
        int data_access_events;
        int security_violations;
        std::unordered_map<std::string, int> events_by_user;
        std::unordered_map<std::string, int> events_by_ip;
        std::unordered_map<AuditEventType, int> events_by_type;
    };
    
    AuditStatistics GetStatistics(std::chrono::system_clock::time_point start_time,
                                 std::chrono::system_clock::time_point end_time);
    
    // 实时告警
    void SetAlertCallback(std::function<void(const AuditEvent&)> callback);
    
    // 日志轮转
    void RotateLog();
    
    // 清理过期日志
    void CleanupOldLogs(std::chrono::hours retention_period);

private:
    AuditConfig config_;
    std::vector<AuditEvent> events_;
    std::mutex events_mutex_;
    std::ofstream log_file_;
    std::function<void(const AuditEvent&)> alert_callback_;
    bool initialized_;
    
    // 生成事件ID
    std::string GenerateEventId();
    
    // 写入日志文件
    void WriteToFile(const AuditEvent& event);
    
    // 检查是否需要告警
    bool ShouldAlert(const AuditEvent& event);
    
    // 触发实时告警
    void TriggerAlert(const AuditEvent& event);
    
    // 日志格式化
    std::string FormatEvent(const AuditEvent& event);
    
    // 事件类型转换
    std::string EventTypeToString(AuditEventType type);
    AuditEventType StringToEventType(const std::string& type_str);
    std::string LevelToString(AuditLevel level);
    AuditLevel StringToLevel(const std::string& level_str);
    
    // 内存管理
    void ManageMemoryUsage();
    
    // 文件管理
    bool OpenLogFile();
    void CloseLogFile();
    std::string GetLogFileName();
    std::string GetRotatedLogFileName(int index);
};

} // namespace security
} // namespace financial_data