#include "data_integrity_checker.h"
#include "alert_manager.h"
#include "prometheus_metrics.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <iomanip>

namespace monitoring {

DataIntegrityChecker::DataIntegrityChecker(std::shared_ptr<AlertManager> alert_manager)
    : alert_manager_(alert_manager) {
}

DataIntegrityChecker::~DataIntegrityChecker() {
    stop();
}

bool DataIntegrityChecker::start() {
    if (running_.load()) {
        return true;
    }
    
    running_.store(true);
    processing_thread_ = std::thread(&DataIntegrityChecker::processingLoop, this);
    timeout_check_thread_ = std::thread(&DataIntegrityChecker::timeoutCheck<PERSON>oop, this);
    
    std::cout << "Data integrity checker started with timeout: " 
              << missing_data_timeout_.count() << " seconds" << std::endl;
    return true;
}

void DataIntegrityChecker::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    queue_cv_.notify_all();
    
    if (processing_thread_.joinable()) {
        processing_thread_.join();
    }
    if (timeout_check_thread_.joinable()) {
        timeout_check_thread_.join();
    }
    
    std::cout << "Data integrity checker stopped" << std::endl;
}

void DataIntegrityChecker::recordMessage(const DataMessage& message) {
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        message_queue_.push(message);
    }
    queue_cv_.notify_one();
}

void DataIntegrityChecker::processingLoop() {
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        queue_cv_.wait(lock, [this] { 
            return !message_queue_.empty() || !running_.load(); 
        });
        
        while (!message_queue_.empty() && running_.load()) {
            DataMessage message = message_queue_.front();
            message_queue_.pop();
            lock.unlock();
            
            processMessage(message);
            
            lock.lock();
        }
    }
}

void DataIntegrityChecker::timeoutCheckLoop() {
    while (running_.load()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        if (running_.load()) {
            checkForTimeouts();
        }
    }
}

void DataIntegrityChecker::processMessage(const DataMessage& message) {
    total_messages_received_.fetch_add(1);
    
    // Update Prometheus metrics
    auto& metrics = PrometheusMetrics::getInstance();
    metrics.incrementDataReceived(message.symbol);
    
    // Check sequence integrity
    checkSequenceIntegrity(message.symbol, message);
}

void DataIntegrityChecker::checkSequenceIntegrity(const std::string& symbol, const DataMessage& message) {
    std::lock_guard<std::mutex> lock(trackers_mutex_);
    
    auto& tracker = sequence_trackers_[symbol];
    tracker.last_update = message.timestamp;
    tracker.total_received++;
    
    if (!tracker.initialized) {
        tracker.last_sequence = message.sequence_number;
        tracker.initialized = true;
        return;
    }
    
    uint64_t expected_sequence = tracker.last_sequence + 1;
    
    if (message.sequence_number == expected_sequence) {
        // Sequence is correct, remove from missing if it was there
        tracker.missing_sequences.erase(message.sequence_number);
        tracker.last_sequence = message.sequence_number;
    } else if (message.sequence_number > expected_sequence) {
        // Gap detected - sequences are missing
        uint64_t gap_size = message.sequence_number - expected_sequence;
        
        if (gap_size <= max_sequence_gap_) {
            // Add missing sequences to tracker
            detectMissingSequences(symbol, message.sequence_number, tracker.last_sequence);
        } else {
            // Large gap - send immediate alert
            sendSequenceGapAlert(symbol, gap_size, expected_sequence, message.sequence_number);
        }
        
        tracker.last_sequence = message.sequence_number;
        total_sequence_gaps_.fetch_add(1);
    } else {
        // Out-of-order or duplicate message
        if (tracker.missing_sequences.count(message.sequence_number)) {
            // This was a missing sequence that arrived late
            tracker.missing_sequences.erase(message.sequence_number);
        }
        // Don't update last_sequence for out-of-order messages
    }
}

void DataIntegrityChecker::detectMissingSequences(const std::string& symbol, uint64_t current_sequence, uint64_t last_sequence) {
    std::vector<uint64_t> missing_sequences;
    
    for (uint64_t seq = last_sequence + 1; seq < current_sequence; ++seq) {
        missing_sequences.push_back(seq);
        sequence_trackers_[symbol].missing_sequences.insert(seq);
    }
    
    if (!missing_sequences.empty()) {
        sequence_trackers_[symbol].total_lost += missing_sequences.size();
        total_messages_lost_.fetch_add(missing_sequences.size());
        
        // Update Prometheus metrics
        updatePrometheusMetrics(symbol, missing_sequences.size());
        
        // Send alert if within timeout window
        sendDataLossAlert(symbol, missing_sequences);
    }
}

void DataIntegrityChecker::checkForTimeouts() {
    auto now = std::chrono::high_resolution_clock::now();
    
    std::lock_guard<std::mutex> lock(trackers_mutex_);
    
    for (auto& [symbol, tracker] : sequence_trackers_) {
        if (!tracker.initialized) continue;
        
        auto time_since_last_update = now - tracker.last_update;
        if (time_since_last_update > missing_data_timeout_) {
            auto timeout_duration = std::chrono::duration_cast<std::chrono::seconds>(time_since_last_update);
            sendTimeoutAlert(symbol, timeout_duration);
        }
        
        // Check for missing sequences that have timed out
        if (!tracker.missing_sequences.empty()) {
            std::vector<uint64_t> timed_out_sequences;
            for (uint64_t seq : tracker.missing_sequences) {
                // Assume missing sequences are from the time of last update
                if (time_since_last_update > missing_data_timeout_) {
                    timed_out_sequences.push_back(seq);
                }
            }
            
            if (!timed_out_sequences.empty()) {
                sendDataLossAlert(symbol, timed_out_sequences);
                // Remove timed out sequences from tracking
                for (uint64_t seq : timed_out_sequences) {
                    tracker.missing_sequences.erase(seq);
                }
            }
        }
    }
}

void DataIntegrityChecker::sendDataLossAlert(const std::string& symbol, const std::vector<uint64_t>& missing_sequences) {
    if (!shouldSendAlert(symbol)) {
        return;
    }
    
    std::ostringstream message;
    message << "Data loss detected!\n";
    message << "Symbol: " << symbol << "\n";
    message << "Missing sequences: " << formatMissingSequences(missing_sequences) << "\n";
    message << "Count: " << missing_sequences.size() << " messages\n";
    message << "Detection time: " << std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count() << "s";
    
    if (alert_manager_) {
        alert_manager_->sendAlert(
            "data_loss",
            "CRITICAL",
            message.str(),
            {
                {"symbol", symbol},
                {"missing_count", std::to_string(missing_sequences.size())},
                {"missing_sequences", formatMissingSequences(missing_sequences)}
            }
        );
    }
    
    // Record alert metric
    auto& metrics = PrometheusMetrics::getInstance();
    metrics.incrementAlert("data_loss", "critical");
    
    std::cout << "ALERT: " << message.str() << std::endl;
}

void DataIntegrityChecker::sendSequenceGapAlert(const std::string& symbol, uint64_t gap_size, uint64_t expected, uint64_t received) {
    if (!shouldSendAlert(symbol)) {
        return;
    }
    
    std::ostringstream message;
    message << "Large sequence gap detected!\n";
    message << "Symbol: " << symbol << "\n";
    message << "Gap size: " << gap_size << " sequences\n";
    message << "Expected: " << expected << "\n";
    message << "Received: " << received << "\n";
    message << "This may indicate a significant data loss event.";
    
    if (alert_manager_) {
        alert_manager_->sendAlert(
            "sequence_gap",
            "CRITICAL",
            message.str(),
            {
                {"symbol", symbol},
                {"gap_size", std::to_string(gap_size)},
                {"expected_sequence", std::to_string(expected)},
                {"received_sequence", std::to_string(received)}
            }
        );
    }
    
    // Record alert metric
    auto& metrics = PrometheusMetrics::getInstance();
    metrics.incrementAlert("sequence_gap", "critical");
    
    std::cout << "ALERT: " << message.str() << std::endl;
}

void DataIntegrityChecker::sendTimeoutAlert(const std::string& symbol, std::chrono::seconds timeout_duration) {
    if (!shouldSendAlert(symbol)) {
        return;
    }
    
    std::ostringstream message;
    message << "Data timeout detected!\n";
    message << "Symbol: " << symbol << "\n";
    message << "No data received for: " << timeout_duration.count() << " seconds\n";
    message << "Timeout threshold: " << missing_data_timeout_.count() << " seconds\n";
    message << "This may indicate a connection or data source issue.";
    
    if (alert_manager_) {
        alert_manager_->sendAlert(
            "data_timeout",
            "WARNING",
            message.str(),
            {
                {"symbol", symbol},
                {"timeout_duration", std::to_string(timeout_duration.count())},
                {"timeout_threshold", std::to_string(missing_data_timeout_.count())}
            }
        );
    }
    
    // Record alert metric
    auto& metrics = PrometheusMetrics::getInstance();
    metrics.incrementAlert("data_timeout", "warning");
    
    std::cout << "ALERT: " << message.str() << std::endl;
}

bool DataIntegrityChecker::shouldSendAlert(const std::string& symbol) {
    std::lock_guard<std::mutex> lock(alert_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    auto it = last_alert_times_.find(symbol);
    
    if (it == last_alert_times_.end() || (now - it->second) >= alert_cooldown_) {
        last_alert_times_[symbol] = now;
        return true;
    }
    
    return false;
}

std::string DataIntegrityChecker::formatMissingSequences(const std::vector<uint64_t>& sequences) {
    if (sequences.empty()) {
        return "none";
    }
    
    std::ostringstream oss;
    if (sequences.size() <= 10) {
        // Show all sequences if there are few
        for (size_t i = 0; i < sequences.size(); ++i) {
            if (i > 0) oss << ", ";
            oss << sequences[i];
        }
    } else {
        // Show first few and last few with ellipsis
        for (size_t i = 0; i < 5; ++i) {
            if (i > 0) oss << ", ";
            oss << sequences[i];
        }
        oss << " ... ";
        for (size_t i = sequences.size() - 3; i < sequences.size(); ++i) {
            oss << sequences[i];
            if (i < sequences.size() - 1) oss << ", ";
        }
    }
    
    return oss.str();
}

void DataIntegrityChecker::updatePrometheusMetrics(const std::string& symbol, uint64_t lost_count) {
    auto& metrics = PrometheusMetrics::getInstance();
    for (uint64_t i = 0; i < lost_count; ++i) {
        metrics.incrementDataLoss(symbol);
    }
    metrics.recordSequenceGap(symbol, static_cast<int>(lost_count));
}

DataIntegrityChecker::IntegrityStats DataIntegrityChecker::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    std::lock_guard<std::mutex> trackers_lock(trackers_mutex_);
    
    IntegrityStats stats;
    stats.total_messages_received = total_messages_received_.load();
    stats.total_messages_lost = total_messages_lost_.load();
    stats.total_sequence_gaps = total_sequence_gaps_.load();
    
    if (stats.total_messages_received > 0) {
        stats.data_loss_rate = static_cast<double>(stats.total_messages_lost) / 
                              static_cast<double>(stats.total_messages_received + stats.total_messages_lost);
    } else {
        stats.data_loss_rate = 0.0;
    }
    
    for (const auto& [symbol, tracker] : sequence_trackers_) {
        stats.symbol_loss_count[symbol] = tracker.total_lost;
    }
    
    return stats;
}

void DataIntegrityChecker::resetStatistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    std::lock_guard<std::mutex> trackers_lock(trackers_mutex_);
    
    total_messages_received_.store(0);
    total_messages_lost_.store(0);
    total_sequence_gaps_.store(0);
    
    for (auto& [symbol, tracker] : sequence_trackers_) {
        tracker.total_received = 0;
        tracker.total_lost = 0;
    }
}

} // namespace monitoring