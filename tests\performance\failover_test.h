/**
 * @file failover_test.h
 * @brief Failover and disaster recovery testing
 */

#pragma once

#include "benchmark_runner.h"
#include <memory>

namespace performance_tests {

class TestUtils;

/**
 * @class FailoverTest
 * @brief Test system failover and recovery capabilities
 * 
 * Tests the system's ability to handle failures and recover within 5 seconds
 */
class FailoverTest {
public:
    FailoverTest();
    ~FailoverTest();
    
    /**
     * @brief Test primary server failover
     * @return FailoverResult with failover timing and success metrics
     * 
     * Requirement: Failover within 5 seconds (Req 5.1)
     */
    FailoverResult TestPrimaryServerFailover();
    
    /**
     * @brief Test database failover
     * @return FailoverResult with database failover metrics
     */
    FailoverResult TestDatabaseFailover();
    
    /**
     * @brief Test network failover
     * @return FailoverResult with network failover metrics
     */
    FailoverResult TestNetworkFailover();
    
    /**
     * @brief Test data recovery after failover
     * @return FailoverResult with data recovery metrics
     * 
     * Requirement: Complete data backup and recovery (Req 5.3)
     */
    FailoverResult TestDataRecoveryAfterFailover();

private:
    std::unique_ptr<TestUtils> test_utils_;
};

} // namespace performance_tests