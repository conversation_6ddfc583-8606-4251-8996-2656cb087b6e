#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <chrono>
#include <thread>

#include "../src/storage/storage_layer_selector.h"

using namespace financial_data;
using namespace testing;

class StorageLayerSelectorTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.hot_storage_days = 7;
        config_.warm_storage_days = 730;
        config_.enable_automatic_failover = true;
        config_.max_consecutive_failures = 3;
        config_.health_check_interval = std::chrono::seconds(1);
        
        selector_ = std::make_unique<StorageLayerSelector>(config_);
        ASSERT_TRUE(selector_->Initialize());
    }
    
    void TearDown() override {
        selector_->Shutdown();
    }
    
    int64_t GetCurrentTimestampNs() {
        auto now = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::nanoseconds>(
            now.time_since_epoch()).count();
    }
    
    int64_t DaysToNanoseconds(int days) {
        return static_cast<int64_t>(days) * 24 * 3600 * 1000000000LL;
    }

protected:
    StorageSelectionConfig config_;
    std::unique_ptr<StorageLayerSelector> selector_;
};

// 测试基于时间的存储层选择
TEST_F(StorageLayerSelectorTest, TimeBasedSelectionTest) {
    int64_t current_time = GetCurrentTimestampNs();
    
    // 测试热存储范围（最近7天）
    int64_t hot_timestamp = current_time - DaysToNanoseconds(3);
    EXPECT_EQ(selector_->SelectStorageLayer(hot_timestamp), StorageLayer::HOT);
    
    // 测试温存储范围（7天到730天）
    int64_t warm_timestamp = current_time - DaysToNanoseconds(30);
    EXPECT_EQ(selector_->SelectStorageLayer(warm_timestamp), StorageLayer::WARM);
    
    // 测试冷存储范围（超过730天）
    int64_t cold_timestamp = current_time - DaysToNanoseconds(800);
    EXPECT_EQ(selector_->SelectStorageLayer(cold_timestamp), StorageLayer::COLD);
}

// 测试跨层时间范围选择
TEST_F(StorageLayerSelectorTest, MultiLayerSelectionTest) {
    int64_t current_time = GetCurrentTimestampNs();
    
    // 测试跨热存储和温存储的时间范围
    int64_t start_time = current_time - DaysToNanoseconds(30); // 温存储
    int64_t end_time = current_time - DaysToNanoseconds(1);    // 热存储
    
    auto layers = selector_->SelectStorageLayers(start_time, end_time);
    
    EXPECT_GE(layers.size(), 2);
    EXPECT_TRUE(std::find(layers.begin(), layers.end(), StorageLayer::HOT) != layers.end());
    EXPECT_TRUE(std::find(layers.begin(), layers.end(), StorageLayer::WARM) != layers.end());
}

// 测试存储层健康状态更新
TEST_F(StorageLayerSelectorTest, HealthStatusUpdateTest) {
    // 初始状态应该是未知
    auto initial_status = selector_->GetLayerStatus(StorageLayer::HOT);
    EXPECT_EQ(initial_status.health, StorageLayerHealth::UNKNOWN);
    
    // 报告成功的查询
    selector_->UpdateLayerMetrics(StorageLayer::HOT, true, 50.0);
    
    auto updated_status = selector_->GetLayerStatus(StorageLayer::HOT);
    EXPECT_GT(updated_status.metrics.total_requests.load(), 0);
    EXPECT_GT(updated_status.metrics.successful_requests.load(), 0);
    EXPECT_EQ(updated_status.metrics.avg_response_time_ms.load(), 50.0);
    EXPECT_EQ(updated_status.metrics.success_rate.load(), 1.0);
}

// 测试故障报告和恢复
TEST_F(StorageLayerSelectorTest, FailureReportingTest) {
    // 报告多次失败
    for (int i = 0; i < config_.max_consecutive_failures; ++i) {
        selector_->ReportLayerFailure(StorageLayer::HOT, "Connection failed");
    }
    
    // 检查存储层是否进入故障转移模式
    EXPECT_TRUE(selector_->IsLayerInFailover(StorageLayer::HOT));
    
    auto status = selector_->GetLayerStatus(StorageLayer::HOT);
    EXPECT_EQ(status.health, StorageLayerHealth::UNHEALTHY);
    EXPECT_FALSE(status.error_message.empty());
    
    // 报告恢复
    selector_->ReportLayerRecovery(StorageLayer::HOT);
    
    auto recovered_status = selector_->GetLayerStatus(StorageLayer::HOT);
    EXPECT_EQ(recovered_status.health, StorageLayerHealth::HEALTHY);
    EXPECT_TRUE(recovered_status.error_message.empty());
    EXPECT_FALSE(selector_->IsLayerInFailover(StorageLayer::HOT));
}

// 测试故障转移选择
TEST_F(StorageLayerSelectorTest, FailoverSelectionTest) {
    int64_t current_time = GetCurrentTimestampNs();
    int64_t hot_timestamp = current_time - DaysToNanoseconds(1);
    
    // 正常情况下应该选择热存储
    EXPECT_EQ(selector_->SelectStorageLayer(hot_timestamp), StorageLayer::HOT);
    
    // 报告热存储故障
    for (int i = 0; i < config_.max_consecutive_failures; ++i) {
        selector_->ReportLayerFailure(StorageLayer::HOT, "Storage unavailable");
    }
    
    // 现在应该选择故障转移目标
    StorageLayer failover_layer = selector_->SelectWithFailover(hot_timestamp, {StorageLayer::HOT});
    EXPECT_NE(failover_layer, StorageLayer::HOT);
    EXPECT_TRUE(failover_layer == StorageLayer::WARM || failover_layer == StorageLayer::COLD);
}

// 测试性能基于的选择策略
TEST_F(StorageLayerSelectorTest, PerformanceBasedSelectionTest) {
    // 配置性能基于的选择策略
    StorageSelectionConfig perf_config = config_;
    perf_config.strategy = StorageSelectionStrategy::PERFORMANCE_BASED;
    
    auto perf_selector = std::make_unique<StorageLayerSelector>(perf_config);
    ASSERT_TRUE(perf_selector->Initialize());
    
    int64_t current_time = GetCurrentTimestampNs();
    int64_t hot_timestamp = current_time - DaysToNanoseconds(1);
    
    // 模拟热存储性能较差
    perf_selector->UpdateLayerMetrics(StorageLayer::HOT, true, 1500.0); // 高延迟
    perf_selector->UpdateLayerMetrics(StorageLayer::WARM, true, 100.0); // 低延迟
    
    // 等待一段时间让健康状态更新
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 性能基于的选择可能会选择温存储而不是热存储
    StorageLayer selected = perf_selector->SelectStorageLayer(hot_timestamp);
    
    perf_selector->Shutdown();
}

// 测试负载均衡选择策略
TEST_F(StorageLayerSelectorTest, LoadBalancedSelectionTest) {
    // 配置负载均衡选择策略
    StorageSelectionConfig lb_config = config_;
    lb_config.strategy = StorageSelectionStrategy::LOAD_BALANCED;
    lb_config.enable_load_balancing = true;
    lb_config.load_balance_threshold = 0.5; // 50%负载阈值
    
    auto lb_selector = std::make_unique<StorageLayerSelector>(lb_config);
    ASSERT_TRUE(lb_selector->Initialize());
    
    int64_t current_time = GetCurrentTimestampNs();
    int64_t hot_timestamp = current_time - DaysToNanoseconds(1);
    
    // 模拟热存储高负载（低成功率）
    for (int i = 0; i < 10; ++i) {
        lb_selector->UpdateLayerMetrics(StorageLayer::HOT, i < 4, 100.0); // 40%成功率
    }
    
    // 模拟温存储低负载（高成功率）
    for (int i = 0; i < 10; ++i) {
        lb_selector->UpdateLayerMetrics(StorageLayer::WARM, i < 9, 150.0); // 90%成功率
    }
    
    // 等待健康状态更新
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 负载均衡可能会选择温存储
    StorageLayer selected = lb_selector->SelectStorageLayer(hot_timestamp);
    
    lb_selector->Shutdown();
}

// 测试最优存储层选择
TEST_F(StorageLayerSelectorTest, OptimalLayerSelectionTest) {
    int64_t current_time = GetCurrentTimestampNs();
    int64_t hot_timestamp = current_time - DaysToNanoseconds(1);
    
    // 设置不同存储层的性能指标
    selector_->UpdateLayerMetrics(StorageLayer::HOT, true, 200.0);   // 中等性能
    selector_->UpdateLayerMetrics(StorageLayer::WARM, true, 50.0);   // 高性能
    selector_->UpdateLayerMetrics(StorageLayer::COLD, true, 1000.0); // 低性能
    
    // 等待健康状态更新
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 选择最优存储层
    StorageLayer optimal = selector_->SelectOptimalLayer(hot_timestamp);
    
    // 应该选择性能最好的存储层
    auto optimal_status = selector_->GetLayerStatus(optimal);
    EXPECT_TRUE(optimal_status.IsAvailable());
}

// 测试手动启用/禁用存储层
TEST_F(StorageLayerSelectorTest, ManualLayerControlTest) {
    // 手动禁用热存储
    selector_->DisableLayer(StorageLayer::HOT, "Maintenance");
    
    auto disabled_status = selector_->GetLayerStatus(StorageLayer::HOT);
    EXPECT_EQ(disabled_status.health, StorageLayerHealth::UNHEALTHY);
    EXPECT_FALSE(disabled_status.error_message.empty());
    
    // 手动启用热存储
    selector_->EnableLayer(StorageLayer::HOT);
    
    auto enabled_status = selector_->GetLayerStatus(StorageLayer::HOT);
    EXPECT_EQ(enabled_status.health, StorageLayerHealth::HEALTHY);
    EXPECT_TRUE(enabled_status.error_message.empty());
}

// 测试统计信息
TEST_F(StorageLayerSelectorTest, StatisticsTest) {
    int64_t current_time = GetCurrentTimestampNs();
    
    // 执行多次选择
    selector_->SelectStorageLayer(current_time - DaysToNanoseconds(1));  // 热存储
    selector_->SelectStorageLayer(current_time - DaysToNanoseconds(30)); // 温存储
    selector_->SelectStorageLayer(current_time - DaysToNanoseconds(800)); // 冷存储
    
    auto stats = selector_->GetStatistics();
    EXPECT_EQ(stats.total_selections.load(), 3);
    EXPECT_GT(stats.hot_selections.load(), 0);
    EXPECT_GT(stats.warm_selections.load(), 0);
    EXPECT_GT(stats.cold_selections.load(), 0);
    
    // 重置统计信息
    selector_->ResetStatistics();
    auto reset_stats = selector_->GetStatistics();
    EXPECT_EQ(reset_stats.total_selections.load(), 0);
}

// 测试配置更新
TEST_F(StorageLayerSelectorTest, ConfigUpdateTest) {
    StorageSelectionConfig new_config = config_;
    new_config.hot_storage_days = 14;  // 从7天改为14天
    new_config.warm_storage_days = 1460; // 从730天改为1460天
    new_config.strategy = StorageSelectionStrategy::PERFORMANCE_BASED;
    
    EXPECT_TRUE(selector_->UpdateConfig(new_config));
    
    auto current_config = selector_->GetConfig();
    EXPECT_EQ(current_config.hot_storage_days, 14);
    EXPECT_EQ(current_config.warm_storage_days, 1460);
    EXPECT_EQ(current_config.strategy, StorageSelectionStrategy::PERFORMANCE_BASED);
}

// 测试所有存储层状态获取
TEST_F(StorageLayerSelectorTest, AllLayerStatusTest) {
    // 更新各存储层的指标
    selector_->UpdateLayerMetrics(StorageLayer::HOT, true, 100.0);
    selector_->UpdateLayerMetrics(StorageLayer::WARM, true, 200.0);
    selector_->UpdateLayerMetrics(StorageLayer::COLD, false, 1000.0);
    
    auto all_status = selector_->GetAllLayerStatus();
    EXPECT_EQ(all_status.size(), 3);
    
    // 验证每个存储层都有状态信息
    bool has_hot = false, has_warm = false, has_cold = false;
    for (const auto& status : all_status) {
        switch (status.layer) {
            case StorageLayer::HOT:
                has_hot = true;
                EXPECT_GT(status.metrics.total_requests.load(), 0);
                break;
            case StorageLayer::WARM:
                has_warm = true;
                EXPECT_GT(status.metrics.total_requests.load(), 0);
                break;
            case StorageLayer::COLD:
                has_cold = true;
                EXPECT_GT(status.metrics.total_requests.load(), 0);
                break;
            default:
                break;
        }
    }
    
    EXPECT_TRUE(has_hot && has_warm && has_cold);
}

// 测试工厂方法
TEST_F(StorageLayerSelectorTest, FactoryMethodsTest) {
    // 测试默认工厂方法
    auto default_selector = StorageLayerSelectorFactory::CreateDefault();
    EXPECT_TRUE(default_selector != nullptr);
    EXPECT_TRUE(default_selector->Initialize());
    default_selector->Shutdown();
    
    // 测试高可用性工厂方法
    auto ha_selector = StorageLayerSelectorFactory::CreateHighAvailability();
    EXPECT_TRUE(ha_selector != nullptr);
    EXPECT_TRUE(ha_selector->Initialize());
    
    auto ha_config = ha_selector->GetConfig();
    EXPECT_EQ(ha_config.strategy, StorageSelectionStrategy::PERFORMANCE_BASED);
    EXPECT_TRUE(ha_config.enable_automatic_failover);
    
    ha_selector->Shutdown();
    
    // 测试性能优化工厂方法
    auto perf_selector = StorageLayerSelectorFactory::CreatePerformanceOptimized();
    EXPECT_TRUE(perf_selector != nullptr);
    EXPECT_TRUE(perf_selector->Initialize());
    
    auto perf_config = perf_selector->GetConfig();
    EXPECT_EQ(perf_config.strategy, StorageSelectionStrategy::LOAD_BALANCED);
    EXPECT_TRUE(perf_config.enable_load_balancing);
    
    perf_selector->Shutdown();
}

// 测试并发访问安全性
TEST_F(StorageLayerSelectorTest, ConcurrentAccessTest) {
    const int num_threads = 10;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    
    int64_t current_time = GetCurrentTimestampNs();
    
    // 启动多个线程同时进行选择操作
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, current_time, operations_per_thread, i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                int64_t timestamp = current_time - DaysToNanoseconds(i * 10 + j);
                
                // 执行各种操作
                selector_->SelectStorageLayer(timestamp);
                selector_->UpdateLayerMetrics(StorageLayer::HOT, j % 2 == 0, 100.0 + j);
                
                if (j % 10 == 0) {
                    selector_->GetLayerStatus(StorageLayer::WARM);
                    selector_->GetAllLayerStatus();
                }
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    // 验证统计信息
    auto stats = selector_->GetStatistics();
    EXPECT_EQ(stats.total_selections.load(), num_threads * operations_per_thread);
    
    // 验证指标更新
    auto hot_status = selector_->GetLayerStatus(StorageLayer::HOT);
    EXPECT_EQ(hot_status.metrics.total_requests.load(), num_threads * operations_per_thread);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}