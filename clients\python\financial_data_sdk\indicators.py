"""
Technical Indicators for Financial Data Analysis

Provides common technical indicators optimized for pandas DataFrames and numpy arrays
"""

import pandas as pd
import numpy as np
from typing import Union, Optional, List, Tuple
import warnings
from scipy import stats
from .data_models import KlineData, TickData


class TechnicalIndicators:
    """
    Technical indicators calculator with pandas/numpy optimization
    
    All methods support both pandas DataFrames and numpy arrays as input
    """
    
    @staticmethod
    def sma(data: Union[pd.Series, np.ndarray], window: int) -> Union[pd.Series, np.ndarray]:
        """
        Simple Moving Average
        
        Args:
            data: Price data (Series or array)
            window: Moving average window
            
        Returns:
            SMA values
        """
        if isinstance(data, pd.Series):
            return data.rolling(window=window, min_periods=1).mean()
        else:
            return np.convolve(data, np.ones(window)/window, mode='same')
    
    @staticmethod
    def ema(data: Union[pd.Series, np.ndarray], window: int, alpha: Optional[float] = None) -> Union[pd.Series, np.ndarray]:
        """
        Exponential Moving Average
        
        Args:
            data: Price data
            window: EMA window
            alpha: Smoothing factor (default: 2/(window+1))
            
        Returns:
            EMA values
        """
        if alpha is None:
            alpha = 2.0 / (window + 1)
        
        if isinstance(data, pd.Series):
            return data.ewm(alpha=alpha, adjust=False).mean()
        else:
            ema_values = np.zeros_like(data)
            ema_values[0] = data[0]
            for i in range(1, len(data)):
                ema_values[i] = alpha * data[i] + (1 - alpha) * ema_values[i-1]
            return ema_values
    
    @staticmethod
    def rsi(data: Union[pd.Series, np.ndarray], window: int = 14) -> Union[pd.Series, np.ndarray]:
        """
        Relative Strength Index
        
        Args:
            data: Price data
            window: RSI calculation window
            
        Returns:
            RSI values (0-100)
        """
        if isinstance(data, pd.Series):
            delta = data.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
        else:
            delta = np.diff(data, prepend=data[0])
            gain = np.where(delta > 0, delta, 0)
            loss = np.where(delta < 0, -delta, 0)
            
            avg_gain = np.zeros_like(data)
            avg_loss = np.zeros_like(data)
            
            avg_gain[window-1] = np.mean(gain[:window])
            avg_loss[window-1] = np.mean(loss[:window])
            
            for i in range(window, len(data)):
                avg_gain[i] = (avg_gain[i-1] * (window-1) + gain[i]) / window
                avg_loss[i] = (avg_loss[i-1] * (window-1) + loss[i]) / window
            
            rs = avg_gain / (avg_loss + 1e-10)  # Avoid division by zero
            return 100 - (100 / (1 + rs))
    
    @staticmethod
    def macd(data: Union[pd.Series, np.ndarray], 
             fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[Union[pd.Series, np.ndarray], ...]:
        """
        MACD (Moving Average Convergence Divergence)
        
        Args:
            data: Price data
            fast: Fast EMA period
            slow: Slow EMA period
            signal: Signal line EMA period
            
        Returns:
            Tuple of (MACD line, Signal line, Histogram)
        """
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(data: Union[pd.Series, np.ndarray], 
                       window: int = 20, num_std: float = 2) -> Tuple[Union[pd.Series, np.ndarray], ...]:
        """
        Bollinger Bands
        
        Args:
            data: Price data
            window: Moving average window
            num_std: Number of standard deviations
            
        Returns:
            Tuple of (Upper band, Middle band, Lower band)
        """
        if isinstance(data, pd.Series):
            middle = data.rolling(window=window).mean()
            std = data.rolling(window=window).std()
            upper = middle + (std * num_std)
            lower = middle - (std * num_std)
        else:
            middle = np.convolve(data, np.ones(window)/window, mode='same')
            # Calculate rolling standard deviation
            std = np.zeros_like(data)
            for i in range(window-1, len(data)):
                std[i] = np.std(data[i-window+1:i+1])
            upper = middle + (std * num_std)
            lower = middle - (std * num_std)
        
        return upper, middle, lower
    
    @staticmethod
    def stochastic(high: Union[pd.Series, np.ndarray],
                  low: Union[pd.Series, np.ndarray],
                  close: Union[pd.Series, np.ndarray],
                  k_window: int = 14, d_window: int = 3) -> Tuple[Union[pd.Series, np.ndarray], ...]:
        """
        Stochastic Oscillator
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            k_window: %K calculation window
            d_window: %D smoothing window
            
        Returns:
            Tuple of (%K, %D)
        """
        if isinstance(close, pd.Series):
            lowest_low = low.rolling(window=k_window).min()
            highest_high = high.rolling(window=k_window).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_window).mean()
        else:
            k_percent = np.zeros_like(close)
            for i in range(k_window-1, len(close)):
                lowest_low = np.min(low[i-k_window+1:i+1])
                highest_high = np.max(high[i-k_window+1:i+1])
                k_percent[i] = 100 * ((close[i] - lowest_low) / (highest_high - lowest_low + 1e-10))
            
            d_percent = np.convolve(k_percent, np.ones(d_window)/d_window, mode='same')
        
        return k_percent, d_percent
    
    @staticmethod
    def atr(high: Union[pd.Series, np.ndarray],
            low: Union[pd.Series, np.ndarray],
            close: Union[pd.Series, np.ndarray],
            window: int = 14) -> Union[pd.Series, np.ndarray]:
        """
        Average True Range
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            window: ATR calculation window
            
        Returns:
            ATR values
        """
        if isinstance(close, pd.Series):
            prev_close = close.shift(1)
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            return true_range.rolling(window=window).mean()
        else:
            true_range = np.zeros_like(close)
            true_range[0] = high[0] - low[0]
            
            for i in range(1, len(close)):
                tr1 = high[i] - low[i]
                tr2 = abs(high[i] - close[i-1])
                tr3 = abs(low[i] - close[i-1])
                true_range[i] = max(tr1, tr2, tr3)
            
            # Calculate ATR using exponential moving average
            atr_values = np.zeros_like(true_range)
            atr_values[window-1] = np.mean(true_range[:window])
            
            for i in range(window, len(true_range)):
                atr_values[i] = (atr_values[i-1] * (window-1) + true_range[i]) / window
            
            return atr_values
    
    @staticmethod
    def williams_r(high: Union[pd.Series, np.ndarray],
                   low: Union[pd.Series, np.ndarray],
                   close: Union[pd.Series, np.ndarray],
                   window: int = 14) -> Union[pd.Series, np.ndarray]:
        """
        Williams %R
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            window: Calculation window
            
        Returns:
            Williams %R values (-100 to 0)
        """
        if isinstance(close, pd.Series):
            highest_high = high.rolling(window=window).max()
            lowest_low = low.rolling(window=window).min()
            return -100 * ((highest_high - close) / (highest_high - lowest_low))
        else:
            williams_r = np.zeros_like(close)
            for i in range(window-1, len(close)):
                highest_high = np.max(high[i-window+1:i+1])
                lowest_low = np.min(low[i-window+1:i+1])
                williams_r[i] = -100 * ((highest_high - close[i]) / (highest_high - lowest_low + 1e-10))
            
            return williams_r
    
    @staticmethod
    def cci(high: Union[pd.Series, np.ndarray],
            low: Union[pd.Series, np.ndarray],
            close: Union[pd.Series, np.ndarray],
            window: int = 20) -> Union[pd.Series, np.ndarray]:
        """
        Commodity Channel Index
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            window: CCI calculation window
            
        Returns:
            CCI values
        """
        typical_price = (high + low + close) / 3
        
        if isinstance(close, pd.Series):
            sma_tp = typical_price.rolling(window=window).mean()
            mad = typical_price.rolling(window=window).apply(lambda x: np.mean(np.abs(x - x.mean())))
            return (typical_price - sma_tp) / (0.015 * mad)
        else:
            cci_values = np.zeros_like(close)
            for i in range(window-1, len(close)):
                tp_window = typical_price[i-window+1:i+1]
                sma_tp = np.mean(tp_window)
                mad = np.mean(np.abs(tp_window - sma_tp))
                cci_values[i] = (typical_price[i] - sma_tp) / (0.015 * mad + 1e-10)
            
            return cci_values
    
    @staticmethod
    def obv(close: Union[pd.Series, np.ndarray],
            volume: Union[pd.Series, np.ndarray]) -> Union[pd.Series, np.ndarray]:
        """
        On-Balance Volume
        
        Args:
            close: Close prices
            volume: Volume data
            
        Returns:
            OBV values
        """
        if isinstance(close, pd.Series):
            price_change = close.diff()
            obv_values = volume.copy()
            obv_values[price_change < 0] = -volume[price_change < 0]
            obv_values[price_change == 0] = 0
            return obv_values.cumsum()
        else:
            obv_values = np.zeros_like(close)
            obv_values[0] = volume[0]
            
            for i in range(1, len(close)):
                if close[i] > close[i-1]:
                    obv_values[i] = obv_values[i-1] + volume[i]
                elif close[i] < close[i-1]:
                    obv_values[i] = obv_values[i-1] - volume[i]
                else:
                    obv_values[i] = obv_values[i-1]
            
            return obv_values
    
    @staticmethod
    def vwap(high: Union[pd.Series, np.ndarray],
             low: Union[pd.Series, np.ndarray],
             close: Union[pd.Series, np.ndarray],
             volume: Union[pd.Series, np.ndarray]) -> Union[pd.Series, np.ndarray]:
        """
        Volume Weighted Average Price
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            volume: Volume data
            
        Returns:
            VWAP values
        """
        typical_price = (high + low + close) / 3
        
        if isinstance(close, pd.Series):
            return (typical_price * volume).cumsum() / volume.cumsum()
        else:
            pv = typical_price * volume
            return np.cumsum(pv) / np.cumsum(volume)


class IndicatorAnalyzer:
    """
    Advanced indicator analysis and signal generation
    """
    
    @staticmethod
    def generate_signals(df: pd.DataFrame, 
                        indicators: List[str] = None,
                        thresholds: Dict = None) -> pd.DataFrame:
        """
        Generate trading signals based on technical indicators
        
        Args:
            df: DataFrame with OHLCV data
            indicators: List of indicators to calculate
            thresholds: Custom thresholds for signal generation
            
        Returns:
            DataFrame with signals
        """
        if indicators is None:
            indicators = ['rsi', 'macd', 'bollinger']
        
        if thresholds is None:
            thresholds = {
                'rsi_oversold': 30,
                'rsi_overbought': 70,
                'macd_signal': 0,
                'bb_lower': 0.02,  # 2% below middle band
                'bb_upper': 0.02   # 2% above middle band
            }
        
        signals_df = df.copy()
        
        # RSI signals
        if 'rsi' in indicators:
            rsi = TechnicalIndicators.rsi(df['close'])
            signals_df['rsi'] = rsi
            signals_df['rsi_buy'] = rsi < thresholds['rsi_oversold']
            signals_df['rsi_sell'] = rsi > thresholds['rsi_overbought']
        
        # MACD signals
        if 'macd' in indicators:
            macd_line, signal_line, histogram = TechnicalIndicators.macd(df['close'])
            signals_df['macd'] = macd_line
            signals_df['macd_signal'] = signal_line
            signals_df['macd_histogram'] = histogram
            signals_df['macd_buy'] = (macd_line > signal_line) & (macd_line.shift(1) <= signal_line.shift(1))
            signals_df['macd_sell'] = (macd_line < signal_line) & (macd_line.shift(1) >= signal_line.shift(1))
        
        # Bollinger Bands signals
        if 'bollinger' in indicators:
            bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['close'])
            signals_df['bb_upper'] = bb_upper
            signals_df['bb_middle'] = bb_middle
            signals_df['bb_lower'] = bb_lower
            signals_df['bb_buy'] = df['close'] < bb_lower
            signals_df['bb_sell'] = df['close'] > bb_upper
        
        return signals_df
    
    @staticmethod
    def backtest_strategy(df: pd.DataFrame,
                         buy_signals: pd.Series,
                         sell_signals: pd.Series,
                         initial_capital: float = 100000) -> Dict:
        """
        Simple backtest of a trading strategy
        
        Args:
            df: DataFrame with price data
            buy_signals: Boolean series for buy signals
            sell_signals: Boolean series for sell signals
            initial_capital: Starting capital
            
        Returns:
            Dictionary with backtest results
        """
        position = 0
        capital = initial_capital
        trades = []
        
        for i in range(len(df)):
            price = df['close'].iloc[i]
            
            if buy_signals.iloc[i] and position == 0:
                # Buy signal
                shares = capital // price
                if shares > 0:
                    position = shares
                    capital -= shares * price
                    trades.append({
                        'type': 'buy',
                        'price': price,
                        'shares': shares,
                        'timestamp': df.index[i]
                    })
            
            elif sell_signals.iloc[i] and position > 0:
                # Sell signal
                capital += position * price
                trades.append({
                    'type': 'sell',
                    'price': price,
                    'shares': position,
                    'timestamp': df.index[i]
                })
                position = 0
        
        # Final portfolio value
        final_price = df['close'].iloc[-1]
        final_value = capital + position * final_price
        
        return {
            'initial_capital': initial_capital,
            'final_value': final_value,
            'total_return': (final_value - initial_capital) / initial_capital,
            'num_trades': len(trades),
            'trades': trades
        }


# Utility functions for indicator calculations
def calculate_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate all common technical indicators for a DataFrame
    
    Args:
        df: DataFrame with OHLCV columns
        
    Returns:
        DataFrame with all indicators added
    """
    result_df = df.copy()
    
    # Moving averages
    result_df['sma_20'] = TechnicalIndicators.sma(df['close'], 20)
    result_df['sma_50'] = TechnicalIndicators.sma(df['close'], 50)
    result_df['ema_12'] = TechnicalIndicators.ema(df['close'], 12)
    result_df['ema_26'] = TechnicalIndicators.ema(df['close'], 26)
    
    # Momentum indicators
    result_df['rsi'] = TechnicalIndicators.rsi(df['close'])
    macd, signal, histogram = TechnicalIndicators.macd(df['close'])
    result_df['macd'] = macd
    result_df['macd_signal'] = signal
    result_df['macd_histogram'] = histogram
    
    # Volatility indicators
    bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['close'])
    result_df['bb_upper'] = bb_upper
    result_df['bb_middle'] = bb_middle
    result_df['bb_lower'] = bb_lower
    result_df['atr'] = TechnicalIndicators.atr(df['high'], df['low'], df['close'])
    
    # Oscillators
    k_percent, d_percent = TechnicalIndicators.stochastic(df['high'], df['low'], df['close'])
    result_df['stoch_k'] = k_percent
    result_df['stoch_d'] = d_percent
    result_df['williams_r'] = TechnicalIndicators.williams_r(df['high'], df['low'], df['close'])
    result_df['cci'] = TechnicalIndicators.cci(df['high'], df['low'], df['close'])
    
    # Volume indicators
    if 'volume' in df.columns:
        result_df['obv'] = TechnicalIndicators.obv(df['close'], df['volume'])
        result_df['vwap'] = TechnicalIndicators.vwap(df['high'], df['low'], df['close'], df['volume'])
    
    return result_df