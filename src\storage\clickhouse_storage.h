#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <future>
#include <clickhouse/client.h>
#include <clickhouse/columns/column.h>
#include <clickhouse/columns/date.h>
#include <clickhouse/columns/numeric.h>
#include <clickhouse/columns/string.h>
#include <clickhouse/columns/array.h>

namespace financial_data {

// Forward declarations
struct TickData;
struct KlineData;
struct InstrumentInfo;

/**
 * @brief Configuration for ClickHouse warm storage
 */
struct ClickHouseConfig {
    std::string host = "localhost";
    uint16_t port = 9000;
    std::string database = "market_data";
    std::string username = "admin";
    std::string password = "password123";
    std::string cluster_name = "financial_cluster";
    
    // Connection pool settings
    size_t max_connections = 10;
    std::chrono::seconds connection_timeout{30};
    std::chrono::seconds query_timeout{300};
    
    // Batch settings
    size_t batch_size = 10000;
    std::chrono::seconds batch_timeout{5};
    
    // Retry settings
    int max_retries = 3;
    std::chrono::seconds retry_delay{1};
};

/**
 * @brief Standardized tick data structure for ClickHouse storage
 */
struct StandardizedTick {
    int64_t timestamp_ns;
    std::string symbol;
    std::string exchange;
    std::string product_type; // futures, stock, option, forex
    double last_price;
    uint64_t volume;
    double turnover;
    uint64_t open_interest = 0; // For futures/options
    std::vector<double> bid_prices;
    std::vector<uint32_t> bid_volumes;
    std::vector<double> ask_prices;
    std::vector<uint32_t> ask_volumes;
    uint32_t sequence;
    std::string trade_flag;
    
    // Additional fields for different product types
    double settlement_price = 0.0;
    double pre_settlement = 0.0;
    double pre_close_price = 0.0;
    uint64_t pre_open_interest = 0;
    
    // Options specific
    std::string underlying_symbol;
    std::string option_type; // call/put
    double strike_price = 0.0;
    std::string expiry_date;
    double implied_volatility = 0.0;
    double delta = 0.0, gamma = 0.0, theta = 0.0, vega = 0.0;
    
    // Forex specific
    std::string base_currency;
    std::string quote_currency;
    double spread = 0.0;
};

/**
 * @brief Batch data container for efficient bulk operations
 */
template<typename T>
class DataBatch {
public:
    void Add(const T& data) {
        data_.push_back(data);
    }
    
    void Clear() {
        data_.clear();
    }
    
    size_t Size() const {
        return data_.size();
    }
    
    bool Empty() const {
        return data_.empty();
    }
    
    const std::vector<T>& GetData() const {
        return data_;
    }
    
private:
    std::vector<T> data_;
};

/**
 * @brief Query result structure
 */
template<typename T>
struct QueryResult {
    std::vector<T> data;
    bool has_more = false;
    std::string next_cursor;
    size_t total_rows = 0;
    std::chrono::milliseconds query_time{0};
};

/**
 * @brief ClickHouse warm storage implementation
 * 
 * This class provides high-performance storage for financial market data
 * with automatic data migration from hot storage (Redis) and optimized
 * query performance for historical data analysis.
 */
class ClickHouseStorage {
public:
    explicit ClickHouseStorage(const ClickHouseConfig& config);
    ~ClickHouseStorage();
    
    // Initialization and connection management
    bool Initialize();
    bool IsConnected() const;
    void Disconnect();
    bool Reconnect();
    
    // Batch data insertion methods
    std::future<bool> InsertTickDataBatch(const DataBatch<StandardizedTick>& batch);
    std::future<bool> InsertKlineDataBatch(const DataBatch<KlineData>& batch);
    
    // Single data insertion (for testing/debugging)
    bool InsertTickData(const StandardizedTick& tick);
    bool InsertKlineData(const KlineData& kline);
    
    // Query methods
    QueryResult<StandardizedTick> QueryTickData(
        const std::string& symbol,
        const std::string& exchange,
        int64_t start_timestamp,
        int64_t end_timestamp,
        size_t limit = 10000,
        const std::string& cursor = ""
    );
    
    QueryResult<KlineData> QueryKlineData(
        const std::string& symbol,
        const std::string& exchange,
        const std::string& period, // 1m, 5m, 15m, 1h, 1d
        int64_t start_timestamp,
        int64_t end_timestamp,
        size_t limit = 10000
    );
    
    // Aggregation queries
    std::vector<KlineData> GetOHLCData(
        const std::string& symbol,
        const std::string& exchange,
        const std::string& period,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    // Data migration from Redis hot storage
    std::future<bool> MigrateFromRedis(
        const std::string& redis_key_pattern,
        int64_t cutoff_timestamp
    );
    
    // Data lifecycle management
    bool ArchiveOldData(int64_t cutoff_timestamp);
    bool DeleteExpiredData(int64_t cutoff_timestamp);
    
    // Cluster management
    std::vector<std::string> GetClusterNodes();
    bool CheckClusterHealth();
    
    // Performance monitoring
    struct PerformanceMetrics {
        size_t total_inserts = 0;
        size_t total_queries = 0;
        size_t failed_inserts = 0;
        size_t failed_queries = 0;
        std::chrono::milliseconds avg_insert_time{0};
        std::chrono::milliseconds avg_query_time{0};
        size_t current_connections = 0;
        size_t pending_batches = 0;
    };
    
    PerformanceMetrics GetMetrics() const;
    void ResetMetrics();
    
    // Data quality checks
    bool ValidateDataIntegrity(const std::string& table_name, const std::string& date_range);
    size_t CountMissingSequences(const std::string& symbol, int64_t start_time, int64_t end_time);
    
private:
    // Internal helper methods
    std::string GetTableName(const std::string& product_type, const std::string& data_type = "tick");
    clickhouse::Block CreateTickDataBlock(const DataBatch<StandardizedTick>& batch, const std::string& product_type);
    clickhouse::Block CreateKlineDataBlock(const DataBatch<KlineData>& batch, const std::string& period);
    
    bool ExecuteQuery(const std::string& query);
    clickhouse::Block ExecuteSelectQuery(const std::string& query);
    
    void HandleConnectionError();
    void UpdateMetrics(bool is_insert, bool success, std::chrono::milliseconds duration);
    
    // Configuration and connection
    ClickHouseConfig config_;
    std::unique_ptr<clickhouse::Client> client_;
    std::atomic<bool> connected_{false};
    
    // Performance tracking
    mutable std::mutex metrics_mutex_;
    PerformanceMetrics metrics_;
    
    // Batch processing
    std::mutex batch_mutex_;
    std::condition_variable batch_cv_;
    std::thread batch_processor_;
    std::atomic<bool> stop_batch_processing_{false};
    
    std::queue<std::pair<DataBatch<StandardizedTick>, std::promise<bool>>> tick_batch_queue_;
    std::queue<std::pair<DataBatch<KlineData>, std::promise<bool>>> kline_batch_queue_;
    
    void ProcessBatches();
};

/**
 * @brief Data migration utility for moving data from Redis to ClickHouse
 */
class DataMigrationManager {
public:
    DataMigrationManager(
        std::shared_ptr<ClickHouseStorage> clickhouse_storage,
        const std::string& redis_connection_string
    );
    
    // Migration operations
    std::future<bool> MigrateTickData(
        const std::vector<std::string>& symbols,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    std::future<bool> MigrateKlineData(
        const std::vector<std::string>& symbols,
        const std::string& period,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    // Scheduled migration (runs automatically)
    void StartScheduledMigration(std::chrono::seconds interval = std::chrono::hours(1));
    void StopScheduledMigration();
    
    // Migration status
    struct MigrationStatus {
        size_t total_records = 0;
        size_t migrated_records = 0;
        size_t failed_records = 0;
        std::chrono::system_clock::time_point start_time;
        std::chrono::system_clock::time_point end_time;
        bool is_running = false;
        std::string error_message;
    };
    
    MigrationStatus GetMigrationStatus() const;
    
private:
    std::shared_ptr<ClickHouseStorage> clickhouse_storage_;
    std::string redis_connection_string_;
    
    std::atomic<bool> migration_running_{false};
    std::thread migration_thread_;
    mutable std::mutex status_mutex_;
    MigrationStatus status_;
    
    void RunScheduledMigration(std::chrono::seconds interval);
    bool MigrateDataBatch(const std::vector<StandardizedTick>& batch);
};

} // namespace financial_data