# 高频行情实时采集架构设计

## 1. 系统架构概览

### 1.1 整体架构设计
```
┌────────────────────┐    ┌────────────────────┐    ┌────────────────────┐
│   交易所行情源      │    │    行情采集层       │    │    数据处理层       │
│  ┌─────┬─────┬────┐ │    │  ┌─────┬─────┬────┐ │    │  ┌─────┬─────┬────┐ │
│  │SHFE │DCE  │CZCE│ │───▶│  │Feed │Feed │Feed│ │───▶│  │Parse│Norm │Filter│ │
│  │CFFEX│INE  │    │ │    │  │ #1  │ #2  │ #N │ │    │  │     │     │     │ │
│  └─────┴─────┴────┘ │    │  └─────┴─────┴────┘ │    │  └─────┴─────┴────┘ │
└────────────────────┘    └────────────────────┘    └────────────────────┘
                                      │                          │
                                ┌─────┴─────┐            ┌─────┴─────┐
                                │  负载均衡 │            │  内存总线 │
                                │  Gateway  │            │   Kafka   │
                                └─────┬─────┘            └─────┬─────┘
                                      │                          │
┌────────────────────┐    ┌────────────────────┐    ┌────────────────────┐
│    存储层          │    │    分发层          │    │    监控层          │
│  ┌─────┬─────┬────┐ │    │  ┌─────┬─────┬────┐ │    │  ┌─────┬─────┬────┐ │
│  │Redis│Time │Parq│ │    │  │WebSo│REST │gRPC│ │    │  │Prom │Grafa│Alert│ │
│  │Cache│Scale│uet │ │    │  │cket │ API │API │ │    │  │etheus│na  │Mana │ │
│  └─────┴─────┴────┘ │    │  └─────┴─────┴────┘ │    │  └─────┴─────┴────┘ │
└────────────────────┘    └────────────────────┘    └────────────────────┘
```

### 1.2 分层架构说明

#### 1.2.1 数据采集层 (Acquisition Layer)
- **多源适配**: 支持CTP、恒生O32、金仕达、易盛等主流接口
- **协议解析**: 二进制、FAST、JSON、XML多格式支持
- **断线重连**: 毫秒级检测，秒级重连，断线期间数据回补
- **负载均衡**: 多线路并行接收，自动故障转移

#### 1.2.2 数据处理层 (Processing Layer)
- **实时解析**: 纳秒级时间戳，零拷贝解析
- **数据校验**: CRC校验、完整性检查、异常检测
- **数据标准化**: 统一格式，交易所差异归一化
- **过滤降噪**: 重复数据过滤，异常价格过滤

#### 1.2.3 内存总线层 (Bus Layer)
- **零拷贝传输**: 共享内存 + lock-free队列
- **多播分发**: 支持1000+客户端并发订阅
- **持久化**: 内存+磁盘双写，断电不丢数据
- **回溯**: 支持任意时间段tick级回放

## 2. 核心组件设计

### 2.1 行情采集器 (Market Data Collector)

#### 2.1.1 CTP行情采集器
```cpp
class CTPCollector {
private:
    CThostFtdcMdApi* md_api_;
    std::atomic<int64_t> last_timestamp_;
    LockFreeQueue<MarketData> queue_;
    
public:
    // 初始化连接
    bool Initialize(const Config& config);
    
    // 订阅行情
    bool Subscribe(const std::vector<std::string>& symbols);
    
    // 回调函数 - 深度行情
    void OnDepthMarketData(CThostFtdcDepthMarketDataField* data);
    
    // 心跳检测
    void OnHeartbeat();
    
    // 断线重连
    void OnDisconnected();
};
```

#### 2.1.2 多交易所适配器
```cpp
class MultiExchangeCollector {
private:
    std::map<ExchangeType, std::unique_ptr<ICollector>> collectors_;
    
public:
    // 统一接口
    void StartCollection();
    void StopCollection();
    
    // 标准化输出
    StandardTick ConvertToStandard(const RawData& raw);
};
```

### 2.2 数据标准化引擎

#### 2.2.1 时间戳处理
```cpp
class TimestampNormalizer {
public:
    static int64_t Normalize(const CThostFtdcDepthMarketDataField& data) {
        // 处理交易所时间戳差异
        int64_t base_time = ParseDateTime(data.TradingDay, data.UpdateTime);
        return base_time + data.UpdateMillisec * 1000000;
    }
};
```

#### 2.2.2 合约代码标准化
```cpp
class SymbolNormalizer {
public:
    static std::string Normalize(const std::string& raw_symbol, ExchangeType exchange) {
        switch(exchange) {
            case ExchangeType::SHFE:
                return ToLowerCase(raw_symbol);  // cu2409
            case ExchangeType::CZCE:
                return ToUpperCase(raw_symbol);  // CF401
            case ExchangeType::DCE:
                return ToLowerCase(raw_symbol);  // i2409
            default:
                return raw_symbol;
        }
    }
};
```

### 2.3 内存数据总线

#### 2.3.1 Lock-Free队列设计
```cpp
template<typename T>
class LockFreeQueue {
private:
    struct Node {
        T data;
        std::atomic<Node*> next;
    };
    
    std::atomic<Node*> head_;
    std::atomic<Node*> tail_;
    
public:
    bool Push(const T& data);
    bool Pop(T& data);
    size_t Size() const;
};
```

#### 2.3.2 共享内存设计
```cpp
class SharedMemoryBus {
private:
    struct SharedMemoryHeader {
        std::atomic<int64_t> write_index;
        std::atomic<int64_t> read_index;
        std::atomic<int64_t> sequence;
        char data[];
    };
    
    int shm_fd_;
    SharedMemoryHeader* header_;
    char* data_ptr_;
    
public:
    bool Initialize(const std::string& name, size_t size);
    bool Write(const MarketData& data);
    bool Read(MarketData& data);
};
```

## 3. 低延迟优化

### 3.1 网络优化

#### 3.1.1 内核旁路技术
- **DPDK**: 数据平面开发套件，绕过内核协议栈
- **Solarflare**: 专用网卡，硬件时间戳
- **PTP同步**: 精确时间协议，纳秒级时间同步

#### 3.1.2 网络配置优化
```bash
# 禁用中断合并
ethtool -C eth0 rx-usecs 0 tx-usecs 0

# 启用巨页
echo 1024 > /proc/sys/vm/nr_hugepages

# CPU隔离
echo 1 > /sys/devices/system/cpu/cpu1/online
echo 0 > /sys/devices/system/cpu/cpu1/online

# 网卡队列绑定
taskset -pc 2-7 $(pgrep irq/44-eth0)
```

### 3.2 CPU优化

#### 3.2.1 CPU亲和性设置
```cpp
void SetCPUAffinity(int cpu_id) {
    cpu_set_t mask;
    CPU_ZERO(&mask);
    CPU_SET(cpu_id, &mask);
    pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask);
}
```

#### 3.2.2 内存分配优化
```cpp
class HugePageAllocator {
public:
    static void* Allocate(size_t size) {
        return mmap(nullptr, size, PROT_READ | PROT_WRITE,
                   MAP_PRIVATE | MAP_ANONYMOUS | MAP_HUGETLB, -1, 0);
    }
    
    static void Free(void* ptr, size_t size) {
        munmap(ptr, size);
    }
};
```

### 3.3 编译优化

#### 3.3.1 编译器优化标志
```bash
# GCC优化
CXXFLAGS="-O3 -march=native -mtune=native -flto -fno-plt"
CXXFLAGS+=" -DNDEBUG -fno-stack-protector -fno-exceptions"

# 链接器优化
LDFLAGS="-Wl,-O1 -Wl,--as-needed -Wl,--gc-sections"
```

#### 3.3.2 代码优化技巧
```cpp
// 使用likely/unlikely分支预测
#define likely(x)   __builtin_expect(!!(x), 1)
#define unlikely(x) __builtin_expect(!!(x), 0)

// 预取优化
__builtin_prefetch(data, 0, 3);
```

## 4. 故障恢复与高可用

### 4.1 故障检测机制

#### 4.1.1 心跳检测
```cpp
class HeartbeatMonitor {
private:
    std::atomic<int64_t> last_heartbeat_;
    std::thread monitor_thread_;
    
public:
    void Start() {
        monitor_thread_ = std::thread([this]() {
            while (true) {
                int64_t now = GetCurrentTimestamp();
                if (now - last_heartbeat_.load() > 1000000) { // 1ms
                    TriggerFailover();
                }
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        });
    }
};
```

#### 4.1.2 数据完整性检查
```cpp
class DataIntegrityChecker {
public:
    bool CheckSequenceGap(const std::vector<int64_t>& sequences) {
        for (size_t i = 1; i < sequences.size(); ++i) {
            if (sequences[i] != sequences[i-1] + 1) {
                LogGap(sequences[i-1], sequences[i]);
                return false;
            }
        }
        return true;
    }
};
```

### 4.2 故障恢复策略

#### 4.2.1 主备切换
```cpp
class FailoverManager {
private:
    std::atomic<bool> is_primary_;
    std::unique_ptr<HealthChecker> health_checker_;
    
public:
    void PromoteToPrimary() {
        is_primary_.store(true);
        // 重新订阅行情
        collector_->ResubscribeAll();
        // 通知下游客户端
        NotifyFailover();
    }
    
    void DemoteToBackup() {
        is_primary_.store(false);
        collector_->UnsubscribeAll();
    }
};
```

#### 4.2.2 数据回补机制
```cpp
class GapFiller {
public:
    void FillGap(const std::string& symbol, int64_t start_seq, int64_t end_seq) {
        auto historical_data = historical_client_->QueryRange(symbol, start_seq, end_seq);
        for (const auto& data : historical_data) {
            bus_->Publish(data);
        }
    }
};
```

## 5. 性能监控与度量

### 5.1 延迟监控

#### 5.1.1 端到端延迟测量
```cpp
class LatencyMonitor {
private:
    struct LatencyMetrics {
        std::atomic<int64_t> min_latency{INT64_MAX};
        std::atomic<int64_t> max_latency{0};
        std::atomic<int64_t> avg_latency{0};
        std::atomic<int64_t> count{0};
    };
    
    LatencyMetrics metrics_;
    
public:
    void RecordLatency(int64_t latency_ns) {
        metrics_.min_latency.store(std::min(metrics_.min_latency.load(), latency_ns));
        metrics_.max_latency.store(std::max(metrics_.max_latency.load(), latency_ns));
        
        int64_t old_avg = metrics_.avg_latency.load();
        int64_t new_avg = (old_avg * metrics_.count + latency_ns) / (metrics_.count + 1);
        metrics_.avg_latency.store(new_avg);
        metrics_.count++;
    }
};
```

### 5.2 系统资源监控

#### 5.2.1 CPU使用率监控
```cpp
class CPUMonitor {
public:
    double GetCPUUsage() {
        std::ifstream file("/proc/stat");
        std::string line;
        std::getline(file, line);
        
        std::istringstream iss(line);
        std::string cpu;
        int64_t user, nice, system, idle, iowait, irq, softirq, steal;
        iss >> cpu >> user >> nice >> system >> idle >> iowait >> irq >> softirq >> steal;
        
        int64_t total = user + nice + system + idle + iowait + irq + softirq + steal;
        int64_t usage = total - idle - iowait;
        
        return static_cast<double>(usage) / total * 100.0;
    }
};
```

### 5.3 网络监控

#### 5.3.1 网络延迟测量
```cpp
class NetworkLatencyMonitor {
public:
    int64_t MeasureLatency(const std::string& target_ip) {
        struct timespec start, end;
        clock_gettime(CLOCK_MONOTONIC, &start);
        
        // 发送ICMP echo请求
        int sock = socket(AF_INET, SOCK_RAW, IPPROTO_ICMP);
        // ... 发送和接收逻辑 ...
        
        clock_gettime(CLOCK_MONOTONIC, &end);
        return (end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec);
    }
};
```

## 6. 配置管理

### 6.1 动态配置更新
```yaml
# 配置文件示例
market_data:
  sources:
    - name: "SHFE_CTP"
      type: "ctp"
      front_address: "tcp://***************:10131"
      broker_id: "9999"
      user_id: "your_user"
      password: "your_pwd"
      symbols: ["cu2409", "al2409"]
      heartbeat_interval_ms: 1000
      reconnect_interval_ms: 5000
    
    - name: "DCE_CTP"
      type: "ctp"
      front_address: "tcp://***************:10110"
      symbols: ["i2409", "m2409"]

performance:
  cpu_affinity: [2, 3, 4, 5]
  huge_pages: true
  kernel_bypass: true
  
monitoring:
  latency_threshold_us: 50
  gap_threshold_ms: 100
  
failover:
  heartbeat_timeout_ms: 1000
  switch_delay_ms: 100
  backup_sources:
    - "SHFE_CTP_BACKUP"
    - "DCE_CTP_BACKUP"
```

## 7. 部署方案

### 7.1 硬件配置

#### 7.1.1 推荐配置
- **CPU**: Intel Xeon Gold 6248R (20核40线程) 或 AMD EPYC 7742
- **内存**: 128GB DDR4-3200，支持ECC
- **网络**: Solarflare X2522 10GbE 网卡，支持PTP
- **存储**: Intel Optane P5800X 800GB NVMe SSD
- **系统**: CentOS 8 Stream，内核4.18+

#### 7.1.2 网络拓扑
```
交易所行情源
    │
    ├── 主线路 (光纤直连)
    │   ├── 主采集服务器
    │   └── 备份采集服务器
    │
    └── 备份线路 (互联网VPN)
        ├── 备用采集服务器
        └── 灾备采集服务器
```

### 7.2 容器化部署

#### 7.2.1 Docker配置
```dockerfile
FROM centos:8

# 安装依赖
RUN dnf install -y epel-release && \
    dnf install -y gcc-c++ make cmake boost-devel \
    hugepages numactl-devel rdma-core-devel

# 设置大页
RUN echo 1024 > /proc/sys/vm/nr_hugepages

# 复制应用
COPY ./market-data-collector /app/
WORKDIR /app

# 设置CPU亲和性
CMD numactl --cpunodebind=0 --membind=0 ./collector --config=/etc/config.yaml
```

#### 7.2.2 Kubernetes配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: market-data-collector
spec:
  replicas: 2
  selector:
    matchLabels:
      app: market-data-collector
  template:
    metadata:
      labels:
        app: market-data-collector
    spec:
      nodeSelector:
        node-type: low-latency
      containers:
      - name: collector
        image: market-data-collector:latest
        resources:
          requests:
            cpu: 4
            memory: 8Gi
            hugepages-2Mi: 1Gi
          limits:
            cpu: 4
            memory: 8Gi
            hugepages-2Mi: 1Gi
        volumeMounts:
        - name: config
          mountPath: /etc/config
        - name: shm
          mountPath: /dev/shm
      volumes:
      - name: config
        configMap:
          name: collector-config
      - name: shm
        emptyDir:
          medium: Memory
```

通过以上架构设计，可以实现一个高性能、低延迟、高可用的实时行情采集系统，满足量化投资和高频交易的需求。