#include "test_utils.h"
#include <iostream>
#include <sstream>

namespace financial_data {
namespace sdk {
namespace test {

TestFramework& TestFramework::Instance() {
    static TestFramework instance;
    return instance;
}

void TestFramework::RunTest(const std::string& test_name, std::function<void()> test_func) {
    std::cout << "Running test: " << test_name << "... ";
    
    try {
        test_func();
        std::cout << "PASSED" << std::endl;
        passed_tests_++;
    } catch (const AssertionFailure& e) {
        std::cout << "FAILED" << std::endl;
        std::cout << "  Assertion failed: " << e.what() << std::endl;
        failed_tests_++;
    } catch (const std::exception& e) {
        std::cout << "ERROR" << std::endl;
        std::cout << "  Exception: " << e.what() << std::endl;
        failed_tests_++;
    }
    
    total_tests_++;
}

void TestFramework::PrintSummary() {
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "TEST SUMMARY" << std::endl;
    std::cout << std::string(50, '=') << std::endl;
    std::cout << "Total tests: " << total_tests_ << std::endl;
    std::cout << "Passed: " << passed_tests_ << std::endl;
    std::cout << "Failed: " << failed_tests_ << std::endl;
    
    if (failed_tests_ == 0) {
        std::cout << "All tests PASSED!" << std::endl;
    } else {
        std::cout << "Some tests FAILED!" << std::endl;
    }
    std::cout << std::string(50, '=') << std::endl;
}

bool TestFramework::AllTestsPassed() const {
    return failed_tests_ == 0;
}

void TestFramework::Reset() {
    total_tests_ = 0;
    passed_tests_ = 0;
    failed_tests_ = 0;
}

void Assert(bool condition, const std::string& message) {
    if (!condition) {
        throw AssertionFailure(message);
    }
}

void AssertEqual(const std::string& expected, const std::string& actual, const std::string& message) {
    if (expected != actual) {
        std::ostringstream oss;
        oss << message << " - Expected: '" << expected << "', Actual: '" << actual << "'";
        throw AssertionFailure(oss.str());
    }
}

void AssertEqual(double expected, double actual, double tolerance, const std::string& message) {
    if (std::abs(expected - actual) > tolerance) {
        std::ostringstream oss;
        oss << message << " - Expected: " << expected << ", Actual: " << actual 
            << ", Tolerance: " << tolerance;
        throw AssertionFailure(oss.str());
    }
}

void AssertEqual(int64_t expected, int64_t actual, const std::string& message) {
    if (expected != actual) {
        std::ostringstream oss;
        oss << message << " - Expected: " << expected << ", Actual: " << actual;
        throw AssertionFailure(oss.str());
    }
}

void AssertNotNull(const void* ptr, const std::string& message) {
    if (ptr == nullptr) {
        throw AssertionFailure(message + " - Pointer is null");
    }
}

void AssertThrows(std::function<void()> func, const std::string& message) {
    bool threw = false;
    try {
        func();
    } catch (...) {
        threw = true;
    }
    
    if (!threw) {
        throw AssertionFailure(message + " - Expected exception was not thrown");
    }
}

StandardTick CreateTestTick(const std::string& symbol, double price, uint64_t volume) {
    StandardTick tick;
    tick.symbol = symbol;
    tick.exchange = "TEST";
    tick.last_price = price;
    tick.volume = volume;
    tick.turnover = price * volume;
    tick.timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
    tick.sequence = 1;
    tick.trade_flag = "buy";
    
    // Add some bid/ask data
    tick.bids[0] = PriceLevel(price - 0.01, 100, 5);
    tick.bids[1] = PriceLevel(price - 0.02, 200, 8);
    tick.asks[0] = PriceLevel(price + 0.01, 150, 6);
    tick.asks[1] = PriceLevel(price + 0.02, 250, 10);
    
    return tick;
}

Level2Data CreateTestLevel2(const std::string& symbol, int num_levels) {
    Level2Data level2;
    level2.symbol = symbol;
    level2.exchange = "TEST";
    level2.timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
    level2.sequence = 1;
    
    double base_price = 100.0;
    
    // Add bid levels
    for (int i = 0; i < num_levels; ++i) {
        PriceLevel bid;
        bid.price = base_price - (i + 1) * 0.01;
        bid.volume = 100 * (i + 1);
        bid.order_count = i + 1;
        level2.bids.push_back(bid);
    }
    
    // Add ask levels
    for (int i = 0; i < num_levels; ++i) {
        PriceLevel ask;
        ask.price = base_price + (i + 1) * 0.01;
        ask.volume = 100 * (i + 1);
        ask.order_count = i + 1;
        level2.asks.push_back(ask);
    }
    
    return level2;
}

MockServer::MockServer(int port) : port_(port), running_(false) {}

MockServer::~MockServer() {
    Stop();
}

bool MockServer::Start() {
    if (running_) {
        return true;
    }
    
    try {
        // In a real implementation, this would start a gRPC server
        // For this example, we'll just simulate it
        running_ = true;
        
        server_thread_ = std::thread([this]() {
            while (running_) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                // Simulate server processing
            }
        });
        
        return true;
    } catch (...) {
        return false;
    }
}

void MockServer::Stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    if (server_thread_.joinable()) {
        server_thread_.join();
    }
}

bool MockServer::IsRunning() const {
    return running_;
}

void MockServer::SetTickResponse(const std::vector<StandardTick>& ticks) {
    std::lock_guard<std::mutex> lock(mutex_);
    tick_responses_ = ticks;
}

void MockServer::SetErrorResponse(const std::string& error_message) {
    std::lock_guard<std::mutex> lock(mutex_);
    error_message_ = error_message;
}

std::vector<StandardTick> MockServer::GetTickResponses() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return tick_responses_;
}

std::string MockServer::GetErrorMessage() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return error_message_;
}

} // namespace test
} // namespace sdk
} // namespace financial_data