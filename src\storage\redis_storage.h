#pragma once

#include <string>
#include <memory>
#include <vector>
#include <future>
#include <atomic>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <chrono>
#include <unordered_map>
#include "../proto/data_types.h"

// Forward declarations for Redis C++ client
struct redisContext;
struct redisReply;

namespace financial_data {

// Redis连接配置
struct RedisConfig {
    std::string host = "127.0.0.1";
    int port = 6379;
    std::string password;
    int database = 0;
    int connection_timeout_ms = 5000;
    int command_timeout_ms = 1000;
    int max_connections = 10;
    bool enable_cluster = false;
    std::vector<std::string> cluster_nodes;
    
    // 热数据存储优化配置
    int hot_data_ttl_seconds = 7 * 24 * 3600;  // 7天
    int batch_size = 1000;
    int write_worker_count = 4;
    int max_queue_size = 10000;
    bool enable_compression = true;
    bool enable_pipelining = true;
};

// 查询选项
struct QueryOptions {
    int64_t start_time_ns = 0;
    int64_t end_time_ns = 0;
    int limit = 1000;
    bool include_level2 = false;
    std::string cursor;  // 用于分页查询
};

// 查询结果
struct QueryResult {
    std::vector<StandardTick> ticks;
    std::vector<Level2Data> level2_data;
    std::string next_cursor;
    bool has_more = false;
    int64_t query_time_ns = 0;  // 查询耗时（纳秒）
};

// 批量写入任务
struct BatchWriteTask {
    std::vector<StandardTick> ticks;
    std::vector<Level2Data> level2_data;
    std::promise<bool> promise;
    int64_t submit_time_ns;
    
    BatchWriteTask() {
        submit_time_ns = StandardTick::GetCurrentTimestampNs();
    }
};

// Redis连接池
class RedisConnectionPool {
public:
    explicit RedisConnectionPool(const RedisConfig& config);
    ~RedisConnectionPool();
    
    bool Initialize();
    redisContext* GetConnection();
    void ReturnConnection(redisContext* conn);
    void Shutdown();
    
private:
    RedisConfig config_;
    std::queue<redisContext*> available_connections_;
    std::mutex pool_mutex_;
    std::condition_variable pool_cv_;
    std::atomic<bool> shutdown_flag_{false};
    std::atomic<int> active_connections_{0};
};

// Redis热数据存储层
class RedisHotStorage {
public:
    explicit RedisHotStorage(const RedisConfig& config = RedisConfig{});
    ~RedisHotStorage();
    
    // 初始化和关闭
    bool Initialize();
    void Shutdown();
    
    // 同步写入接口
    bool StoreTick(const StandardTick& tick);
    bool StoreLevel2(const Level2Data& level2);
    bool StoreBatch(const std::vector<StandardTick>& ticks);
    bool StoreBatch(const std::vector<Level2Data>& level2_data);
    
    // 异步写入接口
    std::future<bool> StoreTickAsync(const StandardTick& tick);
    std::future<bool> StoreLevel2Async(const Level2Data& level2);
    std::future<bool> StoreBatchAsync(const std::vector<StandardTick>& ticks);
    std::future<bool> StoreBatchAsync(const std::vector<Level2Data>& level2_data);
    
    // 查询接口
    bool GetLatestTick(const std::string& symbol, StandardTick& tick);
    bool GetLatestLevel2(const std::string& symbol, Level2Data& level2);
    QueryResult QueryTicks(const std::string& symbol, const QueryOptions& options);
    QueryResult QueryLevel2(const std::string& symbol, const QueryOptions& options);
    
    // 批量查询接口
    std::unordered_map<std::string, StandardTick> GetLatestTicks(const std::vector<std::string>& symbols);
    std::unordered_map<std::string, Level2Data> GetLatestLevel2Data(const std::vector<std::string>& symbols);
    
    // 统计和监控接口
    struct StorageStats {
        uint64_t total_ticks_stored = 0;
        uint64_t total_level2_stored = 0;
        uint64_t total_queries = 0;
        double avg_write_latency_us = 0.0;
        double avg_query_latency_us = 0.0;
        uint64_t memory_usage_bytes = 0;
        uint64_t expired_keys = 0;
        int active_connections = 0;
    };
    
    StorageStats GetStats() const;
    void ResetStats();
    
    // 数据过期管理
    bool SetupExpirationPolicy();
    uint64_t CleanupExpiredData();
    
private:
    // 内部实现方法
    bool StoreTickInternal(redisContext* conn, const StandardTick& tick);
    bool StoreLevel2Internal(redisContext* conn, const Level2Data& level2);
    bool GetLatestTickInternal(redisContext* conn, const std::string& symbol, StandardTick& tick);
    bool GetLatestLevel2Internal(redisContext* conn, const std::string& symbol, Level2Data& level2);
    
    // 序列化方法
    std::string SerializeTick(const StandardTick& tick);
    std::string SerializeLevel2(const Level2Data& level2);
    bool DeserializeTick(const std::string& data, StandardTick& tick);
    bool DeserializeLevel2(const std::string& data, Level2Data& level2);
    
    // Redis键生成
    std::string GetTickKey(const std::string& symbol);
    std::string GetLevel2Key(const std::string& symbol);
    std::string GetTimeSeriesKey(const std::string& symbol, const std::string& type);
    std::string GetLatestKey(const std::string& symbol, const std::string& type);
    
    // 异步写入线程
    void AsyncWriteWorker();
    void ProcessBatchWriteTask(const BatchWriteTask& task);
    
    // 统计更新
    void UpdateWriteStats(int64_t latency_ns);
    void UpdateQueryStats(int64_t latency_ns);
    
    // 配置和状态
    RedisConfig config_;
    std::unique_ptr<RedisConnectionPool> connection_pool_;
    
    // 异步写入队列
    std::queue<BatchWriteTask> write_queue_;
    std::mutex write_queue_mutex_;
    std::condition_variable write_queue_cv_;
    std::vector<std::thread> write_workers_;
    std::atomic<bool> shutdown_flag_{false};
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    StorageStats stats_;
    
    // 性能优化方法
    bool OptimizeForHotData();
    bool SetupDataSharding();
    bool ConfigureMemoryPolicy();
    
    // 集群支持方法
    bool InitializeCluster();
    std::string GetClusterNodeForKey(const std::string& key);
    bool IsClusterHealthy();
};

} // namespace financial_data