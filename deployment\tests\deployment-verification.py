#!/usr/bin/env python3
"""
Market Data Collection Enhancement - Deployment Verification Tests
This script verifies that the deployment is working correctly.
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
from typing import Dict, List, Optional, Tuple
import requests
import redis
import clickhouse_connect
from minio import Minio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeploymentVerifier:
    """Verifies deployment health and functionality."""
    
    def __init__(self, namespace: str = "market-data"):
        self.namespace = namespace
        self.test_results: Dict[str, bool] = {}
        
    def run_kubectl_command(self, command: List[str]) -> Tuple[bool, str]:
        """Run kubectl command and return success status and output."""
        try:
            result = subprocess.run(
                ["kubectl"] + command,
                capture_output=True,
                text=True,
                timeout=30
            )
            return result.returncode == 0, result.stdout.strip()
        except subprocess.TimeoutExpired:
            return False, "Command timed out"
        except Exception as e:
            return False, str(e)
    
    def test_namespace_exists(self) -> bool:
        """Test if the namespace exists."""
        logger.info("Testing namespace existence...")
        success, output = self.run_kubectl_command([
            "get", "namespace", self.namespace
        ])
        
        if success:
            logger.info(f"✓ Namespace '{self.namespace}' exists")
            return True
        else:
            logger.error(f"✗ Namespace '{self.namespace}' not found")
            return False
    
    def test_pods_running(self) -> bool:
        """Test if all pods are running."""
        logger.info("Testing pod status...")
        success, output = self.run_kubectl_command([
            "get", "pods", "-n", self.namespace, "-o", "json"
        ])
        
        if not success:
            logger.error("✗ Failed to get pod status")
            return False
        
        try:
            pods_data = json.loads(output)
            pods = pods_data.get("items", [])
            
            if not pods:
                logger.error("✗ No pods found")
                return False
            
            all_running = True
            for pod in pods:
                name = pod["metadata"]["name"]
                phase = pod["status"]["phase"]
                
                if phase == "Running":
                    logger.info(f"✓ Pod '{name}' is running")
                else:
                    logger.error(f"✗ Pod '{name}' is in phase '{phase}'")
                    all_running = False
            
            return all_running
            
        except json.JSONDecodeError:
            logger.error("✗ Failed to parse pod status JSON")
            return False
    
    def test_services_accessible(self) -> bool:
        """Test if services are accessible."""
        logger.info("Testing service accessibility...")
        
        services_to_test = [
            "market-data-collector-service",
            "redis-service", 
            "clickhouse-service",
            "minio-service"
        ]
        
        all_accessible = True
        for service in services_to_test:
            success, output = self.run_kubectl_command([
                "get", "service", service, "-n", self.namespace
            ])
            
            if success:
                logger.info(f"✓ Service '{service}' exists")
            else:
                logger.error(f"✗ Service '{service}' not found")
                all_accessible = False
        
        return all_accessible
    
    def test_application_health(self) -> bool:
        """Test application health endpoint."""
        logger.info("Testing application health...")
        
        # Port forward to access the service
        port_forward_process = None
        try:
            port_forward_process = subprocess.Popen([
                "kubectl", "port-forward", "-n", self.namespace,
                "service/market-data-collector-service", "8080:8080"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # Wait for port forward to establish
            time.sleep(5)
            
            # Test health endpoint
            response = requests.get("http://localhost:8080/health", timeout=10)
            
            if response.status_code == 200:
                logger.info("✓ Application health check passed")
                return True
            else:
                logger.error(f"✗ Application health check failed: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"✗ Failed to connect to application: {e}")
            return False
        finally:
            if port_forward_process:
                port_forward_process.terminate()
                port_forward_process.wait()
    
    def test_redis_connectivity(self) -> bool:
        """Test Redis connectivity."""
        logger.info("Testing Redis connectivity...")
        
        port_forward_process = None
        try:
            port_forward_process = subprocess.Popen([
                "kubectl", "port-forward", "-n", self.namespace,
                "service/redis-service", "6379:6379"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            time.sleep(3)
            
            # Test Redis connection
            r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
            r.ping()
            
            logger.info("✓ Redis connectivity test passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Redis connectivity test failed: {e}")
            return False
        finally:
            if port_forward_process:
                port_forward_process.terminate()
                port_forward_process.wait()
    
    def test_clickhouse_connectivity(self) -> bool:
        """Test ClickHouse connectivity."""
        logger.info("Testing ClickHouse connectivity...")
        
        port_forward_process = None
        try:
            port_forward_process = subprocess.Popen([
                "kubectl", "port-forward", "-n", self.namespace,
                "service/clickhouse-service", "8123:8123"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            time.sleep(3)
            
            # Test ClickHouse connection
            client = clickhouse_connect.get_client(
                host='localhost',
                port=8123,
                username='market_user',
                password='market_password',
                database='market_data'
            )
            
            result = client.query("SELECT 1")
            if result.result_rows:
                logger.info("✓ ClickHouse connectivity test passed")
                return True
            else:
                logger.error("✗ ClickHouse query returned no results")
                return False
                
        except Exception as e:
            logger.error(f"✗ ClickHouse connectivity test failed: {e}")
            return False
        finally:
            if port_forward_process:
                port_forward_process.terminate()
                port_forward_process.wait()
    
    def test_minio_connectivity(self) -> bool:
        """Test MinIO connectivity."""
        logger.info("Testing MinIO connectivity...")
        
        port_forward_process = None
        try:
            port_forward_process = subprocess.Popen([
                "kubectl", "port-forward", "-n", self.namespace,
                "service/minio-service", "9000:9000"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            time.sleep(3)
            
            # Test MinIO connection
            client = Minio(
                "localhost:9000",
                access_key="minioadmin",
                secret_key="minioadmin123",
                secure=False
            )
            
            # List buckets to test connectivity
            buckets = client.list_buckets()
            logger.info(f"✓ MinIO connectivity test passed. Found {len(buckets)} buckets")
            return True
            
        except Exception as e:
            logger.error(f"✗ MinIO connectivity test failed: {e}")
            return False
        finally:
            if port_forward_process:
                port_forward_process.terminate()
                port_forward_process.wait()
    
    def test_persistent_volumes(self) -> bool:
        """Test persistent volume claims."""
        logger.info("Testing persistent volumes...")
        
        success, output = self.run_kubectl_command([
            "get", "pvc", "-n", self.namespace, "-o", "json"
        ])
        
        if not success:
            logger.error("✗ Failed to get PVC status")
            return False
        
        try:
            pvc_data = json.loads(output)
            pvcs = pvc_data.get("items", [])
            
            all_bound = True
            for pvc in pvcs:
                name = pvc["metadata"]["name"]
                phase = pvc["status"]["phase"]
                
                if phase == "Bound":
                    logger.info(f"✓ PVC '{name}' is bound")
                else:
                    logger.error(f"✗ PVC '{name}' is in phase '{phase}'")
                    all_bound = False
            
            return all_bound
            
        except json.JSONDecodeError:
            logger.error("✗ Failed to parse PVC status JSON")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all verification tests."""
        logger.info("Starting deployment verification tests...")
        
        tests = [
            ("Namespace Exists", self.test_namespace_exists),
            ("Pods Running", self.test_pods_running),
            ("Services Accessible", self.test_services_accessible),
            ("Persistent Volumes", self.test_persistent_volumes),
            ("Application Health", self.test_application_health),
            ("Redis Connectivity", self.test_redis_connectivity),
            ("ClickHouse Connectivity", self.test_clickhouse_connectivity),
            ("MinIO Connectivity", self.test_minio_connectivity),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n--- Running test: {test_name} ---")
            try:
                result = test_func()
                self.test_results[test_name] = result
                if result:
                    passed_tests += 1
            except Exception as e:
                logger.error(f"✗ Test '{test_name}' failed with exception: {e}")
                self.test_results[test_name] = False
        
        # Print summary
        logger.info(f"\n{'='*50}")
        logger.info("DEPLOYMENT VERIFICATION SUMMARY")
        logger.info(f"{'='*50}")
        
        for test_name, result in self.test_results.items():
            status = "✓ PASS" if result else "✗ FAIL"
            logger.info(f"{test_name:<30} {status}")
        
        logger.info(f"\nTotal: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            logger.info("🎉 All tests passed! Deployment is healthy.")
            return True
        else:
            logger.error("❌ Some tests failed. Please check the deployment.")
            return False

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Verify Market Data Collection Enhancement deployment"
    )
    parser.add_argument(
        "--namespace", "-n",
        default="market-data",
        help="Kubernetes namespace (default: market-data)"
    )
    parser.add_argument(
        "--test", "-t",
        choices=["namespace", "pods", "services", "volumes", "health", "redis", "clickhouse", "minio"],
        help="Run specific test only"
    )
    
    args = parser.parse_args()
    
    verifier = DeploymentVerifier(args.namespace)
    
    if args.test:
        # Run specific test
        test_methods = {
            "namespace": verifier.test_namespace_exists,
            "pods": verifier.test_pods_running,
            "services": verifier.test_services_accessible,
            "volumes": verifier.test_persistent_volumes,
            "health": verifier.test_application_health,
            "redis": verifier.test_redis_connectivity,
            "clickhouse": verifier.test_clickhouse_connectivity,
            "minio": verifier.test_minio_connectivity,
        }
        
        test_func = test_methods[args.test]
        result = test_func()
        sys.exit(0 if result else 1)
    else:
        # Run all tests
        result = verifier.run_all_tests()
        sys.exit(0 if result else 1)

if __name__ == "__main__":
    main()