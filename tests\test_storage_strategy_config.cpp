#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "../src/config/storage_strategy_config.h"
#include "../src/config/storage_strategy_validator.h"
#include "../src/config/config_manager.h"
#include <memory>
#include <fstream>
#include <filesystem>

using namespace config;
using namespace testing;

class StorageStrategyConfigTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建临时配置文件
        temp_config_file_ = "test_storage_config.json";
        CreateTestConfigFile();
        
        // 初始化配置管理器
        config_manager_ = std::make_shared<ConfigManager>();
        ASSERT_TRUE(config_manager_->Initialize(temp_config_file_));
        
        // 初始化存储策略配置
        storage_config_ = std::make_unique<StorageStrategyConfig>();
        ASSERT_TRUE(storage_config_->Initialize(config_manager_));
    }
    
    void TearDown() override {
        storage_config_.reset();
        config_manager_.reset();
        
        // 清理临时文件
        if (std::filesystem::exists(temp_config_file_)) {
            std::filesystem::remove(temp_config_file_);
        }
    }
    
    void CreateTestConfigFile() {
        nlohmann::json config = {
            {"storage", {
                {"strategy", {
                    {"selection_strategy", "time_based"},
                    {"enable_automatic_failover", true},
                    {"enable_load_balancing", false},
                    {"health_check_interval_seconds", 30},
                    {"health_check_timeout_seconds", 5},
                    {"max_consecutive_failures", 3},
                    {"failover_cooldown_seconds", 60},
                    {"max_failover_attempts", 2},
                    {"load_balance_threshold", 0.8},
                    {"thresholds", {
                        {"hot_storage_days", 7},
                        {"warm_storage_days", 730},
                        {"max_response_time_ms", 1000.0},
                        {"min_success_rate", 0.90},
                        {"health_threshold_success_rate", 0.95},
                        {"degraded_threshold_success_rate", 0.80}
                    }},
                    {"data_type_configs", {
                        {"tick", {
                            {"hot_storage_days", 7},
                            {"warm_storage_days", 365},
                            {"priority_storage", "hot"},
                            {"compression_enabled", false},
                            {"batch_size", 1000},
                            {"max_response_time_ms", 100.0}
                        }},
                        {"kline", {
                            {"hot_storage_days", 30},
                            {"warm_storage_days", 1095},
                            {"priority_storage", "warm"},
                            {"compression_enabled", true},
                            {"batch_size", 5000},
                            {"max_response_time_ms", 500.0}
                        }}
                    }},
                    {"migration_policies", {
                        {"tick", {
                            {"hot_to_warm_hours", 168},
                            {"warm_to_cold_days", 365},
                            {"auto_migration", true},
                            {"migration_batch_size", 50000},
                            {"migration_schedule", "0 2 * * *"}
                        }},
                        {"kline", {
                            {"hot_to_warm_hours", 720},
                            {"warm_to_cold_days", 1095},
                            {"auto_migration", true},
                            {"migration_batch_size", 100000},
                            {"migration_schedule", "0 3 * * *"}
                        }}
                    }}
                }}
            }}
        };
        
        std::ofstream file(temp_config_file_);
        file << config.dump(4);
        file.close();
    }
    
    std::string temp_config_file_;
    std::shared_ptr<ConfigManager> config_manager_;
    std::unique_ptr<StorageStrategyConfig> storage_config_;
};

// 测试基本配置访问
TEST_F(StorageStrategyConfigTest, BasicConfigAccess) {
    // 测试获取基本配置
    EXPECT_EQ(storage_config_->GetSelectionStrategy(), "time_based");
    EXPECT_TRUE(storage_config_->IsAutomaticFailoverEnabled());
    EXPECT_FALSE(storage_config_->IsLoadBalancingEnabled());
    EXPECT_EQ(storage_config_->GetHotStorageDays(), 7);
    EXPECT_EQ(storage_config_->GetWarmStorageDays(), 730);
    EXPECT_DOUBLE_EQ(storage_config_->GetMaxResponseTimeMs(), 1000.0);
    EXPECT_DOUBLE_EQ(storage_config_->GetMinSuccessRate(), 0.90);
    
    // 测试设置基本配置
    EXPECT_TRUE(storage_config_->SetSelectionStrategy("performance_based"));
    EXPECT_EQ(storage_config_->GetSelectionStrategy(), "performance_based");
    
    storage_config_->SetAutomaticFailoverEnabled(false);
    EXPECT_FALSE(storage_config_->IsAutomaticFailoverEnabled());
    
    EXPECT_TRUE(storage_config_->SetHotStorageDays(14));
    EXPECT_EQ(storage_config_->GetHotStorageDays(), 14);
    
    EXPECT_TRUE(storage_config_->SetWarmStorageDays(1000));
    EXPECT_EQ(storage_config_->GetWarmStorageDays(), 1000);
}

// 测试无效配置验证
TEST_F(StorageStrategyConfigTest, InvalidConfigValidation) {
    // 测试无效的选择策略
    EXPECT_FALSE(storage_config_->SetSelectionStrategy("invalid_strategy"));
    
    // 测试无效的存储天数
    EXPECT_FALSE(storage_config_->SetHotStorageDays(0));
    EXPECT_FALSE(storage_config_->SetHotStorageDays(-1));
    EXPECT_FALSE(storage_config_->SetHotStorageDays(800)); // 大于warm_storage_days
    
    EXPECT_FALSE(storage_config_->SetWarmStorageDays(5)); // 小于hot_storage_days
}

// 测试数据类型配置
TEST_F(StorageStrategyConfigTest, DataTypeConfig) {
    // 测试获取已配置的数据类型
    EXPECT_TRUE(storage_config_->HasDataTypeConfig("tick"));
    EXPECT_TRUE(storage_config_->HasDataTypeConfig("kline"));
    EXPECT_FALSE(storage_config_->HasDataTypeConfig("level2"));
    
    auto configured_types = storage_config_->GetConfiguredDataTypes();
    EXPECT_EQ(configured_types.size(), 2);
    EXPECT_THAT(configured_types, UnorderedElementsAre("tick", "kline"));
    
    // 测试获取数据类型配置
    auto tick_config = storage_config_->GetDataTypeConfig("tick");
    EXPECT_EQ(tick_config.hot_storage_days, 7);
    EXPECT_EQ(tick_config.warm_storage_days, 365);
    EXPECT_EQ(tick_config.priority_storage, "hot");
    EXPECT_FALSE(tick_config.compression_enabled);
    EXPECT_EQ(tick_config.batch_size, 1000);
    EXPECT_DOUBLE_EQ(tick_config.max_response_time_ms, 100.0);
    
    // 测试获取未配置数据类型的默认配置
    auto level2_config = storage_config_->GetDataTypeConfig("level2");
    EXPECT_EQ(level2_config.hot_storage_days, 7);  // 使用全局默认值
    EXPECT_EQ(level2_config.warm_storage_days, 730);
    
    // 测试设置新的数据类型配置
    DataTypeStorageConfig new_config;
    new_config.hot_storage_days = 3;
    new_config.warm_storage_days = 180;
    new_config.priority_storage = "hot";
    new_config.compression_enabled = false;
    new_config.batch_size = 500;
    new_config.max_response_time_ms = 50.0;
    
    EXPECT_TRUE(storage_config_->SetDataTypeConfig("level2", new_config));
    EXPECT_TRUE(storage_config_->HasDataTypeConfig("level2"));
    
    auto retrieved_config = storage_config_->GetDataTypeConfig("level2");
    EXPECT_EQ(retrieved_config.hot_storage_days, 3);
    EXPECT_EQ(retrieved_config.warm_storage_days, 180);
    
    // 测试删除数据类型配置
    EXPECT_TRUE(storage_config_->RemoveDataTypeConfig("level2"));
    EXPECT_FALSE(storage_config_->HasDataTypeConfig("level2"));
}

// 测试迁移策略配置
TEST_F(StorageStrategyConfigTest, MigrationPolicyConfig) {
    // 测试获取已配置的迁移策略
    EXPECT_TRUE(storage_config_->HasMigrationPolicy("tick"));
    EXPECT_TRUE(storage_config_->HasMigrationPolicy("kline"));
    EXPECT_FALSE(storage_config_->HasMigrationPolicy("level2"));
    
    // 测试获取迁移策略配置
    auto tick_policy = storage_config_->GetMigrationPolicy("tick");
    EXPECT_DOUBLE_EQ(tick_policy.hot_to_warm_hours, 168.0);
    EXPECT_DOUBLE_EQ(tick_policy.warm_to_cold_days, 365.0);
    EXPECT_TRUE(tick_policy.auto_migration);
    EXPECT_EQ(tick_policy.migration_batch_size, 50000);
    EXPECT_EQ(tick_policy.migration_schedule, "0 2 * * *");
    
    // 测试获取未配置数据类型的默认策略
    auto level2_policy = storage_config_->GetMigrationPolicy("level2");
    EXPECT_DOUBLE_EQ(level2_policy.hot_to_warm_hours, 7 * 24.0);  // 基于全局hot_storage_days
    EXPECT_DOUBLE_EQ(level2_policy.warm_to_cold_days, 730.0);     // 基于全局warm_storage_days
    
    // 测试设置新的迁移策略
    DataTypeMigrationPolicy new_policy;
    new_policy.hot_to_warm_hours = 72.0;
    new_policy.warm_to_cold_days = 180.0;
    new_policy.auto_migration = true;
    new_policy.migration_batch_size = 25000;
    new_policy.migration_schedule = "0 1 * * *";
    
    EXPECT_TRUE(storage_config_->SetMigrationPolicy("level2", new_policy));
    EXPECT_TRUE(storage_config_->HasMigrationPolicy("level2"));
    
    auto retrieved_policy = storage_config_->GetMigrationPolicy("level2");
    EXPECT_DOUBLE_EQ(retrieved_policy.hot_to_warm_hours, 72.0);
    EXPECT_DOUBLE_EQ(retrieved_policy.warm_to_cold_days, 180.0);
    
    // 测试删除迁移策略
    EXPECT_TRUE(storage_config_->RemoveMigrationPolicy("level2"));
    EXPECT_FALSE(storage_config_->HasMigrationPolicy("level2"));
}

// 测试存储层选择配置生成
TEST_F(StorageStrategyConfigTest, StorageSelectionConfig) {
    // 测试全局配置
    auto global_config = storage_config_->GetStorageSelectionConfig();
    EXPECT_EQ(global_config.strategy, financial_data::StorageSelectionStrategy::TIME_BASED);
    EXPECT_TRUE(global_config.enable_automatic_failover);
    EXPECT_FALSE(global_config.enable_load_balancing);
    EXPECT_EQ(global_config.hot_storage_days, 7);
    EXPECT_EQ(global_config.warm_storage_days, 730);
    EXPECT_DOUBLE_EQ(global_config.max_acceptable_response_time_ms, 1000.0);
    
    // 测试数据类型特定配置
    auto tick_config = storage_config_->GetStorageSelectionConfig("tick");
    EXPECT_EQ(tick_config.hot_storage_days, 7);
    EXPECT_EQ(tick_config.warm_storage_days, 365);
    EXPECT_DOUBLE_EQ(tick_config.max_acceptable_response_time_ms, 100.0);
    
    auto kline_config = storage_config_->GetStorageSelectionConfig("kline");
    EXPECT_EQ(kline_config.hot_storage_days, 30);
    EXPECT_EQ(kline_config.warm_storage_days, 1095);
    EXPECT_DOUBLE_EQ(kline_config.max_acceptable_response_time_ms, 500.0);
}

// 测试配置验证
TEST_F(StorageStrategyConfigTest, ConfigValidation) {
    // 测试有效配置
    EXPECT_TRUE(storage_config_->ValidateConfiguration());
    EXPECT_TRUE(storage_config_->GetValidationErrors().empty());
    
    // 测试无效配置
    storage_config_->SetSelectionStrategy("invalid_strategy");
    EXPECT_FALSE(storage_config_->ValidateConfiguration());
    EXPECT_FALSE(storage_config_->GetValidationErrors().empty());
    
    // 恢复有效配置
    storage_config_->SetSelectionStrategy("time_based");
    EXPECT_TRUE(storage_config_->ValidateConfiguration());
}

// 测试配置导出和导入
TEST_F(StorageStrategyConfigTest, ConfigExportImport) {
    // 导出配置
    auto exported_config = storage_config_->ExportConfiguration();
    EXPECT_FALSE(exported_config.empty());
    EXPECT_TRUE(exported_config.contains("selection_strategy"));
    EXPECT_TRUE(exported_config.contains("thresholds"));
    EXPECT_TRUE(exported_config.contains("data_type_configs"));
    EXPECT_TRUE(exported_config.contains("migration_policies"));
    
    // 修改配置
    storage_config_->SetSelectionStrategy("performance_based");
    storage_config_->SetHotStorageDays(14);
    
    // 导入原配置
    EXPECT_TRUE(storage_config_->ImportConfiguration(exported_config));
    EXPECT_EQ(storage_config_->GetSelectionStrategy(), "time_based");
    EXPECT_EQ(storage_config_->GetHotStorageDays(), 7);
}

// 测试配置统计信息
TEST_F(StorageStrategyConfigTest, ConfigStatistics) {
    auto stats = storage_config_->GetStatistics();
    EXPECT_EQ(stats.total_data_types, 6);
    EXPECT_EQ(stats.configured_data_types, 2);
    EXPECT_EQ(stats.migration_policies, 2);
    EXPECT_TRUE(stats.has_global_thresholds);
}

// 测试配置重置
TEST_F(StorageStrategyConfigTest, ConfigReset) {
    // 修改配置
    storage_config_->SetSelectionStrategy("performance_based");
    storage_config_->SetHotStorageDays(14);
    
    // 重置配置
    storage_config_->ResetToDefaults();
    
    // 验证重置结果
    EXPECT_EQ(storage_config_->GetSelectionStrategy(), "time_based");
    EXPECT_EQ(storage_config_->GetHotStorageDays(), 7);
    EXPECT_EQ(storage_config_->GetWarmStorageDays(), 730);
    EXPECT_TRUE(storage_config_->GetConfiguredDataTypes().empty());
}

// 存储策略验证器测试
class StorageStrategyValidatorTest : public ::testing::Test {
protected:
    void SetUp() override {
        validator_ = std::make_unique<StorageStrategyValidator>();
    }
    
    std::unique_ptr<StorageStrategyValidator> validator_;
};

TEST_F(StorageStrategyValidatorTest, ValidConfiguration) {
    nlohmann::json valid_config = {
        {"storage", {
            {"strategy", {
                {"selection_strategy", "time_based"},
                {"enable_automatic_failover", true},
                {"thresholds", {
                    {"hot_storage_days", 7},
                    {"warm_storage_days", 730},
                    {"max_response_time_ms", 1000.0},
                    {"min_success_rate", 0.90}
                }},
                {"data_type_configs", {
                    {"tick", {
                        {"hot_storage_days", 7},
                        {"warm_storage_days", 365},
                        {"priority_storage", "hot"},
                        {"batch_size", 1000}
                    }}
                }},
                {"migration_policies", {
                    {"tick", {
                        {"hot_to_warm_hours", 168},
                        {"warm_to_cold_days", 365},
                        {"auto_migration", true},
                        {"migration_schedule", "0 2 * * *"}
                    }}
                }}
            }}
        }}
    };
    
    auto result = validator_->Validate(valid_config);
    EXPECT_TRUE(result.is_valid);
    EXPECT_TRUE(result.errors.empty());
}

TEST_F(StorageStrategyValidatorTest, InvalidSelectionStrategy) {
    nlohmann::json invalid_config = {
        {"storage", {
            {"strategy", {
                {"selection_strategy", "invalid_strategy"}
            }}
        }}
    };
    
    auto result = validator_->Validate(invalid_config);
    EXPECT_FALSE(result.is_valid);
    EXPECT_FALSE(result.errors.empty());
    EXPECT_THAT(result.errors[0], HasSubstr("Invalid selection_strategy"));
}

TEST_F(StorageStrategyValidatorTest, InvalidThresholds) {
    nlohmann::json invalid_config = {
        {"storage", {
            {"strategy", {
                {"thresholds", {
                    {"hot_storage_days", 100},
                    {"warm_storage_days", 50}  // 小于hot_storage_days
                }}
            }}
        }}
    };
    
    auto result = validator_->Validate(invalid_config);
    EXPECT_FALSE(result.is_valid);
    EXPECT_THAT(result.errors[0], HasSubstr("hot_storage_days must be less than warm_storage_days"));
}

TEST_F(StorageStrategyValidatorTest, InvalidDataTypeConfig) {
    nlohmann::json invalid_config = {
        {"storage", {
            {"strategy", {
                {"data_type_configs", {
                    {"tick", {
                        {"hot_storage_days", 100},
                        {"warm_storage_days", 50},  // 小于hot_storage_days
                        {"priority_storage", "invalid_layer"}
                    }}
                }}
            }}
        }}
    };
    
    auto result = validator_->Validate(invalid_config);
    EXPECT_FALSE(result.is_valid);
    EXPECT_GE(result.errors.size(), 2);
}

TEST_F(StorageStrategyValidatorTest, InvalidMigrationPolicy) {
    nlohmann::json invalid_config = {
        {"storage", {
            {"strategy", {
                {"migration_policies", {
                    {"tick", {
                        {"hot_to_warm_hours", 1000},
                        {"warm_to_cold_days", 1},  // 转换为小时后小于hot_to_warm_hours
                        {"auto_migration", true},
                        {"migration_schedule", "invalid cron"}
                    }}
                }}
            }}
        }}
    };
    
    auto result = validator_->Validate(invalid_config);
    EXPECT_FALSE(result.is_valid);
    EXPECT_GE(result.errors.size(), 2);
}

TEST_F(StorageStrategyValidatorTest, MissingStorageSection) {
    nlohmann::json invalid_config = {
        {"other_section", {}}
    };
    
    auto result = validator_->Validate(invalid_config);
    EXPECT_FALSE(result.is_valid);
    EXPECT_THAT(result.errors[0], HasSubstr("Missing 'storage' configuration section"));
}

TEST_F(StorageStrategyValidatorTest, MissingStrategySection) {
    nlohmann::json config_without_strategy = {
        {"storage", {
            {"hot_storage", {}}
        }}
    };
    
    auto result = validator_->Validate(config_without_strategy);
    EXPECT_TRUE(result.is_valid);  // 应该只是警告，不是错误
    EXPECT_FALSE(result.warnings.empty());
    EXPECT_THAT(result.warnings[0], HasSubstr("Missing 'strategy' configuration"));
}

// 数据类型配置结构测试
TEST(DataTypeStorageConfigTest, ValidConfiguration) {
    DataTypeStorageConfig config;
    config.hot_storage_days = 7;
    config.warm_storage_days = 365;
    config.priority_storage = "hot";
    config.compression_enabled = false;
    config.batch_size = 1000;
    config.max_response_time_ms = 100.0;
    
    EXPECT_TRUE(config.IsValid());
    
    auto json_config = config.ToJson();
    EXPECT_EQ(json_config["hot_storage_days"], 7);
    EXPECT_EQ(json_config["priority_storage"], "hot");
    
    DataTypeStorageConfig loaded_config;
    EXPECT_TRUE(loaded_config.LoadFromJson(json_config));
    EXPECT_EQ(loaded_config.hot_storage_days, 7);
    EXPECT_EQ(loaded_config.priority_storage, "hot");
}

TEST(DataTypeStorageConfigTest, InvalidConfiguration) {
    DataTypeStorageConfig config;
    config.hot_storage_days = 0;  // 无效
    config.warm_storage_days = 365;
    config.priority_storage = "invalid";  // 无效
    
    EXPECT_FALSE(config.IsValid());
}

// 迁移策略配置结构测试
TEST(DataTypeMigrationPolicyTest, ValidConfiguration) {
    DataTypeMigrationPolicy policy;
    policy.hot_to_warm_hours = 168.0;
    policy.warm_to_cold_days = 365.0;
    policy.auto_migration = true;
    policy.migration_batch_size = 50000;
    policy.migration_schedule = "0 2 * * *";
    
    EXPECT_TRUE(policy.IsValid());
    
    auto json_policy = policy.ToJson();
    EXPECT_DOUBLE_EQ(json_policy["hot_to_warm_hours"], 168.0);
    EXPECT_EQ(json_policy["migration_schedule"], "0 2 * * *");
    
    DataTypeMigrationPolicy loaded_policy;
    EXPECT_TRUE(loaded_policy.LoadFromJson(json_policy));
    EXPECT_DOUBLE_EQ(loaded_policy.hot_to_warm_hours, 168.0);
    EXPECT_EQ(loaded_policy.migration_schedule, "0 2 * * *");
}

TEST(DataTypeMigrationPolicyTest, InvalidConfiguration) {
    DataTypeMigrationPolicy policy;
    policy.hot_to_warm_hours = 1000.0;
    policy.warm_to_cold_days = 1.0;  // 转换为小时后小于hot_to_warm_hours
    policy.migration_schedule = "invalid cron";
    
    EXPECT_FALSE(policy.IsValid());
}