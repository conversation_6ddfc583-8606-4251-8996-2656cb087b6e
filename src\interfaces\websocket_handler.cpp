#include "websocket_handler.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <random>

namespace financial_data {
namespace interfaces {

WebSocketHandler::WebSocketHandler(const WebSocketConfig& config) 
    : config_(config), logger_(spdlog::get("websocket_handler")) {
    if (!logger_) {
        logger_ = spdlog::default_logger();
    }
    
    logger_->info("WebSocketHandler initialized on {}:{}", config_.host, config_.port);
}

WebSocketHandler::~WebSocketHandler() {
    Stop();
}

bool WebSocketHandler::Initialize() {
    try {
        // 创建WebSocket服务器
        server_ = std::make_shared<WebSocketServer>();
        
        // 设置日志级别
        server_->set_access_channels(websocketpp::log::alevel::all);
        server_->clear_access_channels(websocketpp::log::alevel::frame_payload);
        server_->set_error_channels(websocketpp::log::elevel::all);
        
        // 初始化ASIO
        server_->init_asio();
        
        // 设置重用地址
        server_->set_reuse_addr(true);
        
        // 启用TCP_NODELAY优化以减少延迟
        if (config_.enable_tcp_nodelay) {
            // WebSocketPP会在连接建立时自动设置TCP_NODELAY
        }
        
        // 设置事件处理器
        server_->set_open_handler([this](ConnectionHdl hdl) { OnOpen(hdl); });
        server_->set_close_handler([this](ConnectionHdl hdl) { OnClose(hdl); });
        server_->set_message_handler([this](ConnectionHdl hdl, MessagePtr msg) { OnMessage(hdl, msg); });
        server_->set_ping_handler([this](ConnectionHdl hdl, std::string payload) { 
            OnPing(hdl, payload); 
            return true; 
        });
        server_->set_pong_handler([this](ConnectionHdl hdl, std::string payload) { OnPong(hdl, payload); });
        server_->set_pong_timeout_handler([this](ConnectionHdl hdl, std::string payload) { OnPongTimeout(hdl, payload); });
        
        // 创建订阅管理器
        subscription_manager_ = std::make_shared<SubscriptionManager>();
        
        // 创建消息压缩器
        if (config_.enable_compression) {
            CompressorConfig comp_config;
            comp_config.type = (config_.compression_algorithm == "gzip") ? 
                              CompressionType::GZIP : CompressionType::DEFLATE;
            comp_config.compression_threshold = config_.compression_threshold;
            message_compressor_ = std::make_shared<MessageCompressor>(comp_config);
        }
        
        // 创建心跳管理器
        if (config_.enable_heartbeat) {
            HeartbeatConfig hb_config;
            hb_config.heartbeat_interval_ms = config_.heartbeat_interval_ms;
            hb_config.heartbeat_timeout_ms = config_.heartbeat_timeout_ms;
            heartbeat_manager_ = std::make_shared<HeartbeatManager>(hb_config);
            
            // 设置心跳事件处理器
            heartbeat_manager_->AddEventHandler(
                [this](HeartbeatEvent event, const std::string& client_id, const ClientHeartbeatInfo& info) {
                    OnHeartbeatEvent(event, client_id, info);
                });
            
            // 设置Ping发送器
            heartbeat_manager_->SetPingSender(
                [this](const std::string& client_id, uint64_t timestamp) {
                    return SendPing(client_id, timestamp);
                });
            
            // 设置断开连接处理器
            heartbeat_manager_->SetDisconnectHandler(
                [this](const std::string& client_id, const std::string& reason) {
                    OnClientDisconnect(client_id, reason);
                });
        }
        
        logger_->info("WebSocketHandler initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize WebSocketHandler: {}", e.what());
        return false;
    }
}

bool WebSocketHandler::Start() {
    if (running_.load()) {
        logger_->warn("WebSocketHandler is already running");
        return false;
    }
    
    try {
        // 启动心跳管理器
        if (heartbeat_manager_ && !heartbeat_manager_->Start()) {
            logger_->error("Failed to start heartbeat manager");
            return false;
        }
        
        // 启动消息发送线程
        running_ = true;
        sender_threads_.reserve(config_.thread_pool_size);
        for (size_t i = 0; i < config_.thread_pool_size; ++i) {
            sender_threads_.emplace_back(&WebSocketHandler::MessageSenderLoop, this, i);
        }
        
        // 启动批量处理线程
        if (config_.enable_batching) {
            sender_threads_.emplace_back(&WebSocketHandler::BatchProcessorLoop, this);
        }
        
        // 监听端口
        server_->listen(config_.port);
        server_->start_accept();
        
        logger_->info("WebSocketHandler started on port {}", config_.port);
        
        // 运行服务器（阻塞）
        server_->run();
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to start WebSocketHandler: {}", e.what());
        running_ = false;
        return false;
    }
}

void WebSocketHandler::Stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    
    // 停止WebSocket服务器
    if (server_) {
        server_->stop();
    }
    
    // 停止心跳管理器
    if (heartbeat_manager_) {
        heartbeat_manager_->Stop();
    }
    
    // 关闭所有连接
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        for (auto& [client_id, context] : connections_) {
            context->Shutdown();
            try {
                auto con = server_->get_con_from_hdl(context->hdl);
                if (con) {
                    con->close(websocketpp::close::status::going_away, "Server shutdown");
                }
            } catch (const std::exception& e) {
                logger_->warn("Error closing connection {}: {}", client_id, e.what());
            }
        }
        connections_.clear();
    }
    
    // 等待发送线程结束
    for (auto& thread : sender_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    sender_threads_.clear();
    
    logger_->info("WebSocketHandler stopped");
}

void WebSocketHandler::HandleMarketData(const MarketDataWrapper& data) {
    if (!running_.load()) {
        return;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 获取订阅该数据的客户端列表
    auto target_clients = subscription_manager_->RouteMarketData(data);
    
    if (target_clients.empty()) {
        return;  // 没有客户端订阅
    }
    
    // 创建消息
    nlohmann::json message;
    if (data.type == MarketDataWrapper::DataType::TICK) {
        MessageBatch batch;
        batch.AddMarketData(data.tick_data);
        message = nlohmann::json::parse(batch.ToJsonString());
    } else if (data.type == MarketDataWrapper::DataType::LEVEL2) {
        MessageBatch batch;
        batch.AddLevel2Data(data.level2_data);
        message = nlohmann::json::parse(batch.ToJsonString());
    }
    
    std::string message_str = message.dump();
    
    // 发送到目标客户端
    for (const auto& client_id : target_clients) {
        auto context = GetConnectionContext(client_id);
        if (context) {
            if (config_.enable_batching) {
                context->AddToBatch(message);
            } else {
                context->AddMessage(message_str);
            }
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    
    RecordMessageStats(true, message_str.size(), latency_ns);
    UpdateLatencyStats(latency_ns);
}

void WebSocketHandler::BroadcastMessage(const std::string& message) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    
    for (auto& [client_id, context] : connections_) {
        context->AddMessage(message);
    }
    
    RecordMessageStats(true, message.size());
}

bool WebSocketHandler::SendMessageToClient(const std::string& client_id, const std::string& message) {
    auto context = GetConnectionContext(client_id);
    if (!context) {
        return false;
    }
    
    context->AddMessage(message);
    RecordMessageStats(true, message.size());
    return true;
}

size_t WebSocketHandler::GetConnectionCount() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    return connections_.size();
}

size_t WebSocketHandler::GetActiveConnectionCount() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    size_t count = 0;
    
    for (const auto& [client_id, context] : connections_) {
        if (context->client_info && context->client_info->state == ConnectionState::CONNECTED) {
            count++;
        }
    }
    
    return count;
}

std::vector<std::string> WebSocketHandler::GetClientList() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    std::vector<std::string> clients;
    clients.reserve(connections_.size());
    
    for (const auto& [client_id, context] : connections_) {
        clients.push_back(client_id);
    }
    
    return clients;
}

std::shared_ptr<ClientConnection> WebSocketHandler::GetClientInfo(const std::string& client_id) const {
    return subscription_manager_->GetClient(client_id);
}

bool WebSocketHandler::DisconnectClient(const std::string& client_id, const std::string& reason) {
    auto context = GetConnectionContext(client_id);
    if (!context) {
        return false;
    }
    
    try {
        auto con = server_->get_con_from_hdl(context->hdl);
        if (con) {
            con->close(websocketpp::close::status::normal, reason);
            return true;
        }
    } catch (const std::exception& e) {
        logger_->error("Error disconnecting client {}: {}", client_id, e.what());
    }
    
    return false;
}

bool WebSocketHandler::UpdateConfig(const WebSocketConfig& config) {
    config_ = config;
    
    // 更新压缩器配置
    if (message_compressor_) {
        CompressorConfig comp_config;
        comp_config.type = (config_.compression_algorithm == "gzip") ? 
                          CompressionType::GZIP : CompressionType::DEFLATE;
        comp_config.compression_threshold = config_.compression_threshold;
        message_compressor_->UpdateConfig(comp_config);
    }
    
    // 更新心跳管理器配置
    if (heartbeat_manager_) {
        HeartbeatConfig hb_config;
        hb_config.heartbeat_interval_ms = config_.heartbeat_interval_ms;
        hb_config.heartbeat_timeout_ms = config_.heartbeat_timeout_ms;
        heartbeat_manager_->UpdateConfig(hb_config);
    }
    
    logger_->info("WebSocketHandler config updated");
    return true;
}

void WebSocketHandler::ResetStatistics() {
    statistics_.Reset();
}

std::string WebSocketHandler::GetStatisticsSummary() const {
    std::ostringstream oss;
    auto stats = statistics_;
    
    oss << "WebSocketHandler Statistics:\n";
    oss << "  Total Connections: " << stats.total_connections.load() << "\n";
    oss << "  Active Connections: " << stats.active_connections.load() << "\n";
    oss << "  Max Concurrent Connections: " << stats.max_concurrent_connections.load() << "\n";
    oss << "  Failed Connections: " << stats.failed_connections.load() << "\n";
    oss << "  Total Messages Sent: " << stats.total_messages_sent.load() << "\n";
    oss << "  Total Messages Received: " << stats.total_messages_received.load() << "\n";
    oss << "  Total Bytes Sent: " << stats.total_bytes_sent.load() << "\n";
    oss << "  Total Bytes Received: " << stats.total_bytes_received.load() << "\n";
    oss << "  Average Message Latency: " << stats.avg_message_latency_ns.load() / 1000 << "μs\n";
    oss << "  Max Message Latency: " << stats.max_message_latency_ns.load() / 1000 << "μs\n";
    oss << "  Messages Per Second: " << stats.messages_per_second.load() << "\n";
    
    if (config_.enable_compression && stats.compressed_messages.load() > 0) {
        oss << "  Compressed Messages: " << stats.compressed_messages.load() << "\n";
        oss << "  Compression Ratio: " << stats.compression_ratio_percent.load() << "%\n";
    }
    
    oss << "  Protocol Errors: " << stats.protocol_errors.load() << "\n";
    oss << "  Timeout Errors: " << stats.timeout_errors.load() << "\n";
    oss << "  Send Errors: " << stats.send_errors.load() << "\n";
    oss << "  Receive Errors: " << stats.receive_errors.load() << "\n";
    
    return oss.str();
}

WebSocketHandler::HealthStatus WebSocketHandler::GetHealthStatus() const {
    HealthStatus status;
    auto stats = statistics_;
    
    status.active_connections = stats.active_connections.load();
    status.max_connections = config_.max_connections;
    status.connection_usage = static_cast<double>(status.active_connections) / status.max_connections;
    status.avg_latency_ms = stats.avg_message_latency_ns.load() / 1000000;  // 转换为毫秒
    
    // 判断健康状态
    if (status.connection_usage <= 0.8 && status.avg_latency_ms <= 50) {
        status.overall_healthy = true;
        status.status_message = "Healthy";
    } else if (status.connection_usage <= 0.9 && status.avg_latency_ms <= 100) {
        status.overall_healthy = true;
        status.status_message = "Warning";
    } else {
        status.overall_healthy = false;
        status.status_message = "Unhealthy";
    }
    
    return status;
}

// WebSocket事件处理器实现
void WebSocketHandler::OnOpen(ConnectionHdl hdl) {
    try {
        auto con = server_->get_con_from_hdl(hdl);
        std::string client_id = GenerateClientId(hdl);
        std::string connection_info = GetConnectionInfo(hdl);
        
        // 检查连接数限制 - 支持1000个并发连接
        if (GetConnectionCount() >= config_.max_connections) {
            logger_->warn("Connection limit reached, rejecting connection from {}", connection_info);
            con->close(websocketpp::close::status::try_again_later, "Connection limit reached");
            statistics_.failed_connections++;
            return;
        }
        
        // 创建连接上下文
        auto context = CreateConnectionContext(client_id, hdl);
        
        // 创建客户端连接信息
        auto client_info = std::make_shared<ClientConnection>();
        client_info->client_id = client_id;
        client_info->remote_address = con->get_remote_endpoint();
        client_info->user_agent = con->get_request_header("User-Agent");
        client_info->state = ConnectionState::CONNECTED;
        
        context->client_info = client_info;
        
        // 添加到订阅管理器
        subscription_manager_->AddClient(client_id, client_info);
        
        // 添加到心跳管理器
        if (heartbeat_manager_) {
            heartbeat_manager_->AddClient(client_id);
        }
        
        // 存储连接上下文
        {
            std::lock_guard<std::mutex> lock(connections_mutex_);
            connections_[client_id] = context;
        }
        
        RecordConnectionStats(true);
        
        logger_->info("Client {} connected from {}", client_id, connection_info);
        
    } catch (const std::exception& e) {
        logger_->error("Error handling connection open: {}", e.what());
        statistics_.failed_connections++;
    }
}

void WebSocketHandler::OnClose(ConnectionHdl hdl) {
    try {
        std::string client_id;
        
        // 查找客户端ID
        {
            std::lock_guard<std::mutex> lock(connections_mutex_);
            for (const auto& [id, context] : connections_) {
                if (!context->hdl.owner_before(hdl) && !hdl.owner_before(context->hdl)) {
                    client_id = id;
                    break;
                }
            }
        }
        
        if (!client_id.empty()) {
            CleanupConnection(client_id);
            logger_->info("Client {} disconnected", client_id);
        }
        
        RecordConnectionStats(false);
        
    } catch (const std::exception& e) {
        logger_->error("Error handling connection close: {}", e.what());
    }
}

void WebSocketHandler::OnMessage(ConnectionHdl hdl, MessagePtr msg) {
    try {
        std::string client_id;
        
        // 查找客户端ID
        {
            std::lock_guard<std::mutex> lock(connections_mutex_);
            for (const auto& [id, context] : connections_) {
                if (!context->hdl.owner_before(hdl) && !hdl.owner_before(context->hdl)) {
                    client_id = id;
                    break;
                }
            }
        }
        
        if (client_id.empty()) {
            logger_->warn("Received message from unknown connection");
            return;
        }
        
        std::string payload = msg->get_payload();
        statistics_.total_messages_received++;
        statistics_.total_bytes_received += payload.size();
        
        // 更新客户端活动时间
        auto client_info = GetClientInfo(client_id);
        if (client_info) {
            client_info->UpdateActivity();
            client_info->messages_received++;
            client_info->bytes_received += payload.size();
        }
        
        // 解析消息
        WebSocketMessage ws_message = WebSocketMessage::FromString(payload);
        ws_message.client_id = client_id;
        
        // 处理消息
        HandleClientMessage(client_id, ws_message);
        
    } catch (const std::exception& e) {
        logger_->error("Error handling message: {}", e.what());
        statistics_.protocol_errors++;
    }
}

void WebSocketHandler::OnPing(ConnectionHdl hdl, std::string payload) {
    logger_->trace("Received ping from connection");
    // WebSocketPP会自动回复pong
}

void WebSocketHandler::OnPong(ConnectionHdl hdl, std::string payload) {
    try {
        std::string client_id;
        
        // 查找客户端ID
        {
            std::lock_guard<std::mutex> lock(connections_mutex_);
            for (const auto& [id, context] : connections_) {
                if (!context->hdl.owner_before(hdl) && !hdl.owner_before(context->hdl)) {
                    client_id = id;
                    break;
                }
            }
        }
        
        if (!client_id.empty() && heartbeat_manager_) {
            // 解析时间戳
            if (payload.size() >= sizeof(uint64_t)) {
                uint64_t timestamp = *reinterpret_cast<const uint64_t*>(payload.data());
                heartbeat_manager_->HandlePong(client_id, timestamp);
            }
        }
        
        logger_->trace("Received pong from client {}", client_id);
        
    } catch (const std::exception& e) {
        logger_->error("Error handling pong: {}", e.what());
    }
}

void WebSocketHandler::OnPongTimeout(ConnectionHdl hdl, std::string payload) {
    logger_->warn("Pong timeout from connection");
    statistics_.timeout_errors++;
}

// 继续实现其他方法...
} // namespace interfaces
} // namespace financial_data// 继续WebSock
etHandler的实现

void WebSocketHandler::HandleClientMessage(const std::string& client_id, const WebSocketMessage& message) {
    switch (message.type) {
        case MessageType::SUBSCRIBE:
            HandleSubscriptionRequest(client_id, message.payload);
            break;
        case MessageType::UNSUBSCRIBE:
            HandleUnsubscriptionRequest(client_id, message.payload);
            break;
        case MessageType::HEARTBEAT:
            HandleHeartbeat(client_id);
            break;
        default:
            logger_->warn("Unknown message type from client {}: {}", client_id, static_cast<int>(message.type));
            SendError(client_id, "Unknown message type");
            statistics_.protocol_errors++;
            break;
    }
}

void WebSocketHandler::HandleSubscriptionRequest(const std::string& client_id, const nlohmann::json& payload) {
    try {
        SubscriptionRequest request = SubscriptionRequest::FromJson(payload);
        request.client_id = client_id;
        
        auto response = subscription_manager_->ProcessSubscription(request);
        
        nlohmann::json response_json;
        response_json["type"] = "subscription_response";
        response_json["success"] = response.success;
        response_json["message"] = response.message;
        response_json["subscription_id"] = response.subscription_id;
        response_json["subscribed_symbols"] = response.subscribed_symbols;
        
        SendResponse(client_id, response_json);
        
        if (response.success) {
            logger_->info("Client {} subscribed to {} symbols", client_id, response.subscribed_symbols.size());
        } else {
            logger_->warn("Subscription failed for client {}: {}", client_id, response.message);
        }
        
    } catch (const std::exception& e) {
        logger_->error("Error processing subscription request from client {}: {}", client_id, e.what());
        SendError(client_id, "Invalid subscription request");
        statistics_.subscription_errors++;
    }
}

void WebSocketHandler::HandleUnsubscriptionRequest(const std::string& client_id, const nlohmann::json& payload) {
    try {
        SubscriptionRequest request = SubscriptionRequest::FromJson(payload);
        request.client_id = client_id;
        
        auto response = subscription_manager_->ProcessUnsubscription(request);
        
        nlohmann::json response_json;
        response_json["type"] = "unsubscription_response";
        response_json["success"] = response.success;
        response_json["message"] = response.message;
        
        SendResponse(client_id, response_json);
        
        logger_->info("Client {} unsubscribed", client_id);
        
    } catch (const std::exception& e) {
        logger_->error("Error processing unsubscription request from client {}: {}", client_id, e.what());
        SendError(client_id, "Invalid unsubscription request");
    }
}

void WebSocketHandler::HandleHeartbeat(const std::string& client_id) {
    if (heartbeat_manager_) {
        heartbeat_manager_->UpdateHeartbeat(client_id);
    }
    
    // 发送心跳响应
    nlohmann::json response;
    response["type"] = "heartbeat_response";
    response["timestamp"] = StandardTick::GetCurrentTimestampNs();
    
    SendResponse(client_id, response);
}

void WebSocketHandler::SendResponse(const std::string& client_id, const nlohmann::json& response) {
    std::string message = response.dump();
    SendMessageToClient(client_id, message);
}

void WebSocketHandler::SendError(const std::string& client_id, const std::string& error_message) {
    nlohmann::json error_response;
    error_response["type"] = "error";
    error_response["message"] = error_message;
    error_response["timestamp"] = StandardTick::GetCurrentTimestampNs();
    
    SendResponse(client_id, error_response);
}

void WebSocketHandler::MessageSenderLoop(size_t thread_id) {
    logger_->info("Message sender thread {} started", thread_id);
    
    while (running_.load()) {
        try {
            std::vector<std::shared_ptr<ConnectionContext>> contexts;
            
            // 获取所有连接上下文
            {
                std::lock_guard<std::mutex> lock(connections_mutex_);
                contexts.reserve(connections_.size());
                for (const auto& [client_id, context] : connections_) {
                    contexts.push_back(context);
                }
            }
            
            // 处理每个连接的消息队列
            for (auto& context : contexts) {
                if (!context->queue_active.load()) {
                    continue;
                }
                
                std::unique_lock<std::mutex> lock(context->queue_mutex);
                
                // 等待消息或超时
                context->queue_cv.wait_for(lock, std::chrono::milliseconds(100), 
                    [&context] { return !context->message_queue.empty() || !context->queue_active.load(); });
                
                // 发送队列中的所有消息
                while (!context->message_queue.empty() && context->queue_active.load()) {
                    std::string message = context->message_queue.front();
                    context->message_queue.pop();
                    lock.unlock();
                    
                    // 发送消息
                    if (!SendRawMessage(context->hdl, message)) {
                        statistics_.send_errors++;
                    }
                    
                    lock.lock();
                }
            }
            
        } catch (const std::exception& e) {
            logger_->error("Error in message sender thread {}: {}", thread_id, e.what());
        }
        
        // 短暂休眠避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    logger_->info("Message sender thread {} stopped", thread_id);
}

void WebSocketHandler::BatchProcessorLoop() {
    logger_->info("Batch processor thread started");
    
    while (running_.load()) {
        try {
            std::vector<std::shared_ptr<ConnectionContext>> contexts;
            
            // 获取所有连接上下文
            {
                std::lock_guard<std::mutex> lock(connections_mutex_);
                contexts.reserve(connections_.size());
                for (const auto& [client_id, context] : connections_) {
                    contexts.push_back(context);
                }
            }
            
            // 检查每个连接的批次
            for (auto& context : contexts) {
                if (context->ShouldFlushBatch(config_.batch_size, config_.batch_timeout_ms)) {
                    MessageBatch batch = context->FlushBatch();
                    if (!batch.IsEmpty()) {
                        std::string message = batch.ToJsonString();
                        context->AddMessage(message);
                    }
                }
            }
            
        } catch (const std::exception& e) {
            logger_->error("Error in batch processor thread: {}", e.what());
        }
        
        // 等待一段时间再检查
        std::this_thread::sleep_for(std::chrono::milliseconds(config_.batch_timeout_ms / 2));
    }
    
    logger_->info("Batch processor thread stopped");
}

bool WebSocketHandler::SendRawMessage(ConnectionHdl hdl, const std::string& message) {
    try {
        auto con = server_->get_con_from_hdl(hdl);
        if (!con) {
            return false;
        }
        
        std::string final_message = message;
        
        // 压缩消息（如果启用）
        if (config_.enable_compression && message_compressor_ && 
            message_compressor_->ShouldCompress(message.size())) {
            
            auto comp_result = message_compressor_->Compress(message);
            if (comp_result.success) {
                final_message = std::string(comp_result.compressed_data.begin(), comp_result.compressed_data.end());
                statistics_.compressed_messages++;
                statistics_.compression_ratio_percent = static_cast<uint64_t>(comp_result.compression_ratio * 100);
            }
        }
        
        // 发送消息
        websocketpp::lib::error_code ec;
        con->send(final_message, websocketpp::frame::opcode::text, ec);
        
        if (ec) {
            logger_->error("Error sending message: {}", ec.message());
            return false;
        }
        
        statistics_.total_messages_sent++;
        statistics_.total_bytes_sent += final_message.size();
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Exception sending message: {}", e.what());
        return false;
    }
}

std::string WebSocketHandler::CompressMessage(const std::string& message) {
    if (!message_compressor_ || !message_compressor_->ShouldCompress(message.size())) {
        return message;
    }
    
    auto result = message_compressor_->Compress(message);
    if (result.success) {
        return std::string(result.compressed_data.begin(), result.compressed_data.end());
    }
    
    return message;  // 压缩失败，返回原消息
}

std::string WebSocketHandler::GenerateClientId(ConnectionHdl hdl) {
    try {
        auto con = server_->get_con_from_hdl(hdl);
        if (con) {
            std::ostringstream oss;
            oss << "client_" << std::hex << std::hash<std::string>{}(con->get_remote_endpoint());
            return oss.str();
        }
    } catch (const std::exception& e) {
        logger_->warn("Error generating client ID: {}", e.what());
    }
    
    // 备用方案：使用随机数
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint64_t> dis;
    
    std::ostringstream oss;
    oss << "client_" << std::hex << dis(gen);
    return oss.str();
}

std::string WebSocketHandler::GetConnectionInfo(ConnectionHdl hdl) {
    try {
        auto con = server_->get_con_from_hdl(hdl);
        if (con) {
            return con->get_remote_endpoint();
        }
    } catch (const std::exception& e) {
        logger_->warn("Error getting connection info: {}", e.what());
    }
    
    return "unknown";
}

void WebSocketHandler::RecordConnectionStats(bool connected) {
    if (connected) {
        statistics_.total_connections++;
        uint64_t current_active = statistics_.active_connections++;
        
        // 更新最大并发连接数
        uint64_t current_max = statistics_.max_concurrent_connections.load();
        while (current_active > current_max && 
               !statistics_.max_concurrent_connections.compare_exchange_weak(current_max, current_active)) {
            current_max = statistics_.max_concurrent_connections.load();
        }
    } else {
        if (statistics_.active_connections > 0) {
            statistics_.active_connections--;
        }
    }
}

void WebSocketHandler::RecordMessageStats(bool sent, size_t message_size, uint64_t latency_ns) {
    if (sent) {
        statistics_.total_messages_sent++;
        statistics_.total_bytes_sent += message_size;
    } else {
        statistics_.total_messages_received++;
        statistics_.total_bytes_received += message_size;
    }
    
    if (latency_ns > 0) {
        UpdateLatencyStats(latency_ns);
    }
}

void WebSocketHandler::UpdateLatencyStats(uint64_t latency_ns) {
    // 更新最大延迟
    uint64_t current_max = statistics_.max_message_latency_ns.load();
    while (latency_ns > current_max && 
           !statistics_.max_message_latency_ns.compare_exchange_weak(current_max, latency_ns)) {
        current_max = statistics_.max_message_latency_ns.load();
    }
    
    // 更新平均延迟（简单移动平均）
    uint64_t current_avg = statistics_.avg_message_latency_ns.load();
    uint64_t new_avg = (current_avg * 7 + latency_ns) / 8;
    statistics_.avg_message_latency_ns = new_avg;
}

void WebSocketHandler::OnHeartbeatEvent(HeartbeatEvent event, const std::string& client_id, const ClientHeartbeatInfo& info) {
    switch (event) {
        case HeartbeatEvent::HEARTBEAT_TIMEOUT:
            logger_->warn("Client {} heartbeat timeout", client_id);
            DisconnectClient(client_id, "Heartbeat timeout");
            break;
        case HeartbeatEvent::STATUS_CHANGED:
            logger_->debug("Client {} heartbeat status changed", client_id);
            break;
        default:
            break;
    }
}

bool WebSocketHandler::SendPing(const std::string& client_id, uint64_t timestamp) {
    auto context = GetConnectionContext(client_id);
    if (!context) {
        return false;
    }
    
    try {
        auto con = server_->get_con_from_hdl(context->hdl);
        if (!con) {
            return false;
        }
        
        // 将时间戳作为ping payload
        std::string payload(reinterpret_cast<const char*>(&timestamp), sizeof(timestamp));
        
        websocketpp::lib::error_code ec;
        con->ping(payload, ec);
        
        if (ec) {
            logger_->error("Error sending ping to client {}: {}", client_id, ec.message());
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Exception sending ping to client {}: {}", client_id, e.what());
        return false;
    }
}

void WebSocketHandler::OnClientDisconnect(const std::string& client_id, const std::string& reason) {
    logger_->info("Disconnecting client {} due to: {}", client_id, reason);
    DisconnectClient(client_id, reason);
}

void WebSocketHandler::CleanupConnection(const std::string& client_id) {
    // 从连接映射中移除
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        auto it = connections_.find(client_id);
        if (it != connections_.end()) {
            it->second->Shutdown();
            connections_.erase(it);
        }
    }
    
    // 从订阅管理器中移除
    subscription_manager_->RemoveClient(client_id);
    
    // 从心跳管理器中移除
    if (heartbeat_manager_) {
        heartbeat_manager_->RemoveClient(client_id);
    }
}

bool WebSocketHandler::ValidateMessage(const nlohmann::json& message, std::string& error_message) {
    if (!message.contains("type")) {
        error_message = "Missing message type";
        return false;
    }
    
    if (!message["type"].is_string()) {
        error_message = "Invalid message type";
        return false;
    }
    
    return true;
}

bool WebSocketHandler::ApplyRateLimit(const std::string& client_id) {
    // 简化的速率限制实现
    // 实际应用中可能需要更复杂的令牌桶或滑动窗口算法
    return true;
}

std::shared_ptr<ConnectionContext> WebSocketHandler::GetConnectionContext(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    auto it = connections_.find(client_id);
    return (it != connections_.end()) ? it->second : nullptr;
}

std::shared_ptr<ConnectionContext> WebSocketHandler::CreateConnectionContext(const std::string& client_id, ConnectionHdl hdl) {
    auto context = std::make_shared<ConnectionContext>();
    context->client_id = client_id;
    context->hdl = hdl;
    context->server = server_;
    return context;
}

} // namespace interfaces
} // namespace financial_data