"""
Custom exceptions for the Financial Data SDK
"""


class FinancialDataSDKError(Exception):
    """Base exception for all SDK errors"""
    pass


class ConnectionError(FinancialDataSDKError):
    """Raised when connection to server fails"""
    pass


class DataError(FinancialDataSDKError):
    """Raised when data validation or processing fails"""
    pass


class TimeoutError(FinancialDataSDKError):
    """Raised when operation times out"""
    pass


class AuthenticationError(FinancialDataSDKError):
    """Raised when authentication fails"""
    pass


class RateLimitError(FinancialDataSDKError):
    """Raised when rate limit is exceeded"""
    pass


class ConfigurationError(FinancialDataSDKError):
    """Raised when configuration is invalid"""
    pass


class CacheError(FinancialDataSDKError):
    """Raised when cache operation fails"""
    pass