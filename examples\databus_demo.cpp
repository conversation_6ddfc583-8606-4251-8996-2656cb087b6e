/**
 * @file databus_demo.cpp
 * @brief 高性能数据总线使用示例
 * 
 * 本示例演示了如何使用金融数据服务系统的高性能数据总线，
 * 包括数据推送、客户端注册、订阅管理和性能监控等功能。
 */

#include <iostream>
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <random>

#include "../src/databus/data_bus.h"
#include "../src/proto/data_types.h"

using namespace financial_data;
using namespace financial_data::databus;

/**
 * @brief 创建测试用的Tick数据
 */
StandardTick CreateTestTick(const std::string& symbol, const std::string& exchange, uint32_t sequence) {
    StandardTick tick;
    tick.timestamp_ns = StandardTick::GetCurrentTimestampNs();
    tick.symbol = symbol;
    tick.exchange = exchange;
    tick.last_price = 78000.0 + (sequence % 1000) * 0.1;
    tick.volume = 1000 + sequence;
    tick.turnover = tick.last_price * tick.volume;
    tick.sequence = sequence;
    tick.trade_flag = (sequence % 2 == 0) ? "buy_open" : "sell_close";
    
    // 设置五档买卖盘数据
    for (int i = 0; i < 5; ++i) {
        tick.bids[i] = PriceLevel(tick.last_price - (i + 1) * 10.0, 100 + i * 10, i + 1);
        tick.asks[i] = PriceLevel(tick.last_price + (i + 1) * 10.0, 100 + i * 10, i + 1);
    }
    
    return tick;
}

/**
 * @brief 创建测试用的Level2数据
 */
Level2Data CreateTestLevel2(const std::string& symbol, const std::string& exchange, uint32_t sequence) {
    Level2Data level2;
    level2.timestamp_ns = StandardTick::GetCurrentTimestampNs();
    level2.symbol = symbol;
    level2.exchange = exchange;
    level2.sequence = sequence;
    
    // 添加10档买卖盘数据
    for (int i = 0; i < 10; ++i) {
        level2.bids.emplace_back(78000.0 - i * 10.0, 100 + i * 10, i + 1);
        level2.asks.emplace_back(78100.0 + i * 10.0, 100 + i * 10, i + 1);
    }
    
    return level2;
}

/**
 * @brief 数据生产者线程
 */
void DataProducer(DataBus* data_bus, const std::string& symbol, const std::string& exchange, 
                 int message_count, std::atomic<bool>& running) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1, 10);
    
    int sequence = 0;
    while (running && sequence < message_count) {
        // 随机选择发送Tick或Level2数据
        if (dis(gen) <= 7) {  // 70%概率发送Tick数据
            StandardTick tick = CreateTestTick(symbol, exchange, sequence);
            if (!data_bus->PushTick(tick)) {
                std::cerr << "Failed to push tick data for " << symbol << std::endl;
            }
        } else {  // 30%概率发送Level2数据
            Level2Data level2 = CreateTestLevel2(symbol, exchange, sequence);
            if (!data_bus->PushLevel2(level2)) {
                std::cerr << "Failed to push level2 data for " << symbol << std::endl;
            }
        }
        
        sequence++;
        
        // 控制发送频率（模拟真实市场数据频率）
        std::this_thread::sleep_for(std::chrono::microseconds(100));
    }
    
    std::cout << "Producer for " << symbol << " finished, sent " << sequence << " messages" << std::endl;
}

/**
 * @brief 统计监控线程
 */
void StatisticsMonitor(DataBus* data_bus, std::atomic<bool>& running) {
    while (running) {
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        if (!running) break;
        
        auto stats = data_bus->GetStatistics();
        auto queue_status = data_bus->GetQueueStatus();
        auto routing_stats = data_bus->GetRoutingStatistics();
        auto bp_stats = data_bus->GetBackpressureStatistics();
        
        std::cout << "\n=== DataBus Statistics ===" << std::endl;
        std::cout << "Messages Received: " << stats.total_messages_received.load() << std::endl;
        std::cout << "Messages Processed: " << stats.total_messages_processed.load() << std::endl;
        std::cout << "Messages Sent: " << stats.total_messages_sent.load() << std::endl;
        std::cout << "Messages Dropped: " << stats.total_messages_dropped.load() << std::endl;
        std::cout << "Processing Rate: " << stats.GetProcessingRate() * 100.0 << "%" << std::endl;
        std::cout << "Drop Rate: " << stats.GetDropRate() * 100.0 << "%" << std::endl;
        std::cout << "Throughput: " << stats.throughput_per_second.load() << " msg/sec" << std::endl;
        std::cout << "Avg Latency: " << stats.avg_processing_latency_ns.load() / 1000.0 << " μs" << std::endl;
        std::cout << "Max Latency: " << stats.max_processing_latency_ns.load() / 1000.0 << " μs" << std::endl;
        
        std::cout << "\n--- Queue Status ---" << std::endl;
        std::cout << "Input Queue Usage: " << queue_status.input_queue_usage << "%" << std::endl;
        std::cout << "Output Queue Usage: " << queue_status.output_queue_usage << "%" << std::endl;
        std::cout << "Client Queues Size: " << queue_status.total_client_queues_size << std::endl;
        
        std::cout << "\n--- Routing Statistics ---" << std::endl;
        std::cout << "Messages Routed: " << routing_stats.total_messages_routed.load() << std::endl;
        std::cout << "Routing Errors: " << routing_stats.routing_errors.load() << std::endl;
        std::cout << "Avg Routing Latency: " << routing_stats.avg_routing_latency_ns.load() / 1000.0 << " μs" << std::endl;
        
        std::cout << "\n--- Backpressure Status ---" << std::endl;
        std::cout << "Current State: " << static_cast<int>(bp_stats.current_state) << std::endl;
        std::cout << "Dropped Messages: " << bp_stats.dropped_messages.load() << std::endl;
        std::cout << "Throttled Messages: " << bp_stats.throttled_messages.load() << std::endl;
        
        std::cout << "Active Clients: " << data_bus->GetActiveClients().size() << std::endl;
        std::cout << "========================\n" << std::endl;
    }
}

int main() {
    std::cout << "金融数据服务系统 - 高性能数据总线演示" << std::endl;
    std::cout << "======================================" << std::endl;
    
    // 创建高性能配置的数据总线
    auto data_bus = DataBusFactory::CreateHighPerformance();
    
    // 禁用Kafka以简化演示
    auto config = data_bus->GetConfig();
    config.enable_kafka = false;
    data_bus->UpdateConfig(config);
    
    // 启动数据总线
    if (!data_bus->Start()) {
        std::cerr << "Failed to start DataBus" << std::endl;
        return -1;
    }
    
    std::cout << "数据总线已启动" << std::endl;
    
    // 客户端消息计数器
    std::atomic<int> cu_messages{0};
    std::atomic<int> al_messages{0};
    std::atomic<int> all_messages{0};
    std::atomic<int> high_volume_messages{0};
    
    // 注册CU合约专用客户端
    std::string cu_client = "cu_trader";
    bool cu_registered = data_bus->RegisterClient(
        cu_client,
        "trader",
        [&](const MarketDataWrapper& data) -> bool {
            if (data.type == MarketDataWrapper::DataType::TICK) {
                cu_messages.fetch_add(1);
                if (cu_messages.load() % 1000 == 0) {
                    std::cout << "CU Trader received " << cu_messages.load() << " messages" << std::endl;
                }
            }
            return true;
        }
    );
    
    if (cu_registered) {
        data_bus->Subscribe(cu_client, {"CU2409"}, {});
        std::cout << "CU交易员客户端已注册并订阅CU2409" << std::endl;
    }
    
    // 注册AL合约专用客户端
    std::string al_client = "al_trader";
    bool al_registered = data_bus->RegisterClient(
        al_client,
        "trader",
        [&](const MarketDataWrapper& data) -> bool {
            al_messages.fetch_add(1);
            if (al_messages.load() % 500 == 0) {
                std::cout << "AL Trader received " << al_messages.load() << " messages" << std::endl;
            }
            return true;
        }
    );
    
    if (al_registered) {
        data_bus->Subscribe(al_client, {"AL2409"}, {});
        std::cout << "AL交易员客户端已注册并订阅AL2409" << std::endl;
    }
    
    // 注册全市场监控客户端
    std::string monitor_client = "market_monitor";
    bool monitor_registered = data_bus->RegisterClient(
        monitor_client,
        "monitor",
        [&](const MarketDataWrapper& data) -> bool {
            all_messages.fetch_add(1);
            return true;
        }
    );
    
    if (monitor_registered) {
        data_bus->Subscribe(monitor_client, {"CU2409", "AL2409"}, {"SHFE"});
        std::cout << "市场监控客户端已注册并订阅所有SHFE合约" << std::endl;
    }
    
    // 注册高成交量过滤客户端
    std::string hv_client = "high_volume_filter";
    bool hv_registered = data_bus->RegisterClient(
        hv_client,
        "filter",
        [&](const MarketDataWrapper& data) -> bool {
            high_volume_messages.fetch_add(1);
            return true;
        }
    );
    
    if (hv_registered) {
        data_bus->Subscribe(hv_client, {"CU2409", "AL2409"}, {});
        
        // 添加高成交量路由规则
        RoutingRule hv_rule;
        hv_rule.rule_id = "high_volume_filter";
        hv_rule.symbol_pattern = "*";
        hv_rule.exchange_pattern = "*";
        hv_rule.client_pattern = "high_volume_*";
        hv_rule.min_volume = 5000;  // 最小成交量5000
        hv_rule.priority = 100;
        hv_rule.enabled = true;
        
        data_bus->AddRoutingRule(hv_rule);
        std::cout << "高成交量过滤客户端已注册，只接收成交量>5000的数据" << std::endl;
    }
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    std::cout << "\n开始数据生产和处理..." << std::endl;
    
    // 启动控制标志
    std::atomic<bool> running{true};
    
    // 启动统计监控线程
    std::thread stats_thread(StatisticsMonitor, data_bus.get(), std::ref(running));
    
    // 启动数据生产者线程
    std::vector<std::thread> producer_threads;
    
    // CU合约生产者（高频）
    producer_threads.emplace_back(DataProducer, data_bus.get(), "CU2409", "SHFE", 10000, std::ref(running));
    
    // AL合约生产者（中频）
    producer_threads.emplace_back(DataProducer, data_bus.get(), "AL2409", "SHFE", 5000, std::ref(running));
    
    std::cout << "数据生产者已启动，将生成15000条测试数据" << std::endl;
    std::cout << "按Enter键停止演示..." << std::endl;
    
    // 等待用户输入或生产者完成
    std::thread input_thread([&running]() {
        std::cin.get();
        running = false;
    });
    
    // 等待生产者完成
    for (auto& thread : producer_threads) {
        thread.join();
    }
    
    // 等待一段时间让所有数据处理完成
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 停止运行
    running = false;
    
    // 等待统计线程结束
    if (stats_thread.joinable()) {
        stats_thread.join();
    }
    
    if (input_thread.joinable()) {
        input_thread.join();
    }
    
    // 最终统计
    std::cout << "\n=== 最终统计结果 ===" << std::endl;
    std::cout << "CU交易员接收消息: " << cu_messages.load() << std::endl;
    std::cout << "AL交易员接收消息: " << al_messages.load() << std::endl;
    std::cout << "市场监控接收消息: " << all_messages.load() << std::endl;
    std::cout << "高成交量过滤接收消息: " << high_volume_messages.load() << std::endl;
    
    auto final_stats = data_bus->GetStatistics();
    std::cout << "总处理消息数: " << final_stats.total_messages_processed.load() << std::endl;
    std::cout << "总发送消息数: " << final_stats.total_messages_sent.load() << std::endl;
    std::cout << "消息丢弃数: " << final_stats.total_messages_dropped.load() << std::endl;
    std::cout << "处理成功率: " << final_stats.GetProcessingRate() * 100.0 << "%" << std::endl;
    
    // 健康检查
    auto health = data_bus->GetHealthStatus();
    std::cout << "系统健康状态: " << (health.overall_healthy ? "健康" : "异常") << std::endl;
    if (!health.overall_healthy) {
        std::cout << "错误信息: " << health.error_message << std::endl;
    }
    
    // 清理客户端
    if (cu_registered) data_bus->UnregisterClient(cu_client);
    if (al_registered) data_bus->UnregisterClient(al_client);
    if (monitor_registered) data_bus->UnregisterClient(monitor_client);
    if (hv_registered) {
        data_bus->RemoveRoutingRule("high_volume_filter");
        data_bus->UnregisterClient(hv_client);
    }
    
    // 停止数据总线
    data_bus->Stop();
    
    std::cout << "\n数据总线演示完成！" << std::endl;
    std::cout << "本演示展示了以下功能：" << std::endl;
    std::cout << "1. 高性能无锁队列数据传输" << std::endl;
    std::cout << "2. 智能数据路由和订阅管理" << std::endl;
    std::cout << "3. 背压控制和流量管理" << std::endl;
    std::cout << "4. 实时性能监控和统计" << std::endl;
    std::cout << "5. 多客户端并发处理" << std::endl;
    std::cout << "6. 路由规则和数据过滤" << std::endl;
    
    return 0;
}