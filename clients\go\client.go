package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"math/rand"
	"sync"
	"sync/atomic"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/status"

	pb "github.com/financial-data-service/proto" // 替换为实际的proto包路径
)

// LoadBalancer 客户端负载均衡器
type LoadBalancer struct {
	servers     []string
	serverStats map[string]*ServerStats
	mutex       sync.RWMutex
}

// ServerStats 服务器统计信息
type ServerStats struct {
	Latency   int64 // 微秒
	Errors    int32
	Healthy   int32 // 0=不健康, 1=健康
	LastSeen  time.Time
}

// NewLoadBalancer 创建负载均衡器
func NewLoadBalancer(servers []string) *LoadBalancer {
	lb := &LoadBalancer{
		servers:     servers,
		serverStats: make(map[string]*ServerStats),
	}
	
	for _, server := range servers {
		lb.serverStats[server] = &ServerStats{
			Latency:  0,
			Errors:   0,
			Healthy:  1,
			LastSeen: time.Now(),
		}
	}
	
	return lb
}

// GetBestServer 选择最佳服务器
func (lb *LoadBalancer) GetBestServer() string {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()
	
	var healthyServers []string
	for server, stats := range lb.serverStats {
		if atomic.LoadInt32(&stats.Healthy) == 1 {
			healthyServers = append(healthyServers, server)
		}
	}
	
	if len(healthyServers) == 0 {
		// 重置所有服务器状态
		for _, stats := range lb.serverStats {
			atomic.StoreInt32(&stats.Healthy, 1)
			atomic.StoreInt32(&stats.Errors, 0)
		}
		healthyServers = lb.servers
	}
	
	// 选择延迟最低的服务器
	bestServer := healthyServers[0]
	bestLatency := atomic.LoadInt64(&lb.serverStats[bestServer].Latency)
	
	for _, server := range healthyServers[1:] {
		latency := atomic.LoadInt64(&lb.serverStats[server].Latency)
		if latency < bestLatency {
			bestServer = server
			bestLatency = latency
		}
	}
	
	return bestServer
}

// ReportError 报告服务器错误
func (lb *LoadBalancer) ReportError(server string) {
	lb.mutex.RLock()
	stats, exists := lb.serverStats[server]
	lb.mutex.RUnlock()
	
	if exists {
		errors := atomic.AddInt32(&stats.Errors, 1)
		if errors > 3 {
			atomic.StoreInt32(&stats.Healthy, 0)
			log.Printf("Server %s marked as unhealthy", server)
		}
	}
}

// UpdateLatency 更新服务器延迟
func (lb *LoadBalancer) UpdateLatency(server string, latency time.Duration) {
	lb.mutex.RLock()
	stats, exists := lb.serverStats[server]
	lb.mutex.RUnlock()
	
	if exists {
		atomic.StoreInt64(&stats.Latency, latency.Microseconds())
		stats.LastSeen = time.Now()
	}
}

// StreamFlowController 流量控制器
type StreamFlowController struct {
	bufferSize        int32
	pendingMessages   int32
	backpressureActive int32 // 0=非活跃, 1=活跃
	mutex             sync.Mutex
}

// NewStreamFlowController 创建流量控制器
func NewStreamFlowController(bufferSize int32) *StreamFlowController {
	return &StreamFlowController{
		bufferSize:        bufferSize,
		pendingMessages:   0,
		backpressureActive: 0,
	}
}

// CanSend 检查是否可以发送消息
func (fc *StreamFlowController) CanSend() bool {
	return atomic.LoadInt32(&fc.backpressureActive) == 0 && 
		   atomic.LoadInt32(&fc.pendingMessages) < fc.bufferSize
}

// OnMessageSent 消息发送时调用
func (fc *StreamFlowController) OnMessageSent() {
	pending := atomic.AddInt32(&fc.pendingMessages, 1)
	if pending >= int32(float32(fc.bufferSize)*0.8) {
		if atomic.CompareAndSwapInt32(&fc.backpressureActive, 0, 1) {
			log.Println("Backpressure activated")
		}
	}
}

// OnMessageProcessed 消息处理完成时调用
func (fc *StreamFlowController) OnMessageProcessed() {
	pending := atomic.LoadInt32(&fc.pendingMessages)
	if pending > 0 {
		atomic.AddInt32(&fc.pendingMessages, -1)
		if pending <= int32(float32(fc.bufferSize)*0.5) {
			if atomic.CompareAndSwapInt32(&fc.backpressureActive, 1, 0) {
				log.Println("Backpressure released")
			}
		}
	}
}

// FinancialDataClient gRPC客户端
type FinancialDataClient struct {
	loadBalancer *LoadBalancer
	connections  map[string]*grpc.ClientConn
	clients      map[string]pb.MarketDataServiceClient
	maxRetries   int
	mutex        sync.RWMutex
}

// NewFinancialDataClient 创建客户端
func NewFinancialDataClient(servers []string, maxRetries int) (*FinancialDataClient, error) {
	client := &FinancialDataClient{
		loadBalancer: NewLoadBalancer(servers),
		connections:  make(map[string]*grpc.ClientConn),
		clients:      make(map[string]pb.MarketDataServiceClient),
		maxRetries:   maxRetries,
	}
	
	if err := client.initializeConnections(); err != nil {
		return nil, err
	}
	
	return client, nil
}

// initializeConnections 初始化连接
func (c *FinancialDataClient) initializeConnections() error {
	for _, server := range c.loadBalancer.servers {
		conn, err := grpc.Dial(server, 
			grpc.WithTransportCredentials(insecure.NewCredentials()),
			grpc.WithKeepaliveParams(keepalive.ClientParameters{
				Time:                30 * time.Second,
				Timeout:             5 * time.Second,
				PermitWithoutStream: true,
			}),
			grpc.WithDefaultCallOptions(
				grpc.MaxCallRecvMsgSize(4*1024*1024),
				grpc.MaxCallSendMsgSize(4*1024*1024),
			),
		)
		
		if err != nil {
			log.Printf("Failed to connect to server %s: %v", server, err)
			c.loadBalancer.ReportError(server)
			continue
		}
		
		client := pb.NewMarketDataServiceClient(conn)
		
		c.mutex.Lock()
		c.connections[server] = conn
		c.clients[server] = client
		c.mutex.Unlock()
		
		log.Printf("Connected to server: %s", server)
	}
	
	return nil
}

// getClientWithRetry 获取可用的客户端，支持重试和故障转移
func (c *FinancialDataClient) getClientWithRetry() (pb.MarketDataServiceClient, string, error) {
	for attempt := 0; attempt < c.maxRetries; attempt++ {
		server := c.loadBalancer.GetBestServer()
		
		c.mutex.RLock()
		client, exists := c.clients[server]
		c.mutex.RUnlock()
		
		if exists {
			return client, server, nil
		}
		
		// 尝试重新连接
		if err := c.initializeConnections(); err == nil {
			c.mutex.RLock()
			client, exists := c.clients[server]
			c.mutex.RUnlock()
			
			if exists {
				return client, server, nil
			}
		}
		
		c.loadBalancer.ReportError(server)
		time.Sleep(time.Duration(100*(attempt+1)) * time.Millisecond)
	}
	
	return nil, "", fmt.Errorf("no healthy servers available")
}

// StreamTickData 订阅实时tick数据流
func (c *FinancialDataClient) StreamTickData(ctx context.Context, symbols []string, exchange string, 
	callback func(*pb.TickDataResponse), bufferSize int32) error {
	
	client, server, err := c.getClientWithRetry()
	if err != nil {
		return err
	}
	
	request := &pb.TickDataRequest{
		Symbols:    symbols,
		Exchange:   exchange,
		BufferSize: bufferSize,
	}
	
	flowController := NewStreamFlowController(bufferSize)
	
	stream, err := client.StreamTickData(ctx, request)
	if err != nil {
		c.loadBalancer.ReportError(server)
		return err
	}
	
	for {
		startTime := time.Now()
		
		response, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			c.loadBalancer.ReportError(server)
			return err
		}
		
		// 更新延迟统计
		latency := time.Since(startTime)
		c.loadBalancer.UpdateLatency(server, latency)
		
		// 流量控制
		if !flowController.CanSend() {
			log.Println("Flow control: dropping tick message")
			continue
		}
		
		flowController.OnMessageSent()
		
		// 处理数据
		go func(resp *pb.TickDataResponse) {
			defer flowController.OnMessageProcessed()
			callback(resp)
		}(response)
	}
	
	return nil
}

// StreamKlineData 订阅K线数据流
func (c *FinancialDataClient) StreamKlineData(ctx context.Context, symbols []string, exchange string, 
	period string, callback func(*pb.KlineDataResponse), bufferSize int32) error {
	
	client, server, err := c.getClientWithRetry()
	if err != nil {
		return err
	}
	
	request := &pb.KlineDataRequest{
		Symbols:    symbols,
		Exchange:   exchange,
		Period:     period,
		BufferSize: bufferSize,
	}
	
	flowController := NewStreamFlowController(bufferSize)
	
	stream, err := client.StreamKlineData(ctx, request)
	if err != nil {
		c.loadBalancer.ReportError(server)
		return err
	}
	
	for {
		startTime := time.Now()
		
		response, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			c.loadBalancer.ReportError(server)
			return err
		}
		
		latency := time.Since(startTime)
		c.loadBalancer.UpdateLatency(server, latency)
		
		if !flowController.CanSend() {
			log.Println("Flow control: dropping kline message")
			continue
		}
		
		flowController.OnMessageSent()
		
		go func(resp *pb.KlineDataResponse) {
			defer flowController.OnMessageProcessed()
			callback(resp)
		}(response)
	}
	
	return nil
}

// StreamLevel2Data 订阅Level2深度数据流
func (c *FinancialDataClient) StreamLevel2Data(ctx context.Context, symbols []string, exchange string, 
	depth int32, callback func(*pb.Level2DataResponse), bufferSize int32) error {
	
	client, server, err := c.getClientWithRetry()
	if err != nil {
		return err
	}
	
	request := &pb.Level2DataRequest{
		Symbols:    symbols,
		Exchange:   exchange,
		Depth:      depth,
		BufferSize: bufferSize,
	}
	
	flowController := NewStreamFlowController(bufferSize)
	
	stream, err := client.StreamLevel2Data(ctx, request)
	if err != nil {
		c.loadBalancer.ReportError(server)
		return err
	}
	
	for {
		startTime := time.Now()
		
		response, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			c.loadBalancer.ReportError(server)
			return err
		}
		
		latency := time.Since(startTime)
		c.loadBalancer.UpdateLatency(server, latency)
		
		if !flowController.CanSend() {
			log.Println("Flow control: dropping level2 message")
			continue
		}
		
		flowController.OnMessageSent()
		
		go func(resp *pb.Level2DataResponse) {
			defer flowController.OnMessageProcessed()
			callback(resp)
		}(response)
	}
	
	return nil
}

// GetHistoricalTickData 获取历史tick数据
func (c *FinancialDataClient) GetHistoricalTickData(ctx context.Context, symbol string, exchange string,
	startTimestamp int64, endTimestamp int64, callback func(*pb.TickDataResponse), limit int32) error {
	
	client, server, err := c.getClientWithRetry()
	if err != nil {
		return err
	}
	
	request := &pb.HistoricalTickDataRequest{
		Symbol:         symbol,
		Exchange:       exchange,
		StartTimestamp: startTimestamp,
		EndTimestamp:   endTimestamp,
		Limit:          limit,
	}
	
	stream, err := client.GetHistoricalTickData(ctx, request)
	if err != nil {
		c.loadBalancer.ReportError(server)
		return err
	}
	
	for {
		response, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			c.loadBalancer.ReportError(server)
			return err
		}
		
		callback(response)
	}
	
	return nil
}

// HealthCheck 健康检查
func (c *FinancialDataClient) HealthCheck(ctx context.Context) (bool, error) {
	client, server, err := c.getClientWithRetry()
	if err != nil {
		return false, err
	}
	
	request := &pb.HealthCheckRequest{
		Service: "MarketDataService",
	}
	
	response, err := client.HealthCheck(ctx, request)
	if err != nil {
		c.loadBalancer.ReportError(server)
		return false, err
	}
	
	return response.Status == pb.HealthCheckResponse_SERVING, nil
}

// Close 关闭客户端连接
func (c *FinancialDataClient) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	for server, conn := range c.connections {
		if err := conn.Close(); err != nil {
			log.Printf("Error closing connection to %s: %v", server, err)
		}
	}
	
	return nil
}

// 使用示例
func main() {
	// 服务器列表
	servers := []string{
		"localhost:50051",
		"localhost:50052", 
		"localhost:50053",
	}
	
	// 创建客户端
	client, err := NewFinancialDataClient(servers, 3)
	if err != nil {
		log.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()
	
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	// 健康检查
	if healthy, err := client.HealthCheck(ctx); err == nil && healthy {
		log.Println("Service is healthy")
	}
	
	// 定义回调函数
	tickCallback := func(response *pb.TickDataResponse) {
		for _, tick := range response.Ticks {
			fmt.Printf("Tick: %s @ %.2f vol:%d\n", tick.Symbol, tick.LastPrice, tick.Volume)
		}
	}
	
	klineCallback := func(response *pb.KlineDataResponse) {
		for _, kline := range response.Klines {
			fmt.Printf("Kline: %s %s O:%.2f H:%.2f L:%.2f C:%.2f\n", 
				kline.Symbol, kline.Period, kline.Open, kline.High, kline.Low, kline.Close)
		}
	}
	
	level2Callback := func(response *pb.Level2DataResponse) {
		for _, level2 := range response.Level2Data {
			fmt.Printf("Level2: %s bids:%d asks:%d\n", 
				level2.Symbol, len(level2.Bids), len(level2.Asks))
		}
	}
	
	// 启动数据流
	go func() {
		if err := client.StreamTickData(ctx, []string{"AAPL", "GOOGL"}, "NASDAQ", tickCallback, 1000); err != nil {
			log.Printf("Tick stream error: %v", err)
		}
	}()
	
	go func() {
		if err := client.StreamKlineData(ctx, []string{"AAPL"}, "NASDAQ", "1m", klineCallback, 500); err != nil {
			log.Printf("Kline stream error: %v", err)
		}
	}()
	
	go func() {
		if err := client.StreamLevel2Data(ctx, []string{"AAPL"}, "NASDAQ", 10, level2Callback, 200); err != nil {
			log.Printf("Level2 stream error: %v", err)
		}
	}()
	
	// 等待
	log.Println("Streaming data for 30 seconds...")
	<-ctx.Done()
	log.Println("Client finished")
}