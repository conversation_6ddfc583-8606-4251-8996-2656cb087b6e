#!/usr/bin/env python3
"""
性能压力测试工具
测试高并发数据采集性能，验证存储系统吞吐量，测试查询响应时间，生成性能测试报告
"""

import asyncio
import json
import logging
import multiprocessing
import os
import psutil
import random
import statistics
import sys
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.collectors.pytdx_collector import PytdxCollector
from src.collectors.historical_data_archiver import HistoricalDataArchiver
from src.storage.unified_data_access import UnifiedDataAccessInterface
from src.storage.storage_layer_selector import StorageLayerSelector
from src.config.config_manager_python import ConfigManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.metrics = {
            "throughput": [],
            "latency": [],
            "cpu_usage": [],
            "memory_usage": [],
            "disk_io": [],
            "network_io": [],
            "error_rate": [],
            "concurrent_connections": []
        }
        self.start_time = None
        self.end_time = None
        
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        
    def stop_monitoring(self):
        """停止监控"""
        self.end_time = time.time()
        
    def record_throughput(self, operations_per_second: float):
        """记录吞吐量"""
        self.metrics["throughput"].append({
            "timestamp": time.time(),
            "value": operations_per_second
        })
        
    def record_latency(self, latency_ms: float):
        """记录延迟"""
        self.metrics["latency"].append({
            "timestamp": time.time(),
            "value": latency_ms
        })
        
    def record_system_metrics(self):
        """记录系统指标"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk_io = psutil.disk_io_counters()
        network_io = psutil.net_io_counters()
        
        timestamp = time.time()
        
        self.metrics["cpu_usage"].append({
            "timestamp": timestamp,
            "value": cpu_percent
        })
        
        self.metrics["memory_usage"].append({
            "timestamp": timestamp,
            "value": memory.percent
        })
        
        if disk_io:
            self.metrics["disk_io"].append({
                "timestamp": timestamp,
                "read_bytes": disk_io.read_bytes,
                "write_bytes": disk_io.write_bytes
            })
            
        if network_io:
            self.metrics["network_io"].append({
                "timestamp": timestamp,
                "bytes_sent": network_io.bytes_sent,
                "bytes_recv": network_io.bytes_recv
            })
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        duration = (self.end_time - self.start_time) if self.end_time and self.start_time else 0
        
        summary = {
            "duration_seconds": duration,
            "throughput": self._calculate_stats([m["value"] for m in self.metrics["throughput"]]),
            "latency": self._calculate_stats([m["value"] for m in self.metrics["latency"]]),
            "cpu_usage": self._calculate_stats([m["value"] for m in self.metrics["cpu_usage"]]),
            "memory_usage": self._calculate_stats([m["value"] for m in self.metrics["memory_usage"]]),
            "total_operations": len(self.metrics["throughput"])
        }
        
        return summary
    
    def _calculate_stats(self, values: List[float]) -> Dict[str, float]:
        """计算统计数据"""
        if not values:
            return {"min": 0, "max": 0, "avg": 0, "p50": 0, "p95": 0, "p99": 0}
        
        return {
            "min": min(values),
            "max": max(values),
            "avg": statistics.mean(values),
            "p50": np.percentile(values, 50),
            "p95": np.percentile(values, 95),
            "p99": np.percentile(values, 99)
        }


class PerformanceStressTester:
    """性能压力测试器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics = PerformanceMetrics()
        
        # 初始化组件
        self.pytdx_collector = PytdxCollector(config.get("pytdx", {}))
        self.archiver = HistoricalDataArchiver(config.get("archiver", {}))
        self.unified_access = UnifiedDataAccessInterface(config.get("storage", {}))
        self.storage_selector = StorageLayerSelector(config.get("storage", {}))
        
        # 测试参数
        self.test_symbols = ["000001", "000002", "600000", "600036", "000858"]
        self.concurrent_levels = [1, 5, 10, 20, 50]
        self.data_volumes = [100, 1000, 10000, 50000]
        
        logger.info("Performance stress tester initialized")
    
    async def run_comprehensive_stress_test(self) -> Dict[str, Any]:
        """运行综合压力测试"""
        logger.info("Starting comprehensive performance stress test")
        
        self.metrics.start_monitoring()
        
        # 启动系统监控
        monitor_task = asyncio.create_task(self._monitor_system_resources())
        
        try:
            # 1. 数据采集性能测试
            collection_results = await self._test_data_collection_performance()
            
            # 2. 存储系统吞吐量测试
            storage_results = await self._test_storage_throughput()
            
            # 3. 查询响应时间测试
            query_results = await self._test_query_performance()
            
            # 4. 并发访问压力测试
            concurrent_results = await self._test_concurrent_access()
            
            # 5. 大数据量处理测试
            volume_results = await self._test_large_volume_processing()
            
            # 6. 内存压力测试
            memory_results = await self._test_memory_pressure()
            
            # 7. 网络延迟模拟测试
            network_results = await self._test_network_latency()
            
        finally:
            # 停止监控
            monitor_task.cancel()
            self.metrics.stop_monitoring()
        
        # 汇总结果
        comprehensive_results = {
            "timestamp": datetime.now().isoformat(),
            "test_duration": self.metrics.end_time - self.metrics.start_time,
            "system_info": self._get_system_info(),
            "test_results": {
                "data_collection": collection_results,
                "storage_throughput": storage_results,
                "query_performance": query_results,
                "concurrent_access": concurrent_results,
                "large_volume": volume_results,
                "memory_pressure": memory_results,
                "network_latency": network_results
            },
            "performance_summary": self.metrics.get_summary(),
            "recommendations": self._generate_performance_recommendations()
        }
        
        # 生成详细报告
        self._generate_performance_report(comprehensive_results)
        
        logger.info(f"Comprehensive stress test completed in {comprehensive_results['test_duration']:.2f}s")
        
        return comprehensive_results
    
    async def _test_data_collection_performance(self) -> Dict[str, Any]:
        """测试数据采集性能"""
        logger.info("Testing data collection performance")
        
        results = {
            "single_symbol": {},
            "multi_symbol": {},
            "batch_collection": {}
        }
        
        # 1. 单股票采集性能
        symbol = self.test_symbols[0]
        start_time = time.time()
        
        try:
            k_data = self.pytdx_collector.get_k_data(
                symbol,
                start_date="2024-01-01",
                end_date="2024-01-31"
            )
            
            collection_time = time.time() - start_time
            
            if k_data is not None and len(k_data) > 0:
                throughput = len(k_data) / collection_time
                self.metrics.record_throughput(throughput)
                self.metrics.record_latency(collection_time * 1000)
                
                results["single_symbol"] = {
                    "success": True,
                    "records_collected": len(k_data),
                    "time_seconds": collection_time,
                    "throughput_records_per_second": throughput
                }
            else:
                results["single_symbol"] = {
                    "success": False,
                    "error": "No data collected"
                }
                
        except Exception as e:
            results["single_symbol"] = {
                "success": False,
                "error": str(e)
            }
        
        # 2. 多股票并发采集性能
        start_time = time.time()
        
        try:
            tasks = []
            for symbol in self.test_symbols[:3]:  # 测试3个股票
                task = asyncio.create_task(
                    self._collect_symbol_data_async(symbol)
                )
                tasks.append(task)
            
            multi_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            collection_time = time.time() - start_time
            
            successful_collections = [r for r in multi_results if isinstance(r, dict) and r.get("success")]
            total_records = sum(r.get("records", 0) for r in successful_collections)
            
            if successful_collections:
                throughput = total_records / collection_time
                self.metrics.record_throughput(throughput)
                
                results["multi_symbol"] = {
                    "success": True,
                    "symbols_tested": len(self.test_symbols[:3]),
                    "successful_collections": len(successful_collections),
                    "total_records": total_records,
                    "time_seconds": collection_time,
                    "throughput_records_per_second": throughput
                }
            else:
                results["multi_symbol"] = {
                    "success": False,
                    "error": "No successful collections"
                }
                
        except Exception as e:
            results["multi_symbol"] = {
                "success": False,
                "error": str(e)
            }
        
        # 3. 批量采集性能测试
        batch_sizes = [10, 50, 100]
        batch_results = []
        
        for batch_size in batch_sizes:
            start_time = time.time()
            
            try:
                # 模拟批量数据采集
                batch_data = []
                for i in range(batch_size):
                    # 创建模拟数据
                    data_point = {
                        "symbol": f"TEST{i:03d}",
                        "timestamp": int(time.time() * 1000),
                        "price": 10.0 + random.random(),
                        "volume": random.randint(1000, 10000)
                    }
                    batch_data.append(data_point)
                
                # 转换为DataFrame并归档
                df = pd.DataFrame(batch_data)
                archive_result = await self.archiver.archive_k_data("BATCH_TEST", df)
                
                batch_time = time.time() - start_time
                
                if archive_result:
                    throughput = batch_size / batch_time
                    self.metrics.record_throughput(throughput)
                    
                    batch_results.append({
                        "batch_size": batch_size,
                        "success": True,
                        "time_seconds": batch_time,
                        "throughput_records_per_second": throughput
                    })
                else:
                    batch_results.append({
                        "batch_size": batch_size,
                        "success": False,
                        "error": "Archive failed"
                    })
                    
            except Exception as e:
                batch_results.append({
                    "batch_size": batch_size,
                    "success": False,
                    "error": str(e)
                })
        
        results["batch_collection"] = batch_results
        
        return results
    
    async def _test_storage_throughput(self) -> Dict[str, Any]:
        """测试存储系统吞吐量"""
        logger.info("Testing storage system throughput")
        
        results = {
            "write_throughput": {},
            "read_throughput": {},
            "mixed_workload": {}
        }
        
        # 1. 写入吞吐量测试
        write_volumes = [1000, 5000, 10000]
        write_results = []
        
        for volume in write_volumes:
            # 创建测试数据
            test_data = []
            base_timestamp = int(time.time() * 1_000_000_000)
            
            for i in range(volume):
                data_point = {
                    "symbol": "WRITE_TEST",
                    "timestamp": base_timestamp + i * 1_000_000_000,
                    "price": 10.0 + i * 0.01,
                    "volume": 1000 + i
                }
                test_data.append(data_point)
            
            df = pd.DataFrame(test_data)
            
            # 测试写入性能
            start_time = time.time()
            
            try:
                archive_result = await self.archiver.archive_k_data("WRITE_TEST", df)
                write_time = time.time() - start_time
                
                if archive_result:
                    throughput = volume / write_time
                    self.metrics.record_throughput(throughput)
                    self.metrics.record_latency(write_time * 1000)
                    
                    write_results.append({
                        "volume": volume,
                        "success": True,
                        "time_seconds": write_time,
                        "throughput_records_per_second": throughput,
                        "latency_ms": write_time * 1000
                    })
                else:
                    write_results.append({
                        "volume": volume,
                        "success": False,
                        "error": "Write failed"
                    })
                    
            except Exception as e:
                write_results.append({
                    "volume": volume,
                    "success": False,
                    "error": str(e)
                })
        
        results["write_throughput"] = write_results
        
        # 2. 读取吞吐量测试
        read_results = []
        
        for volume in [100, 500, 1000]:
            query_request = {
                "symbol": "WRITE_TEST",
                "data_type": "kline",
                "start_timestamp": base_timestamp,
                "end_timestamp": base_timestamp + volume * 1_000_000_000,
                "limit": volume
            }
            
            start_time = time.time()
            
            try:
                query_result = await self.unified_access.query_data(query_request)
                read_time = time.time() - start_time
                
                if query_result.get("success", False):
                    records_read = len(query_result.get("data", []))
                    throughput = records_read / read_time if read_time > 0 else 0
                    self.metrics.record_throughput(throughput)
                    self.metrics.record_latency(read_time * 1000)
                    
                    read_results.append({
                        "requested_volume": volume,
                        "actual_records": records_read,
                        "success": True,
                        "time_seconds": read_time,
                        "throughput_records_per_second": throughput,
                        "latency_ms": read_time * 1000
                    })
                else:
                    read_results.append({
                        "requested_volume": volume,
                        "success": False,
                        "error": query_result.get("error", "Unknown error")
                    })
                    
            except Exception as e:
                read_results.append({
                    "requested_volume": volume,
                    "success": False,
                    "error": str(e)
                })
        
        results["read_throughput"] = read_results
        
        # 3. 混合工作负载测试
        try:
            mixed_start_time = time.time()
            
            # 并发执行读写操作
            write_task = asyncio.create_task(
                self._perform_write_operations(100)
            )
            read_task = asyncio.create_task(
                self._perform_read_operations(50)
            )
            
            write_result, read_result = await asyncio.gather(
                write_task, read_task, return_exceptions=True
            )
            
            mixed_time = time.time() - mixed_start_time
            
            results["mixed_workload"] = {
                "success": not isinstance(write_result, Exception) and not isinstance(read_result, Exception),
                "time_seconds": mixed_time,
                "write_result": write_result if not isinstance(write_result, Exception) else str(write_result),
                "read_result": read_result if not isinstance(read_result, Exception) else str(read_result)
            }
            
        except Exception as e:
            results["mixed_workload"] = {
                "success": False,
                "error": str(e)
            }
        
        return results
    
    async def _test_query_performance(self) -> Dict[str, Any]:
        """测试查询响应时间"""
        logger.info("Testing query performance")
        
        results = {
            "simple_queries": [],
            "complex_queries": [],
            "range_queries": [],
            "aggregation_queries": []
        }
        
        # 1. 简单查询测试
        simple_query_types = [
            {"limit": 10, "description": "Small result set"},
            {"limit": 100, "description": "Medium result set"},
            {"limit": 1000, "description": "Large result set"}
        ]
        
        for query_type in simple_query_types:
            start_time = time.time()
            
            try:
                query_request = {
                    "symbol": "WRITE_TEST",
                    "data_type": "kline",
                    "start_timestamp": int(time.time() * 1000) - 86400000,  # 1天前
                    "end_timestamp": int(time.time() * 1000),
                    "limit": query_type["limit"]
                }
                
                query_result = await self.unified_access.query_data(query_request)
                query_time = time.time() - start_time
                
                self.metrics.record_latency(query_time * 1000)
                
                results["simple_queries"].append({
                    "description": query_type["description"],
                    "limit": query_type["limit"],
                    "success": query_result.get("success", False),
                    "response_time_ms": query_time * 1000,
                    "records_returned": len(query_result.get("data", []))
                })
                
            except Exception as e:
                results["simple_queries"].append({
                    "description": query_type["description"],
                    "limit": query_type["limit"],
                    "success": False,
                    "error": str(e)
                })
        
        # 2. 复杂查询测试（多条件）
        complex_queries = [
            {
                "description": "Multi-symbol query",
                "symbols": ["WRITE_TEST", "TEST001", "TEST002"]
            },
            {
                "description": "Time range with filters",
                "symbol": "WRITE_TEST",
                "price_min": 10.0,
                "price_max": 15.0
            }
        ]
        
        for complex_query in complex_queries:
            start_time = time.time()
            
            try:
                # 模拟复杂查询（实际实现可能需要扩展查询接口）
                if "symbols" in complex_query:
                    # 多股票查询
                    tasks = []
                    for symbol in complex_query["symbols"]:
                        query_request = {
                            "symbol": symbol,
                            "data_type": "kline",
                            "start_timestamp": int(time.time() * 1000) - 86400000,
                            "end_timestamp": int(time.time() * 1000),
                            "limit": 100
                        }
                        tasks.append(self.unified_access.query_data(query_request))
                    
                    query_results = await asyncio.gather(*tasks, return_exceptions=True)
                    query_time = time.time() - start_time
                    
                    successful_queries = [r for r in query_results if not isinstance(r, Exception) and r.get("success")]
                    total_records = sum(len(r.get("data", [])) for r in successful_queries)
                    
                    results["complex_queries"].append({
                        "description": complex_query["description"],
                        "success": len(successful_queries) > 0,
                        "response_time_ms": query_time * 1000,
                        "symbols_queried": len(complex_query["symbols"]),
                        "successful_queries": len(successful_queries),
                        "total_records": total_records
                    })
                    
                else:
                    # 带过滤条件的查询
                    query_request = {
                        "symbol": complex_query["symbol"],
                        "data_type": "kline",
                        "start_timestamp": int(time.time() * 1000) - 86400000,
                        "end_timestamp": int(time.time() * 1000),
                        "limit": 1000
                    }
                    
                    query_result = await self.unified_access.query_data(query_request)
                    query_time = time.time() - start_time
                    
                    # 在客户端进行价格过滤（模拟复杂查询）
                    if query_result.get("success", False):
                        data = query_result.get("data", [])
                        filtered_data = [
                            record for record in data
                            if complex_query["price_min"] <= record.get("price", 0) <= complex_query["price_max"]
                        ]
                        
                        results["complex_queries"].append({
                            "description": complex_query["description"],
                            "success": True,
                            "response_time_ms": query_time * 1000,
                            "total_records": len(data),
                            "filtered_records": len(filtered_data)
                        })
                    else:
                        results["complex_queries"].append({
                            "description": complex_query["description"],
                            "success": False,
                            "error": query_result.get("error", "Query failed")
                        })
                
                self.metrics.record_latency(query_time * 1000)
                
            except Exception as e:
                results["complex_queries"].append({
                    "description": complex_query["description"],
                    "success": False,
                    "error": str(e)
                })
        
        return results
    
    async def _test_concurrent_access(self) -> Dict[str, Any]:
        """测试并发访问压力"""
        logger.info("Testing concurrent access pressure")
        
        results = {}
        
        for concurrent_level in self.concurrent_levels:
            logger.info(f"Testing with {concurrent_level} concurrent connections")
            
            start_time = time.time()
            
            try:
                # 创建并发任务
                tasks = []
                for i in range(concurrent_level):
                    task = asyncio.create_task(
                        self._simulate_concurrent_user(f"USER_{i}")
                    )
                    tasks.append(task)
                
                # 等待所有任务完成
                task_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                test_time = time.time() - start_time
                
                # 分析结果
                successful_tasks = [r for r in task_results if not isinstance(r, Exception) and r.get("success")]
                failed_tasks = [r for r in task_results if isinstance(r, Exception) or not r.get("success")]
                
                total_operations = sum(r.get("operations", 0) for r in successful_tasks)
                avg_response_time = statistics.mean([r.get("avg_response_time", 0) for r in successful_tasks]) if successful_tasks else 0
                
                success_rate = len(successful_tasks) / len(task_results) * 100
                throughput = total_operations / test_time if test_time > 0 else 0
                
                self.metrics.record_throughput(throughput)
                
                results[f"concurrent_{concurrent_level}"] = {
                    "concurrent_level": concurrent_level,
                    "success": len(successful_tasks) > 0,
                    "total_tasks": len(task_results),
                    "successful_tasks": len(successful_tasks),
                    "failed_tasks": len(failed_tasks),
                    "success_rate_percent": success_rate,
                    "total_operations": total_operations,
                    "test_time_seconds": test_time,
                    "throughput_ops_per_second": throughput,
                    "avg_response_time_ms": avg_response_time
                }
                
            except Exception as e:
                results[f"concurrent_{concurrent_level}"] = {
                    "concurrent_level": concurrent_level,
                    "success": False,
                    "error": str(e)
                }
        
        return results
    
    async def _test_large_volume_processing(self) -> Dict[str, Any]:
        """测试大数据量处理"""
        logger.info("Testing large volume data processing")
        
        results = {}
        
        for volume in self.data_volumes:
            logger.info(f"Testing with {volume} records")
            
            start_time = time.time()
            
            try:
                # 创建大量测试数据
                large_dataset = []
                base_timestamp = int(time.time() * 1_000_000_000)
                
                for i in range(volume):
                    data_point = {
                        "symbol": f"VOLUME_TEST_{volume}",
                        "timestamp": base_timestamp + i * 1_000_000_000,
                        "price": 10.0 + (i % 1000) * 0.01,
                        "volume": 1000 + i,
                        "high": 10.0 + (i % 1000) * 0.01 + 0.05,
                        "low": 10.0 + (i % 1000) * 0.01 - 0.05,
                        "open": 10.0 + (i % 1000) * 0.01,
                        "close": 10.0 + (i % 1000) * 0.01
                    }
                    large_dataset.append(data_point)
                
                df = pd.DataFrame(large_dataset)
                
                # 测试存储性能
                storage_start = time.time()
                archive_result = await self.archiver.archive_k_data(f"VOLUME_TEST_{volume}", df)
                storage_time = time.time() - storage_start
                
                if not archive_result:
                    results[f"volume_{volume}"] = {
                        "volume": volume,
                        "success": False,
                        "error": "Storage failed"
                    }
                    continue
                
                # 测试查询性能
                query_start = time.time()
                query_request = {
                    "symbol": f"VOLUME_TEST_{volume}",
                    "data_type": "kline",
                    "start_timestamp": base_timestamp,
                    "end_timestamp": base_timestamp + volume * 1_000_000_000,
                    "limit": volume
                }
                
                query_result = await self.unified_access.query_data(query_request)
                query_time = time.time() - query_start
                
                total_time = time.time() - start_time
                
                if query_result.get("success", False):
                    retrieved_records = len(query_result.get("data", []))
                    
                    storage_throughput = volume / storage_time if storage_time > 0 else 0
                    query_throughput = retrieved_records / query_time if query_time > 0 else 0
                    
                    self.metrics.record_throughput(storage_throughput)
                    self.metrics.record_latency(storage_time * 1000)
                    
                    results[f"volume_{volume}"] = {
                        "volume": volume,
                        "success": True,
                        "storage_time_seconds": storage_time,
                        "query_time_seconds": query_time,
                        "total_time_seconds": total_time,
                        "storage_throughput_records_per_second": storage_throughput,
                        "query_throughput_records_per_second": query_throughput,
                        "retrieved_records": retrieved_records,
                        "data_integrity": retrieved_records == volume
                    }
                else:
                    results[f"volume_{volume}"] = {
                        "volume": volume,
                        "success": False,
                        "error": f"Query failed: {query_result.get('error', 'Unknown error')}"
                    }
                    
            except Exception as e:
                results[f"volume_{volume}"] = {
                    "volume": volume,
                    "success": False,
                    "error": str(e)
                }
        
        return results
    
    async def _test_memory_pressure(self) -> Dict[str, Any]:
        """测试内存压力"""
        logger.info("Testing memory pressure")
        
        results = {
            "initial_memory": psutil.virtual_memory().percent,
            "memory_tests": []
        }
        
        # 逐步增加内存使用量
        memory_loads = [10, 50, 100, 200]  # MB
        
        for memory_load in memory_loads:
            start_memory = psutil.virtual_memory().percent
            
            try:
                # 创建内存压力
                memory_data = []
                
                # 分配指定大小的内存
                for _ in range(memory_load):
                    # 每次分配1MB的数据
                    chunk = [random.random() for _ in range(250000)]  # 约1MB
                    memory_data.append(chunk)
                
                current_memory = psutil.virtual_memory().percent
                
                # 在内存压力下执行操作
                test_start = time.time()
                
                # 执行一些数据操作
                test_data = pd.DataFrame([
                    {
                        "symbol": "MEMORY_TEST",
                        "timestamp": int(time.time() * 1000) + i,
                        "price": 10.0 + i * 0.01,
                        "volume": 1000 + i
                    }
                    for i in range(1000)
                ])
                
                archive_result = await self.archiver.archive_k_data("MEMORY_TEST", test_data)
                
                test_time = time.time() - test_start
                
                # 清理内存
                del memory_data
                
                final_memory = psutil.virtual_memory().percent
                
                results["memory_tests"].append({
                    "memory_load_mb": memory_load,
                    "start_memory_percent": start_memory,
                    "peak_memory_percent": current_memory,
                    "final_memory_percent": final_memory,
                    "operation_success": archive_result,
                    "operation_time_seconds": test_time,
                    "memory_impact": current_memory - start_memory
                })
                
            except Exception as e:
                results["memory_tests"].append({
                    "memory_load_mb": memory_load,
                    "success": False,
                    "error": str(e)
                })
        
        return results
    
    async def _test_network_latency(self) -> Dict[str, Any]:
        """测试网络延迟模拟"""
        logger.info("Testing network latency simulation")
        
        results = {
            "baseline": {},
            "simulated_latency": []
        }
        
        # 基线测试（无延迟）
        start_time = time.time()
        
        try:
            query_request = {
                "symbol": "WRITE_TEST",
                "data_type": "kline",
                "start_timestamp": int(time.time() * 1000) - 3600000,
                "end_timestamp": int(time.time() * 1000),
                "limit": 100
            }
            
            baseline_result = await self.unified_access.query_data(query_request)
            baseline_time = time.time() - start_time
            
            results["baseline"] = {
                "success": baseline_result.get("success", False),
                "response_time_ms": baseline_time * 1000,
                "records_returned": len(baseline_result.get("data", []))
            }
            
        except Exception as e:
            results["baseline"] = {
                "success": False,
                "error": str(e)
            }
        
        # 模拟不同网络延迟
        latency_scenarios = [50, 100, 200, 500]  # 毫秒
        
        for latency_ms in latency_scenarios:
            start_time = time.time()
            
            try:
                # 模拟网络延迟
                await asyncio.sleep(latency_ms / 1000.0)
                
                query_result = await self.unified_access.query_data(query_request)
                
                # 再次模拟返回延迟
                await asyncio.sleep(latency_ms / 1000.0)
                
                total_time = time.time() - start_time
                
                results["simulated_latency"].append({
                    "simulated_latency_ms": latency_ms,
                    "success": query_result.get("success", False),
                    "total_response_time_ms": total_time * 1000,
                    "actual_latency_impact": (total_time * 1000) - baseline_time * 1000,
                    "records_returned": len(query_result.get("data", []))
                })
                
            except Exception as e:
                results["simulated_latency"].append({
                    "simulated_latency_ms": latency_ms,
                    "success": False,
                    "error": str(e)
                })
        
        return results
    
    async def _monitor_system_resources(self):
        """监控系统资源"""
        while True:
            try:
                self.metrics.record_system_metrics()
                await asyncio.sleep(5)  # 每5秒记录一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"System monitoring error: {e}")
    
    async def _collect_symbol_data_async(self, symbol: str) -> Dict[str, Any]:
        """异步采集股票数据"""
        try:
            k_data = self.pytdx_collector.get_k_data(
                symbol,
                start_date="2024-01-01",
                end_date="2024-01-31"
            )
            
            if k_data is not None and len(k_data) > 0:
                return {
                    "success": True,
                    "symbol": symbol,
                    "records": len(k_data)
                }
            else:
                return {
                    "success": False,
                    "symbol": symbol,
                    "error": "No data collected"
                }
                
        except Exception as e:
            return {
                "success": False,
                "symbol": symbol,
                "error": str(e)
            }
    
    async def _perform_write_operations(self, count: int) -> Dict[str, Any]:
        """执行写入操作"""
        successful_writes = 0
        
        for i in range(count):
            try:
                test_data = pd.DataFrame([{
                    "symbol": f"MIXED_WRITE_{i}",
                    "timestamp": int(time.time() * 1000) + i,
                    "price": 10.0 + i * 0.01,
                    "volume": 1000 + i
                }])
                
                result = await self.archiver.archive_k_data(f"MIXED_WRITE_{i}", test_data)
                if result:
                    successful_writes += 1
                    
            except Exception:
                pass
        
        return {
            "total_writes": count,
            "successful_writes": successful_writes,
            "success_rate": successful_writes / count * 100
        }
    
    async def _perform_read_operations(self, count: int) -> Dict[str, Any]:
        """执行读取操作"""
        successful_reads = 0
        
        for i in range(count):
            try:
                query_request = {
                    "symbol": "WRITE_TEST",
                    "data_type": "kline",
                    "start_timestamp": int(time.time() * 1000) - 3600000,
                    "end_timestamp": int(time.time() * 1000),
                    "limit": 10
                }
                
                result = await self.unified_access.query_data(query_request)
                if result.get("success", False):
                    successful_reads += 1
                    
            except Exception:
                pass
        
        return {
            "total_reads": count,
            "successful_reads": successful_reads,
            "success_rate": successful_reads / count * 100
        }
    
    async def _simulate_concurrent_user(self, user_id: str) -> Dict[str, Any]:
        """模拟并发用户操作"""
        operations = 0
        response_times = []
        
        try:
            # 每个用户执行10个操作
            for i in range(10):
                start_time = time.time()
                
                # 随机选择操作类型
                if random.random() < 0.7:  # 70%概率执行查询
                    query_request = {
                        "symbol": random.choice(self.test_symbols),
                        "data_type": "kline",
                        "start_timestamp": int(time.time() * 1000) - 3600000,
                        "end_timestamp": int(time.time() * 1000),
                        "limit": random.randint(10, 100)
                    }
                    
                    result = await self.unified_access.query_data(query_request)
                    success = result.get("success", False)
                    
                else:  # 30%概率执行写入
                    test_data = pd.DataFrame([{
                        "symbol": f"{user_id}_DATA",
                        "timestamp": int(time.time() * 1000) + i,
                        "price": 10.0 + random.random(),
                        "volume": random.randint(1000, 10000)
                    }])
                    
                    success = await self.archiver.archive_k_data(f"{user_id}_DATA", test_data)
                
                operation_time = time.time() - start_time
                response_times.append(operation_time * 1000)
                
                if success:
                    operations += 1
                
                # 模拟用户思考时间
                await asyncio.sleep(random.uniform(0.1, 0.5))
            
            return {
                "success": True,
                "user_id": user_id,
                "operations": operations,
                "avg_response_time": statistics.mean(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0
            }
            
        except Exception as e:
            return {
                "success": False,
                "user_id": user_id,
                "error": str(e)
            }
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "cpu_count": psutil.cpu_count(),
            "memory_total_gb": psutil.virtual_memory().total / (1024**3),
            "disk_total_gb": psutil.disk_usage('/').total / (1024**3),
            "python_version": sys.version,
            "platform": sys.platform
        }
    
    def _generate_performance_recommendations(self) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        summary = self.metrics.get_summary()
        
        # 基于性能指标生成建议
        if summary["latency"]["avg"] > 1000:  # 平均延迟超过1秒
            recommendations.append("考虑优化查询索引以减少响应时间")
        
        if summary["throughput"]["avg"] < 100:  # 吞吐量低于100 ops/s
            recommendations.append("考虑增加并发处理能力或优化批处理大小")
        
        if summary["cpu_usage"]["avg"] > 80:  # CPU使用率超过80%
            recommendations.append("考虑优化CPU密集型操作或增加计算资源")
        
        if summary["memory_usage"]["avg"] > 80:  # 内存使用率超过80%
            recommendations.append("考虑优化内存使用或增加内存容量")
        
        if not recommendations:
            recommendations.append("系统性能表现良好，建议继续监控关键指标")
        
        return recommendations
    
    def _generate_performance_report(self, results: Dict[str, Any]):
        """生成性能测试报告"""
        report_file = f"tests/integration/performance_stress_report_{int(time.time())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # 生成简化的控制台报告
        print("\n" + "="*80)
        print("PERFORMANCE STRESS TEST REPORT")
        print("="*80)
        print(f"Test Duration: {results['test_duration']:.2f} seconds")
        print(f"System: {results['system_info']['cpu_count']} CPUs, "
              f"{results['system_info']['memory_total_gb']:.1f}GB RAM")
        print()
        
        # 显示关键性能指标
        perf_summary = results["performance_summary"]
        print("Performance Summary:")
        print(f"  Average Throughput: {perf_summary['throughput']['avg']:.2f} ops/sec")
        print(f"  Average Latency: {perf_summary['latency']['avg']:.2f} ms")
        print(f"  Peak CPU Usage: {perf_summary['cpu_usage']['max']:.1f}%")
        print(f"  Peak Memory Usage: {perf_summary['memory_usage']['max']:.1f}%")
        print()
        
        # 显示测试结果摘要
        print("Test Results Summary:")
        for test_name, test_result in results["test_results"].items():
            if isinstance(test_result, dict):
                success_count = sum(1 for k, v in test_result.items() 
                                  if isinstance(v, dict) and v.get("success", False))
                total_count = len([k for k, v in test_result.items() 
                                 if isinstance(v, dict) and "success" in v])
                if total_count > 0:
                    print(f"  {test_name.replace('_', ' ').title()}: "
                          f"{success_count}/{total_count} passed")
        
        print()
        print("Recommendations:")
        for i, rec in enumerate(results["recommendations"], 1):
            print(f"  {i}. {rec}")
        
        print("="*80)
        
        logger.info(f"Performance test report saved to: {report_file}")


async def main():
    """主函数"""
    # 测试配置
    config = {
        "pytdx": {
            "servers": [
                {"host": "**************", "port": 7709},
                {"host": "************", "port": 7709}
            ],
            "timeout": 10,
            "retry_count": 3
        },
        "archiver": {
            "batch_size": 1000,
            "validation_enabled": True,
            "deduplication_enabled": True
        },
        "storage": {
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 1
            },
            "clickhouse": {
                "host": "localhost",
                "port": 8123,
                "database": "test_market_data"
            },
            "hot_storage_days": 7,
            "warm_storage_days": 730
        }
    }
    
    # 创建性能测试器并运行测试
    tester = PerformanceStressTester(config)
    
    try:
        results = await tester.run_comprehensive_stress_test()
        
        # 判断测试是否通过
        overall_success = True
        
        # 检查关键性能指标
        perf_summary = results["performance_summary"]
        if perf_summary["latency"]["avg"] > 5000:  # 平均延迟超过5秒
            overall_success = False
        
        if perf_summary["throughput"]["avg"] < 10:  # 吞吐量低于10 ops/s
            overall_success = False
        
        # 返回适当的退出码
        sys.exit(0 if overall_success else 1)
        
    except Exception as e:
        logger.error(f"Performance stress test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())