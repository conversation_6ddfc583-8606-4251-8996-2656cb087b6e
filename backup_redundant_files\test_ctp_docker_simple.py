#!/usr/bin/env python3
"""
CTP采集器Docker简化测试
专注于测试Docker镜像构建和基本功能
"""

import subprocess
import time
import sys
import os

def print_info(msg):
    print(f"[INFO] {msg}")

def print_success(msg):
    print(f"[SUCCESS] ✅ {msg}")

def print_error(msg):
    print(f"[ERROR] ❌ {msg}")

def print_warning(msg):
    print(f"[WARNING] ⚠️  {msg}")

def check_docker():
    """检查Docker环境"""
    print_info("检查Docker环境...")
    
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print_success(f"Docker: {result.stdout.strip()}")
            return True
        else:
            print_error("Docker不可用")
            return False
    except Exception as e:
        print_error(f"Docker检查失败: {e}")
        return False

def build_ctp_image():
    """构建CTP采集器镜像"""
    print_info("构建CTP采集器Docker镜像...")
    
    try:
        # 检查Dockerfile
        if not os.path.exists("docker/ctp-collector.Dockerfile"):
            print_error("Dockerfile不存在")
            return False
        
        # 检查requirements文件
        if not os.path.exists("requirements_ctp.txt"):
            print_warning("requirements_ctp.txt不存在，创建简化版本")
            with open("requirements_ctp.txt", "w") as f:
                f.write("redis>=4.5.0\naiohttp>=3.8.0\npsutil>=5.9.0\n")
        
        print_info("开始构建镜像...")
        start_time = time.time()
        
        # 构建命令
        cmd = [
            'docker', 'build',
            '-f', 'docker/ctp-collector.Dockerfile',
            '-t', 'ctp-data-collector:test',
            '.'
        ]
        
        # 执行构建
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 text=True, bufsize=1, universal_newlines=True)
        
        # 实时显示构建输出
        for line in process.stdout:
            if line.strip():
                print(f"  {line.strip()}")
        
        process.wait()
        build_time = time.time() - start_time
        
        if process.returncode == 0:
            print_success(f"镜像构建成功 ({build_time:.1f}秒)")
            return True
        else:
            print_error("镜像构建失败")
            return False
            
    except Exception as e:
        print_error(f"镜像构建异常: {e}")
        return False

def test_image():
    """测试镜像"""
    print_info("测试CTP采集器镜像...")
    
    try:
        # 运行容器进行基本测试
        cmd = [
            'docker', 'run', '--rm',
            '-e', 'PYTHONPATH=/app/src',
            'ctp-data-collector:test',
            'python3', '-c', 'print("CTP采集器镜像测试成功")'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print_success("镜像基本功能测试通过")
            print_info(f"输出: {result.stdout.strip()}")
            return True
        else:
            print_error("镜像测试失败")
            print_error(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print_error("镜像测试超时")
        return False
    except Exception as e:
        print_error(f"镜像测试异常: {e}")
        return False

def test_with_redis():
    """使用Redis测试"""
    print_info("启动Redis并测试CTP采集器...")
    
    try:
        # 启动Redis容器
        print_info("启动Redis容器...")
        redis_cmd = [
            'docker', 'run', '-d', '--name', 'test-redis',
            '-p', '6380:6379',  # 使用不同端口避免冲突
            'redis:7-alpine'
        ]
        
        subprocess.run(redis_cmd, capture_output=True, timeout=30)
        time.sleep(5)  # 等待Redis启动
        
        # 测试CTP采集器连接Redis
        print_info("测试CTP采集器连接Redis...")
        ctp_cmd = [
            'docker', 'run', '--rm', '--link', 'test-redis:redis',
            '-e', 'PYTHONPATH=/app/src',
            'ctp-data-collector:test',
            'python3', '-c', '''
import redis
try:
    r = redis.Redis(host="redis", port=6379, socket_timeout=5)
    r.ping()
    r.set("test_key", "CTP Docker测试成功")
    value = r.get("test_key")
    print(f"Redis连接测试成功: {value.decode()}")
except Exception as e:
    print(f"Redis连接失败: {e}")
    exit(1)
'''
        ]
        
        result = subprocess.run(ctp_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print_success("CTP采集器Redis连接测试通过")
            print_info(f"输出: {result.stdout.strip()}")
            success = True
        else:
            print_error("CTP采集器Redis连接测试失败")
            print_error(f"错误: {result.stderr}")
            success = False
        
        # 清理Redis容器
        subprocess.run(['docker', 'stop', 'test-redis'], capture_output=True)
        subprocess.run(['docker', 'rm', 'test-redis'], capture_output=True)
        
        return success
        
    except Exception as e:
        print_error(f"Redis测试异常: {e}")
        # 清理
        subprocess.run(['docker', 'stop', 'test-redis'], capture_output=True)
        subprocess.run(['docker', 'rm', 'test-redis'], capture_output=True)
        return False

def cleanup():
    """清理测试镜像"""
    print_info("清理测试镜像...")
    
    try:
        subprocess.run(['docker', 'rmi', 'ctp-data-collector:test'], 
                      capture_output=True)
        print_success("测试镜像清理完成")
    except:
        pass

def main():
    """主函数"""
    print("=" * 60)
    print("    CTP采集器 - Docker简化测试")
    print("=" * 60)
    print()
    
    tests = [
        ("Docker环境检查", check_docker),
        ("构建CTP镜像", build_ctp_image),
        ("镜像基本测试", test_image),
        ("Redis集成测试", test_with_redis)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if not result:
                print_error(f"{test_name} 失败")
                break  # 如果关键测试失败，停止后续测试
                
        except Exception as e:
            print_error(f"{test_name} 异常: {e}")
            results.append((test_name, False))
            break
    
    # 显示结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print_success("🎉 CTP采集器Docker测试完全成功！")
        print("\n✅ 镜像已准备就绪，可以用于:")
        print("  • 生产环境部署")
        print("  • Docker Compose集成")
        print("  • Kubernetes部署")
        print("  • CI/CD流水线")
    else:
        print_warning("部分测试失败，请检查配置")
    
    # 询问是否清理
    if input("\n是否清理测试镜像? (y/N): ").lower() == 'y':
        cleanup()
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        cleanup()
        sys.exit(1)
    except Exception as e:
        print_error(f"测试异常: {e}")
        cleanup()
        sys.exit(1)