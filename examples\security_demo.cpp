#include "../src/security/security_manager.h"
#include <iostream>
#include <fstream>
#include <json/json.h>

using namespace financial_data::security;

// 从JSON文件加载安全配置
SecurityConfig LoadSecurityConfig(const std::string& config_file) {
    SecurityConfig config;
    
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Cannot open config file: " << config_file << std::endl;
        return config;
    }
    
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(file, root)) {
        std::cerr << "Failed to parse config file" << std::endl;
        return config;
    }
    
    // TLS配置
    if (root.isMember("tls")) {
        const Json::Value& tls = root["tls"];
        config.tls.cert_file = tls.get("cert_file", "").asString();
        config.tls.key_file = tls.get("key_file", "").asString();
        config.tls.ca_file = tls.get("ca_file", "").asString();
        config.tls.cipher_suites = tls.get("cipher_suites", "").asString();
        config.tls.require_client_cert = tls.get("require_client_cert", true).asBool();
        config.tls.min_tls_version = tls.get("min_tls_version", 13).asInt();
    }
    
    // 加密配置
    if (root.isMember("encryption")) {
        const Json::Value& enc = root["encryption"];
        config.encryption.key_file = enc.get("key_file", "").asString();
        config.encryption.algorithm = enc.get("algorithm", "AES-256-GCM").asString();
        config.encryption.key_rotation_days = enc.get("key_rotation_days", 90).asInt();
        config.encryption.enable_disk_encryption = enc.get("enable_disk_encryption", true).asBool();
    }
    
    // JWT配置
    if (root.isMember("jwt")) {
        const Json::Value& jwt = root["jwt"];
        config.jwt.secret_key = jwt.get("secret_key", "").asString();
        config.jwt.issuer = jwt.get("issuer", "").asString();
        config.jwt.token_expiry = std::chrono::seconds(jwt.get("token_expiry_seconds", 3600).asInt());
        config.jwt.refresh_expiry = std::chrono::seconds(jwt.get("refresh_expiry_seconds", 604800).asInt());
        config.jwt.require_mfa = jwt.get("require_mfa", true).asBool();
        config.jwt.mfa_secret_key = jwt.get("mfa_secret_key", "").asString();
    }
    
    // RBAC配置
    if (root.isMember("rbac")) {
        const Json::Value& rbac = root["rbac"];
        config.rbac.roles_config_file = rbac.get("roles_config_file", "").asString();
        config.rbac.permissions_config_file = rbac.get("permissions_config_file", "").asString();
        config.rbac.enable_dynamic_permissions = rbac.get("enable_dynamic_permissions", true).asBool();
        config.rbac.cache_ttl = std::chrono::seconds(rbac.get("cache_ttl_seconds", 300).asInt());
    }
    
    // 审计配置
    if (root.isMember("audit")) {
        const Json::Value& audit = root["audit"];
        config.audit.log_file = audit.get("log_file", "").asString();
        config.audit.log_level = audit.get("log_level", "INFO").asString();
        config.audit.enable_real_time_alerts = audit.get("enable_real_time_alerts", true).asBool();
        config.audit.max_log_size_mb = audit.get("max_log_size_mb", 100).asInt();
        config.audit.max_log_files = audit.get("max_log_files", 10).asInt();
        
        const Json::Value& sensitive_ops = audit["sensitive_operations"];
        if (sensitive_ops.isArray()) {
            for (const auto& op : sensitive_ops) {
                config.audit.sensitive_operations.push_back(op.asString());
            }
        }
    }
    
    // 其他配置
    config.enable_rate_limiting = root.get("enable_rate_limiting", true).asBool();
    config.max_requests_per_minute = root.get("max_requests_per_minute", 1000).asInt();
    config.enable_ip_whitelist = root.get("enable_ip_whitelist", false).asBool();
    
    const Json::Value& allowed_ips = root["allowed_ips"];
    if (allowed_ips.isArray()) {
        for (const auto& ip : allowed_ips) {
            config.allowed_ips.push_back(ip.asString());
        }
    }
    
    return config;
}

void DemoAuthentication(SecurityManager& security_manager) {
    std::cout << "\n=== 认证演示 ===" << std::endl;
    
    // 测试用户登录
    auto auth_result = security_manager.AuthenticateUser(
        "admin", "admin123", "*************", "SecurityDemo/1.0");
    
    if (auth_result.success) {
        std::cout << "用户认证成功!" << std::endl;
        std::cout << "用户ID: " << auth_result.user_id << std::endl;
        std::cout << "会话ID: " << auth_result.session_id << std::endl;
        std::cout << "用户角色: ";
        for (const auto& role : auth_result.roles) {
            std::cout << role << " ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "用户认证失败: " << auth_result.error_message << std::endl;
    }
    
    // 测试错误密码
    auto failed_auth = security_manager.AuthenticateUser(
        "admin", "wrong_password", "*************", "SecurityDemo/1.0");
    
    if (!failed_auth.success) {
        std::cout << "预期的认证失败: " << failed_auth.error_message << std::endl;
    }
}

void DemoAuthorization(SecurityManager& security_manager) {
    std::cout << "\n=== 授权演示 ===" << std::endl;
    
    std::string user_id = "admin";
    
    // 测试不同的权限检查
    struct PermissionTest {
        std::string resource;
        std::string action;
        std::string description;
    };
    
    std::vector<PermissionTest> tests = {
        {"market_data", "read", "读取市场数据"},
        {"market_data", "write", "写入市场数据"},
        {"historical_data", "export", "导出历史数据"},
        {"user_management", "manage_users", "管理用户"},
        {"system_config", "system_config", "系统配置"},
        {"audit_logs", "view_audit", "查看审计日志"}
    };
    
    for (const auto& test : tests) {
        bool authorized = security_manager.AuthorizeAction(user_id, test.resource, test.action);
        std::cout << test.description << ": " << (authorized ? "允许" : "拒绝") << std::endl;
    }
}

void DemoEncryption(SecurityManager& security_manager) {
    std::cout << "\n=== 数据加密演示 ===" << std::endl;
    
    std::string sensitive_data = "这是敏感的金融数据：价格=78560.0, 成交量=12580";
    std::string encrypted_data;
    std::string decrypted_data;
    
    // 加密数据
    if (security_manager.EncryptSensitiveData(sensitive_data, encrypted_data)) {
        std::cout << "数据加密成功" << std::endl;
        std::cout << "原始数据长度: " << sensitive_data.length() << " 字节" << std::endl;
        std::cout << "加密数据长度: " << encrypted_data.length() << " 字节" << std::endl;
        
        // 解密数据
        if (security_manager.DecryptSensitiveData(encrypted_data, decrypted_data)) {
            std::cout << "数据解密成功" << std::endl;
            std::cout << "解密后数据: " << decrypted_data << std::endl;
            std::cout << "数据完整性: " << (sensitive_data == decrypted_data ? "验证通过" : "验证失败") << std::endl;
        } else {
            std::cout << "数据解密失败" << std::endl;
        }
    } else {
        std::cout << "数据加密失败" << std::endl;
    }
}

void DemoAuditLog(SecurityManager& security_manager) {
    std::cout << "\n=== 审计日志演示 ===" << std::endl;
    
    auto* audit_logger = security_manager.GetAuditLogger();
    if (!audit_logger) {
        std::cout << "审计日志系统未初始化" << std::endl;
        return;
    }
    
    // 查询最近的审计事件
    AuditQuery query;
    query.limit = 10;
    query.start_time = std::chrono::system_clock::now() - std::chrono::hours(1);
    
    auto events = audit_logger->QueryEvents(query);
    
    std::cout << "最近的审计事件 (" << events.size() << " 条):" << std::endl;
    for (const auto& event : events) {
        auto time_t = std::chrono::system_clock::to_time_t(event.timestamp);
        std::cout << "- " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
                  << " [" << (event.success ? "成功" : "失败") << "] "
                  << event.description << " (用户: " << event.user_id << ")" << std::endl;
    }
    
    // 获取统计信息
    auto stats = audit_logger->GetStatistics(
        std::chrono::system_clock::now() - std::chrono::hours(24),
        std::chrono::system_clock::now()
    );
    
    std::cout << "\n24小时内统计信息:" << std::endl;
    std::cout << "- 总事件数: " << stats.total_events << std::endl;
    std::cout << "- 登录尝试: " << stats.login_attempts << std::endl;
    std::cout << "- 失败登录: " << stats.failed_logins << std::endl;
    std::cout << "- 数据访问事件: " << stats.data_access_events << std::endl;
    std::cout << "- 安全违规: " << stats.security_violations << std::endl;
}

void DemoSecurityStatus(SecurityManager& security_manager) {
    std::cout << "\n=== 安全状态监控 ===" << std::endl;
    
    auto status = security_manager.GetSecurityStatus();
    
    std::cout << "安全组件状态:" << std::endl;
    std::cout << "- TLS加密: " << (status.tls_enabled ? "启用" : "禁用") << std::endl;
    std::cout << "- 数据加密: " << (status.encryption_enabled ? "启用" : "禁用") << std::endl;
    std::cout << "- JWT认证: " << (status.jwt_enabled ? "启用" : "禁用") << std::endl;
    std::cout << "- RBAC授权: " << (status.rbac_enabled ? "启用" : "禁用") << std::endl;
    std::cout << "- 审计日志: " << (status.audit_enabled ? "启用" : "禁用") << std::endl;
    
    std::cout << "\n运行时状态:" << std::endl;
    std::cout << "- 活跃会话数: " << status.active_sessions << std::endl;
    std::cout << "- 最近1小时失败登录: " << status.failed_login_attempts_last_hour << std::endl;
    
    if (status.last_security_incident != std::chrono::system_clock::time_point{}) {
        auto time_t = std::chrono::system_clock::to_time_t(status.last_security_incident);
        std::cout << "- 最后安全事件: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << std::endl;
    } else {
        std::cout << "- 最后安全事件: 无" << std::endl;
    }
}

int main() {
    std::cout << "金融数据服务系统 - 安全模块演示" << std::endl;
    std::cout << "=================================" << std::endl;
    
    // 加载配置
    SecurityConfig config = LoadSecurityConfig("config/security_config.json");
    
    // 创建安全管理器
    SecurityManager security_manager(config);
    
    // 初始化
    if (!security_manager.Initialize()) {
        std::cerr << "安全管理器初始化失败" << std::endl;
        return 1;
    }
    
    std::cout << "安全管理器初始化成功" << std::endl;
    
    try {
        // 演示各个功能
        DemoAuthentication(security_manager);
        DemoAuthorization(security_manager);
        DemoEncryption(security_manager);
        DemoAuditLog(security_manager);
        DemoSecurityStatus(security_manager);
        
        // 执行安全维护
        std::cout << "\n=== 安全维护 ===" << std::endl;
        security_manager.PerformSecurityMaintenance();
        std::cout << "安全维护完成" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "演示过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n演示完成!" << std::endl;
    return 0;
}