#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码表更新脚本
专门用于更新各市场的代码表（股票、指数、期货、基金、债券）
"""

import asyncio
import logging
import sys
import os
import json
from datetime import datetime
from typing import Dict, List

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from collectors.pytdx_collector import PyTDXCollector, PyTDXConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SymbolListUpdater:
    """代码表更新器"""
    
    def __init__(self):
        self.config = PyTDXConfig()
        self.collector = PyTDXCollector(self.config)
        self.symbol_lists = {}
    
    async def initialize(self) -> bool:
        """初始化"""
        try:
            logger.info("初始化PyTDX连接...")
            success = await self.collector.initialize()
            if success:
                logger.info("✅ 连接成功")
                return True
            else:
                logger.error("❌ 连接失败")
                return False
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    async def update_all_symbols(self) -> Dict[str, List[Dict]]:
        """更新所有类型的代码表"""
        logger.info("开始更新所有代码表...")
        
        symbol_types = ['stock', 'index', 'futures', 'fund', 'bond']
        
        try:
            # 获取所有代码表
            self.symbol_lists = await self.collector.get_all_symbol_lists(symbol_types)
            
            # 显示统计信息
            total_symbols = 0
            for symbol_type, symbols in self.symbol_lists.items():
                count = len(symbols)
                total_symbols += count
                logger.info(f"✅ {symbol_type:8s}: {count:6d} 个")
            
            logger.info(f"✅ 总计: {total_symbols} 个标的")
            return self.symbol_lists
            
        except Exception as e:
            logger.error(f"❌ 更新代码表失败: {e}")
            raise
    
    async def save_to_files(self, output_dir: str = "data/symbols"):
        """保存代码表到文件"""
        if not self.symbol_lists:
            logger.warning("没有代码表数据，请先运行更新")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for symbol_type, symbols in self.symbol_lists.items():
            # 保存为JSON格式
            json_file = os.path.join(output_dir, f"{symbol_type}_symbols_{timestamp}.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(symbols, f, ensure_ascii=False, indent=2)
            
            # 保存为CSV格式
            csv_file = os.path.join(output_dir, f"{symbol_type}_symbols_{timestamp}.csv")
            if symbols:
                import pandas as pd
                df = pd.DataFrame(symbols)
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            logger.info(f"✅ {symbol_type} 代码表已保存: {json_file}")
    
    async def show_sample_data(self, sample_size: int = 5):
        """显示样本数据"""
        if not self.symbol_lists:
            return
        
        logger.info("\n📋 代码表样本数据:")
        logger.info("=" * 80)
        
        for symbol_type, symbols in self.symbol_lists.items():
            if not symbols:
                continue
            
            logger.info(f"\n{symbol_type.upper()} ({len(symbols)} 个):")
            logger.info("-" * 40)
            
            sample_symbols = symbols[:sample_size]
            for symbol in sample_symbols:
                code = symbol.get('code', 'N/A')
                name = symbol.get('name', 'N/A')
                market = symbol.get('market', 'N/A')
                logger.info(f"  {code:8s} {name:20s} [{market}]")
            
            if len(symbols) > sample_size:
                logger.info(f"  ... 还有 {len(symbols) - sample_size} 个")
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.collector.close()
            logger.info("✅ 资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


async def main():
    """主函数"""
    print("PyTDX代码表更新工具")
    print("=" * 50)
    print("功能: 获取并更新各市场的代码表")
    print("支持: 股票、指数、期货、基金、债券")
    print()
    
    updater = SymbolListUpdater()
    
    try:
        # 初始化
        if not await updater.initialize():
            return 1
        
        # 更新代码表
        await updater.update_all_symbols()
        
        # 显示样本数据
        await updater.show_sample_data()
        
        # 保存到文件
        await updater.save_to_files()
        
        print("\n🎉 代码表更新完成！")
        print(f"数据已保存到 data/symbols/ 目录")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 更新失败: {e}")
        return 1
    finally:
        await updater.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)