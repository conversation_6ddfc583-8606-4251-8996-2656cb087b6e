#include "../src/storage/cold_storage.hpp"
#include "../src/storage/lifecycle_manager.hpp"
#include "../src/storage/archive_interface.hpp"
#include <iostream>
#include <chrono>
#include <thread>
#include <random>

using namespace financial_data::storage;

class ColdStorageDemo {
public:
    ColdStorageDemo() {
        // 配置冷存储
        config_.minio_endpoint = "localhost:9000";
        config_.minio_access_key = "admin";
        config_.minio_secret_key = "admin123456";
        config_.minio_bucket = "market-data";
        config_.minio_secure = false;
        
        config_.s3_region = "us-east-1";
        config_.s3_access_key = "your-access-key";
        config_.s3_secret_key = "your-secret-key";
        config_.s3_bucket = "financial-data-backup";
        
        config_.archive_threshold_days = 730;
        config_.compression_level = 9;
        config_.batch_size = 100000;
        
        cold_storage_ = std::make_shared<ColdDataStorage>(config_);
    }
    
    void RunDemo() {
        std::cout << "=== MinIO冷数据存储系统演示 ===" << std::endl;
        std::cout << std::endl;
        
        // 1. 初始化存储系统
        DemoInitialization();
        
        // 2. 演示数据归档
        DemoDataArchiving();
        
        // 3. 演示数据检索
        DemoDataRetrieval();
        
        // 4. 演示批量查询
        DemoBulkQuery();
        
        // 5. 演示生命周期管理
        DemoLifecycleManagement();
        
        // 6. 演示性能统计
        DemoPerformanceStats();
        
        std::cout << "=== 演示完成 ===" << std::endl;
    }

private:
    ColdStorageConfig config_;
    std::shared_ptr<ColdDataStorage> cold_storage_;
    
    void DemoInitialization() {
        std::cout << "1. 初始化冷存储系统..." << std::endl;
        
        bool success = cold_storage_->Initialize();
        if (success) {
            std::cout << "   ✓ 冷存储系统初始化成功" << std::endl;
            std::cout << "   ✓ MinIO连接: " << config_.minio_endpoint << std::endl;
            std::cout << "   ✓ 存储桶: " << config_.minio_bucket << std::endl;
        } else {
            std::cout << "   ✗ 冷存储系统初始化失败" << std::endl;
            std::cout << "   请确保MinIO服务正在运行" << std::endl;
            return;
        }
        std::cout << std::endl;
    }
    
    void DemoDataArchiving() {
        std::cout << "2. 演示数据归档功能..." << std::endl;
        
        // 创建模拟的历史数据
        auto batch = CreateSampleData(50000); // 5万条记录
        auto archive_date = std::chrono::system_clock::now() - std::chrono::hours(24 * 800); // 800天前的数据
        
        std::cout << "   创建了 " << batch.size() << " 条模拟历史数据" << std::endl;
        std::cout << "   数据日期: " << FormatTime(archive_date) << std::endl;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // 归档数据
        auto future = cold_storage_->ArchiveData(batch, "CU2409", "SHFE", archive_date);
        bool success = future.get();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        if (success) {
            std::cout << "   ✓ 数据归档成功" << std::endl;
            std::cout << "   ✓ 归档时间: " << duration.count() << " ms" << std::endl;
            std::cout << "   ✓ 吞吐量: " << (batch.size() * 1000 / duration.count()) << " 记录/秒" << std::endl;
            
            // 估算压缩比
            size_t original_size = batch.size() * 200; // 假设每条记录200字节
            size_t compressed_size = original_size / 8; // 假设8:1压缩比
            std::cout << "   ✓ 预估压缩比: 8:1" << std::endl;
            std::cout << "   ✓ 原始大小: " << FormatBytes(original_size) << std::endl;
            std::cout << "   ✓ 压缩后大小: " << FormatBytes(compressed_size) << std::endl;
        } else {
            std::cout << "   ✗ 数据归档失败" << std::endl;
        }
        std::cout << std::endl;
    }
    
    void DemoDataRetrieval() {
        std::cout << "3. 演示数据检索功能..." << std::endl;
        
        auto start_time = std::chrono::system_clock::now() - std::chrono::hours(24 * 801);
        auto end_time = std::chrono::system_clock::now() - std::chrono::hours(24 * 799);
        
        std::cout << "   查询时间范围: " << FormatTime(start_time) << " 到 " << FormatTime(end_time) << std::endl;
        std::cout << "   查询合约: CU2409 (SHFE)" << std::endl;
        
        auto query_start = std::chrono::high_resolution_clock::now();
        
        auto future = cold_storage_->RetrieveData("CU2409", "SHFE", start_time, end_time);
        auto result = future.get();
        
        auto query_end = std::chrono::high_resolution_clock::now();
        auto query_duration = std::chrono::duration_cast<std::chrono::milliseconds>(query_end - query_start);
        
        std::cout << "   ✓ 检索到 " << result.size() << " 条记录" << std::endl;
        std::cout << "   ✓ 查询时间: " << query_duration.count() << " ms" << std::endl;
        
        if (result.size() > 0) {
            std::cout << "   ✓ 样本数据:" << std::endl;
            std::cout << "     - 时间戳: " << result.timestamps[0] << std::endl;
            std::cout << "     - 合约: " << result.symbols[0] << std::endl;
            std::cout << "     - 交易所: " << result.exchanges[0] << std::endl;
            std::cout << "     - 最新价: " << result.last_prices[0] << std::endl;
            std::cout << "     - 成交量: " << result.volumes[0] << std::endl;
        }
        std::cout << std::endl;
    }
    
    void DemoBulkQuery() {
        std::cout << "4. 演示批量查询功能..." << std::endl;
        
        ArchiveInterface archive_interface(cold_storage_);
        
        // 创建批量查询请求
        BulkQueryRequest request;
        request.request_id = "demo_bulk_query_" + std::to_string(std::time(nullptr));
        request.output_format = "parquet";
        request.compression = "zstd";
        request.async_mode = true;
        
        // 添加多个查询条件
        QueryCondition condition1;
        condition1.symbols = {"CU2409"};
        condition1.exchanges = {"SHFE"};
        condition1.start_time = std::chrono::system_clock::now() - std::chrono::hours(24 * 800);
        condition1.end_time = std::chrono::system_clock::now() - std::chrono::hours(24 * 799);
        
        QueryCondition condition2;
        condition2.symbols = {"AL2409"};
        condition2.exchanges = {"SHFE"};
        condition2.start_time = std::chrono::system_clock::now() - std::chrono::hours(24 * 800);
        condition2.end_time = std::chrono::system_clock::now() - std::chrono::hours(24 * 799);
        
        request.conditions = {condition1, condition2};
        
        std::cout << "   提交批量查询请求: " << request.request_id << std::endl;
        std::cout << "   查询条件数量: " << request.conditions.size() << std::endl;
        std::cout << "   输出格式: " << request.output_format << std::endl;
        std::cout << "   压缩方式: " << request.compression << std::endl;
        
        auto submit_future = archive_interface.SubmitBulkQuery(request);
        std::string request_id = submit_future.get();
        
        if (!request_id.empty()) {
            std::cout << "   ✓ 批量查询请求提交成功" << std::endl;
            
            // 监控查询进度
            for (int i = 0; i < 10; ++i) {
                auto status = archive_interface.GetBulkQueryStatus(request_id);
                std::cout << "   进度: " << status.progress << "% (" << status.status << ")" << std::endl;
                
                if (status.status == "completed" || status.status == "failed") {
                    break;
                }
                
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
            
            auto final_status = archive_interface.GetBulkQueryStatus(request_id);
            if (final_status.status == "completed") {
                std::cout << "   ✓ 批量查询完成" << std::endl;
                std::cout << "   ✓ 输出文件数量: " << final_status.output_files.size() << std::endl;
                std::cout << "   ✓ 总记录数: " << final_status.summary.total_records << std::endl;
            } else {
                std::cout << "   ✗ 批量查询失败: " << final_status.error_message << std::endl;
            }
        } else {
            std::cout << "   ✗ 批量查询请求提交失败" << std::endl;
        }
        std::cout << std::endl;
    }
    
    void DemoLifecycleManagement() {
        std::cout << "5. 演示生命周期管理功能..." << std::endl;
        
        // 配置迁移策略
        MigrationPolicy policy;
        policy.warm_to_cold_days = 730;
        policy.retention_years = 10;
        policy.cron_schedule = "0 2 * * *";
        policy.enable_s3_backup = true;
        policy.enable_compression = true;
        policy.compression_level = 9;
        policy.batch_size = 100000;
        
        LifecycleManager lifecycle_manager(cold_storage_);
        lifecycle_manager.Initialize(policy);
        
        std::cout << "   生命周期策略配置:" << std::endl;
        std::cout << "   - 温数据到冷数据迁移阈值: " << policy.warm_to_cold_days << " 天" << std::endl;
        std::cout << "   - 数据保留期: " << policy.retention_years << " 年" << std::endl;
        std::cout << "   - 执行计划: " << policy.cron_schedule << std::endl;
        std::cout << "   - S3备份: " << (policy.enable_s3_backup ? "启用" : "禁用") << std::endl;
        
        // 手动触发迁移任务
        std::cout << "   手动触发数据迁移..." << std::endl;
        auto migration_future = lifecycle_manager.TriggerMigration();
        bool migration_success = migration_future.get();
        
        if (migration_success) {
            std::cout << "   ✓ 数据迁移任务执行成功" << std::endl;
        } else {
            std::cout << "   ✗ 数据迁移任务执行失败" << std::endl;
        }
        
        // 检查健康状态
        auto health = lifecycle_manager.CheckHealth();
        std::cout << "   系统健康状态: " << (health.is_healthy ? "健康" : "异常") << std::endl;
        std::cout << "   状态信息: " << health.status_message << std::endl;
        std::cout << "   待处理任务: " << health.pending_tasks << std::endl;
        std::cout << "   失败任务: " << health.failed_tasks << std::endl;
        
        // 获取迁移统计信息
        auto stats = lifecycle_manager.GetMigrationStats();
        std::cout << "   迁移统计信息:" << std::endl;
        std::cout << "   - 总任务数: " << stats.total_tasks << std::endl;
        std::cout << "   - 已完成: " << stats.completed_tasks << std::endl;
        std::cout << "   - 失败: " << stats.failed_tasks << std::endl;
        std::cout << "   - 待处理: " << stats.pending_tasks << std::endl;
        std::cout << "   - 运行中: " << stats.running_tasks << std::endl;
        std::cout << std::endl;
    }
    
    void DemoPerformanceStats() {
        std::cout << "6. 演示性能统计功能..." << std::endl;
        
        auto stats = cold_storage_->GetStorageStats();
        
        std::cout << "   存储统计信息:" << std::endl;
        std::cout << "   - 总文件数: " << stats.total_files << std::endl;
        std::cout << "   - 总记录数: " << stats.total_records << std::endl;
        std::cout << "   - 压缩后大小: " << FormatBytes(stats.total_compressed_size) << std::endl;
        std::cout << "   - 原始大小: " << FormatBytes(stats.total_original_size) << std::endl;
        std::cout << "   - 平均压缩比: " << std::fixed << std::setprecision(1) 
                  << stats.average_compression_ratio << ":1" << std::endl;
        std::cout << "   - S3备份文件: " << stats.s3_backup_files << std::endl;
        
        // 演示存储分析
        StorageAnalyzer analyzer(cold_storage_);
        auto report_future = analyzer.GenerateReport();
        auto report = report_future.get();
        
        std::cout << "   存储分析报告 (生成时间: " << FormatTime(report.generated_at) << "):" << std::endl;
        std::cout << "   - 热存储: " << FormatBytes(report.hot_storage.total_size) 
                  << " (" << report.hot_storage.file_count << " 文件)" << std::endl;
        std::cout << "   - 温存储: " << FormatBytes(report.warm_storage.total_size) 
                  << " (" << report.warm_storage.file_count << " 文件)" << std::endl;
        std::cout << "   - 冷存储: " << FormatBytes(report.cold_storage.total_size) 
                  << " (" << report.cold_storage.file_count << " 文件)" << std::endl;
        std::cout << "   - 总大小: " << FormatBytes(report.total_size) << std::endl;
        std::cout << "   - 压缩比: " << std::fixed << std::setprecision(1) 
                  << report.compression_ratio << ":1" << std::endl;
        
        if (!report.recommendations.empty()) {
            std::cout << "   优化建议:" << std::endl;
            for (const auto& recommendation : report.recommendations) {
                std::cout << "   - " << recommendation << std::endl;
            }
        }
        std::cout << std::endl;
    }
    
    TickDataBatch CreateSampleData(size_t size) {
        TickDataBatch batch;
        batch.reserve(size);
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<> price_dist(74000.0, 76000.0);
        std::uniform_int_distribution<> volume_dist(1, 1000);
        
        auto base_time = std::chrono::system_clock::now() - std::chrono::hours(24 * 800);
        auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            base_time.time_since_epoch()).count();
        
        for (size_t i = 0; i < size; ++i) {
            batch.timestamps.push_back(base_timestamp + i * 1000000); // 每毫秒一条
            batch.symbols.push_back("CU2409");
            batch.exchanges.push_back("SHFE");
            batch.last_prices.push_back(price_dist(gen));
            batch.volumes.push_back(volume_dist(gen));
            batch.turnovers.push_back(batch.last_prices.back() * batch.volumes.back());
            batch.open_interests.push_back(50000 + (i % 10000));
            batch.sequences.push_back(static_cast<uint32_t>(i));
            
            // 生成买卖盘数据
            double base_price = batch.last_prices.back();
            batch.bid_prices.push_back({
                base_price - 10, base_price - 20, base_price - 30, base_price - 40, base_price - 50
            });
            batch.bid_volumes.push_back({
                volume_dist(gen), volume_dist(gen), volume_dist(gen), volume_dist(gen), volume_dist(gen)
            });
            batch.ask_prices.push_back({
                base_price + 10, base_price + 20, base_price + 30, base_price + 40, base_price + 50
            });
            batch.ask_volumes.push_back({
                volume_dist(gen), volume_dist(gen), volume_dist(gen), volume_dist(gen), volume_dist(gen)
            });
        }
        
        return batch;
    }
    
    std::string FormatTime(const std::chrono::system_clock::time_point& time_point) {
        auto time_t = std::chrono::system_clock::to_time_t(time_point);
        auto tm = *std::localtime(&time_t);
        
        std::ostringstream oss;
        oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
        return oss.str();
    }
    
    std::string FormatBytes(size_t bytes) {
        const char* units[] = {"B", "KB", "MB", "GB", "TB"};
        int unit_index = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024.0 && unit_index < 4) {
            size /= 1024.0;
            unit_index++;
        }
        
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
        return oss.str();
    }
};

int main() {
    try {
        ColdStorageDemo demo;
        demo.RunDemo();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "演示程序异常: " << e.what() << std::endl;
        return 1;
    }
}