"""
Asynchronous Financial Data Client with asyncio support
"""

import asyncio
import aiogrpc
import time
import logging
from typing import List, Dict, Optional, Callable, Union, AsyncIterator
import pandas as pd
import numpy as np

from .data_models import TickData, KlineData, Level2Data, DataFrameBuilder
from .cache import AsyncDataCache
from .utils import DataConverter
from .exceptions import ConnectionError, DataError, TimeoutError

logger = logging.getLogger(__name__)


class AsyncLoadBalancer:
    """Async load balancer for multiple servers"""
    
    def __init__(self, servers: List[str]):
        self.servers = servers
        self.server_stats = {
            server: {'latency': 0.0, 'errors': 0, 'healthy': True} 
            for server in servers
        }
        self.lock = asyncio.Lock()
    
    async def get_best_server(self) -> str:
        """Select the best available server"""
        async with self.lock:
            healthy_servers = [
                server for server, stats in self.server_stats.items() 
                if stats['healthy']
            ]
            
            if not healthy_servers:
                for stats in self.server_stats.values():
                    stats['healthy'] = True
                    stats['errors'] = 0
                healthy_servers = list(self.servers)
            
            best_server = min(
                healthy_servers, 
                key=lambda s: self.server_stats[s]['latency']
            )
            return best_server
    
    async def report_error(self, server: str):
        """Report server error"""
        async with self.lock:
            if server in self.server_stats:
                self.server_stats[server]['errors'] += 1
                if self.server_stats[server]['errors'] > 3:
                    self.server_stats[server]['healthy'] = False
                    logger.warning(f"Server {server} marked as unhealthy")
    
    async def update_latency(self, server: str, latency: float):
        """Update server latency statistics"""
        async with self.lock:
            if server in self.server_stats:
                current = self.server_stats[server]['latency']
                self.server_stats[server]['latency'] = 0.7 * current + 0.3 * latency


class AsyncFinancialDataClient:
    """
    Asynchronous Financial Data Client with asyncio support
    
    Features:
    - Full asyncio support for non-blocking operations
    - Async streaming with async generators
    - Concurrent data fetching
    - Async caching
    """
    
    def __init__(self, 
                 servers: List[str],
                 max_retries: int = 3,
                 cache_size: int = 10000,
                 enable_cache: bool = True,
                 connection_timeout: float = 5.0):
        """
        Initialize the async financial data client
        
        Args:
            servers: List of server addresses
            max_retries: Maximum retry attempts
            cache_size: Cache size for data storage
            enable_cache: Enable/disable data caching
            connection_timeout: Connection timeout in seconds
        """
        self.load_balancer = AsyncLoadBalancer(servers)
        self.max_retries = max_retries
        self.connection_timeout = connection_timeout
        self.channels = {}
        self.stubs = {}
        
        # Async components
        self.cache = AsyncDataCache(cache_size) if enable_cache else None
        self.converter = DataConverter()
        
        # Connection semaphore to limit concurrent connections
        self.connection_semaphore = asyncio.Semaphore(10)
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._initialize_connections()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def _initialize_connections(self):
        """Initialize connections to all servers"""
        tasks = []
        for server in self.load_balancer.servers:
            tasks.append(self._connect_to_server(server))
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _connect_to_server(self, server: str):
        """Connect to a single server"""
        try:
            async with self.connection_semaphore:
                channel = aiogrpc.insecure_channel(server, options=[
                    ('grpc.keepalive_time_ms', 30000),
                    ('grpc.keepalive_timeout_ms', 5000),
                    ('grpc.keepalive_permit_without_calls', True),
                    ('grpc.max_receive_message_length', 4 * 1024 * 1024),
                ])
                # stub = market_data_service_pb2_grpc.MarketDataServiceStub(channel)
                
                self.channels[server] = channel
                # self.stubs[server] = stub
                logger.info(f"Connected to server: {server}")
                
        except Exception as e:
            logger.error(f"Failed to connect to server {server}: {e}")
            await self.load_balancer.report_error(server)
    
    async def _get_stub_with_retry(self):
        """Get available stub with retry and failover"""
        for attempt in range(self.max_retries):
            server = await self.load_balancer.get_best_server()
            if server in self.stubs:
                return self.stubs[server], server
            
            try:
                await self._connect_to_server(server)
                if server in self.stubs:
                    return self.stubs[server], server
            except Exception as e:
                logger.error(f"Retry {attempt + 1} failed for server {server}: {e}")
                await self.load_balancer.report_error(server)
                await asyncio.sleep(0.1 * (attempt + 1))
        
        raise ConnectionError("No healthy servers available")
    
    async def get_tick_data(self, 
                           symbol: str, 
                           exchange: str = None,
                           start_time: Union[int, str, pd.Timestamp] = None,
                           end_time: Union[int, str, pd.Timestamp] = None,
                           limit: int = 1000,
                           as_dataframe: bool = True) -> Union[pd.DataFrame, List[TickData]]:
        """
        Async get historical tick data
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            start_time: Start timestamp
            end_time: End timestamp
            limit: Maximum number of records
            as_dataframe: Return as pandas DataFrame
            
        Returns:
            DataFrame or list of TickData objects
        """
        start_ts = self.converter.to_nanoseconds(start_time) if start_time else None
        end_ts = self.converter.to_nanoseconds(end_time) if end_time else None
        
        cache_key = f"tick:{symbol}:{exchange}:{start_ts}:{end_ts}:{limit}"
        if self.cache:
            cached_data = await self.cache.get(cache_key)
            if cached_data is not None:
                return cached_data
        
        try:
            stub, server = await self._get_stub_with_retry()
            
            start_time_req = time.time()
            # Placeholder for actual async gRPC call
            # request = market_data_service_pb2.TickDataRequest(...)
            # response = await stub.GetTickData(request)
            
            latency = (time.time() - start_time_req) * 1000
            await self.load_balancer.update_latency(server, latency)
            
            # Convert response to TickData objects (placeholder)
            ticks = []
            
            if self.cache:
                await self.cache.put(cache_key, ticks)
            
            if as_dataframe:
                result = DataFrameBuilder.from_tick_list(ticks)
            else:
                result = ticks
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get tick data: {e}")
            raise DataError(f"Failed to get tick data: {e}")
    
    async def get_kline_data(self,
                            symbol: str,
                            period: str = "1m",
                            exchange: str = None,
                            start_time: Union[int, str, pd.Timestamp] = None,
                            end_time: Union[int, str, pd.Timestamp] = None,
                            limit: int = 1000,
                            as_dataframe: bool = True) -> Union[pd.DataFrame, List[KlineData]]:
        """
        Async get historical K-line data
        """
        start_ts = self.converter.to_nanoseconds(start_time) if start_time else None
        end_ts = self.converter.to_nanoseconds(end_time) if end_time else None
        
        cache_key = f"kline:{symbol}:{period}:{exchange}:{start_ts}:{end_ts}:{limit}"
        if self.cache:
            cached_data = await self.cache.get(cache_key)
            if cached_data is not None:
                return cached_data
        
        try:
            stub, server = await self._get_stub_with_retry()
            
            # Placeholder for actual implementation
            klines = []
            
            if self.cache:
                await self.cache.put(cache_key, klines)
            
            if as_dataframe:
                result = DataFrameBuilder.from_kline_list(klines)
            else:
                result = klines
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get kline data: {e}")
            raise DataError(f"Failed to get kline data: {e}")
    
    async def stream_tick_data(self,
                              symbols: List[str],
                              exchange: str = None,
                              buffer_size: int = 1000) -> AsyncIterator[TickData]:
        """
        Async stream real-time tick data
        
        Args:
            symbols: List of trading symbols
            exchange: Exchange name
            buffer_size: Stream buffer size
            
        Yields:
            TickData objects
        """
        stub, server = await self._get_stub_with_retry()
        
        try:
            # Placeholder for async streaming
            # request = market_data_service_pb2.StreamTickDataRequest(...)
            # async for response in stub.StreamTickData(request):
            #     for pb_tick in response.ticks:
            #         yield TickData.from_protobuf(pb_tick)
            
            # Placeholder implementation
            for i in range(10):
                await asyncio.sleep(0.1)
                yield TickData(
                    timestamp=int(time.time() * 1_000_000_000),
                    symbol=symbols[0] if symbols else "TEST",
                    exchange=exchange or "TEST",
                    last_price=100.0 + i,
                    volume=1000,
                    turnover=100000.0
                )
                
        except Exception as e:
            logger.error(f"Stream error: {e}")
            await self.load_balancer.report_error(server)
            raise
    
    async def stream_kline_data(self,
                               symbols: List[str],
                               period: str = "1m",
                               exchange: str = None,
                               buffer_size: int = 1000) -> AsyncIterator[KlineData]:
        """
        Async stream real-time K-line data
        
        Args:
            symbols: List of trading symbols
            period: Time period
            exchange: Exchange name
            buffer_size: Stream buffer size
            
        Yields:
            KlineData objects
        """
        stub, server = await self._get_stub_with_retry()
        
        try:
            # Placeholder for async streaming
            for i in range(5):
                await asyncio.sleep(1.0)
                yield KlineData(
                    timestamp=int(time.time() * 1_000_000_000),
                    symbol=symbols[0] if symbols else "TEST",
                    exchange=exchange or "TEST",
                    period=period,
                    open=100.0,
                    high=105.0,
                    low=95.0,
                    close=102.0 + i,
                    volume=10000,
                    turnover=1000000.0
                )
                
        except Exception as e:
            logger.error(f"Kline stream error: {e}")
            await self.load_balancer.report_error(server)
            raise
    
    async def get_concurrent_data(self,
                                 requests: List[Dict],
                                 max_concurrent: int = 10,
                                 as_dataframe: bool = True) -> Dict[str, Union[pd.DataFrame, List]]:
        """
        Get multiple data requests concurrently
        
        Args:
            requests: List of data requests
            max_concurrent: Maximum concurrent requests
            as_dataframe: Return as pandas DataFrames
            
        Returns:
            Dictionary of results keyed by request identifier
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def _process_request(req, req_id):
            async with semaphore:
                try:
                    req_type = req.get('type', 'tick')
                    if req_type == 'tick':
                        return await self.get_tick_data(
                            req['symbol'],
                            req.get('exchange'),
                            req.get('start_time'),
                            req.get('end_time'),
                            req.get('limit', 1000),
                            as_dataframe
                        )
                    elif req_type == 'kline':
                        return await self.get_kline_data(
                            req['symbol'],
                            req.get('period', '1m'),
                            req.get('exchange'),
                            req.get('start_time'),
                            req.get('end_time'),
                            req.get('limit', 1000),
                            as_dataframe
                        )
                except Exception as e:
                    logger.error(f"Concurrent request {req_id} failed: {e}")
                    return None
        
        # Create tasks for all requests
        tasks = []
        req_ids = []
        for i, req in enumerate(requests):
            req_id = req.get('id', f'req_{i}')
            req_ids.append(req_id)
            tasks.append(_process_request(req, req_id))
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Combine results with request IDs
        return dict(zip(req_ids, results))
    
    async def health_check(self) -> bool:
        """Async health check"""
        try:
            stub, server = await self._get_stub_with_retry()
            # Placeholder for actual health check
            return True
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    async def get_server_stats(self) -> Dict:
        """Get server statistics"""
        async with self.load_balancer.lock:
            return dict(self.load_balancer.server_stats)
    
    async def clear_cache(self):
        """Clear data cache"""
        if self.cache:
            await self.cache.clear()
    
    async def close(self):
        """Close client connections"""
        tasks = []
        for channel in self.channels.values():
            tasks.append(channel.close())
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info("Async client closed")


# Utility functions for async operations
async def create_client(servers: List[str], **kwargs) -> AsyncFinancialDataClient:
    """Create and initialize async client"""
    client = AsyncFinancialDataClient(servers, **kwargs)
    await client._initialize_connections()
    return client


async def stream_multiple_symbols(client: AsyncFinancialDataClient,
                                 symbols: List[str],
                                 data_type: str = "tick",
                                 exchange: str = None) -> AsyncIterator[Union[TickData, KlineData]]:
    """
    Stream data for multiple symbols concurrently
    
    Args:
        client: Async client instance
        symbols: List of symbols to stream
        data_type: Type of data ("tick" or "kline")
        exchange: Exchange name
        
    Yields:
        Market data objects
    """
    async def _stream_symbol(symbol):
        if data_type == "tick":
            async for data in client.stream_tick_data([symbol], exchange):
                yield data
        elif data_type == "kline":
            async for data in client.stream_kline_data([symbol], exchange=exchange):
                yield data
    
    # Create tasks for all symbols
    tasks = [_stream_symbol(symbol) for symbol in symbols]
    
    # Merge all streams
    async for task in asyncio.as_completed(tasks):
        async for data in await task:
            yield data