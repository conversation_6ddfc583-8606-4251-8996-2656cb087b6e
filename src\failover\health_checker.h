#pragma once

#include <memory>
#include <string>
#include <vector>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <functional>
#include <unordered_map>
#include <spdlog/spdlog.h>

namespace financial_data {

enum class HealthStatus {
    HEALTHY,
    WARNING,
    CRITICAL,
    UNKNOWN
};

struct HealthMetric {
    std::string name;
    double value;
    double threshold_warning;
    double threshold_critical;
    HealthStatus status;
    std::chrono::steady_clock::time_point last_updated;
    std::string description;
    
    HealthMetric() : value(0.0), threshold_warning(0.0), threshold_critical(0.0), 
                    status(HealthStatus::UNKNOWN) {}
};

struct ComponentHealth {
    std::string component_name;
    HealthStatus overall_status;
    std::vector<HealthMetric> metrics;
    std::chrono::steady_clock::time_point last_check;
    std::string error_message;
    
    ComponentHealth() : overall_status(HealthStatus::UNKNOWN) {}
};

struct SystemHealth {
    HealthStatus overall_status;
    std::vector<ComponentHealth> components;
    std::chrono::steady_clock::time_point last_check;
    int healthy_components;
    int warning_components;
    int critical_components;
    
    SystemHealth() : overall_status(HealthStatus::UNKNOWN), 
                    healthy_components(0), warning_components(0), critical_components(0) {}
};

struct HealthCheckConfig {
    int check_interval_ms = 5000;          // 健康检查间隔
    int component_timeout_ms = 3000;       // 组件检查超时
    bool enable_auto_recovery = true;      // 启用自动恢复
    int recovery_attempts = 3;             // 恢复尝试次数
    int recovery_delay_ms = 1000;          // 恢复延迟
    std::string log_level = "info";        // 日志级别
};

class HealthChecker {
public:
    using HealthCheckFunction = std::function<ComponentHealth()>;
    using RecoveryFunction = std::function<bool(const std::string& component)>;
    using AlertCallback = std::function<void(const ComponentHealth& component)>;

    explicit HealthChecker(const HealthCheckConfig& config = HealthCheckConfig{});
    ~HealthChecker();

    // 启动健康检查器
    bool Start();
    
    // 停止健康检查器
    void Stop();
    
    // 注册组件健康检查函数
    void RegisterComponent(const std::string& component_name, HealthCheckFunction check_func);
    
    // 注册组件恢复函数
    void RegisterRecovery(const std::string& component_name, RecoveryFunction recovery_func);
    
    // 设置告警回调
    void SetAlertCallback(AlertCallback callback) { alert_callback_ = callback; }
    
    // 获取系统整体健康状态
    SystemHealth GetSystemHealth() const;
    
    // 获取特定组件健康状态
    ComponentHealth GetComponentHealth(const std::string& component_name) const;
    
    // 手动触发健康检查
    void TriggerHealthCheck();
    
    // 手动触发组件恢复
    bool TriggerRecovery(const std::string& component_name);
    
    // 更新组件指标
    void UpdateMetric(const std::string& component_name, const std::string& metric_name, 
                     double value, double warning_threshold = 0.0, double critical_threshold = 0.0);
    
    // 检查系统是否健康
    bool IsSystemHealthy() const;
    
    // 获取健康检查统计信息
    struct HealthStats {
        int total_checks;
        int successful_checks;
        int failed_checks;
        int recovery_attempts;
        int successful_recoveries;
        std::chrono::steady_clock::time_point last_check_time;
    };
    
    HealthStats GetHealthStats() const;

private:
    // 健康检查线程
    void HealthCheckThread();
    
    // 执行单个组件的健康检查
    ComponentHealth CheckComponent(const std::string& component_name);
    
    // 尝试恢复组件
    bool AttemptRecovery(const std::string& component_name);
    
    // 计算整体健康状态
    HealthStatus CalculateOverallStatus(const std::vector<ComponentHealth>& components);
    
    // 发送告警
    void SendAlert(const ComponentHealth& component);
    
    // 内置的系统组件检查
    ComponentHealth CheckCPUUsage();
    ComponentHealth CheckMemoryUsage();
    ComponentHealth CheckDiskUsage();
    ComponentHealth CheckNetworkConnectivity();

private:
    HealthCheckConfig config_;
    std::shared_ptr<spdlog::logger> logger_;
    
    std::atomic<bool> running_;
    std::thread health_check_thread_;
    
    mutable std::mutex components_mutex_;
    std::unordered_map<std::string, HealthCheckFunction> check_functions_;
    std::unordered_map<std::string, RecoveryFunction> recovery_functions_;
    std::unordered_map<std::string, ComponentHealth> component_health_;
    
    AlertCallback alert_callback_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    HealthStats stats_;
    
    // 恢复尝试计数
    std::unordered_map<std::string, int> recovery_attempts_;
};

} // namespace financial_data