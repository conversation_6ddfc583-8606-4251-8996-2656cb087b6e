#pragma once

#include <memory>
#include <string>
#include <vector>
#include <atomic>
#include <thread>
#include <functional>
#include <unordered_map>
#include <mutex>
#include <condition_variable>

#include "lock_free_queue.h"
#include "kafka_integration.h"
#include "data_router.h"
#include "backpressure_controller.h"
#include "data_types.h"
#include "../storage/storage_manager.h"

namespace financial_data {
namespace databus {

/**
 * @brief 数据总线配置
 */
struct DataBusConfig {
    // 队列配置
    size_t input_queue_size = 131072;      // 128K
    size_t output_queue_size = 65536;      // 64K
    size_t batch_queue_size = 4096;        // 4K
    
    // 工作线程配置
    size_t worker_thread_count = 4;
    size_t router_thread_count = 2;
    
    // Kafka配置
    bool enable_kafka = true;
    KafkaConfig kafka_config;
    
    // 存储配置
    bool enable_storage = true;
    StorageManagerConfig storage_config;
    
    // 背压控制配置
    bool enable_backpressure = true;
    BackpressureConfig backpressure_config;
    
    // 路由配置
    size_t max_clients = 1000;
    int64_t client_heartbeat_timeout_ms = 30000;
    
    // 性能配置
    bool enable_batching = true;
    size_t batch_size = 100;
    uint32_t batch_timeout_ms = 10;
    
    // 监控配置
    bool enable_monitoring = true;
    uint32_t statistics_interval_ms = 1000;
    
    // 持久化配置
    bool enable_persistence = false;
    std::string persistence_path = "./data_bus_persistence";
};

/**
 * @brief 数据总线统计信息
 */
struct DataBusStatistics {
    // 消息统计
    std::atomic<uint64_t> total_messages_received{0};
    std::atomic<uint64_t> total_messages_processed{0};
    std::atomic<uint64_t> total_messages_sent{0};
    std::atomic<uint64_t> total_messages_dropped{0};
    
    // 性能统计
    std::atomic<uint64_t> avg_processing_latency_ns{0};
    std::atomic<uint64_t> max_processing_latency_ns{0};
    std::atomic<uint64_t> throughput_per_second{0};
    
    // 队列统计
    std::atomic<uint64_t> input_queue_size{0};
    std::atomic<uint64_t> output_queue_size{0};
    std::atomic<uint64_t> max_queue_size{0};
    
    // 客户端统计
    std::atomic<uint64_t> active_clients{0};
    std::atomic<uint64_t> total_subscriptions{0};
    
    // 错误统计
    std::atomic<uint64_t> processing_errors{0};
    std::atomic<uint64_t> routing_errors{0};
    std::atomic<uint64_t> kafka_errors{0};
    
    void Reset() {
        total_messages_received = 0;
        total_messages_processed = 0;
        total_messages_sent = 0;
        total_messages_dropped = 0;
        avg_processing_latency_ns = 0;
        max_processing_latency_ns = 0;
        throughput_per_second = 0;
        input_queue_size = 0;
        output_queue_size = 0;
        max_queue_size = 0;
        active_clients = 0;
        total_subscriptions = 0;
        processing_errors = 0;
        routing_errors = 0;
        kafka_errors = 0;
    }
    
    double GetProcessingRate() const {
        uint64_t received = total_messages_received.load();
        return received > 0 ? static_cast<double>(total_messages_processed.load()) / received : 0.0;
    }
    
    double GetDropRate() const {
        uint64_t received = total_messages_received.load();
        return received > 0 ? static_cast<double>(total_messages_dropped.load()) / received : 0.0;
    }
};

/**
 * @brief 数据总线事件类型
 */
enum class DataBusEvent {
    STARTED,
    STOPPED,
    CLIENT_CONNECTED,
    CLIENT_DISCONNECTED,
    BACKPRESSURE_TRIGGERED,
    BACKPRESSURE_RELIEVED,
    ERROR_OCCURRED
};

/**
 * @brief 数据总线事件处理器
 */
using DataBusEventHandler = std::function<void(DataBusEvent, const std::string&)>;

/**
 * @brief 高性能数据总线
 * 
 * 集成无锁队列、Kafka消息队列、数据路由器和背压控制器，
 * 提供微秒级延迟的数据传输和分发服务
 */
class DataBus {
private:
    DataBusConfig config_;
    DataBusStatistics statistics_;
    
    // 核心组件
    std::unique_ptr<WrapperQueue> input_queue_;
    std::unique_ptr<WrapperQueue> output_queue_;
    std::unique_ptr<BatchQueue> batch_queue_;
    
    std::unique_ptr<KafkaIntegration> kafka_integration_;
    std::unique_ptr<DataRouter> data_router_;
    std::unique_ptr<BackpressureController> backpressure_controller_;
    std::unique_ptr<StorageManager> storage_manager_;
    
    // 工作线程
    std::vector<std::thread> worker_threads_;
    std::vector<std::thread> batch_threads_;
    std::thread statistics_thread_;
    
    std::atomic<bool> running_{false};
    
    // 批处理相关
    std::vector<MarketDataWrapper> current_batch_;
    std::mutex batch_mutex_;
    std::condition_variable batch_cv_;
    std::chrono::steady_clock::time_point last_batch_time_;
    
    // 事件处理
    std::vector<DataBusEventHandler> event_handlers_;
    std::mutex event_handlers_mutex_;
    
    // 性能监控
    std::chrono::steady_clock::time_point start_time_;
    std::atomic<uint64_t> last_message_count_{0};
    std::chrono::steady_clock::time_point last_throughput_check_;

public:
    explicit DataBus(const DataBusConfig& config = DataBusConfig{});
    ~DataBus();

    /**
     * @brief 启动数据总线
     */
    bool Start();

    /**
     * @brief 停止数据总线
     */
    void Stop();

    /**
     * @brief 检查是否正在运行
     */
    bool IsRunning() const { return running_.load(); }

    /**
     * @brief 推送Tick数据
     */
    bool PushTick(const StandardTick& tick);

    /**
     * @brief 推送Level2数据
     */
    bool PushLevel2(const Level2Data& level2);

    /**
     * @brief 推送包装数据
     */
    bool PushData(const MarketDataWrapper& data);

    /**
     * @brief 批量推送数据
     */
    bool PushBatch(const std::vector<MarketDataWrapper>& data_batch);

    /**
     * @brief 注册客户端
     */
    bool RegisterClient(const std::string& client_id, 
                       const std::string& client_type,
                       std::function<bool(const MarketDataWrapper&)> callback);

    /**
     * @brief 注销客户端
     */
    bool UnregisterClient(const std::string& client_id);

    /**
     * @brief 客户端订阅
     */
    bool Subscribe(const std::string& client_id, 
                  const std::vector<std::string>& symbols,
                  const std::vector<std::string>& exchanges = {});

    /**
     * @brief 客户端取消订阅
     */
    bool Unsubscribe(const std::string& client_id, 
                    const std::vector<std::string>& symbols,
                    const std::vector<std::string>& exchanges = {});

    /**
     * @brief 客户端心跳
     */
    void ClientHeartbeat(const std::string& client_id);

    /**
     * @brief 获取客户端信息
     */
    std::shared_ptr<ClientInfo> GetClientInfo(const std::string& client_id);

    /**
     * @brief 获取活跃客户端列表
     */
    std::vector<std::string> GetActiveClients();

    /**
     * @brief 添加路由规则
     */
    bool AddRoutingRule(const RoutingRule& rule);

    /**
     * @brief 删除路由规则
     */
    bool RemoveRoutingRule(const std::string& rule_id);

    /**
     * @brief 获取统计信息
     */
    DataBusStatistics GetStatistics() const;

    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

    /**
     * @brief 获取队列状态
     */
    struct QueueStatus {
        size_t input_queue_size;
        size_t output_queue_size;
        size_t batch_queue_size;
        size_t total_client_queues_size;
        double input_queue_usage;
        double output_queue_usage;
    };
    
    QueueStatus GetQueueStatus() const;

    /**
     * @brief 获取Kafka统计信息
     */
    KafkaProducer::Statistics GetKafkaStatistics() const;

    /**
     * @brief 获取路由统计信息
     */
    RoutingStatistics GetRoutingStatistics() const;

    /**
     * @brief 获取背压统计信息
     */
    BackpressureStatistics GetBackpressureStatistics() const;

    /**
     * @brief 获取存储统计信息
     */
    StorageStatistics GetStorageStatistics() const;

    /**
     * @brief 添加事件处理器
     */
    void AddEventHandler(DataBusEventHandler handler);

    /**
     * @brief 移除事件处理器
     */
    void RemoveEventHandler(DataBusEventHandler handler);

    /**
     * @brief 更新配置
     */
    bool UpdateConfig(const DataBusConfig& config);

    /**
     * @brief 获取配置
     */
    DataBusConfig GetConfig() const { return config_; }

    /**
     * @brief 刷新所有缓冲区
     */
    void Flush();

    /**
     * @brief 执行健康检查
     */
    struct HealthStatus {
        bool overall_healthy;
        bool queues_healthy;
        bool kafka_healthy;
        bool router_healthy;
        bool backpressure_healthy;
        bool storage_healthy;
        std::string error_message;
    };
    
    HealthStatus GetHealthStatus() const;

private:
    /**
     * @brief 初始化组件
     */
    bool InitializeComponents();

    /**
     * @brief 清理组件
     */
    void CleanupComponents();

    /**
     * @brief 工作线程主循环
     */
    void WorkerLoop(size_t worker_id);

    /**
     * @brief 批处理线程主循环
     */
    void BatchWorkerLoop();

    /**
     * @brief 统计线程主循环
     */
    void StatisticsLoop();

    /**
     * @brief 处理单条数据
     */
    void ProcessData(const MarketDataWrapper& data);

    /**
     * @brief 处理批量数据
     */
    void ProcessBatch(const MarketDataBatch& batch);

    /**
     * @brief 发送批次数据
     */
    void FlushBatch();

    /**
     * @brief 更新性能统计
     */
    void UpdateStatistics();

    /**
     * @brief 触发事件
     */
    void TriggerEvent(DataBusEvent event, const std::string& message = "");

    /**
     * @brief 检查背压状态
     */
    void CheckBackpressure();

    /**
     * @brief 记录处理延迟
     */
    void RecordProcessingLatency(uint64_t latency_ns);

    /**
     * @brief 创建队列监控器
     */
    template<typename QueueType>
    std::shared_ptr<QueueMonitor> CreateQueueMonitor(std::shared_ptr<QueueType> queue) {
        return std::make_shared<QueueMonitorImpl<QueueType>>(queue);
    }
};

/**
 * @brief 数据总线工厂
 */
class DataBusFactory {
public:
    /**
     * @brief 创建默认配置的数据总线
     */
    static std::unique_ptr<DataBus> CreateDefault();

    /**
     * @brief 创建高性能配置的数据总线
     */
    static std::unique_ptr<DataBus> CreateHighPerformance();

    /**
     * @brief 创建低延迟配置的数据总线
     */
    static std::unique_ptr<DataBus> CreateLowLatency();

    /**
     * @brief 创建内存优化配置的数据总线
     */
    static std::unique_ptr<DataBus> CreateMemoryOptimized();

    /**
     * @brief 从配置文件创建数据总线
     */
    static std::unique_ptr<DataBus> CreateFromConfig(const std::string& config_file);
};

} // namespace databus
} // namespace financial_data