# 金融数据服务系统

## 概述

本系统是一个专业的量化投资高频交易行情数据服务平台，提供微秒级延迟、零数据丢失、全市场覆盖的实时和历史行情数据服务。

## 系统架构

- **数据采集层**: 支持CTP、股票、期权、外汇等多种数据源
- **数据处理层**: 实时数据标准化、校验和路由
- **存储层**: Redis热数据 + ClickHouse温数据 + MinIO冷数据
- **接口层**: WebSocket、REST API、gRPC多协议支持

## 快速开始

### 环境要求

- C++17编译器 (GCC 8+ 或 Clang 7+)
- CMake 3.15+
- Docker 和 Docker Compose
- Redis 6.0+
- ClickHouse 21.0+
- Kafka 2.8+

### 构建项目

```bash
mkdir build
cd build
cmake ..
make -j$(nproc)
```

### 运行开发环境

```bash
docker-compose up -d
```

### 运行测试

```bash
cd build
ctest
```

## 性能指标

- 端到端延迟: < 50微秒
- 数据吞吐量: 100万条/秒
- 并发连接: 1000个客户端
- 系统可用性: 99.99%

## 许可证

本项目采用MIT许可证。