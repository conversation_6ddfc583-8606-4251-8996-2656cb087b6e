# Market Data Collection Enhancement - Common Issues and Solutions

## Overview

This document provides solutions to common issues encountered when deploying and operating the Market Data Collection Enhancement system. Each issue includes symptoms, root causes, diagnostic steps, and detailed solutions.

## Deployment Issues

### Issue 1: Pod Stuck in CrashLoopBackOff

#### Symptoms
- Pod status shows `CrashLoopBackOff`
- Application exits immediately after startup
- Repeated restart attempts

#### Root Causes
- Configuration errors
- Missing dependencies
- Resource constraints
- Permission issues

#### Diagnostic Steps
```bash
# Check pod status and events
kubectl get pods -n market-data
kubectl describe pod <pod-name> -n market-data

# Check application logs
kubectl logs <pod-name> -n market-data --previous

# Check resource usage
kubectl top pods -n market-data
```

#### Solutions

**Configuration Issues**
```bash
# Validate configuration
kubectl get configmap market-data-config -n market-data -o yaml

# Check for syntax errors in JSON configuration
kubectl exec -n market-data <pod-name> -- python3 -c "
import json
with open('/app/config/unified_config.json', 'r') as f:
    config = json.load(f)
print('Configuration is valid')
"

# Fix common configuration errors
kubectl patch configmap market-data-config -n market-data --patch '
data:
  unified_config.json: |
    {
      "collection": {
        "pytdx": {
          "enabled": true,
          "servers": [
            {"host": "**************", "port": 7709}
          ]
        }
      }
    }
'
```

**Resource Constraints**
```bash
# Increase resource limits
kubectl patch deployment market-data-collector -n market-data -p '
{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "market-data-collector",
            "resources": {
              "requests": {
                "memory": "1Gi",
                "cpu": "500m"
              },
              "limits": {
                "memory": "4Gi",
                "cpu": "2000m"
              }
            }
          }
        ]
      }
    }
  }
}'
```

**Permission Issues**
```bash
# Check and fix file permissions
kubectl exec -n market-data <pod-name> -- ls -la /app/config/
kubectl exec -n market-data <pod-name> -- chmod 644 /app/config/unified_config.json
```

### Issue 2: Service Discovery Problems

#### Symptoms
- Cannot connect to Redis/ClickHouse/MinIO
- DNS resolution failures
- Connection timeouts

#### Diagnostic Steps
```bash
# Test service connectivity
kubectl exec -n market-data <pod-name> -- nslookup redis-service
kubectl exec -n market-data <pod-name> -- nc -zv redis-service 6379

# Check service endpoints
kubectl get endpoints -n market-data
```

#### Solutions

**DNS Resolution**
```bash
# Check DNS configuration
kubectl exec -n market-data <pod-name> -- cat /etc/resolv.conf

# Test DNS resolution
kubectl exec -n market-data <pod-name> -- nslookup kubernetes.default.svc.cluster.local

# Fix DNS issues by restarting CoreDNS
kubectl rollout restart deployment/coredns -n kube-system
```

**Service Configuration**
```yaml
# Ensure services are properly configured
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: market-data
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
```

## Data Collection Issues

### Issue 3: No Data Being Collected

#### Symptoms
- Collection metrics show zero
- No new data in storage
- No error messages

#### Diagnostic Steps
```bash
# Check collector status
curl http://localhost:8080/api/v1/collectors/status

# Check collection metrics
curl http://localhost:8080/api/v1/metrics/summary

# Test pytdx connectivity
kubectl exec -n market-data <pod-name> -- python3 -c "
import socket
try:
    sock = socket.create_connection(('**************', 7709), timeout=10)
    print('pytdx server is reachable')
    sock.close()
except Exception as e:
    print(f'pytdx server unreachable: {e}')
"
```

#### Solutions

**Market Hours Check**
```python
# Check if market is open
import datetime
import pytz

def is_market_open():
    tz = pytz.timezone('Asia/Shanghai')
    now = datetime.datetime.now(tz)
    
    # Check if it's a weekday
    if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
        return False
    
    # Check trading hours
    morning_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
    morning_end = now.replace(hour=11, minute=30, second=0, microsecond=0)
    afternoon_start = now.replace(hour=13, minute=0, second=0, microsecond=0)
    afternoon_end = now.replace(hour=15, minute=0, second=0, microsecond=0)
    
    return (morning_start <= now <= morning_end) or (afternoon_start <= now <= afternoon_end)

print(f"Market is {'open' if is_market_open() else 'closed'}")
```

**Configuration Fix**
```bash
# Enable collectors
curl -X PUT http://localhost:8080/api/v1/config -d '
{
  "collection": {
    "pytdx": {
      "enabled": true
    },
    "ctp": {
      "enabled": true
    }
  }
}'

# Start collectors manually
curl -X POST http://localhost:8080/api/v1/collectors/pytdx/start -d '
{
  "symbols": ["000001.SZ", "600000.SH"]
}'
```

### Issue 4: High Collection Error Rate

#### Symptoms
- Error rate > 5%
- Frequent timeout errors
- Incomplete data

#### Diagnostic Steps
```bash
# Check error logs
kubectl logs -n market-data deployment/market-data-collector | grep -i error | tail -20

# Check error metrics
curl http://localhost:8080/api/v1/metrics/summary | jq '.data_collection.error_rate_percent'
```

#### Solutions

**Timeout Issues**
```json
{
  "collection": {
    "pytdx": {
      "connection_timeout_seconds": 30,
      "read_timeout_seconds": 60,
      "retry_attempts": 3,
      "retry_delay_seconds": 5
    }
  }
}
```

**Server Load Balancing**
```json
{
  "collection": {
    "pytdx": {
      "servers": [
        {"host": "**************", "port": 7709},
        {"host": "************", "port": 7709},
        {"host": "*************", "port": 7709}
      ],
      "server_selection_strategy": "round_robin"
    }
  }
}
```

**Rate Limiting**
```json
{
  "collection": {
    "pytdx": {
      "batch_size": 500,
      "concurrent_requests": 3,
      "request_delay_ms": 100
    }
  }
}
```

## Storage Issues

### Issue 5: Redis Memory Issues

#### Symptoms
- Redis out of memory errors
- Data eviction warnings
- Slow response times

#### Diagnostic Steps
```bash
# Check Redis memory usage
kubectl exec -n market-data deployment/redis -- redis-cli INFO memory

# Check Redis configuration
kubectl exec -n market-data deployment/redis -- redis-cli CONFIG GET maxmemory
```

#### Solutions

**Memory Configuration**
```bash
# Increase Redis memory limit
kubectl patch deployment redis -n market-data -p '
{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "redis",
            "resources": {
              "limits": {
                "memory": "4Gi"
              }
            }
          }
        ]
      }
    }
  }
}'

# Configure Redis memory policy
kubectl exec -n market-data deployment/redis -- redis-cli CONFIG SET maxmemory 3gb
kubectl exec -n market-data deployment/redis -- redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

**Data Retention Policy**
```json
{
  "storage": {
    "hot_storage": {
      "retention_days": 3,
      "cleanup_schedule": "0 2 * * *"
    }
  }
}
```

### Issue 6: ClickHouse Query Performance

#### Symptoms
- Slow query responses
- Query timeouts
- High CPU usage

#### Diagnostic Steps
```bash
# Check running queries
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "
SELECT * FROM system.processes WHERE elapsed > 10
"

# Check query log
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "
SELECT query, query_duration_ms, memory_usage 
FROM system.query_log 
WHERE event_time > now() - INTERVAL 1 HOUR 
ORDER BY query_duration_ms DESC 
LIMIT 10
"
```

#### Solutions

**Index Optimization**
```sql
-- Add indexes for common queries
ALTER TABLE market_data.standard_ticks 
ADD INDEX idx_symbol_time (symbol, timestamp_ns) TYPE minmax GRANULARITY 1;

ALTER TABLE market_data.standard_ticks 
ADD INDEX idx_price_range (price) TYPE minmax GRANULARITY 4;
```

**Query Optimization**
```sql
-- Use proper WHERE clauses
SELECT symbol, price, volume, timestamp_ns
FROM market_data.standard_ticks
WHERE symbol = '000001.SZ'
  AND timestamp_ns BETWEEN 1705312800000000000 AND 1705316400000000000
ORDER BY timestamp_ns
LIMIT 1000
SETTINGS max_threads = 4, max_memory_usage = 1000000000;
```

**Table Optimization**
```sql
-- Optimize table settings
ALTER TABLE market_data.standard_ticks 
MODIFY SETTING merge_max_block_size = 8192;

-- Optimize partitions
OPTIMIZE TABLE market_data.standard_ticks PARTITION '202401';
```

## Performance Issues

### Issue 7: High API Latency

#### Symptoms
- API response times > 5 seconds
- Timeout errors
- Poor user experience

#### Diagnostic Steps
```bash
# Check API metrics
curl http://localhost:8080/api/v1/metrics/summary | jq '.performance'

# Test API endpoints
time curl "http://localhost:8080/api/v1/ticks/000001.SZ?limit=100"
```

#### Solutions

**Caching Implementation**
```json
{
  "api": {
    "caching": {
      "enabled": true,
      "ttl_seconds": 300,
      "max_size_mb": 512
    }
  }
}
```

**Query Optimization**
```bash
# Use appropriate limits
curl "http://localhost:8080/api/v1/ticks/000001.SZ?limit=1000&start_time=2024-01-15T09:00:00Z&end_time=2024-01-15T10:00:00Z"

# Use pagination
curl "http://localhost:8080/api/v1/ticks/000001.SZ?limit=1000&cursor=eyJ0aW1lc3RhbXAiOjE3MDUzMTI4MDB9"
```

**Connection Pooling**
```json
{
  "storage": {
    "hot_storage": {
      "config": {
        "max_connections": 100,
        "connection_timeout_seconds": 5
      }
    }
  }
}
```

### Issue 8: Memory Leaks

#### Symptoms
- Gradually increasing memory usage
- Out of memory errors
- Pod restarts

#### Diagnostic Steps
```bash
# Monitor memory usage over time
kubectl top pods -n market-data --containers

# Check for memory leaks in logs
kubectl logs -n market-data deployment/market-data-collector | grep -i "memory\|leak\|oom"

# Get memory profile
curl http://localhost:8080/debug/pprof/heap > heap_profile.prof
```

#### Solutions

**Memory Limits**
```yaml
resources:
  limits:
    memory: "4Gi"
  requests:
    memory: "2Gi"
```

**Garbage Collection Tuning**
```bash
# Set environment variables for Go applications
env:
- name: GOGC
  value: "100"
- name: GOMEMLIMIT
  value: "3GiB"
```

**Connection Pool Management**
```python
# Proper connection cleanup
class ConnectionManager:
    def __init__(self):
        self.pools = {}
    
    def __del__(self):
        for pool in self.pools.values():
            pool.close()
    
    def get_connection(self, service):
        if service not in self.pools:
            self.pools[service] = self.create_pool(service)
        return self.pools[service].get_connection()
```

## Data Quality Issues

### Issue 9: Data Inconsistencies

#### Symptoms
- Different data from different sources
- Missing data points
- Duplicate records

#### Diagnostic Steps
```bash
# Check data quality metrics
curl http://localhost:8080/api/v1/metrics/summary | jq '.data_quality'

# Compare data sources
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "
SELECT 
    data_source,
    count(*) as record_count,
    min(timestamp_ns) as min_time,
    max(timestamp_ns) as max_time
FROM market_data.standard_ticks 
WHERE symbol = '000001.SZ' 
  AND timestamp_ns > 1705312800000000000
GROUP BY data_source
"
```

#### Solutions

**Data Validation Rules**
```json
{
  "data_quality": {
    "validation_rules": {
      "price_range": {
        "min": 0.01,
        "max": 10000
      },
      "volume_range": {
        "min": 0,
        "max": 1000000000
      },
      "timestamp_tolerance_seconds": 300
    }
  }
}
```

**Deduplication Strategy**
```python
def deduplicate_data(data_points):
    """Remove duplicate data points based on symbol and timestamp."""
    seen = set()
    unique_data = []
    
    for point in data_points:
        key = (point.symbol, point.timestamp_ns)
        if key not in seen:
            seen.add(key)
            unique_data.append(point)
    
    return unique_data
```

**Data Reconciliation**
```sql
-- Find and resolve data conflicts
WITH conflicts AS (
    SELECT symbol, timestamp_ns, count(*) as count
    FROM market_data.standard_ticks
    GROUP BY symbol, timestamp_ns
    HAVING count > 1
)
SELECT 
    t.symbol,
    t.timestamp_ns,
    t.data_source,
    t.price,
    t.volume
FROM market_data.standard_ticks t
JOIN conflicts c ON t.symbol = c.symbol AND t.timestamp_ns = c.timestamp_ns
ORDER BY t.symbol, t.timestamp_ns, t.data_source;
```

## Security Issues

### Issue 10: Authentication Failures

#### Symptoms
- 401 Unauthorized errors
- Token validation failures
- Access denied messages

#### Diagnostic Steps
```bash
# Check authentication logs
kubectl logs -n market-data deployment/market-data-collector | grep -i "auth\|token\|unauthorized"

# Test API authentication
curl -v -H "Authorization: Bearer <token>" http://localhost:8080/api/v1/health
```

#### Solutions

**Token Configuration**
```json
{
  "security": {
    "authentication": {
      "enabled": true,
      "type": "bearer_token",
      "token_expiry_hours": 24,
      "secret_key": "${JWT_SECRET_KEY}"
    }
  }
}
```

**Certificate Issues**
```bash
# Check certificate validity
kubectl get secret tls-secret -n market-data -o yaml

# Renew certificates
kubectl create secret tls tls-secret \
  --cert=server.crt \
  --key=server.key \
  -n market-data \
  --dry-run=client -o yaml | kubectl replace -f -
```

## Monitoring and Alerting Issues

### Issue 11: Missing Metrics

#### Symptoms
- Grafana dashboards show no data
- Prometheus targets down
- Missing alerts

#### Diagnostic Steps
```bash
# Check Prometheus targets
curl http://prometheus:9090/api/v1/targets

# Check metrics endpoint
curl http://localhost:9090/metrics | grep market_data

# Check Prometheus configuration
kubectl get configmap prometheus-config -n market-data -o yaml
```

#### Solutions

**Metrics Endpoint Configuration**
```yaml
# Ensure metrics are exposed
ports:
- containerPort: 9090
  name: metrics
  protocol: TCP

# Add Prometheus annotations
annotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "9090"
  prometheus.io/path: "/metrics"
```

**Service Monitor Configuration**
```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: market-data-collector
  namespace: market-data
spec:
  selector:
    matchLabels:
      app: market-data-collector
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
```

## Best Practices for Issue Prevention

### 1. Proactive Monitoring
```bash
# Set up comprehensive monitoring
kubectl apply -f monitoring-alerting-templates.yaml

# Regular health checks
*/5 * * * * curl -f http://localhost:8080/health || echo "Health check failed"
```

### 2. Configuration Management
```bash
# Validate configuration before deployment
kubectl create configmap test-config --from-file=config.json --dry-run=client

# Use configuration versioning
kubectl label configmap market-data-config version=v1.2.3
```

### 3. Resource Planning
```yaml
# Set appropriate resource requests and limits
resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "4Gi"
    cpu: "2000m"
```

### 4. Regular Maintenance
```bash
# Schedule regular maintenance tasks
0 2 * * * kubectl exec -n market-data deployment/redis -- redis-cli BGREWRITEAOF
0 3 * * * kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "OPTIMIZE TABLE market_data.standard_ticks"
```

### 5. Backup and Recovery
```bash
# Regular backups
0 1 * * * kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "BACKUP TABLE market_data.standard_ticks TO S3('s3://backup-bucket/clickhouse/', 'access_key', 'secret_key')"
```

This comprehensive guide should help resolve most common issues encountered in the Market Data Collection Enhancement system. For issues not covered here, refer to the troubleshooting guide and contact the development team with detailed logs and error descriptions.