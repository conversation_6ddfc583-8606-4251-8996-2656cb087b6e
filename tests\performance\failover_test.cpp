/**
 * @file failover_test.cpp
 * @brief Failover and disaster recovery testing implementation
 */

#include "failover_test.h"
#include "test_utils.h"
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <future>
#include <iostream>

namespace performance_tests {

FailoverTest::FailoverTest() : test_utils_(std::make_unique<TestUtils>()) {}

FailoverTest::~FailoverTest() = default;

FailoverResult FailoverTest::TestPrimaryServerFailover() {
    std::cout << "    Testing primary server failover..." << std::endl;
    
    // Setup primary and backup servers
    auto primary_server = test_utils_->CreateMockMarketDataServer("primary");
    auto backup_server = test_utils_->CreateMockMarketDataServer("backup");
    auto load_balancer = test_utils_->CreateMockLoadBalancer();
    
    // Setup test clients
    const uint32_t num_clients = 50;
    std::vector<std::unique_ptr<MockTestClient>> clients;
    
    for (uint32_t i = 0; i < num_clients; ++i) {
        auto client = test_utils_->CreateTestClient();
        client->Connect(load_balancer->GetPrimaryEndpoint());
        client->Subscribe({"CU2409", "AL2409", "ZN2409"});
        clients.push_back(std::move(client));
    }
    
    // Start data flow
    auto tick_generator = test_utils_->CreateTickGenerator();
    std::atomic<bool> stop_data_flow{false};
    std::atomic<uint64_t> messages_sent_before_failover{0};
    std::atomic<uint64_t> messages_sent_after_failover{0};
    std::atomic<uint64_t> messages_lost_during_failover{0};
    
    // Data producer thread
    auto producer_future = std::async(std::launch::async, [&]() {
        bool failover_triggered = false;
        uint64_t sequence = 1;
        
        while (!stop_data_flow.load()) {
            auto tick = tick_generator->GenerateTestTick();
            tick.sequence = sequence++;
            
            bool sent = primary_server->IsActive() ? 
                       primary_server->PublishTick(tick) : 
                       backup_server->PublishTick(tick);
            
            if (sent) {
                if (!failover_triggered) {
                    messages_sent_before_failover.fetch_add(1);
                } else {
                    messages_sent_after_failover.fetch_add(1);
                }
            } else {
                messages_lost_during_failover.fetch_add(1);
            }
            
            // Trigger failover after 5 seconds
            if (!failover_triggered && messages_sent_before_failover.load() > 5000) {
                failover_triggered = true;
            }
            
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    });
    
    // Client monitoring threads
    std::vector<std::future<void>> client_futures;
    std::atomic<uint64_t> total_messages_received{0};
    std::atomic<uint64_t> clients_reconnected{0};
    
    for (auto& client : clients) {
        client_futures.push_back(std::async(std::launch::async, [&, &client]() {
            uint64_t received_count = 0;
            bool reconnected = false;
            
            while (!stop_data_flow.load()) {
                auto message = client->WaitForMessage(std::chrono::milliseconds(100));
                
                if (message.has_value()) {
                    received_count++;
                } else if (!client->IsConnected() && !reconnected) {
                    // Client disconnected, wait for reconnection
                    if (client->WaitForReconnection(std::chrono::seconds(10))) {
                        reconnected = true;
                        clients_reconnected.fetch_add(1);
                    }
                }
            }
            
            total_messages_received.fetch_add(received_count);
        }));
    }
    
    // Run for 5 seconds before triggering failover
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Trigger primary server failure
    auto failover_start = std::chrono::high_resolution_clock::now();
    
    std::cout << "      Triggering primary server failure..." << std::endl;
    primary_server->SimulateFailure();
    
    // Wait for load balancer to detect failure and switch to backup
    auto failover_detected = load_balancer->WaitForFailoverDetection(std::chrono::seconds(10));
    
    if (failover_detected) {
        load_balancer->SwitchToBackup();
        backup_server->Activate();
    }
    
    auto failover_end = std::chrono::high_resolution_clock::now();
    auto failover_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        failover_end - failover_start).count();
    
    std::cout << "      Failover completed in " << failover_time_ms << "ms" << std::endl;
    
    // Continue running for another 5 seconds to verify recovery
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    stop_data_flow.store(true);
    
    // Wait for all threads to complete
    producer_future.wait();
    for (auto& future : client_futures) {
        future.wait();
    }
    
    // Verify backup server is handling traffic
    bool backup_active = backup_server->IsActive();
    uint64_t backup_message_count = backup_server->GetProcessedMessageCount();
    
    FailoverResult result;
    result.failover_time_ms = static_cast<uint32_t>(failover_time_ms);
    result.failover_success = failover_detected && backup_active;
    result.messages_lost_during_failover = messages_lost_during_failover.load();
    result.recovery_time_ms = static_cast<uint32_t>(failover_time_ms); // Same as failover time
    result.recovery_success = backup_active && (backup_message_count > 0);
    result.recovered_messages = messages_sent_after_failover.load();
    
    std::cout << "      Failover success: " << (result.failover_success ? "Yes" : "No") << std::endl;
    std::cout << "      Messages lost during failover: " << result.messages_lost_during_failover << std::endl;
    std::cout << "      Clients reconnected: " << clients_reconnected.load() << "/" << num_clients << std::endl;
    std::cout << "      Backup server active: " << (backup_active ? "Yes" : "No") << std::endl;
    
    return result;
}

FailoverResult FailoverTest::TestDatabaseFailover() {
    std::cout << "    Testing database failover..." << std::endl;
    
    // Setup primary and backup databases
    auto primary_redis = test_utils_->CreateRedisTestClient("primary");
    auto backup_redis = test_utils_->CreateRedisTestClient("backup");
    auto primary_clickhouse = test_utils_->CreateClickHouseTestClient("primary");
    auto backup_clickhouse = test_utils_->CreateClickHouseTestClient("backup");
    
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    std::atomic<uint64_t> writes_before_failover{0};
    std::atomic<uint64_t> writes_after_failover{0};
    std::atomic<uint64_t> failed_writes{0};
    std::atomic<bool> stop_test{false};
    
    // Database writer thread
    auto writer_future = std::async(std::launch::async, [&]() {
        bool failover_triggered = false;
        uint64_t sequence = 1;
        
        while (!stop_test.load()) {
            auto tick = tick_generator->GenerateTestTick();
            tick.sequence = sequence++;
            
            bool redis_success = false;
            bool clickhouse_success = false;
            
            // Try primary databases first
            if (!failover_triggered) {
                redis_success = primary_redis->StoreTick(tick);
                clickhouse_success = primary_clickhouse->StoreTick(tick);
                
                if (redis_success && clickhouse_success) {
                    writes_before_failover.fetch_add(1);
                } else {
                    // Try backup databases
                    if (!redis_success) {
                        redis_success = backup_redis->StoreTick(tick);
                    }
                    if (!clickhouse_success) {
                        clickhouse_success = backup_clickhouse->StoreTick(tick);
                    }
                    
                    if (redis_success && clickhouse_success) {
                        writes_after_failover.fetch_add(1);
                        failover_triggered = true;
                    } else {
                        failed_writes.fetch_add(1);
                    }
                }
            } else {
                // Use backup databases
                redis_success = backup_redis->StoreTick(tick);
                clickhouse_success = backup_clickhouse->StoreTick(tick);
                
                if (redis_success && clickhouse_success) {
                    writes_after_failover.fetch_add(1);
                } else {
                    failed_writes.fetch_add(1);
                }
            }
            
            std::this_thread::sleep_for(std::chrono::microseconds(500));
        }
    });
    
    // Run for 10 seconds before triggering database failure
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    // Trigger database failure
    auto failover_start = std::chrono::high_resolution_clock::now();
    
    std::cout << "      Triggering primary database failure..." << std::endl;
    primary_redis->SimulateFailure();
    primary_clickhouse->SimulateFailure();
    
    // Wait for failover detection and recovery
    auto redis_failover_detected = backup_redis->WaitForActivation(std::chrono::seconds(5));
    auto clickhouse_failover_detected = backup_clickhouse->WaitForActivation(std::chrono::seconds(5));
    
    auto failover_end = std::chrono::high_resolution_clock::now();
    auto failover_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        failover_end - failover_start).count();
    
    std::cout << "      Database failover completed in " << failover_time_ms << "ms" << std::endl;
    
    // Continue running for another 10 seconds to verify recovery
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    stop_test.store(true);
    writer_future.wait();
    
    // Verify backup databases are working
    bool redis_backup_active = backup_redis->IsActive();
    bool clickhouse_backup_active = backup_clickhouse->IsActive();
    
    FailoverResult result;
    result.failover_time_ms = static_cast<uint32_t>(failover_time_ms);
    result.failover_success = redis_failover_detected && clickhouse_failover_detected;
    result.messages_lost_during_failover = failed_writes.load();
    result.recovery_time_ms = static_cast<uint32_t>(failover_time_ms);
    result.recovery_success = redis_backup_active && clickhouse_backup_active;
    result.recovered_messages = writes_after_failover.load();
    
    std::cout << "      Database failover success: " << (result.failover_success ? "Yes" : "No") << std::endl;
    std::cout << "      Failed writes: " << result.messages_lost_during_failover << std::endl;
    std::cout << "      Writes after failover: " << result.recovered_messages << std::endl;
    std::cout << "      Redis backup active: " << (redis_backup_active ? "Yes" : "No") << std::endl;
    std::cout << "      ClickHouse backup active: " << (clickhouse_backup_active ? "Yes" : "No") << std::endl;
    
    return result;
}

FailoverResult FailoverTest::TestNetworkFailover() {
    std::cout << "    Testing network failover..." << std::endl;
    
    // Setup network simulation
    auto network_simulator = test_utils_->CreateNetworkSimulator();
    auto primary_endpoint = "tcp://primary.example.com:5555";
    auto backup_endpoint = "tcp://backup.example.com:5555";
    
    // Setup clients with failover capability
    const uint32_t num_clients = 20;
    std::vector<std::unique_ptr<MockTestClient>> clients;
    
    for (uint32_t i = 0; i < num_clients; ++i) {
        auto client = test_utils_->CreateTestClient();
        client->SetPrimaryEndpoint(primary_endpoint);
        client->SetBackupEndpoint(backup_endpoint);
        client->Connect();
        clients.push_back(std::move(client));
    }
    
    std::atomic<uint64_t> messages_before_failure{0};
    std::atomic<uint64_t> messages_after_recovery{0};
    std::atomic<uint64_t> clients_recovered{0};
    std::atomic<bool> stop_test{false};
    
    // Message monitoring
    std::vector<std::future<void>> client_futures;
    for (auto& client : clients) {
        client_futures.push_back(std::async(std::launch::async, [&, &client]() {
            bool network_failed = false;
            uint64_t received_count = 0;
            
            while (!stop_test.load()) {
                auto message = client->WaitForMessage(std::chrono::milliseconds(100));
                
                if (message.has_value()) {
                    if (!network_failed) {
                        messages_before_failure.fetch_add(1);
                    } else {
                        messages_after_recovery.fetch_add(1);
                    }
                    received_count++;
                } else if (!client->IsConnected() && !network_failed) {
                    network_failed = true;
                    
                    // Try to reconnect to backup
                    if (client->ReconnectToBackup(std::chrono::seconds(5))) {
                        clients_recovered.fetch_add(1);
                    }
                }
            }
        }));
    }
    
    // Run for 5 seconds with normal network
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Simulate network failure
    auto failover_start = std::chrono::high_resolution_clock::now();
    
    std::cout << "      Simulating network failure..." << std::endl;
    network_simulator->SimulateNetworkPartition(primary_endpoint);
    
    // Wait for clients to detect failure and reconnect
    std::this_thread::sleep_for(std::chrono::seconds(3));
    
    // Restore network to backup endpoint
    network_simulator->RestoreNetwork(backup_endpoint);
    
    auto failover_end = std::chrono::high_resolution_clock::now();
    auto failover_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        failover_end - failover_start).count();
    
    std::cout << "      Network failover completed in " << failover_time_ms << "ms" << std::endl;
    
    // Continue running for another 5 seconds
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    stop_test.store(true);
    
    // Wait for all client threads
    for (auto& future : client_futures) {
        future.wait();
    }
    
    FailoverResult result;
    result.failover_time_ms = static_cast<uint32_t>(failover_time_ms);
    result.failover_success = clients_recovered.load() > (num_clients * 0.8); // 80% recovery rate
    result.messages_lost_during_failover = 0; // Network failover shouldn't lose messages
    result.recovery_time_ms = static_cast<uint32_t>(failover_time_ms);
    result.recovery_success = clients_recovered.load() > 0;
    result.recovered_messages = messages_after_recovery.load();
    
    std::cout << "      Network failover success: " << (result.failover_success ? "Yes" : "No") << std::endl;
    std::cout << "      Clients recovered: " << clients_recovered.load() << "/" << num_clients << std::endl;
    std::cout << "      Messages after recovery: " << result.recovered_messages << std::endl;
    
    return result;
}

FailoverResult FailoverTest::TestDataRecoveryAfterFailover() {
    std::cout << "    Testing data recovery after failover..." << std::endl;
    
    // Setup primary and backup storage
    auto primary_storage = test_utils_->CreateMockStorage("primary");
    auto backup_storage = test_utils_->CreateMockStorage("backup");
    auto recovery_manager = test_utils_->CreateRecoveryManager();
    
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    // Generate test data and store in primary
    const uint32_t test_messages = 10000;
    std::vector<TestTick> test_data;
    
    for (uint32_t i = 0; i < test_messages; ++i) {
        auto tick = tick_generator->GenerateTestTick();
        tick.sequence = i + 1;
        tick.timestamp = std::chrono::system_clock::now().time_since_epoch().count() + i * 1000000;
        
        test_data.push_back(tick);
        primary_storage->StoreTick(tick);
        
        // Store only half in backup to simulate data loss
        if (i < test_messages / 2) {
            backup_storage->StoreTick(tick);
        }
    }
    
    // Simulate primary storage failure
    std::cout << "      Simulating primary storage failure..." << std::endl;
    primary_storage->SimulateFailure();
    
    // Start recovery process
    auto recovery_start = std::chrono::high_resolution_clock::now();
    
    // Recovery manager should detect missing data and recover it
    auto recovery_future = std::async(std::launch::async, [&]() {
        return recovery_manager->RecoverMissingData(
            backup_storage.get(),
            primary_storage.get(),
            std::chrono::system_clock::now() - std::chrono::hours(1),
            std::chrono::system_clock::now()
        );
    });
    
    // Wait for recovery to complete
    auto recovery_result = recovery_future.get();
    
    auto recovery_end = std::chrono::high_resolution_clock::now();
    auto recovery_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        recovery_end - recovery_start).count();
    
    std::cout << "      Data recovery completed in " << recovery_time_ms << "ms" << std::endl;
    
    // Verify recovery results
    uint64_t recovered_messages = recovery_result.recovered_message_count;
    uint64_t missing_messages = test_messages - backup_storage->GetMessageCount();
    
    // Check data integrity after recovery
    uint64_t verified_messages = 0;
    for (const auto& original_tick : test_data) {
        auto recovered_tick = backup_storage->GetTickBySequence(original_tick.symbol, original_tick.sequence);
        
        if (recovered_tick.has_value() && 
            recovered_tick->sequence == original_tick.sequence &&
            recovered_tick->symbol == original_tick.symbol) {
            verified_messages++;
        }
    }
    
    FailoverResult result;
    result.failover_time_ms = 0; // Not applicable for recovery test
    result.failover_success = true; // Recovery process itself
    result.messages_lost_during_failover = missing_messages;
    result.recovery_time_ms = static_cast<uint32_t>(recovery_time_ms);
    result.recovery_success = recovery_result.success;
    result.recovered_messages = recovered_messages;
    
    double recovery_rate = static_cast<double>(verified_messages) / test_messages;
    
    std::cout << "      Recovery success: " << (result.recovery_success ? "Yes" : "No") << std::endl;
    std::cout << "      Messages recovered: " << recovered_messages << std::endl;
    std::cout << "      Messages verified: " << verified_messages << "/" << test_messages << std::endl;
    std::cout << "      Recovery rate: " << (recovery_rate * 100) << "%" << std::endl;
    
    return result;
}

} // namespace performance_tests