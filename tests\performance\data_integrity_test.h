/**
 * @file data_integrity_test.h
 * @brief Data integrity testing for financial data service
 */

#pragma once

#include "benchmark_runner.h"
#include <memory>

namespace performance_tests {

class TestUtils;

/**
 * @class DataIntegrityTest
 * @brief Comprehensive data integrity testing
 * 
 * Tests to ensure zero data loss and data consistency across the system
 */
class DataIntegrityTest {
public:
    DataIntegrityTest();
    ~DataIntegrityTest();
    
    /**
     * @brief Test zero data loss under normal and stress conditions
     * @return DataIntegrityResult with data loss statistics
     * 
     * Requirement: Zero data loss (Req 1.5, 2.1)
     */
    DataIntegrityResult TestZeroDataLoss();
    
    /**
     * @brief Test sequence number continuity
     * @return DataIntegrityResult with sequence gap analysis
     * 
     * Ensures no gaps in sequence numbers indicating lost messages
     */
    DataIntegrityResult TestSequenceNumberContinuity();
    
    /**
     * @brief Test data consistency across storage layers
     * @return DataIntegrityResult with consistency statistics
     * 
     * Verifies data consistency between hot, warm, and cold storage
     */
    DataIntegrityResult TestDataConsistencyAcrossLayers();
    
    /**
     * @brief Test timestamp accuracy and precision
     * @return DataIntegrityResult with timestamp accuracy metrics
     * 
     * Requirement: Nanosecond precision timestamps (Req 8.3)
     */
    DataIntegrityResult TestTimestampAccuracy();

private:
    std::unique_ptr<TestUtils> test_utils_;
};

} // namespace performance_tests