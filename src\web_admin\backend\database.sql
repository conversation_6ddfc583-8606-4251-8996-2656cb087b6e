-- 金融数据服务系统管理界面数据库初始化脚本
-- Financial Data Service System Admin Database Initialization

-- 创建数据库
CREATE DATABASE financial_data_admin;

-- 连接到数据库
\c financial_data_admin;

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'operator', 'viewer')),
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 告警配置表
CREATE TABLE alert_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    metric VARCHAR(50) NOT NULL,
    threshold DECIMAL(10,4) NOT NULL,
    operator VARCHAR(5) NOT NULL CHECK (operator IN ('>', '<', '>=', '<=', '==')),
    enabled BOOLEAN DEFAULT true,
    notification_channels JSONB NOT NULL DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 系统配置表
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category VARCHAR(50) NOT NULL,
    key VARCHAR(100) NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    requires_restart BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(category, key)
);

-- 审计日志表
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(100),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 系统指标历史表
CREATE TABLE system_metrics_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    disk_usage DECIMAL(5,2),
    network_rx_bytes BIGINT,
    network_tx_bytes BIGINT,
    active_connections INTEGER,
    data_throughput DECIMAL(15,2),
    latency_p99 DECIMAL(8,3),
    error_rate DECIMAL(8,6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_alert_configs_enabled ON alert_configs(enabled);
CREATE INDEX idx_system_configs_category ON system_configs(category);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_system_metrics_timestamp ON system_metrics_history(timestamp);

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO users (username, email, password_hash, role) VALUES 
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx/L/jyG', 'admin');

-- 插入默认系统配置
INSERT INTO system_configs (category, key, value, description, requires_restart) VALUES
('data_collection', 'max_connections', '1000', '最大并发连接数', true),
('data_collection', 'buffer_size', '1048576', '数据缓冲区大小(字节)', true),
('data_collection', 'timeout_seconds', '30', '连接超时时间(秒)', false),
('storage', 'hot_data_retention_days', '7', '热数据保留天数', false),
('storage', 'warm_data_retention_years', '2', '温数据保留年数', false),
('storage', 'compression_ratio_target', '8.0', '目标压缩比', false),
('monitoring', 'metrics_collection_interval', '1', '指标收集间隔(秒)', false),
('monitoring', 'alert_check_interval', '5', '告警检查间隔(秒)', false),
('api', 'rate_limit_per_minute', '1000', 'API速率限制(每分钟)', false),
('api', 'max_query_range_days', '30', '最大查询时间范围(天)', false);

-- 插入默认告警配置
INSERT INTO alert_configs (name, metric, threshold, operator, notification_channels) VALUES
('CPU使用率过高', 'cpu_usage', 85.0, '>', '["email", "sms"]'),
('内存使用率过高', 'memory_usage', 85.0, '>', '["email"]'),
('磁盘使用率过高', 'disk_usage', 90.0, '>', '["email", "sms"]'),
('延迟过高', 'latency_p99', 50.0, '>', '["email", "sms", "webhook"]'),
('错误率过高', 'error_rate', 0.01, '>', '["email", "sms"]'),
('连接数过多', 'active_connections', 900, '>', '["email"]');

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间戳触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alert_configs_updated_at BEFORE UPDATE ON alert_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();