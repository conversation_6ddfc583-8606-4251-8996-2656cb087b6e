#include "prometheus_metrics.h"
#include <iostream>
#include <vector>

namespace monitoring {

PrometheusMetrics& PrometheusMetrics::getInstance() {
    static PrometheusMetrics instance;
    return instance;
}

bool PrometheusMetrics::initialize(const std::string& bind_address) {
    try {
        registry_ = std::make_shared<prometheus::Registry>();
        exposer_ = std::make_unique<prometheus::Exposer>(bind_address);
        exposer_->RegisterCollectable(registry_);
        
        initializeMetrics();
        
        std::cout << "Prometheus metrics server started on " << bind_address << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize Prometheus metrics: " << e.what() << std::endl;
        return false;
    }
}

void PrometheusMetrics::initializeMetrics() {
    // Latency metrics
    latency_histogram_family_ = &prometheus::BuildHistogram()
        .Name("financial_data_latency_microseconds")
        .Help("Latency of various operations in microseconds")
        .Register(*registry_);
    
    end_to_end_latency_family_ = &prometheus::BuildHistogram()
        .Name("financial_data_end_to_end_latency_microseconds")
        .Help("End-to-end latency from data source to client in microseconds")
        .Register(*registry_);
    
    // Data integrity metrics
    data_received_family_ = &prometheus::BuildCounter()
        .Name("financial_data_received_total")
        .Help("Total number of data messages received")
        .Register(*registry_);
    
    data_loss_family_ = &prometheus::BuildCounter()
        .Name("financial_data_loss_total")
        .Help("Total number of data messages lost")
        .Register(*registry_);
    
    sequence_gap_family_ = &prometheus::BuildHistogram()
        .Name("financial_data_sequence_gap")
        .Help("Size of sequence gaps detected")
        .Register(*registry_);
    
    // Resource metrics
    cpu_usage_gauge_ = &prometheus::BuildGauge()
        .Name("system_cpu_usage_percent")
        .Help("CPU usage percentage")
        .Register(*registry_)
        .Add({});
    
    memory_usage_gauge_ = &prometheus::BuildGauge()
        .Name("system_memory_usage_percent")
        .Help("Memory usage percentage")
        .Register(*registry_)
        .Add({});
    
    disk_usage_gauge_ = &prometheus::BuildGauge()
        .Name("system_disk_usage_percent")
        .Help("Disk usage percentage")
        .Register(*registry_)
        .Add({});
    
    network_bandwidth_gauge_ = &prometheus::BuildGauge()
        .Name("system_network_bandwidth_bytes_per_second")
        .Help("Network bandwidth in bytes per second")
        .Register(*registry_)
        .Add({});
    
    // System health metrics
    connection_count_gauge_ = &prometheus::BuildGauge()
        .Name("financial_data_connections_active")
        .Help("Number of active client connections")
        .Register(*registry_)
        .Add({});
    
    queue_size_family_ = &prometheus::BuildGauge()
        .Name("financial_data_queue_size")
        .Help("Size of various queues in the system")
        .Register(*registry_);
    
    throughput_family_ = &prometheus::BuildGauge()
        .Name("financial_data_throughput_messages_per_second")
        .Help("Throughput of various components in messages per second")
        .Register(*registry_);
    
    // Alert metrics
    alert_counter_family_ = &prometheus::BuildCounter()
        .Name("financial_data_alerts_total")
        .Help("Total number of alerts triggered")
        .Register(*registry_);
}

prometheus::Histogram::BucketBoundaries PrometheusMetrics::createLatencyBuckets() {
    // Buckets optimized for microsecond latency measurements
    return {1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000};
}

void PrometheusMetrics::recordLatency(const std::string& operation, double latency_microseconds) {
    auto it = latency_histograms_.find(operation);
    if (it == latency_histograms_.end()) {
        auto& histogram = latency_histogram_family_->Add(
            {{"operation", operation}}, createLatencyBuckets());
        latency_histograms_[operation] = &histogram;
        it = latency_histograms_.find(operation);
    }
    it->second->Observe(latency_microseconds);
}

void PrometheusMetrics::recordEndToEndLatency(double latency_microseconds) {
    static auto& histogram = end_to_end_latency_family_->Add({}, createLatencyBuckets());
    histogram.Observe(latency_microseconds);
}

void PrometheusMetrics::incrementDataReceived(const std::string& symbol) {
    auto it = data_received_counters_.find(symbol);
    if (it == data_received_counters_.end()) {
        auto& counter = data_received_family_->Add({{"symbol", symbol}});
        data_received_counters_[symbol] = &counter;
        it = data_received_counters_.find(symbol);
    }
    it->second->Increment();
}

void PrometheusMetrics::incrementDataLoss(const std::string& symbol) {
    auto it = data_loss_counters_.find(symbol);
    if (it == data_loss_counters_.end()) {
        auto& counter = data_loss_family_->Add({{"symbol", symbol}});
        data_loss_counters_[symbol] = &counter;
        it = data_loss_counters_.find(symbol);
    }
    it->second->Increment();
}

void PrometheusMetrics::recordSequenceGap(const std::string& symbol, int gap_size) {
    static auto& histogram = sequence_gap_family_->Add(
        {{"symbol", symbol}}, {1, 2, 5, 10, 25, 50, 100, 250, 500, 1000});
    histogram.Observe(gap_size);
}

void PrometheusMetrics::updateCpuUsage(double percentage) {
    cpu_usage_gauge_->Set(percentage);
}

void PrometheusMetrics::updateMemoryUsage(double percentage) {
    memory_usage_gauge_->Set(percentage);
}

void PrometheusMetrics::updateDiskUsage(double percentage) {
    disk_usage_gauge_->Set(percentage);
}

void PrometheusMetrics::updateNetworkBandwidth(double bytes_per_second) {
    network_bandwidth_gauge_->Set(bytes_per_second);
}

void PrometheusMetrics::incrementConnectionCount() {
    connection_count_gauge_->Increment();
}

void PrometheusMetrics::decrementConnectionCount() {
    connection_count_gauge_->Decrement();
}

void PrometheusMetrics::updateQueueSize(const std::string& queue_name, int size) {
    auto it = queue_size_gauges_.find(queue_name);
    if (it == queue_size_gauges_.end()) {
        auto& gauge = queue_size_family_->Add({{"queue", queue_name}});
        queue_size_gauges_[queue_name] = &gauge;
        it = queue_size_gauges_.find(queue_name);
    }
    it->second->Set(size);
}

void PrometheusMetrics::recordThroughput(const std::string& component, double messages_per_second) {
    auto it = throughput_gauges_.find(component);
    if (it == throughput_gauges_.end()) {
        auto& gauge = throughput_family_->Add({{"component", component}});
        throughput_gauges_[component] = &gauge;
        it = throughput_gauges_.find(component);
    }
    it->second->Set(messages_per_second);
}

void PrometheusMetrics::incrementAlert(const std::string& alert_type, const std::string& severity) {
    static std::unordered_map<std::string, prometheus::Counter*> alert_counters;
    std::string key = alert_type + "_" + severity;
    
    auto it = alert_counters.find(key);
    if (it == alert_counters.end()) {
        auto& counter = alert_counter_family_->Add({
            {"type", alert_type}, 
            {"severity", severity}
        });
        alert_counters[key] = &counter;
        it = alert_counters.find(key);
    }
    it->second->Increment();
}

} // namespace monitoring