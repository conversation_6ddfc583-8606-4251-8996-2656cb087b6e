#!/usr/bin/env python3
"""
Demo script for the RESTful API historical data interface
Shows how to use the API endpoints for querying historical market data
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, Any


class FinancialDataAPIClient:
    """Client for the Financial Data Service API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check API health"""
        async with self.session.get(f"{self.base_url}/health") as response:
            return await response.json()
    
    async def get_tick_data(
        self, 
        symbol: str, 
        exchange: str = None,
        start_time: datetime = None,
        end_time: datetime = None,
        limit: int = 1000,
        cursor: str = None
    ) -> Dict[str, Any]:
        """Query tick data"""
        params = {"symbol": symbol, "limit": limit}
        
        if exchange:
            params["exchange"] = exchange
        if start_time:
            params["start_time"] = start_time.isoformat()
        if end_time:
            params["end_time"] = end_time.isoformat()
        if cursor:
            params["cursor"] = cursor
        
        async with self.session.get(
            f"{self.base_url}/api/v1/tick-data", 
            params=params
        ) as response:
            return await response.json()
    
    async def get_kline_data(
        self,
        symbol: str,
        period: str = "1m",
        exchange: str = None,
        start_time: datetime = None,
        end_time: datetime = None,
        limit: int = 1000
    ) -> Dict[str, Any]:
        """Query K-line data"""
        params = {"symbol": symbol, "period": period, "limit": limit}
        
        if exchange:
            params["exchange"] = exchange
        if start_time:
            params["start_time"] = start_time.isoformat()
        if end_time:
            params["end_time"] = end_time.isoformat()
        
        async with self.session.get(
            f"{self.base_url}/api/v1/kline-data",
            params=params
        ) as response:
            return await response.json()
    
    async def get_level2_data(
        self,
        symbol: str,
        exchange: str = None,
        start_time: datetime = None,
        end_time: datetime = None,
        limit: int = 1000
    ) -> Dict[str, Any]:
        """Query Level-2 data"""
        params = {"symbol": symbol, "limit": limit}
        
        if exchange:
            params["exchange"] = exchange
        if start_time:
            params["start_time"] = start_time.isoformat()
        if end_time:
            params["end_time"] = end_time.isoformat()
        
        async with self.session.get(
            f"{self.base_url}/api/v1/level2-data",
            params=params
        ) as response:
            return await response.json()
    
    async def get_symbols(
        self, 
        exchange: str = None, 
        product_type: str = None
    ) -> Dict[str, Any]:
        """Get available symbols"""
        params = {}
        if exchange:
            params["exchange"] = exchange
        if product_type:
            params["product_type"] = product_type
        
        async with self.session.get(
            f"{self.base_url}/api/v1/symbols",
            params=params
        ) as response:
            return await response.json()
    
    async def get_exchanges(self) -> Dict[str, Any]:
        """Get available exchanges"""
        async with self.session.get(f"{self.base_url}/api/v1/exchanges") as response:
            return await response.json()


async def demo_api_usage():
    """Demonstrate API usage"""
    print("=== Financial Data Service API Demo ===\n")
    
    async with FinancialDataAPIClient() as client:
        try:
            # 1. Health check
            print("1. Health Check:")
            health = await client.health_check()
            print(f"   Status: {health.get('status', 'unknown')}")
            print(f"   Timestamp: {health.get('timestamp', 'unknown')}\n")
            
            # 2. Get available exchanges
            print("2. Available Exchanges:")
            exchanges = await client.get_exchanges()
            print(f"   Exchanges: {exchanges}\n")
            
            # 3. Get available symbols
            print("3. Available Symbols (first 5):")
            symbols = await client.get_symbols()
            if isinstance(symbols, list) and len(symbols) > 0:
                for symbol in symbols[:5]:
                    print(f"   {symbol.get('symbol', 'N/A')} @ {symbol.get('exchange', 'N/A')} ({symbol.get('product_type', 'N/A')})")
            else:
                print("   No symbols available (this is expected in demo mode)")
            print()
            
            # 4. Query tick data
            print("4. Tick Data Query:")
            try:
                tick_data = await client.get_tick_data(
                    symbol="CU2409",
                    exchange="SHFE",
                    limit=5
                )
                print(f"   Success: {tick_data.get('success', False)}")
                print(f"   Records: {len(tick_data.get('data', []))}")
                print(f"   Message: {tick_data.get('message', 'N/A')}")
                
                if tick_data.get('data'):
                    sample_tick = tick_data['data'][0]
                    print(f"   Sample tick: {sample_tick.get('symbol', 'N/A')} @ {sample_tick.get('last_price', 'N/A')}")
            except Exception as e:
                print(f"   Error: {e} (expected in demo mode)")
            print()
            
            # 5. Query K-line data
            print("5. K-line Data Query:")
            try:
                kline_data = await client.get_kline_data(
                    symbol="CU2409",
                    period="1m",
                    exchange="SHFE",
                    limit=5
                )
                print(f"   Success: {kline_data.get('success', False)}")
                print(f"   Records: {len(kline_data.get('data', []))}")
                print(f"   Message: {kline_data.get('message', 'N/A')}")
            except Exception as e:
                print(f"   Error: {e} (expected in demo mode)")
            print()
            
            # 6. Query Level-2 data
            print("6. Level-2 Data Query:")
            try:
                level2_data = await client.get_level2_data(
                    symbol="CU2409",
                    exchange="SHFE",
                    limit=5
                )
                print(f"   Success: {level2_data.get('success', False)}")
                print(f"   Records: {len(level2_data.get('data', []))}")
                print(f"   Message: {level2_data.get('message', 'N/A')}")
            except Exception as e:
                print(f"   Error: {e} (expected in demo mode)")
            print()
            
            # 7. Demonstrate pagination
            print("7. Pagination Demo:")
            try:
                page1 = await client.get_tick_data(
                    symbol="CU2409",
                    limit=2
                )
                print(f"   Page 1 records: {len(page1.get('data', []))}")
                
                pagination = page1.get('pagination', {})
                if pagination.get('has_next') and pagination.get('next_cursor'):
                    page2 = await client.get_tick_data(
                        symbol="CU2409",
                        limit=2,
                        cursor=pagination['next_cursor']
                    )
                    print(f"   Page 2 records: {len(page2.get('data', []))}")
                else:
                    print("   No next page available")
            except Exception as e:
                print(f"   Error: {e} (expected in demo mode)")
            print()
            
            # 8. Error handling demo
            print("8. Error Handling Demo:")
            try:
                # Invalid symbol
                error_response = await client.get_tick_data(symbol="")
                print(f"   Unexpected success: {error_response}")
            except aiohttp.ClientResponseError as e:
                print(f"   Expected error for empty symbol: HTTP {e.status}")
            except Exception as e:
                print(f"   Error: {e}")
            
            try:
                # Invalid period
                error_response = await client.get_kline_data(
                    symbol="CU2409", 
                    period="invalid"
                )
                print(f"   Unexpected success: {error_response}")
            except aiohttp.ClientResponseError as e:
                print(f"   Expected error for invalid period: HTTP {e.status}")
            except Exception as e:
                print(f"   Error: {e}")
            
        except aiohttp.ClientConnectorError:
            print("❌ Could not connect to API server.")
            print("   Please make sure the API server is running on http://localhost:8000")
            print("   You can start it with: python src/api/run_server.py")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")


def print_api_info():
    """Print API information"""
    print("=== API Endpoints ===")
    print("Health Check:     GET  /health")
    print("Tick Data:        GET  /api/v1/tick-data")
    print("K-line Data:      GET  /api/v1/kline-data")
    print("Level-2 Data:     GET  /api/v1/level2-data")
    print("Symbols:          GET  /api/v1/symbols")
    print("Exchanges:        GET  /api/v1/exchanges")
    print()
    print("API Documentation: http://localhost:8000/docs")
    print("ReDoc:            http://localhost:8000/redoc")
    print()


if __name__ == "__main__":
    print_api_info()
    asyncio.run(demo_api_usage())