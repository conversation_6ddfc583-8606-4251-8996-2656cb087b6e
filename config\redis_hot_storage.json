{"redis_config": {"single_node": {"host": "127.0.0.1", "port": 6379, "password": "", "database": 0, "connection_timeout_ms": 5000, "command_timeout_ms": 1000, "max_connections": 20}, "cluster": {"enable_cluster": true, "cluster_nodes": ["127.0.0.1:7001", "127.0.0.1:7002", "127.0.0.1:7003", "127.0.0.1:7004", "127.0.0.1:7005", "127.0.0.1:7006"], "connection_timeout_ms": 5000, "command_timeout_ms": 1000, "max_connections": 50}}, "hot_data_config": {"ttl_seconds": 604800, "batch_size": 1000, "write_worker_count": 8, "max_queue_size": 50000, "enable_compression": true, "enable_pipelining": true}, "performance_config": {"memory_policy": "allkeys-lru", "memory_samples": 10, "hash_max_ziplist_entries": 512, "hash_max_ziplist_value": 64, "zset_max_ziplist_entries": 128, "zset_max_ziplist_value": 64, "list_max_ziplist_size": -2, "set_max_intset_entries": 512}, "monitoring_config": {"enable_slow_log": true, "slow_log_threshold_us": 10000, "slow_log_max_len": 128, "enable_keyspace_notifications": true, "notification_events": "Ex"}, "schema_config": {"key_prefixes": {"tick_latest": "hot:latest:tick:", "tick_data": "hot:tick:", "tick_timeseries": "hot:ts:tick:", "level2_latest": "hot:latest:level2:", "level2_data": "hot:level2:", "level2_timeseries": "hot:ts:level2:"}, "index_config": {"enable_symbol_index": true, "enable_exchange_index": true, "enable_time_index": true}}}