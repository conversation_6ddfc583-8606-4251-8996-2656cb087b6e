#!/bin/bash

# Market Data Collection Enhancement - Environment Initialization Script
# This script initializes the deployment environment

set -e

# Configuration
NAMESPACE="market-data"
CONFIG_DIR="$(dirname "$0")/../config"
KUBE_DIR="$(dirname "$0")/../kubernetes"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed. Please install docker first."
        exit 1
    fi
    
    # Check if helm is installed (optional)
    if ! command -v helm &> /dev/null; then
        log_warn "helm is not installed. Some features may not be available."
    fi
    
    log_info "Prerequisites check completed."
}

# Create namespace
create_namespace() {
    log_info "Creating namespace: $NAMESPACE"
    kubectl apply -f "$KUBE_DIR/namespace.yaml"
}

# Initialize storage
init_storage() {
    log_info "Initializing storage services..."
    
    # Apply storage services
    kubectl apply -f "$KUBE_DIR/storage-services.yaml"
    
    # Wait for storage services to be ready
    log_info "Waiting for Redis to be ready..."
    kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s
    
    log_info "Waiting for ClickHouse to be ready..."
    kubectl wait --for=condition=ready pod -l app=clickhouse -n $NAMESPACE --timeout=300s
    
    log_info "Waiting for MinIO to be ready..."
    kubectl wait --for=condition=ready pod -l app=minio -n $NAMESPACE --timeout=300s
    
    log_info "Storage services initialized successfully."
}

# Initialize ClickHouse database
init_clickhouse() {
    log_info "Initializing ClickHouse database..."
    
    # Get ClickHouse pod name
    CLICKHOUSE_POD=$(kubectl get pods -n $NAMESPACE -l app=clickhouse -o jsonpath='{.items[0].metadata.name}')
    
    # Execute initialization SQL
    kubectl exec -n $NAMESPACE $CLICKHOUSE_POD -- clickhouse-client --query "
        CREATE DATABASE IF NOT EXISTS market_data;
        
        CREATE TABLE IF NOT EXISTS market_data.standard_ticks (
            symbol String,
            timestamp_ns UInt64,
            price Float64,
            volume UInt64,
            bid_price Float64,
            ask_price Float64,
            bid_volume UInt64,
            ask_volume UInt64,
            data_source String,
            collection_timestamp_ns UInt64
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(toDateTime(timestamp_ns / 1000000000))
        ORDER BY (symbol, timestamp_ns);
        
        CREATE TABLE IF NOT EXISTS market_data.level2_data (
            symbol String,
            timestamp_ns UInt64,
            bid_prices Array(Float64),
            bid_volumes Array(UInt64),
            ask_prices Array(Float64),
            ask_volumes Array(UInt64),
            data_source String
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(toDateTime(timestamp_ns / 1000000000))
        ORDER BY (symbol, timestamp_ns);
    "
    
    log_info "ClickHouse database initialized successfully."
}

# Initialize MinIO buckets
init_minio() {
    log_info "Initializing MinIO buckets..."
    
    # Get MinIO pod name
    MINIO_POD=$(kubectl get pods -n $NAMESPACE -l app=minio -o jsonpath='{.items[0].metadata.name}')
    
    # Create buckets
    kubectl exec -n $NAMESPACE $MINIO_POD -- mc alias set local http://localhost:9000 minioadmin minioadmin123
    kubectl exec -n $NAMESPACE $MINIO_POD -- mc mb local/market-data-archive --ignore-existing
    kubectl exec -n $NAMESPACE $MINIO_POD -- mc mb local/market-data-backup --ignore-existing
    
    log_info "MinIO buckets initialized successfully."
}

# Deploy application
deploy_application() {
    log_info "Deploying market data collector application..."
    
    # Apply ConfigMap
    kubectl apply -f "$KUBE_DIR/configmap.yaml"
    
    # Apply application deployment
    kubectl apply -f "$KUBE_DIR/market-data-collector-deployment.yaml"
    
    # Wait for deployment to be ready
    log_info "Waiting for application deployment to be ready..."
    kubectl wait --for=condition=available deployment/market-data-collector -n $NAMESPACE --timeout=300s
    
    log_info "Application deployed successfully."
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check pod status
    kubectl get pods -n $NAMESPACE
    
    # Check services
    kubectl get services -n $NAMESPACE
    
    # Check if application is responding
    APP_POD=$(kubectl get pods -n $NAMESPACE -l app=market-data-collector -o jsonpath='{.items[0].metadata.name}')
    
    if kubectl exec -n $NAMESPACE $APP_POD -- curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_info "Application health check passed."
    else
        log_warn "Application health check failed. Please check logs."
    fi
    
    log_info "Deployment verification completed."
}

# Main execution
main() {
    log_info "Starting Market Data Collection Enhancement deployment..."
    
    check_prerequisites
    create_namespace
    init_storage
    init_clickhouse
    init_minio
    deploy_application
    verify_deployment
    
    log_info "Deployment completed successfully!"
    log_info "You can access the application at:"
    log_info "  - HTTP API: kubectl port-forward -n $NAMESPACE service/market-data-collector-service 8080:8080"
    log_info "  - gRPC API: kubectl port-forward -n $NAMESPACE service/market-data-collector-service 8081:8081"
    log_info "  - Metrics: kubectl port-forward -n $NAMESPACE service/market-data-collector-service 9090:9090"
}

# Handle script arguments
case "${1:-}" in
    "check")
        check_prerequisites
        ;;
    "storage")
        create_namespace
        init_storage
        init_clickhouse
        init_minio
        ;;
    "app")
        deploy_application
        ;;
    "verify")
        verify_deployment
        ;;
    "")
        main
        ;;
    *)
        echo "Usage: $0 [check|storage|app|verify]"
        echo "  check   - Check prerequisites only"
        echo "  storage - Initialize storage services only"
        echo "  app     - Deploy application only"
        echo "  verify  - Verify deployment only"
        echo "  (empty) - Run full deployment"
        exit 1
        ;;
esac