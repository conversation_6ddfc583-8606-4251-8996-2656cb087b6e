#!/bin/bash

# Development environment setup script
set -e

echo "Setting up Financial Data Service development environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Start development services
echo "Starting development services..."
docker-compose up -d

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 30

# Check service health
echo "Checking service health..."
docker-compose ps

# Create ClickHouse database and tables
echo "Setting up ClickHouse database..."
docker exec financial-clickhouse clickhouse-client --query "CREATE DATABASE IF NOT EXISTS market_data"

# Create Redis test data
echo "Setting up Redis test data..."
docker exec financial-redis redis-cli SET test:key "Financial Data Service Ready"

echo "Development environment setup complete!"
echo "Services available at:"
echo "  - Redis: localhost:6379"
echo "  - ClickHouse: localhost:8123 (HTTP), localhost:9000 (TCP)"
echo "  - Kafka: localhost:9092"
echo "  - MinIO: localhost:9001 (Console), localhost:9002 (API)"
echo "  - Prometheus: localhost:9090"
echo "  - Grafana: localhost:3000 (admin/admin123)"