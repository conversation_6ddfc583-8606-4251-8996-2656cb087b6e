#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <fstream>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#endif

#include <nlohmann/json.hpp>

using json = nlohmann::json;

class ConnectionTester {
private:
    json config;
    
    bool InitializeWinsock() {
#ifdef _WIN32
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            std::cerr << "WSAStartup failed: " << result << std::endl;
            return false;
        }
#endif
        return true;
    }
    
    void CleanupWinsock() {
#ifdef _WIN32
        WSACleanup();
#endif
    }
    
    bool TestTCPConnection(const std::string& host, int port, const std::string& service_name) {
        std::cout << "Testing " << service_name << " connection to " << host << ":" << port << "..." << std::endl;
        
#ifdef _WIN32
        SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (sock == INVALID_SOCKET) {
            std::cerr << "Failed to create socket for " << service_name << std::endl;
            return false;
        }
#else
        int sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock < 0) {
            std::cerr << "Failed to create socket for " << service_name << std::endl;
            return false;
        }
#endif
        
        struct sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port);
        
        if (inet_pton(AF_INET, host.c_str(), &server_addr.sin_addr) <= 0) {
            // 如果不是IP地址，尝试localhost
            if (inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr) <= 0) {
                std::cerr << "Invalid address for " << service_name << std::endl;
#ifdef _WIN32
                closesocket(sock);
#else
                close(sock);
#endif
                return false;
            }
        }
        
        // 设置连接超时
#ifdef _WIN32
        DWORD timeout = 3000; // 3秒
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));
#else
        struct timeval timeout;
        timeout.tv_sec = 3;
        timeout.tv_usec = 0;
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
#endif
        
        int result = connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr));
        
#ifdef _WIN32
        closesocket(sock);
#else
        close(sock);
#endif
        
        if (result == 0) {
            std::cout << "✓ " << service_name << " connection successful" << std::endl;
            return true;
        } else {
            std::cout << "✗ " << service_name << " connection failed" << std::endl;
            return false;
        }
    }
    
    bool TestHTTPConnection(const std::string& url, const std::string& service_name) {
        std::cout << "Testing " << service_name << " HTTP endpoint: " << url << std::endl;
        
        // 简单的HTTP测试 - 在实际应用中应该使用HTTP库
        std::string command;
#ifdef _WIN32
        command = "curl -s --connect-timeout 3 \"" + url + "\" >nul 2>&1";
#else
        command = "curl -s --connect-timeout 3 \"" + url + "\" >/dev/null 2>&1";
#endif
        
        int result = system(command.c_str());
        if (result == 0) {
            std::cout << "✓ " << service_name << " HTTP endpoint accessible" << std::endl;
            return true;
        } else {
            std::cout << "✗ " << service_name << " HTTP endpoint not accessible" << std::endl;
            return false;
        }
    }
    
public:
    bool LoadConfig(const std::string& config_path = "config/app.json") {
        try {
            std::ifstream config_file(config_path);
            if (!config_file.is_open()) {
                std::cerr << "Failed to open configuration file: " << config_path << std::endl;
                return false;
            }
            
            config_file >> config;
            std::cout << "Configuration loaded from " << config_path << std::endl;
            return true;
        } catch (const std::exception& e) {
            std::cerr << "Failed to parse configuration: " << e.what() << std::endl;
            return false;
        }
    }
    
    void RunConnectionTests() {
        if (!InitializeWinsock()) {
            return;
        }
        
        std::cout << "========================================" << std::endl;
        std::cout << "Financial Data Service Connection Test" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << std::endl;
        
        bool all_passed = true;
        
        // 测试 Redis 连接
        std::cout << "1. Testing Redis Connection" << std::endl;
        std::cout << "----------------------------" << std::endl;
        std::string redis_host = config["redis"]["host"].get<std::string>();
        int redis_port = config["redis"]["port"].get<int>();
        bool redis_ok = TestTCPConnection(redis_host, redis_port, "Redis");
        all_passed &= redis_ok;
        std::cout << std::endl;
        
        // 测试 ClickHouse 连接
        std::cout << "2. Testing ClickHouse Connection" << std::endl;
        std::cout << "---------------------------------" << std::endl;
        std::string ch_host = config["clickhouse"]["host"].get<std::string>();
        int ch_port = config["clickhouse"]["port"].get<int>();
        bool ch_native_ok = TestTCPConnection(ch_host, ch_port, "ClickHouse Native");
        bool ch_http_ok = TestTCPConnection(ch_host, 8123, "ClickHouse HTTP");
        
        // 测试 ClickHouse HTTP 接口
        std::string ch_http_url = "http://" + ch_host + ":8123/?query=SELECT 1";
        bool ch_http_endpoint_ok = TestHTTPConnection(ch_http_url, "ClickHouse");
        
        all_passed &= (ch_native_ok && ch_http_ok);
        std::cout << std::endl;
        
        // 测试 Kafka 连接
        std::cout << "3. Testing Kafka Connection" << std::endl;
        std::cout << "----------------------------" << std::endl;
        auto kafka_brokers = config["kafka"]["brokers"];
        bool kafka_ok = false;
        for (const auto& broker : kafka_brokers) {
            std::string broker_str = broker.get<std::string>();
            size_t colon_pos = broker_str.find(':');
            if (colon_pos != std::string::npos) {
                std::string kafka_host = broker_str.substr(0, colon_pos);
                int kafka_port = std::stoi(broker_str.substr(colon_pos + 1));
                kafka_ok = TestTCPConnection(kafka_host, kafka_port, "Kafka");
                if (kafka_ok) break;
            }
        }
        all_passed &= kafka_ok;
        std::cout << std::endl;
        
        // 测试 MinIO 连接（如果配置了）
        std::cout << "4. Testing MinIO Connection" << std::endl;
        std::cout << "----------------------------" << std::endl;
        bool minio_console_ok = TestTCPConnection("localhost", 9001, "MinIO Console");
        bool minio_api_ok = TestTCPConnection("localhost", 9002, "MinIO API");
        
        // 测试 MinIO 健康检查
        std::string minio_health_url = "http://localhost:9002/minio/health/live";
        bool minio_health_ok = TestHTTPConnection(minio_health_url, "MinIO Health");
        
        std::cout << std::endl;
        
        // 总结
        std::cout << "========================================" << std::endl;
        std::cout << "Connection Test Summary" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "Redis:      " << (redis_ok ? "✓ PASS" : "✗ FAIL") << std::endl;
        std::cout << "ClickHouse: " << (ch_native_ok && ch_http_ok ? "✓ PASS" : "✗ FAIL") << std::endl;
        std::cout << "Kafka:      " << (kafka_ok ? "✓ PASS" : "✗ FAIL") << std::endl;
        std::cout << "MinIO:      " << (minio_console_ok && minio_api_ok ? "✓ PASS" : "✗ FAIL") << std::endl;
        std::cout << std::endl;
        
        if (all_passed) {
            std::cout << "🎉 All core services are accessible!" << std::endl;
            std::cout << "The main application should be able to connect to all required services." << std::endl;
        } else {
            std::cout << "⚠️  Some services are not accessible." << std::endl;
            std::cout << "Please ensure all required services are running before starting the main application." << std::endl;
            std::cout << std::endl;
            std::cout << "To start the development environment:" << std::endl;
            std::cout << "  Windows: scripts\\start_dev_env.bat" << std::endl;
            std::cout << "  Linux:   ./scripts/start_dev_env.sh" << std::endl;
        }
        
        CleanupWinsock();
    }
};

int main(int argc, char* argv[]) {
    std::cout << "Financial Data Service - Connection Tester v1.0" << std::endl;
    std::cout << std::endl;
    
    ConnectionTester tester;
    
    std::string config_path = "config/app.json";
    if (argc > 1) {
        config_path = argv[1];
    }
    
    if (!tester.LoadConfig(config_path)) {
        std::cerr << "Failed to load configuration, exiting..." << std::endl;
        return 1;
    }
    
    tester.RunConnectionTests();
    
    std::cout << std::endl;
    std::cout << "Press Enter to exit...";
    std::cin.get();
    
    return 0;
}