#pragma once

#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <zlib.h>
#include <spdlog/spdlog.h>

namespace financial_data {
namespace interfaces {

/**
 * @brief 压缩算法类型
 */
enum class CompressionType {
    NONE,
    DEFLATE,
    GZIP
};

/**
 * @brief 压缩结果
 */
struct CompressionResult {
    bool success = false;
    std::vector<uint8_t> compressed_data;
    size_t original_size = 0;
    size_t compressed_size = 0;
    double compression_ratio = 0.0;
    std::string error_message;
    
    double GetCompressionRatio() const {
        return original_size > 0 ? static_cast<double>(compressed_size) / original_size : 1.0;
    }
    
    size_t GetSavedBytes() const {
        return original_size > compressed_size ? original_size - compressed_size : 0;
    }
};

/**
 * @brief 解压缩结果
 */
struct DecompressionResult {
    bool success = false;
    std::string decompressed_data;
    size_t compressed_size = 0;
    size_t decompressed_size = 0;
    std::string error_message;
};

/**
 * @brief 消息压缩器配置
 */
struct CompressorConfig {
    CompressionType type = CompressionType::DEFLATE;
    int compression_level = Z_DEFAULT_COMPRESSION;  // -1 to 9
    size_t compression_threshold = 1024;            // 压缩阈值（字节）
    size_t max_message_size = 1024 * 1024;         // 最大消息大小（1MB）
    bool enable_statistics = true;
    
    // 窗口大小配置（用于deflate/gzip）
    int window_bits = 15;  // 8-15, 15 for maximum compression
    
    // 内存级别配置
    int mem_level = 8;     // 1-9, 8 is default
    
    // 策略配置
    int strategy = Z_DEFAULT_STRATEGY;  // Z_DEFAULT_STRATEGY, Z_FILTERED, Z_HUFFMAN_ONLY, Z_RLE, Z_FIXED
};

/**
 * @brief 压缩统计信息
 */
struct CompressionStatistics {
    std::atomic<uint64_t> total_messages{0};
    std::atomic<uint64_t> compressed_messages{0};
    std::atomic<uint64_t> total_original_bytes{0};
    std::atomic<uint64_t> total_compressed_bytes{0};
    std::atomic<uint64_t> compression_errors{0};
    std::atomic<uint64_t> decompression_errors{0};
    std::atomic<uint64_t> total_compression_time_ns{0};
    std::atomic<uint64_t> total_decompression_time_ns{0};
    
    double GetOverallCompressionRatio() const {
        uint64_t original = total_original_bytes.load();
        return original > 0 ? static_cast<double>(total_compressed_bytes.load()) / original : 1.0;
    }
    
    double GetCompressionRate() const {
        uint64_t total = total_messages.load();
        return total > 0 ? static_cast<double>(compressed_messages.load()) / total : 0.0;
    }
    
    uint64_t GetTotalSavedBytes() const {
        uint64_t original = total_original_bytes.load();
        uint64_t compressed = total_compressed_bytes.load();
        return original > compressed ? original - compressed : 0;
    }
    
    double GetAverageCompressionTimeMs() const {
        uint64_t compressed = compressed_messages.load();
        return compressed > 0 ? static_cast<double>(total_compression_time_ns.load()) / compressed / 1000000.0 : 0.0;
    }
    
    double GetAverageDecompressionTimeMs() const {
        uint64_t compressed = compressed_messages.load();
        return compressed > 0 ? static_cast<double>(total_decompression_time_ns.load()) / compressed / 1000000.0 : 0.0;
    }
    
    void Reset() {
        total_messages = 0;
        compressed_messages = 0;
        total_original_bytes = 0;
        total_compressed_bytes = 0;
        compression_errors = 0;
        decompression_errors = 0;
        total_compression_time_ns = 0;
        total_decompression_time_ns = 0;
    }
};

/**
 * @brief 消息压缩器
 * 
 * 提供高性能的消息压缩和解压缩功能，支持多种压缩算法
 */
class MessageCompressor {
private:
    CompressorConfig config_;
    CompressionStatistics statistics_;
    std::shared_ptr<spdlog::logger> logger_;
    
    // zlib流对象池（用于复用）
    struct ZlibStream {
        z_stream stream;
        bool initialized = false;
        CompressionType type;
        
        ZlibStream() {
            memset(&stream, 0, sizeof(stream));
        }
        
        ~ZlibStream() {
            if (initialized) {
                if (type == CompressionType::DEFLATE || type == CompressionType::GZIP) {
                    deflateEnd(&stream);
                }
            }
        }
    };
    
    // 线程本地存储的流对象
    thread_local static std::unique_ptr<ZlibStream> compression_stream_;
    thread_local static std::unique_ptr<ZlibStream> decompression_stream_;

public:
    explicit MessageCompressor(const CompressorConfig& config = CompressorConfig{});
    ~MessageCompressor() = default;

    /**
     * @brief 压缩消息
     */
    CompressionResult Compress(const std::string& message);

    /**
     * @brief 压缩二进制数据
     */
    CompressionResult Compress(const std::vector<uint8_t>& data);

    /**
     * @brief 压缩数据（通用接口）
     */
    CompressionResult Compress(const void* data, size_t size);

    /**
     * @brief 解压缩消息
     */
    DecompressionResult Decompress(const std::vector<uint8_t>& compressed_data);

    /**
     * @brief 解压缩数据（通用接口）
     */
    DecompressionResult Decompress(const void* compressed_data, size_t size);

    /**
     * @brief 检查是否应该压缩
     */
    bool ShouldCompress(size_t message_size) const;

    /**
     * @brief 获取压缩类型字符串
     */
    std::string GetCompressionTypeString() const;

    /**
     * @brief 获取配置
     */
    CompressorConfig GetConfig() const { return config_; }

    /**
     * @brief 更新配置
     */
    bool UpdateConfig(const CompressorConfig& config);

    /**
     * @brief 获取统计信息
     */
    CompressionStatistics GetStatistics() const { return statistics_; }

    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

    /**
     * @brief 获取统计摘要
     */
    std::string GetStatisticsSummary() const;

    /**
     * @brief 测试压缩性能
     */
    struct PerformanceTestResult {
        double compression_throughput_mbps;
        double decompression_throughput_mbps;
        double average_compression_ratio;
        uint64_t total_test_time_ms;
        bool success;
        std::string error_message;
    };
    
    PerformanceTestResult RunPerformanceTest(size_t test_data_size = 1024 * 1024, 
                                           size_t iterations = 100);

    /**
     * @brief 验证压缩器功能
     */
    bool ValidateCompressor(std::string& error_message);

    /**
     * @brief 创建压缩器实例
     */
    static std::unique_ptr<MessageCompressor> Create(CompressionType type, int level = Z_DEFAULT_COMPRESSION);

private:
    /**
     * @brief 初始化压缩流
     */
    bool InitializeCompressionStream(ZlibStream& stream);

    /**
     * @brief 初始化解压缩流
     */
    bool InitializeDecompressionStream(ZlibStream& stream);

    /**
     * @brief 执行deflate压缩
     */
    CompressionResult CompressDeflate(const void* data, size_t size);

    /**
     * @brief 执行gzip压缩
     */
    CompressionResult CompressGzip(const void* data, size_t size);

    /**
     * @brief 执行deflate解压缩
     */
    DecompressionResult DecompressDeflate(const void* compressed_data, size_t size);

    /**
     * @brief 执行gzip解压缩
     */
    DecompressionResult DecompressGzip(const void* compressed_data, size_t size);

    /**
     * @brief 获取zlib错误信息
     */
    std::string GetZlibError(int error_code) const;

    /**
     * @brief 记录压缩统计
     */
    void RecordCompressionStats(size_t original_size, size_t compressed_size, 
                               uint64_t compression_time_ns, bool success);

    /**
     * @brief 记录解压缩统计
     */
    void RecordDecompressionStats(size_t compressed_size, size_t decompressed_size, 
                                 uint64_t decompression_time_ns, bool success);

    /**
     * @brief 生成测试数据
     */
    std::vector<uint8_t> GenerateTestData(size_t size) const;
};

/**
 * @brief 压缩器工厂
 */
class CompressorFactory {
public:
    /**
     * @brief 创建默认压缩器
     */
    static std::unique_ptr<MessageCompressor> CreateDefault();

    /**
     * @brief 创建高压缩比压缩器
     */
    static std::unique_ptr<MessageCompressor> CreateHighCompression();

    /**
     * @brief 创建快速压缩器
     */
    static std::unique_ptr<MessageCompressor> CreateFastCompression();

    /**
     * @brief 创建平衡压缩器
     */
    static std::unique_ptr<MessageCompressor> CreateBalanced();

    /**
     * @brief 从配置创建压缩器
     */
    static std::unique_ptr<MessageCompressor> CreateFromConfig(const CompressorConfig& config);
};

} // namespace interfaces
} // namespace financial_data