#include "subscription_manager.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <random>

namespace financial_data {
namespace interfaces {

// SubscriptionManager类实现 - 动态订阅管理
SubscriptionManager::SubscriptionManager() 
    : logger_(spdlog::get("subscription_manager")) {
    if (!logger_) {
        logger_ = spdlog::default_logger();
    }
}

bool SubscriptionManager::AddClient(const std::string& client_id, std::shared_ptr<ClientConnection> client) {
    std::unique_lock<std::shared_mutex> lock(clients_mutex_);
    
    if (clients_.find(client_id) != clients_.end()) {
        logger_->warn("Client {} already exists", client_id);
        return false;
    }
    
    clients_[client_id] = client;
    logger_->info("Added client: {}", client_id);
    return true;
}

bool SubscriptionManager::RemoveClient(const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(clients_mutex_);
    
    auto it = clients_.find(client_id);
    if (it == clients_.end()) {
        logger_->warn("Client {} not found for removal", client_id);
        return false;
    }
    
    // 清理所有订阅
    auto client = it->second;
    clients_.erase(it);
    lock.unlock();
    
    // 清理符号订阅
    {
        std::unique_lock<std::shared_mutex> symbol_lock(symbol_subscriptions_mutex_);
        for (const auto& symbol : client->subscribed_symbols) {
            auto symbol_it = symbol_subscriptions_.find(symbol);
            if (symbol_it != symbol_subscriptions_.end()) {
                symbol_it->second.erase(client_id);
                if (symbol_it->second.empty()) {
                    symbol_subscriptions_.erase(symbol_it);
                }
            }
        }
    }
    
    // 清理交易所订阅
    {
        std::unique_lock<std::shared_mutex> exchange_lock(exchange_subscriptions_mutex_);
        for (const auto& exchange : client->subscribed_exchanges) {
            auto exchange_it = exchange_subscriptions_.find(exchange);
            if (exchange_it != exchange_subscriptions_.end()) {
                exchange_it->second.erase(client_id);
                if (exchange_it->second.empty()) {
                    exchange_subscriptions_.erase(exchange_it);
                }
            }
        }
    }
    
    // 清理数据类型订阅
    {
        std::unique_lock<std::shared_mutex> datatype_lock(datatype_subscriptions_mutex_);
        for (const auto& data_type : client->subscribed_data_types) {
            auto datatype_it = datatype_subscriptions_.find(data_type);
            if (datatype_it != datatype_subscriptions_.end()) {
                datatype_it->second.erase(client_id);
                if (datatype_it->second.empty()) {
                    datatype_subscriptions_.erase(datatype_it);
                }
            }
        }
    }
    
    // 清理过滤器
    ClearClientFilters(client_id);
    
    logger_->info("Removed client: {}", client_id);
    return true;
}

std::shared_ptr<ClientConnection> SubscriptionManager::GetClient(const std::string& client_id) const {
    std::shared_lock<std::shared_mutex> lock(clients_mutex_);
    auto it = clients_.find(client_id);
    return (it != clients_.end()) ? it->second : nullptr;
}

std::vector<std::string> SubscriptionManager::GetActiveClients() const {
    std::shared_lock<std::shared_mutex> lock(clients_mutex_);
    std::vector<std::string> active_clients;
    active_clients.reserve(clients_.size());
    
    for (const auto& [client_id, client] : clients_) {
        if (client->state == ConnectionState::CONNECTED) {
            active_clients.push_back(client_id);
        }
    }
    
    return active_clients;
}

SubscriptionResponse SubscriptionManager::ProcessSubscription(const SubscriptionRequest& request) {
    SubscriptionResponse response;
    std::string error_message;
    
    // 验证请求
    if (!ValidateSubscriptionRequest(request, error_message)) {
        response.success = false;
        response.message = error_message;
        return response;
    }
    
    auto client = GetClient(request.client_id);
    if (!client) {
        response.success = false;
        response.message = "Client not found: " + request.client_id;
        return response;
    }
    
    // 处理符号订阅
    for (const auto& symbol : request.symbols) {
        AddSymbolSubscription(symbol, request.client_id);
        client->subscribed_symbols.insert(symbol);
        response.subscribed_symbols.push_back(symbol);
    }
    
    // 处理交易所订阅
    for (const auto& exchange : request.exchanges) {
        AddExchangeSubscription(exchange, request.client_id);
        client->subscribed_exchanges.insert(exchange);
    }
    
    // 处理数据类型订阅
    for (const auto& data_type : request.data_types) {
        AddDataTypeSubscription(data_type, request.client_id);
        client->subscribed_data_types.insert(data_type);
    }
    
    // 处理过滤器
    for (const auto& [filter_type, filter_params] : request.filters) {
        std::unordered_map<std::string, std::string> params;
        // 解析过滤器参数（简化实现，实际应该解析JSON）
        params["value"] = filter_params;
        auto filter = CreateFilter(filter_type, params);
        if (filter) {
            AddClientFilter(request.client_id, std::move(filter));
        }
    }
    
    // 生成订阅ID
    response.subscription_id = GenerateSubscriptionId(request.client_id, request);
    response.success = true;
    response.message = "Subscription successful";
    
    total_subscriptions_++;
    active_subscriptions_++;
    
    LogSubscriptionOperation("SUBSCRIBE", request.client_id, request);
    
    return response;
}

SubscriptionResponse SubscriptionManager::ProcessUnsubscription(const SubscriptionRequest& request) {
    SubscriptionResponse response;
    
    auto client = GetClient(request.client_id);
    if (!client) {
        response.success = false;
        response.message = "Client not found: " + request.client_id;
        return response;
    }
    
    // 处理符号取消订阅
    for (const auto& symbol : request.symbols) {
        RemoveSymbolSubscription(symbol, request.client_id);
        client->subscribed_symbols.erase(symbol);
    }
    
    // 处理交易所取消订阅
    for (const auto& exchange : request.exchanges) {
        RemoveExchangeSubscription(exchange, request.client_id);
        client->subscribed_exchanges.erase(exchange);
    }
    
    // 处理数据类型取消订阅
    for (const auto& data_type : request.data_types) {
        RemoveDataTypeSubscription(data_type, request.client_id);
        client->subscribed_data_types.erase(data_type);
    }
    
    response.success = true;
    response.message = "Unsubscription successful";
    
    if (active_subscriptions_ > 0) {
        active_subscriptions_--;
    }
    
    LogSubscriptionOperation("UNSUBSCRIBE", request.client_id, request);
    
    return response;
}

std::vector<std::string> SubscriptionManager::GetSubscribedClients(const std::string& symbol, 
                                                                  const std::string& exchange) const {
    std::vector<std::string> clients;
    
    // 获取符号订阅的客户端
    {
        std::shared_lock<std::shared_mutex> lock(symbol_subscriptions_mutex_);
        auto it = symbol_subscriptions_.find(symbol);
        if (it != symbol_subscriptions_.end()) {
            for (const auto& client_id : it->second) {
                clients.push_back(client_id);
            }
        }
    }
    
    // 如果指定了交易所，还需要检查交易所订阅
    if (!exchange.empty()) {
        std::vector<std::string> exchange_clients;
        {
            std::shared_lock<std::shared_mutex> lock(exchange_subscriptions_mutex_);
            auto it = exchange_subscriptions_.find(exchange);
            if (it != exchange_subscriptions_.end()) {
                for (const auto& client_id : it->second) {
                    exchange_clients.push_back(client_id);
                }
            }
        }
        
        // 取交集
        std::vector<std::string> intersection;
        std::sort(clients.begin(), clients.end());
        std::sort(exchange_clients.begin(), exchange_clients.end());
        std::set_intersection(clients.begin(), clients.end(),
                            exchange_clients.begin(), exchange_clients.end(),
                            std::back_inserter(intersection));
        clients = intersection;
    }
    
    return clients;
}

std::vector<std::string> SubscriptionManager::GetDataTypeSubscribers(const std::string& data_type) const {
    std::shared_lock<std::shared_mutex> lock(datatype_subscriptions_mutex_);
    auto it = datatype_subscriptions_.find(data_type);
    if (it != datatype_subscriptions_.end()) {
        return std::vector<std::string>(it->second.begin(), it->second.end());
    }
    return {};
}

bool SubscriptionManager::IsClientSubscribed(const std::string& client_id, 
                                           const std::string& symbol, 
                                           const std::string& exchange) const {
    auto client = GetClient(client_id);
    if (!client) {
        return false;
    }
    
    return client->IsSubscribedTo(symbol, exchange);
}

std::vector<std::string> SubscriptionManager::RouteMarketData(const MarketDataWrapper& data) const {
    std::vector<std::string> target_clients;
    
    std::string symbol, exchange, data_type;
    
    if (data.type == MarketDataWrapper::DataType::TICK) {
        symbol = data.tick_data.symbol;
        exchange = data.tick_data.exchange;
        data_type = "tick";
    } else if (data.type == MarketDataWrapper::DataType::LEVEL2) {
        symbol = data.level2_data.symbol;
        exchange = data.level2_data.exchange;
        data_type = "level2";
    } else {
        return target_clients;  // 未知数据类型
    }
    
    // 获取订阅该合约的客户端
    auto symbol_clients = GetSubscribedClients(symbol, exchange);
    
    // 获取订阅该数据类型的客户端
    auto datatype_clients = GetDataTypeSubscribers(data_type);
    
    // 取交集
    std::sort(symbol_clients.begin(), symbol_clients.end());
    std::sort(datatype_clients.begin(), datatype_clients.end());
    std::set_intersection(symbol_clients.begin(), symbol_clients.end(),
                        datatype_clients.begin(), datatype_clients.end(),
                        std::back_inserter(target_clients));
    
    // 应用过滤器
    std::vector<std::string> filtered_clients;
    for (const auto& client_id : target_clients) {
        if (ShouldSendToClient(client_id, data)) {
            filtered_clients.push_back(client_id);
        } else {
            filtered_messages_++;
        }
    }
    
    routed_messages_++;
    return filtered_clients;
}

bool SubscriptionManager::ShouldSendToClient(const std::string& client_id, const MarketDataWrapper& data) const {
    std::shared_lock<std::shared_mutex> lock(filters_mutex_);
    auto it = client_filters_.find(client_id);
    if (it == client_filters_.end()) {
        return true;  // 没有过滤器，发送所有数据
    }
    
    auto client = GetClient(client_id);
    if (!client) {
        return false;
    }
    
    // 应用所有过滤器
    for (const auto& filter : it->second) {
        if (!filter->ShouldSend(data, *client)) {
            return false;
        }
    }
    
    return true;
}

bool SubscriptionManager::AddClientFilter(const std::string& client_id, std::unique_ptr<SubscriptionFilter> filter) {
    std::unique_lock<std::shared_mutex> lock(filters_mutex_);
    client_filters_[client_id].push_back(std::move(filter));
    return true;
}

bool SubscriptionManager::RemoveClientFilter(const std::string& client_id, const std::string& filter_type) {
    std::unique_lock<std::shared_mutex> lock(filters_mutex_);
    auto it = client_filters_.find(client_id);
    if (it == client_filters_.end()) {
        return false;
    }
    
    auto& filters = it->second;
    filters.erase(std::remove_if(filters.begin(), filters.end(),
        [&filter_type](const std::unique_ptr<SubscriptionFilter>& filter) {
            return filter->GetFilterType() == filter_type;
        }), filters.end());
    
    if (filters.empty()) {
        client_filters_.erase(it);
    }
    
    return true;
}

void SubscriptionManager::ClearClientFilters(const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(filters_mutex_);
    client_filters_.erase(client_id);
}

SubscriptionManager::ClientSubscriptionInfo SubscriptionManager::GetClientSubscriptionInfo(const std::string& client_id) const {
    ClientSubscriptionInfo info;
    
    auto client = GetClient(client_id);
    if (!client) {
        return info;
    }
    
    info.symbols = client->subscribed_symbols;
    info.exchanges = client->subscribed_exchanges;
    info.data_types = client->subscribed_data_types;
    info.subscription_count = info.symbols.size() + info.exchanges.size() + info.data_types.size();
    
    // 获取过滤器类型
    {
        std::shared_lock<std::shared_mutex> lock(filters_mutex_);
        auto it = client_filters_.find(client_id);
        if (it != client_filters_.end()) {
            for (const auto& filter : it->second) {
                info.filter_types.push_back(filter->GetFilterType());
            }
        }
    }
    
    return info;
}

SubscriptionManager::SubscriptionStatistics SubscriptionManager::GetStatistics() const {
    SubscriptionStatistics stats;
    
    {
        std::shared_lock<std::shared_mutex> lock(clients_mutex_);
        stats.total_clients = clients_.size();
        for (const auto& [client_id, client] : clients_) {
            if (client->state == ConnectionState::CONNECTED) {
                stats.active_clients++;
            }
        }
    }
    
    stats.total_subscriptions = total_subscriptions_.load();
    stats.active_subscriptions = active_subscriptions_.load();
    stats.filtered_messages = filtered_messages_.load();
    stats.routed_messages = routed_messages_.load();
    
    // 统计符号订阅数量
    {
        std::shared_lock<std::shared_mutex> lock(symbol_subscriptions_mutex_);
        for (const auto& [symbol, clients] : symbol_subscriptions_) {
            stats.symbol_subscription_counts[symbol] = clients.size();
        }
    }
    
    // 统计交易所订阅数量
    {
        std::shared_lock<std::shared_mutex> lock(exchange_subscriptions_mutex_);
        for (const auto& [exchange, clients] : exchange_subscriptions_) {
            stats.exchange_subscription_counts[exchange] = clients.size();
        }
    }
    
    // 统计数据类型订阅数量
    {
        std::shared_lock<std::shared_mutex> lock(datatype_subscriptions_mutex_);
        for (const auto& [data_type, clients] : datatype_subscriptions_) {
            stats.datatype_subscription_counts[data_type] = clients.size();
        }
    }
    
    return stats;
}

void SubscriptionManager::ResetStatistics() {
    total_subscriptions_ = 0;
    active_subscriptions_ = 0;
    filtered_messages_ = 0;
    routed_messages_ = 0;
}

size_t SubscriptionManager::CleanupExpiredClients(std::chrono::milliseconds timeout) {
    std::vector<std::string> expired_clients;
    auto now = std::chrono::steady_clock::now();
    
    {
        std::shared_lock<std::shared_mutex> lock(clients_mutex_);
        for (const auto& [client_id, client] : clients_) {
            if (now - client->last_activity > timeout) {
                expired_clients.push_back(client_id);
            }
        }
    }
    
    for (const auto& client_id : expired_clients) {
        RemoveClient(client_id);
    }
    
    return expired_clients.size();
}

std::string SubscriptionManager::GetSubscriptionSummary() const {
    auto stats = GetStatistics();
    std::ostringstream oss;
    
    oss << "Subscription Summary:\n";
    oss << "  Total Clients: " << stats.total_clients << "\n";
    oss << "  Active Clients: " << stats.active_clients << "\n";
    oss << "  Total Subscriptions: " << stats.total_subscriptions << "\n";
    oss << "  Active Subscriptions: " << stats.active_subscriptions << "\n";
    oss << "  Filtered Messages: " << stats.filtered_messages << "\n";
    oss << "  Routed Messages: " << stats.routed_messages << "\n";
    
    if (!stats.symbol_subscription_counts.empty()) {
        oss << "  Top Subscribed Symbols:\n";
        std::vector<std::pair<std::string, uint64_t>> sorted_symbols(
            stats.symbol_subscription_counts.begin(), stats.symbol_subscription_counts.end());
        std::sort(sorted_symbols.begin(), sorted_symbols.end(),
                 [](const auto& a, const auto& b) { return a.second > b.second; });
        
        for (size_t i = 0; i < std::min(size_t(5), sorted_symbols.size()); ++i) {
            oss << "    " << sorted_symbols[i].first << ": " << sorted_symbols[i].second << " clients\n";
        }
    }
    
    return oss.str();
}

bool SubscriptionManager::ValidateSubscriptionRequest(const SubscriptionRequest& request, std::string& error_message) const {
    if (request.client_id.empty()) {
        error_message = "Client ID cannot be empty";
        return false;
    }
    
    if (request.symbols.empty() && request.exchanges.empty()) {
        error_message = "Must specify at least one symbol or exchange";
        return false;
    }
    
    if (request.data_types.empty()) {
        error_message = "Must specify at least one data type";
        return false;
    }
    
    // 验证数据类型
    for (const auto& data_type : request.data_types) {
        if (data_type != "tick" && data_type != "level2" && data_type != "kline") {
            error_message = "Invalid data type: " + data_type;
            return false;
        }
    }
    
    return true;
}

std::unique_ptr<SubscriptionFilter> SubscriptionManager::CreateFilter(const std::string& filter_type, 
                                                                     const std::unordered_map<std::string, std::string>& params) const {
    if (filter_type == "price_range") {
        auto min_it = params.find("min_price");
        auto max_it = params.find("max_price");
        if (min_it != params.end() && max_it != params.end()) {
            try {
                double min_price = std::stod(min_it->second);
                double max_price = std::stod(max_it->second);
                return std::make_unique<PriceRangeFilter>(min_price, max_price);
            } catch (const std::exception& e) {
                logger_->error("Failed to create price range filter: {}", e.what());
            }
        }
    } else if (filter_type == "volume") {
        auto volume_it = params.find("min_volume");
        if (volume_it != params.end()) {
            try {
                uint64_t min_volume = std::stoull(volume_it->second);
                return std::make_unique<VolumeFilter>(min_volume);
            } catch (const std::exception& e) {
                logger_->error("Failed to create volume filter: {}", e.what());
            }
        }
    }
    
    return nullptr;
}

void SubscriptionManager::AddSymbolSubscription(const std::string& symbol, const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(symbol_subscriptions_mutex_);
    symbol_subscriptions_[symbol].insert(client_id);
}

void SubscriptionManager::RemoveSymbolSubscription(const std::string& symbol, const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(symbol_subscriptions_mutex_);
    auto it = symbol_subscriptions_.find(symbol);
    if (it != symbol_subscriptions_.end()) {
        it->second.erase(client_id);
        if (it->second.empty()) {
            symbol_subscriptions_.erase(it);
        }
    }
}

void SubscriptionManager::AddExchangeSubscription(const std::string& exchange, const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(exchange_subscriptions_mutex_);
    exchange_subscriptions_[exchange].insert(client_id);
}

void SubscriptionManager::RemoveExchangeSubscription(const std::string& exchange, const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(exchange_subscriptions_mutex_);
    auto it = exchange_subscriptions_.find(exchange);
    if (it != exchange_subscriptions_.end()) {
        it->second.erase(client_id);
        if (it->second.empty()) {
            exchange_subscriptions_.erase(it);
        }
    }
}

void SubscriptionManager::AddDataTypeSubscription(const std::string& data_type, const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(datatype_subscriptions_mutex_);
    datatype_subscriptions_[data_type].insert(client_id);
}

void SubscriptionManager::RemoveDataTypeSubscription(const std::string& data_type, const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(datatype_subscriptions_mutex_);
    auto it = datatype_subscriptions_.find(data_type);
    if (it != datatype_subscriptions_.end()) {
        it->second.erase(client_id);
        if (it->second.empty()) {
            datatype_subscriptions_.erase(it);
        }
    }
}

std::string SubscriptionManager::GenerateSubscriptionId(const std::string& client_id, const SubscriptionRequest& request) const {
    std::ostringstream oss;
    oss << client_id << "_" << std::hex << std::hash<std::string>{}(request.ToJson().dump());
    return oss.str();
}

void SubscriptionManager::LogSubscriptionOperation(const std::string& operation, 
                                                  const std::string& client_id, 
                                                  const SubscriptionRequest& request) const {
    logger_->info("{} - Client: {}, Symbols: [{}], Exchanges: [{}], DataTypes: [{}]",
                 operation, client_id,
                 fmt::join(request.symbols, ", "),
                 fmt::join(request.exchanges, ", "),
                 fmt::join(request.data_types, ", "));
}

} // namespace interfaces
} // namespace financial_data