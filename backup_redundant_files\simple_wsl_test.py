#!/usr/bin/env python3
"""
简化的WSL测试脚本
测试基本的Redis连接和Python功能
"""

import asyncio
import sys
import os
from datetime import datetime

def print_header():
    print("=" * 50)
    print("    简化WSL测试")
    print("=" * 50)
    print()

def print_info(msg):
    print(f"[INFO] {msg}")

def print_success(msg):
    print(f"[SUCCESS] ✅ {msg}")

def print_error(msg):
    print(f"[ERROR] ❌ {msg}")

def test_redis_connection():
    """测试Redis连接"""
    print_info("测试Redis连接...")
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
        
        # 测试连接
        response = r.ping()
        if response:
            print_success("Redis连接成功")
            
            # 测试基本操作
            r.set('test_key', 'test_value')
            value = r.get('test_key')
            if value and value.decode('utf-8') == 'test_value':
                print_success("Redis读写操作正常")
            
            # 清理测试数据
            r.delete('test_key')
            return True
        else:
            print_error("Redis连接失败")
            return False
            
    except ImportError:
        print_error("Redis模块未安装")
        return False
    except Exception as e:
        print_error(f"Redis连接异常: {e}")
        return False

async def test_async_functionality():
    """测试异步功能"""
    print_info("测试异步功能...")
    
    try:
        # 简单异步任务
        async def simple_task(n):
            await asyncio.sleep(0.1)
            return f"任务{n}完成"
        
        # 并发执行
        tasks = [simple_task(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        
        print_success(f"异步任务完成: {len(results)}个")
        return True
        
    except Exception as e:
        print_error(f"异步功能测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print_info("测试文件操作...")
    
    try:
        # 创建测试目录
        os.makedirs('logs', exist_ok=True)
        os.makedirs('data', exist_ok=True)
        
        # 写入测试文件
        test_file = 'logs/simple_test.log'
        with open(test_file, 'w') as f:
            f.write(f"测试时间: {datetime.now()}\n")
            f.write("WSL环境测试成功\n")
        
        # 读取测试文件
        with open(test_file, 'r') as f:
            content = f.read()
            if "测试时间" in content:
                print_success("文件读写操作正常")
                return True
        
        return False
        
    except Exception as e:
        print_error(f"文件操作失败: {e}")
        return False

def test_basic_imports():
    """测试基本模块导入"""
    print_info("测试基本模块导入...")
    
    modules = [
        'asyncio',
        'json', 
        'datetime',
        'logging',
        'os',
        'sys'
    ]
    
    success_count = 0
    for module in modules:
        try:
            __import__(module)
            print_success(f"{module} 模块导入成功")
            success_count += 1
        except ImportError:
            print_error(f"{module} 模块导入失败")
    
    return success_count == len(modules)

async def main():
    """主函数"""
    print_header()
    
    test_results = []
    
    # 基本模块测试
    test_results.append(("基本模块", test_basic_imports()))
    
    # 文件操作测试
    test_results.append(("文件操作", test_file_operations()))
    
    # 异步功能测试
    test_results.append(("异步功能", await test_async_functionality()))
    
    # Redis连接测试
    test_results.append(("Redis连接", test_redis_connection()))
    
    # 显示结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print_success("🎉 所有基本测试通过！")
        print("\n环境已准备就绪，可以进行进一步的功能测试")
        
        # 显示下一步建议
        print("\n下一步建议:")
        print("1. 安装更多依赖: pip install pandas pytdx")
        print("2. 启动Docker服务: sudo service docker start")
        print("3. 运行完整测试: python3 test_enhanced_features.py")
        
    else:
        print_error("部分测试失败，请检查环境配置")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] 测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)