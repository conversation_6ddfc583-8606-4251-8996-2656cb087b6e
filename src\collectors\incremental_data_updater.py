"""
增量数据更新器 - 智能数据补充和更新机制

功能特性：
- 数据时间戳检查避免重复更新
- 智能数据补充机制
- 批量数据获取性能优化
- 数据完整性验证
- 增量更新策略管理
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
import json
import hashlib

logger = logging.getLogger(__name__)


class UpdateStrategy(Enum):
    """更新策略枚举"""
    FULL_REFRESH = "full_refresh"          # 全量刷新
    INCREMENTAL = "incremental"            # 增量更新
    SMART_FILL = "smart_fill"              # 智能填充
    TIME_BASED = "time_based"              # 基于时间的更新
    CHANGE_DETECTION = "change_detection"   # 变化检测


class DataGranularity(Enum):
    """数据粒度枚举"""
    TICK = "tick"           # tick数据
    MINUTE_1 = "1min"       # 1分钟K线
    MINUTE_5 = "5min"       # 5分钟K线
    MINUTE_15 = "15min"     # 15分钟K线
    MINUTE_30 = "30min"     # 30分钟K线
    HOUR_1 = "1hour"        # 1小时K线
    DAILY = "daily"         # 日K线
    WEEKLY = "weekly"       # 周K线
    MONTHLY = "monthly"     # 月K线


@dataclass
class UpdateConfig:
    """增量更新配置"""
    strategy: UpdateStrategy = UpdateStrategy.INCREMENTAL
    granularity: DataGranularity = DataGranularity.DAILY
    lookback_days: int = 7
    max_gap_minutes: int = 60
    batch_size: int = 1000
    max_concurrent_updates: int = 5
    enable_gap_detection: bool = True
    enable_duplicate_check: bool = True
    enable_data_validation: bool = True
    update_timeout_seconds: int = 300
    retry_failed_updates: bool = True
    max_retries: int = 3
    retry_delay_seconds: int = 30


@dataclass
class DataGap:
    """数据缺口信息"""
    symbol: str
    granularity: DataGranularity
    start_time: datetime
    end_time: datetime
    expected_points: int
    actual_points: int
    gap_ratio: float
    priority: int = 1  # 1=低, 2=中, 3=高
    
    @property
    def duration_minutes(self) -> int:
        """缺口持续时间（分钟）"""
        return int((self.end_time - self.start_time).total_seconds() / 60)
    
    @property
    def is_critical(self) -> bool:
        """是否为关键缺口"""
        return self.gap_ratio > 0.5 or self.duration_minutes > 240  # 4小时


@dataclass
class UpdateResult:
    """更新结果"""
    symbol: str
    granularity: DataGranularity
    strategy: UpdateStrategy
    start_time: datetime
    end_time: datetime
    records_added: int = 0
    records_updated: int = 0
    records_skipped: int = 0
    gaps_filled: int = 0
    execution_time_seconds: float = 0.0
    success: bool = True
    error_message: str = ""
    data_quality_score: float = 1.0


class DataSourceInterface(ABC):
    """数据源接口"""
    
    @abstractmethod
    async def get_latest_timestamp(self, symbol: str, granularity: DataGranularity) -> Optional[datetime]:
        """获取最新数据时间戳"""
        pass
    
    @abstractmethod
    async def get_data_range(self, symbol: str, granularity: DataGranularity, 
                           start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """获取指定时间范围的数据"""
        pass
    
    @abstractmethod
    async def check_data_completeness(self, symbol: str, granularity: DataGranularity,
                                    start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """检查数据完整性"""
        pass


class StorageInterface(ABC):
    """存储接口"""
    
    @abstractmethod
    async def get_latest_timestamp(self, symbol: str, granularity: DataGranularity) -> Optional[datetime]:
        """获取存储中的最新数据时间戳"""
        pass
    
    @abstractmethod
    async def get_data_gaps(self, symbol: str, granularity: DataGranularity,
                          start_time: datetime, end_time: datetime) -> List[DataGap]:
        """获取数据缺口"""
        pass
    
    @abstractmethod
    async def store_data(self, symbol: str, granularity: DataGranularity, data: pd.DataFrame) -> bool:
        """存储数据"""
        pass
    
    @abstractmethod
    async def get_data_statistics(self, symbol: str, granularity: DataGranularity,
                                start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """获取数据统计信息"""
        pass


class MockDataSource(DataSourceInterface):
    """模拟数据源（用于测试）"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MockDataSource")
        self.data_cache = {}
    
    async def get_latest_timestamp(self, symbol: str, granularity: DataGranularity) -> Optional[datetime]:
        """获取最新数据时间戳"""
        # 模拟返回当前时间减去一些随机偏移
        base_time = datetime.now()
        if granularity == DataGranularity.TICK:
            return base_time - timedelta(seconds=30)
        elif granularity in [DataGranularity.MINUTE_1, DataGranularity.MINUTE_5]:
            return base_time - timedelta(minutes=5)
        else:
            return base_time - timedelta(hours=1)
    
    async def get_data_range(self, symbol: str, granularity: DataGranularity,
                           start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """获取指定时间范围的数据"""
        # 生成模拟数据
        time_delta = self._get_time_delta(granularity)
        timestamps = pd.date_range(start=start_time, end=end_time, freq=time_delta)
        
        if len(timestamps) == 0:
            return pd.DataFrame()
        
        # 生成模拟价格数据
        base_price = 100.0
        price_data = []
        
        for i, ts in enumerate(timestamps):
            # 模拟价格波动
            price = base_price + np.random.normal(0, 1) * 0.5
            volume = np.random.randint(1000, 10000)
            
            price_data.append({
                'timestamp': ts,
                'open': price,
                'high': price * (1 + np.random.uniform(0, 0.02)),
                'low': price * (1 - np.random.uniform(0, 0.02)),
                'close': price + np.random.normal(0, 0.1),
                'volume': volume,
                'amount': price * volume
            })
        
        df = pd.DataFrame(price_data)
        df.set_index('timestamp', inplace=True)
        
        # 随机移除一些数据点来模拟数据缺口
        if len(df) > 10:
            remove_count = np.random.randint(0, min(5, len(df) // 10))
            if remove_count > 0:
                remove_indices = np.random.choice(df.index, remove_count, replace=False)
                df = df.drop(remove_indices)
        
        return df
    
    async def check_data_completeness(self, symbol: str, granularity: DataGranularity,
                                    start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """检查数据完整性"""
        expected_points = self._calculate_expected_points(granularity, start_time, end_time)
        actual_data = await self.get_data_range(symbol, granularity, start_time, end_time)
        actual_points = len(actual_data)
        
        return {
            'symbol': symbol,
            'granularity': granularity.value,
            'start_time': start_time,
            'end_time': end_time,
            'expected_points': expected_points,
            'actual_points': actual_points,
            'completeness_ratio': actual_points / max(expected_points, 1),
            'missing_points': max(0, expected_points - actual_points)
        }
    
    def _get_time_delta(self, granularity: DataGranularity) -> str:
        """获取时间间隔字符串"""
        delta_map = {
            DataGranularity.TICK: '1S',
            DataGranularity.MINUTE_1: '1T',
            DataGranularity.MINUTE_5: '5T',
            DataGranularity.MINUTE_15: '15T',
            DataGranularity.MINUTE_30: '30T',
            DataGranularity.HOUR_1: '1H',
            DataGranularity.DAILY: '1D',
            DataGranularity.WEEKLY: '1W',
            DataGranularity.MONTHLY: '1M'
        }
        return delta_map.get(granularity, '1T')
    
    def _calculate_expected_points(self, granularity: DataGranularity, 
                                 start_time: datetime, end_time: datetime) -> int:
        """计算期望的数据点数"""
        total_seconds = (end_time - start_time).total_seconds()
        
        if granularity == DataGranularity.TICK:
            return int(total_seconds)  # 假设每秒1个tick
        elif granularity == DataGranularity.MINUTE_1:
            return int(total_seconds / 60)
        elif granularity == DataGranularity.MINUTE_5:
            return int(total_seconds / 300)
        elif granularity == DataGranularity.MINUTE_15:
            return int(total_seconds / 900)
        elif granularity == DataGranularity.MINUTE_30:
            return int(total_seconds / 1800)
        elif granularity == DataGranularity.HOUR_1:
            return int(total_seconds / 3600)
        elif granularity == DataGranularity.DAILY:
            return max(1, int(total_seconds / 86400))
        else:
            return max(1, int(total_seconds / 3600))  # 默认按小时计算


class MockStorage(StorageInterface):
    """模拟存储（用于测试）"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MockStorage")
        self.stored_data = {}
        self.timestamps = {}
    
    async def get_latest_timestamp(self, symbol: str, granularity: DataGranularity) -> Optional[datetime]:
        """获取存储中的最新数据时间戳"""
        key = f"{symbol}_{granularity.value}"
        return self.timestamps.get(key)
    
    async def get_data_gaps(self, symbol: str, granularity: DataGranularity,
                          start_time: datetime, end_time: datetime) -> List[DataGap]:
        """获取数据缺口"""
        gaps = []
        
        # 模拟一些数据缺口
        current_time = start_time
        while current_time < end_time:
            # 随机决定是否有缺口
            if np.random.random() < 0.1:  # 10%概率有缺口
                gap_duration = timedelta(minutes=np.random.randint(30, 180))
                gap_end = min(current_time + gap_duration, end_time)
                
                expected_points = self._calculate_expected_points(granularity, current_time, gap_end)
                actual_points = max(0, expected_points - np.random.randint(1, expected_points + 1))
                
                gap = DataGap(
                    symbol=symbol,
                    granularity=granularity,
                    start_time=current_time,
                    end_time=gap_end,
                    expected_points=expected_points,
                    actual_points=actual_points,
                    gap_ratio=(expected_points - actual_points) / max(expected_points, 1),
                    priority=2 if expected_points > 100 else 1
                )
                gaps.append(gap)
                current_time = gap_end
            else:
                current_time += timedelta(hours=1)
        
        return gaps
    
    async def store_data(self, symbol: str, granularity: DataGranularity, data: pd.DataFrame) -> bool:
        """存储数据"""
        try:
            key = f"{symbol}_{granularity.value}"
            
            if key not in self.stored_data:
                self.stored_data[key] = []
            
            # 转换数据为字典列表
            data_records = data.reset_index().to_dict('records')
            self.stored_data[key].extend(data_records)
            
            # 更新最新时间戳
            if not data.empty:
                latest_timestamp = data.index.max()
                if isinstance(latest_timestamp, pd.Timestamp):
                    latest_timestamp = latest_timestamp.to_pydatetime()
                self.timestamps[key] = latest_timestamp
            
            self.logger.info(f"Stored {len(data)} records for {symbol} {granularity.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store data for {symbol}: {e}")
            return False
    
    async def get_data_statistics(self, symbol: str, granularity: DataGranularity,
                                start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """获取数据统计信息"""
        key = f"{symbol}_{granularity.value}"
        records = self.stored_data.get(key, [])
        
        # 过滤时间范围内的数据
        filtered_records = []
        for record in records:
            timestamp = record.get('timestamp')
            if isinstance(timestamp, str):
                timestamp = pd.to_datetime(timestamp)
            elif isinstance(timestamp, pd.Timestamp):
                timestamp = timestamp.to_pydatetime()
            
            if start_time <= timestamp <= end_time:
                filtered_records.append(record)
        
        return {
            'symbol': symbol,
            'granularity': granularity.value,
            'total_records': len(filtered_records),
            'start_time': start_time,
            'end_time': end_time,
            'storage_size_mb': len(str(filtered_records)) / (1024 * 1024)
        }
    
    def _calculate_expected_points(self, granularity: DataGranularity, 
                                 start_time: datetime, end_time: datetime) -> int:
        """计算期望的数据点数"""
        total_seconds = (end_time - start_time).total_seconds()
        
        if granularity == DataGranularity.MINUTE_1:
            return int(total_seconds / 60)
        elif granularity == DataGranularity.MINUTE_5:
            return int(total_seconds / 300)
        elif granularity == DataGranularity.MINUTE_15:
            return int(total_seconds / 900)
        elif granularity == DataGranularity.MINUTE_30:
            return int(total_seconds / 1800)
        elif granularity == DataGranularity.HOUR_1:
            return int(total_seconds / 3600)
        elif granularity == DataGranularity.DAILY:
            return max(1, int(total_seconds / 86400))
        else:
            return max(1, int(total_seconds / 3600))


class IncrementalDataUpdater:
    """增量数据更新器"""
    
    def __init__(self, config: UpdateConfig, 
                 data_source: DataSourceInterface,
                 storage: StorageInterface):
        self.config = config
        self.data_source = data_source
        self.storage = storage
        self.logger = logging.getLogger(f"{__name__}.IncrementalDataUpdater")
        
        # 更新统计
        self.stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'total_records_added': 0,
            'total_records_updated': 0,
            'total_gaps_filled': 0,
            'average_update_time': 0.0,
            'last_update_time': None
        }
        
        # 更新历史
        self.update_history: List[UpdateResult] = []
        
        # 并发控制
        self.semaphore = asyncio.Semaphore(config.max_concurrent_updates)
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.completion_callback: Optional[Callable] = None
    
    def set_callbacks(self, progress_callback: Optional[Callable] = None,
                     completion_callback: Optional[Callable] = None):
        """设置回调函数"""
        self.progress_callback = progress_callback
        self.completion_callback = completion_callback
    
    async def update_symbol_data(self, symbol: str, granularity: DataGranularity,
                               strategy: Optional[UpdateStrategy] = None) -> UpdateResult:
        """更新单个股票的数据"""
        async with self.semaphore:
            return await self._update_symbol_data_internal(symbol, granularity, strategy)
    
    async def _update_symbol_data_internal(self, symbol: str, granularity: DataGranularity,
                                         strategy: Optional[UpdateStrategy] = None) -> UpdateResult:
        """内部更新单个股票数据的实现"""
        start_time = time.time()
        update_strategy = strategy or self.config.strategy
        
        result = UpdateResult(
            symbol=symbol,
            granularity=granularity,
            strategy=update_strategy,
            start_time=datetime.now(),
            end_time=datetime.now()
        )
        
        try:
            self.logger.info(f"Starting {update_strategy.value} update for {symbol} {granularity.value}")
            
            if update_strategy == UpdateStrategy.FULL_REFRESH:
                await self._full_refresh_update(symbol, granularity, result)
            elif update_strategy == UpdateStrategy.INCREMENTAL:
                await self._incremental_update(symbol, granularity, result)
            elif update_strategy == UpdateStrategy.SMART_FILL:
                await self._smart_fill_update(symbol, granularity, result)
            elif update_strategy == UpdateStrategy.TIME_BASED:
                await self._time_based_update(symbol, granularity, result)
            elif update_strategy == UpdateStrategy.CHANGE_DETECTION:
                await self._change_detection_update(symbol, granularity, result)
            else:
                raise ValueError(f"Unsupported update strategy: {update_strategy}")
            
            result.success = True
            result.execution_time_seconds = time.time() - start_time
            
            # 更新统计信息
            self.stats['successful_updates'] += 1
            self.stats['total_records_added'] += result.records_added
            self.stats['total_records_updated'] += result.records_updated
            self.stats['total_gaps_filled'] += result.gaps_filled
            
            self.logger.info(f"Update completed for {symbol}: +{result.records_added} records, "
                           f"{result.gaps_filled} gaps filled in {result.execution_time_seconds:.2f}s")
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            result.execution_time_seconds = time.time() - start_time
            
            self.stats['failed_updates'] += 1
            self.logger.error(f"Update failed for {symbol}: {e}")
        
        finally:
            result.end_time = datetime.now()
            self.stats['total_updates'] += 1
            self.stats['last_update_time'] = result.end_time.isoformat()
            
            # 更新平均执行时间
            total_successful = self.stats['successful_updates']
            if total_successful > 0:
                current_avg = self.stats['average_update_time']
                new_avg = ((current_avg * (total_successful - 1)) + result.execution_time_seconds) / total_successful
                self.stats['average_update_time'] = new_avg
            
            # 添加到历史记录
            self.update_history.append(result)
            
            # 限制历史记录数量
            if len(self.update_history) > 1000:
                self.update_history = self.update_history[-500:]
            
            # 调用完成回调
            if self.completion_callback:
                try:
                    self.completion_callback(result)
                except Exception as callback_error:
                    self.logger.error(f"Error in completion callback: {callback_error}")
        
        return result
    
    async def _full_refresh_update(self, symbol: str, granularity: DataGranularity, result: UpdateResult):
        """全量刷新更新"""
        # 计算更新时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=self.config.lookback_days)
        
        result.start_time = start_time
        result.end_time = end_time
        
        # 从数据源获取全量数据
        data = await self.data_source.get_data_range(symbol, granularity, start_time, end_time)
        
        if not data.empty:
            # 存储数据
            success = await self.storage.store_data(symbol, granularity, data)
            if success:
                result.records_added = len(data)
                result.data_quality_score = self._calculate_data_quality_score(data)
            else:
                raise Exception("Failed to store data")
        
        self.logger.info(f"Full refresh completed for {symbol}: {len(data)} records")
    
    async def _incremental_update(self, symbol: str, granularity: DataGranularity, result: UpdateResult):
        """增量更新"""
        # 获取存储中的最新时间戳
        latest_stored = await self.storage.get_latest_timestamp(symbol, granularity)
        
        if latest_stored is None:
            # 如果没有历史数据，执行全量刷新
            await self._full_refresh_update(symbol, granularity, result)
            return
        
        # 获取数据源的最新时间戳
        latest_source = await self.data_source.get_latest_timestamp(symbol, granularity)
        
        if latest_source is None or latest_source <= latest_stored:
            self.logger.info(f"No new data available for {symbol}")
            return
        
        # 计算增量更新范围
        start_time = latest_stored
        end_time = latest_source
        
        result.start_time = start_time
        result.end_time = end_time
        
        # 获取增量数据
        data = await self.data_source.get_data_range(symbol, granularity, start_time, end_time)
        
        if not data.empty:
            # 去重处理（避免重复数据）
            if self.config.enable_duplicate_check:
                data = self._remove_duplicates(data, latest_stored)
            
            # 数据验证
            if self.config.enable_data_validation:
                data = self._validate_data(data)
            
            if not data.empty:
                # 存储增量数据
                success = await self.storage.store_data(symbol, granularity, data)
                if success:
                    result.records_added = len(data)
                    result.data_quality_score = self._calculate_data_quality_score(data)
                else:
                    raise Exception("Failed to store incremental data")
        
        self.logger.info(f"Incremental update completed for {symbol}: {len(data)} new records")
    
    async def _smart_fill_update(self, symbol: str, granularity: DataGranularity, result: UpdateResult):
        """智能填充更新"""
        # 计算检查范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=self.config.lookback_days)
        
        result.start_time = start_time
        result.end_time = end_time
        
        # 检测数据缺口
        gaps = await self.storage.get_data_gaps(symbol, granularity, start_time, end_time)
        
        if not gaps:
            self.logger.info(f"No data gaps found for {symbol}")
            return
        
        # 按优先级排序缺口
        gaps.sort(key=lambda g: (g.priority, g.gap_ratio), reverse=True)
        
        total_filled = 0
        
        # 填充缺口
        for gap in gaps:
            try:
                # 从数据源获取缺口数据
                gap_data = await self.data_source.get_data_range(
                    symbol, granularity, gap.start_time, gap.end_time
                )
                
                if not gap_data.empty:
                    # 数据验证
                    if self.config.enable_data_validation:
                        gap_data = self._validate_data(gap_data)
                    
                    if not gap_data.empty:
                        # 存储缺口数据
                        success = await self.storage.store_data(symbol, granularity, gap_data)
                        if success:
                            result.records_added += len(gap_data)
                            result.gaps_filled += 1
                            total_filled += len(gap_data)
                            
                            self.logger.info(f"Filled gap for {symbol}: {len(gap_data)} records "
                                           f"from {gap.start_time} to {gap.end_time}")
                
            except Exception as e:
                self.logger.error(f"Failed to fill gap for {symbol}: {e}")
        
        result.data_quality_score = self._calculate_gap_fill_quality_score(gaps, result.gaps_filled)
        self.logger.info(f"Smart fill completed for {symbol}: {total_filled} records, {result.gaps_filled} gaps filled")
    
    async def _time_based_update(self, symbol: str, granularity: DataGranularity, result: UpdateResult):
        """基于时间的更新"""
        # 根据粒度确定更新窗口
        if granularity in [DataGranularity.TICK, DataGranularity.MINUTE_1]:
            lookback_hours = 2
        elif granularity in [DataGranularity.MINUTE_5, DataGranularity.MINUTE_15]:
            lookback_hours = 6
        elif granularity == DataGranularity.MINUTE_30:
            lookback_hours = 12
        elif granularity == DataGranularity.HOUR_1:
            lookback_hours = 24
        else:
            lookback_hours = 48
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=lookback_hours)
        
        result.start_time = start_time
        result.end_time = end_time
        
        # 获取时间窗口内的数据
        data = await self.data_source.get_data_range(symbol, granularity, start_time, end_time)
        
        if not data.empty:
            # 检查是否有新数据
            latest_stored = await self.storage.get_latest_timestamp(symbol, granularity)
            
            if latest_stored:
                # 只保留新于存储时间戳的数据
                data = data[data.index > latest_stored]
            
            if not data.empty:
                # 数据验证和去重
                if self.config.enable_data_validation:
                    data = self._validate_data(data)
                
                if self.config.enable_duplicate_check and latest_stored:
                    data = self._remove_duplicates(data, latest_stored)
                
                if not data.empty:
                    # 存储数据
                    success = await self.storage.store_data(symbol, granularity, data)
                    if success:
                        result.records_added = len(data)
                        result.data_quality_score = self._calculate_data_quality_score(data)
                    else:
                        raise Exception("Failed to store time-based data")
        
        self.logger.info(f"Time-based update completed for {symbol}: {len(data)} records")
    
    async def _change_detection_update(self, symbol: str, granularity: DataGranularity, result: UpdateResult):
        """变化检测更新"""
        # 获取最近的数据进行比较
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)  # 检查最近1小时
        
        result.start_time = start_time
        result.end_time = end_time
        
        # 从数据源获取最新数据
        source_data = await self.data_source.get_data_range(symbol, granularity, start_time, end_time)
        
        if source_data.empty:
            return
        
        # 获取存储中的对应数据
        storage_stats = await self.storage.get_data_statistics(symbol, granularity, start_time, end_time)
        
        # 比较数据量和最新时间戳
        source_latest = source_data.index.max()
        storage_latest = await self.storage.get_latest_timestamp(symbol, granularity)
        
        has_changes = False
        
        if storage_latest is None or source_latest > storage_latest:
            has_changes = True
        elif len(source_data) != storage_stats.get('total_records', 0):
            has_changes = True
        
        if has_changes:
            # 有变化，执行增量更新
            await self._incremental_update(symbol, granularity, result)
        else:
            self.logger.info(f"No changes detected for {symbol}")
    
    def _remove_duplicates(self, data: pd.DataFrame, latest_timestamp: datetime) -> pd.DataFrame:
        """移除重复数据"""
        if data.empty:
            return data
        
        # 移除时间戳重复的数据
        data = data[~data.index.duplicated(keep='last')]
        
        # 移除早于或等于最新存储时间戳的数据
        data = data[data.index > latest_timestamp]
        
        return data
    
    def _validate_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """验证数据质量"""
        if data.empty:
            return data
        
        validated_data = data.copy()
        
        # 移除价格为0或负数的记录
        price_columns = ['open', 'high', 'low', 'close', 'last_price']
        for col in price_columns:
            if col in validated_data.columns:
                validated_data = validated_data[validated_data[col] > 0]
        
        # 移除成交量为负数的记录
        if 'volume' in validated_data.columns:
            validated_data = validated_data[validated_data['volume'] >= 0]
        
        # 检查价格逻辑关系
        if all(col in validated_data.columns for col in ['open', 'high', 'low', 'close']):
            # high >= max(open, close) and low <= min(open, close)
            valid_mask = (
                (validated_data['high'] >= validated_data[['open', 'close']].max(axis=1)) &
                (validated_data['low'] <= validated_data[['open', 'close']].min(axis=1))
            )
            validated_data = validated_data[valid_mask]
        
        # 移除异常波动的数据（价格变化超过50%）
        if 'close' in validated_data.columns and len(validated_data) > 1:
            price_changes = validated_data['close'].pct_change().abs()
            validated_data = validated_data[price_changes <= 0.5]
        
        removed_count = len(data) - len(validated_data)
        if removed_count > 0:
            self.logger.info(f"Removed {removed_count} invalid records during validation")
        
        return validated_data
    
    def _calculate_data_quality_score(self, data: pd.DataFrame) -> float:
        """计算数据质量分数"""
        if data.empty:
            return 0.0
        
        score = 1.0
        
        # 检查必要字段的完整性
        required_fields = ['open', 'high', 'low', 'close', 'volume']
        available_fields = [field for field in required_fields if field in data.columns]
        completeness_score = len(available_fields) / len(required_fields)
        
        # 检查数据的连续性
        if len(data) > 1:
            time_gaps = data.index.to_series().diff().dt.total_seconds()
            expected_interval = time_gaps.median()
            large_gaps = (time_gaps > expected_interval * 2).sum()
            continuity_score = max(0, 1 - (large_gaps / len(data)))
        else:
            continuity_score = 1.0
        
        # 检查价格数据的合理性
        if 'close' in data.columns:
            price_changes = data['close'].pct_change().abs()
            extreme_changes = (price_changes > 0.1).sum()  # 10%以上变化视为极端
            reasonableness_score = max(0, 1 - (extreme_changes / len(data)))
        else:
            reasonableness_score = 1.0
        
        # 综合评分
        score = (completeness_score * 0.4 + continuity_score * 0.3 + reasonableness_score * 0.3)
        
        return round(score, 3)
    
    def _calculate_gap_fill_quality_score(self, gaps: List[DataGap], filled_count: int) -> float:
        """计算缺口填充质量分数"""
        if not gaps:
            return 1.0
        
        total_gaps = len(gaps)
        critical_gaps = len([g for g in gaps if g.is_critical])
        
        # 基础填充率
        fill_ratio = filled_count / total_gaps
        
        # 如果填充了关键缺口，给予额外分数
        if critical_gaps > 0:
            critical_filled = min(filled_count, critical_gaps)
            critical_fill_ratio = critical_filled / critical_gaps
            score = fill_ratio * 0.6 + critical_fill_ratio * 0.4
        else:
            score = fill_ratio
        
        return round(score, 3)
    
    async def batch_update_symbols(self, symbols: List[str], granularity: DataGranularity,
                                 strategy: Optional[UpdateStrategy] = None) -> List[UpdateResult]:
        """批量更新多个股票的数据"""
        self.logger.info(f"Starting batch update for {len(symbols)} symbols with {granularity.value}")
        
        # 创建并发任务
        tasks = []
        for symbol in symbols:
            task = self.update_symbol_data(symbol, granularity, strategy)
            tasks.append(task)
        
        # 执行并发更新
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        update_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 创建失败结果
                failed_result = UpdateResult(
                    symbol=symbols[i],
                    granularity=granularity,
                    strategy=strategy or self.config.strategy,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    success=False,
                    error_message=str(result)
                )
                update_results.append(failed_result)
            else:
                update_results.append(result)
        
        # 统计批量更新结果
        successful_count = len([r for r in update_results if r.success])
        total_records = sum(r.records_added for r in update_results if r.success)
        total_gaps = sum(r.gaps_filled for r in update_results if r.success)
        
        self.logger.info(f"Batch update completed: {successful_count}/{len(symbols)} successful, "
                        f"{total_records} records added, {total_gaps} gaps filled")
        
        return update_results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取更新统计信息"""
        return self.stats.copy()
    
    def get_update_history(self, symbol: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取更新历史"""
        history = self.update_history
        
        if symbol:
            history = [result for result in history if result.symbol == symbol]
        
        # 按时间倒序排列
        history = sorted(history, key=lambda x: x.start_time, reverse=True)
        
        # 限制返回数量
        history = history[:limit]
        
        # 转换为字典格式
        return [
            {
                'symbol': result.symbol,
                'granularity': result.granularity.value,
                'strategy': result.strategy.value,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat(),
                'records_added': result.records_added,
                'records_updated': result.records_updated,
                'records_skipped': result.records_skipped,
                'gaps_filled': result.gaps_filled,
                'execution_time_seconds': result.execution_time_seconds,
                'success': result.success,
                'error_message': result.error_message,
                'data_quality_score': result.data_quality_score
            }
            for result in history
        ]
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'total_records_added': 0,
            'total_records_updated': 0,
            'total_gaps_filled': 0,
            'average_update_time': 0.0,
            'last_update_time': None
        }
        self.update_history.clear()
        self.logger.info("Statistics reset completed")