"""
增量数据更新器测试模块

测试覆盖：
- 不同更新策略的执行
- 数据时间戳检查和去重
- 数据缺口检测和填充
- 批量更新功能
- 数据质量验证
- 统计信息和历史记录
"""

import asyncio
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# 添加src路径以便导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'collectors'))

from incremental_data_updater import (
    IncrementalDataUpdater,
    UpdateConfig,
    UpdateStrategy,
    DataGranularity,
    DataGap,
    UpdateResult,
    MockDataSource,
    MockStorage
)


class TestUpdateConfig:
    """测试UpdateConfig类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = UpdateConfig()
        
        assert config.strategy == UpdateStrategy.INCREMENTAL
        assert config.granularity == DataGranularity.DAILY
        assert config.lookback_days == 7
        assert config.batch_size == 1000
        assert config.enable_gap_detection is True
        assert config.enable_duplicate_check is True
        assert config.enable_data_validation is True
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = UpdateConfig(
            strategy=UpdateStrategy.SMART_FILL,
            granularity=DataGranularity.MINUTE_5,
            lookback_days=30,
            batch_size=5000,
            max_concurrent_updates=10,
            enable_gap_detection=False
        )
        
        assert config.strategy == UpdateStrategy.SMART_FILL
        assert config.granularity == DataGranularity.MINUTE_5
        assert config.lookback_days == 30
        assert config.batch_size == 5000
        assert config.max_concurrent_updates == 10
        assert config.enable_gap_detection is False


class TestDataGap:
    """测试DataGap类"""
    
    def test_gap_creation(self):
        """测试数据缺口创建"""
        start_time = datetime(2024, 1, 1, 10, 0)
        end_time = datetime(2024, 1, 1, 14, 0)
        
        gap = DataGap(
            symbol="AAPL",
            granularity=DataGranularity.MINUTE_5,
            start_time=start_time,
            end_time=end_time,
            expected_points=48,  # 4小时 * 12个5分钟
            actual_points=20,
            gap_ratio=0.58,
            priority=2
        )
        
        assert gap.symbol == "AAPL"
        assert gap.granularity == DataGranularity.MINUTE_5
        assert gap.duration_minutes == 240  # 4小时
        assert gap.is_critical is True  # gap_ratio > 0.5
    
    def test_gap_properties(self):
        """测试缺口属性"""
        # 非关键缺口
        gap1 = DataGap(
            symbol="GOOGL",
            granularity=DataGranularity.MINUTE_1,
            start_time=datetime(2024, 1, 1, 10, 0),
            end_time=datetime(2024, 1, 1, 10, 30),
            expected_points=30,
            actual_points=25,
            gap_ratio=0.17
        )
        
        assert gap1.duration_minutes == 30
        assert gap1.is_critical is False  # gap_ratio < 0.5 and duration < 240min
        
        # 关键缺口（长时间）
        gap2 = DataGap(
            symbol="MSFT",
            granularity=DataGranularity.MINUTE_1,
            start_time=datetime(2024, 1, 1, 10, 0),
            end_time=datetime(2024, 1, 1, 15, 0),  # 5小时
            expected_points=300,
            actual_points=280,
            gap_ratio=0.07
        )
        
        assert gap2.duration_minutes == 300
        assert gap2.is_critical is True  # duration > 240min


class TestUpdateResult:
    """测试UpdateResult类"""
    
    def test_result_creation(self):
        """测试更新结果创建"""
        result = UpdateResult(
            symbol="AAPL",
            granularity=DataGranularity.DAILY,
            strategy=UpdateStrategy.INCREMENTAL,
            start_time=datetime(2024, 1, 1),
            end_time=datetime(2024, 1, 2),
            records_added=100,
            records_updated=50,
            gaps_filled=2,
            execution_time_seconds=5.5,
            success=True,
            data_quality_score=0.95
        )
        
        assert result.symbol == "AAPL"
        assert result.granularity == DataGranularity.DAILY
        assert result.strategy == UpdateStrategy.INCREMENTAL
        assert result.records_added == 100
        assert result.records_updated == 50
        assert result.gaps_filled == 2
        assert result.execution_time_seconds == 5.5
        assert result.success is True
        assert result.data_quality_score == 0.95


class TestMockDataSource:
    """测试MockDataSource类"""
    
    def setup_method(self):
        """测试前设置"""
        self.data_source = MockDataSource()
    
    @pytest.mark.asyncio
    async def test_get_latest_timestamp(self):
        """测试获取最新时间戳"""
        timestamp = await self.data_source.get_latest_timestamp("AAPL", DataGranularity.DAILY)
        
        assert timestamp is not None
        assert isinstance(timestamp, datetime)
        assert timestamp < datetime.now()
    
    @pytest.mark.asyncio
    async def test_get_data_range(self):
        """测试获取数据范围"""
        start_time = datetime(2024, 1, 1)
        end_time = datetime(2024, 1, 2)
        
        data = await self.data_source.get_data_range("AAPL", DataGranularity.MINUTE_5, start_time, end_time)
        
        assert isinstance(data, pd.DataFrame)
        if not data.empty:
            assert 'open' in data.columns
            assert 'high' in data.columns
            assert 'low' in data.columns
            assert 'close' in data.columns
            assert 'volume' in data.columns
            assert all(data['open'] > 0)
            assert all(data['volume'] >= 0)
    
    @pytest.mark.asyncio
    async def test_check_data_completeness(self):
        """测试数据完整性检查"""
        start_time = datetime(2024, 1, 1)
        end_time = datetime(2024, 1, 1, 1, 0)  # 1小时
        
        completeness = await self.data_source.check_data_completeness(
            "AAPL", DataGranularity.MINUTE_5, start_time, end_time
        )
        
        assert 'symbol' in completeness
        assert 'granularity' in completeness
        assert 'expected_points' in completeness
        assert 'actual_points' in completeness
        assert 'completeness_ratio' in completeness
        assert completeness['symbol'] == "AAPL"
        assert completeness['granularity'] == DataGranularity.MINUTE_5.value


class TestMockStorage:
    """测试MockStorage类"""
    
    def setup_method(self):
        """测试前设置"""
        self.storage = MockStorage()
    
    @pytest.mark.asyncio
    async def test_store_and_retrieve_data(self):
        """测试数据存储和检索"""
        # 创建测试数据
        timestamps = pd.date_range(start='2024-01-01', periods=10, freq='5T')
        data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 10),
            'high': np.random.uniform(110, 120, 10),
            'low': np.random.uniform(90, 100, 10),
            'close': np.random.uniform(100, 110, 10),
            'volume': np.random.randint(1000, 10000, 10)
        }, index=timestamps)
        
        # 存储数据
        success = await self.storage.store_data("AAPL", DataGranularity.MINUTE_5, data)
        assert success is True
        
        # 检查最新时间戳
        latest_timestamp = await self.storage.get_latest_timestamp("AAPL", DataGranularity.MINUTE_5)
        assert latest_timestamp is not None
        assert latest_timestamp == timestamps[-1].to_pydatetime()
    
    @pytest.mark.asyncio
    async def test_get_data_gaps(self):
        """测试获取数据缺口"""
        start_time = datetime(2024, 1, 1)
        end_time = datetime(2024, 1, 2)
        
        gaps = await self.storage.get_data_gaps("AAPL", DataGranularity.MINUTE_5, start_time, end_time)
        
        assert isinstance(gaps, list)
        for gap in gaps:
            assert isinstance(gap, DataGap)
            assert gap.symbol == "AAPL"
            assert gap.granularity == DataGranularity.MINUTE_5
            assert gap.start_time >= start_time
            assert gap.end_time <= end_time
    
    @pytest.mark.asyncio
    async def test_get_data_statistics(self):
        """测试获取数据统计"""
        start_time = datetime(2024, 1, 1)
        end_time = datetime(2024, 1, 2)
        
        stats = await self.storage.get_data_statistics("AAPL", DataGranularity.MINUTE_5, start_time, end_time)
        
        assert 'symbol' in stats
        assert 'granularity' in stats
        assert 'total_records' in stats
        assert 'start_time' in stats
        assert 'end_time' in stats
        assert stats['symbol'] == "AAPL"
        assert stats['granularity'] == DataGranularity.MINUTE_5.value


class TestIncrementalDataUpdater:
    """测试IncrementalDataUpdater类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = UpdateConfig(
            strategy=UpdateStrategy.INCREMENTAL,
            granularity=DataGranularity.MINUTE_5,
            lookback_days=1,
            batch_size=100,
            max_concurrent_updates=2
        )
        self.data_source = MockDataSource()
        self.storage = MockStorage()
        self.updater = IncrementalDataUpdater(self.config, self.data_source, self.storage)
    
    @pytest.mark.asyncio
    async def test_full_refresh_update(self):
        """测试全量刷新更新"""
        result = await self.updater.update_symbol_data("AAPL", DataGranularity.MINUTE_5, UpdateStrategy.FULL_REFRESH)
        
        assert isinstance(result, UpdateResult)
        assert result.symbol == "AAPL"
        assert result.granularity == DataGranularity.MINUTE_5
        assert result.strategy == UpdateStrategy.FULL_REFRESH
        assert result.success is True
        assert result.records_added >= 0
        assert result.execution_time_seconds > 0
    
    @pytest.mark.asyncio
    async def test_incremental_update_no_existing_data(self):
        """测试增量更新（无现有数据）"""
        result = await self.updater.update_symbol_data("GOOGL", DataGranularity.MINUTE_5, UpdateStrategy.INCREMENTAL)
        
        assert result.success is True
        # 当没有现有数据时，应该执行全量刷新
        assert result.records_added >= 0
    
    @pytest.mark.asyncio
    async def test_incremental_update_with_existing_data(self):
        """测试增量更新（有现有数据）"""
        # 先存储一些历史数据
        timestamps = pd.date_range(start='2024-01-01', periods=10, freq='5T')
        historical_data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 10),
            'high': np.random.uniform(110, 120, 10),
            'low': np.random.uniform(90, 100, 10),
            'close': np.random.uniform(100, 110, 10),
            'volume': np.random.randint(1000, 10000, 10)
        }, index=timestamps)
        
        await self.storage.store_data("MSFT", DataGranularity.MINUTE_5, historical_data)
        
        # 执行增量更新
        result = await self.updater.update_symbol_data("MSFT", DataGranularity.MINUTE_5, UpdateStrategy.INCREMENTAL)
        
        assert result.success is True
        assert result.strategy == UpdateStrategy.INCREMENTAL
    
    @pytest.mark.asyncio
    async def test_smart_fill_update(self):
        """测试智能填充更新"""
        result = await self.updater.update_symbol_data("TSLA", DataGranularity.MINUTE_5, UpdateStrategy.SMART_FILL)
        
        assert result.success is True
        assert result.strategy == UpdateStrategy.SMART_FILL
        assert result.gaps_filled >= 0
    
    @pytest.mark.asyncio
    async def test_time_based_update(self):
        """测试基于时间的更新"""
        result = await self.updater.update_symbol_data("NVDA", DataGranularity.MINUTE_5, UpdateStrategy.TIME_BASED)
        
        assert result.success is True
        assert result.strategy == UpdateStrategy.TIME_BASED
    
    @pytest.mark.asyncio
    async def test_change_detection_update(self):
        """测试变化检测更新"""
        result = await self.updater.update_symbol_data("AMD", DataGranularity.MINUTE_5, UpdateStrategy.CHANGE_DETECTION)
        
        assert result.success is True
        assert result.strategy == UpdateStrategy.CHANGE_DETECTION
    
    @pytest.mark.asyncio
    async def test_batch_update_symbols(self):
        """测试批量更新股票"""
        symbols = ["AAPL", "GOOGL", "MSFT", "TSLA"]
        
        results = await self.updater.batch_update_symbols(symbols, DataGranularity.MINUTE_5)
        
        assert len(results) == len(symbols)
        for i, result in enumerate(results):
            assert isinstance(result, UpdateResult)
            assert result.symbol == symbols[i]
            assert result.granularity == DataGranularity.MINUTE_5
    
    @pytest.mark.asyncio
    async def test_concurrent_updates(self):
        """测试并发更新限制"""
        symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA", "AMD"]  # 超过max_concurrent_updates
        
        start_time = asyncio.get_event_loop().time()
        results = await self.updater.batch_update_symbols(symbols, DataGranularity.MINUTE_5)
        end_time = asyncio.get_event_loop().time()
        
        assert len(results) == len(symbols)
        # 由于并发限制，总时间应该比串行执行短，但比单个任务长
        assert end_time - start_time > 0
    
    def test_data_validation(self):
        """测试数据验证"""
        # 创建包含无效数据的DataFrame
        timestamps = pd.date_range(start='2024-01-01', periods=10, freq='5T')
        invalid_data = pd.DataFrame({
            'open': [100, -50, 0, 105, 110, 108, 1000, 95, 102, 98],  # 包含负数、零和异常值
            'high': [110, 120, 115, 115, 120, 118, 1100, 105, 112, 108],
            'low': [90, 80, 85, 95, 100, 98, 900, 85, 92, 88],
            'close': [105, 110, 100, 110, 115, 113, 1050, 100, 107, 103],
            'volume': [1000, -500, 2000, 1500, 1800, 1600, 2200, 1200, 1400, 1300]  # 包含负数
        }, index=timestamps)
        
        validated_data = self.updater._validate_data(invalid_data)
        
        # 验证无效数据被移除
        assert len(validated_data) < len(invalid_data)
        assert all(validated_data['open'] > 0)
        assert all(validated_data['volume'] >= 0)
        
        # 验证价格逻辑关系
        if not validated_data.empty:
            assert all(validated_data['high'] >= validated_data[['open', 'close']].max(axis=1))
            assert all(validated_data['low'] <= validated_data[['open', 'close']].min(axis=1))
    
    def test_remove_duplicates(self):
        """测试去重功能"""
        # 创建包含重复时间戳的数据
        timestamps = pd.date_range(start='2024-01-01', periods=5, freq='5T')
        duplicate_timestamps = list(timestamps) + [timestamps[2], timestamps[3]]  # 添加重复时间戳
        
        data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 7),
            'close': np.random.uniform(100, 110, 7),
            'volume': np.random.randint(1000, 10000, 7)
        }, index=duplicate_timestamps)
        
        latest_timestamp = timestamps[1]  # 设置一个较早的最新时间戳
        
        deduplicated_data = self.updater._remove_duplicates(data, latest_timestamp)
        
        # 验证重复时间戳被移除
        assert not deduplicated_data.index.duplicated().any()
        
        # 验证只保留晚于latest_timestamp的数据
        assert all(deduplicated_data.index > latest_timestamp)
    
    def test_data_quality_score_calculation(self):
        """测试数据质量分数计算"""
        # 创建高质量数据
        timestamps = pd.date_range(start='2024-01-01', periods=10, freq='5T')
        high_quality_data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 10),
            'high': np.random.uniform(110, 120, 10),
            'low': np.random.uniform(90, 100, 10),
            'close': np.random.uniform(100, 110, 10),
            'volume': np.random.randint(1000, 10000, 10)
        }, index=timestamps)
        
        # 确保价格逻辑关系正确
        for i in range(len(high_quality_data)):
            high_quality_data.iloc[i, high_quality_data.columns.get_loc('high')] = max(
                high_quality_data.iloc[i]['open'], high_quality_data.iloc[i]['close']
            ) * 1.01
            high_quality_data.iloc[i, high_quality_data.columns.get_loc('low')] = min(
                high_quality_data.iloc[i]['open'], high_quality_data.iloc[i]['close']
            ) * 0.99
        
        score = self.updater._calculate_data_quality_score(high_quality_data)
        
        assert 0 <= score <= 1
        assert score > 0.8  # 高质量数据应该有高分数
        
        # 测试空数据
        empty_score = self.updater._calculate_data_quality_score(pd.DataFrame())
        assert empty_score == 0.0
    
    def test_gap_fill_quality_score(self):
        """测试缺口填充质量分数"""
        # 创建一些数据缺口
        gaps = [
            DataGap("AAPL", DataGranularity.MINUTE_5, datetime(2024, 1, 1, 10, 0), 
                   datetime(2024, 1, 1, 11, 0), 12, 8, 0.33, 1),
            DataGap("AAPL", DataGranularity.MINUTE_5, datetime(2024, 1, 1, 14, 0), 
                   datetime(2024, 1, 1, 18, 0), 48, 10, 0.79, 3),  # 关键缺口
            DataGap("AAPL", DataGranularity.MINUTE_5, datetime(2024, 1, 1, 20, 0), 
                   datetime(2024, 1, 1, 21, 0), 12, 12, 0.0, 1)
        ]
        
        # 测试部分填充
        score1 = self.updater._calculate_gap_fill_quality_score(gaps, 2)
        assert 0 <= score1 <= 1
        
        # 测试全部填充
        score2 = self.updater._calculate_gap_fill_quality_score(gaps, 3)
        assert score2 == 1.0
        
        # 测试无缺口
        score3 = self.updater._calculate_gap_fill_quality_score([], 0)
        assert score3 == 1.0
    
    def test_statistics_and_history(self):
        """测试统计信息和历史记录"""
        # 初始统计信息
        initial_stats = self.updater.get_statistics()
        assert initial_stats['total_updates'] == 0
        assert initial_stats['successful_updates'] == 0
        assert initial_stats['failed_updates'] == 0
        
        # 初始历史记录
        initial_history = self.updater.get_update_history()
        assert len(initial_history) == 0
    
    def test_callbacks(self):
        """测试回调函数"""
        progress_callback = Mock()
        completion_callback = Mock()
        
        self.updater.set_callbacks(
            progress_callback=progress_callback,
            completion_callback=completion_callback
        )
        
        assert self.updater.progress_callback == progress_callback
        assert self.updater.completion_callback == completion_callback
    
    def test_reset_statistics(self):
        """测试重置统计信息"""
        # 手动设置一些统计数据
        self.updater.stats['total_updates'] = 10
        self.updater.stats['successful_updates'] = 8
        self.updater.stats['failed_updates'] = 2
        
        # 添加一些历史记录
        result = UpdateResult(
            symbol="TEST",
            granularity=DataGranularity.MINUTE_5,
            strategy=UpdateStrategy.INCREMENTAL,
            start_time=datetime.now(),
            end_time=datetime.now()
        )
        self.updater.update_history.append(result)
        
        # 重置统计信息
        self.updater.reset_statistics()
        
        # 验证重置结果
        stats = self.updater.get_statistics()
        assert stats['total_updates'] == 0
        assert stats['successful_updates'] == 0
        assert stats['failed_updates'] == 0
        
        history = self.updater.get_update_history()
        assert len(history) == 0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])