#include "test_utils.h"
#include "financial_data_sdk.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace financial_data::sdk;
using namespace financial_data::sdk::test;

// Test utility functions
void TestUtilityFunctions() {
    // Test timestamp conversion
    auto now = std::chrono::system_clock::now();
    int64_t ns = utils::SystemTimeToNanoseconds(now);
    auto converted_back = utils::NanosecondsToSystemTime(ns);
    
    auto diff = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::abs(now - converted_back));
    Assert(diff.count() < 1, "Timestamp conversion should be accurate within 1ms");
    
    // Test symbol validation
    Assert(utils::ValidateSymbol("CU2409"), "Valid symbol should pass validation");
    Assert(!utils::ValidateSymbol(""), "Empty symbol should fail validation");
    Assert(!utils::ValidateSymbol(std::string(50, 'A')), "Too long symbol should fail validation");
    
    // Test exchange validation
    Assert(utils::ValidateExchange("SHFE"), "Valid exchange should pass validation");
    Assert(!utils::ValidateExchange(""), "Empty exchange should fail validation");
    
    // Test timestamp validation
    auto current_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
    Assert(utils::ValidateTimestamp(current_ns), "Current timestamp should be valid");
    Assert(!utils::ValidateTimestamp(0), "Zero timestamp should be invalid");
    Assert(!utils::ValidateTimestamp(current_ns + 2LL * 365 * 24 * 3600 * 1000000000LL), 
           "Future timestamp should be invalid");
}

void TestLatencyMeasurer() {
    utils::LatencyMeasurer measurer;
    
    measurer.Start();
    std::this_thread::sleep_for(std::chrono::microseconds(1000)); // Sleep 1ms
    auto latency = measurer.Stop();
    
    // Should be approximately 1000 microseconds, allow some tolerance
    Assert(latency.count() >= 900 && latency.count() <= 1100, 
           "Latency measurement should be approximately 1000μs");
    
    AssertEqual(latency.count(), measurer.GetLastLatency().count(), 
                "GetLastLatency should return the same value as Stop()");
}

void TestDataStructures() {
    // Test StandardTick
    StandardTick tick = CreateTestTick("CU2409", 78560.0, 1000);
    
    Assert(tick.IsValid(), "Test tick should be valid");
    AssertEqual(std::string("CU2409"), tick.symbol, "Symbol should match");
    AssertEqual(78560.0, tick.last_price, 1e-6, "Price should match");
    AssertEqual(1000ULL, tick.volume, "Volume should match");
    
    // Test invalid tick
    StandardTick invalid_tick;
    Assert(!invalid_tick.IsValid(), "Empty tick should be invalid");
    
    // Test Level2Data
    Level2Data level2 = CreateTestLevel2("AL2409", 5);
    
    Assert(level2.IsValid(), "Test level2 should be valid");
    AssertEqual(std::string("AL2409"), level2.symbol, "Level2 symbol should match");
    AssertEqual(5UL, level2.bids.size(), "Should have 5 bid levels");
    AssertEqual(5UL, level2.asks.size(), "Should have 5 ask levels");
    
    // Test bid/ask ordering
    for (size_t i = 1; i < level2.bids.size(); ++i) {
        Assert(level2.bids[i-1].price > level2.bids[i].price, 
               "Bids should be in descending price order");
    }
    
    for (size_t i = 1; i < level2.asks.size(); ++i) {
        Assert(level2.asks[i-1].price < level2.asks[i].price, 
               "Asks should be in ascending price order");
    }
}

void TestMarketDataWrapper() {
    StandardTick tick = CreateTestTick("CU2409", 78560.0, 1000);
    Level2Data level2 = CreateTestLevel2("AL2409", 5);
    
    // Test tick wrapper
    MarketDataWrapper tick_wrapper(tick);
    Assert(tick_wrapper.type == MarketDataWrapper::DataType::TICK, 
           "Wrapper should have TICK type");
    Assert(tick_wrapper.IsValid(), "Tick wrapper should be valid");
    AssertEqual(tick.symbol, tick_wrapper.tick_data.symbol, 
                "Wrapped tick symbol should match");
    
    // Test level2 wrapper
    MarketDataWrapper level2_wrapper(level2);
    Assert(level2_wrapper.type == MarketDataWrapper::DataType::LEVEL2, 
           "Wrapper should have LEVEL2 type");
    Assert(level2_wrapper.IsValid(), "Level2 wrapper should be valid");
    AssertEqual(level2.symbol, level2_wrapper.level2_data.symbol, 
                "Wrapped level2 symbol should match");
}

void TestMarketDataBatch() {
    MarketDataBatch batch;
    
    Assert(batch.IsEmpty(), "New batch should be empty");
    AssertEqual(0UL, batch.Size(), "New batch size should be 0");
    
    // Add some data
    StandardTick tick1 = CreateTestTick("CU2409", 78560.0, 1000);
    StandardTick tick2 = CreateTestTick("AL2409", 19850.0, 2000);
    Level2Data level2 = CreateTestLevel2("ZN2409", 5);
    
    batch.AddTick(tick1);
    batch.AddTick(tick2);
    batch.AddLevel2(level2);
    
    Assert(!batch.IsEmpty(), "Batch with data should not be empty");
    AssertEqual(3UL, batch.Size(), "Batch should have 3 items");
    
    // Test data retrieval
    AssertEqual(tick1.symbol, batch.data[0].tick_data.symbol, 
                "First item should be tick1");
    AssertEqual(tick2.symbol, batch.data[1].tick_data.symbol, 
                "Second item should be tick2");
    AssertEqual(level2.symbol, batch.data[2].level2_data.symbol, 
                "Third item should be level2");
    
    // Test clear
    batch.Clear();
    Assert(batch.IsEmpty(), "Cleared batch should be empty");
    AssertEqual(0UL, batch.Size(), "Cleared batch size should be 0");
}

void TestConnectionConfig() {
    ConnectionConfig config;
    
    // Test default values
    AssertEqual(std::string("localhost:50051"), config.server_address, 
                "Default server address should be localhost:50051");
    Assert(config.connect_timeout.count() > 0, "Connect timeout should be positive");
    Assert(config.request_timeout.count() > 0, "Request timeout should be positive");
    Assert(config.max_retry_attempts > 0, "Max retry attempts should be positive");
    Assert(config.retry_interval.count() > 0, "Retry interval should be positive");
    
    // Test custom configuration
    config.server_address = "production.server.com:443";
    config.auth_token = "test_token";
    config.connect_timeout = std::chrono::milliseconds(10000);
    config.enable_compression = false;
    
    AssertEqual(std::string("production.server.com:443"), config.server_address, 
                "Custom server address should be set");
    AssertEqual(std::string("test_token"), config.auth_token, 
                "Auth token should be set");
    AssertEqual(10000LL, config.connect_timeout.count(), 
                "Custom connect timeout should be set");
    Assert(!config.enable_compression, "Compression should be disabled");
}

void TestSubscriptionConfig() {
    SubscriptionConfig config;
    
    // Test default values
    Assert(config.symbols.empty(), "Default symbols should be empty");
    Assert(config.exchange.empty(), "Default exchange should be empty");
    Assert(!config.include_level2, "Level2 should be disabled by default");
    Assert(config.buffer_size > 0, "Buffer size should be positive");
    
    // Test custom configuration
    config.symbols = {"CU2409", "AL2409", "ZN2409"};
    config.exchange = "SHFE";
    config.include_level2 = true;
    config.buffer_size = 2000;
    
    AssertEqual(3UL, config.symbols.size(), "Should have 3 symbols");
    AssertEqual(std::string("SHFE"), config.exchange, "Exchange should be SHFE");
    Assert(config.include_level2, "Level2 should be enabled");
    AssertEqual(2000, config.buffer_size, "Buffer size should be 2000");
}

void TestErrorInfo() {
    auto before = std::chrono::system_clock::now();
    ErrorInfo error(ErrorCode::CONNECTION_FAILED, "Test connection error");
    auto after = std::chrono::system_clock::now();
    
    Assert(error.code == ErrorCode::CONNECTION_FAILED, 
           "Error code should be CONNECTION_FAILED");
    AssertEqual(std::string("Test connection error"), error.message, 
                "Error message should match");
    
    // Check timestamp is reasonable
    Assert(error.timestamp >= before && error.timestamp <= after, 
           "Error timestamp should be within reasonable range");
}

int main() {
    std::cout << "Running Financial Data SDK Unit Tests" << std::endl;
    std::cout << "=====================================" << std::endl;

    TestFramework::Instance().Reset();

    // Run all unit tests
    RUN_TEST(TestUtilityFunctions);
    RUN_TEST(TestLatencyMeasurer);
    RUN_TEST(TestDataStructures);
    RUN_TEST(TestMarketDataWrapper);
    RUN_TEST(TestMarketDataBatch);
    RUN_TEST(TestConnectionConfig);
    RUN_TEST(TestSubscriptionConfig);
    RUN_TEST(TestErrorInfo);

    // Print summary
    TestFramework::Instance().PrintSummary();

    return TestFramework::Instance().AllTestsPassed() ? 0 : 1;
}