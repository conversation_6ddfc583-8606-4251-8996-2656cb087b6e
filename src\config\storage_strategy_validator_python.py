"""
Python implementation of storage strategy configuration validator
"""

import re
from typing import Dict, Any, List, Set
from config_manager_python import ConfigValidator, ValidationResult


class StorageStrategyValidator(ConfigValidator):
    """存储策略配置验证器"""
    
    # 支持的数据类型
    SUPPORTED_DATA_TYPES: Set[str] = {
        "tick", "kline", "level2", "fundamental", "news", "announcement"
    }
    
    # 支持的存储层
    SUPPORTED_STORAGE_LAYERS: Set[str] = {
        "hot", "warm", "cold"
    }
    
    # 支持的选择策略
    SUPPORTED_SELECTION_STRATEGIES: Set[str] = {
        "time_based", "performance_based", "load_balanced", "failover_only"
    }
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        """验证存储策略配置"""
        result = ValidationResult()
        
        # 检查是否存在storage配置
        if "storage" not in config:
            result.add_error("Missing 'storage' configuration section")
            return result
        
        storage_config = config["storage"]
        
        # 检查是否存在strategy配置
        if "strategy" not in storage_config:
            result.add_warning("Missing 'strategy' configuration in storage section, using defaults")
            return result
        
        strategy_config = storage_config["strategy"]
        
        # 验证基本策略配置
        self._validate_basic_strategy(strategy_config, result)
        
        # 验证阈值配置
        if "thresholds" in strategy_config:
            self._validate_thresholds(strategy_config["thresholds"], result)
        
        # 验证数据类型配置
        if "data_type_configs" in strategy_config:
            self._validate_data_type_configs(strategy_config["data_type_configs"], result)
        
        # 验证迁移策略配置
        if "migration_policies" in strategy_config:
            self._validate_migration_policies(strategy_config["migration_policies"], result)
        
        # 验证时间配置的一致性
        self._validate_time_consistency(strategy_config, result)
        
        return result
    
    def get_validator_name(self) -> str:
        """获取验证器名称"""
        return "StorageStrategyValidator"
    
    def _validate_basic_strategy(self, strategy_config: Dict[str, Any], 
                                result: ValidationResult) -> None:
        """验证基本策略配置"""
        # 验证选择策略
        if "selection_strategy" in strategy_config:
            if not isinstance(strategy_config["selection_strategy"], str):
                result.add_error("selection_strategy must be a string")
            else:
                strategy = strategy_config["selection_strategy"]
                if not self._validate_selection_strategy(strategy):
                    result.add_error(f"Invalid selection_strategy: {strategy}. "
                                   f"Supported strategies: {', '.join(self.SUPPORTED_SELECTION_STRATEGIES)}")
        
        # 验证布尔配置
        bool_configs = [
            "enable_automatic_failover", "enable_load_balancing"
        ]
        
        for config_name in bool_configs:
            if config_name in strategy_config:
                if not isinstance(strategy_config[config_name], bool):
                    result.add_error(f"{config_name} must be a boolean value")
        
        # 验证数值配置
        numeric_configs = {
            "health_check_interval_seconds": (1.0, 3600.0),
            "health_check_timeout_seconds": (1.0, 300.0),
            "max_consecutive_failures": (1.0, 100.0),
            "failover_cooldown_seconds": (1.0, 7200.0),
            "max_failover_attempts": (1.0, 10.0),
            "load_balance_threshold": (0.1, 1.0)
        }
        
        for config_name, (min_val, max_val) in numeric_configs.items():
            if config_name in strategy_config:
                if not isinstance(strategy_config[config_name], (int, float)):
                    result.add_error(f"{config_name} must be a numeric value")
                else:
                    value = float(strategy_config[config_name])
                    if not self._validate_numeric_range(value, min_val, max_val):
                        result.add_error(f"{config_name} must be between {min_val} and {max_val}")
    
    def _validate_thresholds(self, thresholds_config: Dict[str, Any], 
                           result: ValidationResult) -> None:
        """验证阈值配置"""
        # 验证存储天数阈值
        day_configs = {
            "hot_storage_days": (1.0, 365.0),
            "warm_storage_days": (1.0, 3650.0)
        }
        
        for config_name, (min_val, max_val) in day_configs.items():
            if config_name in thresholds_config:
                if not isinstance(thresholds_config[config_name], (int, float)):
                    result.add_error(f"thresholds.{config_name} must be a numeric value")
                else:
                    value = float(thresholds_config[config_name])
                    if not self._validate_numeric_range(value, min_val, max_val):
                        result.add_error(f"thresholds.{config_name} must be between {min_val} and {max_val} days")
        
        # 验证性能阈值
        perf_configs = {
            "max_response_time_ms": (1.0, 60000.0),
            "min_success_rate": (0.0, 1.0),
            "health_threshold_success_rate": (0.0, 1.0),
            "degraded_threshold_success_rate": (0.0, 1.0)
        }
        
        for config_name, (min_val, max_val) in perf_configs.items():
            if config_name in thresholds_config:
                if not isinstance(thresholds_config[config_name], (int, float)):
                    result.add_error(f"thresholds.{config_name} must be a numeric value")
                else:
                    value = float(thresholds_config[config_name])
                    if not self._validate_numeric_range(value, min_val, max_val):
                        result.add_error(f"thresholds.{config_name} must be between {min_val} and {max_val}")
        
        # 验证阈值逻辑一致性
        if ("hot_storage_days" in thresholds_config and 
            "warm_storage_days" in thresholds_config):
            hot_days = float(thresholds_config["hot_storage_days"])
            warm_days = float(thresholds_config["warm_storage_days"])
            
            if hot_days >= warm_days:
                result.add_error("hot_storage_days must be less than warm_storage_days")
        
        if ("health_threshold_success_rate" in thresholds_config and 
            "degraded_threshold_success_rate" in thresholds_config):
            health_rate = float(thresholds_config["health_threshold_success_rate"])
            degraded_rate = float(thresholds_config["degraded_threshold_success_rate"])
            
            if health_rate <= degraded_rate:
                result.add_error("health_threshold_success_rate must be greater than degraded_threshold_success_rate")
    
    def _validate_data_type_configs(self, data_type_configs: Dict[str, Any], 
                                  result: ValidationResult) -> None:
        """验证数据类型配置"""
        if not isinstance(data_type_configs, dict):
            result.add_error("data_type_configs must be an object")
            return
        
        for data_type, config in data_type_configs.items():
            # 验证数据类型是否支持
            if data_type not in self.SUPPORTED_DATA_TYPES:
                result.add_warning(f"Unknown data type: {data_type}. "
                                 f"Consider adding it to supported data types if needed")
            
            # 验证单个数据类型配置
            self._validate_data_type_config(data_type, config, result)
    
    def _validate_data_type_config(self, data_type: str, config: Dict[str, Any], 
                                 result: ValidationResult) -> None:
        """验证单个数据类型配置"""
        prefix = f"data_type_configs.{data_type}."
        
        # 验证必需字段
        required_fields = [
            "hot_storage_days", "warm_storage_days", "priority_storage"
        ]
        
        for field in required_fields:
            if field not in config:
                result.add_error(f"{prefix}{field} is required")
        
        # 验证存储天数
        day_configs = {
            "hot_storage_days": (1.0, 365.0),
            "warm_storage_days": (1.0, 3650.0)
        }
        
        for field_name, (min_val, max_val) in day_configs.items():
            if field_name in config:
                if not isinstance(config[field_name], (int, float)):
                    result.add_error(f"{prefix}{field_name} must be a numeric value")
                else:
                    value = float(config[field_name])
                    if not self._validate_numeric_range(value, min_val, max_val):
                        result.add_error(f"{prefix}{field_name} must be between {min_val} and {max_val} days")
        
        # 验证优先存储层
        if "priority_storage" in config:
            if not isinstance(config["priority_storage"], str):
                result.add_error(f"{prefix}priority_storage must be a string")
            else:
                priority = config["priority_storage"]
                if not self._validate_storage_layer(priority):
                    result.add_error(f"{prefix}priority_storage must be one of: hot, warm, cold")
        
        # 验证可选字段
        if "compression_enabled" in config and not isinstance(config["compression_enabled"], bool):
            result.add_error(f"{prefix}compression_enabled must be a boolean value")
        
        if "batch_size" in config:
            if not isinstance(config["batch_size"], int) or config["batch_size"] <= 0:
                result.add_error(f"{prefix}batch_size must be a positive integer")
        
        if "max_response_time_ms" in config:
            if not isinstance(config["max_response_time_ms"], (int, float)):
                result.add_error(f"{prefix}max_response_time_ms must be a numeric value")
            else:
                value = float(config["max_response_time_ms"])
                if not self._validate_numeric_range(value, 1.0, 60000.0):
                    result.add_error(f"{prefix}max_response_time_ms must be between 1 and 60000 ms")
        
        # 验证时间配置一致性
        if "hot_storage_days" in config and "warm_storage_days" in config:
            hot_days = float(config["hot_storage_days"])
            warm_days = float(config["warm_storage_days"])
            
            if hot_days >= warm_days:
                result.add_error(f"{prefix}hot_storage_days must be less than warm_storage_days")
    
    def _validate_migration_policies(self, migration_policies: Dict[str, Any], 
                                   result: ValidationResult) -> None:
        """验证迁移策略配置"""
        if not isinstance(migration_policies, dict):
            result.add_error("migration_policies must be an object")
            return
        
        for data_type, policy in migration_policies.items():
            # 验证数据类型是否支持
            if data_type not in self.SUPPORTED_DATA_TYPES:
                result.add_warning(f"Unknown data type in migration policy: {data_type}")
            
            # 验证单个迁移策略配置
            self._validate_migration_policy(data_type, policy, result)
    
    def _validate_migration_policy(self, data_type: str, policy: Dict[str, Any], 
                                 result: ValidationResult) -> None:
        """验证单个迁移策略配置"""
        prefix = f"migration_policies.{data_type}."
        
        # 验证必需字段
        required_fields = [
            "hot_to_warm_hours", "warm_to_cold_days", "auto_migration"
        ]
        
        for field in required_fields:
            if field not in policy:
                result.add_error(f"{prefix}{field} is required")
        
        # 验证时间配置
        if "hot_to_warm_hours" in policy:
            if not isinstance(policy["hot_to_warm_hours"], (int, float)):
                result.add_error(f"{prefix}hot_to_warm_hours must be a numeric value")
            else:
                value = float(policy["hot_to_warm_hours"])
                if not self._validate_numeric_range(value, 1.0, 8760.0):  # 1小时到1年
                    result.add_error(f"{prefix}hot_to_warm_hours must be between 1 and 8760 hours")
        
        if "warm_to_cold_days" in policy:
            if not isinstance(policy["warm_to_cold_days"], (int, float)):
                result.add_error(f"{prefix}warm_to_cold_days must be a numeric value")
            else:
                value = float(policy["warm_to_cold_days"])
                if not self._validate_numeric_range(value, 1.0, 3650.0):  # 1天到10年
                    result.add_error(f"{prefix}warm_to_cold_days must be between 1 and 3650 days")
        
        # 验证布尔配置
        if "auto_migration" in policy and not isinstance(policy["auto_migration"], bool):
            result.add_error(f"{prefix}auto_migration must be a boolean value")
        
        # 验证批量大小
        if "migration_batch_size" in policy:
            if not isinstance(policy["migration_batch_size"], int) or policy["migration_batch_size"] <= 0:
                result.add_error(f"{prefix}migration_batch_size must be a positive integer")
        
        # 验证cron表达式
        if "migration_schedule" in policy:
            if not isinstance(policy["migration_schedule"], str):
                result.add_error(f"{prefix}migration_schedule must be a string")
            else:
                cron_expr = policy["migration_schedule"]
                if not self._validate_cron_expression(cron_expr):
                    result.add_error(f"{prefix}migration_schedule contains invalid cron expression: {cron_expr}")
        
        # 验证时间配置逻辑一致性
        if "hot_to_warm_hours" in policy and "warm_to_cold_days" in policy:
            hot_to_warm_hours = float(policy["hot_to_warm_hours"])
            warm_to_cold_days = float(policy["warm_to_cold_days"])
            
            # 转换为相同单位进行比较（都转换为小时）
            warm_to_cold_hours = warm_to_cold_days * 24.0
            
            if hot_to_warm_hours >= warm_to_cold_hours:
                result.add_error(f"{prefix}hot_to_warm_hours must be less than warm_to_cold_days (converted to hours)")
    
    def _validate_cron_expression(self, cron_expr: str) -> bool:
        """验证cron表达式"""
        # 分割cron表达式
        parts = cron_expr.strip().split()
        if len(parts) != 5:
            return False
        
        # 验证每个部分
        ranges = [(0, 59), (0, 23), (1, 31), (1, 12), (0, 6)]  # 分, 时, 日, 月, 周
        
        for i, (part, (min_val, max_val)) in enumerate(zip(parts, ranges)):
            if not self._validate_cron_field(part, min_val, max_val):
                return False
        
        return True
    
    def _validate_cron_field(self, field: str, min_val: int, max_val: int) -> bool:
        """验证cron字段"""
        if field == "*":
            return True
        
        # 处理步长 (*/n)
        if field.startswith("*/"):
            try:
                step = int(field[2:])
                return step > 0 and step <= max_val
            except ValueError:
                return False
        
        # 处理范围和列表
        for part in field.split(","):
            if "-" in part:
                # 范围 (n-m)
                try:
                    start, end = map(int, part.split("-"))
                    if not (min_val <= start <= max_val and min_val <= end <= max_val and start <= end):
                        return False
                except ValueError:
                    return False
            else:
                # 单个数值
                try:
                    value = int(part)
                    if not (min_val <= value <= max_val):
                        return False
                except ValueError:
                    return False
        
        return True
    
    def _validate_storage_layer(self, layer: str) -> bool:
        """验证存储层名称"""
        return layer in self.SUPPORTED_STORAGE_LAYERS
    
    def _validate_selection_strategy(self, strategy: str) -> bool:
        """验证选择策略"""
        return strategy in self.SUPPORTED_SELECTION_STRATEGIES
    
    def _validate_numeric_range(self, value: float, min_val: float, max_val: float) -> bool:
        """验证数值范围"""
        return min_val <= value <= max_val
    
    def _validate_time_consistency(self, config: Dict[str, Any], 
                                 result: ValidationResult) -> None:
        """验证时间配置的一致性"""
        # 验证全局阈值与数据类型配置的一致性
        if "thresholds" not in config or "data_type_configs" not in config:
            return
        
        thresholds = config["thresholds"]
        data_type_configs = config["data_type_configs"]
        
        if "hot_storage_days" in thresholds and "warm_storage_days" in thresholds:
            global_hot_days = float(thresholds["hot_storage_days"])
            global_warm_days = float(thresholds["warm_storage_days"])
            
            # 检查每个数据类型的配置是否与全局配置冲突
            for data_type, type_config in data_type_configs.items():
                if ("hot_storage_days" in type_config and 
                    "warm_storage_days" in type_config):
                    type_hot_days = float(type_config["hot_storage_days"])
                    type_warm_days = float(type_config["warm_storage_days"])
                    
                    # 警告：数据类型配置与全局配置差异较大
                    if abs(type_hot_days - global_hot_days) > global_hot_days * 0.5:
                        result.add_warning(f"Data type '{data_type}' hot_storage_days ({type_hot_days}) "
                                         f"differs significantly from global setting ({global_hot_days})")
                    
                    if abs(type_warm_days - global_warm_days) > global_warm_days * 0.5:
                        result.add_warning(f"Data type '{data_type}' warm_storage_days ({type_warm_days}) "
                                         f"differs significantly from global setting ({global_warm_days})")
        
        # 验证迁移策略与存储配置的一致性
        if "migration_policies" in config:
            migration_policies = config["migration_policies"]
            
            for data_type, policy in migration_policies.items():
                if data_type in data_type_configs:
                    type_config = data_type_configs[data_type]
                    
                    if ("hot_to_warm_hours" in policy and 
                        "hot_storage_days" in type_config):
                        migration_hours = float(policy["hot_to_warm_hours"])
                        storage_days = float(type_config["hot_storage_days"])
                        storage_hours = storage_days * 24.0
                        
                        # 迁移时间应该与存储时间相匹配或略小
                        if migration_hours > storage_hours * 1.2:
                            result.add_warning(f"Migration policy for '{data_type}' "
                                             f"hot_to_warm_hours ({migration_hours}) is much larger than "
                                             f"hot_storage_days ({storage_days} days)")