#pragma once

#include <grpcpp/grpcpp.h>
#include <grpcpp/health_check_service_interface.h>
#include <grpcpp/ext/proto_server_reflection_plugin.h>
#include <memory>
#include <thread>
#include <atomic>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <chrono>

#include "market_data_service.grpc.pb.h"
#include "../proto/data_types.h"

namespace financial_data {

// Enhanced flow control and backpressure management
class StreamFlowController {
public:
    StreamFlowController(int32_t buffer_size = 1000, int32_t max_queue_size = 10000);
    
    bool CanSend() const;
    void OnMessageSent();
    void OnClientAck();
    void SetBufferSize(int32_t size);
    
    // Enhanced backpressure methods
    bool IsBackpressureActive() const;
    void ApplyBackpressure();
    void ReleaseBackpressure();
    double GetUtilizationRatio() const;
    void UpdateClientLatency(double latency_us);
    
private:
    std::atomic<int32_t> pending_messages_;
    std::atomic<int32_t> buffer_size_;
    std::atomic<int32_t> max_queue_size_;
    std::atomic<bool> backpressure_active_;
    std::atomic<double> client_latency_us_;
    mutable std::mutex mutex_;
    
    void AdjustBufferSize();
};

// Enhanced client connection manager for load balancing and failover
class ClientConnectionManager {
public:
    struct ClientInfo {
        std::string client_id;
        std::string server_address;
        std::chrono::steady_clock::time_point last_seen;
        int32_t active_streams;
        double cpu_usage;
        double avg_latency_us;
        int32_t error_count;
        bool healthy;
        bool primary;
    };
    
    struct LoadBalancingStats {
        int32_t total_clients;
        int32_t healthy_clients;
        int32_t total_streams;
        double avg_latency_us;
        std::string primary_server;
    };
    
    void RegisterClient(const std::string& client_id, const std::string& server_address, bool is_primary = false);
    void UnregisterClient(const std::string& client_id);
    void UpdateClientHealth(const std::string& client_id, bool healthy);
    void UpdateClientStats(const std::string& client_id, double latency_us, int32_t active_streams);
    void ReportClientError(const std::string& client_id);
    
    std::string SelectBestClient() const;
    std::string SelectPrimaryClient() const;
    std::vector<std::string> GetHealthyClients() const;
    LoadBalancingStats GetStats() const;
    
    // Failover management
    void TriggerFailover(const std::string& failed_client_id);
    bool IsFailoverInProgress() const;
    
private:
    mutable std::mutex clients_mutex_;
    std::unordered_map<std::string, ClientInfo> clients_;
    std::atomic<bool> failover_in_progress_;
    std::chrono::steady_clock::time_point last_failover_time_;
    
    double CalculateClientScore(const ClientInfo& client) const;
};

// High-performance data stream processor
class DataStreamProcessor {
public:
    DataStreamProcessor();
    ~DataStreamProcessor();
    
    void Start();
    void Stop();
    
    // Add data to stream queues
    void AddTickData(const TickData& tick);
    void AddKlineData(const KlineData& kline);
    void AddLevel2Data(const Level2Data& level2);
    
    // Subscribe to data streams
    void SubscribeTickData(const std::string& stream_id, 
                          const TickDataRequest& request,
                          grpc::ServerWriter<TickDataResponse>* writer);
    void SubscribeKlineData(const std::string& stream_id,
                           const KlineDataRequest& request,
                           grpc::ServerWriter<KlineDataResponse>* writer);
    void SubscribeLevel2Data(const std::string& stream_id,
                            const Level2DataRequest& request,
                            grpc::ServerWriter<Level2DataResponse>* writer);
    
    void UnsubscribeStream(const std::string& stream_id);
    
private:
    struct StreamSubscription {
        std::string stream_id;
        std::string type;
        std::vector<std::string> symbols;
        std::string exchange;
        std::unique_ptr<StreamFlowController> flow_controller;
        void* writer; // Type-erased writer pointer
        std::chrono::steady_clock::time_point last_activity;
        bool active;
    };
    
    std::atomic<bool> running_;
    std::thread processor_thread_;
    
    // Data queues
    std::queue<TickData> tick_queue_;
    std::queue<KlineData> kline_queue_;
    std::queue<Level2Data> level2_queue_;
    
    // Subscription management
    std::unordered_map<std::string, std::unique_ptr<StreamSubscription>> subscriptions_;
    mutable std::mutex subscriptions_mutex_;
    mutable std::mutex queues_mutex_;
    std::condition_variable queue_cv_;
    
    void ProcessorLoop();
    void ProcessTickData();
    void ProcessKlineData();
    void ProcessLevel2Data();
    void CleanupInactiveStreams();
    ResponseMetadata CreateResponseMetadata() const;
};

// Main gRPC service implementation
class MarketDataServiceImpl final : public MarketDataService::Service {
public:
    MarketDataServiceImpl();
    ~MarketDataServiceImpl();
    
    // Service methods
    grpc::Status StreamTickData(grpc::ServerContext* context,
                               const TickDataRequest* request,
                               grpc::ServerWriter<TickDataResponse>* writer) override;
    
    grpc::Status GetHistoricalTickData(grpc::ServerContext* context,
                                      const HistoricalTickDataRequest* request,
                                      grpc::ServerWriter<TickDataResponse>* writer) override;
    
    grpc::Status StreamKlineData(grpc::ServerContext* context,
                                const KlineDataRequest* request,
                                grpc::ServerWriter<KlineDataResponse>* writer) override;
    
    grpc::Status StreamLevel2Data(grpc::ServerContext* context,
                                 const Level2DataRequest* request,
                                 grpc::ServerWriter<Level2DataResponse>* writer) override;
    
    grpc::Status HealthCheck(grpc::ServerContext* context,
                            const HealthCheckRequest* request,
                            HealthCheckResponse* response) override;
    
    void Start(const std::string& server_address);
    void Stop();
    void WaitForTermination();
    
private:
    std::unique_ptr<grpc::Server> server_;
    std::unique_ptr<DataStreamProcessor> stream_processor_;
    std::unique_ptr<ClientConnectionManager> connection_manager_;
    std::atomic<bool> running_;
    
    std::string GenerateStreamId() const;
    ResponseMetadata CreateResponseMetadata() const;
    bool ValidateRequest(const TickDataRequest& request) const;
    bool ValidateRequest(const KlineDataRequest& request) const;
    bool ValidateRequest(const Level2DataRequest& request) const;
};

// gRPC server wrapper
class GrpcServer {
public:
    GrpcServer();
    ~GrpcServer();
    
    bool Initialize(const std::string& server_address, 
                   int max_receive_message_size = 4 * 1024 * 1024,
                   int max_send_message_size = 4 * 1024 * 1024);
    
    void Start();
    void Stop();
    void WaitForTermination();
    
    bool IsRunning() const { return running_; }
    
private:
    std::unique_ptr<MarketDataServiceImpl> service_impl_;
    std::string server_address_;
    std::atomic<bool> running_;
    int max_receive_message_size_;
    int max_send_message_size_;
};

} // namespace financial_data