// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: market_data_service.proto
// Protobuf C++ Version: 4.25.3

#ifndef GOOGLE_PROTOBUF_INCLUDED_market_5fdata_5fservice_2eproto_2epb_2eh
#define GOOGLE_PROTOBUF_INCLUDED_market_5fdata_5fservice_2eproto_2epb_2eh

#include <limits>
#include <string>
#include <type_traits>
#include <utility>

#include "google/protobuf/port_def.inc"
#if PROTOBUF_VERSION < 4025000
#error "This file was generated by a newer version of protoc which is"
#error "incompatible with your Protocol Buffer headers. Please update"
#error "your headers."
#endif  // PROTOBUF_VERSION

#if 4025003 < PROTOBUF_MIN_PROTOC_VERSION
#error "This file was generated by an older version of protoc which is"
#error "incompatible with your Protocol Buffer headers. Please"
#error "regenerate this file with a newer version of protoc."
#endif  // PROTOBUF_MIN_PROTOC_VERSION
#include "google/protobuf/port_undef.inc"
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/arena.h"
#include "google/protobuf/arenastring.h"
#include "google/protobuf/generated_message_tctable_decl.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/metadata_lite.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/message.h"
#include "google/protobuf/repeated_field.h"  // IWYU pragma: export
#include "google/protobuf/extension_set.h"  // IWYU pragma: export
#include "google/protobuf/generated_enum_reflection.h"
#include "google/protobuf/unknown_field_set.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"

#define PROTOBUF_INTERNAL_EXPORT_market_5fdata_5fservice_2eproto

namespace google {
namespace protobuf {
namespace internal {
class AnyMetadata;
}  // namespace internal
}  // namespace protobuf
}  // namespace google

// Internal implementation detail -- do not use these members.
struct TableStruct_market_5fdata_5fservice_2eproto {
  static const ::uint32_t offsets[];
};
extern const ::google::protobuf::internal::DescriptorTable
    descriptor_table_market_5fdata_5fservice_2eproto;
namespace financial_data {
class HealthCheckRequest;
struct HealthCheckRequestDefaultTypeInternal;
extern HealthCheckRequestDefaultTypeInternal _HealthCheckRequest_default_instance_;
class HealthCheckResponse;
struct HealthCheckResponseDefaultTypeInternal;
extern HealthCheckResponseDefaultTypeInternal _HealthCheckResponse_default_instance_;
class HistoricalTickDataRequest;
struct HistoricalTickDataRequestDefaultTypeInternal;
extern HistoricalTickDataRequestDefaultTypeInternal _HistoricalTickDataRequest_default_instance_;
class KlineData;
struct KlineDataDefaultTypeInternal;
extern KlineDataDefaultTypeInternal _KlineData_default_instance_;
class KlineDataRequest;
struct KlineDataRequestDefaultTypeInternal;
extern KlineDataRequestDefaultTypeInternal _KlineDataRequest_default_instance_;
class KlineDataResponse;
struct KlineDataResponseDefaultTypeInternal;
extern KlineDataResponseDefaultTypeInternal _KlineDataResponse_default_instance_;
class Level2Data;
struct Level2DataDefaultTypeInternal;
extern Level2DataDefaultTypeInternal _Level2Data_default_instance_;
class Level2DataRequest;
struct Level2DataRequestDefaultTypeInternal;
extern Level2DataRequestDefaultTypeInternal _Level2DataRequest_default_instance_;
class Level2DataResponse;
struct Level2DataResponseDefaultTypeInternal;
extern Level2DataResponseDefaultTypeInternal _Level2DataResponse_default_instance_;
class PriceLevel;
struct PriceLevelDefaultTypeInternal;
extern PriceLevelDefaultTypeInternal _PriceLevel_default_instance_;
class ResponseMetadata;
struct ResponseMetadataDefaultTypeInternal;
extern ResponseMetadataDefaultTypeInternal _ResponseMetadata_default_instance_;
class TickData;
struct TickDataDefaultTypeInternal;
extern TickDataDefaultTypeInternal _TickData_default_instance_;
class TickDataRequest;
struct TickDataRequestDefaultTypeInternal;
extern TickDataRequestDefaultTypeInternal _TickDataRequest_default_instance_;
class TickDataResponse;
struct TickDataResponseDefaultTypeInternal;
extern TickDataResponseDefaultTypeInternal _TickDataResponse_default_instance_;
}  // namespace financial_data
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google

namespace financial_data {
enum HealthCheckResponse_ServingStatus : int {
  HealthCheckResponse_ServingStatus_UNKNOWN = 0,
  HealthCheckResponse_ServingStatus_SERVING = 1,
  HealthCheckResponse_ServingStatus_NOT_SERVING = 2,
  HealthCheckResponse_ServingStatus_SERVICE_UNKNOWN = 3,
  HealthCheckResponse_ServingStatus_HealthCheckResponse_ServingStatus_INT_MIN_SENTINEL_DO_NOT_USE_ =
      std::numeric_limits<::int32_t>::min(),
  HealthCheckResponse_ServingStatus_HealthCheckResponse_ServingStatus_INT_MAX_SENTINEL_DO_NOT_USE_ =
      std::numeric_limits<::int32_t>::max(),
};

bool HealthCheckResponse_ServingStatus_IsValid(int value);
extern const uint32_t HealthCheckResponse_ServingStatus_internal_data_[];
constexpr HealthCheckResponse_ServingStatus HealthCheckResponse_ServingStatus_ServingStatus_MIN = static_cast<HealthCheckResponse_ServingStatus>(0);
constexpr HealthCheckResponse_ServingStatus HealthCheckResponse_ServingStatus_ServingStatus_MAX = static_cast<HealthCheckResponse_ServingStatus>(3);
constexpr int HealthCheckResponse_ServingStatus_ServingStatus_ARRAYSIZE = 3 + 1;
const ::google::protobuf::EnumDescriptor*
HealthCheckResponse_ServingStatus_descriptor();
template <typename T>
const std::string& HealthCheckResponse_ServingStatus_Name(T value) {
  static_assert(std::is_same<T, HealthCheckResponse_ServingStatus>::value ||
                    std::is_integral<T>::value,
                "Incorrect type passed to ServingStatus_Name().");
  return HealthCheckResponse_ServingStatus_Name(static_cast<HealthCheckResponse_ServingStatus>(value));
}
template <>
inline const std::string& HealthCheckResponse_ServingStatus_Name(HealthCheckResponse_ServingStatus value) {
  return ::google::protobuf::internal::NameOfDenseEnum<HealthCheckResponse_ServingStatus_descriptor,
                                                 0, 3>(
      static_cast<int>(value));
}
inline bool HealthCheckResponse_ServingStatus_Parse(absl::string_view name, HealthCheckResponse_ServingStatus* value) {
  return ::google::protobuf::internal::ParseNamedEnum<HealthCheckResponse_ServingStatus>(
      HealthCheckResponse_ServingStatus_descriptor(), name, value);
}

// ===================================================================


// -------------------------------------------------------------------

class TickDataRequest final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.TickDataRequest) */ {
 public:
  inline TickDataRequest() : TickDataRequest(nullptr) {}
  ~TickDataRequest() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR TickDataRequest(::google::protobuf::internal::ConstantInitialized);

  inline TickDataRequest(const TickDataRequest& from)
      : TickDataRequest(nullptr, from) {}
  TickDataRequest(TickDataRequest&& from) noexcept
    : TickDataRequest() {
    *this = ::std::move(from);
  }

  inline TickDataRequest& operator=(const TickDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TickDataRequest& operator=(TickDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TickDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const TickDataRequest* internal_default_instance() {
    return reinterpret_cast<const TickDataRequest*>(
               &_TickDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TickDataRequest& a, TickDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TickDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TickDataRequest* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TickDataRequest* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TickDataRequest>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const TickDataRequest& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const TickDataRequest& from) {
    TickDataRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(TickDataRequest* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.TickDataRequest";
  }
  protected:
  explicit TickDataRequest(::google::protobuf::Arena* arena);
  TickDataRequest(::google::protobuf::Arena* arena, const TickDataRequest& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSymbolsFieldNumber = 1,
    kExchangeFieldNumber = 2,
    kIncludeLevel2FieldNumber = 3,
    kBufferSizeFieldNumber = 4,
  };
  // repeated string symbols = 1;
  int symbols_size() const;
  private:
  int _internal_symbols_size() const;

  public:
  void clear_symbols() ;
  const std::string& symbols(int index) const;
  std::string* mutable_symbols(int index);
  void set_symbols(int index, const std::string& value);
  void set_symbols(int index, std::string&& value);
  void set_symbols(int index, const char* value);
  void set_symbols(int index, const char* value, std::size_t size);
  void set_symbols(int index, absl::string_view value);
  std::string* add_symbols();
  void add_symbols(const std::string& value);
  void add_symbols(std::string&& value);
  void add_symbols(const char* value);
  void add_symbols(const char* value, std::size_t size);
  void add_symbols(absl::string_view value);
  const ::google::protobuf::RepeatedPtrField<std::string>& symbols() const;
  ::google::protobuf::RepeatedPtrField<std::string>* mutable_symbols();

  private:
  const ::google::protobuf::RepeatedPtrField<std::string>& _internal_symbols() const;
  ::google::protobuf::RepeatedPtrField<std::string>* _internal_mutable_symbols();

  public:
  // string exchange = 2;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // bool include_level2 = 3;
  void clear_include_level2() ;
  bool include_level2() const;
  void set_include_level2(bool value);

  private:
  bool _internal_include_level2() const;
  void _internal_set_include_level2(bool value);

  public:
  // int32 buffer_size = 4;
  void clear_buffer_size() ;
  ::int32_t buffer_size() const;
  void set_buffer_size(::int32_t value);

  private:
  ::int32_t _internal_buffer_size() const;
  void _internal_set_buffer_size(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.TickDataRequest)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 0,
      54, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::RepeatedPtrField<std::string> symbols_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    bool include_level2_;
    ::int32_t buffer_size_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class ResponseMetadata final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.ResponseMetadata) */ {
 public:
  inline ResponseMetadata() : ResponseMetadata(nullptr) {}
  ~ResponseMetadata() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR ResponseMetadata(::google::protobuf::internal::ConstantInitialized);

  inline ResponseMetadata(const ResponseMetadata& from)
      : ResponseMetadata(nullptr, from) {}
  ResponseMetadata(ResponseMetadata&& from) noexcept
    : ResponseMetadata() {
    *this = ::std::move(from);
  }

  inline ResponseMetadata& operator=(const ResponseMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResponseMetadata& operator=(ResponseMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResponseMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResponseMetadata* internal_default_instance() {
    return reinterpret_cast<const ResponseMetadata*>(
               &_ResponseMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(ResponseMetadata& a, ResponseMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(ResponseMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResponseMetadata* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResponseMetadata* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResponseMetadata>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ResponseMetadata& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const ResponseMetadata& from) {
    ResponseMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(ResponseMetadata* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.ResponseMetadata";
  }
  protected:
  explicit ResponseMetadata(::google::protobuf::Arena* arena);
  ResponseMetadata(::google::protobuf::Arena* arena, const ResponseMetadata& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kServerIdFieldNumber = 2,
    kServerTimestampFieldNumber = 1,
    kProcessingLatencyUsFieldNumber = 4,
    kSequenceNumberFieldNumber = 3,
  };
  // string server_id = 2;
  void clear_server_id() ;
  const std::string& server_id() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_server_id(Arg_&& arg, Args_... args);
  std::string* mutable_server_id();
  PROTOBUF_NODISCARD std::string* release_server_id();
  void set_allocated_server_id(std::string* value);

  private:
  const std::string& _internal_server_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_server_id(
      const std::string& value);
  std::string* _internal_mutable_server_id();

  public:
  // int64 server_timestamp = 1;
  void clear_server_timestamp() ;
  ::int64_t server_timestamp() const;
  void set_server_timestamp(::int64_t value);

  private:
  ::int64_t _internal_server_timestamp() const;
  void _internal_set_server_timestamp(::int64_t value);

  public:
  // double processing_latency_us = 4;
  void clear_processing_latency_us() ;
  double processing_latency_us() const;
  void set_processing_latency_us(double value);

  private:
  double _internal_processing_latency_us() const;
  void _internal_set_processing_latency_us(double value);

  public:
  // int32 sequence_number = 3;
  void clear_sequence_number() ;
  ::int32_t sequence_number() const;
  void set_sequence_number(::int32_t value);

  private:
  ::int32_t _internal_sequence_number() const;
  void _internal_set_sequence_number(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.ResponseMetadata)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 0,
      49, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::ArenaStringPtr server_id_;
    ::int64_t server_timestamp_;
    double processing_latency_us_;
    ::int32_t sequence_number_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class PriceLevel final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.PriceLevel) */ {
 public:
  inline PriceLevel() : PriceLevel(nullptr) {}
  ~PriceLevel() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR PriceLevel(::google::protobuf::internal::ConstantInitialized);

  inline PriceLevel(const PriceLevel& from)
      : PriceLevel(nullptr, from) {}
  PriceLevel(PriceLevel&& from) noexcept
    : PriceLevel() {
    *this = ::std::move(from);
  }

  inline PriceLevel& operator=(const PriceLevel& from) {
    CopyFrom(from);
    return *this;
  }
  inline PriceLevel& operator=(PriceLevel&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PriceLevel& default_instance() {
    return *internal_default_instance();
  }
  static inline const PriceLevel* internal_default_instance() {
    return reinterpret_cast<const PriceLevel*>(
               &_PriceLevel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(PriceLevel& a, PriceLevel& b) {
    a.Swap(&b);
  }
  inline void Swap(PriceLevel* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PriceLevel* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PriceLevel* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PriceLevel>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const PriceLevel& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const PriceLevel& from) {
    PriceLevel::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(PriceLevel* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.PriceLevel";
  }
  protected:
  explicit PriceLevel(::google::protobuf::Arena* arena);
  PriceLevel(::google::protobuf::Arena* arena, const PriceLevel& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPriceFieldNumber = 1,
    kVolumeFieldNumber = 2,
    kOrderCountFieldNumber = 3,
  };
  // double price = 1;
  void clear_price() ;
  double price() const;
  void set_price(double value);

  private:
  double _internal_price() const;
  void _internal_set_price(double value);

  public:
  // int32 volume = 2;
  void clear_volume() ;
  ::int32_t volume() const;
  void set_volume(::int32_t value);

  private:
  ::int32_t _internal_volume() const;
  void _internal_set_volume(::int32_t value);

  public:
  // int32 order_count = 3;
  void clear_order_count() ;
  ::int32_t order_count() const;
  void set_order_count(::int32_t value);

  private:
  ::int32_t _internal_order_count() const;
  void _internal_set_order_count(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.PriceLevel)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 0,
      0, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    double price_;
    ::int32_t volume_;
    ::int32_t order_count_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class Level2DataRequest final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.Level2DataRequest) */ {
 public:
  inline Level2DataRequest() : Level2DataRequest(nullptr) {}
  ~Level2DataRequest() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR Level2DataRequest(::google::protobuf::internal::ConstantInitialized);

  inline Level2DataRequest(const Level2DataRequest& from)
      : Level2DataRequest(nullptr, from) {}
  Level2DataRequest(Level2DataRequest&& from) noexcept
    : Level2DataRequest() {
    *this = ::std::move(from);
  }

  inline Level2DataRequest& operator=(const Level2DataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline Level2DataRequest& operator=(Level2DataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Level2DataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const Level2DataRequest* internal_default_instance() {
    return reinterpret_cast<const Level2DataRequest*>(
               &_Level2DataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Level2DataRequest& a, Level2DataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(Level2DataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Level2DataRequest* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Level2DataRequest* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Level2DataRequest>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const Level2DataRequest& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const Level2DataRequest& from) {
    Level2DataRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(Level2DataRequest* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.Level2DataRequest";
  }
  protected:
  explicit Level2DataRequest(::google::protobuf::Arena* arena);
  Level2DataRequest(::google::protobuf::Arena* arena, const Level2DataRequest& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSymbolsFieldNumber = 1,
    kExchangeFieldNumber = 2,
    kDepthFieldNumber = 3,
    kBufferSizeFieldNumber = 4,
  };
  // repeated string symbols = 1;
  int symbols_size() const;
  private:
  int _internal_symbols_size() const;

  public:
  void clear_symbols() ;
  const std::string& symbols(int index) const;
  std::string* mutable_symbols(int index);
  void set_symbols(int index, const std::string& value);
  void set_symbols(int index, std::string&& value);
  void set_symbols(int index, const char* value);
  void set_symbols(int index, const char* value, std::size_t size);
  void set_symbols(int index, absl::string_view value);
  std::string* add_symbols();
  void add_symbols(const std::string& value);
  void add_symbols(std::string&& value);
  void add_symbols(const char* value);
  void add_symbols(const char* value, std::size_t size);
  void add_symbols(absl::string_view value);
  const ::google::protobuf::RepeatedPtrField<std::string>& symbols() const;
  ::google::protobuf::RepeatedPtrField<std::string>* mutable_symbols();

  private:
  const ::google::protobuf::RepeatedPtrField<std::string>& _internal_symbols() const;
  ::google::protobuf::RepeatedPtrField<std::string>* _internal_mutable_symbols();

  public:
  // string exchange = 2;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // int32 depth = 3;
  void clear_depth() ;
  ::int32_t depth() const;
  void set_depth(::int32_t value);

  private:
  ::int32_t _internal_depth() const;
  void _internal_set_depth(::int32_t value);

  public:
  // int32 buffer_size = 4;
  void clear_buffer_size() ;
  ::int32_t buffer_size() const;
  void set_buffer_size(::int32_t value);

  private:
  ::int32_t _internal_buffer_size() const;
  void _internal_set_buffer_size(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.Level2DataRequest)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 0,
      56, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::RepeatedPtrField<std::string> symbols_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    ::int32_t depth_;
    ::int32_t buffer_size_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class KlineDataRequest final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.KlineDataRequest) */ {
 public:
  inline KlineDataRequest() : KlineDataRequest(nullptr) {}
  ~KlineDataRequest() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR KlineDataRequest(::google::protobuf::internal::ConstantInitialized);

  inline KlineDataRequest(const KlineDataRequest& from)
      : KlineDataRequest(nullptr, from) {}
  KlineDataRequest(KlineDataRequest&& from) noexcept
    : KlineDataRequest() {
    *this = ::std::move(from);
  }

  inline KlineDataRequest& operator=(const KlineDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline KlineDataRequest& operator=(KlineDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KlineDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const KlineDataRequest* internal_default_instance() {
    return reinterpret_cast<const KlineDataRequest*>(
               &_KlineDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(KlineDataRequest& a, KlineDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(KlineDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KlineDataRequest* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KlineDataRequest* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KlineDataRequest>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const KlineDataRequest& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const KlineDataRequest& from) {
    KlineDataRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(KlineDataRequest* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.KlineDataRequest";
  }
  protected:
  explicit KlineDataRequest(::google::protobuf::Arena* arena);
  KlineDataRequest(::google::protobuf::Arena* arena, const KlineDataRequest& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSymbolsFieldNumber = 1,
    kExchangeFieldNumber = 2,
    kPeriodFieldNumber = 3,
    kStartTimestampFieldNumber = 4,
    kEndTimestampFieldNumber = 5,
    kBufferSizeFieldNumber = 6,
  };
  // repeated string symbols = 1;
  int symbols_size() const;
  private:
  int _internal_symbols_size() const;

  public:
  void clear_symbols() ;
  const std::string& symbols(int index) const;
  std::string* mutable_symbols(int index);
  void set_symbols(int index, const std::string& value);
  void set_symbols(int index, std::string&& value);
  void set_symbols(int index, const char* value);
  void set_symbols(int index, const char* value, std::size_t size);
  void set_symbols(int index, absl::string_view value);
  std::string* add_symbols();
  void add_symbols(const std::string& value);
  void add_symbols(std::string&& value);
  void add_symbols(const char* value);
  void add_symbols(const char* value, std::size_t size);
  void add_symbols(absl::string_view value);
  const ::google::protobuf::RepeatedPtrField<std::string>& symbols() const;
  ::google::protobuf::RepeatedPtrField<std::string>* mutable_symbols();

  private:
  const ::google::protobuf::RepeatedPtrField<std::string>& _internal_symbols() const;
  ::google::protobuf::RepeatedPtrField<std::string>* _internal_mutable_symbols();

  public:
  // string exchange = 2;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // string period = 3;
  void clear_period() ;
  const std::string& period() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_period(Arg_&& arg, Args_... args);
  std::string* mutable_period();
  PROTOBUF_NODISCARD std::string* release_period();
  void set_allocated_period(std::string* value);

  private:
  const std::string& _internal_period() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_period(
      const std::string& value);
  std::string* _internal_mutable_period();

  public:
  // int64 start_timestamp = 4;
  void clear_start_timestamp() ;
  ::int64_t start_timestamp() const;
  void set_start_timestamp(::int64_t value);

  private:
  ::int64_t _internal_start_timestamp() const;
  void _internal_set_start_timestamp(::int64_t value);

  public:
  // int64 end_timestamp = 5;
  void clear_end_timestamp() ;
  ::int64_t end_timestamp() const;
  void set_end_timestamp(::int64_t value);

  private:
  ::int64_t _internal_end_timestamp() const;
  void _internal_set_end_timestamp(::int64_t value);

  public:
  // int32 buffer_size = 6;
  void clear_buffer_size() ;
  ::int32_t buffer_size() const;
  void set_buffer_size(::int32_t value);

  private:
  ::int32_t _internal_buffer_size() const;
  void _internal_set_buffer_size(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.KlineDataRequest)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 6, 0,
      61, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::RepeatedPtrField<std::string> symbols_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    ::google::protobuf::internal::ArenaStringPtr period_;
    ::int64_t start_timestamp_;
    ::int64_t end_timestamp_;
    ::int32_t buffer_size_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class KlineData final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.KlineData) */ {
 public:
  inline KlineData() : KlineData(nullptr) {}
  ~KlineData() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR KlineData(::google::protobuf::internal::ConstantInitialized);

  inline KlineData(const KlineData& from)
      : KlineData(nullptr, from) {}
  KlineData(KlineData&& from) noexcept
    : KlineData() {
    *this = ::std::move(from);
  }

  inline KlineData& operator=(const KlineData& from) {
    CopyFrom(from);
    return *this;
  }
  inline KlineData& operator=(KlineData&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KlineData& default_instance() {
    return *internal_default_instance();
  }
  static inline const KlineData* internal_default_instance() {
    return reinterpret_cast<const KlineData*>(
               &_KlineData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(KlineData& a, KlineData& b) {
    a.Swap(&b);
  }
  inline void Swap(KlineData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KlineData* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KlineData* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KlineData>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const KlineData& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const KlineData& from) {
    KlineData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(KlineData* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.KlineData";
  }
  protected:
  explicit KlineData(::google::protobuf::Arena* arena);
  KlineData(::google::protobuf::Arena* arena, const KlineData& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSymbolFieldNumber = 1,
    kExchangeFieldNumber = 2,
    kPeriodFieldNumber = 3,
    kTimestampFieldNumber = 4,
    kOpenFieldNumber = 5,
    kHighFieldNumber = 6,
    kLowFieldNumber = 7,
    kCloseFieldNumber = 8,
    kVolumeFieldNumber = 9,
    kTurnoverFieldNumber = 10,
    kOpenInterestFieldNumber = 11,
  };
  // string symbol = 1;
  void clear_symbol() ;
  const std::string& symbol() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_symbol(Arg_&& arg, Args_... args);
  std::string* mutable_symbol();
  PROTOBUF_NODISCARD std::string* release_symbol();
  void set_allocated_symbol(std::string* value);

  private:
  const std::string& _internal_symbol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_symbol(
      const std::string& value);
  std::string* _internal_mutable_symbol();

  public:
  // string exchange = 2;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // string period = 3;
  void clear_period() ;
  const std::string& period() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_period(Arg_&& arg, Args_... args);
  std::string* mutable_period();
  PROTOBUF_NODISCARD std::string* release_period();
  void set_allocated_period(std::string* value);

  private:
  const std::string& _internal_period() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_period(
      const std::string& value);
  std::string* _internal_mutable_period();

  public:
  // int64 timestamp = 4;
  void clear_timestamp() ;
  ::int64_t timestamp() const;
  void set_timestamp(::int64_t value);

  private:
  ::int64_t _internal_timestamp() const;
  void _internal_set_timestamp(::int64_t value);

  public:
  // double open = 5;
  void clear_open() ;
  double open() const;
  void set_open(double value);

  private:
  double _internal_open() const;
  void _internal_set_open(double value);

  public:
  // double high = 6;
  void clear_high() ;
  double high() const;
  void set_high(double value);

  private:
  double _internal_high() const;
  void _internal_set_high(double value);

  public:
  // double low = 7;
  void clear_low() ;
  double low() const;
  void set_low(double value);

  private:
  double _internal_low() const;
  void _internal_set_low(double value);

  public:
  // double close = 8;
  void clear_close() ;
  double close() const;
  void set_close(double value);

  private:
  double _internal_close() const;
  void _internal_set_close(double value);

  public:
  // int64 volume = 9;
  void clear_volume() ;
  ::int64_t volume() const;
  void set_volume(::int64_t value);

  private:
  ::int64_t _internal_volume() const;
  void _internal_set_volume(::int64_t value);

  public:
  // double turnover = 10;
  void clear_turnover() ;
  double turnover() const;
  void set_turnover(double value);

  private:
  double _internal_turnover() const;
  void _internal_set_turnover(double value);

  public:
  // int64 open_interest = 11;
  void clear_open_interest() ;
  ::int64_t open_interest() const;
  void set_open_interest(::int64_t value);

  private:
  ::int64_t _internal_open_interest() const;
  void _internal_set_open_interest(::int64_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.KlineData)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      4, 11, 0,
      61, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::ArenaStringPtr symbol_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    ::google::protobuf::internal::ArenaStringPtr period_;
    ::int64_t timestamp_;
    double open_;
    double high_;
    double low_;
    double close_;
    ::int64_t volume_;
    double turnover_;
    ::int64_t open_interest_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class HistoricalTickDataRequest final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.HistoricalTickDataRequest) */ {
 public:
  inline HistoricalTickDataRequest() : HistoricalTickDataRequest(nullptr) {}
  ~HistoricalTickDataRequest() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR HistoricalTickDataRequest(::google::protobuf::internal::ConstantInitialized);

  inline HistoricalTickDataRequest(const HistoricalTickDataRequest& from)
      : HistoricalTickDataRequest(nullptr, from) {}
  HistoricalTickDataRequest(HistoricalTickDataRequest&& from) noexcept
    : HistoricalTickDataRequest() {
    *this = ::std::move(from);
  }

  inline HistoricalTickDataRequest& operator=(const HistoricalTickDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline HistoricalTickDataRequest& operator=(HistoricalTickDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HistoricalTickDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const HistoricalTickDataRequest* internal_default_instance() {
    return reinterpret_cast<const HistoricalTickDataRequest*>(
               &_HistoricalTickDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(HistoricalTickDataRequest& a, HistoricalTickDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(HistoricalTickDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HistoricalTickDataRequest* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HistoricalTickDataRequest* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HistoricalTickDataRequest>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const HistoricalTickDataRequest& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const HistoricalTickDataRequest& from) {
    HistoricalTickDataRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(HistoricalTickDataRequest* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.HistoricalTickDataRequest";
  }
  protected:
  explicit HistoricalTickDataRequest(::google::protobuf::Arena* arena);
  HistoricalTickDataRequest(::google::protobuf::Arena* arena, const HistoricalTickDataRequest& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSymbolFieldNumber = 1,
    kExchangeFieldNumber = 2,
    kCursorFieldNumber = 6,
    kStartTimestampFieldNumber = 3,
    kEndTimestampFieldNumber = 4,
    kLimitFieldNumber = 5,
    kBufferSizeFieldNumber = 7,
  };
  // string symbol = 1;
  void clear_symbol() ;
  const std::string& symbol() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_symbol(Arg_&& arg, Args_... args);
  std::string* mutable_symbol();
  PROTOBUF_NODISCARD std::string* release_symbol();
  void set_allocated_symbol(std::string* value);

  private:
  const std::string& _internal_symbol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_symbol(
      const std::string& value);
  std::string* _internal_mutable_symbol();

  public:
  // string exchange = 2;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // string cursor = 6;
  void clear_cursor() ;
  const std::string& cursor() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_cursor(Arg_&& arg, Args_... args);
  std::string* mutable_cursor();
  PROTOBUF_NODISCARD std::string* release_cursor();
  void set_allocated_cursor(std::string* value);

  private:
  const std::string& _internal_cursor() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cursor(
      const std::string& value);
  std::string* _internal_mutable_cursor();

  public:
  // int64 start_timestamp = 3;
  void clear_start_timestamp() ;
  ::int64_t start_timestamp() const;
  void set_start_timestamp(::int64_t value);

  private:
  ::int64_t _internal_start_timestamp() const;
  void _internal_set_start_timestamp(::int64_t value);

  public:
  // int64 end_timestamp = 4;
  void clear_end_timestamp() ;
  ::int64_t end_timestamp() const;
  void set_end_timestamp(::int64_t value);

  private:
  ::int64_t _internal_end_timestamp() const;
  void _internal_set_end_timestamp(::int64_t value);

  public:
  // int32 limit = 5;
  void clear_limit() ;
  ::int32_t limit() const;
  void set_limit(::int32_t value);

  private:
  ::int32_t _internal_limit() const;
  void _internal_set_limit(::int32_t value);

  public:
  // int32 buffer_size = 7;
  void clear_buffer_size() ;
  ::int32_t buffer_size() const;
  void set_buffer_size(::int32_t value);

  private:
  ::int32_t _internal_buffer_size() const;
  void _internal_set_buffer_size(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.HistoricalTickDataRequest)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 7, 0,
      69, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::ArenaStringPtr symbol_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    ::google::protobuf::internal::ArenaStringPtr cursor_;
    ::int64_t start_timestamp_;
    ::int64_t end_timestamp_;
    ::int32_t limit_;
    ::int32_t buffer_size_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class HealthCheckResponse final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.HealthCheckResponse) */ {
 public:
  inline HealthCheckResponse() : HealthCheckResponse(nullptr) {}
  ~HealthCheckResponse() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR HealthCheckResponse(::google::protobuf::internal::ConstantInitialized);

  inline HealthCheckResponse(const HealthCheckResponse& from)
      : HealthCheckResponse(nullptr, from) {}
  HealthCheckResponse(HealthCheckResponse&& from) noexcept
    : HealthCheckResponse() {
    *this = ::std::move(from);
  }

  inline HealthCheckResponse& operator=(const HealthCheckResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline HealthCheckResponse& operator=(HealthCheckResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HealthCheckResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const HealthCheckResponse* internal_default_instance() {
    return reinterpret_cast<const HealthCheckResponse*>(
               &_HealthCheckResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(HealthCheckResponse& a, HealthCheckResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(HealthCheckResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HealthCheckResponse* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HealthCheckResponse* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HealthCheckResponse>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const HealthCheckResponse& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const HealthCheckResponse& from) {
    HealthCheckResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(HealthCheckResponse* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.HealthCheckResponse";
  }
  protected:
  explicit HealthCheckResponse(::google::protobuf::Arena* arena);
  HealthCheckResponse(::google::protobuf::Arena* arena, const HealthCheckResponse& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  using ServingStatus = HealthCheckResponse_ServingStatus;
  static constexpr ServingStatus UNKNOWN = HealthCheckResponse_ServingStatus_UNKNOWN;
  static constexpr ServingStatus SERVING = HealthCheckResponse_ServingStatus_SERVING;
  static constexpr ServingStatus NOT_SERVING = HealthCheckResponse_ServingStatus_NOT_SERVING;
  static constexpr ServingStatus SERVICE_UNKNOWN = HealthCheckResponse_ServingStatus_SERVICE_UNKNOWN;
  static inline bool ServingStatus_IsValid(int value) {
    return HealthCheckResponse_ServingStatus_IsValid(value);
  }
  static constexpr ServingStatus ServingStatus_MIN = HealthCheckResponse_ServingStatus_ServingStatus_MIN;
  static constexpr ServingStatus ServingStatus_MAX = HealthCheckResponse_ServingStatus_ServingStatus_MAX;
  static constexpr int ServingStatus_ARRAYSIZE = HealthCheckResponse_ServingStatus_ServingStatus_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor* ServingStatus_descriptor() {
    return HealthCheckResponse_ServingStatus_descriptor();
  }
  template <typename T>
  static inline const std::string& ServingStatus_Name(T value) {
    return HealthCheckResponse_ServingStatus_Name(value);
  }
  static inline bool ServingStatus_Parse(absl::string_view name, ServingStatus* value) {
    return HealthCheckResponse_ServingStatus_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kStatusFieldNumber = 1,
  };
  // string message = 2;
  void clear_message() ;
  const std::string& message() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_message(Arg_&& arg, Args_... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* value);

  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(
      const std::string& value);
  std::string* _internal_mutable_message();

  public:
  // .financial_data.HealthCheckResponse.ServingStatus status = 1;
  void clear_status() ;
  ::financial_data::HealthCheckResponse_ServingStatus status() const;
  void set_status(::financial_data::HealthCheckResponse_ServingStatus value);

  private:
  ::financial_data::HealthCheckResponse_ServingStatus _internal_status() const;
  void _internal_set_status(::financial_data::HealthCheckResponse_ServingStatus value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.HealthCheckResponse)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 0,
      50, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::ArenaStringPtr message_;
    int status_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class HealthCheckRequest final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.HealthCheckRequest) */ {
 public:
  inline HealthCheckRequest() : HealthCheckRequest(nullptr) {}
  ~HealthCheckRequest() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR HealthCheckRequest(::google::protobuf::internal::ConstantInitialized);

  inline HealthCheckRequest(const HealthCheckRequest& from)
      : HealthCheckRequest(nullptr, from) {}
  HealthCheckRequest(HealthCheckRequest&& from) noexcept
    : HealthCheckRequest() {
    *this = ::std::move(from);
  }

  inline HealthCheckRequest& operator=(const HealthCheckRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline HealthCheckRequest& operator=(HealthCheckRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HealthCheckRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const HealthCheckRequest* internal_default_instance() {
    return reinterpret_cast<const HealthCheckRequest*>(
               &_HealthCheckRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(HealthCheckRequest& a, HealthCheckRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(HealthCheckRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HealthCheckRequest* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HealthCheckRequest* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HealthCheckRequest>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const HealthCheckRequest& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const HealthCheckRequest& from) {
    HealthCheckRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(HealthCheckRequest* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.HealthCheckRequest";
  }
  protected:
  explicit HealthCheckRequest(::google::protobuf::Arena* arena);
  HealthCheckRequest(::google::protobuf::Arena* arena, const HealthCheckRequest& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kServiceFieldNumber = 1,
  };
  // string service = 1;
  void clear_service() ;
  const std::string& service() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_service(Arg_&& arg, Args_... args);
  std::string* mutable_service();
  PROTOBUF_NODISCARD std::string* release_service();
  void set_allocated_service(std::string* value);

  private:
  const std::string& _internal_service() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_service(
      const std::string& value);
  std::string* _internal_mutable_service();

  public:
  // @@protoc_insertion_point(class_scope:financial_data.HealthCheckRequest)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 0,
      49, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::ArenaStringPtr service_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class TickData final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.TickData) */ {
 public:
  inline TickData() : TickData(nullptr) {}
  ~TickData() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR TickData(::google::protobuf::internal::ConstantInitialized);

  inline TickData(const TickData& from)
      : TickData(nullptr, from) {}
  TickData(TickData&& from) noexcept
    : TickData() {
    *this = ::std::move(from);
  }

  inline TickData& operator=(const TickData& from) {
    CopyFrom(from);
    return *this;
  }
  inline TickData& operator=(TickData&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TickData& default_instance() {
    return *internal_default_instance();
  }
  static inline const TickData* internal_default_instance() {
    return reinterpret_cast<const TickData*>(
               &_TickData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(TickData& a, TickData& b) {
    a.Swap(&b);
  }
  inline void Swap(TickData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TickData* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TickData* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TickData>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const TickData& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const TickData& from) {
    TickData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(TickData* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.TickData";
  }
  protected:
  explicit TickData(::google::protobuf::Arena* arena);
  TickData(::google::protobuf::Arena* arena, const TickData& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBidsFieldNumber = 8,
    kAsksFieldNumber = 9,
    kSymbolFieldNumber = 2,
    kExchangeFieldNumber = 3,
    kTradeFlagFieldNumber = 11,
    kTimestampFieldNumber = 1,
    kLastPriceFieldNumber = 4,
    kVolumeFieldNumber = 5,
    kTurnoverFieldNumber = 6,
    kOpenInterestFieldNumber = 7,
    kSequenceFieldNumber = 10,
  };
  // repeated .financial_data.PriceLevel bids = 8;
  int bids_size() const;
  private:
  int _internal_bids_size() const;

  public:
  void clear_bids() ;
  ::financial_data::PriceLevel* mutable_bids(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel >*
      mutable_bids();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>& _internal_bids() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>* _internal_mutable_bids();
  public:
  const ::financial_data::PriceLevel& bids(int index) const;
  ::financial_data::PriceLevel* add_bids();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel >&
      bids() const;
  // repeated .financial_data.PriceLevel asks = 9;
  int asks_size() const;
  private:
  int _internal_asks_size() const;

  public:
  void clear_asks() ;
  ::financial_data::PriceLevel* mutable_asks(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel >*
      mutable_asks();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>& _internal_asks() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>* _internal_mutable_asks();
  public:
  const ::financial_data::PriceLevel& asks(int index) const;
  ::financial_data::PriceLevel* add_asks();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel >&
      asks() const;
  // string symbol = 2;
  void clear_symbol() ;
  const std::string& symbol() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_symbol(Arg_&& arg, Args_... args);
  std::string* mutable_symbol();
  PROTOBUF_NODISCARD std::string* release_symbol();
  void set_allocated_symbol(std::string* value);

  private:
  const std::string& _internal_symbol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_symbol(
      const std::string& value);
  std::string* _internal_mutable_symbol();

  public:
  // string exchange = 3;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // string trade_flag = 11;
  void clear_trade_flag() ;
  const std::string& trade_flag() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_trade_flag(Arg_&& arg, Args_... args);
  std::string* mutable_trade_flag();
  PROTOBUF_NODISCARD std::string* release_trade_flag();
  void set_allocated_trade_flag(std::string* value);

  private:
  const std::string& _internal_trade_flag() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_trade_flag(
      const std::string& value);
  std::string* _internal_mutable_trade_flag();

  public:
  // int64 timestamp = 1;
  void clear_timestamp() ;
  ::int64_t timestamp() const;
  void set_timestamp(::int64_t value);

  private:
  ::int64_t _internal_timestamp() const;
  void _internal_set_timestamp(::int64_t value);

  public:
  // double last_price = 4;
  void clear_last_price() ;
  double last_price() const;
  void set_last_price(double value);

  private:
  double _internal_last_price() const;
  void _internal_set_last_price(double value);

  public:
  // int64 volume = 5;
  void clear_volume() ;
  ::int64_t volume() const;
  void set_volume(::int64_t value);

  private:
  ::int64_t _internal_volume() const;
  void _internal_set_volume(::int64_t value);

  public:
  // double turnover = 6;
  void clear_turnover() ;
  double turnover() const;
  void set_turnover(double value);

  private:
  double _internal_turnover() const;
  void _internal_set_turnover(double value);

  public:
  // int64 open_interest = 7;
  void clear_open_interest() ;
  ::int64_t open_interest() const;
  void set_open_interest(::int64_t value);

  private:
  ::int64_t _internal_open_interest() const;
  void _internal_set_open_interest(::int64_t value);

  public:
  // uint32 sequence = 10;
  void clear_sequence() ;
  ::uint32_t sequence() const;
  void set_sequence(::uint32_t value);

  private:
  ::uint32_t _internal_sequence() const;
  void _internal_set_sequence(::uint32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.TickData)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      4, 11, 2,
      64, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel > bids_;
    ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel > asks_;
    ::google::protobuf::internal::ArenaStringPtr symbol_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    ::google::protobuf::internal::ArenaStringPtr trade_flag_;
    ::int64_t timestamp_;
    double last_price_;
    ::int64_t volume_;
    double turnover_;
    ::int64_t open_interest_;
    ::uint32_t sequence_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class Level2Data final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.Level2Data) */ {
 public:
  inline Level2Data() : Level2Data(nullptr) {}
  ~Level2Data() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR Level2Data(::google::protobuf::internal::ConstantInitialized);

  inline Level2Data(const Level2Data& from)
      : Level2Data(nullptr, from) {}
  Level2Data(Level2Data&& from) noexcept
    : Level2Data() {
    *this = ::std::move(from);
  }

  inline Level2Data& operator=(const Level2Data& from) {
    CopyFrom(from);
    return *this;
  }
  inline Level2Data& operator=(Level2Data&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Level2Data& default_instance() {
    return *internal_default_instance();
  }
  static inline const Level2Data* internal_default_instance() {
    return reinterpret_cast<const Level2Data*>(
               &_Level2Data_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(Level2Data& a, Level2Data& b) {
    a.Swap(&b);
  }
  inline void Swap(Level2Data* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Level2Data* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Level2Data* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Level2Data>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const Level2Data& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const Level2Data& from) {
    Level2Data::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(Level2Data* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.Level2Data";
  }
  protected:
  explicit Level2Data(::google::protobuf::Arena* arena);
  Level2Data(::google::protobuf::Arena* arena, const Level2Data& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBidsFieldNumber = 4,
    kAsksFieldNumber = 5,
    kSymbolFieldNumber = 2,
    kExchangeFieldNumber = 3,
    kTimestampFieldNumber = 1,
    kSequenceFieldNumber = 6,
  };
  // repeated .financial_data.PriceLevel bids = 4;
  int bids_size() const;
  private:
  int _internal_bids_size() const;

  public:
  void clear_bids() ;
  ::financial_data::PriceLevel* mutable_bids(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel >*
      mutable_bids();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>& _internal_bids() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>* _internal_mutable_bids();
  public:
  const ::financial_data::PriceLevel& bids(int index) const;
  ::financial_data::PriceLevel* add_bids();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel >&
      bids() const;
  // repeated .financial_data.PriceLevel asks = 5;
  int asks_size() const;
  private:
  int _internal_asks_size() const;

  public:
  void clear_asks() ;
  ::financial_data::PriceLevel* mutable_asks(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel >*
      mutable_asks();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>& _internal_asks() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>* _internal_mutable_asks();
  public:
  const ::financial_data::PriceLevel& asks(int index) const;
  ::financial_data::PriceLevel* add_asks();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel >&
      asks() const;
  // string symbol = 2;
  void clear_symbol() ;
  const std::string& symbol() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_symbol(Arg_&& arg, Args_... args);
  std::string* mutable_symbol();
  PROTOBUF_NODISCARD std::string* release_symbol();
  void set_allocated_symbol(std::string* value);

  private:
  const std::string& _internal_symbol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_symbol(
      const std::string& value);
  std::string* _internal_mutable_symbol();

  public:
  // string exchange = 3;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // int64 timestamp = 1;
  void clear_timestamp() ;
  ::int64_t timestamp() const;
  void set_timestamp(::int64_t value);

  private:
  ::int64_t _internal_timestamp() const;
  void _internal_set_timestamp(::int64_t value);

  public:
  // uint32 sequence = 6;
  void clear_sequence() ;
  ::uint32_t sequence() const;
  void set_sequence(::uint32_t value);

  private:
  ::uint32_t _internal_sequence() const;
  void _internal_set_sequence(::uint32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.Level2Data)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 6, 2,
      48, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel > bids_;
    ::google::protobuf::RepeatedPtrField< ::financial_data::PriceLevel > asks_;
    ::google::protobuf::internal::ArenaStringPtr symbol_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    ::int64_t timestamp_;
    ::uint32_t sequence_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class KlineDataResponse final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.KlineDataResponse) */ {
 public:
  inline KlineDataResponse() : KlineDataResponse(nullptr) {}
  ~KlineDataResponse() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR KlineDataResponse(::google::protobuf::internal::ConstantInitialized);

  inline KlineDataResponse(const KlineDataResponse& from)
      : KlineDataResponse(nullptr, from) {}
  KlineDataResponse(KlineDataResponse&& from) noexcept
    : KlineDataResponse() {
    *this = ::std::move(from);
  }

  inline KlineDataResponse& operator=(const KlineDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline KlineDataResponse& operator=(KlineDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KlineDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const KlineDataResponse* internal_default_instance() {
    return reinterpret_cast<const KlineDataResponse*>(
               &_KlineDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(KlineDataResponse& a, KlineDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(KlineDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KlineDataResponse* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KlineDataResponse* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KlineDataResponse>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const KlineDataResponse& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const KlineDataResponse& from) {
    KlineDataResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(KlineDataResponse* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.KlineDataResponse";
  }
  protected:
  explicit KlineDataResponse(::google::protobuf::Arena* arena);
  KlineDataResponse(::google::protobuf::Arena* arena, const KlineDataResponse& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKlinesFieldNumber = 1,
    kMetadataFieldNumber = 3,
    kHasMoreFieldNumber = 2,
  };
  // repeated .financial_data.KlineData klines = 1;
  int klines_size() const;
  private:
  int _internal_klines_size() const;

  public:
  void clear_klines() ;
  ::financial_data::KlineData* mutable_klines(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::KlineData >*
      mutable_klines();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::KlineData>& _internal_klines() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::KlineData>* _internal_mutable_klines();
  public:
  const ::financial_data::KlineData& klines(int index) const;
  ::financial_data::KlineData* add_klines();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::KlineData >&
      klines() const;
  // .financial_data.ResponseMetadata metadata = 3;
  bool has_metadata() const;
  void clear_metadata() ;
  const ::financial_data::ResponseMetadata& metadata() const;
  PROTOBUF_NODISCARD ::financial_data::ResponseMetadata* release_metadata();
  ::financial_data::ResponseMetadata* mutable_metadata();
  void set_allocated_metadata(::financial_data::ResponseMetadata* value);
  void unsafe_arena_set_allocated_metadata(::financial_data::ResponseMetadata* value);
  ::financial_data::ResponseMetadata* unsafe_arena_release_metadata();

  private:
  const ::financial_data::ResponseMetadata& _internal_metadata() const;
  ::financial_data::ResponseMetadata* _internal_mutable_metadata();

  public:
  // bool has_more = 2;
  void clear_has_more() ;
  bool has_more() const;
  void set_has_more(bool value);

  private:
  bool _internal_has_more() const;
  void _internal_set_has_more(bool value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.KlineDataResponse)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 2,
      0, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::financial_data::KlineData > klines_;
    ::financial_data::ResponseMetadata* metadata_;
    bool has_more_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class TickDataResponse final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.TickDataResponse) */ {
 public:
  inline TickDataResponse() : TickDataResponse(nullptr) {}
  ~TickDataResponse() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR TickDataResponse(::google::protobuf::internal::ConstantInitialized);

  inline TickDataResponse(const TickDataResponse& from)
      : TickDataResponse(nullptr, from) {}
  TickDataResponse(TickDataResponse&& from) noexcept
    : TickDataResponse() {
    *this = ::std::move(from);
  }

  inline TickDataResponse& operator=(const TickDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TickDataResponse& operator=(TickDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TickDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const TickDataResponse* internal_default_instance() {
    return reinterpret_cast<const TickDataResponse*>(
               &_TickDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(TickDataResponse& a, TickDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TickDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TickDataResponse* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TickDataResponse* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TickDataResponse>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const TickDataResponse& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const TickDataResponse& from) {
    TickDataResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(TickDataResponse* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.TickDataResponse";
  }
  protected:
  explicit TickDataResponse(::google::protobuf::Arena* arena);
  TickDataResponse(::google::protobuf::Arena* arena, const TickDataResponse& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTicksFieldNumber = 1,
    kNextCursorFieldNumber = 3,
    kMetadataFieldNumber = 4,
    kHasMoreFieldNumber = 2,
  };
  // repeated .financial_data.TickData ticks = 1;
  int ticks_size() const;
  private:
  int _internal_ticks_size() const;

  public:
  void clear_ticks() ;
  ::financial_data::TickData* mutable_ticks(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::TickData >*
      mutable_ticks();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::TickData>& _internal_ticks() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::TickData>* _internal_mutable_ticks();
  public:
  const ::financial_data::TickData& ticks(int index) const;
  ::financial_data::TickData* add_ticks();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::TickData >&
      ticks() const;
  // string next_cursor = 3;
  void clear_next_cursor() ;
  const std::string& next_cursor() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_next_cursor(Arg_&& arg, Args_... args);
  std::string* mutable_next_cursor();
  PROTOBUF_NODISCARD std::string* release_next_cursor();
  void set_allocated_next_cursor(std::string* value);

  private:
  const std::string& _internal_next_cursor() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_next_cursor(
      const std::string& value);
  std::string* _internal_mutable_next_cursor();

  public:
  // .financial_data.ResponseMetadata metadata = 4;
  bool has_metadata() const;
  void clear_metadata() ;
  const ::financial_data::ResponseMetadata& metadata() const;
  PROTOBUF_NODISCARD ::financial_data::ResponseMetadata* release_metadata();
  ::financial_data::ResponseMetadata* mutable_metadata();
  void set_allocated_metadata(::financial_data::ResponseMetadata* value);
  void unsafe_arena_set_allocated_metadata(::financial_data::ResponseMetadata* value);
  ::financial_data::ResponseMetadata* unsafe_arena_release_metadata();

  private:
  const ::financial_data::ResponseMetadata& _internal_metadata() const;
  ::financial_data::ResponseMetadata* _internal_mutable_metadata();

  public:
  // bool has_more = 2;
  void clear_has_more() ;
  bool has_more() const;
  void set_has_more(bool value);

  private:
  bool _internal_has_more() const;
  void _internal_set_has_more(bool value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.TickDataResponse)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 2,
      51, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::financial_data::TickData > ticks_;
    ::google::protobuf::internal::ArenaStringPtr next_cursor_;
    ::financial_data::ResponseMetadata* metadata_;
    bool has_more_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};// -------------------------------------------------------------------

class Level2DataResponse final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.Level2DataResponse) */ {
 public:
  inline Level2DataResponse() : Level2DataResponse(nullptr) {}
  ~Level2DataResponse() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR Level2DataResponse(::google::protobuf::internal::ConstantInitialized);

  inline Level2DataResponse(const Level2DataResponse& from)
      : Level2DataResponse(nullptr, from) {}
  Level2DataResponse(Level2DataResponse&& from) noexcept
    : Level2DataResponse() {
    *this = ::std::move(from);
  }

  inline Level2DataResponse& operator=(const Level2DataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline Level2DataResponse& operator=(Level2DataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Level2DataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const Level2DataResponse* internal_default_instance() {
    return reinterpret_cast<const Level2DataResponse*>(
               &_Level2DataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(Level2DataResponse& a, Level2DataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(Level2DataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Level2DataResponse* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Level2DataResponse* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Level2DataResponse>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const Level2DataResponse& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const Level2DataResponse& from) {
    Level2DataResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(Level2DataResponse* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.Level2DataResponse";
  }
  protected:
  explicit Level2DataResponse(::google::protobuf::Arena* arena);
  Level2DataResponse(::google::protobuf::Arena* arena, const Level2DataResponse& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLevel2DataFieldNumber = 1,
    kMetadataFieldNumber = 2,
  };
  // repeated .financial_data.Level2Data level2_data = 1;
  int level2_data_size() const;
  private:
  int _internal_level2_data_size() const;

  public:
  void clear_level2_data() ;
  ::financial_data::Level2Data* mutable_level2_data(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::Level2Data >*
      mutable_level2_data();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::Level2Data>& _internal_level2_data() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::Level2Data>* _internal_mutable_level2_data();
  public:
  const ::financial_data::Level2Data& level2_data(int index) const;
  ::financial_data::Level2Data* add_level2_data();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::Level2Data >&
      level2_data() const;
  // .financial_data.ResponseMetadata metadata = 2;
  bool has_metadata() const;
  void clear_metadata() ;
  const ::financial_data::ResponseMetadata& metadata() const;
  PROTOBUF_NODISCARD ::financial_data::ResponseMetadata* release_metadata();
  ::financial_data::ResponseMetadata* mutable_metadata();
  void set_allocated_metadata(::financial_data::ResponseMetadata* value);
  void unsafe_arena_set_allocated_metadata(::financial_data::ResponseMetadata* value);
  ::financial_data::ResponseMetadata* unsafe_arena_release_metadata();

  private:
  const ::financial_data::ResponseMetadata& _internal_metadata() const;
  ::financial_data::ResponseMetadata* _internal_mutable_metadata();

  public:
  // @@protoc_insertion_point(class_scope:financial_data.Level2DataResponse)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 2,
      0, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::financial_data::Level2Data > level2_data_;
    ::financial_data::ResponseMetadata* metadata_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_5fservice_2eproto;
};

// ===================================================================




// ===================================================================


#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// TickDataRequest

// repeated string symbols = 1;
inline int TickDataRequest::_internal_symbols_size() const {
  return _internal_symbols().size();
}
inline int TickDataRequest::symbols_size() const {
  return _internal_symbols_size();
}
inline void TickDataRequest::clear_symbols() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbols_.Clear();
}
inline std::string* TickDataRequest::add_symbols()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  std::string* _s = _internal_mutable_symbols()->Add();
  // @@protoc_insertion_point(field_add_mutable:financial_data.TickDataRequest.symbols)
  return _s;
}
inline const std::string& TickDataRequest::symbols(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickDataRequest.symbols)
  return _internal_symbols().Get(index);
}
inline std::string* TickDataRequest::mutable_symbols(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.TickDataRequest.symbols)
  return _internal_mutable_symbols()->Mutable(index);
}
inline void TickDataRequest::set_symbols(int index, const std::string& value) {
  _internal_mutable_symbols()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::set_symbols(int index, std::string&& value) {
  _internal_mutable_symbols()->Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::set_symbols(int index, const char* value) {
  ABSL_DCHECK(value != nullptr);
  _internal_mutable_symbols()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::set_symbols(int index, const char* value,
                              std::size_t size) {
  _internal_mutable_symbols()->Mutable(index)->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::set_symbols(int index, absl::string_view value) {
  _internal_mutable_symbols()->Mutable(index)->assign(value.data(),
                                                     value.size());
  // @@protoc_insertion_point(field_set_string_piece:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::add_symbols(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value);
  // @@protoc_insertion_point(field_add:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::add_symbols(std::string&& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add(std::move(value));
  // @@protoc_insertion_point(field_add:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::add_symbols(const char* value) {
  ABSL_DCHECK(value != nullptr);
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::add_symbols(const char* value, std::size_t size) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:financial_data.TickDataRequest.symbols)
}
inline void TickDataRequest::add_symbols(absl::string_view value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value.data(), value.size());
  // @@protoc_insertion_point(field_add_string_piece:financial_data.TickDataRequest.symbols)
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
TickDataRequest::symbols() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.TickDataRequest.symbols)
  return _internal_symbols();
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
TickDataRequest::mutable_symbols() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.TickDataRequest.symbols)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_symbols();
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
TickDataRequest::_internal_symbols() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbols_;
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
TickDataRequest::_internal_mutable_symbols() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.symbols_;
}

// string exchange = 2;
inline void TickDataRequest::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& TickDataRequest::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickDataRequest.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void TickDataRequest::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.TickDataRequest.exchange)
}
inline std::string* TickDataRequest::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.TickDataRequest.exchange)
  return _s;
}
inline const std::string& TickDataRequest::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void TickDataRequest::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* TickDataRequest::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* TickDataRequest::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.TickDataRequest.exchange)
  return _impl_.exchange_.Release();
}
inline void TickDataRequest::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.TickDataRequest.exchange)
}

// bool include_level2 = 3;
inline void TickDataRequest::clear_include_level2() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.include_level2_ = false;
}
inline bool TickDataRequest::include_level2() const {
  // @@protoc_insertion_point(field_get:financial_data.TickDataRequest.include_level2)
  return _internal_include_level2();
}
inline void TickDataRequest::set_include_level2(bool value) {
  _internal_set_include_level2(value);
  // @@protoc_insertion_point(field_set:financial_data.TickDataRequest.include_level2)
}
inline bool TickDataRequest::_internal_include_level2() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.include_level2_;
}
inline void TickDataRequest::_internal_set_include_level2(bool value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.include_level2_ = value;
}

// int32 buffer_size = 4;
inline void TickDataRequest::clear_buffer_size() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.buffer_size_ = 0;
}
inline ::int32_t TickDataRequest::buffer_size() const {
  // @@protoc_insertion_point(field_get:financial_data.TickDataRequest.buffer_size)
  return _internal_buffer_size();
}
inline void TickDataRequest::set_buffer_size(::int32_t value) {
  _internal_set_buffer_size(value);
  // @@protoc_insertion_point(field_set:financial_data.TickDataRequest.buffer_size)
}
inline ::int32_t TickDataRequest::_internal_buffer_size() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.buffer_size_;
}
inline void TickDataRequest::_internal_set_buffer_size(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.buffer_size_ = value;
}

// -------------------------------------------------------------------

// HistoricalTickDataRequest

// string symbol = 1;
inline void HistoricalTickDataRequest::clear_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.ClearToEmpty();
}
inline const std::string& HistoricalTickDataRequest::symbol() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.HistoricalTickDataRequest.symbol)
  return _internal_symbol();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void HistoricalTickDataRequest::set_symbol(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.HistoricalTickDataRequest.symbol)
}
inline std::string* HistoricalTickDataRequest::mutable_symbol() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_symbol();
  // @@protoc_insertion_point(field_mutable:financial_data.HistoricalTickDataRequest.symbol)
  return _s;
}
inline const std::string& HistoricalTickDataRequest::_internal_symbol() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbol_.Get();
}
inline void HistoricalTickDataRequest::_internal_set_symbol(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(value, GetArena());
}
inline std::string* HistoricalTickDataRequest::_internal_mutable_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.symbol_.Mutable( GetArena());
}
inline std::string* HistoricalTickDataRequest::release_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.HistoricalTickDataRequest.symbol)
  return _impl_.symbol_.Release();
}
inline void HistoricalTickDataRequest::set_allocated_symbol(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.symbol_.IsDefault()) {
          _impl_.symbol_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.HistoricalTickDataRequest.symbol)
}

// string exchange = 2;
inline void HistoricalTickDataRequest::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& HistoricalTickDataRequest::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.HistoricalTickDataRequest.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void HistoricalTickDataRequest::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.HistoricalTickDataRequest.exchange)
}
inline std::string* HistoricalTickDataRequest::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.HistoricalTickDataRequest.exchange)
  return _s;
}
inline const std::string& HistoricalTickDataRequest::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void HistoricalTickDataRequest::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* HistoricalTickDataRequest::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* HistoricalTickDataRequest::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.HistoricalTickDataRequest.exchange)
  return _impl_.exchange_.Release();
}
inline void HistoricalTickDataRequest::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.HistoricalTickDataRequest.exchange)
}

// int64 start_timestamp = 3;
inline void HistoricalTickDataRequest::clear_start_timestamp() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.start_timestamp_ = ::int64_t{0};
}
inline ::int64_t HistoricalTickDataRequest::start_timestamp() const {
  // @@protoc_insertion_point(field_get:financial_data.HistoricalTickDataRequest.start_timestamp)
  return _internal_start_timestamp();
}
inline void HistoricalTickDataRequest::set_start_timestamp(::int64_t value) {
  _internal_set_start_timestamp(value);
  // @@protoc_insertion_point(field_set:financial_data.HistoricalTickDataRequest.start_timestamp)
}
inline ::int64_t HistoricalTickDataRequest::_internal_start_timestamp() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.start_timestamp_;
}
inline void HistoricalTickDataRequest::_internal_set_start_timestamp(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.start_timestamp_ = value;
}

// int64 end_timestamp = 4;
inline void HistoricalTickDataRequest::clear_end_timestamp() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.end_timestamp_ = ::int64_t{0};
}
inline ::int64_t HistoricalTickDataRequest::end_timestamp() const {
  // @@protoc_insertion_point(field_get:financial_data.HistoricalTickDataRequest.end_timestamp)
  return _internal_end_timestamp();
}
inline void HistoricalTickDataRequest::set_end_timestamp(::int64_t value) {
  _internal_set_end_timestamp(value);
  // @@protoc_insertion_point(field_set:financial_data.HistoricalTickDataRequest.end_timestamp)
}
inline ::int64_t HistoricalTickDataRequest::_internal_end_timestamp() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.end_timestamp_;
}
inline void HistoricalTickDataRequest::_internal_set_end_timestamp(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.end_timestamp_ = value;
}

// int32 limit = 5;
inline void HistoricalTickDataRequest::clear_limit() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.limit_ = 0;
}
inline ::int32_t HistoricalTickDataRequest::limit() const {
  // @@protoc_insertion_point(field_get:financial_data.HistoricalTickDataRequest.limit)
  return _internal_limit();
}
inline void HistoricalTickDataRequest::set_limit(::int32_t value) {
  _internal_set_limit(value);
  // @@protoc_insertion_point(field_set:financial_data.HistoricalTickDataRequest.limit)
}
inline ::int32_t HistoricalTickDataRequest::_internal_limit() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.limit_;
}
inline void HistoricalTickDataRequest::_internal_set_limit(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.limit_ = value;
}

// string cursor = 6;
inline void HistoricalTickDataRequest::clear_cursor() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.cursor_.ClearToEmpty();
}
inline const std::string& HistoricalTickDataRequest::cursor() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.HistoricalTickDataRequest.cursor)
  return _internal_cursor();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void HistoricalTickDataRequest::set_cursor(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.cursor_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.HistoricalTickDataRequest.cursor)
}
inline std::string* HistoricalTickDataRequest::mutable_cursor() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_cursor();
  // @@protoc_insertion_point(field_mutable:financial_data.HistoricalTickDataRequest.cursor)
  return _s;
}
inline const std::string& HistoricalTickDataRequest::_internal_cursor() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.cursor_.Get();
}
inline void HistoricalTickDataRequest::_internal_set_cursor(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.cursor_.Set(value, GetArena());
}
inline std::string* HistoricalTickDataRequest::_internal_mutable_cursor() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.cursor_.Mutable( GetArena());
}
inline std::string* HistoricalTickDataRequest::release_cursor() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.HistoricalTickDataRequest.cursor)
  return _impl_.cursor_.Release();
}
inline void HistoricalTickDataRequest::set_allocated_cursor(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.cursor_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.cursor_.IsDefault()) {
          _impl_.cursor_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.HistoricalTickDataRequest.cursor)
}

// int32 buffer_size = 7;
inline void HistoricalTickDataRequest::clear_buffer_size() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.buffer_size_ = 0;
}
inline ::int32_t HistoricalTickDataRequest::buffer_size() const {
  // @@protoc_insertion_point(field_get:financial_data.HistoricalTickDataRequest.buffer_size)
  return _internal_buffer_size();
}
inline void HistoricalTickDataRequest::set_buffer_size(::int32_t value) {
  _internal_set_buffer_size(value);
  // @@protoc_insertion_point(field_set:financial_data.HistoricalTickDataRequest.buffer_size)
}
inline ::int32_t HistoricalTickDataRequest::_internal_buffer_size() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.buffer_size_;
}
inline void HistoricalTickDataRequest::_internal_set_buffer_size(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.buffer_size_ = value;
}

// -------------------------------------------------------------------

// KlineDataRequest

// repeated string symbols = 1;
inline int KlineDataRequest::_internal_symbols_size() const {
  return _internal_symbols().size();
}
inline int KlineDataRequest::symbols_size() const {
  return _internal_symbols_size();
}
inline void KlineDataRequest::clear_symbols() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbols_.Clear();
}
inline std::string* KlineDataRequest::add_symbols()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  std::string* _s = _internal_mutable_symbols()->Add();
  // @@protoc_insertion_point(field_add_mutable:financial_data.KlineDataRequest.symbols)
  return _s;
}
inline const std::string& KlineDataRequest::symbols(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataRequest.symbols)
  return _internal_symbols().Get(index);
}
inline std::string* KlineDataRequest::mutable_symbols(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.KlineDataRequest.symbols)
  return _internal_mutable_symbols()->Mutable(index);
}
inline void KlineDataRequest::set_symbols(int index, const std::string& value) {
  _internal_mutable_symbols()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::set_symbols(int index, std::string&& value) {
  _internal_mutable_symbols()->Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::set_symbols(int index, const char* value) {
  ABSL_DCHECK(value != nullptr);
  _internal_mutable_symbols()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::set_symbols(int index, const char* value,
                              std::size_t size) {
  _internal_mutable_symbols()->Mutable(index)->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::set_symbols(int index, absl::string_view value) {
  _internal_mutable_symbols()->Mutable(index)->assign(value.data(),
                                                     value.size());
  // @@protoc_insertion_point(field_set_string_piece:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::add_symbols(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value);
  // @@protoc_insertion_point(field_add:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::add_symbols(std::string&& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add(std::move(value));
  // @@protoc_insertion_point(field_add:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::add_symbols(const char* value) {
  ABSL_DCHECK(value != nullptr);
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::add_symbols(const char* value, std::size_t size) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:financial_data.KlineDataRequest.symbols)
}
inline void KlineDataRequest::add_symbols(absl::string_view value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value.data(), value.size());
  // @@protoc_insertion_point(field_add_string_piece:financial_data.KlineDataRequest.symbols)
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
KlineDataRequest::symbols() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.KlineDataRequest.symbols)
  return _internal_symbols();
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
KlineDataRequest::mutable_symbols() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.KlineDataRequest.symbols)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_symbols();
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
KlineDataRequest::_internal_symbols() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbols_;
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
KlineDataRequest::_internal_mutable_symbols() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.symbols_;
}

// string exchange = 2;
inline void KlineDataRequest::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& KlineDataRequest::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataRequest.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void KlineDataRequest::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.KlineDataRequest.exchange)
}
inline std::string* KlineDataRequest::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.KlineDataRequest.exchange)
  return _s;
}
inline const std::string& KlineDataRequest::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void KlineDataRequest::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* KlineDataRequest::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* KlineDataRequest::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.KlineDataRequest.exchange)
  return _impl_.exchange_.Release();
}
inline void KlineDataRequest::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.KlineDataRequest.exchange)
}

// string period = 3;
inline void KlineDataRequest::clear_period() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.period_.ClearToEmpty();
}
inline const std::string& KlineDataRequest::period() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataRequest.period)
  return _internal_period();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void KlineDataRequest::set_period(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.period_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.KlineDataRequest.period)
}
inline std::string* KlineDataRequest::mutable_period() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_period();
  // @@protoc_insertion_point(field_mutable:financial_data.KlineDataRequest.period)
  return _s;
}
inline const std::string& KlineDataRequest::_internal_period() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.period_.Get();
}
inline void KlineDataRequest::_internal_set_period(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.period_.Set(value, GetArena());
}
inline std::string* KlineDataRequest::_internal_mutable_period() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.period_.Mutable( GetArena());
}
inline std::string* KlineDataRequest::release_period() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.KlineDataRequest.period)
  return _impl_.period_.Release();
}
inline void KlineDataRequest::set_allocated_period(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.period_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.period_.IsDefault()) {
          _impl_.period_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.KlineDataRequest.period)
}

// int64 start_timestamp = 4;
inline void KlineDataRequest::clear_start_timestamp() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.start_timestamp_ = ::int64_t{0};
}
inline ::int64_t KlineDataRequest::start_timestamp() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataRequest.start_timestamp)
  return _internal_start_timestamp();
}
inline void KlineDataRequest::set_start_timestamp(::int64_t value) {
  _internal_set_start_timestamp(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineDataRequest.start_timestamp)
}
inline ::int64_t KlineDataRequest::_internal_start_timestamp() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.start_timestamp_;
}
inline void KlineDataRequest::_internal_set_start_timestamp(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.start_timestamp_ = value;
}

// int64 end_timestamp = 5;
inline void KlineDataRequest::clear_end_timestamp() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.end_timestamp_ = ::int64_t{0};
}
inline ::int64_t KlineDataRequest::end_timestamp() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataRequest.end_timestamp)
  return _internal_end_timestamp();
}
inline void KlineDataRequest::set_end_timestamp(::int64_t value) {
  _internal_set_end_timestamp(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineDataRequest.end_timestamp)
}
inline ::int64_t KlineDataRequest::_internal_end_timestamp() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.end_timestamp_;
}
inline void KlineDataRequest::_internal_set_end_timestamp(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.end_timestamp_ = value;
}

// int32 buffer_size = 6;
inline void KlineDataRequest::clear_buffer_size() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.buffer_size_ = 0;
}
inline ::int32_t KlineDataRequest::buffer_size() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataRequest.buffer_size)
  return _internal_buffer_size();
}
inline void KlineDataRequest::set_buffer_size(::int32_t value) {
  _internal_set_buffer_size(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineDataRequest.buffer_size)
}
inline ::int32_t KlineDataRequest::_internal_buffer_size() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.buffer_size_;
}
inline void KlineDataRequest::_internal_set_buffer_size(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.buffer_size_ = value;
}

// -------------------------------------------------------------------

// Level2DataRequest

// repeated string symbols = 1;
inline int Level2DataRequest::_internal_symbols_size() const {
  return _internal_symbols().size();
}
inline int Level2DataRequest::symbols_size() const {
  return _internal_symbols_size();
}
inline void Level2DataRequest::clear_symbols() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbols_.Clear();
}
inline std::string* Level2DataRequest::add_symbols()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  std::string* _s = _internal_mutable_symbols()->Add();
  // @@protoc_insertion_point(field_add_mutable:financial_data.Level2DataRequest.symbols)
  return _s;
}
inline const std::string& Level2DataRequest::symbols(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.Level2DataRequest.symbols)
  return _internal_symbols().Get(index);
}
inline std::string* Level2DataRequest::mutable_symbols(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.Level2DataRequest.symbols)
  return _internal_mutable_symbols()->Mutable(index);
}
inline void Level2DataRequest::set_symbols(int index, const std::string& value) {
  _internal_mutable_symbols()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::set_symbols(int index, std::string&& value) {
  _internal_mutable_symbols()->Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::set_symbols(int index, const char* value) {
  ABSL_DCHECK(value != nullptr);
  _internal_mutable_symbols()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::set_symbols(int index, const char* value,
                              std::size_t size) {
  _internal_mutable_symbols()->Mutable(index)->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::set_symbols(int index, absl::string_view value) {
  _internal_mutable_symbols()->Mutable(index)->assign(value.data(),
                                                     value.size());
  // @@protoc_insertion_point(field_set_string_piece:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::add_symbols(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value);
  // @@protoc_insertion_point(field_add:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::add_symbols(std::string&& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add(std::move(value));
  // @@protoc_insertion_point(field_add:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::add_symbols(const char* value) {
  ABSL_DCHECK(value != nullptr);
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::add_symbols(const char* value, std::size_t size) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:financial_data.Level2DataRequest.symbols)
}
inline void Level2DataRequest::add_symbols(absl::string_view value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value.data(), value.size());
  // @@protoc_insertion_point(field_add_string_piece:financial_data.Level2DataRequest.symbols)
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
Level2DataRequest::symbols() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.Level2DataRequest.symbols)
  return _internal_symbols();
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
Level2DataRequest::mutable_symbols() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.Level2DataRequest.symbols)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_symbols();
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
Level2DataRequest::_internal_symbols() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbols_;
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
Level2DataRequest::_internal_mutable_symbols() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.symbols_;
}

// string exchange = 2;
inline void Level2DataRequest::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& Level2DataRequest::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.Level2DataRequest.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void Level2DataRequest::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.Level2DataRequest.exchange)
}
inline std::string* Level2DataRequest::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.Level2DataRequest.exchange)
  return _s;
}
inline const std::string& Level2DataRequest::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void Level2DataRequest::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* Level2DataRequest::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* Level2DataRequest::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.Level2DataRequest.exchange)
  return _impl_.exchange_.Release();
}
inline void Level2DataRequest::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.Level2DataRequest.exchange)
}

// int32 depth = 3;
inline void Level2DataRequest::clear_depth() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.depth_ = 0;
}
inline ::int32_t Level2DataRequest::depth() const {
  // @@protoc_insertion_point(field_get:financial_data.Level2DataRequest.depth)
  return _internal_depth();
}
inline void Level2DataRequest::set_depth(::int32_t value) {
  _internal_set_depth(value);
  // @@protoc_insertion_point(field_set:financial_data.Level2DataRequest.depth)
}
inline ::int32_t Level2DataRequest::_internal_depth() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.depth_;
}
inline void Level2DataRequest::_internal_set_depth(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.depth_ = value;
}

// int32 buffer_size = 4;
inline void Level2DataRequest::clear_buffer_size() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.buffer_size_ = 0;
}
inline ::int32_t Level2DataRequest::buffer_size() const {
  // @@protoc_insertion_point(field_get:financial_data.Level2DataRequest.buffer_size)
  return _internal_buffer_size();
}
inline void Level2DataRequest::set_buffer_size(::int32_t value) {
  _internal_set_buffer_size(value);
  // @@protoc_insertion_point(field_set:financial_data.Level2DataRequest.buffer_size)
}
inline ::int32_t Level2DataRequest::_internal_buffer_size() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.buffer_size_;
}
inline void Level2DataRequest::_internal_set_buffer_size(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.buffer_size_ = value;
}

// -------------------------------------------------------------------

// HealthCheckRequest

// string service = 1;
inline void HealthCheckRequest::clear_service() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.service_.ClearToEmpty();
}
inline const std::string& HealthCheckRequest::service() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.HealthCheckRequest.service)
  return _internal_service();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void HealthCheckRequest::set_service(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.service_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.HealthCheckRequest.service)
}
inline std::string* HealthCheckRequest::mutable_service() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_service();
  // @@protoc_insertion_point(field_mutable:financial_data.HealthCheckRequest.service)
  return _s;
}
inline const std::string& HealthCheckRequest::_internal_service() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.service_.Get();
}
inline void HealthCheckRequest::_internal_set_service(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.service_.Set(value, GetArena());
}
inline std::string* HealthCheckRequest::_internal_mutable_service() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.service_.Mutable( GetArena());
}
inline std::string* HealthCheckRequest::release_service() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.HealthCheckRequest.service)
  return _impl_.service_.Release();
}
inline void HealthCheckRequest::set_allocated_service(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.service_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.service_.IsDefault()) {
          _impl_.service_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.HealthCheckRequest.service)
}

// -------------------------------------------------------------------

// TickDataResponse

// repeated .financial_data.TickData ticks = 1;
inline int TickDataResponse::_internal_ticks_size() const {
  return _internal_ticks().size();
}
inline int TickDataResponse::ticks_size() const {
  return _internal_ticks_size();
}
inline void TickDataResponse::clear_ticks() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.ticks_.Clear();
}
inline ::financial_data::TickData* TickDataResponse::mutable_ticks(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.TickDataResponse.ticks)
  return _internal_mutable_ticks()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::TickData>* TickDataResponse::mutable_ticks()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.TickDataResponse.ticks)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_ticks();
}
inline const ::financial_data::TickData& TickDataResponse::ticks(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickDataResponse.ticks)
  return _internal_ticks().Get(index);
}
inline ::financial_data::TickData* TickDataResponse::add_ticks() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::TickData* _add = _internal_mutable_ticks()->Add();
  // @@protoc_insertion_point(field_add:financial_data.TickDataResponse.ticks)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::TickData>& TickDataResponse::ticks() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.TickDataResponse.ticks)
  return _internal_ticks();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::TickData>&
TickDataResponse::_internal_ticks() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.ticks_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::TickData>*
TickDataResponse::_internal_mutable_ticks() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.ticks_;
}

// bool has_more = 2;
inline void TickDataResponse::clear_has_more() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.has_more_ = false;
}
inline bool TickDataResponse::has_more() const {
  // @@protoc_insertion_point(field_get:financial_data.TickDataResponse.has_more)
  return _internal_has_more();
}
inline void TickDataResponse::set_has_more(bool value) {
  _internal_set_has_more(value);
  // @@protoc_insertion_point(field_set:financial_data.TickDataResponse.has_more)
}
inline bool TickDataResponse::_internal_has_more() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.has_more_;
}
inline void TickDataResponse::_internal_set_has_more(bool value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.has_more_ = value;
}

// string next_cursor = 3;
inline void TickDataResponse::clear_next_cursor() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.next_cursor_.ClearToEmpty();
}
inline const std::string& TickDataResponse::next_cursor() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickDataResponse.next_cursor)
  return _internal_next_cursor();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void TickDataResponse::set_next_cursor(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.next_cursor_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.TickDataResponse.next_cursor)
}
inline std::string* TickDataResponse::mutable_next_cursor() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_next_cursor();
  // @@protoc_insertion_point(field_mutable:financial_data.TickDataResponse.next_cursor)
  return _s;
}
inline const std::string& TickDataResponse::_internal_next_cursor() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.next_cursor_.Get();
}
inline void TickDataResponse::_internal_set_next_cursor(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.next_cursor_.Set(value, GetArena());
}
inline std::string* TickDataResponse::_internal_mutable_next_cursor() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.next_cursor_.Mutable( GetArena());
}
inline std::string* TickDataResponse::release_next_cursor() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.TickDataResponse.next_cursor)
  return _impl_.next_cursor_.Release();
}
inline void TickDataResponse::set_allocated_next_cursor(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.next_cursor_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.next_cursor_.IsDefault()) {
          _impl_.next_cursor_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.TickDataResponse.next_cursor)
}

// .financial_data.ResponseMetadata metadata = 4;
inline bool TickDataResponse::has_metadata() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.metadata_ != nullptr);
  return value;
}
inline void TickDataResponse::clear_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (_impl_.metadata_ != nullptr) _impl_.metadata_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::financial_data::ResponseMetadata& TickDataResponse::_internal_metadata() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  const ::financial_data::ResponseMetadata* p = _impl_.metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::financial_data::ResponseMetadata&>(::financial_data::_ResponseMetadata_default_instance_);
}
inline const ::financial_data::ResponseMetadata& TickDataResponse::metadata() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickDataResponse.metadata)
  return _internal_metadata();
}
inline void TickDataResponse::unsafe_arena_set_allocated_metadata(::financial_data::ResponseMetadata* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.metadata_);
  }
  _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:financial_data.TickDataResponse.metadata)
}
inline ::financial_data::ResponseMetadata* TickDataResponse::release_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::financial_data::ResponseMetadata* released = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
  released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  if (GetArena() == nullptr) {
    delete old;
  }
#else   // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArena() != nullptr) {
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return released;
}
inline ::financial_data::ResponseMetadata* TickDataResponse::unsafe_arena_release_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.TickDataResponse.metadata)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::financial_data::ResponseMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
  return temp;
}
inline ::financial_data::ResponseMetadata* TickDataResponse::_internal_mutable_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_._has_bits_[0] |= 0x00000001u;
  if (_impl_.metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::financial_data::ResponseMetadata>(GetArena());
    _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(p);
  }
  return _impl_.metadata_;
}
inline ::financial_data::ResponseMetadata* TickDataResponse::mutable_metadata() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::financial_data::ResponseMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:financial_data.TickDataResponse.metadata)
  return _msg;
}
inline void TickDataResponse::set_allocated_metadata(::financial_data::ResponseMetadata* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (message_arena == nullptr) {
    delete reinterpret_cast<::financial_data::ResponseMetadata*>(_impl_.metadata_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = reinterpret_cast<::financial_data::ResponseMetadata*>(value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(value);
  // @@protoc_insertion_point(field_set_allocated:financial_data.TickDataResponse.metadata)
}

// -------------------------------------------------------------------

// KlineDataResponse

// repeated .financial_data.KlineData klines = 1;
inline int KlineDataResponse::_internal_klines_size() const {
  return _internal_klines().size();
}
inline int KlineDataResponse::klines_size() const {
  return _internal_klines_size();
}
inline void KlineDataResponse::clear_klines() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.klines_.Clear();
}
inline ::financial_data::KlineData* KlineDataResponse::mutable_klines(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.KlineDataResponse.klines)
  return _internal_mutable_klines()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::KlineData>* KlineDataResponse::mutable_klines()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.KlineDataResponse.klines)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_klines();
}
inline const ::financial_data::KlineData& KlineDataResponse::klines(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataResponse.klines)
  return _internal_klines().Get(index);
}
inline ::financial_data::KlineData* KlineDataResponse::add_klines() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::KlineData* _add = _internal_mutable_klines()->Add();
  // @@protoc_insertion_point(field_add:financial_data.KlineDataResponse.klines)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::KlineData>& KlineDataResponse::klines() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.KlineDataResponse.klines)
  return _internal_klines();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::KlineData>&
KlineDataResponse::_internal_klines() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.klines_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::KlineData>*
KlineDataResponse::_internal_mutable_klines() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.klines_;
}

// bool has_more = 2;
inline void KlineDataResponse::clear_has_more() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.has_more_ = false;
}
inline bool KlineDataResponse::has_more() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataResponse.has_more)
  return _internal_has_more();
}
inline void KlineDataResponse::set_has_more(bool value) {
  _internal_set_has_more(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineDataResponse.has_more)
}
inline bool KlineDataResponse::_internal_has_more() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.has_more_;
}
inline void KlineDataResponse::_internal_set_has_more(bool value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.has_more_ = value;
}

// .financial_data.ResponseMetadata metadata = 3;
inline bool KlineDataResponse::has_metadata() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.metadata_ != nullptr);
  return value;
}
inline void KlineDataResponse::clear_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (_impl_.metadata_ != nullptr) _impl_.metadata_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::financial_data::ResponseMetadata& KlineDataResponse::_internal_metadata() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  const ::financial_data::ResponseMetadata* p = _impl_.metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::financial_data::ResponseMetadata&>(::financial_data::_ResponseMetadata_default_instance_);
}
inline const ::financial_data::ResponseMetadata& KlineDataResponse::metadata() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.KlineDataResponse.metadata)
  return _internal_metadata();
}
inline void KlineDataResponse::unsafe_arena_set_allocated_metadata(::financial_data::ResponseMetadata* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.metadata_);
  }
  _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:financial_data.KlineDataResponse.metadata)
}
inline ::financial_data::ResponseMetadata* KlineDataResponse::release_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::financial_data::ResponseMetadata* released = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
  released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  if (GetArena() == nullptr) {
    delete old;
  }
#else   // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArena() != nullptr) {
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return released;
}
inline ::financial_data::ResponseMetadata* KlineDataResponse::unsafe_arena_release_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.KlineDataResponse.metadata)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::financial_data::ResponseMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
  return temp;
}
inline ::financial_data::ResponseMetadata* KlineDataResponse::_internal_mutable_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_._has_bits_[0] |= 0x00000001u;
  if (_impl_.metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::financial_data::ResponseMetadata>(GetArena());
    _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(p);
  }
  return _impl_.metadata_;
}
inline ::financial_data::ResponseMetadata* KlineDataResponse::mutable_metadata() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::financial_data::ResponseMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:financial_data.KlineDataResponse.metadata)
  return _msg;
}
inline void KlineDataResponse::set_allocated_metadata(::financial_data::ResponseMetadata* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (message_arena == nullptr) {
    delete reinterpret_cast<::financial_data::ResponseMetadata*>(_impl_.metadata_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = reinterpret_cast<::financial_data::ResponseMetadata*>(value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(value);
  // @@protoc_insertion_point(field_set_allocated:financial_data.KlineDataResponse.metadata)
}

// -------------------------------------------------------------------

// Level2DataResponse

// repeated .financial_data.Level2Data level2_data = 1;
inline int Level2DataResponse::_internal_level2_data_size() const {
  return _internal_level2_data().size();
}
inline int Level2DataResponse::level2_data_size() const {
  return _internal_level2_data_size();
}
inline void Level2DataResponse::clear_level2_data() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.level2_data_.Clear();
}
inline ::financial_data::Level2Data* Level2DataResponse::mutable_level2_data(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.Level2DataResponse.level2_data)
  return _internal_mutable_level2_data()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::Level2Data>* Level2DataResponse::mutable_level2_data()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.Level2DataResponse.level2_data)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_level2_data();
}
inline const ::financial_data::Level2Data& Level2DataResponse::level2_data(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.Level2DataResponse.level2_data)
  return _internal_level2_data().Get(index);
}
inline ::financial_data::Level2Data* Level2DataResponse::add_level2_data() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::Level2Data* _add = _internal_mutable_level2_data()->Add();
  // @@protoc_insertion_point(field_add:financial_data.Level2DataResponse.level2_data)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::Level2Data>& Level2DataResponse::level2_data() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.Level2DataResponse.level2_data)
  return _internal_level2_data();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::Level2Data>&
Level2DataResponse::_internal_level2_data() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.level2_data_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::Level2Data>*
Level2DataResponse::_internal_mutable_level2_data() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.level2_data_;
}

// .financial_data.ResponseMetadata metadata = 2;
inline bool Level2DataResponse::has_metadata() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.metadata_ != nullptr);
  return value;
}
inline void Level2DataResponse::clear_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (_impl_.metadata_ != nullptr) _impl_.metadata_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::financial_data::ResponseMetadata& Level2DataResponse::_internal_metadata() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  const ::financial_data::ResponseMetadata* p = _impl_.metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::financial_data::ResponseMetadata&>(::financial_data::_ResponseMetadata_default_instance_);
}
inline const ::financial_data::ResponseMetadata& Level2DataResponse::metadata() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.Level2DataResponse.metadata)
  return _internal_metadata();
}
inline void Level2DataResponse::unsafe_arena_set_allocated_metadata(::financial_data::ResponseMetadata* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.metadata_);
  }
  _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:financial_data.Level2DataResponse.metadata)
}
inline ::financial_data::ResponseMetadata* Level2DataResponse::release_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::financial_data::ResponseMetadata* released = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
  released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  if (GetArena() == nullptr) {
    delete old;
  }
#else   // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArena() != nullptr) {
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return released;
}
inline ::financial_data::ResponseMetadata* Level2DataResponse::unsafe_arena_release_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.Level2DataResponse.metadata)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::financial_data::ResponseMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
  return temp;
}
inline ::financial_data::ResponseMetadata* Level2DataResponse::_internal_mutable_metadata() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_._has_bits_[0] |= 0x00000001u;
  if (_impl_.metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::financial_data::ResponseMetadata>(GetArena());
    _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(p);
  }
  return _impl_.metadata_;
}
inline ::financial_data::ResponseMetadata* Level2DataResponse::mutable_metadata() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::financial_data::ResponseMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:financial_data.Level2DataResponse.metadata)
  return _msg;
}
inline void Level2DataResponse::set_allocated_metadata(::financial_data::ResponseMetadata* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (message_arena == nullptr) {
    delete reinterpret_cast<::financial_data::ResponseMetadata*>(_impl_.metadata_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = reinterpret_cast<::financial_data::ResponseMetadata*>(value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.metadata_ = reinterpret_cast<::financial_data::ResponseMetadata*>(value);
  // @@protoc_insertion_point(field_set_allocated:financial_data.Level2DataResponse.metadata)
}

// -------------------------------------------------------------------

// HealthCheckResponse

// .financial_data.HealthCheckResponse.ServingStatus status = 1;
inline void HealthCheckResponse::clear_status() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.status_ = 0;
}
inline ::financial_data::HealthCheckResponse_ServingStatus HealthCheckResponse::status() const {
  // @@protoc_insertion_point(field_get:financial_data.HealthCheckResponse.status)
  return _internal_status();
}
inline void HealthCheckResponse::set_status(::financial_data::HealthCheckResponse_ServingStatus value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:financial_data.HealthCheckResponse.status)
}
inline ::financial_data::HealthCheckResponse_ServingStatus HealthCheckResponse::_internal_status() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return static_cast<::financial_data::HealthCheckResponse_ServingStatus>(_impl_.status_);
}
inline void HealthCheckResponse::_internal_set_status(::financial_data::HealthCheckResponse_ServingStatus value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.status_ = value;
}

// string message = 2;
inline void HealthCheckResponse::clear_message() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.message_.ClearToEmpty();
}
inline const std::string& HealthCheckResponse::message() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.HealthCheckResponse.message)
  return _internal_message();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void HealthCheckResponse::set_message(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.message_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.HealthCheckResponse.message)
}
inline std::string* HealthCheckResponse::mutable_message() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:financial_data.HealthCheckResponse.message)
  return _s;
}
inline const std::string& HealthCheckResponse::_internal_message() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.message_.Get();
}
inline void HealthCheckResponse::_internal_set_message(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.message_.Set(value, GetArena());
}
inline std::string* HealthCheckResponse::_internal_mutable_message() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.message_.Mutable( GetArena());
}
inline std::string* HealthCheckResponse::release_message() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.HealthCheckResponse.message)
  return _impl_.message_.Release();
}
inline void HealthCheckResponse::set_allocated_message(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.message_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.message_.IsDefault()) {
          _impl_.message_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.HealthCheckResponse.message)
}

// -------------------------------------------------------------------

// TickData

// int64 timestamp = 1;
inline void TickData::clear_timestamp() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.timestamp_ = ::int64_t{0};
}
inline ::int64_t TickData::timestamp() const {
  // @@protoc_insertion_point(field_get:financial_data.TickData.timestamp)
  return _internal_timestamp();
}
inline void TickData::set_timestamp(::int64_t value) {
  _internal_set_timestamp(value);
  // @@protoc_insertion_point(field_set:financial_data.TickData.timestamp)
}
inline ::int64_t TickData::_internal_timestamp() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.timestamp_;
}
inline void TickData::_internal_set_timestamp(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.timestamp_ = value;
}

// string symbol = 2;
inline void TickData::clear_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.ClearToEmpty();
}
inline const std::string& TickData::symbol() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickData.symbol)
  return _internal_symbol();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void TickData::set_symbol(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.TickData.symbol)
}
inline std::string* TickData::mutable_symbol() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_symbol();
  // @@protoc_insertion_point(field_mutable:financial_data.TickData.symbol)
  return _s;
}
inline const std::string& TickData::_internal_symbol() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbol_.Get();
}
inline void TickData::_internal_set_symbol(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(value, GetArena());
}
inline std::string* TickData::_internal_mutable_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.symbol_.Mutable( GetArena());
}
inline std::string* TickData::release_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.TickData.symbol)
  return _impl_.symbol_.Release();
}
inline void TickData::set_allocated_symbol(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.symbol_.IsDefault()) {
          _impl_.symbol_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.TickData.symbol)
}

// string exchange = 3;
inline void TickData::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& TickData::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickData.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void TickData::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.TickData.exchange)
}
inline std::string* TickData::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.TickData.exchange)
  return _s;
}
inline const std::string& TickData::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void TickData::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* TickData::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* TickData::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.TickData.exchange)
  return _impl_.exchange_.Release();
}
inline void TickData::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.TickData.exchange)
}

// double last_price = 4;
inline void TickData::clear_last_price() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.last_price_ = 0;
}
inline double TickData::last_price() const {
  // @@protoc_insertion_point(field_get:financial_data.TickData.last_price)
  return _internal_last_price();
}
inline void TickData::set_last_price(double value) {
  _internal_set_last_price(value);
  // @@protoc_insertion_point(field_set:financial_data.TickData.last_price)
}
inline double TickData::_internal_last_price() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.last_price_;
}
inline void TickData::_internal_set_last_price(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.last_price_ = value;
}

// int64 volume = 5;
inline void TickData::clear_volume() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.volume_ = ::int64_t{0};
}
inline ::int64_t TickData::volume() const {
  // @@protoc_insertion_point(field_get:financial_data.TickData.volume)
  return _internal_volume();
}
inline void TickData::set_volume(::int64_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:financial_data.TickData.volume)
}
inline ::int64_t TickData::_internal_volume() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.volume_;
}
inline void TickData::_internal_set_volume(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.volume_ = value;
}

// double turnover = 6;
inline void TickData::clear_turnover() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.turnover_ = 0;
}
inline double TickData::turnover() const {
  // @@protoc_insertion_point(field_get:financial_data.TickData.turnover)
  return _internal_turnover();
}
inline void TickData::set_turnover(double value) {
  _internal_set_turnover(value);
  // @@protoc_insertion_point(field_set:financial_data.TickData.turnover)
}
inline double TickData::_internal_turnover() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.turnover_;
}
inline void TickData::_internal_set_turnover(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.turnover_ = value;
}

// int64 open_interest = 7;
inline void TickData::clear_open_interest() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.open_interest_ = ::int64_t{0};
}
inline ::int64_t TickData::open_interest() const {
  // @@protoc_insertion_point(field_get:financial_data.TickData.open_interest)
  return _internal_open_interest();
}
inline void TickData::set_open_interest(::int64_t value) {
  _internal_set_open_interest(value);
  // @@protoc_insertion_point(field_set:financial_data.TickData.open_interest)
}
inline ::int64_t TickData::_internal_open_interest() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.open_interest_;
}
inline void TickData::_internal_set_open_interest(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.open_interest_ = value;
}

// repeated .financial_data.PriceLevel bids = 8;
inline int TickData::_internal_bids_size() const {
  return _internal_bids().size();
}
inline int TickData::bids_size() const {
  return _internal_bids_size();
}
inline void TickData::clear_bids() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.bids_.Clear();
}
inline ::financial_data::PriceLevel* TickData::mutable_bids(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.TickData.bids)
  return _internal_mutable_bids()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>* TickData::mutable_bids()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.TickData.bids)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_bids();
}
inline const ::financial_data::PriceLevel& TickData::bids(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickData.bids)
  return _internal_bids().Get(index);
}
inline ::financial_data::PriceLevel* TickData::add_bids() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::PriceLevel* _add = _internal_mutable_bids()->Add();
  // @@protoc_insertion_point(field_add:financial_data.TickData.bids)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>& TickData::bids() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.TickData.bids)
  return _internal_bids();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>&
TickData::_internal_bids() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.bids_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>*
TickData::_internal_mutable_bids() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.bids_;
}

// repeated .financial_data.PriceLevel asks = 9;
inline int TickData::_internal_asks_size() const {
  return _internal_asks().size();
}
inline int TickData::asks_size() const {
  return _internal_asks_size();
}
inline void TickData::clear_asks() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.asks_.Clear();
}
inline ::financial_data::PriceLevel* TickData::mutable_asks(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.TickData.asks)
  return _internal_mutable_asks()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>* TickData::mutable_asks()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.TickData.asks)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_asks();
}
inline const ::financial_data::PriceLevel& TickData::asks(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickData.asks)
  return _internal_asks().Get(index);
}
inline ::financial_data::PriceLevel* TickData::add_asks() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::PriceLevel* _add = _internal_mutable_asks()->Add();
  // @@protoc_insertion_point(field_add:financial_data.TickData.asks)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>& TickData::asks() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.TickData.asks)
  return _internal_asks();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>&
TickData::_internal_asks() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.asks_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>*
TickData::_internal_mutable_asks() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.asks_;
}

// uint32 sequence = 10;
inline void TickData::clear_sequence() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.sequence_ = 0u;
}
inline ::uint32_t TickData::sequence() const {
  // @@protoc_insertion_point(field_get:financial_data.TickData.sequence)
  return _internal_sequence();
}
inline void TickData::set_sequence(::uint32_t value) {
  _internal_set_sequence(value);
  // @@protoc_insertion_point(field_set:financial_data.TickData.sequence)
}
inline ::uint32_t TickData::_internal_sequence() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.sequence_;
}
inline void TickData::_internal_set_sequence(::uint32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.sequence_ = value;
}

// string trade_flag = 11;
inline void TickData::clear_trade_flag() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.trade_flag_.ClearToEmpty();
}
inline const std::string& TickData::trade_flag() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.TickData.trade_flag)
  return _internal_trade_flag();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void TickData::set_trade_flag(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.trade_flag_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.TickData.trade_flag)
}
inline std::string* TickData::mutable_trade_flag() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_trade_flag();
  // @@protoc_insertion_point(field_mutable:financial_data.TickData.trade_flag)
  return _s;
}
inline const std::string& TickData::_internal_trade_flag() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.trade_flag_.Get();
}
inline void TickData::_internal_set_trade_flag(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.trade_flag_.Set(value, GetArena());
}
inline std::string* TickData::_internal_mutable_trade_flag() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.trade_flag_.Mutable( GetArena());
}
inline std::string* TickData::release_trade_flag() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.TickData.trade_flag)
  return _impl_.trade_flag_.Release();
}
inline void TickData::set_allocated_trade_flag(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.trade_flag_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.trade_flag_.IsDefault()) {
          _impl_.trade_flag_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.TickData.trade_flag)
}

// -------------------------------------------------------------------

// KlineData

// string symbol = 1;
inline void KlineData::clear_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.ClearToEmpty();
}
inline const std::string& KlineData::symbol() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.symbol)
  return _internal_symbol();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void KlineData::set_symbol(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.KlineData.symbol)
}
inline std::string* KlineData::mutable_symbol() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_symbol();
  // @@protoc_insertion_point(field_mutable:financial_data.KlineData.symbol)
  return _s;
}
inline const std::string& KlineData::_internal_symbol() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbol_.Get();
}
inline void KlineData::_internal_set_symbol(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(value, GetArena());
}
inline std::string* KlineData::_internal_mutable_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.symbol_.Mutable( GetArena());
}
inline std::string* KlineData::release_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.KlineData.symbol)
  return _impl_.symbol_.Release();
}
inline void KlineData::set_allocated_symbol(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.symbol_.IsDefault()) {
          _impl_.symbol_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.KlineData.symbol)
}

// string exchange = 2;
inline void KlineData::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& KlineData::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void KlineData::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.KlineData.exchange)
}
inline std::string* KlineData::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.KlineData.exchange)
  return _s;
}
inline const std::string& KlineData::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void KlineData::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* KlineData::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* KlineData::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.KlineData.exchange)
  return _impl_.exchange_.Release();
}
inline void KlineData::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.KlineData.exchange)
}

// string period = 3;
inline void KlineData::clear_period() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.period_.ClearToEmpty();
}
inline const std::string& KlineData::period() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.period)
  return _internal_period();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void KlineData::set_period(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.period_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.KlineData.period)
}
inline std::string* KlineData::mutable_period() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_period();
  // @@protoc_insertion_point(field_mutable:financial_data.KlineData.period)
  return _s;
}
inline const std::string& KlineData::_internal_period() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.period_.Get();
}
inline void KlineData::_internal_set_period(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.period_.Set(value, GetArena());
}
inline std::string* KlineData::_internal_mutable_period() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.period_.Mutable( GetArena());
}
inline std::string* KlineData::release_period() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.KlineData.period)
  return _impl_.period_.Release();
}
inline void KlineData::set_allocated_period(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.period_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.period_.IsDefault()) {
          _impl_.period_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.KlineData.period)
}

// int64 timestamp = 4;
inline void KlineData::clear_timestamp() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.timestamp_ = ::int64_t{0};
}
inline ::int64_t KlineData::timestamp() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.timestamp)
  return _internal_timestamp();
}
inline void KlineData::set_timestamp(::int64_t value) {
  _internal_set_timestamp(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineData.timestamp)
}
inline ::int64_t KlineData::_internal_timestamp() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.timestamp_;
}
inline void KlineData::_internal_set_timestamp(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.timestamp_ = value;
}

// double open = 5;
inline void KlineData::clear_open() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.open_ = 0;
}
inline double KlineData::open() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.open)
  return _internal_open();
}
inline void KlineData::set_open(double value) {
  _internal_set_open(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineData.open)
}
inline double KlineData::_internal_open() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.open_;
}
inline void KlineData::_internal_set_open(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.open_ = value;
}

// double high = 6;
inline void KlineData::clear_high() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.high_ = 0;
}
inline double KlineData::high() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.high)
  return _internal_high();
}
inline void KlineData::set_high(double value) {
  _internal_set_high(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineData.high)
}
inline double KlineData::_internal_high() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.high_;
}
inline void KlineData::_internal_set_high(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.high_ = value;
}

// double low = 7;
inline void KlineData::clear_low() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.low_ = 0;
}
inline double KlineData::low() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.low)
  return _internal_low();
}
inline void KlineData::set_low(double value) {
  _internal_set_low(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineData.low)
}
inline double KlineData::_internal_low() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.low_;
}
inline void KlineData::_internal_set_low(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.low_ = value;
}

// double close = 8;
inline void KlineData::clear_close() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.close_ = 0;
}
inline double KlineData::close() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.close)
  return _internal_close();
}
inline void KlineData::set_close(double value) {
  _internal_set_close(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineData.close)
}
inline double KlineData::_internal_close() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.close_;
}
inline void KlineData::_internal_set_close(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.close_ = value;
}

// int64 volume = 9;
inline void KlineData::clear_volume() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.volume_ = ::int64_t{0};
}
inline ::int64_t KlineData::volume() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.volume)
  return _internal_volume();
}
inline void KlineData::set_volume(::int64_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineData.volume)
}
inline ::int64_t KlineData::_internal_volume() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.volume_;
}
inline void KlineData::_internal_set_volume(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.volume_ = value;
}

// double turnover = 10;
inline void KlineData::clear_turnover() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.turnover_ = 0;
}
inline double KlineData::turnover() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.turnover)
  return _internal_turnover();
}
inline void KlineData::set_turnover(double value) {
  _internal_set_turnover(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineData.turnover)
}
inline double KlineData::_internal_turnover() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.turnover_;
}
inline void KlineData::_internal_set_turnover(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.turnover_ = value;
}

// int64 open_interest = 11;
inline void KlineData::clear_open_interest() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.open_interest_ = ::int64_t{0};
}
inline ::int64_t KlineData::open_interest() const {
  // @@protoc_insertion_point(field_get:financial_data.KlineData.open_interest)
  return _internal_open_interest();
}
inline void KlineData::set_open_interest(::int64_t value) {
  _internal_set_open_interest(value);
  // @@protoc_insertion_point(field_set:financial_data.KlineData.open_interest)
}
inline ::int64_t KlineData::_internal_open_interest() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.open_interest_;
}
inline void KlineData::_internal_set_open_interest(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.open_interest_ = value;
}

// -------------------------------------------------------------------

// Level2Data

// int64 timestamp = 1;
inline void Level2Data::clear_timestamp() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.timestamp_ = ::int64_t{0};
}
inline ::int64_t Level2Data::timestamp() const {
  // @@protoc_insertion_point(field_get:financial_data.Level2Data.timestamp)
  return _internal_timestamp();
}
inline void Level2Data::set_timestamp(::int64_t value) {
  _internal_set_timestamp(value);
  // @@protoc_insertion_point(field_set:financial_data.Level2Data.timestamp)
}
inline ::int64_t Level2Data::_internal_timestamp() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.timestamp_;
}
inline void Level2Data::_internal_set_timestamp(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.timestamp_ = value;
}

// string symbol = 2;
inline void Level2Data::clear_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.ClearToEmpty();
}
inline const std::string& Level2Data::symbol() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.Level2Data.symbol)
  return _internal_symbol();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void Level2Data::set_symbol(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.Level2Data.symbol)
}
inline std::string* Level2Data::mutable_symbol() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_symbol();
  // @@protoc_insertion_point(field_mutable:financial_data.Level2Data.symbol)
  return _s;
}
inline const std::string& Level2Data::_internal_symbol() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbol_.Get();
}
inline void Level2Data::_internal_set_symbol(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(value, GetArena());
}
inline std::string* Level2Data::_internal_mutable_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.symbol_.Mutable( GetArena());
}
inline std::string* Level2Data::release_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.Level2Data.symbol)
  return _impl_.symbol_.Release();
}
inline void Level2Data::set_allocated_symbol(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.symbol_.IsDefault()) {
          _impl_.symbol_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.Level2Data.symbol)
}

// string exchange = 3;
inline void Level2Data::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& Level2Data::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.Level2Data.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void Level2Data::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.Level2Data.exchange)
}
inline std::string* Level2Data::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.Level2Data.exchange)
  return _s;
}
inline const std::string& Level2Data::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void Level2Data::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* Level2Data::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* Level2Data::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.Level2Data.exchange)
  return _impl_.exchange_.Release();
}
inline void Level2Data::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.Level2Data.exchange)
}

// repeated .financial_data.PriceLevel bids = 4;
inline int Level2Data::_internal_bids_size() const {
  return _internal_bids().size();
}
inline int Level2Data::bids_size() const {
  return _internal_bids_size();
}
inline void Level2Data::clear_bids() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.bids_.Clear();
}
inline ::financial_data::PriceLevel* Level2Data::mutable_bids(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.Level2Data.bids)
  return _internal_mutable_bids()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>* Level2Data::mutable_bids()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.Level2Data.bids)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_bids();
}
inline const ::financial_data::PriceLevel& Level2Data::bids(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.Level2Data.bids)
  return _internal_bids().Get(index);
}
inline ::financial_data::PriceLevel* Level2Data::add_bids() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::PriceLevel* _add = _internal_mutable_bids()->Add();
  // @@protoc_insertion_point(field_add:financial_data.Level2Data.bids)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>& Level2Data::bids() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.Level2Data.bids)
  return _internal_bids();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>&
Level2Data::_internal_bids() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.bids_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>*
Level2Data::_internal_mutable_bids() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.bids_;
}

// repeated .financial_data.PriceLevel asks = 5;
inline int Level2Data::_internal_asks_size() const {
  return _internal_asks().size();
}
inline int Level2Data::asks_size() const {
  return _internal_asks_size();
}
inline void Level2Data::clear_asks() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.asks_.Clear();
}
inline ::financial_data::PriceLevel* Level2Data::mutable_asks(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.Level2Data.asks)
  return _internal_mutable_asks()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>* Level2Data::mutable_asks()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.Level2Data.asks)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_asks();
}
inline const ::financial_data::PriceLevel& Level2Data::asks(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.Level2Data.asks)
  return _internal_asks().Get(index);
}
inline ::financial_data::PriceLevel* Level2Data::add_asks() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::PriceLevel* _add = _internal_mutable_asks()->Add();
  // @@protoc_insertion_point(field_add:financial_data.Level2Data.asks)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>& Level2Data::asks() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.Level2Data.asks)
  return _internal_asks();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>&
Level2Data::_internal_asks() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.asks_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::PriceLevel>*
Level2Data::_internal_mutable_asks() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.asks_;
}

// uint32 sequence = 6;
inline void Level2Data::clear_sequence() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.sequence_ = 0u;
}
inline ::uint32_t Level2Data::sequence() const {
  // @@protoc_insertion_point(field_get:financial_data.Level2Data.sequence)
  return _internal_sequence();
}
inline void Level2Data::set_sequence(::uint32_t value) {
  _internal_set_sequence(value);
  // @@protoc_insertion_point(field_set:financial_data.Level2Data.sequence)
}
inline ::uint32_t Level2Data::_internal_sequence() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.sequence_;
}
inline void Level2Data::_internal_set_sequence(::uint32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.sequence_ = value;
}

// -------------------------------------------------------------------

// PriceLevel

// double price = 1;
inline void PriceLevel::clear_price() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.price_ = 0;
}
inline double PriceLevel::price() const {
  // @@protoc_insertion_point(field_get:financial_data.PriceLevel.price)
  return _internal_price();
}
inline void PriceLevel::set_price(double value) {
  _internal_set_price(value);
  // @@protoc_insertion_point(field_set:financial_data.PriceLevel.price)
}
inline double PriceLevel::_internal_price() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.price_;
}
inline void PriceLevel::_internal_set_price(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.price_ = value;
}

// int32 volume = 2;
inline void PriceLevel::clear_volume() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.volume_ = 0;
}
inline ::int32_t PriceLevel::volume() const {
  // @@protoc_insertion_point(field_get:financial_data.PriceLevel.volume)
  return _internal_volume();
}
inline void PriceLevel::set_volume(::int32_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:financial_data.PriceLevel.volume)
}
inline ::int32_t PriceLevel::_internal_volume() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.volume_;
}
inline void PriceLevel::_internal_set_volume(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.volume_ = value;
}

// int32 order_count = 3;
inline void PriceLevel::clear_order_count() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.order_count_ = 0;
}
inline ::int32_t PriceLevel::order_count() const {
  // @@protoc_insertion_point(field_get:financial_data.PriceLevel.order_count)
  return _internal_order_count();
}
inline void PriceLevel::set_order_count(::int32_t value) {
  _internal_set_order_count(value);
  // @@protoc_insertion_point(field_set:financial_data.PriceLevel.order_count)
}
inline ::int32_t PriceLevel::_internal_order_count() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.order_count_;
}
inline void PriceLevel::_internal_set_order_count(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.order_count_ = value;
}

// -------------------------------------------------------------------

// ResponseMetadata

// int64 server_timestamp = 1;
inline void ResponseMetadata::clear_server_timestamp() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.server_timestamp_ = ::int64_t{0};
}
inline ::int64_t ResponseMetadata::server_timestamp() const {
  // @@protoc_insertion_point(field_get:financial_data.ResponseMetadata.server_timestamp)
  return _internal_server_timestamp();
}
inline void ResponseMetadata::set_server_timestamp(::int64_t value) {
  _internal_set_server_timestamp(value);
  // @@protoc_insertion_point(field_set:financial_data.ResponseMetadata.server_timestamp)
}
inline ::int64_t ResponseMetadata::_internal_server_timestamp() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.server_timestamp_;
}
inline void ResponseMetadata::_internal_set_server_timestamp(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.server_timestamp_ = value;
}

// string server_id = 2;
inline void ResponseMetadata::clear_server_id() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.server_id_.ClearToEmpty();
}
inline const std::string& ResponseMetadata::server_id() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ResponseMetadata.server_id)
  return _internal_server_id();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ResponseMetadata::set_server_id(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.server_id_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ResponseMetadata.server_id)
}
inline std::string* ResponseMetadata::mutable_server_id() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_server_id();
  // @@protoc_insertion_point(field_mutable:financial_data.ResponseMetadata.server_id)
  return _s;
}
inline const std::string& ResponseMetadata::_internal_server_id() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.server_id_.Get();
}
inline void ResponseMetadata::_internal_set_server_id(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.server_id_.Set(value, GetArena());
}
inline std::string* ResponseMetadata::_internal_mutable_server_id() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.server_id_.Mutable( GetArena());
}
inline std::string* ResponseMetadata::release_server_id() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ResponseMetadata.server_id)
  return _impl_.server_id_.Release();
}
inline void ResponseMetadata::set_allocated_server_id(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.server_id_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.server_id_.IsDefault()) {
          _impl_.server_id_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ResponseMetadata.server_id)
}

// int32 sequence_number = 3;
inline void ResponseMetadata::clear_sequence_number() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.sequence_number_ = 0;
}
inline ::int32_t ResponseMetadata::sequence_number() const {
  // @@protoc_insertion_point(field_get:financial_data.ResponseMetadata.sequence_number)
  return _internal_sequence_number();
}
inline void ResponseMetadata::set_sequence_number(::int32_t value) {
  _internal_set_sequence_number(value);
  // @@protoc_insertion_point(field_set:financial_data.ResponseMetadata.sequence_number)
}
inline ::int32_t ResponseMetadata::_internal_sequence_number() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.sequence_number_;
}
inline void ResponseMetadata::_internal_set_sequence_number(::int32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.sequence_number_ = value;
}

// double processing_latency_us = 4;
inline void ResponseMetadata::clear_processing_latency_us() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.processing_latency_us_ = 0;
}
inline double ResponseMetadata::processing_latency_us() const {
  // @@protoc_insertion_point(field_get:financial_data.ResponseMetadata.processing_latency_us)
  return _internal_processing_latency_us();
}
inline void ResponseMetadata::set_processing_latency_us(double value) {
  _internal_set_processing_latency_us(value);
  // @@protoc_insertion_point(field_set:financial_data.ResponseMetadata.processing_latency_us)
}
inline double ResponseMetadata::_internal_processing_latency_us() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.processing_latency_us_;
}
inline void ResponseMetadata::_internal_set_processing_latency_us(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.processing_latency_us_ = value;
}

#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)
}  // namespace financial_data


namespace google {
namespace protobuf {

template <>
struct is_proto_enum<::financial_data::HealthCheckResponse_ServingStatus> : std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor<::financial_data::HealthCheckResponse_ServingStatus>() {
  return ::financial_data::HealthCheckResponse_ServingStatus_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#include "google/protobuf/port_undef.inc"

#endif  // GOOGLE_PROTOBUF_INCLUDED_market_5fdata_5fservice_2eproto_2epb_2eh
