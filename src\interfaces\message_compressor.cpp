#include "message_compressor.h"
#include <chrono>
#include <random>
#include <sstream>
#include <iomanip>
#include <cstring>

namespace financial_data {
namespace interfaces {

// 线程本地存储定义
thread_local std::unique_ptr<MessageCompressor::ZlibStream> MessageCompressor::compression_stream_;
thread_local std::unique_ptr<MessageCompressor::ZlibStream> MessageCompressor::decompression_stream_;

// MessageCompressor类实现 - 消息压缩和批量推送优化
MessageCompressor::MessageCompressor(const CompressorConfig& config) 
    : config_(config), logger_(spdlog::get("message_compressor")) {
    if (!logger_) {
        logger_ = spdlog::default_logger();
    }
    
    logger_->info("MessageCompressor initialized with type: {}, level: {}, threshold: {} bytes",
                 GetCompressionTypeString(), config_.compression_level, config_.compression_threshold);
}

CompressionResult MessageCompressor::Compress(const std::string& message) {
    return Compress(message.data(), message.size());
}

CompressionResult MessageCompressor::Compress(const std::vector<uint8_t>& data) {
    return Compress(data.data(), data.size());
}

CompressionResult MessageCompressor::Compress(const void* data, size_t size) {
    auto start_time = std::chrono::high_resolution_clock::now();
    CompressionResult result;
    result.original_size = size;
    
    // 检查是否需要压缩
    if (!ShouldCompress(size)) {
        // 不压缩，直接复制数据
        result.compressed_data.resize(size);
        std::memcpy(result.compressed_data.data(), data, size);
        result.compressed_size = size;
        result.compression_ratio = 1.0;
        result.success = true;
        
        statistics_.total_messages++;
        statistics_.total_original_bytes += size;
        statistics_.total_compressed_bytes += size;
        return result;
    }
    
    // 检查数据大小限制
    if (size > config_.max_message_size) {
        result.error_message = "Message size exceeds maximum limit: " + std::to_string(size);
        RecordCompressionStats(size, 0, 0, false);
        return result;
    }
    
    // 根据压缩类型执行压缩
    switch (config_.type) {
        case CompressionType::DEFLATE:
            result = CompressDeflate(data, size);
            break;
        case CompressionType::GZIP:
            result = CompressGzip(data, size);
            break;
        case CompressionType::NONE:
            // 不压缩
            result.compressed_data.resize(size);
            std::memcpy(result.compressed_data.data(), data, size);
            result.compressed_size = size;
            result.compression_ratio = 1.0;
            result.success = true;
            break;
        default:
            result.error_message = "Unsupported compression type";
            break;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto compression_time = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    
    RecordCompressionStats(size, result.compressed_size, compression_time, result.success);
    
    return result;
}

DecompressionResult MessageCompressor::Decompress(const std::vector<uint8_t>& compressed_data) {
    return Decompress(compressed_data.data(), compressed_data.size());
}

DecompressionResult MessageCompressor::Decompress(const void* compressed_data, size_t size) {
    auto start_time = std::chrono::high_resolution_clock::now();
    DecompressionResult result;
    result.compressed_size = size;
    
    // 根据压缩类型执行解压缩
    switch (config_.type) {
        case CompressionType::DEFLATE:
            result = DecompressDeflate(compressed_data, size);
            break;
        case CompressionType::GZIP:
            result = DecompressGzip(compressed_data, size);
            break;
        case CompressionType::NONE:
            // 不解压缩，直接复制数据
            result.decompressed_data.resize(size);
            std::memcpy(&result.decompressed_data[0], compressed_data, size);
            result.decompressed_size = size;
            result.success = true;
            break;
        default:
            result.error_message = "Unsupported compression type";
            break;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto decompression_time = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    
    RecordDecompressionStats(size, result.decompressed_size, decompression_time, result.success);
    
    return result;
}

bool MessageCompressor::ShouldCompress(size_t message_size) const {
    return config_.type != CompressionType::NONE && message_size >= config_.compression_threshold;
}

std::string MessageCompressor::GetCompressionTypeString() const {
    switch (config_.type) {
        case CompressionType::NONE: return "NONE";
        case CompressionType::DEFLATE: return "DEFLATE";
        case CompressionType::GZIP: return "GZIP";
        default: return "UNKNOWN";
    }
}

bool MessageCompressor::UpdateConfig(const CompressorConfig& config) {
    config_ = config;
    
    // 清理现有的流对象，强制重新初始化
    compression_stream_.reset();
    decompression_stream_.reset();
    
    logger_->info("MessageCompressor config updated: type={}, level={}, threshold={}",
                 GetCompressionTypeString(), config_.compression_level, config_.compression_threshold);
    
    return true;
}

void MessageCompressor::ResetStatistics() {
    statistics_.Reset();
}

std::string MessageCompressor::GetStatisticsSummary() const {
    std::ostringstream oss;
    auto stats = statistics_;
    
    oss << "MessageCompressor Statistics:\n";
    oss << "  Total Messages: " << stats.total_messages.load() << "\n";
    oss << "  Compressed Messages: " << stats.compressed_messages.load() << "\n";
    oss << "  Compression Rate: " << std::fixed << std::setprecision(2) 
        << (stats.GetCompressionRate() * 100) << "%\n";
    oss << "  Overall Compression Ratio: " << std::fixed << std::setprecision(3) 
        << stats.GetOverallCompressionRatio() << "\n";
    oss << "  Total Original Bytes: " << stats.total_original_bytes.load() << "\n";
    oss << "  Total Compressed Bytes: " << stats.total_compressed_bytes.load() << "\n";
    oss << "  Total Saved Bytes: " << stats.GetTotalSavedBytes() << "\n";
    oss << "  Average Compression Time: " << std::fixed << std::setprecision(3) 
        << stats.GetAverageCompressionTimeMs() << " ms\n";
    oss << "  Average Decompression Time: " << std::fixed << std::setprecision(3) 
        << stats.GetAverageDecompressionTimeMs() << " ms\n";
    oss << "  Compression Errors: " << stats.compression_errors.load() << "\n";
    oss << "  Decompression Errors: " << stats.decompression_errors.load() << "\n";
    
    return oss.str();
}

MessageCompressor::PerformanceTestResult MessageCompressor::RunPerformanceTest(size_t test_data_size, size_t iterations) {
    PerformanceTestResult result;
    result.success = false;
    
    try {
        auto test_data = GenerateTestData(test_data_size);
        auto start_time = std::chrono::high_resolution_clock::now();
        
        std::vector<CompressionResult> compression_results;
        compression_results.reserve(iterations);
        
        // 压缩测试
        auto compression_start = std::chrono::high_resolution_clock::now();
        for (size_t i = 0; i < iterations; ++i) {
            auto comp_result = Compress(test_data);
            if (!comp_result.success) {
                result.error_message = "Compression failed: " + comp_result.error_message;
                return result;
            }
            compression_results.push_back(std::move(comp_result));
        }
        auto compression_end = std::chrono::high_resolution_clock::now();
        
        // 解压缩测试
        auto decompression_start = std::chrono::high_resolution_clock::now();
        for (const auto& comp_result : compression_results) {
            auto decomp_result = Decompress(comp_result.compressed_data);
            if (!decomp_result.success) {
                result.error_message = "Decompression failed: " + decomp_result.error_message;
                return result;
            }
        }
        auto decompression_end = std::chrono::high_resolution_clock::now();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        
        // 计算性能指标
        auto compression_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            compression_end - compression_start).count();
        auto decompression_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            decompression_end - decompression_start).count();
        
        result.total_test_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        double total_mb = (test_data_size * iterations) / (1024.0 * 1024.0);
        result.compression_throughput_mbps = total_mb / (compression_time_ms / 1000.0);
        result.decompression_throughput_mbps = total_mb / (decompression_time_ms / 1000.0);
        
        // 计算平均压缩比
        double total_ratio = 0.0;
        for (const auto& comp_result : compression_results) {
            total_ratio += comp_result.GetCompressionRatio();
        }
        result.average_compression_ratio = total_ratio / iterations;
        
        result.success = true;
        
    } catch (const std::exception& e) {
        result.error_message = "Performance test exception: " + std::string(e.what());
    }
    
    return result;
}

bool MessageCompressor::ValidateCompressor(std::string& error_message) {
    try {
        // 测试不同大小的数据
        std::vector<size_t> test_sizes = {100, 1024, 10240, 102400};
        
        for (size_t size : test_sizes) {
            auto test_data = GenerateTestData(size);
            
            // 压缩
            auto comp_result = Compress(test_data);
            if (!comp_result.success) {
                error_message = "Compression validation failed for size " + std::to_string(size) + 
                               ": " + comp_result.error_message;
                return false;
            }
            
            // 解压缩
            auto decomp_result = Decompress(comp_result.compressed_data);
            if (!decomp_result.success) {
                error_message = "Decompression validation failed for size " + std::to_string(size) + 
                               ": " + decomp_result.error_message;
                return false;
            }
            
            // 验证数据一致性
            if (decomp_result.decompressed_data.size() != size) {
                error_message = "Size mismatch after decompression for size " + std::to_string(size);
                return false;
            }
            
            if (std::memcmp(test_data.data(), decomp_result.decompressed_data.data(), size) != 0) {
                error_message = "Data mismatch after decompression for size " + std::to_string(size);
                return false;
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        error_message = "Validation exception: " + std::string(e.what());
        return false;
    }
}

std::unique_ptr<MessageCompressor> MessageCompressor::Create(CompressionType type, int level) {
    CompressorConfig config;
    config.type = type;
    config.compression_level = level;
    return std::make_unique<MessageCompressor>(config);
}

bool MessageCompressor::InitializeCompressionStream(ZlibStream& stream) {
    if (stream.initialized) {
        deflateReset(&stream.stream);
        return true;
    }
    
    stream.stream.zalloc = Z_NULL;
    stream.stream.zfree = Z_NULL;
    stream.stream.opaque = Z_NULL;
    
    int window_bits = config_.window_bits;
    if (config_.type == CompressionType::GZIP) {
        window_bits += 16;  // 添加gzip头
    }
    
    int ret = deflateInit2(&stream.stream, config_.compression_level, Z_DEFLATED,
                          window_bits, config_.mem_level, config_.strategy);
    
    if (ret != Z_OK) {
        logger_->error("Failed to initialize compression stream: {}", GetZlibError(ret));
        return false;
    }
    
    stream.initialized = true;
    stream.type = config_.type;
    return true;
}

bool MessageCompressor::InitializeDecompressionStream(ZlibStream& stream) {
    if (stream.initialized) {
        inflateReset(&stream.stream);
        return true;
    }
    
    stream.stream.zalloc = Z_NULL;
    stream.stream.zfree = Z_NULL;
    stream.stream.opaque = Z_NULL;
    stream.stream.avail_in = 0;
    stream.stream.next_in = Z_NULL;
    
    int window_bits = config_.window_bits;
    if (config_.type == CompressionType::GZIP) {
        window_bits += 16;  // 支持gzip头
    }
    
    int ret = inflateInit2(&stream.stream, window_bits);
    
    if (ret != Z_OK) {
        logger_->error("Failed to initialize decompression stream: {}", GetZlibError(ret));
        return false;
    }
    
    stream.initialized = true;
    stream.type = config_.type;
    return true;
}

CompressionResult MessageCompressor::CompressDeflate(const void* data, size_t size) {
    CompressionResult result;
    result.original_size = size;
    
    // 获取或创建压缩流
    if (!compression_stream_) {
        compression_stream_ = std::make_unique<ZlibStream>();
    }
    
    if (!InitializeCompressionStream(*compression_stream_)) {
        result.error_message = "Failed to initialize compression stream";
        return result;
    }
    
    // 估算输出缓冲区大小
    size_t output_size = deflateBound(&compression_stream_->stream, size);
    result.compressed_data.resize(output_size);
    
    // 设置输入
    compression_stream_->stream.avail_in = size;
    compression_stream_->stream.next_in = const_cast<Bytef*>(static_cast<const Bytef*>(data));
    
    // 设置输出
    compression_stream_->stream.avail_out = output_size;
    compression_stream_->stream.next_out = result.compressed_data.data();
    
    // 执行压缩
    int ret = deflate(&compression_stream_->stream, Z_FINISH);
    
    if (ret != Z_STREAM_END) {
        result.error_message = "Compression failed: " + GetZlibError(ret);
        return result;
    }
    
    // 调整输出大小
    result.compressed_size = output_size - compression_stream_->stream.avail_out;
    result.compressed_data.resize(result.compressed_size);
    result.compression_ratio = static_cast<double>(result.compressed_size) / size;
    result.success = true;
    
    return result;
}

CompressionResult MessageCompressor::CompressGzip(const void* data, size_t size) {
    // GZIP压缩与DEFLATE类似，但使用不同的窗口位设置
    return CompressDeflate(data, size);
}

DecompressionResult MessageCompressor::DecompressDeflate(const void* compressed_data, size_t size) {
    DecompressionResult result;
    result.compressed_size = size;
    
    // 获取或创建解压缩流
    if (!decompression_stream_) {
        decompression_stream_ = std::make_unique<ZlibStream>();
    }
    
    if (!InitializeDecompressionStream(*decompression_stream_)) {
        result.error_message = "Failed to initialize decompression stream";
        return result;
    }
    
    // 估算输出缓冲区大小（通常是输入的2-4倍）
    size_t output_size = size * 4;
    result.decompressed_data.resize(output_size);
    
    // 设置输入
    decompression_stream_->stream.avail_in = size;
    decompression_stream_->stream.next_in = const_cast<Bytef*>(static_cast<const Bytef*>(compressed_data));
    
    // 设置输出
    decompression_stream_->stream.avail_out = output_size;
    decompression_stream_->stream.next_out = reinterpret_cast<Bytef*>(&result.decompressed_data[0]);
    
    // 执行解压缩
    int ret = inflate(&decompression_stream_->stream, Z_FINISH);
    
    if (ret != Z_STREAM_END) {
        // 如果缓冲区不够大，尝试扩展
        if (ret == Z_BUF_ERROR && decompression_stream_->stream.avail_out == 0) {
            size_t current_size = output_size;
            output_size *= 2;
            result.decompressed_data.resize(output_size);
            
            decompression_stream_->stream.avail_out = output_size - current_size;
            decompression_stream_->stream.next_out = reinterpret_cast<Bytef*>(&result.decompressed_data[current_size]);
            
            ret = inflate(&decompression_stream_->stream, Z_FINISH);
        }
        
        if (ret != Z_STREAM_END) {
            result.error_message = "Decompression failed: " + GetZlibError(ret);
            return result;
        }
    }
    
    // 调整输出大小
    result.decompressed_size = output_size - decompression_stream_->stream.avail_out;
    result.decompressed_data.resize(result.decompressed_size);
    result.success = true;
    
    return result;
}

DecompressionResult MessageCompressor::DecompressGzip(const void* compressed_data, size_t size) {
    // GZIP解压缩与DEFLATE类似
    return DecompressDeflate(compressed_data, size);
}

std::string MessageCompressor::GetZlibError(int error_code) const {
    switch (error_code) {
        case Z_OK: return "Z_OK";
        case Z_STREAM_END: return "Z_STREAM_END";
        case Z_NEED_DICT: return "Z_NEED_DICT";
        case Z_ERRNO: return "Z_ERRNO";
        case Z_STREAM_ERROR: return "Z_STREAM_ERROR";
        case Z_DATA_ERROR: return "Z_DATA_ERROR";
        case Z_MEM_ERROR: return "Z_MEM_ERROR";
        case Z_BUF_ERROR: return "Z_BUF_ERROR";
        case Z_VERSION_ERROR: return "Z_VERSION_ERROR";
        default: return "Unknown error: " + std::to_string(error_code);
    }
}

void MessageCompressor::RecordCompressionStats(size_t original_size, size_t compressed_size, 
                                              uint64_t compression_time_ns, bool success) {
    if (!config_.enable_statistics) return;
    
    statistics_.total_messages++;
    statistics_.total_original_bytes += original_size;
    
    if (success) {
        statistics_.compressed_messages++;
        statistics_.total_compressed_bytes += compressed_size;
        statistics_.total_compression_time_ns += compression_time_ns;
    } else {
        statistics_.compression_errors++;
    }
}

void MessageCompressor::RecordDecompressionStats(size_t compressed_size, size_t decompressed_size, 
                                                uint64_t decompression_time_ns, bool success) {
    if (!config_.enable_statistics) return;
    
    if (success) {
        statistics_.total_decompression_time_ns += decompression_time_ns;
    } else {
        statistics_.decompression_errors++;
    }
}

std::vector<uint8_t> MessageCompressor::GenerateTestData(size_t size) const {
    std::vector<uint8_t> data(size);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint8_t> dis(0, 255);
    
    for (size_t i = 0; i < size; ++i) {
        data[i] = dis(gen);
    }
    
    return data;
}

// CompressorFactory实现
std::unique_ptr<MessageCompressor> CompressorFactory::CreateDefault() {
    return std::make_unique<MessageCompressor>();
}

std::unique_ptr<MessageCompressor> CompressorFactory::CreateHighCompression() {
    CompressorConfig config;
    config.type = CompressionType::GZIP;
    config.compression_level = Z_BEST_COMPRESSION;
    config.compression_threshold = 512;
    return std::make_unique<MessageCompressor>(config);
}

std::unique_ptr<MessageCompressor> CompressorFactory::CreateFastCompression() {
    CompressorConfig config;
    config.type = CompressionType::DEFLATE;
    config.compression_level = Z_BEST_SPEED;
    config.compression_threshold = 2048;
    return std::make_unique<MessageCompressor>(config);
}

std::unique_ptr<MessageCompressor> CompressorFactory::CreateBalanced() {
    CompressorConfig config;
    config.type = CompressionType::DEFLATE;
    config.compression_level = Z_DEFAULT_COMPRESSION;
    config.compression_threshold = 1024;
    return std::make_unique<MessageCompressor>(config);
}

std::unique_ptr<MessageCompressor> CompressorFactory::CreateFromConfig(const CompressorConfig& config) {
    return std::make_unique<MessageCompressor>(config);
}

} // namespace interfaces
} // namespace financial_data