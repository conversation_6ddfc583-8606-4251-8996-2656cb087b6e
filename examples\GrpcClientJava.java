import io.grpc.*;
import io.grpc.stub.StreamObserver;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * High-performance Java gRPC client for Financial Data Service
 * Demonstrates streaming data consumption with flow control, load balancing, and error handling
 */
public class GrpcClientJava {
    
    private static final Logger logger = Logger.getLogger(GrpcClientJava.class.getName());
    
    // Mock protobuf classes (replace with actual generated classes)
    public static class TickDataRequest {
        private List<String> symbols;
        private String exchange;
        private int bufferSize;
        
        public TickDataRequest(List<String> symbols, String exchange, int bufferSize) {
            this.symbols = symbols;
            this.exchange = exchange;
            this.bufferSize = bufferSize;
        }
        
        public List<String> getSymbols() { return symbols; }
        public String getExchange() { return exchange; }
        public int getBufferSize() { return bufferSize; }
    }
    
    public static class TickData {
        private long timestamp;
        private String symbol;
        private String exchange;
        private double lastPrice;
        private long volume;
        private double turnover;
        private List<PriceLevel> bids;
        private List<PriceLevel> asks;
        private int sequence;
        
        // Constructors and getters
        public TickData(long timestamp, String symbol, String exchange, double lastPrice, 
                       long volume, double turnover, int sequence) {
            this.timestamp = timestamp;
            this.symbol = symbol;
            this.exchange = exchange;
            this.lastPrice = lastPrice;
            this.volume = volume;
            this.turnover = turnover;
            this.sequence = sequence;
            this.bids = new ArrayList<>();
            this.asks = new ArrayList<>();
        }
        
        // Getters
        public long getTimestamp() { return timestamp; }
        public String getSymbol() { return symbol; }
        public String getExchange() { return exchange; }
        public double getLastPrice() { return lastPrice; }
        public long getVolume() { return volume; }
        public double getTurnover() { return turnover; }
        public List<PriceLevel> getBids() { return bids; }
        public List<PriceLevel> getAsks() { return asks; }
        public int getSequence() { return sequence; }
    }
    
    public static class PriceLevel {
        private double price;
        private int volume;
        
        public PriceLevel(double price, int volume) {
            this.price = price;
            this.volume = volume;
        }
        
        public double getPrice() { return price; }
        public int getVolume() { return volume; }
    }
    
    public static class TickDataResponse {
        private List<TickData> ticks;
        private boolean hasMore;
        private ResponseMetadata metadata;
        
        public TickDataResponse(List<TickData> ticks, boolean hasMore, ResponseMetadata metadata) {
            this.ticks = ticks;
            this.hasMore = hasMore;
            this.metadata = metadata;
        }
        
        public List<TickData> getTicks() { return ticks; }
        public boolean hasMore() { return hasMore; }
        public ResponseMetadata getMetadata() { return metadata; }
    }
    
    public static class ResponseMetadata {
        private long serverTimestamp;
        private String serverId;
        private int sequenceNumber;
        private double processingLatencyUs;
        
        public ResponseMetadata(long serverTimestamp, String serverId, int sequenceNumber, double processingLatencyUs) {
            this.serverTimestamp = serverTimestamp;
            this.serverId = serverId;
            this.sequenceNumber = sequenceNumber;
            this.processingLatencyUs = processingLatencyUs;
        }
        
        public long getServerTimestamp() { return serverTimestamp; }
        public String getServerId() { return serverId; }
        public int getSequenceNumber() { return sequenceNumber; }
        public double getProcessingLatencyUs() { return processingLatencyUs; }
    }
    
    // Mock gRPC service interface
    public interface MarketDataServiceGrpc {
        public interface MarketDataServiceStub {
            void streamTickData(TickDataRequest request, StreamObserver<TickDataResponse> responseObserver);
        }
        
        public static MarketDataServiceStub newStub(ManagedChannel channel) {
            return new MarketDataServiceStub() {
                @Override
                public void streamTickData(TickDataRequest request, StreamObserver<TickDataResponse> responseObserver) {
                    // Mock implementation - would use actual gRPC stub
                }
            };
        }
    }
    
    /**
     * High-performance gRPC client with load balancing and failover
     */
    public static class GrpcClient {
        private final List<String> serverAddresses;
        private final List<ManagedChannel> channels;
        private final List<MarketDataServiceGrpc.MarketDataServiceStub> stubs;
        private final AtomicInteger currentServerIndex;
        private final int maxRetries;
        private final ExecutorService executorService;
        
        public GrpcClient(List<String> serverAddresses, int maxRetries) {
            this.serverAddresses = serverAddresses;
            this.maxRetries = maxRetries;
            this.currentServerIndex = new AtomicInteger(0);
            this.channels = new ArrayList<>();
            this.stubs = new ArrayList<>();
            this.executorService = Executors.newCachedThreadPool();
            
            initializeConnections();
        }
        
        private void initializeConnections() {
            for (String address : serverAddresses) {
                try {
                    ManagedChannel channel = ManagedChannelBuilder.forTarget(address)
                        .usePlaintext()
                        .keepAliveTime(30, TimeUnit.SECONDS)
                        .keepAliveTimeout(5, TimeUnit.SECONDS)
                        .keepAliveWithoutCalls(true)
                        .maxInboundMessageSize(4 * 1024 * 1024) // 4MB
                        .maxInboundMetadataSize(8 * 1024)       // 8KB
                        .build();
                    
                    MarketDataServiceGrpc.MarketDataServiceStub stub = MarketDataServiceGrpc.newStub(channel);
                    
                    channels.add(channel);
                    stubs.add(stub);
                    
                    logger.info("Connected to server: " + address);
                    
                } catch (Exception e) {
                    logger.severe("Failed to connect to " + address + ": " + e.getMessage());
                }
            }
        }
        
        private MarketDataServiceGrpc.MarketDataServiceStub getCurrentStub() throws Exception {
            for (int attempt = 0; attempt < serverAddresses.size(); attempt++) {
                int index = currentServerIndex.get();
                try {
                    MarketDataServiceGrpc.MarketDataServiceStub stub = stubs.get(index);
                    // Test connection health (simplified)
                    return stub;
                } catch (Exception e) {
                    logger.warning("Server " + index + " unavailable: " + e.getMessage());
                    currentServerIndex.compareAndSet(index, (index + 1) % serverAddresses.size());
                }
            }
            throw new Exception("All servers unavailable");
        }
        
        public void streamTickData(List<String> symbols, String exchange, int bufferSize, 
                                 TickDataHandler handler) throws Exception {
            
            TickDataRequest request = new TickDataRequest(symbols, exchange, bufferSize);
            
            int retryCount = 0;
            while (retryCount < maxRetries) {
                try {
                    MarketDataServiceGrpc.MarketDataServiceStub stub = getCurrentStub();
                    
                    logger.info("Starting tick data stream for symbols: " + symbols);
                    
                    CountDownLatch streamLatch = new CountDownLatch(1);
                    AtomicInteger messageCount = new AtomicInteger(0);
                    
                    StreamObserver<TickDataResponse> responseObserver = new StreamObserver<TickDataResponse>() {
                        @Override
                        public void onNext(TickDataResponse response) {
                            try {
                                for (TickData tick : response.getTicks()) {
                                    handler.onTickData(tick);
                                    messageCount.incrementAndGet();
                                }
                            } catch (Exception e) {
                                logger.severe("Error processing tick data: " + e.getMessage());
                                onError(e);
                            }
                        }
                        
                        @Override
                        public void onError(Throwable t) {
                            logger.severe("Stream error: " + t.getMessage());
                            streamLatch.countDown();
                        }
                        
                        @Override
                        public void onCompleted() {
                            logger.info("Stream completed. Total messages: " + messageCount.get());
                            streamLatch.countDown();
                        }
                    };
                    
                    stub.streamTickData(request, responseObserver);
                    
                    // Wait for stream to complete or timeout
                    if (streamLatch.await(5, TimeUnit.MINUTES)) {
                        break; // Success
                    } else {
                        throw new Exception("Stream timeout");
                    }
                    
                } catch (Exception e) {
                    retryCount++;
                    logger.severe("Stream error (attempt " + retryCount + "): " + e.getMessage());
                    
                    if (retryCount < maxRetries) {
                        // Switch to next server
                        int current = currentServerIndex.get();
                        currentServerIndex.compareAndSet(current, (current + 1) % serverAddresses.size());
                        
                        // Brief delay before retry
                        Thread.sleep(1000 * retryCount);
                    }
                }
            }
            
            if (retryCount >= maxRetries) {
                throw new Exception("Failed to establish stream after " + maxRetries + " retries");
            }
        }
        
        public void close() {
            executorService.shutdown();
            for (ManagedChannel channel : channels) {
                channel.shutdown();
                try {
                    if (!channel.awaitTermination(5, TimeUnit.SECONDS)) {
                        channel.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    channel.shutdownNow();
                }
            }
            logger.info("All gRPC channels closed");
        }
    }
    
    /**
     * Interface for handling tick data
     */
    public interface TickDataHandler {
        void onTickData(TickData tick) throws Exception;
    }
    
    /**
     * Flow-controlled consumer with backpressure handling
     */
    public static class FlowControlledConsumer {
        private final GrpcClient client;
        private final int bufferSize;
        private final BlockingQueue<TickData> messageQueue;
        private final ProcessingStats stats;
        private final ExecutorService processingExecutor;
        
        public FlowControlledConsumer(GrpcClient client, int bufferSize) {
            this.client = client;
            this.bufferSize = bufferSize;
            this.messageQueue = new ArrayBlockingQueue<>(bufferSize);
            this.stats = new ProcessingStats();
            this.processingExecutor = Executors.newSingleThreadExecutor();
        }
        
        public void consumeTickData(List<String> symbols, String exchange, 
                                  TickDataHandler messageHandler) throws Exception {
            
            // Start consumer thread
            Future<?> consumerFuture = processingExecutor.submit(() -> {
                try {
                    while (!Thread.currentThread().isInterrupted()) {
                        TickData tick = messageQueue.poll(30, TimeUnit.SECONDS);
                        if (tick == null) {
                            logger.warning("Consumer timeout, no messages received");
                            break;
                        }
                        
                        long startTime = System.nanoTime();
                        
                        try {
                            messageHandler.onTickData(tick);
                            stats.messagesProcessed.incrementAndGet();
                            
                            // Update average latency
                            long processingTime = (System.nanoTime() - startTime) / 1000; // microseconds
                            stats.updateLatency(processingTime);
                            
                        } catch (Exception e) {
                            stats.processingErrors.incrementAndGet();
                            logger.severe("Error processing tick: " + e.getMessage());
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
            
            // Start producer (gRPC stream)
            TickDataHandler queueHandler = tick -> {
                if (!messageQueue.offer(tick)) {
                    logger.warning("Message queue full, dropping tick data");
                } else {
                    stats.messagesReceived.incrementAndGet();
                }
            };
            
            try {
                client.streamTickData(symbols, exchange, bufferSize, queueHandler);
            } finally {
                processingExecutor.shutdown();
                consumerFuture.cancel(true);
            }
        }
        
        public ProcessingStats getStats() {
            return stats;
        }
    }
    
    /**
     * Processing statistics
     */
    public static class ProcessingStats {
        private final AtomicLong messagesReceived = new AtomicLong(0);
        private final AtomicLong messagesProcessed = new AtomicLong(0);
        private final AtomicLong processingErrors = new AtomicLong(0);
        private volatile double avgLatencyUs = 0.0;
        private final long startTime = System.currentTimeMillis();
        
        public synchronized void updateLatency(long latencyUs) {
            long processed = messagesProcessed.get();
            if (processed == 1) {
                avgLatencyUs = latencyUs;
            } else {
                avgLatencyUs = (avgLatencyUs * (processed - 1) + latencyUs) / processed;
            }
        }
        
        public long getMessagesReceived() { return messagesReceived.get(); }
        public long getMessagesProcessed() { return messagesProcessed.get(); }
        public long getProcessingErrors() { return processingErrors.get(); }
        public double getAvgLatencyUs() { return avgLatencyUs; }
        public long getDurationMs() { return System.currentTimeMillis() - startTime; }
        
        @Override
        public String toString() {
            long duration = getDurationMs();
            double throughput = duration > 0 ? (double) messagesProcessed.get() * 1000 / duration : 0;
            
            return String.format(
                "Stats: received=%d, processed=%d, errors=%d, avgLatency=%.2fμs, throughput=%.2f msg/s",
                messagesReceived.get(), messagesProcessed.get(), processingErrors.get(),
                avgLatencyUs, throughput
            );
        }
    }
    
    /**
     * Example usage
     */
    public static void main(String[] args) {
        // Configure multiple server addresses for load balancing
        List<String> serverAddresses = Arrays.asList(
            "localhost:50051",
            "localhost:50052",
            "localhost:50053"
        );
        
        // Create client with failover support
        GrpcClient client = new GrpcClient(serverAddresses, 3);
        
        try {
            // Create flow-controlled consumer
            FlowControlledConsumer consumer = new FlowControlledConsumer(client, 1000);
            
            // Define tick data handler
            TickDataHandler tickHandler = tick -> {
                System.out.printf("Processing tick: %s @ %.4f (volume: %d, sequence: %d)%n",
                    tick.getSymbol(), tick.getLastPrice(), tick.getVolume(), tick.getSequence());
                
                // Add your custom processing logic here
                // e.g., store to database, trigger alerts, etc.
            };
            
            // Start consuming tick data
            List<String> symbols = Arrays.asList("BTCUSDT", "ETHUSDT", "ADAUSDT");
            String exchange = "binance";
            
            System.out.println("Starting to consume tick data for " + symbols + " from " + exchange);
            
            // Run consumer
            consumer.consumeTickData(symbols, exchange, tickHandler);
            
            // Print final statistics
            ProcessingStats stats = consumer.getStats();
            System.out.println("Processing completed. " + stats);
            
        } catch (Exception e) {
            logger.severe("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            client.close();
        }
    } 