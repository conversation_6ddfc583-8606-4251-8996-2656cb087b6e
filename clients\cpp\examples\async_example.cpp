#include "financial_data_sdk.h"
#include <iostream>
#include <future>
#include <vector>
#include <chrono>

using namespace financial_data::sdk;

int main() {
    std::cout << "Financial Data SDK - Async Example" << std::endl;
    std::cout << "===================================" << std::endl;

    // Configure connection
    ConnectionConfig config;
    config.server_address = "localhost:50051";
    config.connect_timeout = std::chrono::milliseconds(5000);
    config.request_timeout = std::chrono::milliseconds(10000);

    // Create SDK instance
    FinancialDataSDK sdk(config);

    try {
        // Connect to server
        std::cout << "Connecting to server..." << std::endl;
        if (!sdk.Connect()) {
            std::cerr << "Failed to connect to server" << std::endl;
            return 1;
        }

        std::cout << "Connected successfully!" << std::endl;

        // Demonstrate async operations
        std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409", "AU2412", "AG2412"};
        
        std::cout << "Starting async requests for " << symbols.size() << " symbols..." << std::endl;

        // Start multiple async requests
        std::vector<std::future<std::vector<StandardTick>>> futures;
        auto start_time = std::chrono::high_resolution_clock::now();

        for (const auto& symbol : symbols) {
            auto future = sdk.GetLatestTicksAsync({symbol}, "SHFE");
            futures.push_back(std::move(future));
        }

        // Also start some historical data requests
        auto now = std::chrono::system_clock::now();
        auto one_hour_ago = now - std::chrono::hours(1);
        
        int64_t start_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            one_hour_ago.time_since_epoch()).count();
        int64_t end_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            now.time_since_epoch()).count();

        std::vector<std::future<std::vector<StandardTick>>> historical_futures;
        for (const auto& symbol : symbols) {
            auto future = sdk.GetHistoricalTicksAsync(symbol, "SHFE", 
                                                    start_timestamp, end_timestamp, 50);
            historical_futures.push_back(std::move(future));
        }

        // Wait for all latest tick requests to complete
        std::cout << "Waiting for latest tick requests..." << std::endl;
        std::vector<std::vector<StandardTick>> latest_results;
        
        for (size_t i = 0; i < futures.size(); ++i) {
            try {
                // Wait with timeout
                auto status = futures[i].wait_for(std::chrono::seconds(10));
                if (status == std::future_status::ready) {
                    auto result = futures[i].get();
                    latest_results.push_back(result);
                    std::cout << "Received " << result.size() << " ticks for " 
                              << symbols[i] << std::endl;
                } else {
                    std::cout << "Timeout waiting for " << symbols[i] << std::endl;
                }
            } catch (const std::exception& e) {
                std::cout << "Error getting data for " << symbols[i] 
                          << ": " << e.what() << std::endl;
            }
        }

        // Wait for historical data requests
        std::cout << "\nWaiting for historical data requests..." << std::endl;
        std::vector<std::vector<StandardTick>> historical_results;
        
        for (size_t i = 0; i < historical_futures.size(); ++i) {
            try {
                auto status = historical_futures[i].wait_for(std::chrono::seconds(15));
                if (status == std::future_status::ready) {
                    auto result = historical_futures[i].get();
                    historical_results.push_back(result);
                    std::cout << "Received " << result.size() << " historical ticks for " 
                              << symbols[i] << std::endl;
                } else {
                    std::cout << "Timeout waiting for historical data for " 
                              << symbols[i] << std::endl;
                }
            } catch (const std::exception& e) {
                std::cout << "Error getting historical data for " << symbols[i] 
                          << ": " << e.what() << std::endl;
            }
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto total_time = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time);

        std::cout << "\nAll async operations completed in " 
                  << total_time.count() << " ms" << std::endl;

        // Display some results
        std::cout << "\n=== Latest Tick Results ===" << std::endl;
        for (size_t i = 0; i < latest_results.size() && i < symbols.size(); ++i) {
            if (!latest_results[i].empty()) {
                const auto& tick = latest_results[i][0];
                std::cout << symbols[i] << ": Price=" << tick.last_price 
                          << ", Volume=" << tick.volume 
                          << ", Timestamp=" << tick.timestamp_ns << std::endl;
            }
        }

        std::cout << "\n=== Historical Data Summary ===" << std::endl;
        for (size_t i = 0; i < historical_results.size() && i < symbols.size(); ++i) {
            if (!historical_results[i].empty()) {
                const auto& first_tick = historical_results[i].front();
                const auto& last_tick = historical_results[i].back();
                
                std::cout << symbols[i] << ": " << historical_results[i].size() 
                          << " ticks, Price range: " << first_tick.last_price 
                          << " - " << last_tick.last_price << std::endl;
            }
        }

        // Demonstrate concurrent streaming with async operations
        std::cout << "\n=== Concurrent Streaming Test ===" << std::endl;
        
        std::atomic<int> stream_tick_count{0};
        auto stream_callback = [&stream_tick_count](const StandardTick& tick) {
            stream_tick_count++;
            if (stream_tick_count % 50 == 0) {
                std::cout << "Stream received " << stream_tick_count 
                          << " ticks (latest: " << tick.symbol << ")" << std::endl;
            }
        };

        // Start streaming
        SubscriptionConfig sub_config;
        sub_config.symbols = {"CU2409", "AL2409"};
        sub_config.exchange = "SHFE";
        sub_config.buffer_size = 500;

        sdk.SubscribeTickData(sub_config, stream_callback);
        std::cout << "Started streaming for concurrent test..." << std::endl;

        // While streaming, perform async queries
        std::vector<std::future<std::vector<StandardTick>>> concurrent_futures;
        for (int i = 0; i < 5; ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            
            auto future = sdk.GetLatestTicksAsync({"ZN2409"}, "SHFE");
            concurrent_futures.push_back(std::move(future));
            std::cout << "Started async query #" << (i + 1) << std::endl;
        }

        // Wait for concurrent queries
        for (size_t i = 0; i < concurrent_futures.size(); ++i) {
            try {
                auto result = concurrent_futures[i].get();
                std::cout << "Concurrent query #" << (i + 1) << " completed: " 
                          << result.size() << " ticks" << std::endl;
            } catch (const std::exception& e) {
                std::cout << "Concurrent query #" << (i + 1) << " failed: " 
                          << e.what() << std::endl;
            }
        }

        std::cout << "Total stream ticks received during concurrent test: " 
                  << stream_tick_count.load() << std::endl;

        // Stop streaming
        sdk.UnsubscribeAll();

        // Display final statistics
        auto stats = sdk.GetStatistics();
        std::cout << "\n=== Final SDK Statistics ===" << std::endl;
        std::cout << "Messages received: " << stats.messages_received << std::endl;
        std::cout << "Messages sent: " << stats.messages_sent << std::endl;
        std::cout << "Connection count: " << stats.connection_count << std::endl;
        std::cout << "Average latency: " << stats.avg_latency.count() << " μs" << std::endl;
        std::cout << "Max latency: " << stats.max_latency.count() << " μs" << std::endl;

        // Disconnect
        std::cout << "\nDisconnecting..." << std::endl;
        sdk.Disconnect();

    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "Async example completed successfully!" << std::endl;
    return 0;
}