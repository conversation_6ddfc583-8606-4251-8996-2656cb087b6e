"""
Historical data service implementation
Interfaces with Redis, ClickHouse, and MinIO storage layers
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
import json
import base64
from dataclasses import dataclass

import redis.asyncio as aioredis
import asyncpg
from clickhouse_driver import Client as ClickHouseClient
import aioboto3

from .models import (
    TickData, KlineData, Level2Data, SymbolInfo,
    TickDataRequest, KlineDataRequest, Level2DataRequest,
    TickDataResponse, KlineDataResponse, Level2DataResponse,
    PaginationInfo, QueryStats
)
from .config import APIConfig

logger = logging.getLogger(__name__)


@dataclass
class StorageStats:
    """Storage layer statistics"""
    hot_queries: int = 0
    warm_queries: int = 0
    cold_queries: int = 0
    cache_hits: int = 0
    total_queries: int = 0


class HistoricalDataService:
    """Service for querying historical market data from multiple storage layers"""
    
    def __init__(self, config: APIConfig):
        self.config = config
        self.redis_pool: Optional[aioredis.Redis] = None
        self.clickhouse_client: Optional[ClickHouseClient] = None
        self.s3_client = None
        self.stats = StorageStats()
        
    async def initialize(self):
        """Initialize connections to storage systems"""
        try:
            # Initialize Redis connection for hot data
            self.redis_pool = aioredis.from_url(
                self.config.redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=20
            )
            
            # Test Redis connection
            await self.redis_pool.ping()
            logger.info("Redis connection established")
            
            # Initialize ClickHouse client for warm data
            self.clickhouse_client = ClickHouseClient(
                host=self.config.clickhouse_host,
                port=self.config.clickhouse_port,
                user=self.config.clickhouse_user,
                password=self.config.clickhouse_password,
                database=self.config.clickhouse_database
            )
            
            # Test ClickHouse connection
            self.clickhouse_client.execute("SELECT 1")
            logger.info("ClickHouse connection established")
            
            # Initialize S3 client for cold data
            session = aioboto3.Session()
            self.s3_client = session.client(
                's3',
                endpoint_url=self.config.s3_endpoint,
                aws_access_key_id=self.config.s3_access_key,
                aws_secret_access_key=self.config.s3_secret_key
            )
            
            logger.info("S3 client initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize data service: {e}")
            raise
    
    async def close(self):
        """Close all connections"""
        if self.redis_pool:
            await self.redis_pool.close()
        if self.s3_client:
            await self.s3_client.close()
    
    async def get_tick_data(self, request: TickDataRequest) -> TickDataResponse:
        """Query tick data from appropriate storage layer"""
        start_time = datetime.utcnow()
        
        try:
            # Determine which storage layer to query based on time range
            data_source, data = await self._query_tick_data_by_time_range(request)
            
            # Convert to response format
            tick_data = [self._convert_to_tick_data(record) for record in data]
            
            # Generate pagination info
            pagination = self._generate_pagination_info(
                data, request.limit, request.cursor
            )
            
            # Update statistics
            self._update_stats(data_source)
            
            query_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            return TickDataResponse(
                data=tick_data,
                pagination=pagination,
                message=f"Retrieved {len(tick_data)} tick records from {data_source}"
            )
            
        except Exception as e:
            logger.error(f"Error querying tick data: {e}")
            raise
    
    async def get_kline_data(self, request: KlineDataRequest) -> KlineDataResponse:
        """Query K-line data from appropriate storage layer"""
        start_time = datetime.utcnow()
        
        try:
            # K-line data is typically queried from warm storage (ClickHouse)
            data = await self._query_kline_from_clickhouse(request)
            
            # Convert to response format
            kline_data = [self._convert_to_kline_data(record) for record in data]
            
            # Generate pagination info
            pagination = self._generate_pagination_info(
                data, request.limit, request.cursor
            )
            
            self._update_stats("warm")
            
            return KlineDataResponse(
                data=kline_data,
                pagination=pagination,
                message=f"Retrieved {len(kline_data)} K-line records"
            )
            
        except Exception as e:
            logger.error(f"Error querying K-line data: {e}")
            raise
    
    async def get_level2_data(self, request: Level2DataRequest) -> Level2DataResponse:
        """Query Level-2 data from appropriate storage layer"""
        start_time = datetime.utcnow()
        
        try:
            # Level-2 data is typically in hot or warm storage
            data_source, data = await self._query_level2_data_by_time_range(request)
            
            # Convert to response format
            level2_data = [self._convert_to_level2_data(record) for record in data]
            
            # Generate pagination info
            pagination = self._generate_pagination_info(
                data, request.limit, request.cursor
            )
            
            self._update_stats(data_source)
            
            return Level2DataResponse(
                data=level2_data,
                pagination=pagination,
                message=f"Retrieved {len(level2_data)} Level-2 records from {data_source}"
            )
            
        except Exception as e:
            logger.error(f"Error querying Level-2 data: {e}")
            raise
    
    async def _query_tick_data_by_time_range(
        self, request: TickDataRequest
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """Query tick data from appropriate storage based on time range"""
        
        # If no time range specified, query recent data from hot storage
        if not request.start_time and not request.end_time:
            data = await self._query_tick_from_redis(request)
            return "hot", data
        
        # Determine time range
        now = datetime.utcnow()
        hot_threshold = now - timedelta(days=7)
        warm_threshold = now - timedelta(days=730)  # 2 years
        
        start_time = request.start_time or (now - timedelta(hours=1))
        end_time = request.end_time or now
        
        # Query from appropriate storage layer
        if start_time >= hot_threshold:
            # Recent data - query from Redis (hot storage)
            data = await self._query_tick_from_redis(request)
            return "hot", data
        elif start_time >= warm_threshold:
            # Historical data within 2 years - query from ClickHouse (warm storage)
            data = await self._query_tick_from_clickhouse(request)
            return "warm", data
        else:
            # Old historical data - query from S3 (cold storage)
            data = await self._query_tick_from_s3(request)
            return "cold", data
    
    async def _query_tick_from_redis(self, request: TickDataRequest) -> List[Dict[str, Any]]:
        """Query tick data from Redis hot storage"""
        try:
            # Build Redis key pattern
            key_pattern = f"tick:{request.symbol}:*"
            if request.exchange:
                key_pattern = f"tick:{request.symbol}:{request.exchange}:*"
            
            # Get keys matching pattern
            keys = await self.redis_pool.keys(key_pattern)
            
            if not keys:
                return []
            
            # Get data from Redis
            pipeline = self.redis_pool.pipeline()
            for key in keys[:request.limit]:
                pipeline.hgetall(key)
            
            results = await pipeline.execute()
            
            # Filter by time range if specified
            filtered_data = []
            for result in results:
                if result:
                    timestamp = int(result.get('timestamp', 0))
                    if self._is_in_time_range(timestamp, request.start_time, request.end_time):
                        filtered_data.append(result)
            
            # Sort by timestamp
            filtered_data.sort(key=lambda x: int(x.get('timestamp', 0)))
            
            return filtered_data[:request.limit]
            
        except Exception as e:
            logger.error(f"Error querying tick data from Redis: {e}")
            return []
    
    async def _query_tick_from_clickhouse(self, request: TickDataRequest) -> List[Dict[str, Any]]:
        """Query tick data from ClickHouse warm storage"""
        try:
            # Build SQL query
            sql = """
            SELECT 
                toUnixTimestamp64Nano(timestamp) as timestamp,
                symbol,
                exchange,
                last_price,
                volume,
                turnover,
                open_interest,
                bid_prices,
                bid_volumes,
                ask_prices,
                ask_volumes,
                sequence
            FROM market_data.futures_tick
            WHERE symbol = %(symbol)s
            """
            
            params = {'symbol': request.symbol}
            
            if request.exchange:
                sql += " AND exchange = %(exchange)s"
                params['exchange'] = request.exchange
            
            if request.start_time:
                sql += " AND timestamp >= %(start_time)s"
                params['start_time'] = request.start_time
            
            if request.end_time:
                sql += " AND timestamp <= %(end_time)s"
                params['end_time'] = request.end_time
            
            sql += " ORDER BY timestamp"
            
            if request.cursor:
                # Implement cursor-based pagination
                cursor_timestamp = self._decode_cursor(request.cursor)
                sql += f" AND timestamp > {cursor_timestamp}"
            
            sql += f" LIMIT {request.limit}"
            
            # Execute query
            result = self.clickhouse_client.execute(sql, params)
            
            # Convert to dict format
            columns = [
                'timestamp', 'symbol', 'exchange', 'last_price', 'volume',
                'turnover', 'open_interest', 'bid_prices', 'bid_volumes',
                'ask_prices', 'ask_volumes', 'sequence'
            ]
            
            data = []
            for row in result:
                record = dict(zip(columns, row))
                # Convert arrays to list of price levels
                record['bids'] = self._convert_price_levels(
                    record['bid_prices'], record['bid_volumes']
                )
                record['asks'] = self._convert_price_levels(
                    record['ask_prices'], record['ask_volumes']
                )
                data.append(record)
            
            return data
            
        except Exception as e:
            logger.error(f"Error querying tick data from ClickHouse: {e}")
            return []
    
    async def _query_tick_from_s3(self, request: TickDataRequest) -> List[Dict[str, Any]]:
        """Query tick data from S3 cold storage"""
        try:
            # Build S3 key pattern based on date
            # Assuming data is partitioned by date: tick-data/YYYY/MM/DD/symbol.parquet
            data = []
            
            if request.start_time and request.end_time:
                current_date = request.start_time.date()
                end_date = request.end_time.date()
                
                while current_date <= end_date:
                    s3_key = f"tick-data/{current_date.year:04d}/{current_date.month:02d}/{current_date.day:02d}/{request.symbol}.parquet"
                    
                    try:
                        # Download and parse Parquet file
                        response = await self.s3_client.get_object(
                            Bucket=self.config.s3_bucket,
                            Key=s3_key
                        )
                        
                        # Parse Parquet data (simplified - would use pyarrow in real implementation)
                        file_data = await self._parse_parquet_data(response['Body'])
                        data.extend(file_data)
                        
                    except Exception as e:
                        logger.warning(f"No data found for {s3_key}: {e}")
                    
                    current_date += timedelta(days=1)
            
            # Filter and sort data
            filtered_data = [
                record for record in data
                if self._is_in_time_range(
                    record.get('timestamp', 0), request.start_time, request.end_time
                )
            ]
            
            filtered_data.sort(key=lambda x: x.get('timestamp', 0))
            return filtered_data[:request.limit]
            
        except Exception as e:
            logger.error(f"Error querying tick data from S3: {e}")
            return []
    
    async def _query_kline_from_clickhouse(self, request: KlineDataRequest) -> List[Dict[str, Any]]:
        """Query K-line data from ClickHouse"""
        try:
            # Build SQL query for K-line data
            sql = """
            SELECT 
                toUnixTimestamp64Nano(timestamp) as timestamp,
                symbol,
                exchange,
                period,
                open,
                high,
                low,
                close,
                volume,
                turnover,
                open_interest
            FROM market_data.kline_data
            WHERE symbol = %(symbol)s AND period = %(period)s
            """
            
            params = {
                'symbol': request.symbol,
                'period': request.period
            }
            
            if request.exchange:
                sql += " AND exchange = %(exchange)s"
                params['exchange'] = request.exchange
            
            if request.start_time:
                sql += " AND timestamp >= %(start_time)s"
                params['start_time'] = request.start_time
            
            if request.end_time:
                sql += " AND timestamp <= %(end_time)s"
                params['end_time'] = request.end_time
            
            sql += " ORDER BY timestamp"
            
            if request.cursor:
                cursor_timestamp = self._decode_cursor(request.cursor)
                sql += f" AND timestamp > {cursor_timestamp}"
            
            sql += f" LIMIT {request.limit}"
            
            # Execute query
            result = self.clickhouse_client.execute(sql, params)
            
            # Convert to dict format
            columns = [
                'timestamp', 'symbol', 'exchange', 'period', 'open',
                'high', 'low', 'close', 'volume', 'turnover', 'open_interest'
            ]
            
            return [dict(zip(columns, row)) for row in result]
            
        except Exception as e:
            logger.error(f"Error querying K-line data from ClickHouse: {e}")
            return []
    
    async def _query_level2_data_by_time_range(
        self, request: Level2DataRequest
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """Query Level-2 data from appropriate storage based on time range"""
        
        # Similar logic to tick data
        if not request.start_time and not request.end_time:
            data = await self._query_level2_from_redis(request)
            return "hot", data
        
        now = datetime.utcnow()
        hot_threshold = now - timedelta(days=7)
        
        start_time = request.start_time or (now - timedelta(hours=1))
        
        if start_time >= hot_threshold:
            data = await self._query_level2_from_redis(request)
            return "hot", data
        else:
            data = await self._query_level2_from_clickhouse(request)
            return "warm", data
    
    async def _query_level2_from_redis(self, request: Level2DataRequest) -> List[Dict[str, Any]]:
        """Query Level-2 data from Redis"""
        try:
            key_pattern = f"level2:{request.symbol}:*"
            if request.exchange:
                key_pattern = f"level2:{request.symbol}:{request.exchange}:*"
            
            keys = await self.redis_pool.keys(key_pattern)
            
            if not keys:
                return []
            
            pipeline = self.redis_pool.pipeline()
            for key in keys[:request.limit]:
                pipeline.hgetall(key)
            
            results = await pipeline.execute()
            
            filtered_data = []
            for result in results:
                if result:
                    timestamp = int(result.get('timestamp', 0))
                    if self._is_in_time_range(timestamp, request.start_time, request.end_time):
                        filtered_data.append(result)
            
            filtered_data.sort(key=lambda x: int(x.get('timestamp', 0)))
            return filtered_data[:request.limit]
            
        except Exception as e:
            logger.error(f"Error querying Level-2 data from Redis: {e}")
            return []
    
    async def _query_level2_from_clickhouse(self, request: Level2DataRequest) -> List[Dict[str, Any]]:
        """Query Level-2 data from ClickHouse"""
        try:
            sql = """
            SELECT 
                toUnixTimestamp64Nano(timestamp) as timestamp,
                symbol,
                exchange,
                bid_prices,
                bid_volumes,
                ask_prices,
                ask_volumes,
                last_price,
                sequence
            FROM market_data.level2_data
            WHERE symbol = %(symbol)s
            """
            
            params = {'symbol': request.symbol}
            
            if request.exchange:
                sql += " AND exchange = %(exchange)s"
                params['exchange'] = request.exchange
            
            if request.start_time:
                sql += " AND timestamp >= %(start_time)s"
                params['start_time'] = request.start_time
            
            if request.end_time:
                sql += " AND timestamp <= %(end_time)s"
                params['end_time'] = request.end_time
            
            sql += " ORDER BY timestamp"
            
            if request.cursor:
                cursor_timestamp = self._decode_cursor(request.cursor)
                sql += f" AND timestamp > {cursor_timestamp}"
            
            sql += f" LIMIT {request.limit}"
            
            result = self.clickhouse_client.execute(sql, params)
            
            columns = [
                'timestamp', 'symbol', 'exchange', 'bid_prices', 'bid_volumes',
                'ask_prices', 'ask_volumes', 'last_price', 'sequence'
            ]
            
            data = []
            for row in result:
                record = dict(zip(columns, row))
                record['bids'] = self._convert_price_levels(
                    record['bid_prices'], record['bid_volumes']
                )
                record['asks'] = self._convert_price_levels(
                    record['ask_prices'], record['ask_volumes']
                )
                data.append(record)
            
            return data
            
        except Exception as e:
            logger.error(f"Error querying Level-2 data from ClickHouse: {e}")
            return []
    
    async def get_available_symbols(
        self, exchange: Optional[str] = None, product_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get available symbols/contracts"""
        try:
            sql = """
            SELECT 
                symbol,
                exchange,
                product_type,
                underlying,
                expiry_date,
                contract_size,
                tick_size,
                created_at,
                updated_at
            FROM metadata.instruments
            WHERE 1=1
            """
            
            params = {}
            
            if exchange:
                sql += " AND exchange = %(exchange)s"
                params['exchange'] = exchange
            
            if product_type:
                sql += " AND product_type = %(product_type)s"
                params['product_type'] = product_type
            
            sql += " ORDER BY symbol"
            
            result = self.clickhouse_client.execute(sql, params)
            
            columns = [
                'symbol', 'exchange', 'product_type', 'underlying',
                'expiry_date', 'contract_size', 'tick_size', 'created_at', 'updated_at'
            ]
            
            return [dict(zip(columns, row)) for row in result]
            
        except Exception as e:
            logger.error(f"Error getting available symbols: {e}")
            return []
    
    async def get_available_exchanges(self) -> List[str]:
        """Get list of available exchanges"""
        try:
            sql = "SELECT DISTINCT exchange FROM metadata.instruments ORDER BY exchange"
            result = self.clickhouse_client.execute(sql)
            return [row[0] for row in result]
        except Exception as e:
            logger.error(f"Error getting available exchanges: {e}")
            return []
    
    def _convert_to_tick_data(self, record: Dict[str, Any]) -> TickData:
        """Convert database record to TickData model"""
        return TickData(
            timestamp=int(record.get('timestamp', 0)),
            symbol=record.get('symbol', ''),
            exchange=record.get('exchange', ''),
            last_price=float(record.get('last_price', 0)),
            volume=int(record.get('volume', 0)),
            turnover=float(record.get('turnover', 0)),
            open_interest=record.get('open_interest'),
            bids=record.get('bids', []),
            asks=record.get('asks', []),
            sequence=int(record.get('sequence', 0)),
            trade_flag=record.get('trade_flag')
        )
    
    def _convert_to_kline_data(self, record: Dict[str, Any]) -> KlineData:
        """Convert database record to KlineData model"""
        return KlineData(
            timestamp=int(record.get('timestamp', 0)),
            symbol=record.get('symbol', ''),
            exchange=record.get('exchange', ''),
            period=record.get('period', ''),
            open=float(record.get('open', 0)),
            high=float(record.get('high', 0)),
            low=float(record.get('low', 0)),
            close=float(record.get('close', 0)),
            volume=int(record.get('volume', 0)),
            turnover=float(record.get('turnover', 0)),
            open_interest=record.get('open_interest')
        )
    
    def _convert_to_level2_data(self, record: Dict[str, Any]) -> Level2Data:
        """Convert database record to Level2Data model"""
        return Level2Data(
            timestamp=int(record.get('timestamp', 0)),
            symbol=record.get('symbol', ''),
            exchange=record.get('exchange', ''),
            bids=record.get('bids', []),
            asks=record.get('asks', []),
            last_price=record.get('last_price'),
            sequence=int(record.get('sequence', 0))
        )
    
    def _convert_price_levels(self, prices: List[float], volumes: List[int]) -> List[Dict[str, Any]]:
        """Convert price and volume arrays to price level objects"""
        if not prices or not volumes:
            return []
        
        return [
            {"price": price, "volume": volume, "order_count": None}
            for price, volume in zip(prices, volumes)
            if price > 0 and volume > 0
        ]
    
    def _generate_pagination_info(
        self, data: List[Dict[str, Any]], limit: int, cursor: Optional[str]
    ) -> PaginationInfo:
        """Generate pagination information"""
        has_next = len(data) >= limit
        next_cursor = None
        
        if has_next and data:
            # Use timestamp of last record as cursor
            last_timestamp = data[-1].get('timestamp', 0)
            next_cursor = self._encode_cursor(last_timestamp)
        
        return PaginationInfo(
            has_next=has_next,
            next_cursor=next_cursor,
            total_count=None,  # Would require additional query
            page_size=len(data)
        )
    
    def _encode_cursor(self, timestamp: int) -> str:
        """Encode timestamp as cursor"""
        return base64.b64encode(str(timestamp).encode()).decode()
    
    def _decode_cursor(self, cursor: str) -> int:
        """Decode cursor to timestamp"""
        try:
            return int(base64.b64decode(cursor.encode()).decode())
        except Exception:
            return 0
    
    def _is_in_time_range(
        self, timestamp: int, start_time: Optional[datetime], end_time: Optional[datetime]
    ) -> bool:
        """Check if timestamp is within specified time range"""
        if start_time and timestamp < int(start_time.timestamp() * 1_000_000_000):
            return False
        if end_time and timestamp > int(end_time.timestamp() * 1_000_000_000):
            return False
        return True
    
    def _update_stats(self, data_source: str):
        """Update query statistics"""
        self.stats.total_queries += 1
        if data_source == "hot":
            self.stats.hot_queries += 1
        elif data_source == "warm":
            self.stats.warm_queries += 1
        elif data_source == "cold":
            self.stats.cold_queries += 1
    
    async def _parse_parquet_data(self, data_stream) -> List[Dict[str, Any]]:
        """Parse Parquet data from S3 (simplified implementation)"""
        # In a real implementation, this would use pyarrow to parse Parquet files
        # For now, return empty list as placeholder
        logger.warning("Parquet parsing not implemented - returning empty data")
        return []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        return {
            "total_queries": self.stats.total_queries,
            "hot_queries": self.stats.hot_queries,
            "warm_queries": self.stats.warm_queries,
            "cold_queries": self.stats.cold_queries,
            "cache_hits": self.stats.cache_hits
        }
 