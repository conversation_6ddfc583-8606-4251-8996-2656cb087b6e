#include "config_validators.h"
#include "storage_strategy_validator.h"
#include <algorithm>
#include <set>

namespace config {

// BaseConfigValidator 实现
bool BaseConfigValidator::ValidateRequired(const nlohmann::json& config, 
                                          const std::vector<std::string>& required_keys,
                                          ValidationResult& result) const {
    bool all_present = true;
    
    for (const auto& key : required_keys) {
        if (!config.contains(key)) {
            result.AddError("Missing required key: " + key);
            all_present = false;
        }
    }
    
    return all_present;
}

bool BaseConfigValidator::ValidateType(const nlohmann::json& config, 
                                      const std::string& key,
                                      nlohmann::json::value_t expected_type,
                                      ValidationResult& result) const {
    if (!config.contains(key)) {
        return true; // 由 ValidateRequired 处理
    }
    
    if (config[key].type() != expected_type) {
        result.AddError("Key '" + key + "' has wrong type");
        return false;
    }
    
    return true;
}

bool BaseConfigValidator::ValidateRange(const nlohmann::json& config,
                                       const std::string& key,
                                       double min_value, double max_value,
                                       ValidationResult& result) const {
    if (!config.contains(key)) {
        return true;
    }
    
    if (!config[key].is_number()) {
        return true; // 由 ValidateType 处理
    }
    
    double value = config[key].get<double>();
    if (value < min_value || value > max_value) {
        result.AddError("Key '" + key + "' value " + std::to_string(value) + 
                       " is out of range [" + std::to_string(min_value) + 
                       ", " + std::to_string(max_value) + "]");
        return false;
    }
    
    return true;
}

bool BaseConfigValidator::ValidateRegex(const nlohmann::json& config,
                                       const std::string& key,
                                       const std::string& pattern,
                                       ValidationResult& result) const {
    if (!config.contains(key)) {
        return true;
    }
    
    if (!config[key].is_string()) {
        return true; // 由 ValidateType 处理
    }
    
    std::string value = config[key].get<std::string>();
    std::regex regex_pattern(pattern);
    
    if (!std::regex_match(value, regex_pattern)) {
        result.AddError("Key '" + key + "' value '" + value + 
                       "' does not match pattern: " + pattern);
        return false;
    }
    
    return true;
}

// ServerConfigValidator 实现
ValidationResult ServerConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 验证必需字段
    ValidateRequired(config, {"host", "port", "threads"}, result);
    
    // 验证类型
    ValidateType(config, "host", nlohmann::json::value_t::string, result);
    ValidateType(config, "port", nlohmann::json::value_t::number_integer, result);
    ValidateType(config, "threads", nlohmann::json::value_t::number_integer, result);
    
    // 验证范围
    ValidateRange(config, "port", 1, 65535, result);
    ValidateRange(config, "threads", 1, 1000, result);
    
    // 验证主机地址格式
    ValidateRegex(config, "host", R"(^(\d{1,3}\.){3}\d{1,3}$|^localhost$|^0\.0\.0\.0$)", result);
    
    return result;
}

// RedisConfigValidator 实现
ValidationResult RedisConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 验证必需字段
    ValidateRequired(config, {"host", "port", "database"}, result);
    
    // 验证类型
    ValidateType(config, "host", nlohmann::json::value_t::string, result);
    ValidateType(config, "port", nlohmann::json::value_t::number_integer, result);
    ValidateType(config, "database", nlohmann::json::value_t::number_integer, result);
    ValidateType(config, "password", nlohmann::json::value_t::string, result);
    ValidateType(config, "pool_size", nlohmann::json::value_t::number_integer, result);
    
    // 验证范围
    ValidateRange(config, "port", 1, 65535, result);
    ValidateRange(config, "database", 0, 15, result);
    ValidateRange(config, "pool_size", 1, 100, result);
    
    // 验证连接超时
    if (config.contains("timeout_ms")) {
        ValidateType(config, "timeout_ms", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "timeout_ms", 100, 30000, result);
    }
    
    return result;
}

// ClickHouseConfigValidator 实现
ValidationResult ClickHouseConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 验证必需字段
    ValidateRequired(config, {"host", "port", "database", "username"}, result);
    
    // 验证类型
    ValidateType(config, "host", nlohmann::json::value_t::string, result);
    ValidateType(config, "port", nlohmann::json::value_t::number_integer, result);
    ValidateType(config, "database", nlohmann::json::value_t::string, result);
    ValidateType(config, "username", nlohmann::json::value_t::string, result);
    ValidateType(config, "password", nlohmann::json::value_t::string, result);
    
    // 验证范围
    ValidateRange(config, "port", 1, 65535, result);
    
    // 验证数据库名称格式
    ValidateRegex(config, "database", R"(^[a-zA-Z_][a-zA-Z0-9_]*$)", result);
    
    // 验证连接池配置
    if (config.contains("max_connections")) {
        ValidateType(config, "max_connections", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "max_connections", 1, 1000, result);
    }
    
    return result;
}

// CTPConfigValidator 实现
ValidationResult CTPConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 验证必需字段
    ValidateRequired(config, {"front_address", "broker_id", "user_id", "password"}, result);
    
    // 验证类型
    ValidateType(config, "front_address", nlohmann::json::value_t::string, result);
    ValidateType(config, "broker_id", nlohmann::json::value_t::string, result);
    ValidateType(config, "user_id", nlohmann::json::value_t::string, result);
    ValidateType(config, "password", nlohmann::json::value_t::string, result);
    ValidateType(config, "flow_path", nlohmann::json::value_t::string, result);
    
    // 验证地址格式
    ValidateRegex(config, "front_address", R"(^tcp://[\w\.-]+:\d+$)", result);
    
    // 验证数值配置
    if (config.contains("heartbeat_interval")) {
        ValidateType(config, "heartbeat_interval", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "heartbeat_interval", 5, 300, result);
    }
    
    if (config.contains("reconnect_interval")) {
        ValidateType(config, "reconnect_interval", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "reconnect_interval", 1, 60, result);
    }
    
    if (config.contains("max_reconnect_attempts")) {
        ValidateType(config, "max_reconnect_attempts", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "max_reconnect_attempts", 1, 100, result);
    }
    
    // 验证布尔配置
    if (config.contains("enable_level2")) {
        ValidateType(config, "enable_level2", nlohmann::json::value_t::boolean, result);
    }
    
    return result;
}

// CollectionConfigValidator 实现
ValidationResult CollectionConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 验证pytdx配置
    if (config.contains("pytdx")) {
        const auto& pytdx_config = config["pytdx"];
        
        ValidateType(pytdx_config, "enabled", nlohmann::json::value_t::boolean, result);
        ValidateType(pytdx_config, "batch_size", nlohmann::json::value_t::number_integer, result);
        ValidateType(pytdx_config, "concurrent_requests", nlohmann::json::value_t::number_integer, result);
        
        ValidateRange(pytdx_config, "batch_size", 1, 10000, result);
        ValidateRange(pytdx_config, "concurrent_requests", 1, 50, result);
        
        if (pytdx_config.contains("servers") && pytdx_config["servers"].is_array()) {
            for (const auto& server : pytdx_config["servers"]) {
                if (server.contains("host") && server.contains("port")) {
                    ValidateType(server, "host", nlohmann::json::value_t::string, result);
                    ValidateType(server, "port", nlohmann::json::value_t::number_integer, result);
                    ValidateRange(server, "port", 1, 65535, result);
                }
            }
        }
    }
    
    // 验证CTP配置
    if (config.contains("ctp")) {
        const auto& ctp_config = config["ctp"];
        
        ValidateType(ctp_config, "enabled", nlohmann::json::value_t::boolean, result);
        ValidateType(ctp_config, "config_path", nlohmann::json::value_t::string, result);
        
        if (ctp_config.contains("failover_timeout")) {
            ValidateType(ctp_config, "failover_timeout", nlohmann::json::value_t::number_integer, result);
            ValidateRange(ctp_config, "failover_timeout", 5, 300, result);
        }
    }
    
    // 验证协调配置
    if (config.contains("coordination")) {
        const auto& coord_config = config["coordination"];
        
        if (coord_config.contains("priority_source")) {
            ValidateType(coord_config, "priority_source", nlohmann::json::value_t::string, result);
            
            std::string priority = coord_config["priority_source"].get<std::string>();
            if (priority != "ctp" && priority != "pytdx") {
                result.AddError("priority_source must be 'ctp' or 'pytdx'");
            }
        }
        
        if (coord_config.contains("overlap_tolerance_seconds")) {
            ValidateType(coord_config, "overlap_tolerance_seconds", nlohmann::json::value_t::number_integer, result);
            ValidateRange(coord_config, "overlap_tolerance_seconds", 0, 3600, result);
        }
    }
    
    return result;
}

// StorageConfigValidator 实现
ValidationResult StorageConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 验证存储层配置
    std::vector<std::string> storage_layers = {"hot_storage", "warm_storage", "cold_storage"};
    
    for (const auto& layer : storage_layers) {
        if (config.contains(layer)) {
            const auto& layer_config = config[layer];
            
            ValidateRequired(layer_config, {"type"}, result);
            ValidateType(layer_config, "type", nlohmann::json::value_t::string, result);
            
            if (layer_config.contains("retention_days")) {
                ValidateType(layer_config, "retention_days", nlohmann::json::value_t::number_integer, result);
                ValidateRange(layer_config, "retention_days", 1, 36500, result); // 最多100年
            }
            
            // 验证存储类型
            if (layer_config.contains("type")) {
                std::string storage_type = layer_config["type"].get<std::string>();
                std::set<std::string> valid_types = {"redis", "clickhouse", "s3", "file"};
                
                if (valid_types.find(storage_type) == valid_types.end()) {
                    result.AddError("Invalid storage type for " + layer + ": " + storage_type);
                }
            }
        }
    }
    
    // 验证数据迁移配置
    if (config.contains("migration")) {
        const auto& migration_config = config["migration"];
        
        if (migration_config.contains("batch_size")) {
            ValidateType(migration_config, "batch_size", nlohmann::json::value_t::number_integer, result);
            ValidateRange(migration_config, "batch_size", 100, 100000, result);
        }
        
        if (migration_config.contains("parallel_workers")) {
            ValidateType(migration_config, "parallel_workers", nlohmann::json::value_t::number_integer, result);
            ValidateRange(migration_config, "parallel_workers", 1, 50, result);
        }
    }
    
    return result;
}

// MonitoringConfigValidator 实现
ValidationResult MonitoringConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    ValidateType(config, "enable_metrics", nlohmann::json::value_t::boolean, result);
    
    // 验证告警阈值
    if (config.contains("alert_thresholds")) {
        const auto& thresholds = config["alert_thresholds"];
        
        if (thresholds.contains("data_delay_seconds")) {
            ValidateType(thresholds, "data_delay_seconds", nlohmann::json::value_t::number_integer, result);
            ValidateRange(thresholds, "data_delay_seconds", 1, 3600, result);
        }
        
        if (thresholds.contains("error_rate_percent")) {
            ValidateType(thresholds, "error_rate_percent", nlohmann::json::value_t::number_float, result);
            ValidateRange(thresholds, "error_rate_percent", 0.0, 100.0, result);
        }
        
        if (thresholds.contains("cpu_threshold_percent")) {
            ValidateType(thresholds, "cpu_threshold_percent", nlohmann::json::value_t::number_float, result);
            ValidateRange(thresholds, "cpu_threshold_percent", 0.0, 100.0, result);
        }
        
        if (thresholds.contains("memory_threshold_percent")) {
            ValidateType(thresholds, "memory_threshold_percent", nlohmann::json::value_t::number_float, result);
            ValidateRange(thresholds, "memory_threshold_percent", 0.0, 100.0, result);
        }
    }
    
    // 验证Prometheus配置
    if (config.contains("prometheus")) {
        const auto& prometheus_config = config["prometheus"];
        
        if (prometheus_config.contains("bind_address")) {
            ValidateType(prometheus_config, "bind_address", nlohmann::json::value_t::string, result);
            ValidateRegex(prometheus_config, "bind_address", R"(^[\w\.-]+:\d+$)", result);
        }
        
        if (prometheus_config.contains("metrics_path")) {
            ValidateType(prometheus_config, "metrics_path", nlohmann::json::value_t::string, result);
            ValidateRegex(prometheus_config, "metrics_path", R"(^/[\w/]*$)", result);
        }
    }
    
    return result;
}

// SchedulingConfigValidator 实现
ValidationResult SchedulingConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 验证历史数据更新配置
    if (config.contains("historical_update")) {
        const auto& hist_config = config["historical_update"];
        
        if (hist_config.contains("cron")) {
            ValidateType(hist_config, "cron", nlohmann::json::value_t::string, result);
            
            std::string cron_expr = hist_config["cron"].get<std::string>();
            if (!ValidateCronExpression(cron_expr)) {
                result.AddError("Invalid cron expression: " + cron_expr);
            }
        }
        
        if (hist_config.contains("lookback_days")) {
            ValidateType(hist_config, "lookback_days", nlohmann::json::value_t::number_integer, result);
            ValidateRange(hist_config, "lookback_days", 1, 365, result);
        }
        
        if (hist_config.contains("symbols")) {
            ValidateType(hist_config, "symbols", nlohmann::json::value_t::array, result);
        }
    }
    
    // 验证数据迁移配置
    if (config.contains("data_migration")) {
        const auto& migration_config = config["data_migration"];
        
        if (migration_config.contains("cron")) {
            ValidateType(migration_config, "cron", nlohmann::json::value_t::string, result);
            
            std::string cron_expr = migration_config["cron"].get<std::string>();
            if (!ValidateCronExpression(cron_expr)) {
                result.AddError("Invalid cron expression: " + cron_expr);
            }
        }
        
        if (migration_config.contains("batch_size")) {
            ValidateType(migration_config, "batch_size", nlohmann::json::value_t::number_integer, result);
            ValidateRange(migration_config, "batch_size", 100, 100000, result);
        }
    }
    
    return result;
}

bool SchedulingConfigValidator::ValidateCronExpression(const std::string& cron_expr) const {
    // 简单的cron表达式验证 (分 时 日 月 周)
    std::regex cron_regex(R"(^(\*|[0-5]?\d|\*\/\d+)\s+(\*|[01]?\d|2[0-3]|\*\/\d+)\s+(\*|[0-2]?\d|3[01]|\*\/\d+)\s+(\*|[0]?\d|1[0-2]|\*\/\d+)\s+(\*|[0-6]|\*\/\d+)$)");
    
    return std::regex_match(cron_expr, cron_regex);
}

// PerformanceConfigValidator 实现
ValidationResult PerformanceConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    if (config.contains("max_concurrent_clients")) {
        ValidateType(config, "max_concurrent_clients", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "max_concurrent_clients", 1, 100000, result);
    }
    
    if (config.contains("worker_threads")) {
        ValidateType(config, "worker_threads", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "worker_threads", 1, 1000, result);
    }
    
    if (config.contains("io_threads")) {
        ValidateType(config, "io_threads", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "io_threads", 1, 100, result);
    }
    
    if (config.contains("buffer_size")) {
        ValidateType(config, "buffer_size", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "buffer_size", 1024, 1048576, result); // 1KB - 1MB
    }
    
    if (config.contains("batch_timeout_ms")) {
        ValidateType(config, "batch_timeout_ms", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "batch_timeout_ms", 1, 10000, result);
    }
    
    return result;
}

// LoggingConfigValidator 实现
ValidationResult LoggingConfigValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 验证日志级别
    if (config.contains("level")) {
        ValidateType(config, "level", nlohmann::json::value_t::string, result);
        
        std::string level = config["level"].get<std::string>();
        std::transform(level.begin(), level.end(), level.begin(), ::toupper);
        
        std::set<std::string> valid_levels = {"TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"};
        if (valid_levels.find(level) == valid_levels.end()) {
            result.AddError("Invalid log level: " + level);
        }
    }
    
    // 验证日志文件配置
    if (config.contains("file")) {
        ValidateType(config, "file", nlohmann::json::value_t::string, result);
    }
    
    if (config.contains("max_file_size_mb")) {
        ValidateType(config, "max_file_size_mb", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "max_file_size_mb", 1, 1000, result);
    }
    
    if (config.contains("max_files")) {
        ValidateType(config, "max_files", nlohmann::json::value_t::number_integer, result);
        ValidateRange(config, "max_files", 1, 100, result);
    }
    
    // 验证控制台输出配置
    if (config.contains("console")) {
        ValidateType(config, "console", nlohmann::json::value_t::boolean, result);
    }
    
    return result;
}

} // namespace config// 注册所有验证器
的辅助函数
void RegisterAllValidators(ConfigManager& config_manager) {
    // 注册基础配置验证器
    config_manager.RegisterValidator("server", std::make_shared<ServerConfigValidator>());
    config_manager.RegisterValidator("logging", std::make_shared<LoggingConfigValidator>());
    config_manager.RegisterValidator("performance", std::make_shared<PerformanceConfigValidator>());
    config_manager.RegisterValidator("monitoring", std::make_shared<MonitoringConfigValidator>());
    config_manager.RegisterValidator("scheduling", std::make_shared<SchedulingConfigValidator>());
    
    // 注册存储相关验证器
    config_manager.RegisterValidator("storage", std::make_shared<StorageConfigValidator>());
    config_manager.RegisterValidator("storage", std::make_shared<StorageStrategyValidator>());
    
    // 注册采集相关验证器
    config_manager.RegisterValidator("collection", std::make_shared<CollectionConfigValidator>());
    
    // 注册Redis配置验证器
    config_manager.RegisterValidator("storage.hot_storage.config", std::make_shared<RedisConfigValidator>());
    
    // 注册ClickHouse配置验证器
    config_manager.RegisterValidator("storage.warm_storage.config", std::make_shared<ClickHouseConfigValidator>());
    
    // 注册CTP配置验证器
    config_manager.RegisterValidator("collection.ctp", std::make_shared<CTPConfigValidator>());
}