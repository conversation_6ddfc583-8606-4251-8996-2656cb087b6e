version: '3.8'

services:
  # 任务调度器服务
  scheduler:
    build:
      context: ../..
      dockerfile: deployment/docker/scheduler.Dockerfile
    container_name: financial-data-scheduler
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app/src
      - TZ=Asia/Shanghai
      - LOG_LEVEL=INFO
      - CONFIG_PATH=/app/config/scheduler_config.json
    volumes:
      # 配置文件
      - ../../config/scheduler_config.json:/app/config/scheduler_config.json:ro
      # 日志目录
      - scheduler_logs:/app/logs
      # 数据目录（如果需要）
      - scheduler_data:/app/data
    networks:
      - financial-data-network
    depends_on:
      - redis
      - clickhouse
    healthcheck:
      test: ["CMD", "python3", "scripts/start_scheduler.py", "--status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Redis服务（如果不存在）
  redis:
    image: redis:7-alpine
    container_name: financial-data-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - financial-data-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # ClickHouse服务（如果不存在）
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: financial-data-clickhouse
    restart: unless-stopped
    environment:
      - CLICKHOUSE_DB=financial_data
      - CLICKHOUSE_USER=admin
      - CLICKHOUSE_PASSWORD=password123
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - clickhouse_logs:/var/log/clickhouse-server
    networks:
      - financial-data-network
    ports:
      - "8123:8123"  # HTTP接口
      - "9000:9000"  # Native接口
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # 监控服务（可选）
  scheduler-monitor:
    image: prom/prometheus:latest
    container_name: financial-data-scheduler-monitor
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - financial-data-network
    ports:
      - "9090:9090"
    depends_on:
      - scheduler

# 网络配置
networks:
  financial-data-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  scheduler_logs:
    driver: local
  scheduler_data:
    driver: local
  redis_data:
    driver: local
  clickhouse_data:
    driver: local
  clickhouse_logs:
    driver: local
  prometheus_data:
    driver: local