#include "metrics_collector.h"
#include <iostream>
#include <sstream>

namespace monitoring {

MetricsCollector::MetricsCollector(const MonitoringConfig& config)
    : config_(config) {
}

MetricsCollector::~MetricsCollector() {
    stop();
    cleanupComponents();
}

bool MetricsCollector::initialize() {
    if (initialized_.load()) {
        return true;
    }
    
    std::cout << "Initializing monitoring system..." << std::endl;
    
    if (!initializeComponents()) {
        std::cerr << "Failed to initialize monitoring components" << std::endl;
        return false;
    }
    
    initialized_.store(true);
    std::cout << "Monitoring system initialized successfully" << std::endl;
    return true;
}

bool MetricsCollector::start() {
    if (!initialized_.load()) {
        if (!initialize()) {
            return false;
        }
    }
    
    if (running_.load()) {
        return true;
    }
    
    std::cout << "Starting monitoring system..." << std::endl;
    
    // Start all components
    if (!alert_manager_->start()) {
        std::cerr << "Failed to start alert manager" << std::endl;
        return false;
    }
    
    if (!latency_monitor_->start()) {
        std::cerr << "Failed to start latency monitor" << std::endl;
        return false;
    }
    
    if (!data_integrity_checker_->start()) {
        std::cerr << "Failed to start data integrity checker" << std::endl;
        return false;
    }
    
    if (!resource_monitor_->start()) {
        std::cerr << "Failed to start resource monitor" << std::endl;
        return false;
    }
    
    // Start health check thread
    running_.store(true);
    health_check_thread_ = std::thread(&MetricsCollector::healthCheckLoop, this);
    
    std::cout << "Monitoring system started successfully" << std::endl;
    std::cout << "Prometheus metrics available at: http://" << config_.prometheus_bind_address << "/metrics" << std::endl;
    
    return true;
}

void MetricsCollector::stop() {
    if (!running_.load()) {
        return;
    }
    
    std::cout << "Stopping monitoring system..." << std::endl;
    
    running_.store(false);
    
    // Stop health check thread
    if (health_check_thread_.joinable()) {
        health_check_thread_.join();
    }
    
    // Stop all components
    if (resource_monitor_) {
        resource_monitor_->stop();
    }
    
    if (data_integrity_checker_) {
        data_integrity_checker_->stop();
    }
    
    if (latency_monitor_) {
        latency_monitor_->stop();
    }
    
    if (alert_manager_) {
        alert_manager_->stop();
    }
    
    std::cout << "Monitoring system stopped" << std::endl;
}

bool MetricsCollector::initializeComponents() {
    try {
        // Initialize Prometheus metrics
        auto& prometheus = PrometheusMetrics::getInstance();
        if (!prometheus.initialize(config_.prometheus_bind_address)) {
            return false;
        }
        
        // Create alert manager
        alert_manager_ = std::make_shared<AlertManager>();
        applyAlertConfig();
        
        // Create latency monitor
        latency_monitor_ = std::make_unique<LatencyMonitor>(alert_manager_);
        applyLatencyConfig();
        
        // Create data integrity checker
        data_integrity_checker_ = std::make_unique<DataIntegrityChecker>(alert_manager_);
        applyIntegrityConfig();
        
        // Create resource monitor
        resource_monitor_ = std::make_unique<ResourceMonitor>(alert_manager_);
        applyResourceConfig();
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Exception during component initialization: " << e.what() << std::endl;
        return false;
    }
}

void MetricsCollector::cleanupComponents() {
    resource_monitor_.reset();
    data_integrity_checker_.reset();
    latency_monitor_.reset();
    alert_manager_.reset();
}

void MetricsCollector::updateConfig(const MonitoringConfig& config) {
    config_ = config;
    
    if (initialized_.load()) {
        applyLatencyConfig();
        applyIntegrityConfig();
        applyResourceConfig();
        applyAlertConfig();
    }
}

void MetricsCollector::applyLatencyConfig() {
    if (latency_monitor_) {
        latency_monitor_->setLatencyThreshold(config_.latency_threshold_microseconds);
        latency_monitor_->setAlertCooldown(config_.latency_alert_cooldown);
    }
}

void MetricsCollector::applyIntegrityConfig() {
    if (data_integrity_checker_) {
        data_integrity_checker_->setMissingDataTimeout(config_.missing_data_timeout);
        data_integrity_checker_->setAlertCooldown(config_.integrity_alert_cooldown);
        data_integrity_checker_->setMaxSequenceGap(config_.max_sequence_gap);
    }
}

void MetricsCollector::applyResourceConfig() {
    if (resource_monitor_) {
        resource_monitor_->setMonitoringInterval(config_.resource_monitoring_interval);
        resource_monitor_->setCpuThreshold(config_.cpu_threshold);
        resource_monitor_->setMemoryThreshold(config_.memory_threshold);
        resource_monitor_->setDiskThreshold(config_.disk_threshold);
        resource_monitor_->setAlertCooldown(config_.resource_alert_cooldown);
    }
}

void MetricsCollector::applyAlertConfig() {
    if (alert_manager_) {
        alert_manager_->setMaxRetries(config_.max_alert_retries);
        alert_manager_->setRetryDelay(config_.alert_retry_delay);
        alert_manager_->setRateLimitWindow(config_.alert_rate_limit_window);
        alert_manager_->setMaxAlertsPerWindow(config_.max_alerts_per_window);
    }
}

void MetricsCollector::recordLatency(const std::string& operation, double latency_microseconds, 
                                   const std::string& symbol) {
    if (latency_monitor_) {
        LatencyMeasurement measurement;
        measurement.operation = operation;
        measurement.symbol = symbol;
        measurement.latency_microseconds = latency_microseconds;
        measurement.start_time = std::chrono::high_resolution_clock::now() - 
                               std::chrono::microseconds(static_cast<int64_t>(latency_microseconds));
        measurement.end_time = std::chrono::high_resolution_clock::now();
        measurement.sequence_id = 0; // Not used for direct recording
        
        latency_monitor_->recordLatency(measurement);
    }
}

uint64_t MetricsCollector::startLatencyMeasurement(const std::string& operation, const std::string& symbol) {
    if (latency_monitor_) {
        return latency_monitor_->startMeasurement(operation, symbol);
    }
    return 0;
}

void MetricsCollector::endLatencyMeasurement(uint64_t measurement_id) {
    if (latency_monitor_) {
        latency_monitor_->endMeasurement(measurement_id);
    }
}

void MetricsCollector::recordDataMessage(const std::string& symbol, uint64_t sequence_number,
                                       const std::string& source, const std::string& message_type) {
    if (data_integrity_checker_) {
        DataMessage message;
        message.symbol = symbol;
        message.sequence_number = sequence_number;
        message.timestamp = std::chrono::high_resolution_clock::now();
        message.source = source;
        message.message_type = message_type;
        
        data_integrity_checker_->recordMessage(message);
    }
}

void MetricsCollector::sendAlert(const std::string& type, const std::string& severity,
                               const std::string& message,
                               const std::unordered_map<std::string, std::string>& metadata) {
    if (alert_manager_) {
        alert_manager_->sendAlert(type, severity, message, metadata);
    }
}

void MetricsCollector::healthCheckLoop() {
    while (running_.load()) {
        performHealthCheck();
        std::this_thread::sleep_for(health_check_interval_);
    }
}

void MetricsCollector::performHealthCheck() {
    bool overall_health = true;
    
    // Check Prometheus metrics
    overall_health &= checkComponentHealth("prometheus", []() {
        // Simple health check - assume healthy if instance exists
        return true;
    });
    
    // Check latency monitor
    overall_health &= checkComponentHealth("latency_monitor", [this]() {
        return latency_monitor_ != nullptr;
    });
    
    // Check data integrity checker
    overall_health &= checkComponentHealth("data_integrity_checker", [this]() {
        return data_integrity_checker_ != nullptr;
    });
    
    // Check resource monitor
    overall_health &= checkComponentHealth("resource_monitor", [this]() {
        return resource_monitor_ != nullptr;
    });
    
    // Check alert manager
    overall_health &= checkComponentHealth("alert_manager", [this]() {
        return alert_manager_ != nullptr;
    });
    
    if (!overall_health) {
        std::cerr << "Health check detected issues with monitoring components" << std::endl;
    }
}

bool MetricsCollector::checkComponentHealth(const std::string& component_name,
                                          const std::function<bool()>& health_check) {
    try {
        bool healthy = health_check();
        if (!healthy) {
            std::cerr << "Health check failed for component: " << component_name << std::endl;
        }
        return healthy;
    } catch (const std::exception& e) {
        std::cerr << "Exception during health check for " << component_name 
                  << ": " << e.what() << std::endl;
        return false;
    }
}

MetricsCollector::SystemHealth MetricsCollector::getSystemHealth() const {
    SystemHealth health;
    
    // Component health
    health.prometheus_healthy = true; // Assume healthy if running
    health.latency_monitor_healthy = (latency_monitor_ != nullptr);
    health.data_integrity_healthy = (data_integrity_checker_ != nullptr);
    health.resource_monitor_healthy = (resource_monitor_ != nullptr);
    health.alert_manager_healthy = (alert_manager_ != nullptr);
    
    // Latency statistics
    if (latency_monitor_) {
        health.average_latency_microseconds = latency_monitor_->getAverageLatency();
        health.max_latency_microseconds = latency_monitor_->getMaxLatency();
        health.latency_violations = latency_monitor_->getViolationCount();
    } else {
        health.average_latency_microseconds = 0.0;
        health.max_latency_microseconds = 0.0;
        health.latency_violations = 0;
    }
    
    // Data integrity statistics
    if (data_integrity_checker_) {
        auto integrity_stats = data_integrity_checker_->getStatistics();
        health.total_messages_received = integrity_stats.total_messages_received;
        health.total_messages_lost = integrity_stats.total_messages_lost;
        health.data_loss_rate = integrity_stats.data_loss_rate;
    } else {
        health.total_messages_received = 0;
        health.total_messages_lost = 0;
        health.data_loss_rate = 0.0;
    }
    
    // Resource usage
    if (resource_monitor_) {
        auto current_usage = resource_monitor_->getCurrentUsage();
        health.current_cpu_usage = current_usage.cpu_percentage;
        health.current_memory_usage = current_usage.memory_percentage;
        health.current_disk_usage = current_usage.disk_percentage;
    } else {
        health.current_cpu_usage = 0.0;
        health.current_memory_usage = 0.0;
        health.current_disk_usage = 0.0;
    }
    
    // Alert statistics
    if (alert_manager_) {
        auto alert_stats = alert_manager_->getStatistics();
        health.total_alerts_sent = alert_stats.total_alerts_sent;
        health.total_alerts_failed = alert_stats.total_alerts_failed;
        health.alerts_rate_limited = alert_stats.alerts_rate_limited;
    } else {
        health.total_alerts_sent = 0;
        health.total_alerts_failed = 0;
        health.alerts_rate_limited = 0;
    }
    
    return health;
}

void MetricsCollector::resetAllStatistics() {
    if (latency_monitor_) {
        // Latency monitor doesn't have a reset method, but we could add one
    }
    
    if (data_integrity_checker_) {
        data_integrity_checker_->resetStatistics();
    }
    
    if (resource_monitor_) {
        resource_monitor_->resetStatistics();
    }
    
    if (alert_manager_) {
        alert_manager_->resetStatistics();
    }
}

void MetricsCollector::addEmailAlerts(const std::string& smtp_server, int port,
                                    const std::string& username, const std::string& password,
                                    const std::vector<std::string>& recipients) {
    if (alert_manager_) {
        alert_manager_->addChannel(std::make_unique<EmailAlertChannel>(
            smtp_server, port, username, password, recipients));
    }
}

void MetricsCollector::addWebhookAlerts(const std::string& webhook_url) {
    if (alert_manager_) {
        alert_manager_->addChannel(std::make_unique<WebhookAlertChannel>(webhook_url));
    }
}

void MetricsCollector::addSlackAlerts(const std::string& webhook_url, const std::string& channel) {
    if (alert_manager_) {
        alert_manager_->addChannel(std::make_unique<SlackAlertChannel>(webhook_url, channel));
    }
}

} // namespace monitoring