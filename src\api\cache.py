"""
Cache manager for API responses
"""

import json
import logging
from typing import Optional, Any, Dict
import redis.asyncio as aioredis
from datetime import datetime, timedelta

from .config import RedisConfig

logger = logging.getLogger(__name__)


class CacheManager:
    """Redis-based cache manager for API responses"""
    
    def __init__(self, redis_config: RedisConfig):
        self.config = redis_config
        self.redis: Optional[aioredis.Redis] = None
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "errors": 0
        }
    
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            redis_url = f"redis://{self.config.host}:{self.config.port}/{self.config.db}"
            if self.config.password:
                redis_url = f"redis://:{self.config.password}@{self.config.host}:{self.config.port}/{self.config.db}"
            
            self.redis = aioredis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=self.config.max_connections
            )
            
            # Test connection
            await self.redis.ping()
            logger.info("Cache manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cache manager: {e}")
            raise
    
    async def close(self):
        """Close Redis connection"""
        if self.redis:
            await self.redis.close()
    
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached value"""
        try:
            if not self.redis:
                return None
            
            cached_data = await self.redis.get(f"api_cache:{key}")
            if cached_data:
                self.stats["hits"] += 1
                return json.loads(cached_data)
            else:
                self.stats["misses"] += 1
                return None
                
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self.stats["errors"] += 1
            return None
    
    async def set(self, key: str, value: Dict[str, Any], expire: int = 300):
        """Set cached value with expiration"""
        try:
            if not self.redis:
                return False
            
            # Serialize value to JSON
            serialized_value = json.dumps(value, default=self._json_serializer)
            
            # Set with expiration
            await self.redis.setex(
                f"api_cache:{key}",
                expire,
                serialized_value
            )
            
            self.stats["sets"] += 1
            return True
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            self.stats["errors"] += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete cached value"""
        try:
            if not self.redis:
                return False
            
            result = await self.redis.delete(f"api_cache:{key}")
            return result > 0
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            self.stats["errors"] += 1
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern"""
        try:
            if not self.redis:
                return 0
            
            keys = await self.redis.keys(f"api_cache:{pattern}")
            if keys:
                return await self.redis.delete(*keys)
            return 0
            
        except Exception as e:
            logger.error(f"Cache clear pattern error for {pattern}: {e}")
            self.stats["errors"] += 1
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        cache_info = {}
        if self.redis:
            try:
                info = await self.redis.info()
                cache_info = {
                    "used_memory": info.get("used_memory_human", "N/A"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0)
                }
            except Exception as e:
                logger.error(f"Error getting Redis info: {e}")
        
        return {
            "hits": self.stats["hits"],
            "misses": self.stats["misses"],
            "sets": self.stats["sets"],
            "errors": self.stats["errors"],
            "hit_rate": round(hit_rate, 2),
            "redis_info": cache_info
        }
    
    def _json_serializer(self, obj):
        """Custom JSON serializer for datetime objects"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    async def warm_cache(self, symbols: list, exchanges: list):
        """Pre-warm cache with frequently accessed data"""
        logger.info("Starting cache warm-up process...")
        
        try:
            # This would typically pre-load popular symbols and recent data
            # Implementation depends on specific business requirements
            for symbol in symbols[:10]:  # Limit to top 10 symbols
                for exchange in exchanges:
                    # Pre-load recent tick data for popular symbols
                    cache_key = f"tick:{symbol}:{exchange}:recent"
                    # Would call data service to get recent data and cache it
                    logger.info(f"Warming cache for {symbol}@{exchange}")
            
            logger.info("Cache warm-up completed")
            
        except Exception as e:
            logger.error(f"Cache warm-up error: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check cache health"""
        try:
            if not self.redis:
                return {"status": "disconnected", "error": "Redis not initialized"}
            
            # Test basic operations
            test_key = "health_check_test"
            test_value = {"timestamp": datetime.utcnow().isoformat()}
            
            # Test set
            await self.redis.setex(test_key, 10, json.dumps(test_value))
            
            # Test get
            result = await self.redis.get(test_key)
            if not result:
                return {"status": "error", "error": "Failed to retrieve test data"}
            
            # Test delete
            await self.redis.delete(test_key)
            
            return {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "stats": await self.get_stats()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }