#!/bin/bash

# 金融数据服务系统Web管理界面停止脚本
# Financial Data Service System Web Admin Stop Script

set -e

echo "=== 停止金融数据服务系统Web管理界面 ==="
echo "=== Stopping Financial Data Service Web Admin ==="

# 检查Docker Compose是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装"
    echo "Error: Docker Compose is not installed"
    exit 1
fi

# 停止所有服务
echo "停止所有服务..."
docker-compose down

# 可选：清理数据卷（谨慎使用）
if [ "$1" = "--clean" ]; then
    echo "清理数据卷..."
    docker-compose down -v
    docker system prune -f
    echo "数据卷已清理"
fi

echo "服务已停止"
echo "Services stopped successfully"