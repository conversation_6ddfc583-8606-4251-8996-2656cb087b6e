cmake_minimum_required(VERSION 3.16)

# Find required packages
find_package(Protobuf REQUIRED)
find_package(gRPC REQUIRED)
find_package(Threads REQUIRED)

# Include SDK headers
include_directories(../include)
include_directories(../../../src/proto)

# Test framework (using simple assertions for this example)
# In a real project, you might use Google Test, Catch2, etc.

# Unit tests
add_executable(unit_tests
    unit_tests.cpp
    test_utils.cpp
)

target_link_libraries(unit_tests 
    financial_data_sdk
    gRPC::grpc++
    protobuf::libprotobuf
    Threads::Threads
)

# Integration tests
add_executable(integration_tests
    integration_tests.cpp
    test_utils.cpp
)

target_link_libraries(integration_tests 
    financial_data_sdk
    gRPC::grpc++
    protobuf::libprotobuf
    Threads::Threads
)

# Performance tests
add_executable(performance_tests
    performance_tests.cpp
    test_utils.cpp
)

target_link_libraries(performance_tests 
    financial_data_sdk
    gRPC::grpc++
    protobuf::libprotobuf
    Threads::Threads
)

# Enable testing
enable_testing()

add_test(NAME UnitTests COMMAND unit_tests)
add_test(NAME IntegrationTests COMMAND integration_tests)
add_test(NAME PerformanceTests COMMAND performance_tests)