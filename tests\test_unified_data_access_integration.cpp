#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <chrono>
#include <thread>

#include "../src/storage/unified_data_access.h"
#include "../src/storage/storage_layer_selector.h"
#include "../src/storage/query_performance_optimizer.h"

using namespace financial_data;
using namespace testing;

// Mock storage classes for integration testing
class MockRedisHotStorage : public RedisHotStorage {
public:
    MockRedisHotStorage() : RedisHotStorage(RedisConfig{}) {}
    
    MOCK_METHOD(bool, GetLatestTick, (const std::string& symbol, StandardTick& tick), (override));
    MOCK_METHOD(bool, GetLatestLevel2, (const std::string& symbol, Level2Data& level2), (override));
    MOCK_METHOD(QueryResult, QueryTicks, (const std::string& symbol, const QueryOptions& options), (override));
    MOCK_METHOD(QueryResult, QueryLevel2, (const std::string& symbol, const QueryOptions& options), (override));
    MOCK_METHOD(std::unordered_map<std::string, StandardTick>, GetLatestTicks, (const std::vector<std::string>& symbols), (override));
    MOCK_METHOD(StorageStats, GetStats, (), (const, override));
};

class MockClickHouseStorage : public ClickHouseStorage {
public:
    MockClickHouseStorage() : ClickHouseStorage(ClickHouseConfig{}) {}
    
    MOCK_METHOD(bool, IsConnected, (), (const, override));
    MOCK_METHOD(QueryResult<StandardizedTick>, QueryTickData, 
                (const std::string& symbol, const std::string& exchange,
                 int64_t start_timestamp, int64_t end_timestamp,
                 size_t limit, const std::string& cursor), (override));
    MOCK_METHOD(PerformanceMetrics, GetMetrics, (), (const, override));
};

class MockColdDataStorage : public storage::ColdDataStorage {
public:
    MockColdDataStorage() : storage::ColdDataStorage(storage::ColdStorageConfig{}) {}
    
    MOCK_METHOD(std::future<storage::TickDataBatch>, RetrieveData,
                (const std::string& symbol, const std::string& exchange,
                 const std::chrono::system_clock::time_point& start_time,
                 const std::chrono::system_clock::time_point& end_time), (override));
};

class UnifiedDataAccessIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建mock存储层
        mock_hot_storage_ = std::make_shared<MockRedisHotStorage>();
        mock_warm_storage_ = std::make_shared<MockClickHouseStorage>();
        mock_cold_storage_ = std::make_shared<MockColdDataStorage>();
        
        // 创建存储层选择器
        StorageSelectionConfig selector_config;
        selector_config.hot_storage_days = 7;
        selector_config.warm_storage_days = 730;
        selector_config.enable_automatic_failover = true;
        layer_selector_ = std::make_shared<StorageLayerSelector>(selector_config);
        
        // 创建查询性能优化器
        QueryOptimizationConfig optimizer_config;
        optimizer_config.max_cache_size = 1000;
        optimizer_config.enable_batch_optimization = true;
        optimizer_config.enable_pagination = true;
        optimizer_config.enable_prefetching = true;
        query_optimizer_ = std::make_shared<QueryPerformanceOptimizer>(optimizer_config);
        
        // 创建统一数据访问接口
        StorageLayerConfig access_config;
        access_config.hot_storage_days = 7;
        access_config.warm_storage_days = 730;
        access_config.enable_query_cache = true;
        unified_access_ = std::make_shared<UnifiedDataAccessInterface>(access_config);
        
        // 初始化所有组件
        ASSERT_TRUE(unified_access_->Initialize(
            mock_hot_storage_, mock_warm_storage_, mock_cold_storage_, 
            layer_selector_, query_optimizer_));
    }
    
    void TearDown() override {
        unified_access_->Shutdown();
    }
    
    StandardTick CreateTestTick(const std::string& symbol, int64_t timestamp_ns, double price) {
        StandardTick tick;
        tick.symbol = symbol;
        tick.exchange = "TEST";
        tick.timestamp_ns = timestamp_ns;
        tick.last_price = price;
        tick.volume = 1000;
        tick.turnover = price * 1000;
        tick.bids[0] = PriceLevel(price - 0.01, 500);
        tick.asks[0] = PriceLevel(price + 0.01, 500);
        return tick;
    }
    
    int64_t GetCurrentTimestampNs() {
        auto now = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::nanoseconds>(
            now.time_since_epoch()).count();
    }
    
    int64_t DaysToNanoseconds(int days) {
        return static_cast<int64_t>(days) * 24 * 3600 * 1000000000LL;
    }

protected:
    std::shared_ptr<MockRedisHotStorage> mock_hot_storage_;
    std::shared_ptr<MockClickHouseStorage> mock_warm_storage_;
    std::shared_ptr<MockColdDataStorage> mock_cold_storage_;
    std::shared_ptr<StorageLayerSelector> layer_selector_;
    std::shared_ptr<QueryPerformanceOptimizer> query_optimizer_;
    std::shared_ptr<UnifiedDataAccessInterface> unified_access_;
};

// 测试完整的热存储查询流程
TEST_F(UnifiedDataAccessIntegrationTest, HotStorageQueryIntegrationTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(1); // 1天前（热存储）
    int64_t end_time = current_time;
    
    QueryRequest request(symbol, start_time, end_time, 100);
    request.exchange = "TEST";
    
    // 准备热存储mock数据
    QueryResult hot_result;
    hot_result.ticks.push_back(CreateTestTick(symbol, start_time + 1000000, 100.0));
    hot_result.ticks.push_back(CreateTestTick(symbol, start_time + 2000000, 100.1));
    hot_result.has_more = false;
    
    // 设置存储层健康状态
    RedisHotStorage::StorageStats redis_stats;
    redis_stats.active_connections = 5;
    redis_stats.total_queries = 10;
    
    EXPECT_CALL(*mock_hot_storage_, GetStats())
        .WillRepeatedly(Return(redis_stats));
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillOnce(Return(hot_result));
    
    // 执行优化查询
    auto future = unified_access_->OptimizedQuery(request);
    auto response = future.get();
    
    EXPECT_EQ(response.ticks.size(), 2);
    EXPECT_EQ(response.ticks[0].symbol, symbol);
    EXPECT_EQ(response.ticks[0].last_price, 100.0);
    EXPECT_EQ(response.ticks[1].last_price, 100.1);
    
    // 验证缓存生效
    EXPECT_GT(query_optimizer_->GetCacheSize(), 0);
}

// 测试温存储查询流程
TEST_F(UnifiedDataAccessIntegrationTest, WarmStorageQueryIntegrationTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(30); // 30天前（温存储）
    int64_t end_time = current_time - DaysToNanoseconds(10);   // 10天前
    
    QueryRequest request(symbol, start_time, end_time, 100);
    request.exchange = "TEST";
    
    // 准备温存储mock数据
    QueryResult<StandardizedTick> warm_result;
    StandardizedTick standardized_tick;
    standardized_tick.symbol = symbol;
    standardized_tick.exchange = "TEST";
    standardized_tick.timestamp_ns = start_time + 1000000;
    standardized_tick.last_price = 100.0;
    standardized_tick.volume = 1000;
    standardized_tick.bid_prices = {99.9};
    standardized_tick.bid_volumes = {500};
    standardized_tick.ask_prices = {100.1};
    standardized_tick.ask_volumes = {500};
    
    warm_result.data.push_back(standardized_tick);
    warm_result.has_more = false;
    warm_result.total_rows = 1;
    
    // 设置存储层健康状态
    ClickHouseStorage::PerformanceMetrics ch_metrics;
    ch_metrics.total_queries = 20;
    ch_metrics.failed_queries = 1;
    
    EXPECT_CALL(*mock_warm_storage_, IsConnected())
        .WillRepeatedly(Return(true));
    EXPECT_CALL(*mock_warm_storage_, GetMetrics())
        .WillRepeatedly(Return(ch_metrics));
    EXPECT_CALL(*mock_warm_storage_, QueryTickData(symbol, "TEST", start_time, end_time, 100, ""))
        .WillOnce(Return(warm_result));
    
    // 执行查询
    auto future = unified_access_->QueryData(request);
    auto response = future.get();
    
    EXPECT_EQ(response.storage_source, "warm");
    EXPECT_EQ(response.ticks.size(), 1);
    EXPECT_EQ(response.ticks[0].symbol, symbol);
    EXPECT_EQ(response.ticks[0].last_price, 100.0);
}

// 测试跨层查询集成
TEST_F(UnifiedDataAccessIntegrationTest, MultiLayerQueryIntegrationTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(30); // 温存储范围
    int64_t end_time = current_time - DaysToNanoseconds(1);    // 热存储范围
    
    QueryRequest request(symbol, start_time, end_time, 200);
    request.exchange = "TEST";
    
    // 准备热存储mock数据
    QueryResult hot_result;
    hot_result.ticks.push_back(CreateTestTick(symbol, current_time - DaysToNanoseconds(1), 101.0));
    
    // 准备温存储mock数据
    QueryResult<StandardizedTick> warm_result;
    StandardizedTick standardized_tick;
    standardized_tick.symbol = symbol;
    standardized_tick.exchange = "TEST";
    standardized_tick.timestamp_ns = current_time - DaysToNanoseconds(15);
    standardized_tick.last_price = 100.0;
    standardized_tick.volume = 1000;
    warm_result.data.push_back(standardized_tick);
    
    // 设置存储层健康状态
    RedisHotStorage::StorageStats redis_stats;
    redis_stats.active_connections = 5;
    
    ClickHouseStorage::PerformanceMetrics ch_metrics;
    ch_metrics.total_queries = 20;
    ch_metrics.failed_queries = 0;
    
    EXPECT_CALL(*mock_hot_storage_, GetStats())
        .WillRepeatedly(Return(redis_stats));
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillOnce(Return(hot_result));
    
    EXPECT_CALL(*mock_warm_storage_, IsConnected())
        .WillRepeatedly(Return(true));
    EXPECT_CALL(*mock_warm_storage_, GetMetrics())
        .WillRepeatedly(Return(ch_metrics));
    EXPECT_CALL(*mock_warm_storage_, QueryTickData(symbol, "TEST", _, _, _, _))
        .WillOnce(Return(warm_result));
    
    // 执行跨层查询
    auto future = unified_access_->QueryData(request);
    auto response = future.get();
    
    EXPECT_EQ(response.storage_source, "mixed");
    EXPECT_EQ(response.ticks.size(), 2);
    
    // 验证数据按时间戳排序
    EXPECT_LT(response.ticks[0].timestamp_ns, response.ticks[1].timestamp_ns);
}

// 测试批量查询集成
TEST_F(UnifiedDataAccessIntegrationTest, BatchQueryIntegrationTest) {
    std::vector<QueryRequest> requests;
    int64_t current_time = GetCurrentTimestampNs();
    
    // 创建多个查询请求
    for (int i = 0; i < 3; ++i) {
        std::string symbol = "TEST" + std::to_string(i);
        QueryRequest request(symbol, current_time - DaysToNanoseconds(1), current_time, 50);
        request.exchange = "TEST";
        requests.push_back(request);
    }
    
    // 为每个查询准备mock数据
    RedisHotStorage::StorageStats redis_stats;
    redis_stats.active_connections = 5;
    
    EXPECT_CALL(*mock_hot_storage_, GetStats())
        .WillRepeatedly(Return(redis_stats));
    
    for (int i = 0; i < 3; ++i) {
        std::string symbol = "TEST" + std::to_string(i);
        QueryResult hot_result;
        hot_result.ticks.push_back(CreateTestTick(symbol, current_time - 1000000, 100.0 + i));
        
        EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
            .WillOnce(Return(hot_result));
    }
    
    // 执行批量查询
    auto future = unified_access_->BatchQuery(requests);
    auto responses = future.get();
    
    EXPECT_EQ(responses.size(), 3);
    for (size_t i = 0; i < responses.size(); ++i) {
        EXPECT_EQ(responses[i].ticks.size(), 1);
        EXPECT_EQ(responses[i].ticks[0].symbol, "TEST" + std::to_string(i));
        EXPECT_EQ(responses[i].ticks[0].last_price, 100.0 + i);
    }
}

// 测试分页查询集成
TEST_F(UnifiedDataAccessIntegrationTest, PaginatedQueryIntegrationTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    
    QueryRequest request(symbol, current_time - DaysToNanoseconds(1), current_time, 50);
    request.exchange = "TEST";
    
    // 准备包含更多数据的热存储响应
    QueryResult hot_result;
    for (int i = 0; i < 100; ++i) {
        hot_result.ticks.push_back(CreateTestTick(symbol, current_time - 1000000 + i * 1000, 100.0 + i * 0.01));
    }
    hot_result.has_more = true;
    
    RedisHotStorage::StorageStats redis_stats;
    redis_stats.active_connections = 5;
    
    EXPECT_CALL(*mock_hot_storage_, GetStats())
        .WillRepeatedly(Return(redis_stats));
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillOnce(Return(hot_result));
    
    // 执行分页查询
    auto future = unified_access_->PaginatedQuery(request);
    auto response = future.get();
    
    EXPECT_LE(response.ticks.size(), 50); // 应该限制在页面大小内
    EXPECT_EQ(response.ticks[0].symbol, symbol);
    
    // 如果有更多数据，应该有下一页游标
    if (response.has_more) {
        EXPECT_FALSE(response.next_cursor.empty());
    }
}

// 测试故障转移集成
TEST_F(UnifiedDataAccessIntegrationTest, FailoverIntegrationTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(1); // 热存储范围
    int64_t end_time = current_time;
    
    QueryRequest request(symbol, start_time, end_time, 100);
    request.exchange = "TEST";
    
    // 模拟热存储故障
    RedisHotStorage::StorageStats unhealthy_redis_stats;
    unhealthy_redis_stats.active_connections = 0; // 无连接表示不健康
    
    // 准备温存储作为故障转移目标
    QueryResult<StandardizedTick> warm_result;
    StandardizedTick standardized_tick;
    standardized_tick.symbol = symbol;
    standardized_tick.exchange = "TEST";
    standardized_tick.timestamp_ns = start_time + 1000000;
    standardized_tick.last_price = 100.0;
    standardized_tick.volume = 1000;
    warm_result.data.push_back(standardized_tick);
    
    ClickHouseStorage::PerformanceMetrics ch_metrics;
    ch_metrics.total_queries = 20;
    ch_metrics.failed_queries = 0;
    
    EXPECT_CALL(*mock_hot_storage_, GetStats())
        .WillRepeatedly(Return(unhealthy_redis_stats));
    
    EXPECT_CALL(*mock_warm_storage_, IsConnected())
        .WillRepeatedly(Return(true));
    EXPECT_CALL(*mock_warm_storage_, GetMetrics())
        .WillRepeatedly(Return(ch_metrics));
    EXPECT_CALL(*mock_warm_storage_, QueryTickData(symbol, "TEST", _, _, _, _))
        .WillOnce(Return(warm_result));
    
    // 等待健康检查更新
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 执行查询，应该自动故障转移到温存储
    auto future = unified_access_->QueryData(request);
    auto response = future.get();
    
    EXPECT_EQ(response.ticks.size(), 1);
    EXPECT_EQ(response.ticks[0].symbol, symbol);
    EXPECT_EQ(response.ticks[0].last_price, 100.0);
}

// 测试性能监控集成
TEST_F(UnifiedDataAccessIntegrationTest, PerformanceMonitoringIntegrationTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    
    QueryRequest request(symbol, current_time - DaysToNanoseconds(1), current_time, 50);
    request.exchange = "TEST";
    
    // 准备mock数据
    QueryResult hot_result;
    hot_result.ticks.push_back(CreateTestTick(symbol, current_time - 1000000, 100.0));
    
    RedisHotStorage::StorageStats redis_stats;
    redis_stats.active_connections = 5;
    redis_stats.total_queries = 1;
    
    EXPECT_CALL(*mock_hot_storage_, GetStats())
        .WillRepeatedly(Return(redis_stats));
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillOnce(Return(hot_result));
    
    // 执行查询
    auto future = unified_access_->OptimizedQuery(request);
    auto response = future.get();
    
    EXPECT_EQ(response.ticks.size(), 1);
    
    // 检查统一访问接口的性能指标
    auto access_metrics = unified_access_->GetMetrics();
    EXPECT_GT(access_metrics.total_queries.load(), 0);
    EXPECT_GT(access_metrics.hot_storage_queries.load(), 0);
    
    // 检查查询优化器的性能指标
    auto optimizer_metrics = query_optimizer_->GetMetrics();
    EXPECT_GT(optimizer_metrics.total_queries.load(), 0);
    
    // 检查存储层选择器的统计信息
    auto selector_stats = layer_selector_->GetStatistics();
    EXPECT_GT(selector_stats.total_selections.load(), 0);
    EXPECT_GT(selector_stats.hot_selections.load(), 0);
}

// 测试健康检查集成
TEST_F(UnifiedDataAccessIntegrationTest, HealthCheckIntegrationTest) {
    // 设置所有存储层为健康状态
    RedisHotStorage::StorageStats redis_stats;
    redis_stats.active_connections = 5;
    
    ClickHouseStorage::PerformanceMetrics ch_metrics;
    ch_metrics.total_queries = 20;
    ch_metrics.failed_queries = 1;
    
    EXPECT_CALL(*mock_hot_storage_, GetStats())
        .WillRepeatedly(Return(redis_stats));
    EXPECT_CALL(*mock_warm_storage_, IsConnected())
        .WillRepeatedly(Return(true));
    EXPECT_CALL(*mock_warm_storage_, GetMetrics())
        .WillRepeatedly(Return(ch_metrics));
    
    // 触发健康检查
    unified_access_->TriggerHealthCheck();
    
    // 等待健康检查完成
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 检查健康状态
    auto health_status = unified_access_->GetHealthStatus();
    EXPECT_TRUE(health_status.overall_healthy);
    EXPECT_TRUE(health_status.hot_storage_healthy);
    EXPECT_TRUE(health_status.warm_storage_healthy);
    EXPECT_TRUE(health_status.cold_storage_healthy);
    
    // 检查存储层选择器的健康状态
    auto hot_status = layer_selector_->GetLayerStatus(StorageLayer::HOT);
    auto warm_status = layer_selector_->GetLayerStatus(StorageLayer::WARM);
    auto cold_status = layer_selector_->GetLayerStatus(StorageLayer::COLD);
    
    EXPECT_TRUE(hot_status.IsAvailable());
    EXPECT_TRUE(warm_status.IsAvailable());
    EXPECT_TRUE(cold_status.IsAvailable());
}

// 测试配置管理集成
TEST_F(UnifiedDataAccessIntegrationTest, ConfigurationManagementIntegrationTest) {
    // 更新统一访问接口配置
    StorageLayerConfig new_access_config;
    new_access_config.hot_storage_days = 14;
    new_access_config.warm_storage_days = 1460;
    new_access_config.cache_size = 5000;
    
    EXPECT_TRUE(unified_access_->UpdateConfig(new_access_config));
    
    // 更新存储层选择器配置
    StorageSelectionConfig new_selector_config;
    new_selector_config.hot_storage_days = 14;
    new_selector_config.warm_storage_days = 1460;
    new_selector_config.strategy = StorageSelectionStrategy::PERFORMANCE_BASED;
    
    EXPECT_TRUE(layer_selector_->UpdateConfig(new_selector_config));
    
    // 更新查询优化器配置
    QueryOptimizationConfig new_optimizer_config;
    new_optimizer_config.max_cache_size = 2000;
    new_optimizer_config.cache_strategy = CacheStrategy::ADAPTIVE;
    new_optimizer_config.enable_prefetching = true;
    
    EXPECT_TRUE(query_optimizer_->UpdateConfig(new_optimizer_config));
    
    // 验证配置更新
    auto current_access_config = unified_access_->GetConfig();
    EXPECT_EQ(current_access_config.hot_storage_days, 14);
    EXPECT_EQ(current_access_config.warm_storage_days, 1460);
    
    auto current_selector_config = layer_selector_->GetConfig();
    EXPECT_EQ(current_selector_config.strategy, StorageSelectionStrategy::PERFORMANCE_BASED);
    
    auto current_optimizer_config = query_optimizer_->GetConfig();
    EXPECT_EQ(current_optimizer_config.cache_strategy, CacheStrategy::ADAPTIVE);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}