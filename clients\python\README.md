# Financial Data SDK - Python Client

A comprehensive Python SDK for accessing real-time and historical financial market data with advanced features including pandas/numpy integration, technical indicators, async operations, and data caching.

## Features

### 🚀 Core Features
- **Pandas & NumPy Integration**: Native support for pandas DataFrames and numpy arrays
- **Async Operations**: Full asyncio support with async/await patterns
- **Technical Indicators**: 15+ built-in technical indicators optimized for performance
- **Data Caching**: Intelligent caching with LRU and time-based expiration
- **Batch Operations**: Efficient batch data retrieval with parallel processing
- **Load Balancing**: Client-side load balancing with automatic failover
- **Data Validation**: Comprehensive data quality checks and outlier detection

### 📊 Data Types Supported
- **Tick Data**: Real-time and historical tick-by-tick data
- **K-line/Candlestick Data**: OHLCV data with multiple timeframes
- **Level 2 Data**: Market depth with bid/ask levels
- **Volume Data**: Trading volume and turnover information

### 📈 Technical Indicators
- Moving Averages (SMA, EMA)
- Momentum Indicators (RSI, MACD, Stochastic)
- Volatility Indicators (Bollinger Bands, ATR)
- Volume Indicators (OBV, VWAP)
- Oscillators (Williams %R, CCI)
- Custom indicator support

## Installation

```bash
# Install from requirements
pip install -r requirements.txt

# Or install individual packages
pip install pandas numpy scipy matplotlib seaborn
pip install grpcio grpcio-tools protobuf
pip install aiogrpc websockets aiohttp
pip install jupyter plotly
```

## Quick Start

### Synchronous Client

```python
from financial_data_sdk import FinancialDataClient
from financial_data_sdk.indicators import TechnicalIndicators

# Initialize client
servers = ["localhost:50051", "localhost:50052"]
client = FinancialDataClient(servers, enable_cache=True)

# Get historical data as pandas DataFrame
df = client.get_tick_data(
    symbol="AAPL",
    exchange="NASDAQ",
    start_time="2024-01-01",
    end_time="2024-01-02",
    limit=1000,
    as_dataframe=True
)

# Calculate technical indicators
rsi = TechnicalIndicators.rsi(df['last_price'])
sma_20 = TechnicalIndicators.sma(df['last_price'], 20)

# Stream real-time data
def tick_handler(tick):
    print(f"Tick: {tick.symbol} @ {tick.last_price}")

future = client.stream_tick_data(
    symbols=["AAPL", "GOOGL"],
    callback=tick_handler
)

client.close()
```

### Asynchronous Client

```python
import asyncio
from financial_data_sdk import AsyncFinancialDataClient

async def main():
    servers = ["localhost:50051", "localhost:50052"]
    
    async with AsyncFinancialDataClient(servers) as client:
        # Get data asynchronously
        df = await client.get_tick_data("AAPL", limit=100)
        
        # Stream data with async generator
        async for tick in client.stream_tick_data(["AAPL"]):
            print(f"Async tick: {tick.symbol} @ {tick.last_price}")
            break  # Just one example

asyncio.run(main())
```

## Advanced Usage

### Batch Data Retrieval

```python
# Define multiple data requests
requests = [
    {
        'id': 'aapl_tick',
        'type': 'tick',
        'symbol': 'AAPL',
        'limit': 1000
    },
    {
        'id': 'googl_kline',
        'type': 'kline',
        'symbol': 'GOOGL',
        'period': '1h',
        'limit': 500
    }
]

# Execute in parallel
results = client.get_batch_data(requests, parallel=True)
for req_id, data in results.items():
    print(f"{req_id}: {len(data)} records")
```

### Technical Analysis

```python
from financial_data_sdk.indicators import calculate_all_indicators, IndicatorAnalyzer

# Calculate all indicators at once
df_with_indicators = calculate_all_indicators(kline_df)

# Generate trading signals
signals_df = IndicatorAnalyzer.generate_signals(
    df_with_indicators,
    indicators=['rsi', 'macd', 'bollinger'],
    thresholds={
        'rsi_oversold': 30,
        'rsi_overbought': 70
    }
)

# Backtest strategy
backtest_result = IndicatorAnalyzer.backtest_strategy(
    signals_df,
    signals_df['rsi_buy'],
    signals_df['rsi_sell'],
    initial_capital=100000
)

print(f"Strategy return: {backtest_result['total_return']:.2%}")
```

### Data Format Conversions

```python
from financial_data_sdk.data_models import NumpyArrayBuilder, DataFrameBuilder

# Convert to numpy arrays
tick_array = NumpyArrayBuilder.from_tick_list(tick_data_list)
ohlcv_array = NumpyArrayBuilder.ohlcv_array(kline_data_list)

# Convert to pandas DataFrames
tick_df = DataFrameBuilder.from_tick_list(tick_data_list)
kline_df = DataFrameBuilder.from_kline_list(kline_data_list)
```

### Caching and Performance

```python
from financial_data_sdk.cache import PersistentCache, BatchCache

# Use persistent cache
persistent_cache = PersistentCache(cache_dir="./data_cache")
client = FinancialDataClient(servers, cache=persistent_cache)

# Batch cache operations
batch_cache = BatchCache(client.cache)
batch_cache.begin_batch()
# ... multiple cache operations ...
batch_cache.commit_batch()

# Performance profiling
from financial_data_sdk.utils import PerformanceProfiler

profiler = PerformanceProfiler()
with profiler.time_operation("data_retrieval"):
    data = client.get_tick_data("AAPL", limit=1000)

stats = profiler.get_stats()
print(f"Average time: {stats['timings']['data_retrieval']['average']:.3f}s")
```

## API Reference

### FinancialDataClient

#### Methods

- `get_tick_data(symbol, exchange=None, start_time=None, end_time=None, limit=1000, as_dataframe=True)`
- `get_kline_data(symbol, period="1m", exchange=None, start_time=None, end_time=None, limit=1000, as_dataframe=True)`
- `get_level2_data(symbol, exchange=None, depth=10, start_time=None, end_time=None, limit=1000, as_dataframe=True)`
- `stream_tick_data(symbols, exchange=None, callback=None, buffer_size=1000)`
- `get_batch_data(requests, as_dataframe=True, parallel=True)`
- `health_check()`
- `get_server_stats()`
- `clear_cache()`
- `close()`

### AsyncFinancialDataClient

#### Methods

- `async get_tick_data(...)` - Async version of get_tick_data
- `async get_kline_data(...)` - Async version of get_kline_data
- `async stream_tick_data(symbols, exchange=None, buffer_size=1000)` - Returns AsyncIterator
- `async get_concurrent_data(requests, max_concurrent=10, as_dataframe=True)`
- `async health_check()`
- `async close()`

### TechnicalIndicators

#### Static Methods

- `sma(data, window)` - Simple Moving Average
- `ema(data, window, alpha=None)` - Exponential Moving Average
- `rsi(data, window=14)` - Relative Strength Index
- `macd(data, fast=12, slow=26, signal=9)` - MACD
- `bollinger_bands(data, window=20, num_std=2)` - Bollinger Bands
- `stochastic(high, low, close, k_window=14, d_window=3)` - Stochastic Oscillator
- `atr(high, low, close, window=14)` - Average True Range
- `williams_r(high, low, close, window=14)` - Williams %R
- `cci(high, low, close, window=20)` - Commodity Channel Index
- `obv(close, volume)` - On-Balance Volume
- `vwap(high, low, close, volume)` - Volume Weighted Average Price

### Data Models

#### TickData
```python
@dataclass
class TickData:
    timestamp: int  # nanoseconds
    symbol: str
    exchange: str
    last_price: float
    volume: int
    turnover: float
    # ... additional fields
```

#### KlineData
```python
@dataclass
class KlineData:
    timestamp: int  # nanoseconds
    symbol: str
    exchange: str
    period: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    turnover: float
```

#### Level2Data
```python
@dataclass
class Level2Data:
    timestamp: int  # nanoseconds
    symbol: str
    exchange: str
    bid_prices: List[float]
    bid_volumes: List[int]
    ask_prices: List[float]
    ask_volumes: List[int]
```

## Examples and Tutorials

### Jupyter Notebooks

1. **[Getting Started](notebooks/01_Getting_Started.ipynb)** - Basic usage and features
2. **[Technical Analysis](notebooks/02_Technical_Analysis.ipynb)** - Advanced technical analysis

### Python Examples

1. **[Basic Usage](examples/basic_usage.py)** - Fundamental operations
2. **[Advanced Usage](examples/advanced_usage.py)** - Complex scenarios and strategies

### Running Examples

```bash
# Basic usage example
python examples/basic_usage.py

# Advanced usage with visualization
python examples/advanced_usage.py

# Start Jupyter notebook
jupyter notebook notebooks/
```

## Configuration

### Client Configuration

```python
client = FinancialDataClient(
    servers=["localhost:50051", "localhost:50052"],
    max_retries=3,
    cache_size=10000,
    enable_cache=True
)
```

### Cache Configuration

```python
from financial_data_sdk.cache import DataCache

cache = DataCache(
    max_size=10000,
    ttl_seconds=300  # 5 minutes
)
```

### Logging Configuration

```python
from financial_data_sdk.utils import setup_logging

setup_logging(level='INFO')
```

## Performance Optimization

### Best Practices

1. **Use Caching**: Enable caching for repeated queries
2. **Batch Operations**: Use batch requests for multiple symbols
3. **Async Operations**: Use async client for I/O intensive operations
4. **Data Format**: Choose appropriate data format (DataFrame vs numpy array)
5. **Connection Pooling**: Reuse client connections

### Performance Benchmarks

Typical performance on modern hardware:

- **Data Retrieval**: ~10ms for 1000 tick records
- **Technical Indicators**: ~1ms for RSI on 1000 data points
- **Batch Operations**: ~50ms for 10 concurrent requests
- **Cache Hit**: ~0.1ms for cached data retrieval

## Error Handling

### Exception Types

```python
from financial_data_sdk.exceptions import (
    ConnectionError,
    DataError,
    TimeoutError,
    AuthenticationError,
    RateLimitError
)

try:
    data = client.get_tick_data("AAPL")
except ConnectionError as e:
    print(f"Connection failed: {e}")
except DataError as e:
    print(f"Data error: {e}")
```

### Retry Logic

The SDK includes automatic retry logic with exponential backoff:

```python
client = FinancialDataClient(
    servers=servers,
    max_retries=3  # Will retry up to 3 times
)
```

## Testing

### Unit Tests

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_client.py

# Run with coverage
python -m pytest tests/ --cov=financial_data_sdk
```

### Integration Tests

```bash
# Requires running server
python -m pytest tests/integration/
```

## Contributing

### Development Setup

```bash
# Clone repository
git clone <repository-url>
cd clients/python

# Install development dependencies
pip install -r requirements-dev.txt

# Install in development mode
pip install -e .
```

### Code Style

```bash
# Format code
black financial_data_sdk/

# Check linting
flake8 financial_data_sdk/

# Type checking
mypy financial_data_sdk/
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

### Documentation
- [API Documentation](docs/api.md)
- [Examples](examples/)
- [Jupyter Notebooks](notebooks/)

### Community
- GitHub Issues: Report bugs and request features
- Discussions: Ask questions and share ideas

### Commercial Support
For commercial support and custom development, please contact our team.

---

## Changelog

### Version 1.0.0
- Initial release
- Pandas/NumPy integration
- Technical indicators
- Async support
- Caching system
- Batch operations
- Load balancing

---

**Happy Trading! 📈**