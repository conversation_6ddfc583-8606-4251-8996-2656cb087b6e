# 开发测试环境 Docker Compose 配置
# 使用方法: docker-compose -f docker-compose.dev.yml up -d
# version: '3.8'  # 移除过时的 version 属性

services:
  # 核心数据库服务
  redis:
    image: redis:7-alpine
    container_name: financial-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    networks:
      - financial-dev-network
    restart: unless-stopped

  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: financial-clickhouse-dev
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - ./config/clickhouse-init-dev.sql:/docker-entrypoint-initdb.d/init.sql
      - clickhouse_dev_data:/var/lib/clickhouse
    environment:
      CLICKHOUSE_DB: market_data
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: password123
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    networks:
      - financial-dev-network
    restart: unless-stopped

  # 消息队列 - 简化配置
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: financial-kafka-dev
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 1
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
      KAFKA_LOG_RETENTION_HOURS: 24
      KAFKA_LOG_SEGMENT_BYTES: 1073741824
    depends_on:
      - zookeeper
    networks:
      - financial-dev-network
    restart: unless-stopped

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: financial-zookeeper-dev
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_INIT_LIMIT: 5
      ZOOKEEPER_SYNC_LIMIT: 2
    networks:
      - financial-dev-network
    restart: unless-stopped

  # 对象存储
  minio:
    image: minio/minio:latest
    container_name: financial-minio-dev
    ports:
      - "9001:9001"
      - "9002:9002"
    volumes:
      - minio_dev_data:/data
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password123
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
    command: server /data --console-address ":9001" --address ":9002"
    networks:
      - financial-dev-network
    restart: unless-stopped

volumes:
  redis_dev_data:
    driver: local
  clickhouse_dev_data:
    driver: local
  minio_dev_data:
    driver: local

networks:
  financial-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16