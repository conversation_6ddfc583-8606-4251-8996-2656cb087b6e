# PyTDX数据采集指南

本指南介绍如何使用PyTDX进行金融数据采集，包括代码表更新和历史行情数据采集。

## 📋 功能概述

### 核心功能
- ✅ **代码表更新**: 获取股票、指数、期货、基金、债券的最新代码表
- ✅ **历史数据采集**: 采集日线、分钟线等历史行情数据
- ✅ **数据质量控制**: 自动验证和去重数据
- ✅ **多种存储格式**: 支持JSON、CSV、Parquet格式
- ✅ **数据库存储**: 自动存储到Redis和ClickHouse
- ✅ **详细日志**: 完整的采集过程记录

### 支持的数据类型
- **股票数据**: 沪深A股的K线数据、实时行情、分笔数据
- **指数数据**: 主要指数的K线数据和实时行情
- **期货数据**: 各交易所期货合约数据
- **基金数据**: 基金净值和基本信息
- **债券数据**: 债券价格和基本信息

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：
```bash
pip install pytdx pandas numpy asyncio
```

### 2. 基础使用

#### 方式一：使用批处理脚本（推荐）
```bash
# Windows
run_pytdx_collection.bat

# Linux/Mac
chmod +x run_pytdx_collection.sh
./run_pytdx_collection.sh
```

#### 方式二：分步执行

**第一步：更新代码表**
```bash
python update_symbol_lists.py
```

**第二步：采集历史数据**
```bash
python collect_historical_data.py
```

#### 方式三：使用增强版采集器
```bash
python pytdx_enhanced_collector.py
```

## 📁 脚本说明

### 1. update_symbol_lists.py
**功能**: 更新各市场的代码表
- 获取股票、指数、期货、基金、债券代码表
- 保存为JSON和CSV格式
- 显示代码表统计信息

**输出文件**:
```
data/symbols/
├── stock_symbols_20240109_143022.json
├── stock_symbols_20240109_143022.csv
├── index_symbols_20240109_143022.json
├── index_symbols_20240109_143022.csv
└── ...
```

### 2. collect_historical_data.py
**功能**: 根据代码表采集历史数据
- 支持多种K线周期（日线、60分钟、30分钟等）
- 可配置回溯天数
- 自动数据质量控制和去重

**输出文件**:
```
data/historical/
├── stock/
│   ├── 000001/
│   │   ├── 000001_daily.json
│   │   ├── 000001_60min.json
│   │   └── ...
│   └── ...
└── index/
    └── ...
```

### 3. pytdx_enhanced_collector.py
**功能**: 增强版数据采集器
- 支持配置文件管理
- 多种输出格式
- 详细的日志和统计
- 自动存储到数据库

### 4. pytdx_data_collector.py
**功能**: 完整的数据采集管理器
- 集成代码表更新和数据采集
- 支持批量处理
- 详细的统计信息

## ⚙️ 配置说明

### pytdx_config.json 配置文件

```json
{
  "collection": {
    "symbol_types": ["stock", "index", "futures", "fund", "bond"],
    "data_types": ["stock", "index"],
    "periods": ["daily", "60min", "30min", "15min", "5min"],
    "days_back": 30,
    "max_symbols_per_type": null,
    "request_interval": 0.05,
    "batch_size": 100
  },
  "servers": {
    "hq_servers": [
      {"ip": "**************", "port": 7709, "name": "通达信主站"}
    ]
  },
  "storage": {
    "save_to_files": true,
    "save_to_database": true,
    "output_directory": "data/historical",
    "file_formats": ["json", "csv", "parquet"]
  }
}
```

### 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `symbol_types` | 要更新的代码表类型 | `["stock", "index"]` |
| `data_types` | 要采集的数据类型 | `["stock", "index"]` |
| `periods` | K线周期 | `["daily", "60min"]` |
| `days_back` | 回溯天数 | `30` |
| `max_symbols_per_type` | 每种类型最大处理数量 | `null`（无限制） |
| `request_interval` | 请求间隔（秒） | `0.05` |

## 📊 数据格式

### 代码表格式
```json
[
  {
    "code": "000001",
    "name": "平安银行",
    "market": "SZ",
    "type": "stock"
  }
]
```

### K线数据格式
```json
{
  "2024-01-09": {
    "open": 10.50,
    "high": 10.80,
    "low": 10.40,
    "close": 10.75,
    "volume": 1000000,
    "amount": 10750000.0
  }
}
```

## 🔧 高级用法

### 1. 自定义采集参数

```python
from pytdx_data_collector import PyTDXDataCollector

collector = PyTDXDataCollector()

# 运行自定义采集
await collector.run_full_collection(
    symbol_types=['stock', 'index'],
    data_types=['stock'],
    periods=['daily', '60min'],
    days_back=60
)
```

### 2. 只更新特定类型代码表

```python
from update_symbol_lists import SymbolListUpdater

updater = SymbolListUpdater()
await updater.initialize()

# 只更新股票代码表
symbol_lists = await updater.collector.get_all_symbol_lists(['stock'])
```

### 3. 采集特定标的数据

```python
from collect_historical_data import HistoricalDataCollector

collector = HistoricalDataCollector()
await collector.initialize()

# 采集特定股票
symbols = [{"code": "000001", "name": "平安银行"}]
await collector.collect_stock_data(symbols, periods=['daily'], days_back=30)
```

## 📈 监控和日志

### 日志文件
- `pytdx_collector.log`: 主要采集日志
- `historical_data_collection.log`: 历史数据采集日志
- `logs/pytdx_collection.log`: 增强版采集器日志

### 统计信息
采集完成后会显示详细统计：
- 代码表更新数量
- 处理的标的数量
- 采集的数据点数量
- 成功/失败统计
- 总耗时

## ⚠️ 注意事项

### 1. 网络要求
- 确保网络连接稳定
- 通达信服务器可能有访问限制
- 建议在交易时间外进行大量数据采集

### 2. 存储空间
- 全量数据需要足够的存储空间
- 建议定期清理旧数据
- 可配置只保存特定格式的文件

### 3. 请求频率
- 避免请求过于频繁
- 默认请求间隔为0.05秒
- 可根据网络情况调整

### 4. 数据质量
- 自动进行数据验证和去重
- 异常数据会被过滤
- 建议定期检查数据完整性

## 🛠️ 故障排除

### 常见问题

**1. 连接失败**
```
❌ PyTDX连接失败
```
解决方案：
- 检查网络连接
- 尝试更换服务器
- 检查防火墙设置

**2. 数据为空**
```
⚠️ 无数据返回
```
解决方案：
- 检查代码是否正确
- 确认日期范围是否合理
- 检查是否为交易日

**3. 内存不足**
```
MemoryError: 内存不足
```
解决方案：
- 减少批处理大小
- 分批处理数据
- 增加系统内存

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📞 技术支持

如有问题，请检查：
1. 依赖包是否正确安装
2. 配置文件是否正确
3. 网络连接是否正常
4. 日志文件中的错误信息

## 🔄 更新日志

### v2.0.0 (2024-01-09)
- ✅ 新增增强版数据采集器
- ✅ 支持配置文件管理
- ✅ 多种输出格式支持
- ✅ 改进的数据质量控制
- ✅ 详细的统计和日志

### v1.0.0 (2024-01-08)
- ✅ 基础代码表更新功能
- ✅ 历史数据采集功能
- ✅ 数据验证和去重
- ✅ 文件存储支持