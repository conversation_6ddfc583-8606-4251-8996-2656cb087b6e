#pragma once

#include <string>
#include <vector>
#include <chrono>
#include <cstdint>
#include <array>

namespace financial_data {

// 价格档位结构
struct PriceLevel {
    double price = 0.0;
    uint64_t volume = 0;
    uint32_t order_count = 0;
    
    PriceLevel() = default;
    PriceLevel(double p, uint64_t v, uint32_t c = 0) : price(p), volume(v), order_count(c) {}
    
    bool IsValid() const {
        return price > 0.0 && volume > 0;
    }
};

// 标准化Tick数据结构
struct StandardTick {
    int64_t timestamp_ns = 0;           // 纳秒级时间戳
    std::string symbol;                 // 合约代码
    std::string exchange;               // 交易所代码
    
    // 基础价格信息
    double last_price = 0.0;           // 最新价
    double pre_close_price = 0.0;      // 昨收价
    double open_price = 0.0;           // 开盘价
    double high_price = 0.0;           // 最高价
    double low_price = 0.0;            // 最低价
    double close_price = 0.0;          // 收盘价
    double settlement_price = 0.0;     // 结算价
    double upper_limit = 0.0;          // 涨停价
    double lower_limit = 0.0;          // 跌停价
    
    // 成交信息
    uint64_t volume = 0;               // 成交量
    double turnover = 0.0;             // 成交额
    uint64_t open_interest = 0;        // 持仓量
    
    // 五档行情
    std::array<PriceLevel, 5> bids;    // 买盘五档
    std::array<PriceLevel, 5> asks;    // 卖盘五档
    
    // 元数据
    uint32_t sequence = 0;             // 序列号
    std::string trade_flag;            // 成交标志
    std::string update_time;           // 更新时间
    int update_millisec = 0;           // 更新毫秒
    
    // 辅助方法
    void SetCurrentTimestamp() {
        timestamp_ns = GetCurrentTimestampNs();
    }
    
    static int64_t GetCurrentTimestampNs() {
        auto now = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
    }
    
    bool IsValid() const {
        return !symbol.empty() && 
               !exchange.empty() && 
               timestamp_ns > 0 && 
               last_price > 0.0;
    }
    
    // 获取买一价/卖一价
    double GetBidPrice() const { return bids[0].price; }
    double GetAskPrice() const { return asks[0].price; }
    uint64_t GetBidVolume() const { return bids[0].volume; }
    uint64_t GetAskVolume() const { return asks[0].volume; }
    
    // 计算中间价
    double GetMidPrice() const {
        if (GetBidPrice() > 0 && GetAskPrice() > 0) {
            return (GetBidPrice() + GetAskPrice()) / 2.0;
        }
        return last_price;
    }
    
    // 计算价差
    double GetSpread() const {
        if (GetBidPrice() > 0 && GetAskPrice() > 0) {
            return GetAskPrice() - GetBidPrice();
        }
        return 0.0;
    }
};

// Level2深度数据结构
struct Level2Data {
    int64_t timestamp_ns = 0;           // 纳秒级时间戳
    std::string symbol;                 // 合约代码
    std::string exchange;               // 交易所代码
    uint32_t sequence = 0;              // 序列号
    
    // 十档行情
    std::vector<PriceLevel> bids;       // 买盘深度
    std::vector<PriceLevel> asks;       // 卖盘深度
    
    // 统计信息
    double total_bid_volume = 0.0;      // 总买量
    double total_ask_volume = 0.0;      // 总卖量
    double weighted_bid_price = 0.0;    // 加权买价
    double weighted_ask_price = 0.0;    // 加权卖价
    
    void SetCurrentTimestamp() {
        timestamp_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    bool IsValid() const {
        return !symbol.empty() && 
               !exchange.empty() && 
               timestamp_ns > 0 && 
               (!bids.empty() || !asks.empty());
    }
    
    // 计算加权价格
    void CalculateWeightedPrices() {
        // 计算买盘加权价格
        double bid_sum = 0.0, bid_vol_sum = 0.0;
        for (const auto& level : bids) {
            bid_sum += level.price * level.volume;
            bid_vol_sum += level.volume;
        }
        weighted_bid_price = bid_vol_sum > 0 ? bid_sum / bid_vol_sum : 0.0;
        total_bid_volume = bid_vol_sum;
        
        // 计算卖盘加权价格
        double ask_sum = 0.0, ask_vol_sum = 0.0;
        for (const auto& level : asks) {
            ask_sum += level.price * level.volume;
            ask_vol_sum += level.volume;
        }
        weighted_ask_price = ask_vol_sum > 0 ? ask_sum / ask_vol_sum : 0.0;
        total_ask_volume = ask_vol_sum;
    }
};

// 市场数据包装器
struct MarketDataWrapper {
    enum class DataType {
        TICK = 1,
        LEVEL2 = 2,
        TRADE = 3,
        ORDER = 4
    };
    
    DataType type;
    std::string source;                 // 数据源标识
    int64_t receive_time_ns = 0;        // 接收时间戳
    
    // 数据联合体
    StandardTick tick_data;
    Level2Data level2_data;
    
    // 构造函数
    MarketDataWrapper() : type(DataType::TICK) {
        receive_time_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    explicit MarketDataWrapper(const StandardTick& tick) 
        : type(DataType::TICK), tick_data(tick) {
        receive_time_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    explicit MarketDataWrapper(const Level2Data& level2) 
        : type(DataType::LEVEL2), level2_data(level2) {
        receive_time_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    bool IsValid() const {
        switch (type) {
            case DataType::TICK:
                return tick_data.IsValid();
            case DataType::LEVEL2:
                return level2_data.IsValid();
            default:
                return false;
        }
    }
    
    std::string GetSymbol() const {
        switch (type) {
            case DataType::TICK:
                return tick_data.symbol;
            case DataType::LEVEL2:
                return level2_data.symbol;
            default:
                return "";
        }
    }
    
    int64_t GetTimestamp() const {
        switch (type) {
            case DataType::TICK:
                return tick_data.timestamp_ns;
            case DataType::LEVEL2:
                return level2_data.timestamp_ns;
            default:
                return 0;
        }
    }
};

// 批量数据结构
struct MarketDataBatch {
    std::vector<MarketDataWrapper> data;
    int64_t batch_timestamp_ns = 0;
    std::string batch_id;
    
    MarketDataBatch() {
        batch_timestamp_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    void AddTick(const StandardTick& tick) {
        data.emplace_back(tick);
    }
    
    void AddLevel2(const Level2Data& level2) {
        data.emplace_back(level2);
    }
    
    void Clear() {
        data.clear();
        batch_timestamp_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    size_t Size() const {
        return data.size();
    }
    
    bool Empty() const {
        return data.empty();
    }
};

// 连接状态枚举
enum class ConnectionStatus {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    RECONNECTING = 3,
    ERROR = 4
};

// 统计信息结构
struct CollectorStatistics {
    uint64_t total_received = 0;
    uint64_t total_processed = 0;
    uint64_t total_errors = 0;
    ConnectionStatus status = ConnectionStatus::DISCONNECTED;
    uint32_t reconnect_attempts = 0;
    std::chrono::steady_clock::time_point last_heartbeat;
    double uptime_seconds = 0.0;
    double messages_per_second = 0.0;
};

} // namespace financial_data