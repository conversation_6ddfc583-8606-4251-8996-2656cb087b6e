/**
 * @file latency_test.h
 * @brief Latency testing for financial data service
 */

#pragma once

#include "benchmark_runner.h"
#include <memory>
#include <vector>

namespace performance_tests {

class TestUtils;

/**
 * @class LatencyTest
 * @brief Comprehensive latency testing for all system components
 * 
 * Tests end-to-end latency to verify the system meets the <50μs requirement
 */
class LatencyTest {
public:
    LatencyTest();
    ~LatencyTest();
    
    /**
     * @brief Test end-to-end latency from data source to client
     * @return LatencyResult with comprehensive latency statistics
     * 
     * Requirement: End-to-end latency must be < 50μs (Req 1.2, 3.5)
     */
    LatencyResult TestEndToEndLatency();
    
    /**
     * @brief Test WebSocket message delivery latency
     * @return LatencyResult for WebSocket communication
     */
    LatencyResult TestWebSocketLatency();
    
    /**
     * @brief Test gRPC streaming latency
     * @return LatencyResult for gRPC communication
     */
    LatencyResult TestGrpcLatency();
    
    /**
     * @brief Test REST API response latency
     * @return LatencyResult for REST API calls
     */
    LatencyResult TestRestApiLatency();
    
    /**
     * @brief Test storage read/write latency
     * @return LatencyResult for storage operations
     */
    LatencyResult TestStorageLatency();

private:
    /**
     * @brief Calculate statistical measures from latency samples
     * @param latencies Vector of latency measurements in microseconds
     * @return LatencyResult with mean, percentiles, and other statistics
     */
    LatencyResult CalculateLatencyStatistics(const std::vector<double>& latencies);
    
    std::unique_ptr<TestUtils> test_utils_;
};

} // namespace performance_tests