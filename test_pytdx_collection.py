#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTDX数据采集测试脚本
用于验证采集功能是否正常工作
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from collectors.pytdx_collector import PyTDXCollector, PyTDXConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_connection():
    """测试PyTDX连接"""
    print("🔗 测试PyTDX连接")
    print("-" * 40)
    
    try:
        config = PyTDXConfig()
        collector = PyTDXCollector(config)
        
        # 初始化连接
        success = await collector.initialize()
        
        if success:
            print("✅ 连接成功")
            
            # 测试获取股票列表
            try:
                stock_list = await collector.get_stock_list(market=0)  # 深圳市场
                print(f"✅ 获取深圳股票列表: {len(stock_list)} 个")
                
                # 显示前5个股票
                for i, stock in enumerate(stock_list[:5]):
                    print(f"  {stock.get('code', 'N/A'):8s} {stock.get('name', 'N/A')}")
                
            except Exception as e:
                print(f"❌ 获取股票列表失败: {e}")
            
            await collector.close()
            return True
        else:
            print("❌ 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False


async def test_symbol_lists():
    """测试代码表获取"""
    print("\n📋 测试代码表获取")
    print("-" * 40)
    
    try:
        config = PyTDXConfig()
        collector = PyTDXCollector(config)
        
        if not await collector.initialize():
            print("❌ 初始化失败")
            return False
        
        # 测试获取股票和指数代码表
        symbol_types = ['stock', 'index']
        symbol_lists = await collector.get_all_symbol_lists(symbol_types)
        
        total_symbols = 0
        for symbol_type, symbols in symbol_lists.items():
            count = len(symbols)
            total_symbols += count
            print(f"✅ {symbol_type:8s}: {count:6d} 个")
            
            # 显示样本
            if symbols:
                print(f"  样本: {symbols[0].get('code', 'N/A')} - {symbols[0].get('name', 'N/A')}")
        
        print(f"✅ 总计: {total_symbols} 个标的")
        
        await collector.close()
        return True
        
    except Exception as e:
        print(f"❌ 代码表获取失败: {e}")
        return False


async def test_k_data():
    """测试K线数据获取"""
    print("\n📊 测试K线数据获取")
    print("-" * 40)
    
    try:
        config = PyTDXConfig()
        collector = PyTDXCollector(config)
        
        if not await collector.initialize():
            print("❌ 初始化失败")
            return False
        
        # 测试获取平安银行的日线数据
        symbol = "000001"
        print(f"获取 {symbol} 的K线数据...")
        
        # 获取最近10天的日线数据
        data = await collector.get_k_data(
            symbol=symbol,
            period='daily',
            count=10
        )
        
        if data is not None and not data.empty:
            print(f"✅ 获取日线数据: {len(data)} 条")
            print("最新5条数据:")
            print(data.tail().to_string())
        else:
            print("⚠️ 未获取到数据")
        
        # 测试获取分钟数据
        print(f"\n获取 {symbol} 的60分钟数据...")
        minute_data = await collector.get_k_data(
            symbol=symbol,
            period='60min',
            count=5
        )
        
        if minute_data is not None and not minute_data.empty:
            print(f"✅ 获取60分钟数据: {len(minute_data)} 条")
        else:
            print("⚠️ 未获取到分钟数据")
        
        await collector.close()
        return True
        
    except Exception as e:
        print(f"❌ K线数据获取失败: {e}")
        return False


async def test_realtime_quotes():
    """测试实时行情获取"""
    print("\n💹 测试实时行情获取")
    print("-" * 40)
    
    try:
        config = PyTDXConfig()
        collector = PyTDXCollector(config)
        
        if not await collector.initialize():
            print("❌ 初始化失败")
            return False
        
        # 测试获取几个股票的实时行情
        symbols = ["000001", "000002", "600000", "600036"]
        print(f"获取实时行情: {symbols}")
        
        quotes = await collector.get_realtime_quotes(symbols)
        
        if quotes:
            print(f"✅ 获取实时行情: {len(quotes)} 个")
            for quote in quotes:
                symbol = quote.get('code', 'N/A')
                name = quote.get('name', 'N/A')
                price = quote.get('price', 0)
                print(f"  {symbol:8s} {name:10s} {price:8.2f}")
        else:
            print("⚠️ 未获取到实时行情")
        
        await collector.close()
        return True
        
    except Exception as e:
        print(f"❌ 实时行情获取失败: {e}")
        return False


async def test_data_archiving():
    """测试数据归档功能"""
    print("\n💾 测试数据归档功能")
    print("-" * 40)
    
    try:
        config = PyTDXConfig()
        config.archive_enabled = True  # 启用归档
        collector = PyTDXCollector(config)
        
        if not await collector.initialize():
            print("❌ 初始化失败")
            return False
        
        # 获取测试数据
        symbol = "000001"
        data = await collector.get_k_data(symbol=symbol, period='daily', count=5)
        
        if data is not None and not data.empty:
            print(f"✅ 获取测试数据: {len(data)} 条")
            
            # 测试归档
            if collector.archiver:
                success = await collector.archiver.archive_k_data(
                    symbol=symbol,
                    data=data,
                    data_type='test_daily'
                )
                
                if success:
                    print("✅ 数据归档成功")
                    
                    # 显示归档统计
                    stats = collector.archiver.get_stats()
                    print(f"归档统计: {stats}")
                else:
                    print("❌ 数据归档失败")
            else:
                print("⚠️ 归档器未启用")
        else:
            print("⚠️ 未获取到测试数据")
        
        await collector.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据归档测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("PyTDX数据采集功能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("连接测试", test_connection),
        ("代码表获取", test_symbol_lists),
        ("K线数据获取", test_k_data),
        ("实时行情获取", test_realtime_quotes),
        ("数据归档功能", test_data_archiving)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果汇总
    print("\n📋 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20s}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {len(results)} 个测试, 通过: {passed}, 失败: {failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")
        return False


async def main():
    """主函数"""
    try:
        success = await run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)