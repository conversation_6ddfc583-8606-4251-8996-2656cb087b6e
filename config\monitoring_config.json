{"prometheus": {"bind_address": "0.0.0.0:9090", "metrics_path": "/metrics"}, "latency_monitoring": {"threshold_microseconds": 50.0, "alert_cooldown_seconds": 30, "measurement_buffer_size": 10000}, "data_integrity": {"missing_data_timeout_seconds": 5, "alert_cooldown_seconds": 30, "max_sequence_gap": 100, "sequence_buffer_size": 1000}, "resource_monitoring": {"monitoring_interval_seconds": 5, "cpu_threshold_percent": 85.0, "memory_threshold_percent": 85.0, "disk_threshold_percent": 85.0, "alert_cooldown_seconds": 300}, "alerting": {"max_retries": 3, "retry_delay_seconds": 5, "rate_limit_window_seconds": 60, "max_alerts_per_window": 10, "channels": {"console": {"enabled": true}, "email": {"enabled": false, "smtp_server": "smtp.example.com", "port": 587, "username": "<EMAIL>", "password": "password", "recipients": ["<EMAIL>", "<EMAIL>"]}, "webhook": {"enabled": false, "url": "https://hooks.example.com/alerts"}, "slack": {"enabled": false, "webhook_url": "https://hooks.slack.com/services/...", "channel": "#alerts"}}}, "health_check": {"interval_seconds": 30, "timeout_seconds": 10}, "logging": {"level": "INFO", "file": "logs/monitoring.log", "max_file_size_mb": 100, "max_files": 10}}