#pragma once

#include <websocketpp/config/asio_no_tls.hpp>
#include <websocketpp/server.hpp>
#include <string>
#include <memory>
#include <unordered_map>
#include <atomic>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>
#include <spdlog/spdlog.h>

#include "websocket_types.h"
#include "subscription_manager.h"
#include "message_compressor.h"
#include "heartbeat_manager.h"
#include "data_types.h"

namespace financial_data {
namespace interfaces {

// WebSocket服务器类型定义
using WebSocketServer = websocketpp::server<websocketpp::config::asio>;
using ConnectionHdl = websocketpp::connection_hdl;
using MessagePtr = WebSocketServer::message_ptr;

/**
 * @brief WebSocket连接上下文
 */
struct ConnectionContext {
    std::string client_id;
    std::shared_ptr<ClientConnection> client_info;
    ConnectionHdl hdl;
    std::weak_ptr<WebSocketServer> server;
    
    // 消息队列
    std::queue<std::string> message_queue;
    std::mutex queue_mutex;
    std::condition_variable queue_cv;
    std::atomic<bool> queue_active{true};
    
    // 批量消息
    MessageBatch current_batch;
    std::mutex batch_mutex;
    std::chrono::steady_clock::time_point last_batch_time;
    
    ConnectionContext() {
        last_batch_time = std::chrono::steady_clock::now();
    }
    
    void AddMessage(const std::string& message) {
        std::lock_guard<std::mutex> lock(queue_mutex);
        if (queue_active.load()) {
            message_queue.push(message);
            queue_cv.notify_one();
        }
    }
    
    void AddToBatch(const nlohmann::json& message) {
        std::lock_guard<std::mutex> lock(batch_mutex);
        current_batch.AddMessage(message);
    }
    
    bool ShouldFlushBatch(size_t batch_size, uint32_t batch_timeout_ms) const {
        std::lock_guard<std::mutex> lock(batch_mutex);
        if (current_batch.IsEmpty()) {
            return false;
        }
        
        if (current_batch.Size() >= batch_size) {
            return true;
        }
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_batch_time);
        return elapsed.count() >= batch_timeout_ms;
    }
    
    MessageBatch FlushBatch() {
        std::lock_guard<std::mutex> lock(batch_mutex);
        MessageBatch batch = std::move(current_batch);
        current_batch.Clear();
        last_batch_time = std::chrono::steady_clock::now();
        return batch;
    }
    
    void Shutdown() {
        queue_active = false;
        queue_cv.notify_all();
    }
};

/**
 * @brief WebSocket处理器
 * 
 * 负责处理WebSocket连接、消息路由和客户端管理
 */
class WebSocketHandler {
private:
    WebSocketConfig config_;
    WebSocketStatistics statistics_;
    
    std::shared_ptr<WebSocketServer> server_;
    std::shared_ptr<SubscriptionManager> subscription_manager_;
    std::shared_ptr<MessageCompressor> message_compressor_;
    std::shared_ptr<HeartbeatManager> heartbeat_manager_;
    
    // 连接管理
    std::unordered_map<std::string, std::shared_ptr<ConnectionContext>> connections_;
    std::mutex connections_mutex_;
    
    // 消息发送线程池
    std::vector<std::thread> sender_threads_;
    std::atomic<bool> running_{false};
    
    // 数据总线回调
    std::function<bool(const MarketDataWrapper&)> data_callback_;
    
    std::shared_ptr<spdlog::logger> logger_;

public:
    explicit WebSocketHandler(const WebSocketConfig& config = WebSocketConfig{});
    ~WebSocketHandler();

    /**
     * @brief 初始化处理器
     */
    bool Initialize();

    /**
     * @brief 启动处理器
     */
    bool Start();

    /**
     * @brief 停止处理器
     */
    void Stop();

    /**
     * @brief 检查是否正在运行
     */
    bool IsRunning() const { return running_.load(); }

    /**
     * @brief 设置数据总线回调
     */
    void SetDataCallback(std::function<bool(const MarketDataWrapper&)> callback) {
        data_callback_ = callback;
    }

    /**
     * @brief 处理市场数据
     */
    void HandleMarketData(const MarketDataWrapper& data);

    /**
     * @brief 广播消息到所有连接
     */
    void BroadcastMessage(const std::string& message);

    /**
     * @brief 发送消息到指定客户端
     */
    bool SendMessageToClient(const std::string& client_id, const std::string& message);

    /**
     * @brief 获取连接数量
     */
    size_t GetConnectionCount() const;

    /**
     * @brief 获取活跃连接数量
     */
    size_t GetActiveConnectionCount() const;

    /**
     * @brief 获取客户端列表
     */
    std::vector<std::string> GetClientList() const;

    /**
     * @brief 获取客户端信息
     */
    std::shared_ptr<ClientConnection> GetClientInfo(const std::string& client_id) const;

    /**
     * @brief 断开客户端连接
     */
    bool DisconnectClient(const std::string& client_id, const std::string& reason = "");

    /**
     * @brief 获取配置
     */
    WebSocketConfig GetConfig() const { return config_; }

    /**
     * @brief 更新配置
     */
    bool UpdateConfig(const WebSocketConfig& config);

    /**
     * @brief 获取统计信息
     */
    WebSocketStatistics GetStatistics() const { return statistics_; }

    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

    /**
     * @brief 获取统计摘要
     */
    std::string GetStatisticsSummary() const;

    /**
     * @brief 获取健康状态
     */
    struct HealthStatus {
        bool overall_healthy;
        size_t active_connections;
        size_t max_connections;
        double connection_usage;
        uint64_t avg_latency_ms;
        std::string status_message;
    };
    
    HealthStatus GetHealthStatus() const;

    /**
     * @brief 获取订阅管理器
     */
    std::shared_ptr<SubscriptionManager> GetSubscriptionManager() const {
        return subscription_manager_;
    }

    /**
     * @brief 获取心跳管理器
     */
    std::shared_ptr<HeartbeatManager> GetHeartbeatManager() const {
        return heartbeat_manager_;
    }

private:
    /**
     * @brief WebSocket事件处理器
     */
    void OnOpen(ConnectionHdl hdl);
    void OnClose(ConnectionHdl hdl);
    void OnMessage(ConnectionHdl hdl, MessagePtr msg);
    void OnPing(ConnectionHdl hdl, std::string payload);
    void OnPong(ConnectionHdl hdl, std::string payload);
    void OnPongTimeout(ConnectionHdl hdl, std::string payload);

    /**
     * @brief 处理客户端消息
     */
    void HandleClientMessage(const std::string& client_id, const WebSocketMessage& message);

    /**
     * @brief 处理订阅请求
     */
    void HandleSubscriptionRequest(const std::string& client_id, const nlohmann::json& payload);

    /**
     * @brief 处理取消订阅请求
     */
    void HandleUnsubscriptionRequest(const std::string& client_id, const nlohmann::json& payload);

    /**
     * @brief 处理心跳消息
     */
    void HandleHeartbeat(const std::string& client_id);

    /**
     * @brief 发送响应消息
     */
    void SendResponse(const std::string& client_id, const nlohmann::json& response);

    /**
     * @brief 发送错误消息
     */
    void SendError(const std::string& client_id, const std::string& error_message);

    /**
     * @brief 消息发送线程
     */
    void MessageSenderLoop(size_t thread_id);

    /**
     * @brief 批量消息处理线程
     */
    void BatchProcessorLoop();

    /**
     * @brief 发送原始消息
     */
    bool SendRawMessage(ConnectionHdl hdl, const std::string& message);

    /**
     * @brief 压缩消息
     */
    std::string CompressMessage(const std::string& message);

    /**
     * @brief 生成客户端ID
     */
    std::string GenerateClientId(ConnectionHdl hdl);

    /**
     * @brief 获取连接信息
     */
    std::string GetConnectionInfo(ConnectionHdl hdl);

    /**
     * @brief 记录连接统计
     */
    void RecordConnectionStats(bool connected);

    /**
     * @brief 记录消息统计
     */
    void RecordMessageStats(bool sent, size_t message_size, uint64_t latency_ns = 0);

    /**
     * @brief 更新延迟统计
     */
    void UpdateLatencyStats(uint64_t latency_ns);

    /**
     * @brief 心跳事件处理器
     */
    void OnHeartbeatEvent(HeartbeatEvent event, const std::string& client_id, const ClientHeartbeatInfo& info);

    /**
     * @brief 发送Ping消息
     */
    bool SendPing(const std::string& client_id, uint64_t timestamp);

    /**
     * @brief 断开连接处理器
     */
    void OnClientDisconnect(const std::string& client_id, const std::string& reason);

    /**
     * @brief 清理连接
     */
    void CleanupConnection(const std::string& client_id);

    /**
     * @brief 验证消息格式
     */
    bool ValidateMessage(const nlohmann::json& message, std::string& error_message);

    /**
     * @brief 应用速率限制
     */
    bool ApplyRateLimit(const std::string& client_id);

    /**
     * @brief 获取连接上下文
     */
    std::shared_ptr<ConnectionContext> GetConnectionContext(const std::string& client_id);

    /**
     * @brief 创建连接上下文
     */
    std::shared_ptr<ConnectionContext> CreateConnectionContext(const std::string& client_id, ConnectionHdl hdl);
};

} // namespace interfaces
} // namespace financial_data