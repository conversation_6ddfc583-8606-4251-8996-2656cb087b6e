apiVersion: apps/v1
kind: Deployment
metadata:
  name: market-data-collector
  namespace: market-data
  labels:
    app: market-data-collector
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: market-data-collector
  template:
    metadata:
      labels:
        app: market-data-collector
        version: v1.0.0
    spec:
      containers:
      - name: market-data-collector
        image: market-data-collector:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http-api
          protocol: TCP
        - containerPort: 8081
          name: grpc-api
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: CONFIG_PATH
          value: "/app/config/unified_config.json"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: logs-volume
          mountPath: /app/logs
        - name: data-volume
          mountPath: /app/data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
      volumes:
      - name: config-volume
        configMap:
          name: market-data-config
      - name: logs-volume
        emptyDir: {}
      - name: data-volume
        persistentVolumeClaim:
          claimName: market-data-pvc
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: market-data-collector-service
  namespace: market-data
  labels:
    app: market-data-collector
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http-api
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: grpc-api
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: market-data-collector
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: market-data-pvc
  namespace: market-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard