#!/bin/bash
# 金融数据服务 - 系统安装脚本
# Financial Data Service - System Installation Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 默认配置
INSTALL_DIR="/opt/financial-data-service"
CONFIG_DIR="/etc/financial-data-service"
LOG_DIR="/var/log/financial-data-service"
RUN_DIR="/var/run/financial-data-service"
USER="financial-data"
GROUP="financial-data"
PYTHON_VERSION="3.11"

# 显示帮助信息
show_help() {
    cat << EOF
金融数据服务 - 系统安装脚本
Financial Data Service - System Installation Script

用法: $0 [选项]
Usage: $0 [options]

选项 Options:
  --install-dir DIR     安装目录 (默认: $INSTALL_DIR)
  --config-dir DIR      配置目录 (默认: $CONFIG_DIR)
  --user USER           运行用户 (默认: $USER)
  --group GROUP         运行用户组 (默认: $GROUP)
  --python-version VER  Python版本 (默认: $PYTHON_VERSION)
  --no-systemd          不安装systemd服务
  --docker-only         仅安装Docker版本
  --help, -h            显示帮助

示例 Examples:
  $0                                    # 默认安装
  $0 --install-dir /usr/local/fds      # 自定义安装目录
  $0 --docker-only                     # 仅Docker安装
  $0 --no-systemd                      # 不安装系统服务

EOF
}

# 解析命令行参数
INSTALL_SYSTEMD=true
DOCKER_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --config-dir)
            CONFIG_DIR="$2"
            shift 2
            ;;
        --user)
            USER="$2"
            shift 2
            ;;
        --group)
            GROUP="$2"
            shift 2
            ;;
        --python-version)
            PYTHON_VERSION="$2"
            shift 2
            ;;
        --no-systemd)
            INSTALL_SYSTEMD=false
            shift
            ;;
        --docker-only)
            DOCKER_ONLY=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        print_error "此脚本需要root权限运行"
        print_error "This script must be run as root"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        print_error "无法检测操作系统"
        exit 1
    fi
    
    print_info "检测到操作系统: $OS $VER"
}

# 安装系统依赖
install_system_dependencies() {
    print_info "安装系统依赖..."
    
    case $OS in
        "Ubuntu"*|"Debian"*)
            apt-get update
            apt-get install -y \
                python3 python3-pip python3-venv \
                gcc g++ libc6-dev libffi-dev libssl-dev \
                curl wget git \
                redis-server \
                systemd
            ;;
        "CentOS"*|"Red Hat"*|"Rocky"*|"AlmaLinux"*)
            yum update -y
            yum install -y \
                python3 python3-pip \
                gcc gcc-c++ glibc-devel libffi-devel openssl-devel \
                curl wget git \
                redis \
                systemd
            ;;
        *)
            print_warning "未知操作系统，请手动安装依赖"
            ;;
    esac
    
    print_success "系统依赖安装完成"
}

# 创建用户和组
create_user() {
    print_info "创建用户和组..."
    
    if ! getent group $GROUP > /dev/null 2>&1; then
        groupadd -r $GROUP
        print_info "创建组: $GROUP"
    fi
    
    if ! getent passwd $USER > /dev/null 2>&1; then
        useradd -r -g $GROUP -d $INSTALL_DIR -s /bin/bash $USER
        print_info "创建用户: $USER"
    fi
    
    print_success "用户和组创建完成"
}

# 创建目录结构
create_directories() {
    print_info "创建目录结构..."
    
    mkdir -p $INSTALL_DIR
    mkdir -p $CONFIG_DIR
    mkdir -p $LOG_DIR
    mkdir -p $RUN_DIR
    
    # 设置权限
    chown -R $USER:$GROUP $INSTALL_DIR
    chown -R $USER:$GROUP $LOG_DIR
    chown -R $USER:$GROUP $RUN_DIR
    chmod 755 $CONFIG_DIR
    
    print_success "目录结构创建完成"
}

# 安装应用文件
install_application() {
    print_info "安装应用文件..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
    
    # 复制应用文件
    cp -r "$PROJECT_ROOT/src" "$INSTALL_DIR/"
    cp -r "$PROJECT_ROOT/scripts" "$INSTALL_DIR/"
    cp -r "$PROJECT_ROOT/config" "$INSTALL_DIR/"
    cp "$PROJECT_ROOT/requirements.txt" "$INSTALL_DIR/"
    
    # 复制配置文件到系统配置目录
    cp "$PROJECT_ROOT/config/scheduler_config.json" "$CONFIG_DIR/"
    
    # 设置权限
    chown -R $USER:$GROUP $INSTALL_DIR
    chmod +x $INSTALL_DIR/scripts/*.sh
    chmod +x $INSTALL_DIR/scripts/*.py
    
    print_success "应用文件安装完成"
}

# 安装Python依赖
install_python_dependencies() {
    print_info "安装Python依赖..."
    
    # 切换到应用用户
    sudo -u $USER bash << EOF
cd $INSTALL_DIR
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
EOF
    
    print_success "Python依赖安装完成"
}

# 安装systemd服务
install_systemd_service() {
    if [[ "$INSTALL_SYSTEMD" != true ]]; then
        print_info "跳过systemd服务安装"
        return
    fi
    
    print_info "安装systemd服务..."
    
    # 创建服务文件
    cat > /etc/systemd/system/financial-data-scheduler.service << EOF
[Unit]
Description=Financial Data Service - Task Scheduler
Documentation=https://github.com/your-org/financial-data-service
After=network.target redis.service
Wants=redis.service

[Service]
Type=forking
User=$USER
Group=$GROUP
WorkingDirectory=$INSTALL_DIR
Environment=PYTHONPATH=$INSTALL_DIR/src
Environment=PATH=$INSTALL_DIR/venv/bin:\$PATH
ExecStart=$INSTALL_DIR/venv/bin/python $INSTALL_DIR/scripts/start_scheduler.py --daemon --config $CONFIG_DIR/scheduler_config.json --pid-file $RUN_DIR/scheduler.pid
ExecStop=$INSTALL_DIR/scripts/start_scheduler.sh --stop
ExecReload=$INSTALL_DIR/scripts/start_scheduler.sh --restart
PIDFile=$RUN_DIR/scheduler.pid
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=financial-data-scheduler

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$LOG_DIR $RUN_DIR $INSTALL_DIR/logs
PrivateTmp=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd
    systemctl daemon-reload
    systemctl enable financial-data-scheduler
    
    print_success "systemd服务安装完成"
}

# Docker安装
install_docker() {
    print_info "安装Docker版本..."
    
    # 检查Docker是否已安装
    if ! command -v docker &> /dev/null; then
        print_info "安装Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
        rm get-docker.sh
        
        # 启动Docker服务
        systemctl enable docker
        systemctl start docker
    fi
    
    # 检查Docker Compose是否已安装
    if ! command -v docker-compose &> /dev/null; then
        print_info "安装Docker Compose..."
        curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
    fi
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
    
    # 复制Docker配置
    mkdir -p $INSTALL_DIR/deployment
    cp -r "$PROJECT_ROOT/deployment/docker" "$INSTALL_DIR/deployment/"
    cp "$PROJECT_ROOT/config/scheduler_config.json" "$INSTALL_DIR/config/"
    
    # 构建镜像
    cd $INSTALL_DIR
    docker build -f deployment/docker/scheduler.Dockerfile -t financial-data-scheduler:latest .
    
    print_success "Docker版本安装完成"
}

# 配置防火墙
configure_firewall() {
    print_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian
        ufw allow 6379/tcp  # Redis
        ufw allow 8123/tcp  # ClickHouse HTTP
        ufw allow 9000/tcp  # ClickHouse Native
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL
        firewall-cmd --permanent --add-port=6379/tcp
        firewall-cmd --permanent --add-port=8123/tcp
        firewall-cmd --permanent --add-port=9000/tcp
        firewall-cmd --reload
    fi
    
    print_success "防火墙配置完成"
}

# 创建启动脚本
create_startup_script() {
    print_info "创建启动脚本..."
    
    cat > /usr/local/bin/financial-data-scheduler << EOF
#!/bin/bash
# 金融数据服务调度器管理脚本

case "\$1" in
    start)
        systemctl start financial-data-scheduler
        ;;
    stop)
        systemctl stop financial-data-scheduler
        ;;
    restart)
        systemctl restart financial-data-scheduler
        ;;
    status)
        systemctl status financial-data-scheduler
        ;;
    logs)
        journalctl -u financial-data-scheduler -f
        ;;
    *)
        echo "用法: \$0 {start|stop|restart|status|logs}"
        exit 1
        ;;
esac
EOF
    
    chmod +x /usr/local/bin/financial-data-scheduler
    
    print_success "启动脚本创建完成"
}

# 验证安装
verify_installation() {
    print_info "验证安装..."
    
    # 检查文件
    if [[ ! -f "$INSTALL_DIR/scripts/start_scheduler.py" ]]; then
        print_error "安装验证失败: 缺少启动脚本"
        exit 1
    fi
    
    # 检查Python环境
    if ! sudo -u $USER bash -c "cd $INSTALL_DIR && source venv/bin/activate && python -c 'import asyncio, croniter'"; then
        print_error "安装验证失败: Python环境问题"
        exit 1
    fi
    
    # 检查服务
    if [[ "$INSTALL_SYSTEMD" == true ]]; then
        if ! systemctl is-enabled financial-data-scheduler &>/dev/null; then
            print_error "安装验证失败: systemd服务未启用"
            exit 1
        fi
    fi
    
    print_success "安装验证通过"
}

# 显示安装后信息
show_post_install_info() {
    print_success "安装完成！"
    echo
    echo "========================================"
    echo "金融数据服务 - 任务调度器"
    echo "Financial Data Service - Task Scheduler"
    echo "========================================"
    echo
    echo "安装目录: $INSTALL_DIR"
    echo "配置目录: $CONFIG_DIR"
    echo "日志目录: $LOG_DIR"
    echo "运行用户: $USER"
    echo
    
    if [[ "$DOCKER_ONLY" == true ]]; then
        echo "Docker使用方法:"
        echo "  启动: cd $INSTALL_DIR && docker-compose -f deployment/docker/docker-compose.scheduler.yml up -d"
        echo "  停止: cd $INSTALL_DIR && docker-compose -f deployment/docker/docker-compose.scheduler.yml down"
        echo "  日志: cd $INSTALL_DIR && docker-compose -f deployment/docker/docker-compose.scheduler.yml logs -f"
    else
        echo "服务管理命令:"
        echo "  启动服务: systemctl start financial-data-scheduler"
        echo "  停止服务: systemctl stop financial-data-scheduler"
        echo "  重启服务: systemctl restart financial-data-scheduler"
        echo "  查看状态: systemctl status financial-data-scheduler"
        echo "  查看日志: journalctl -u financial-data-scheduler -f"
        echo
        echo "或使用便捷命令:"
        echo "  financial-data-scheduler start|stop|restart|status|logs"
    fi
    
    echo
    echo "配置文件: $CONFIG_DIR/scheduler_config.json"
    echo "请根据需要修改配置文件后启动服务"
    echo
}

# 主函数
main() {
    echo "========================================"
    echo "金融数据服务 - 系统安装"
    echo "Financial Data Service - Installation"
    echo "========================================"
    echo
    
    check_permissions
    detect_os
    
    if [[ "$DOCKER_ONLY" == true ]]; then
        print_info "Docker模式安装"
        create_directories
        install_docker
    else
        print_info "标准模式安装"
        install_system_dependencies
        create_user
        create_directories
        install_application
        install_python_dependencies
        install_systemd_service
        configure_firewall
        create_startup_script
    fi
    
    verify_installation
    show_post_install_info
}

# 运行主函数
main "$@"