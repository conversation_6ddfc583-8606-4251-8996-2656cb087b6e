#pragma once

#include <string>
#include <vector>
#include <chrono>

namespace financial_data {
namespace security {

// TLS配置
struct TLSConfig {
    std::string cert_file;
    std::string key_file;
    std::string ca_file;
    std::string cipher_suites;
    bool require_client_cert = true;
    int min_tls_version = 13; // TLS 1.3
};

// 加密配置
struct EncryptionConfig {
    std::string key_file;
    std::string algorithm = "AES-256-GCM";
    int key_rotation_days = 90;
    bool enable_disk_encryption = true;
};

// JWT配置
struct JWTConfig {
    std::string secret_key;
    std::string issuer;
    std::chrono::seconds token_expiry{3600}; // 1小时
    std::chrono::seconds refresh_expiry{86400 * 7}; // 7天
    bool require_mfa = true;
    std::string mfa_secret_key;
};

// RBAC配置
struct RBACConfig {
    std::string roles_config_file;
    std::string permissions_config_file;
    bool enable_dynamic_permissions = true;
    std::chrono::seconds cache_ttl{300}; // 5分钟
};

// 审计配置
struct AuditConfig {
    std::string log_file;
    std::string log_level = "INFO";
    bool enable_real_time_alerts = true;
    int max_log_size_mb = 100;
    int max_log_files = 10;
    std::vector<std::string> sensitive_operations = {
        "LOGIN", "LOGOUT", "DATA_ACCESS", "CONFIG_CHANGE", "USER_MANAGEMENT"
    };
};

// 总体安全配置
struct SecurityConfig {
    TLSConfig tls;
    EncryptionConfig encryption;
    JWTConfig jwt;
    RBACConfig rbac;
    AuditConfig audit;
    
    bool enable_rate_limiting = true;
    int max_requests_per_minute = 1000;
    bool enable_ip_whitelist = false;
    std::vector<std::string> allowed_ips;
};

} // namespace security
} // namespace financial_data