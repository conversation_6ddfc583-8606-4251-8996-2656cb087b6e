#!/usr/bin/env python3
"""
数据一致性验证工具
测试数据去重和冲突解决，验证存储层数据一致性，检查数据完整性和准确性
"""

import asyncio
import hashlib
import json
import logging
import os
import sys
import time
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple, Any
import pandas as pd
import redis
import sqlite3

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.collectors.pytdx_collector import PytdxCollector
from src.collectors.historical_data_archiver import HistoricalDataArchiver
from src.storage.unified_data_access import UnifiedDataAccessInterface
from src.storage.storage_layer_selector import StorageLayerSelector
from src.config.config_manager_python import ConfigManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DataConsistencyValidator:
    """数据一致性验证器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.validation_results = {}
        self.inconsistencies = []
        
        # 初始化组件
        self.archiver = HistoricalDataArchiver(config.get("archiver", {}))
        self.unified_access = UnifiedDataAccessInterface(config.get("storage", {}))
        self.storage_selector = StorageLayerSelector(config.get("storage", {}))
        
        # 创建本地验证数据库
        self.validation_db_path = "tests/data/validation.db"
        self._init_validation_db()
        
        logger.info("Data consistency validator initialized")
    
    def _init_validation_db(self):
        """初始化验证数据库"""
        os.makedirs(os.path.dirname(self.validation_db_path), exist_ok=True)
        
        conn = sqlite3.connect(self.validation_db_path)
        cursor = conn.cursor()
        
        # 创建验证表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS validation_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp_ns INTEGER NOT NULL,
                data_hash TEXT NOT NULL,
                storage_layer TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp_ns, storage_layer)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS consistency_issues (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue_type TEXT NOT NULL,
                symbol TEXT NOT NULL,
                timestamp_ns INTEGER,
                description TEXT NOT NULL,
                severity TEXT NOT NULL,
                detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("Validation database initialized")
    
    async def validate_all_consistency(self) -> Dict[str, Any]:
        """执行全面的数据一致性验证"""
        logger.info("Starting comprehensive data consistency validation")
        
        start_time = time.time()
        
        # 1. 测试数据去重功能
        dedup_result = await self._test_data_deduplication()
        
        # 2. 测试冲突解决机制
        conflict_result = await self._test_conflict_resolution()
        
        # 3. 验证存储层数据一致性
        storage_result = await self._validate_storage_layer_consistency()
        
        # 4. 检查数据完整性
        integrity_result = await self._check_data_integrity()
        
        # 5. 验证数据准确性
        accuracy_result = await self._validate_data_accuracy()
        
        # 6. 跨存储层一致性检查
        cross_storage_result = await self._validate_cross_storage_consistency()
        
        total_time = time.time() - start_time
        
        # 汇总结果
        overall_result = {
            "timestamp": datetime.now().isoformat(),
            "duration_seconds": total_time,
            "tests": {
                "deduplication": dedup_result,
                "conflict_resolution": conflict_result,
                "storage_consistency": storage_result,
                "data_integrity": integrity_result,
                "data_accuracy": accuracy_result,
                "cross_storage_consistency": cross_storage_result
            },
            "overall_success": all([
                dedup_result["success"],
                conflict_result["success"],
                storage_result["success"],
                integrity_result["success"],
                accuracy_result["success"],
                cross_storage_result["success"]
            ]),
            "inconsistencies": self.inconsistencies
        }
        
        # 生成详细报告
        self._generate_consistency_report(overall_result)
        
        logger.info(f"Data consistency validation completed in {total_time:.2f}s - "
                   f"Success: {overall_result['overall_success']}")
        
        return overall_result
    
    async def _test_data_deduplication(self) -> Dict[str, Any]:
        """测试数据去重功能"""
        logger.info("Testing data deduplication")
        
        test_symbol = "DEDUP_TEST"
        
        # 创建包含重复数据的测试集
        base_timestamp = int(datetime.now().timestamp() * 1_000_000_000)
        
        test_data = []
        duplicate_timestamps = set()
        
        # 添加正常数据
        for i in range(100):
            timestamp = base_timestamp + i * 1_000_000_000  # 每秒一个数据点
            test_data.append({
                "symbol": test_symbol,
                "timestamp": timestamp,
                "price": 10.0 + i * 0.01,
                "volume": 1000 + i,
                "data_source": "original"
            })
        
        # 添加重复数据（相同时间戳，不同价格）
        for i in range(0, 20, 2):  # 每隔一个添加重复
            timestamp = base_timestamp + i * 1_000_000_000
            duplicate_timestamps.add(timestamp)
            test_data.append({
                "symbol": test_symbol,
                "timestamp": timestamp,
                "price": 10.0 + i * 0.01 + 0.001,  # 略微不同的价格
                "volume": 1000 + i + 1,
                "data_source": "duplicate"
            })
        
        # 转换为DataFrame
        df = pd.DataFrame(test_data)
        
        try:
            # 归档数据（应该触发去重）
            archive_result = await self.archiver.archive_k_data(test_symbol, df)
            
            if not archive_result:
                return {
                    "success": False,
                    "error": "Failed to archive test data",
                    "details": {}
                }
            
            # 查询存储的数据
            query_request = {
                "symbol": test_symbol,
                "data_type": "kline",
                "start_timestamp": base_timestamp,
                "end_timestamp": base_timestamp + 200 * 1_000_000_000,
                "limit": 1000
            }
            
            query_result = await self.unified_access.query_data(query_request)
            
            if not query_result.get("success", False):
                return {
                    "success": False,
                    "error": "Failed to query stored data",
                    "details": query_result
                }
            
            stored_data = query_result.get("data", [])
            
            # 验证去重效果
            stored_timestamps = set(record.get("timestamp") for record in stored_data)
            expected_unique_count = 100  # 原始数据数量
            actual_unique_count = len(stored_timestamps)
            
            # 检查重复时间戳是否被正确处理
            duplicate_check_passed = True
            for dup_timestamp in duplicate_timestamps:
                # 应该只保留一个版本的数据
                matching_records = [r for r in stored_data if r.get("timestamp") == dup_timestamp]
                if len(matching_records) != 1:
                    duplicate_check_passed = False
                    self.inconsistencies.append({
                        "type": "deduplication_failure",
                        "symbol": test_symbol,
                        "timestamp": dup_timestamp,
                        "description": f"Expected 1 record, found {len(matching_records)}"
                    })
            
            success = (actual_unique_count == expected_unique_count and 
                      duplicate_check_passed)
            
            return {
                "success": success,
                "details": {
                    "input_records": len(test_data),
                    "duplicate_timestamps": len(duplicate_timestamps),
                    "expected_unique_count": expected_unique_count,
                    "actual_unique_count": actual_unique_count,
                    "duplicate_check_passed": duplicate_check_passed,
                    "deduplication_rate": (len(test_data) - actual_unique_count) / len(test_data) * 100
                }
            }
            
        except Exception as e:
            logger.error(f"Data deduplication test failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "details": {}
            }
    
    async def _test_conflict_resolution(self) -> Dict[str, Any]:
        """测试冲突解决机制"""
        logger.info("Testing conflict resolution")
        
        test_symbol = "CONFLICT_TEST"
        base_timestamp = int(datetime.now().timestamp() * 1_000_000_000)
        
        try:
            # 模拟来自不同数据源的冲突数据
            pytdx_data = pd.DataFrame([
                {
                    "symbol": test_symbol,
                    "timestamp": base_timestamp,
                    "price": 10.00,
                    "volume": 1000,
                    "source": "pytdx"
                },
                {
                    "symbol": test_symbol,
                    "timestamp": base_timestamp + 1_000_000_000,
                    "price": 10.01,
                    "volume": 1100,
                    "source": "pytdx"
                }
            ])
            
            ctp_data = pd.DataFrame([
                {
                    "symbol": test_symbol,
                    "timestamp": base_timestamp,
                    "price": 10.005,  # 略微不同的价格
                    "volume": 1050,   # 略微不同的成交量
                    "source": "ctp"
                },
                {
                    "symbol": test_symbol,
                    "timestamp": base_timestamp + 2_000_000_000,
                    "price": 10.02,
                    "volume": 1200,
                    "source": "ctp"
                }
            ])
            
            # 先存储pytdx数据
            pytdx_result = await self.archiver.archive_k_data(test_symbol, pytdx_data)
            
            # 再存储ctp数据（应该触发冲突解决）
            ctp_result = await self.archiver.archive_k_data(test_symbol, ctp_data)
            
            if not (pytdx_result and ctp_result):
                return {
                    "success": False,
                    "error": "Failed to store conflict test data",
                    "details": {
                        "pytdx_result": pytdx_result,
                        "ctp_result": ctp_result
                    }
                }
            
            # 查询最终存储的数据
            query_request = {
                "symbol": test_symbol,
                "data_type": "kline",
                "start_timestamp": base_timestamp,
                "end_timestamp": base_timestamp + 3_000_000_000,
                "limit": 100
            }
            
            query_result = await self.unified_access.query_data(query_request)
            stored_data = query_result.get("data", [])
            
            # 验证冲突解决结果
            conflict_resolution_correct = True
            
            # 检查重叠时间戳的数据
            overlapping_record = next(
                (r for r in stored_data if r.get("timestamp") == base_timestamp), 
                None
            )
            
            if overlapping_record:
                # 根据配置的优先级策略验证（假设CTP优先）
                expected_price = 10.005  # CTP的价格
                actual_price = overlapping_record.get("price", 0)
                
                if abs(actual_price - expected_price) > 0.001:
                    conflict_resolution_correct = False
                    self.inconsistencies.append({
                        "type": "conflict_resolution_failure",
                        "symbol": test_symbol,
                        "timestamp": base_timestamp,
                        "description": f"Expected price {expected_price}, got {actual_price}"
                    })
            
            return {
                "success": conflict_resolution_correct,
                "details": {
                    "pytdx_records": len(pytdx_data),
                    "ctp_records": len(ctp_data),
                    "final_records": len(stored_data),
                    "conflict_resolution_correct": conflict_resolution_correct,
                    "overlapping_timestamp": base_timestamp,
                    "final_price": overlapping_record.get("price") if overlapping_record else None
                }
            }
            
        except Exception as e:
            logger.error(f"Conflict resolution test failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "details": {}
            }
    
    async def _validate_storage_layer_consistency(self) -> Dict[str, Any]:
        """验证存储层数据一致性"""
        logger.info("Validating storage layer consistency")
        
        test_symbol = "STORAGE_CONSISTENCY_TEST"
        
        try:
            # 创建跨越不同存储层的测试数据
            now = datetime.now()
            
            test_cases = [
                {
                    "name": "hot_storage",
                    "timestamp": now - timedelta(days=1),
                    "expected_layer": "hot"
                },
                {
                    "name": "warm_storage",
                    "timestamp": now - timedelta(days=30),
                    "expected_layer": "warm"
                },
                {
                    "name": "cold_storage",
                    "timestamp": now - timedelta(days=800),
                    "expected_layer": "cold"
                }
            ]
            
            consistency_results = []
            
            for case in test_cases:
                timestamp_ns = int(case["timestamp"].timestamp() * 1_000_000_000)
                
                # 创建测试数据
                test_data = pd.DataFrame([{
                    "symbol": test_symbol,
                    "timestamp": timestamp_ns,
                    "price": 10.0,
                    "volume": 1000,
                    "test_case": case["name"]
                }])
                
                # 存储数据
                archive_result = await self.archiver.archive_k_data(test_symbol, test_data)
                
                if not archive_result:
                    consistency_results.append({
                        "case": case["name"],
                        "success": False,
                        "error": "Failed to archive data"
                    })
                    continue
                
                # 验证数据被存储到正确的层
                selected_layer = self.storage_selector.select_storage_layer(timestamp_ns)
                
                # 查询数据验证存储
                query_request = {
                    "symbol": test_symbol,
                    "data_type": "kline",
                    "start_timestamp": timestamp_ns,
                    "end_timestamp": timestamp_ns + 1,
                    "limit": 1
                }
                
                query_result = await self.unified_access.query_data(query_request)
                
                case_success = (
                    query_result.get("success", False) and
                    len(query_result.get("data", [])) > 0 and
                    selected_layer == case["expected_layer"]
                )
                
                consistency_results.append({
                    "case": case["name"],
                    "success": case_success,
                    "expected_layer": case["expected_layer"],
                    "actual_layer": selected_layer,
                    "data_found": len(query_result.get("data", [])) > 0
                })
                
                if not case_success:
                    self.inconsistencies.append({
                        "type": "storage_layer_inconsistency",
                        "symbol": test_symbol,
                        "timestamp": timestamp_ns,
                        "description": f"Expected {case['expected_layer']}, got {selected_layer}"
                    })
            
            overall_success = all(result["success"] for result in consistency_results)
            
            return {
                "success": overall_success,
                "details": {
                    "test_cases": consistency_results,
                    "total_cases": len(test_cases),
                    "passed_cases": sum(1 for r in consistency_results if r["success"])
                }
            }
            
        except Exception as e:
            logger.error(f"Storage layer consistency validation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "details": {}
            }
    
    async def _check_data_integrity(self) -> Dict[str, Any]:
        """检查数据完整性"""
        logger.info("Checking data integrity")
        
        test_symbol = "INTEGRITY_TEST"
        
        try:
            # 创建完整的测试数据集
            base_timestamp = int(datetime.now().timestamp() * 1_000_000_000)
            
            original_data = []
            for i in range(100):
                original_data.append({
                    "symbol": test_symbol,
                    "timestamp": base_timestamp + i * 1_000_000_000,
                    "price": 10.0 + i * 0.01,
                    "volume": 1000 + i,
                    "sequence": i
                })
            
            df = pd.DataFrame(original_data)
            
            # 存储数据
            archive_result = await self.archiver.archive_k_data(test_symbol, df)
            
            if not archive_result:
                return {
                    "success": False,
                    "error": "Failed to archive integrity test data",
                    "details": {}
                }
            
            # 查询所有数据
            query_request = {
                "symbol": test_symbol,
                "data_type": "kline",
                "start_timestamp": base_timestamp,
                "end_timestamp": base_timestamp + 200 * 1_000_000_000,
                "limit": 1000
            }
            
            query_result = await self.unified_access.query_data(query_request)
            stored_data = query_result.get("data", [])
            
            # 完整性检查
            integrity_issues = []
            
            # 1. 数据数量检查
            expected_count = len(original_data)
            actual_count = len(stored_data)
            
            if actual_count != expected_count:
                integrity_issues.append(f"Data count mismatch: expected {expected_count}, got {actual_count}")
            
            # 2. 时间序列连续性检查
            stored_timestamps = sorted([r.get("timestamp") for r in stored_data])
            
            for i in range(1, len(stored_timestamps)):
                expected_gap = 1_000_000_000  # 1秒
                actual_gap = stored_timestamps[i] - stored_timestamps[i-1]
                
                if actual_gap != expected_gap:
                    integrity_issues.append(f"Time gap inconsistency at index {i}: expected {expected_gap}, got {actual_gap}")
            
            # 3. 数据值合理性检查
            for record in stored_data:
                price = record.get("price", 0)
                volume = record.get("volume", 0)
                
                if price <= 0:
                    integrity_issues.append(f"Invalid price: {price}")
                
                if volume <= 0:
                    integrity_issues.append(f"Invalid volume: {volume}")
            
            # 4. 数据完整性哈希检查
            original_hash = self._calculate_data_hash(original_data)
            stored_hash = self._calculate_data_hash(stored_data)
            
            # 由于可能的数据转换，不要求哈希完全相同，但检查关键字段
            key_fields_match = True
            for orig, stored in zip(original_data, stored_data):
                if (orig["timestamp"] != stored.get("timestamp") or
                    abs(orig["price"] - stored.get("price", 0)) > 0.001):
                    key_fields_match = False
                    break
            
            success = (len(integrity_issues) == 0 and key_fields_match)
            
            return {
                "success": success,
                "details": {
                    "expected_count": expected_count,
                    "actual_count": actual_count,
                    "integrity_issues": integrity_issues,
                    "key_fields_match": key_fields_match,
                    "original_hash": original_hash,
                    "stored_hash": stored_hash
                }
            }
            
        except Exception as e:
            logger.error(f"Data integrity check failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "details": {}
            }
    
    async def _validate_data_accuracy(self) -> Dict[str, Any]:
        """验证数据准确性"""
        logger.info("Validating data accuracy")
        
        test_symbol = "ACCURACY_TEST"
        
        try:
            # 创建已知准确性的测试数据
            reference_data = [
                {
                    "symbol": test_symbol,
                    "timestamp": int(datetime(2024, 1, 1, 9, 30, 0).timestamp() * 1_000_000_000),
                    "price": 10.00,
                    "volume": 1000,
                    "high": 10.05,
                    "low": 9.95,
                    "open": 9.98,
                    "close": 10.00
                },
                {
                    "symbol": test_symbol,
                    "timestamp": int(datetime(2024, 1, 1, 9, 31, 0).timestamp() * 1_000_000_000),
                    "price": 10.01,
                    "volume": 1100,
                    "high": 10.06,
                    "low": 9.96,
                    "open": 10.00,
                    "close": 10.01
                }
            ]
            
            df = pd.DataFrame(reference_data)
            
            # 存储参考数据
            archive_result = await self.archiver.archive_k_data(test_symbol, df)
            
            if not archive_result:
                return {
                    "success": False,
                    "error": "Failed to archive accuracy test data",
                    "details": {}
                }
            
            # 查询数据进行准确性验证
            query_request = {
                "symbol": test_symbol,
                "data_type": "kline",
                "start_timestamp": reference_data[0]["timestamp"],
                "end_timestamp": reference_data[-1]["timestamp"] + 1,
                "limit": 10
            }
            
            query_result = await self.unified_access.query_data(query_request)
            stored_data = query_result.get("data", [])
            
            # 准确性验证
            accuracy_issues = []
            
            for i, (reference, stored) in enumerate(zip(reference_data, stored_data)):
                # 检查关键字段的准确性
                for field in ["timestamp", "price", "volume"]:
                    ref_value = reference[field]
                    stored_value = stored.get(field)
                    
                    if field == "timestamp":
                        if ref_value != stored_value:
                            accuracy_issues.append(f"Record {i}: timestamp mismatch - expected {ref_value}, got {stored_value}")
                    else:
                        # 数值字段允许小的浮点误差
                        if abs(ref_value - (stored_value or 0)) > 0.001:
                            accuracy_issues.append(f"Record {i}: {field} mismatch - expected {ref_value}, got {stored_value}")
                
                # 检查OHLC数据的逻辑一致性
                if all(field in stored for field in ["open", "high", "low", "close"]):
                    high = stored.get("high", 0)
                    low = stored.get("low", 0)
                    open_price = stored.get("open", 0)
                    close_price = stored.get("close", 0)
                    
                    if not (low <= open_price <= high and low <= close_price <= high):
                        accuracy_issues.append(f"Record {i}: OHLC logic inconsistency")
            
            success = len(accuracy_issues) == 0
            
            return {
                "success": success,
                "details": {
                    "reference_records": len(reference_data),
                    "stored_records": len(stored_data),
                    "accuracy_issues": accuracy_issues,
                    "accuracy_rate": (len(reference_data) - len(accuracy_issues)) / len(reference_data) * 100 if reference_data else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Data accuracy validation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "details": {}
            }
    
    async def _validate_cross_storage_consistency(self) -> Dict[str, Any]:
        """验证跨存储层一致性"""
        logger.info("Validating cross-storage consistency")
        
        test_symbol = "CROSS_STORAGE_TEST"
        
        try:
            # 创建跨越多个存储层的数据
            now = datetime.now()
            
            # 热存储数据（最近1天）
            hot_data = []
            for i in range(10):
                timestamp = now - timedelta(hours=i)
                hot_data.append({
                    "symbol": test_symbol,
                    "timestamp": int(timestamp.timestamp() * 1_000_000_000),
                    "price": 10.0 + i * 0.01,
                    "volume": 1000 + i,
                    "layer": "hot"
                })
            
            # 温存储数据（30天前）
            warm_data = []
            for i in range(10):
                timestamp = now - timedelta(days=30) - timedelta(hours=i)
                warm_data.append({
                    "symbol": test_symbol,
                    "timestamp": int(timestamp.timestamp() * 1_000_000_000),
                    "price": 9.0 + i * 0.01,
                    "volume": 900 + i,
                    "layer": "warm"
                })
            
            # 存储数据到不同层
            hot_df = pd.DataFrame(hot_data)
            warm_df = pd.DataFrame(warm_data)
            
            hot_result = await self.archiver.archive_k_data(test_symbol, hot_df)
            warm_result = await self.archiver.archive_k_data(test_symbol, warm_df)
            
            if not (hot_result and warm_result):
                return {
                    "success": False,
                    "error": "Failed to store cross-storage test data",
                    "details": {}
                }
            
            # 验证跨存储层查询的一致性
            consistency_checks = []
            
            # 1. 单层查询一致性
            for layer_data, layer_name in [(hot_data, "hot"), (warm_data, "warm")]:
                start_ts = min(d["timestamp"] for d in layer_data)
                end_ts = max(d["timestamp"] for d in layer_data)
                
                query_request = {
                    "symbol": test_symbol,
                    "data_type": "kline",
                    "start_timestamp": start_ts,
                    "end_timestamp": end_ts + 1,
                    "limit": 100
                }
                
                query_result = await self.unified_access.query_data(query_request)
                stored_data = query_result.get("data", [])
                
                # 验证数据完整性
                expected_count = len(layer_data)
                actual_count = len([d for d in stored_data if start_ts <= d.get("timestamp", 0) <= end_ts])
                
                consistency_checks.append({
                    "layer": layer_name,
                    "expected_count": expected_count,
                    "actual_count": actual_count,
                    "consistent": expected_count == actual_count
                })
            
            # 2. 跨层查询一致性
            all_start_ts = min(d["timestamp"] for d in hot_data + warm_data)
            all_end_ts = max(d["timestamp"] for d in hot_data + warm_data)
            
            cross_query_request = {
                "symbol": test_symbol,
                "data_type": "kline",
                "start_timestamp": all_start_ts,
                "end_timestamp": all_end_ts + 1,
                "limit": 1000
            }
            
            cross_query_result = await self.unified_access.query_data(cross_query_request)
            all_stored_data = cross_query_result.get("data", [])
            
            # 验证跨层数据的时间顺序
            timestamps = [d.get("timestamp") for d in all_stored_data]
            time_ordered = timestamps == sorted(timestamps)
            
            # 验证数据无重复
            unique_timestamps = len(set(timestamps))
            no_duplicates = unique_timestamps == len(timestamps)
            
            consistency_checks.append({
                "layer": "cross_storage",
                "total_records": len(all_stored_data),
                "time_ordered": time_ordered,
                "no_duplicates": no_duplicates,
                "consistent": time_ordered and no_duplicates
            })
            
            overall_success = all(check["consistent"] for check in consistency_checks)
            
            return {
                "success": overall_success,
                "details": {
                    "consistency_checks": consistency_checks,
                    "hot_data_count": len(hot_data),
                    "warm_data_count": len(warm_data),
                    "total_stored_count": len(all_stored_data)
                }
            }
            
        except Exception as e:
            logger.error(f"Cross-storage consistency validation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "details": {}
            }
    
    def _calculate_data_hash(self, data: List[Dict]) -> str:
        """计算数据哈希值"""
        # 创建数据的标准化字符串表示
        normalized_data = []
        for record in data:
            # 只包含关键字段进行哈希计算
            key_fields = {
                "symbol": record.get("symbol", ""),
                "timestamp": record.get("timestamp", 0),
                "price": round(record.get("price", 0), 6),  # 保留6位小数
                "volume": record.get("volume", 0)
            }
            normalized_data.append(json.dumps(key_fields, sort_keys=True))
        
        # 计算哈希
        data_string = "|".join(sorted(normalized_data))
        return hashlib.md5(data_string.encode()).hexdigest()
    
    def _generate_consistency_report(self, results: Dict[str, Any]):
        """生成一致性验证报告"""
        report_file = f"tests/integration/data_consistency_report_{int(time.time())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # 生成简化的控制台报告
        print("\n" + "="*80)
        print("DATA CONSISTENCY VALIDATION REPORT")
        print("="*80)
        print(f"Timestamp: {results['timestamp']}")
        print(f"Duration: {results['duration_seconds']:.2f} seconds")
        print(f"Overall Result: {'PASSED' if results['overall_success'] else 'FAILED'}")
        print()
        
        print("Test Results:")
        for test_name, test_result in results["tests"].items():
            status = "PASSED" if test_result["success"] else "FAILED"
            print(f"  {test_name.replace('_', ' ').title()}: {status}")
        
        if results["inconsistencies"]:
            print(f"\nInconsistencies Found: {len(results['inconsistencies'])}")
            for i, issue in enumerate(results["inconsistencies"][:5]):  # 显示前5个
                print(f"  {i+1}. {issue['type']}: {issue['description']}")
            
            if len(results["inconsistencies"]) > 5:
                print(f"  ... and {len(results['inconsistencies']) - 5} more")
        
        print("="*80)
        
        logger.info(f"Consistency validation report saved to: {report_file}")


async def main():
    """主函数"""
    # 测试配置
    config = {
        "archiver": {
            "batch_size": 1000,
            "validation_enabled": True,
            "deduplication_enabled": True
        },
        "storage": {
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 1
            },
            "clickhouse": {
                "host": "localhost",
                "port": 8123,
                "database": "test_market_data"
            },
            "hot_storage_days": 7,
            "warm_storage_days": 730
        }
    }
    
    # 创建验证器并运行测试
    validator = DataConsistencyValidator(config)
    
    try:
        results = await validator.validate_all_consistency()
        
        # 返回适当的退出码
        sys.exit(0 if results["overall_success"] else 1)
        
    except Exception as e:
        logger.error(f"Data consistency validation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())