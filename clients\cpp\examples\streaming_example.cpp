#include "financial_data_sdk.h"
#include <iostream>
#include <atomic>
#include <thread>
#include <chrono>
#include <signal.h>

using namespace financial_data::sdk;

std::atomic<bool> running{true};

void SignalHandler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
    running = false;
}

int main() {
    std::cout << "Financial Data SDK - Streaming Example" << std::endl;
    std::cout << "======================================" << std::endl;

    // Set up signal handler for graceful shutdown
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);

    // Configure connection
    ConnectionConfig config;
    config.server_address = "localhost:50051";
    config.connect_timeout = std::chrono::milliseconds(5000);
    config.enable_compression = true;
    config.enable_keepalive = true;
    config.keepalive_time = std::chrono::seconds(30);

    // Create SDK instance
    FinancialDataSDK sdk(config);

    // Statistics tracking
    std::atomic<uint64_t> tick_count{0};
    std::atomic<uint64_t> error_count{0};
    auto start_time = std::chrono::steady_clock::now();

    // Set up callbacks
    sdk.SetErrorCallback([&error_count](const ErrorInfo& error) {
        error_count++;
        std::cerr << "Error [" << static_cast<int>(error.code) << "]: " 
                  << error.message << std::endl;
    });

    sdk.SetConnectionStatusCallback([](bool connected) {
        std::cout << "Connection status changed: " 
                  << (connected ? "Connected" : "Disconnected") << std::endl;
    });

    // Tick data callback with latency measurement
    auto tick_callback = [&tick_count](const StandardTick& tick) {
        tick_count++;
        
        // Calculate end-to-end latency
        auto now = std::chrono::high_resolution_clock::now();
        auto now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            now.time_since_epoch()).count();
        
        auto latency_ns = now_ns - tick.timestamp_ns;
        auto latency_us = latency_ns / 1000;
        
        // Print every 100th tick to avoid spam
        if (tick_count % 100 == 0) {
            std::cout << "Tick #" << tick_count 
                      << " - Symbol: " << tick.symbol
                      << ", Price: " << tick.last_price
                      << ", Latency: " << latency_us << " μs" << std::endl;
        }
        
        // Alert if latency exceeds threshold (5 microseconds as per requirement)
        if (latency_us > 5) {
            std::cout << "WARNING: High latency detected: " << latency_us 
                      << " μs for " << tick.symbol << std::endl;
        }
    };

    try {
        // Connect to server
        std::cout << "Connecting to server..." << std::endl;
        if (!sdk.Connect()) {
            std::cerr << "Failed to connect to server" << std::endl;
            return 1;
        }

        std::cout << "Connected successfully!" << std::endl;

        // Configure subscription
        SubscriptionConfig sub_config;
        sub_config.symbols = {"CU2409", "AL2409", "ZN2409", "AU2412", "AG2412"};
        sub_config.exchange = "SHFE";
        sub_config.include_level2 = true;
        sub_config.buffer_size = 1000;
        sub_config.heartbeat_interval = std::chrono::milliseconds(10000);

        // Start streaming
        std::cout << "Starting tick data stream for symbols: ";
        for (const auto& symbol : sub_config.symbols) {
            std::cout << symbol << " ";
        }
        std::cout << std::endl;

        if (!sdk.SubscribeTickData(sub_config, tick_callback)) {
            std::cerr << "Failed to subscribe to tick data" << std::endl;
            return 1;
        }

        std::cout << "Streaming started. Press Ctrl+C to stop." << std::endl;

        // Statistics reporting thread
        std::thread stats_thread([&]() {
            while (running) {
                std::this_thread::sleep_for(std::chrono::seconds(10));
                
                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                    now - start_time).count();
                
                if (elapsed > 0) {
                    auto rate = tick_count.load() / elapsed;
                    std::cout << "\n--- Statistics (after " << elapsed << "s) ---" << std::endl;
                    std::cout << "Total ticks received: " << tick_count.load() << std::endl;
                    std::cout << "Average rate: " << rate << " ticks/second" << std::endl;
                    std::cout << "Total errors: " << error_count.load() << std::endl;
                    
                    auto sdk_stats = sdk.GetStatistics();
                    std::cout << "SDK avg latency: " << sdk_stats.avg_latency.count() 
                              << " μs" << std::endl;
                    std::cout << "SDK max latency: " << sdk_stats.max_latency.count() 
                              << " μs" << std::endl;
                    std::cout << "Connection status: " 
                              << (sdk.IsConnected() ? "Connected" : "Disconnected") 
                              << std::endl;
                    std::cout << "-----------------------------------\n" << std::endl;
                }
            }
        });

        // Main loop - just wait for shutdown signal
        while (running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            // Check connection health
            if (!sdk.IsConnected()) {
                std::cout << "Connection lost, attempting to reconnect..." << std::endl;
                if (sdk.Connect()) {
                    std::cout << "Reconnected successfully!" << std::endl;
                    // Re-subscribe after reconnection
                    sdk.SubscribeTickData(sub_config, tick_callback);
                }
            }
        }

        // Cleanup
        stats_thread.join();
        
        std::cout << "Unsubscribing from all streams..." << std::endl;
        sdk.UnsubscribeAll();
        
        std::cout << "Disconnecting..." << std::endl;
        sdk.Disconnect();

        // Final statistics
        auto final_time = std::chrono::steady_clock::now();
        auto total_elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            final_time - start_time).count();
        
        std::cout << "\n=== Final Statistics ===" << std::endl;
        std::cout << "Total runtime: " << total_elapsed << " seconds" << std::endl;
        std::cout << "Total ticks received: " << tick_count.load() << std::endl;
        std::cout << "Total errors: " << error_count.load() << std::endl;
        
        if (total_elapsed > 0) {
            std::cout << "Average throughput: " 
                      << (tick_count.load() / total_elapsed) << " ticks/second" << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "Streaming example completed!" << std::endl;
    return 0;
}