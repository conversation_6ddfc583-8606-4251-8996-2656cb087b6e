.App {
  text-align: center;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 6px;
}

.trigger {
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.metric-card {
  text-align: center;
  padding: 20px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin: 8px 0;
}

.metric-label {
  color: #666;
  font-size: 14px;
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-online {
  background-color: #52c41a;
}

.status-warning {
  background-color: #faad14;
}

.status-error {
  background-color: #f5222d;
}

.chart-container {
  height: 300px;
  margin: 20px 0;
}

.data-table {
  margin-top: 20px;
}

.alert-item {
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-bottom: 8px;
}

.alert-critical {
  border-color: #f5222d;
  background-color: #fff2f0;
}

.alert-warning {
  border-color: #faad14;
  background-color: #fffbe6;
}

.alert-info {
  border-color: #1890ff;
  background-color: #f0f9ff;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.config-section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #262626;
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 400px;
}

.login-title {
  text-align: center;
  margin-bottom: 32px;
  color: #262626;
  font-size: 24px;
  font-weight: bold;
}

.query-form {
  background: #fafafa;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.export-buttons {
  margin-top: 16px;
}

.export-buttons .ant-btn {
  margin-right: 8px;
}