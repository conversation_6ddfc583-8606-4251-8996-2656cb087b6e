#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <unordered_map>
#include <functional>

#include "../data_models.h"

namespace financial_data {

/**
 * @brief 存储层健康状态
 */
enum class StorageLayerHealth {
    HEALTHY = 0,
    DEGRADED = 1,
    UNHEALTHY = 2,
    UNKNOWN = 3
};

/**
 * @brief 存储层性能指标
 */
struct StorageLayerMetrics {
    std::atomic<uint64_t> total_requests{0};
    std::atomic<uint64_t> successful_requests{0};
    std::atomic<uint64_t> failed_requests{0};
    std::atomic<double> avg_response_time_ms{0.0};
    std::atomic<double> success_rate{1.0};
    std::chrono::steady_clock::time_point last_update;
    
    void UpdateMetrics(bool success, double response_time_ms) {
        total_requests++;
        if (success) {
            successful_requests++;
        } else {
            failed_requests++;
        }
        
        // 更新平均响应时间
        uint64_t total = total_requests.load();
        double current_avg = avg_response_time_ms.load();
        double new_avg = (current_avg * (total - 1) + response_time_ms) / total;
        avg_response_time_ms.store(new_avg);
        
        // 更新成功率
        double new_success_rate = double(successful_requests.load()) / total;
        success_rate.store(new_success_rate);
        
        last_update = std::chrono::steady_clock::now();
    }
    
    void Reset() {
        total_requests = 0;
        successful_requests = 0;
        failed_requests = 0;
        avg_response_time_ms = 0.0;
        success_rate = 1.0;
        last_update = std::chrono::steady_clock::now();
    }
};

/**
 * @brief 存储层状态信息
 */
struct StorageLayerStatus {
    StorageLayer layer;
    StorageLayerHealth health;
    StorageLayerMetrics metrics;
    std::string error_message;
    std::chrono::steady_clock::time_point last_health_check;
    
    // 健康检查配置
    double health_threshold_success_rate = 0.95;  // 95%成功率阈值
    double degraded_threshold_success_rate = 0.80; // 80%成功率降级阈值
    double max_response_time_ms = 1000.0;          // 最大响应时间阈值
    
    StorageLayerHealth DetermineHealth() const {
        double current_success_rate = metrics.success_rate.load();
        double current_response_time = metrics.avg_response_time_ms.load();
        
        if (current_success_rate >= health_threshold_success_rate && 
            current_response_time <= max_response_time_ms) {
            return StorageLayerHealth::HEALTHY;
        } else if (current_success_rate >= degraded_threshold_success_rate) {
            return StorageLayerHealth::DEGRADED;
        } else {
            return StorageLayerHealth::UNHEALTHY;
        }
    }
    
    bool IsAvailable() const {
        return health != StorageLayerHealth::UNHEALTHY;
    }
    
    double GetPriorityScore() const {
        // 基于健康状态和性能计算优先级分数
        double health_score = 0.0;
        switch (health) {
            case StorageLayerHealth::HEALTHY:
                health_score = 1.0;
                break;
            case StorageLayerHealth::DEGRADED:
                health_score = 0.7;
                break;
            case StorageLayerHealth::UNHEALTHY:
                health_score = 0.1;
                break;
            default:
                health_score = 0.5;
                break;
        }
        
        // 响应时间权重
        double response_time_score = std::max(0.1, 
            1.0 - (metrics.avg_response_time_ms.load() / max_response_time_ms));
        
        // 成功率权重
        double success_rate_score = metrics.success_rate.load();
        
        return health_score * 0.5 + response_time_score * 0.3 + success_rate_score * 0.2;
    }
};

/**
 * @brief 存储层选择策略
 */
enum class StorageSelectionStrategy {
    TIME_BASED = 0,      // 基于时间的默认策略
    PERFORMANCE_BASED = 1, // 基于性能的策略
    LOAD_BALANCED = 2,   // 负载均衡策略
    FAILOVER_ONLY = 3    // 仅故障转移策略
};

/**
 * @brief 存储层选择配置
 */
struct StorageSelectionConfig {
    StorageSelectionStrategy strategy = StorageSelectionStrategy::TIME_BASED;
    
    // 时间阈值配置
    int hot_storage_days = 7;
    int warm_storage_days = 730;
    
    // 健康检查配置
    std::chrono::seconds health_check_interval{30};
    std::chrono::seconds health_check_timeout{5};
    int max_consecutive_failures = 3;
    
    // 故障转移配置
    bool enable_automatic_failover = true;
    std::chrono::seconds failover_cooldown{60};
    int max_failover_attempts = 2;
    
    // 性能阈值配置
    double max_acceptable_response_time_ms = 1000.0;
    double min_acceptable_success_rate = 0.90;
    
    // 负载均衡配置
    bool enable_load_balancing = false;
    double load_balance_threshold = 0.8; // 80%负载时开始均衡
};

/**
 * @brief 智能存储层选择器
 * 
 * 根据时间戳、存储层健康状态和性能指标智能选择最优存储层
 */
class StorageLayerSelector {
public:
    explicit StorageLayerSelector(const StorageSelectionConfig& config = StorageSelectionConfig{});
    ~StorageLayerSelector();
    
    // 初始化和生命周期管理
    bool Initialize();
    void Shutdown();
    bool IsRunning() const { return running_.load(); }
    
    // 存储层选择接口
    StorageLayer SelectStorageLayer(int64_t timestamp_ns) const;
    std::vector<StorageLayer> SelectStorageLayers(int64_t start_timestamp_ns, 
                                                 int64_t end_timestamp_ns) const;
    
    // 带故障转移的存储层选择
    StorageLayer SelectWithFailover(int64_t timestamp_ns, 
                                   const std::vector<StorageLayer>& failed_layers = {}) const;
    std::vector<StorageLayer> SelectWithFailover(int64_t start_timestamp_ns, 
                                                int64_t end_timestamp_ns,
                                                const std::vector<StorageLayer>& failed_layers = {}) const;
    
    // 性能优化的存储层选择
    StorageLayer SelectOptimalLayer(int64_t timestamp_ns) const;
    
    // 存储层健康管理
    void UpdateLayerMetrics(StorageLayer layer, bool success, double response_time_ms);
    void ReportLayerFailure(StorageLayer layer, const std::string& error_message);
    void ReportLayerRecovery(StorageLayer layer);
    
    // 健康检查接口
    void TriggerHealthCheck();
    StorageLayerStatus GetLayerStatus(StorageLayer layer) const;
    std::vector<StorageLayerStatus> GetAllLayerStatus() const;
    
    // 故障转移管理
    bool IsLayerInFailover(StorageLayer layer) const;
    void EnableLayer(StorageLayer layer);
    void DisableLayer(StorageLayer layer, const std::string& reason = "");
    
    // 配置管理
    bool UpdateConfig(const StorageSelectionConfig& config);
    StorageSelectionConfig GetConfig() const { return config_; }
    
    // 统计信息
    struct SelectionStatistics {
        std::atomic<uint64_t> total_selections{0};
        std::atomic<uint64_t> hot_selections{0};
        std::atomic<uint64_t> warm_selections{0};
        std::atomic<uint64_t> cold_selections{0};
        std::atomic<uint64_t> failover_selections{0};
        std::atomic<uint64_t> optimal_selections{0};
        
        void Reset() {
            total_selections = 0;
            hot_selections = 0;
            warm_selections = 0;
            cold_selections = 0;
            failover_selections = 0;
            optimal_selections = 0;
        }
    };
    
    SelectionStatistics GetStatistics() const;
    void ResetStatistics();

private:
    // 健康检查回调类型
    using HealthCheckCallback = std::function<bool(StorageLayer)>;
    
    // 注册健康检查回调
    void RegisterHealthCheckCallback(StorageLayer layer, HealthCheckCallback callback);
    
    // 内部选择逻辑
    StorageLayer SelectByTime(int64_t timestamp_ns) const;
    StorageLayer SelectByPerformance(int64_t timestamp_ns) const;
    StorageLayer SelectByLoadBalance(int64_t timestamp_ns) const;
    
    // 健康检查实现
    void PerformHealthCheck();
    void CheckLayerHealth(StorageLayer layer);
    bool IsLayerHealthy(StorageLayer layer) const;
    
    // 故障转移逻辑
    bool ShouldFailover(StorageLayer layer) const;
    StorageLayer FindFailoverTarget(StorageLayer failed_layer, 
                                   const std::vector<StorageLayer>& excluded = {}) const;
    
    // 时间转换辅助方法
    int64_t GetCurrentTimestampNs() const;
    int64_t DaysToNanoseconds(int days) const;
    bool IsTimestampInHotRange(int64_t timestamp_ns) const;
    bool IsTimestampInWarmRange(int64_t timestamp_ns) const;
    
    // 配置和状态
    StorageSelectionConfig config_;
    std::atomic<bool> running_{false};
    std::atomic<bool> shutdown_requested_{false};
    
    // 存储层状态管理
    mutable std::mutex status_mutex_;
    std::unordered_map<StorageLayer, StorageLayerStatus> layer_status_;
    
    // 故障转移状态
    mutable std::mutex failover_mutex_;
    std::unordered_map<StorageLayer, std::chrono::steady_clock::time_point> failover_timestamps_;
    std::unordered_map<StorageLayer, int> consecutive_failures_;
    
    // 健康检查
    std::thread health_check_thread_;
    std::unordered_map<StorageLayer, HealthCheckCallback> health_check_callbacks_;
    
    // 统计信息
    mutable SelectionStatistics statistics_;
    
    // 日志记录器
    std::shared_ptr<spdlog::logger> logger_;
    
    void InitializeLogger();
    void InitializeLayerStatus();
    void UpdateSelectionStatistics(StorageLayer layer, bool is_failover = false) const;
};

/**
 * @brief 存储层选择器工厂
 */
class StorageLayerSelectorFactory {
public:
    static std::unique_ptr<StorageLayerSelector> CreateDefault();
    static std::unique_ptr<StorageLayerSelector> CreateHighAvailability();
    static std::unique_ptr<StorageLayerSelector> CreatePerformanceOptimized();
    static std::unique_ptr<StorageLayerSelector> CreateFromConfig(const std::string& config_file);
};

} // namespace financial_data