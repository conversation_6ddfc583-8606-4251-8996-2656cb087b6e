/**
 * @file test_utils.h
 * @brief Test utilities and mock implementations for performance testing
 */

#pragma once

#include <string>
#include <vector>
#include <memory>
#include <optional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <unordered_map>
#include <map>
#include <random>

namespace performance_tests {

// Test data structures
struct TestTick {
    std::string symbol;
    uint64_t timestamp;
    uint64_t sequence;
    double last_price;
    uint64_t volume;
    double turnover;
    
    TestTick();
    size_t GetSerializedSize() const;
};

struct SystemResourceUsage {
    double cpu_usage_percent = 0.0;
    double memory_usage_mb = 0.0;
};

struct HttpResponse {
    int status_code = 200;
    std::string body;
};

// Mock implementations for testing

class MockDataBus {
public:
    class Consumer {
    public:
        virtual ~Consumer() = default;
        virtual std::optional<TestTick> ConsumeMessage(std::chrono::milliseconds timeout) = 0;
        virtual bool HasPendingMessages() = 0;
    };
    
    MockDataBus();
    ~MockDataBus();
    
    bool Publish(const TestTick& tick);
    std::unique_ptr<Consumer> CreateConsumer();
    
private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

class MockWebSocketServer {
public:
    MockWebSocketServer();
    
    bool BroadcastTick(const TestTick& tick);
    uint64_t GetProcessedMessageCount() const;
    bool IsActive() const { return active_.load(); }
    void Activate() { active_ = true; }
    void SimulateFailure() { active_ = false; }
    
private:
    std::atomic<bool> active_;
    std::atomic<uint64_t> message_count_;
};

class MockWebSocketClient {
public:
    MockWebSocketClient();
    
    bool Connect(const std::string& url);
    bool Subscribe(const std::vector<std::string>& symbols);
    std::optional<std::string> WaitForMessage(std::chrono::milliseconds timeout);
    uint64_t GetReceivedMessageCount() const;
    bool IsConnected() const;
    bool WaitForReconnection(std::chrono::milliseconds timeout);
    
private:
    std::atomic<bool> connected_;
    std::atomic<uint64_t> message_count_;
    std::vector<std::string> subscribed_symbols_;
};

class MockGrpcServer {
public:
    MockGrpcServer();
    
    bool StreamTick(const TestTick& tick);
    uint64_t GetProcessedMessageCount() const;
    bool IsActive() const { return active_.load(); }
    void Activate() { active_ = true; }
    void SimulateFailure() { active_ = false; }
    
private:
    std::atomic<bool> active_;
    std::atomic<uint64_t> message_count_;
};

class MockGrpcClient {
public:
    class TickStream {
    public:
        virtual ~TickStream() = default;
        virtual std::optional<TestTick> WaitForTick(std::chrono::milliseconds timeout) = 0;
    };
    
    MockGrpcClient();
    
    bool Connect(const std::string& endpoint);
    std::vector<TestTick> GetHistoricalTicks(
        const std::string& symbol,
        std::chrono::system_clock::time_point start,
        std::chrono::system_clock::time_point end,
        uint32_t limit);
    std::unique_ptr<TickStream> OpenTickStream(const std::vector<std::string>& symbols);
    bool IsConnected() const { return connected_.load(); }
    
private:
    class MockTickStream;
    std::atomic<bool> connected_;
};

class MockRedisClient {
public:
    MockRedisClient(const std::string& instance_name = "default");
    
    bool StoreTick(const TestTick& tick);
    std::optional<TestTick> GetLatestTick(const std::string& symbol);
    std::optional<TestTick> GetTickBySequence(const std::string& symbol, uint64_t sequence);
    void FlushPipeline();
    void SimulateFailure();
    bool WaitForActivation(std::chrono::milliseconds timeout);
    bool IsActive() const;
    
private:
    std::string instance_name_;
    std::atomic<bool> active_;
    std::mutex data_mutex_;
    std::unordered_map<std::string, std::map<uint64_t, TestTick>> stored_ticks_;
};

class MockClickHouseClient {
public:
    MockClickHouseClient(const std::string& instance_name = "default");
    
    bool StoreTick(const TestTick& tick);
    bool BatchInsertTicks(const std::vector<TestTick>& ticks);
    std::vector<TestTick> QueryHistoricalTicks(
        const std::string& symbol,
        std::chrono::system_clock::time_point start,
        std::chrono::system_clock::time_point end,
        uint32_t limit);
    std::optional<TestTick> GetTickBySequence(const std::string& symbol, uint64_t sequence);
    void SimulateFailure();
    bool WaitForActivation(std::chrono::milliseconds timeout);
    bool IsActive() const;
    
private:
    std::string instance_name_;
    std::atomic<bool> active_;
    std::mutex data_mutex_;
    std::vector<TestTick> stored_ticks_;
};

class MockHttpClient {
public:
    HttpResponse Get(const std::string& endpoint) {
        // Simulate HTTP request latency
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        return HttpResponse{200, "{\"status\":\"ok\"}"};
    }
};

class MockMarketDataServer {
public:
    MockMarketDataServer(const std::string& name) : name_(name), active_(true), message_count_(0) {}
    
    bool PublishTick(const TestTick& tick) {
        if (!active_) return false;
        message_count_++;
        return true;
    }
    
    bool IsActive() const { return active_.load(); }
    void Activate() { active_ = true; }
    void SimulateFailure() { active_ = false; }
    uint64_t GetProcessedMessageCount() const { return message_count_.load(); }
    
private:
    std::string name_;
    std::atomic<bool> active_;
    std::atomic<uint64_t> message_count_;
};

class MockTestClient {
public:
    MockTestClient() : connected_(false) {}
    
    bool Connect(const std::string& endpoint) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        connected_ = true;
        return true;
    }
    
    bool Subscribe(const std::vector<std::string>& symbols) { return connected_; }
    std::optional<TestTick> WaitForTick(std::chrono::milliseconds timeout) {
        if (!connected_) return std::nullopt;
        std::this_thread::sleep_for(std::chrono::microseconds(50));
        return TestTick{};
    }
    
    std::optional<std::string> WaitForMessage(std::chrono::milliseconds timeout) {
        if (!connected_) return std::nullopt;
        std::this_thread::sleep_for(std::chrono::microseconds(50));
        return "test_message";
    }
    
    bool IsConnected() const { return connected_.load(); }
    bool WaitForReconnection(std::chrono::milliseconds timeout) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        connected_ = true;
        return true;
    }
    
    void SetPrimaryEndpoint(const std::string& endpoint) { primary_endpoint_ = endpoint; }
    void SetBackupEndpoint(const std::string& endpoint) { backup_endpoint_ = endpoint; }
    bool ReconnectToBackup(std::chrono::milliseconds timeout) {
        std::this_thread::sleep_for(timeout);
        connected_ = true;
        return true;
    }
    
private:
    std::atomic<bool> connected_;
    std::string primary_endpoint_;
    std::string backup_endpoint_;
};

class MockLoadBalancer {
public:
    std::string GetPrimaryEndpoint() const { return "tcp://primary:5555"; }
    bool WaitForFailoverDetection(std::chrono::milliseconds timeout) {
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        return true;
    }
    void SwitchToBackup() {}
};

class MockNetworkSimulator {
public:
    void SimulateNetworkPartition(const std::string& endpoint) {}
    void RestoreNetwork(const std::string& endpoint) {}
};

class MockStorage {
public:
    MockStorage(const std::string& name) : name_(name), active_(true) {}
    
    bool StoreTick(const TestTick& tick) {
        if (!active_) return false;
        std::lock_guard<std::mutex> lock(mutex_);
        ticks_.push_back(tick);
        return true;
    }
    
    std::vector<TestTick> GetAllTicksForSymbol(const std::string& symbol) {
        std::lock_guard<std::mutex> lock(mutex_);
        std::vector<TestTick> result;
        for (const auto& tick : ticks_) {
            if (tick.symbol == symbol) {
                result.push_back(tick);
            }
        }
        return result;
    }
    
    size_t GetMessageCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return ticks_.size();
    }
    
    void SimulateFailure() { active_ = false; }
    bool IsActive() const { return active_.load(); }
    
private:
    std::string name_;
    std::atomic<bool> active_;
    mutable std::mutex mutex_;
    std::vector<TestTick> ticks_;
};

struct RecoveryResult {
    bool success = false;
    uint64_t recovered_message_count = 0;
};

class MockRecoveryManager {
public:
    RecoveryResult RecoverMissingData(
        MockStorage* source,
        MockStorage* target,
        std::chrono::system_clock::time_point start,
        std::chrono::system_clock::time_point end) {
        
        // Simulate recovery process
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        RecoveryResult result;
        result.success = true;
        result.recovered_message_count = 5000; // Mock recovery count
        return result;
    }
};

class TickGenerator {
public:
    TickGenerator();
    
    TestTick GenerateTestTick();
    TestTick GenerateRandomTick();
    std::vector<TestTick> GenerateBatch(uint32_t count);
    
private:
    std::mt19937 rng_;
    std::atomic<uint64_t> sequence_counter_{1};
};

class MockRestApiServer {
public:
    MockRestApiServer() = default;
};

// Main test utilities class
class TestUtils {
public:
    TestUtils();
    ~TestUtils();
    
    // Factory methods for mock objects
    std::unique_ptr<MockDataBus> CreateMockDataBus();
    std::unique_ptr<MockWebSocketServer> CreateMockWebSocketServer();
    std::unique_ptr<MockWebSocketClient> CreateWebSocketTestClient();
    std::unique_ptr<MockGrpcServer> CreateMockGrpcServer();
    std::unique_ptr<MockGrpcClient> CreateGrpcTestClient();
    std::unique_ptr<MockRedisClient> CreateRedisTestClient(const std::string& instance = "default");
    std::unique_ptr<MockClickHouseClient> CreateClickHouseTestClient(const std::string& instance = "default");
    std::unique_ptr<TickGenerator> CreateTickGenerator();
    
    std::unique_ptr<MockHttpClient> CreateHttpTestClient() {
        return std::make_unique<MockHttpClient>();
    }
    
    std::unique_ptr<MockMarketDataServer> CreateMockMarketDataServer(const std::string& name = "default") {
        return std::make_unique<MockMarketDataServer>(name);
    }
    
    std::unique_ptr<MockTestClient> CreateTestClient() {
        return std::make_unique<MockTestClient>();
    }
    
    std::unique_ptr<MockLoadBalancer> CreateMockLoadBalancer() {
        return std::make_unique<MockLoadBalancer>();
    }
    
    std::unique_ptr<MockNetworkSimulator> CreateNetworkSimulator() {
        return std::make_unique<MockNetworkSimulator>();
    }
    
    std::unique_ptr<MockStorage> CreateMockStorage(const std::string& name = "default") {
        return std::make_unique<MockStorage>(name);
    }
    
    std::unique_ptr<MockRecoveryManager> CreateRecoveryManager() {
        return std::make_unique<MockRecoveryManager>();
    }
    
    std::unique_ptr<MockRestApiServer> CreateMockRestApiServer() {
        return std::make_unique<MockRestApiServer>();
    }
    
    // Utility methods
    TestTick GenerateTestTick();
    SystemResourceUsage GetSystemResourceUsage();
};

} // namespace performance_tests