{"tls": {"cert_file": "config/certs/server.crt", "key_file": "config/certs/server.key", "ca_file": "config/certs/ca.crt", "cipher_suites": "TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256", "require_client_cert": true, "min_tls_version": 13}, "encryption": {"key_file": "config/keys/encryption.key", "algorithm": "AES-256-GCM", "key_rotation_days": 90, "enable_disk_encryption": true}, "jwt": {"secret_key": "your-super-secret-jwt-key-change-this-in-production", "issuer": "financial-data-service", "token_expiry_seconds": 3600, "refresh_expiry_seconds": 604800, "require_mfa": true, "mfa_secret_key": "your-mfa-secret-key"}, "rbac": {"roles_config_file": "config/roles.json", "permissions_config_file": "config/permissions.json", "enable_dynamic_permissions": true, "cache_ttl_seconds": 300}, "audit": {"log_file": "logs/audit.log", "log_level": "INFO", "enable_real_time_alerts": true, "max_log_size_mb": 100, "max_log_files": 10, "sensitive_operations": ["LOGIN", "LOGOUT", "DATA_ACCESS", "CONFIG_CHANGE", "USER_MANAGEMENT", "ROLE_MANAGEMENT", "SECURITY_VIOLATION"]}, "enable_rate_limiting": true, "max_requests_per_minute": 1000, "enable_ip_whitelist": false, "allowed_ips": ["***********/24", "10.0.0.0/8"]}