@echo off
REM 金融数据服务系统Web管理界面停止脚本
REM Financial Data Service System Web Admin Stop Script

echo === 停止金融数据服务系统Web管理界面 ===
echo === Stopping Financial Data Service Web Admin ===

REM 检查Docker Compose是否可用
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker Compose未安装
    echo Error: Docker Compose is not installed
    pause
    exit /b 1
)

REM 停止所有服务
echo 停止所有服务...
docker-compose down

REM 可选：清理数据卷（谨慎使用）
if "%1"=="--clean" (
    echo 清理数据卷...
    docker-compose down -v
    docker system prune -f
    echo 数据卷已清理
)

echo 服务已停止
echo Services stopped successfully

pause