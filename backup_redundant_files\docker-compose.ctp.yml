version: '3.8'

# CTP数据采集系统 Docker Compose配置
# 包含数据采集、存储、监控等完整服务

services:
  # CTP数据采集器
  ctp-collector:
    build:
      context: .
      dockerfile: docker/ctp-collector.Dockerfile
    container_name: ctp-data-collector
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app/src
      - TZ=Asia/Shanghai
      - LOG_LEVEL=INFO
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CLICKHOUSE_HOST=clickhouse
      - CLICKHOUSE_PORT=9000
      - COLLECTOR_MODE=production
    volumes:
      - ./config:/app/config:ro
      - ctp_logs:/app/logs
      - ctp_data:/app/data
      - ctp_cache:/app/cache
    networks:
      - ctp-network
    depends_on:
      - redis
      - clickhouse
    healthcheck:
      test: ["CMD", "python3", "-c", "import sys; sys.path.insert(0, '/app/src'); from collectors.pytdx_collector import PyTDXCollector; print('OK')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Redis - 实时数据缓存
  redis:
    image: redis:7-alpine
    container_name: ctp-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - ctp_redis_data:/data
    networks:
      - ctp-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # ClickHouse - 历史数据存储
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: ctp-clickhouse
    restart: unless-stopped
    ports:
      - "8123:8123"
      - "9000:9000"
    environment:
      - CLICKHOUSE_DB=ctp_data
      - CLICKHOUSE_USER=ctp_user
      - CLICKHOUSE_PASSWORD=ctp_password123
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
    volumes:
      - ctp_clickhouse_data:/var/lib/clickhouse
      - ctp_clickhouse_logs:/var/log/clickhouse-server
      - ./config/clickhouse-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - ctp-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Kafka - 消息队列 (可选)
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: ctp-kafka
    restart: unless-stopped
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
    depends_on:
      - zookeeper
    networks:
      - ctp-network
    profiles:
      - messaging
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: ctp-zookeeper
    restart: unless-stopped
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - ctp-network
    profiles:
      - messaging
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Prometheus - 监控指标收集
  prometheus:
    image: prom/prometheus:latest
    container_name: ctp-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus-ctp.yml:/etc/prometheus/prometheus.yml:ro
      - ctp_prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - ctp-network
    profiles:
      - monitoring
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Grafana - 监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: ctp-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=ctp_admin123
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      - GF_SECURITY_ALLOW_EMBEDDING=true
    volumes:
      - ctp_grafana_data:/var/lib/grafana
      - ./config/grafana-dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana-datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - ctp-network
    depends_on:
      - prometheus
    profiles:
      - monitoring
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Nginx - 反向代理和负载均衡
  nginx:
    image: nginx:alpine
    container_name: ctp-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ctp_nginx_logs:/var/log/nginx
    networks:
      - ctp-network
    depends_on:
      - ctp-collector
    profiles:
      - proxy
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

# 数据卷配置
volumes:
  ctp_logs:
    driver: local
  ctp_data:
    driver: local
  ctp_cache:
    driver: local
  ctp_redis_data:
    driver: local
  ctp_clickhouse_data:
    driver: local
  ctp_clickhouse_logs:
    driver: local
  ctp_prometheus_data:
    driver: local
  ctp_grafana_data:
    driver: local
  ctp_nginx_logs:
    driver: local

# 网络配置
networks:
  ctp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16