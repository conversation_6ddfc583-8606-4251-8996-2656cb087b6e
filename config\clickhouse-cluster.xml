<?xml version="1.0"?>
<yandex>
    <logger>
        <level>information</level>
        <console>true</console>
        <log>/var/log/clickhouse-server/clickhouse-server.log</log>
        <errorlog>/var/log/clickhouse-server/clickhouse-server.err.log</errorlog>
        <size>1000M</size>
        <count>10</count>
    </logger>
    
    <!-- HTTP and TCP ports -->
    <http_port>8123</http_port>
    <tcp_port>9000</tcp_port>
    <interserver_http_port>9009</interserver_http_port>
    
    <!-- Paths -->
    <path>/var/lib/clickhouse/</path>
    <tmp_path>/var/lib/clickhouse/tmp/</tmp_path>
    <user_files_path>/var/lib/clickhouse/user_files/</user_files_path>
    <format_schema_path>/var/lib/clickhouse/format_schemas/</format_schema_path>
    
    <!-- Users configuration -->
    <users_config>users.xml</users_config>
    
    <!-- Default settings -->
    <default_profile>default</default_profile>
    <default_database>market_data</default_database>
    
    <!-- Timezone -->
    <timezone>Asia/Shanghai</timezone>
    
    <!-- Memory settings -->
    <max_server_memory_usage_to_ram_ratio>0.8</max_server_memory_usage_to_ram_ratio>
    <mlock_executable>false</mlock_executable>
    
    <!-- Cluster configuration for high availability -->
    <remote_servers>
        <financial_cluster>
            <shard>
                <replica>
                    <host>clickhouse-1</host>
                    <port>9000</port>
                    <user>admin</user>
                    <password>password123</password>
                </replica>
                <replica>
                    <host>clickhouse-2</host>
                    <port>9000</port>
                    <user>admin</user>
                    <password>password123</password>
                </replica>
            </shard>
            <shard>
                <replica>
                    <host>clickhouse-3</host>
                    <port>9000</port>
                    <user>admin</user>
                    <password>password123</password>
                </replica>
            </shard>
        </financial_cluster>
    </remote_servers>
    
    <!-- ZooKeeper configuration for replication -->
    <zookeeper>
        <node index="1">
            <host>zookeeper</host>
            <port>2181</port>
        </node>
    </zookeeper>
    
    <!-- Macros for replica identification -->
    <macros>
        <cluster>financial_cluster</cluster>
        <shard>01</shard>
        <replica>replica_01</replica>
    </macros>
    
    <!-- Performance settings -->
    <profiles>
        <default>
            <max_memory_usage>10000000000</max_memory_usage>
            <use_uncompressed_cache>0</use_uncompressed_cache>
            <load_balancing>random</load_balancing>
            <max_execution_time>300</max_execution_time>
            <max_concurrent_queries_for_user>10</max_concurrent_queries_for_user>
            <background_pool_size>16</background_pool_size>
            <background_merges_mutations_concurrency_ratio>2</background_merges_mutations_concurrency_ratio>
        </default>
        
        <readonly>
            <readonly>1</readonly>
        </readonly>
        
        <high_performance>
            <max_memory_usage>20000000000</max_memory_usage>
            <max_bytes_before_external_group_by>20000000000</max_bytes_before_external_group_by>
            <max_bytes_before_external_sort>20000000000</max_bytes_before_external_sort>
            <max_execution_time>600</max_execution_time>
        </high_performance>
    </profiles>
    
    <!-- Quotas -->
    <quotas>
        <default>
            <interval>
                <duration>3600</duration>
                <queries>0</queries>
                <errors>0</errors>
                <result_rows>0</result_rows>
                <read_rows>0</read_rows>
                <execution_time>0</execution_time>
            </interval>
        </default>
    </quotas>
    
    <!-- Compression settings -->
    <compression>
        <case>
            <method>zstd</method>
            <level>3</level>
        </case>
    </compression>
    
    <!-- Storage policies for tiered storage -->
    <storage_configuration>
        <disks>
            <default>
                <path>/var/lib/clickhouse/</path>
            </default>
            <cold_storage>
                <type>s3</type>
                <endpoint>http://minio:9002/clickhouse-cold/</endpoint>
                <access_key_id>admin</access_key_id>
                <secret_access_key>password123</secret_access_key>
                <region>us-east-1</region>
            </cold_storage>
        </disks>
        
        <policies>
            <tiered_storage>
                <volumes>
                    <hot>
                        <disk>default</disk>
                        <max_data_part_size_bytes>1073741824</max_data_part_size_bytes>
                    </hot>
                    <cold>
                        <disk>cold_storage</disk>
                    </cold>
                </volumes>
                <move_factor>0.1</move_factor>
            </tiered_storage>
        </policies>
    </storage_configuration>
</yandex>