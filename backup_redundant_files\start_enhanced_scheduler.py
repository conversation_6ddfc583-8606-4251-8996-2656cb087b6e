#!/usr/bin/env python3
"""
增强版调度器启动脚本
基于现有系统的升级版本，支持全面的数据类型和自动代码表更新
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

from src.services.scheduler_service import SchedulerService

async def main():
    """主函数"""
    print("=" * 60)
    print("           金融数据服务 - 增强版调度器")
    print("=" * 60)
    print()
    print("🚀 功能特性:")
    print("  ✅ 自动更新所有类型代码表 (股票、指数、期货、基金、债券)")
    print("  ✅ 全量数据更新 (根据代码表自动获取所有标的)")
    print("  ✅ 实时数据更新 (交易时间内自动更新)")
    print("  ✅ 可配置的更新频率和数据类型")
    print("  ✅ 智能调度和故障恢复")
    print()
    print("📋 调度任务:")
    print("  • 每日8:00  - 更新所有代码表")
    print("  • 每日18:00 - 股票数据全量更新")
    print("  • 每日18:05 - 指数数据全量更新") 
    print("  • 每日18:10 - 期货数据全量更新")
    print("  • 每日18:15 - 基金数据全量更新")
    print("  • 交易时间  - 实时数据更新")
    print()
    print("🔧 配置文件: config/scheduler_config.json")
    print("📝 日志文件: logs/scheduler_service.log")
    print()
    print("按 Ctrl+C 停止服务")
    print("=" * 60)
    print()
    
    # 创建服务实例
    service = SchedulerService()
    
    try:
        # 初始化服务
        print("正在初始化服务...")
        if not await service.initialize():
            print("❌ 服务初始化失败")
            sys.exit(1)
        
        print("✅ 服务初始化成功")
        
        # 启动服务
        print("正在启动调度器...")
        await service.start()
        
        print("✅ 调度器启动成功")
        print()
        print("服务正在运行中...")
        print("可以查看日志文件获取详细信息: logs/scheduler_service.log")
        print()
        
        # 运行服务
        await service.run()
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"❌ 服务运行异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await service.shutdown()
        print("✅ 服务已安全关闭")

if __name__ == "__main__":
    asyncio.run(main())