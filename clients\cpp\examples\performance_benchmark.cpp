#include "financial_data_sdk.h"
#include <iostream>
#include <chrono>
#include <vector>
#include <thread>
#include <atomic>
#include <algorithm>
#include <numeric>

using namespace financial_data::sdk;

class PerformanceBenchmark {
public:
    struct BenchmarkResult {
        std::string test_name;
        uint64_t operations_count;
        std::chrono::microseconds total_time;
        std::chrono::microseconds avg_latency;
        std::chrono::microseconds min_latency;
        std::chrono::microseconds max_latency;
        std::chrono::microseconds p95_latency;
        std::chrono::microseconds p99_latency;
        double throughput_ops_per_sec;
    };

    explicit PerformanceBenchmark(FinancialDataSDK& sdk) : sdk_(sdk) {}

    BenchmarkResult BenchmarkSyncRequests(int num_requests = 1000) {
        std::cout << "Running synchronous request benchmark..." << std::endl;
        
        std::vector<std::chrono::microseconds> latencies;
        latencies.reserve(num_requests);
        
        std::vector<std::string> symbols = {"CU2409"};
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < num_requests; ++i) {
            auto request_start = std::chrono::high_resolution_clock::now();
            
            try {
                auto ticks = sdk_.GetLatestTicks(symbols, "SHFE");
                
                auto request_end = std::chrono::high_resolution_clock::now();
                auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
                    request_end - request_start);
                latencies.push_back(latency);
                
                // Verify we got data
                if (ticks.empty()) {
                    std::cout << "Warning: Empty response for request " << i << std::endl;
                }
                
            } catch (const std::exception& e) {
                std::cout << "Error in request " << i << ": " << e.what() << std::endl;
            }
            
            // Small delay to avoid overwhelming the server
            if (i % 100 == 0) {
                std::cout << "Completed " << i << " requests" << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time);
        
        return CalculateStatistics("Sync Requests", latencies, total_time);
    }

    BenchmarkResult BenchmarkAsyncRequests(int num_requests = 1000) {
        std::cout << "Running asynchronous request benchmark..." << std::endl;
        
        std::vector<std::future<std::vector<StandardTick>>> futures;
        std::vector<std::chrono::high_resolution_clock::time_point> start_times;
        
        futures.reserve(num_requests);
        start_times.reserve(num_requests);
        
        std::vector<std::string> symbols = {"CU2409"};
        
        auto benchmark_start = std::chrono::high_resolution_clock::now();
        
        // Start all async requests
        for (int i = 0; i < num_requests; ++i) {
            start_times.push_back(std::chrono::high_resolution_clock::now());
            futures.push_back(sdk_.GetLatestTicksAsync(symbols, "SHFE"));
            
            // Batch the requests to avoid overwhelming
            if ((i + 1) % 50 == 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(5));
            }
        }
        
        std::cout << "All async requests started, waiting for completion..." << std::endl;
        
        // Wait for all requests to complete and measure latencies
        std::vector<std::chrono::microseconds> latencies;
        latencies.reserve(num_requests);
        
        for (int i = 0; i < num_requests; ++i) {
            try {
                auto result = futures[i].get();
                auto end_time = std::chrono::high_resolution_clock::now();
                
                auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
                    end_time - start_times[i]);
                latencies.push_back(latency);
                
                if (result.empty()) {
                    std::cout << "Warning: Empty response for async request " << i << std::endl;
                }
                
            } catch (const std::exception& e) {
                std::cout << "Error in async request " << i << ": " << e.what() << std::endl;
            }
            
            if ((i + 1) % 100 == 0) {
                std::cout << "Completed " << (i + 1) << " async requests" << std::endl;
            }
        }
        
        auto benchmark_end = std::chrono::high_resolution_clock::now();
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
            benchmark_end - benchmark_start);
        
        return CalculateStatistics("Async Requests", latencies, total_time);
    }

    BenchmarkResult BenchmarkStreamingLatency(int duration_seconds = 30) {
        std::cout << "Running streaming latency benchmark for " 
                  << duration_seconds << " seconds..." << std::endl;
        
        std::vector<std::chrono::microseconds> latencies;
        std::atomic<bool> running{true};
        std::mutex latencies_mutex;
        
        auto callback = [&latencies, &latencies_mutex](const StandardTick& tick) {
            auto now = std::chrono::high_resolution_clock::now();
            auto now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                now.time_since_epoch()).count();
            
            // Calculate end-to-end latency
            auto latency_ns = now_ns - tick.timestamp_ns;
            auto latency_us = std::chrono::microseconds(latency_ns / 1000);
            
            std::lock_guard<std::mutex> lock(latencies_mutex);
            latencies.push_back(latency_us);
        };
        
        // Start streaming
        SubscriptionConfig config;
        config.symbols = {"CU2409", "AL2409", "ZN2409"};
        config.exchange = "SHFE";
        config.buffer_size = 1000;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        if (!sdk_.SubscribeTickData(config, callback)) {
            throw std::runtime_error("Failed to start streaming");
        }
        
        // Run for specified duration
        std::this_thread::sleep_for(std::chrono::seconds(duration_seconds));
        
        // Stop streaming
        sdk_.UnsubscribeAll();
        running = false;
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time);
        
        std::cout << "Streaming benchmark completed. Received " 
                  << latencies.size() << " ticks" << std::endl;
        
        return CalculateStatistics("Streaming Latency", latencies, total_time);
    }

    void PrintResults(const std::vector<BenchmarkResult>& results) {
        std::cout << "\n" << std::string(80, '=') << std::endl;
        std::cout << "PERFORMANCE BENCHMARK RESULTS" << std::endl;
        std::cout << std::string(80, '=') << std::endl;
        
        for (const auto& result : results) {
            std::cout << "\nTest: " << result.test_name << std::endl;
            std::cout << std::string(40, '-') << std::endl;
            std::cout << "Operations: " << result.operations_count << std::endl;
            std::cout << "Total time: " << result.total_time.count() << " μs" << std::endl;
            std::cout << "Throughput: " << std::fixed << std::setprecision(2) 
                      << result.throughput_ops_per_sec << " ops/sec" << std::endl;
            std::cout << "Average latency: " << result.avg_latency.count() << " μs" << std::endl;
            std::cout << "Min latency: " << result.min_latency.count() << " μs" << std::endl;
            std::cout << "Max latency: " << result.max_latency.count() << " μs" << std::endl;
            std::cout << "P95 latency: " << result.p95_latency.count() << " μs" << std::endl;
            std::cout << "P99 latency: " << result.p99_latency.count() << " μs" << std::endl;
            
            // Check if latency meets requirements (< 5μs additional overhead)
            if (result.avg_latency.count() <= 5) {
                std::cout << "✓ PASS: Average latency meets requirement (≤5μs)" << std::endl;
            } else {
                std::cout << "✗ FAIL: Average latency exceeds requirement (>5μs)" << std::endl;
            }
        }
        
        std::cout << std::string(80, '=') << std::endl;
    }

private:
    BenchmarkResult CalculateStatistics(const std::string& test_name,
                                       std::vector<std::chrono::microseconds>& latencies,
                                       std::chrono::microseconds total_time) {
        if (latencies.empty()) {
            return BenchmarkResult{test_name, 0, total_time, 
                                 std::chrono::microseconds{0}, std::chrono::microseconds{0},
                                 std::chrono::microseconds{0}, std::chrono::microseconds{0},
                                 std::chrono::microseconds{0}, 0.0};
        }
        
        std::sort(latencies.begin(), latencies.end());
        
        auto min_latency = latencies.front();
        auto max_latency = latencies.back();
        
        auto sum = std::accumulate(latencies.begin(), latencies.end(), 
                                 std::chrono::microseconds{0});
        auto avg_latency = sum / latencies.size();
        
        auto p95_index = static_cast<size_t>(latencies.size() * 0.95);
        auto p99_index = static_cast<size_t>(latencies.size() * 0.99);
        
        auto p95_latency = latencies[std::min(p95_index, latencies.size() - 1)];
        auto p99_latency = latencies[std::min(p99_index, latencies.size() - 1)];
        
        double throughput = 0.0;
        if (total_time.count() > 0) {
            throughput = (static_cast<double>(latencies.size()) * 1000000.0) / 
                        static_cast<double>(total_time.count());
        }
        
        return BenchmarkResult{
            test_name,
            static_cast<uint64_t>(latencies.size()),
            total_time,
            avg_latency,
            min_latency,
            max_latency,
            p95_latency,
            p99_latency,
            throughput
        };
    }

    FinancialDataSDK& sdk_;
};

int main() {
    std::cout << "Financial Data SDK - Performance Benchmark" << std::endl;
    std::cout << "===========================================" << std::endl;

    // Configure connection for optimal performance
    ConnectionConfig config;
    config.server_address = "localhost:50051";
    config.connect_timeout = std::chrono::milliseconds(5000);
    config.request_timeout = std::chrono::milliseconds(30000);
    config.max_retry_attempts = 1; // Minimize retries for benchmark
    config.enable_compression = false; // Disable compression for latency test
    config.enable_keepalive = true;
    config.max_receive_message_size = 64 * 1024 * 1024;

    FinancialDataSDK sdk(config);
    
    // Set up error tracking
    std::atomic<int> error_count{0};
    sdk.SetErrorCallback([&error_count](const ErrorInfo& error) {
        error_count++;
        std::cerr << "Benchmark error: " << error.message << std::endl;
    });

    try {
        // Connect to server
        std::cout << "Connecting to server..." << std::endl;
        if (!sdk.Connect()) {
            std::cerr << "Failed to connect to server" << std::endl;
            return 1;
        }

        std::cout << "Connected successfully!" << std::endl;

        // Warm up the connection
        std::cout << "Warming up connection..." << std::endl;
        for (int i = 0; i < 10; ++i) {
            try {
                sdk.GetLatestTicks({"CU2409"}, "SHFE");
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            } catch (...) {
                // Ignore warm-up errors
            }
        }

        PerformanceBenchmark benchmark(sdk);
        std::vector<PerformanceBenchmark::BenchmarkResult> results;

        // Run benchmarks
        std::cout << "\nStarting performance benchmarks..." << std::endl;

        // 1. Synchronous requests benchmark
        results.push_back(benchmark.BenchmarkSyncRequests(500));

        // 2. Asynchronous requests benchmark  
        results.push_back(benchmark.BenchmarkAsyncRequests(500));

        // 3. Streaming latency benchmark
        results.push_back(benchmark.BenchmarkStreamingLatency(20));

        // Print all results
        benchmark.PrintResults(results);

        // Additional SDK statistics
        auto sdk_stats = sdk.GetStatistics();
        std::cout << "\nSDK Statistics:" << std::endl;
        std::cout << "Total messages received: " << sdk_stats.messages_received << std::endl;
        std::cout << "Total messages sent: " << sdk_stats.messages_sent << std::endl;
        std::cout << "Connection count: " << sdk_stats.connection_count << std::endl;
        std::cout << "Reconnection count: " << sdk_stats.reconnection_count << std::endl;
        std::cout << "Total errors during benchmark: " << error_count.load() << std::endl;

        // Disconnect
        sdk.Disconnect();

    } catch (const std::exception& e) {
        std::cerr << "Benchmark exception: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\nPerformance benchmark completed!" << std::endl;
    return 0;
}