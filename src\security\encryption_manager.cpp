#include "encryption_manager.h"
#include <openssl/sha.h>
#include <fstream>
#include <iostream>
#include <iomanip>
#include <sstream>

namespace financial_data {
namespace security {

EncryptionManager::EncryptionManager(const EncryptionConfig& config)
    : config_(config), initialized_(false) {
    encryption_key_.resize(32); // AES-256需要32字节密钥
}

EncryptionManager::~EncryptionManager() {
    // 清零密钥内存
    if (!encryption_key_.empty()) {
        OPENSSL_cleanse(encryption_key_.data(), encryption_key_.size());
    }
}

bool EncryptionManager::Initialize() {
    if (initialized_) {
        return true;
    }
    
    // 初始化OpenSSL
    OpenSSL_add_all_algorithms();
    
    // 加载或生成密钥
    if (!LoadKey()) {
        if (!GenerateKey()) {
            std::cerr << "Failed to generate encryption key" << std::endl;
            return false;
        }
        if (!<PERSON><PERSON><PERSON>()) {
            std::cerr << "Failed to save encryption key" << std::endl;
            return false;
        }
    }
    
    initialized_ = true;
    return true;
}

bool EncryptionManager::EncryptData(const std::vector<uint8_t>& plaintext,
                                   std::vector<uint8_t>& ciphertext,
                                   std::vector<uint8_t>& iv) {
    if (!initialized_) {
        return false;
    }
    
    // 生成随机IV
    if (!GenerateIV(iv)) {
        return false;
    }
    
    // 预分配密文空间
    ciphertext.resize(plaintext.size() + AES_BLOCK_SIZE);
    size_t ciphertext_len = ciphertext.size();
    
    // 执行加密
    bool result = EncryptInternal(plaintext.data(), plaintext.size(),
                                 encryption_key_.data(), iv.data(),
                                 ciphertext.data(), ciphertext_len);
    
    if (result) {
        ciphertext.resize(ciphertext_len);
    }
    
    return result;
}

bool EncryptionManager::DecryptData(const std::vector<uint8_t>& ciphertext,
                                   const std::vector<uint8_t>& iv,
                                   std::vector<uint8_t>& plaintext) {
    if (!initialized_) {
        return false;
    }
    
    // 预分配明文空间
    plaintext.resize(ciphertext.size());
    size_t plaintext_len = plaintext.size();
    
    // 执行解密
    bool result = DecryptInternal(ciphertext.data(), ciphertext.size(),
                                 encryption_key_.data(), iv.data(),
                                 plaintext.data(), plaintext_len);
    
    if (result) {
        plaintext.resize(plaintext_len);
    }
    
    return result;
}

bool EncryptionManager::EncryptFile(const std::string& input_file, const std::string& output_file) {
    std::ifstream infile(input_file, std::ios::binary);
    if (!infile) {
        std::cerr << "Cannot open input file: " << input_file << std::endl;
        return false;
    }
    
    std::ofstream outfile(output_file, std::ios::binary);
    if (!outfile) {
        std::cerr << "Cannot create output file: " << output_file << std::endl;
        return false;
    }
    
    // 生成IV并写入文件头
    std::vector<uint8_t> iv;
    if (!GenerateIV(iv)) {
        return false;
    }
    outfile.write(reinterpret_cast<const char*>(iv.data()), iv.size());
    
    // 分块加密文件
    const size_t buffer_size = 8192;
    std::vector<uint8_t> buffer(buffer_size);
    std::vector<uint8_t> encrypted_buffer(buffer_size + AES_BLOCK_SIZE);
    
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        return false;
    }
    
    if (EVP_EncryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr, encryption_key_.data(), iv.data()) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    
    int len;
    while (infile.read(reinterpret_cast<char*>(buffer.data()), buffer_size) || infile.gcount() > 0) {
        size_t bytes_read = infile.gcount();
        
        if (EVP_EncryptUpdate(ctx, encrypted_buffer.data(), &len, buffer.data(), bytes_read) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return false;
        }
        
        outfile.write(reinterpret_cast<const char*>(encrypted_buffer.data()), len);
    }
    
    // 完成加密
    if (EVP_EncryptFinal_ex(ctx, encrypted_buffer.data(), &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    outfile.write(reinterpret_cast<const char*>(encrypted_buffer.data()), len);
    
    // 获取认证标签
    std::vector<uint8_t> tag(16);
    if (EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_GET_TAG, 16, tag.data()) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    outfile.write(reinterpret_cast<const char*>(tag.data()), tag.size());
    
    EVP_CIPHER_CTX_free(ctx);
    return true;
}

bool EncryptionManager::DecryptFile(const std::string& input_file, const std::string& output_file) {
    std::ifstream infile(input_file, std::ios::binary);
    if (!infile) {
        std::cerr << "Cannot open input file: " << input_file << std::endl;
        return false;
    }
    
    std::ofstream outfile(output_file, std::ios::binary);
    if (!outfile) {
        std::cerr << "Cannot create output file: " << output_file << std::endl;
        return false;
    }
    
    // 读取IV
    std::vector<uint8_t> iv(16);
    infile.read(reinterpret_cast<char*>(iv.data()), iv.size());
    
    // 获取文件大小并读取认证标签
    infile.seekg(0, std::ios::end);
    size_t file_size = infile.tellg();
    infile.seekg(16, std::ios::beg); // 跳过IV
    
    std::vector<uint8_t> tag(16);
    infile.seekg(file_size - 16, std::ios::beg);
    infile.read(reinterpret_cast<char*>(tag.data()), tag.size());
    infile.seekg(16, std::ios::beg); // 回到数据开始位置
    
    size_t encrypted_data_size = file_size - 16 - 16; // 减去IV和标签
    
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        return false;
    }
    
    if (EVP_DecryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr, encryption_key_.data(), iv.data()) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    
    // 分块解密
    const size_t buffer_size = 8192;
    std::vector<uint8_t> buffer(buffer_size);
    std::vector<uint8_t> decrypted_buffer(buffer_size);
    
    size_t remaining = encrypted_data_size;
    int len;
    
    while (remaining > 0) {
        size_t to_read = std::min(buffer_size, remaining);
        infile.read(reinterpret_cast<char*>(buffer.data()), to_read);
        
        if (EVP_DecryptUpdate(ctx, decrypted_buffer.data(), &len, buffer.data(), to_read) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return false;
        }
        
        outfile.write(reinterpret_cast<const char*>(decrypted_buffer.data()), len);
        remaining -= to_read;
    }
    
    // 设置认证标签
    if (EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_TAG, 16, tag.data()) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    
    // 完成解密
    if (EVP_DecryptFinal_ex(ctx, decrypted_buffer.data(), &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    outfile.write(reinterpret_cast<const char*>(decrypted_buffer.data()), len);
    
    EVP_CIPHER_CTX_free(ctx);
    return true;
}

bool EncryptionManager::GenerateKey() {
    if (RAND_bytes(encryption_key_.data(), encryption_key_.size()) != 1) {
        std::cerr << "Failed to generate random key" << std::endl;
        return false;
    }
    return true;
}

bool EncryptionManager::RotateKey() {
    // 备份当前密钥
    std::vector<uint8_t> old_key = encryption_key_;
    
    // 生成新密钥
    if (!GenerateKey()) {
        encryption_key_ = old_key; // 恢复旧密钥
        return false;
    }
    
    // 保存新密钥
    if (!SaveKey()) {
        encryption_key_ = old_key; // 恢复旧密钥
        return false;
    }
    
    // 清零旧密钥
    OPENSSL_cleanse(old_key.data(), old_key.size());
    
    return true;
}

std::string EncryptionManager::GetKeyFingerprint() const {
    if (encryption_key_.empty()) {
        return "";
    }
    
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256(encryption_key_.data(), encryption_key_.size(), hash);
    
    std::ostringstream oss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }
    
    return oss.str();
}

bool EncryptionManager::LoadKey() {
    std::ifstream keyfile(config_.key_file, std::ios::binary);
    if (!keyfile) {
        return false;
    }
    
    keyfile.read(reinterpret_cast<char*>(encryption_key_.data()), encryption_key_.size());
    return keyfile.good();
}

bool EncryptionManager::SaveKey() {
    std::ofstream keyfile(config_.key_file, std::ios::binary);
    if (!keyfile) {
        return false;
    }
    
    keyfile.write(reinterpret_cast<const char*>(encryption_key_.data()), encryption_key_.size());
    return keyfile.good();
}

bool EncryptionManager::GenerateIV(std::vector<uint8_t>& iv) {
    iv.resize(16); // AES-GCM使用16字节IV
    return RAND_bytes(iv.data(), iv.size()) == 1;
}

bool EncryptionManager::EncryptInternal(const uint8_t* plaintext, size_t plaintext_len,
                                       const uint8_t* key, const uint8_t* iv,
                                       uint8_t* ciphertext, size_t& ciphertext_len) {
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        return false;
    }
    
    if (EVP_EncryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr, key, iv) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    
    int len;
    if (EVP_EncryptUpdate(ctx, ciphertext, &len, plaintext, plaintext_len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    ciphertext_len = len;
    
    if (EVP_EncryptFinal_ex(ctx, ciphertext + len, &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    ciphertext_len += len;
    
    EVP_CIPHER_CTX_free(ctx);
    return true;
}

bool EncryptionManager::DecryptInternal(const uint8_t* ciphertext, size_t ciphertext_len,
                                       const uint8_t* key, const uint8_t* iv,
                                       uint8_t* plaintext, size_t& plaintext_len) {
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        return false;
    }
    
    if (EVP_DecryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr, key, iv) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    
    int len;
    if (EVP_DecryptUpdate(ctx, plaintext, &len, ciphertext, ciphertext_len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    plaintext_len = len;
    
    if (EVP_DecryptFinal_ex(ctx, plaintext + len, &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return false;
    }
    plaintext_len += len;
    
    EVP_CIPHER_CTX_free(ctx);
    return true;
}

} // namespace security
} // namespace financial_data