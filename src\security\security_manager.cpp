#include "security_manager.h"
#include <iostream>
#include <algorithm>
#include <uuid/uuid.h>

namespace financial_data {
namespace security {

SecurityManager::SecurityManager(const SecurityConfig& config)
    : config_(config), initialized_(false) {
}

SecurityManager::~SecurityManager() = default;

bool SecurityManager::Initialize() {
    if (initialized_) {
        return true;
    }
    
    try {
        // 初始化TLS管理器
        tls_manager_ = std::make_unique<TLSManager>(config_.tls);
        if (!tls_manager_->Initialize()) {
            std::cerr << "Failed to initialize TLS manager" << std::endl;
            return false;
        }
        
        // 初始化加密管理器
        encryption_manager_ = std::make_unique<EncryptionManager>(config_.encryption);
        if (!encryption_manager_->Initialize()) {
            std::cerr << "Failed to initialize encryption manager" << std::endl;
            return false;
        }
        
        // 初始化JWT认证
        jwt_auth_ = std::make_unique<JWTAuth>(config_.jwt);
        if (!jwt_auth_->Initialize()) {
            std::cerr << "Failed to initialize JWT auth" << std::endl;
            return false;
        }
        
        // 初始化RBAC管理器
        rbac_manager_ = std::make_unique<RBACManager>(config_.rbac);
        if (!rbac_manager_->Initialize()) {
            std::cerr << "Failed to initialize RBAC manager" << std::endl;
            return false;
        }
        
        // 初始化审计日志
        audit_logger_ = std::make_unique<AuditLogger>(config_.audit);
        if (!audit_logger_->Initialize()) {
            std::cerr << "Failed to initialize audit logger" << std::endl;
            return false;
        }
        
        // 设置审计告警回调
        audit_logger_->SetAlertCallback([this](const AuditEvent& event) {
            HandleSecurityIncident("AUDIT_ALERT", event.description, event.user_id, event.source_ip);
        });
        
        initialized_ = true;
        
        // 记录系统启动事件
        audit_logger_->LogEvent({
            .event_id = GenerateSessionId(),
            .event_type = AuditEventType::CONFIG_CHANGE,
            .level = AuditLevel::INFO,
            .user_id = "system",
            .action = "SYSTEM_START",
            .description = "Security manager initialized",
            .timestamp = std::chrono::system_clock::now(),
            .success = true
        });
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception during security manager initialization: " << e.what() << std::endl;
        return false;
    }
}

SecurityManager::AuthResult SecurityManager::AuthenticateUser(const std::string& username, 
                                                             const std::string& password,
                                                             const std::string& source_ip, 
                                                             const std::string& user_agent) {
    AuthResult result;
    result.success = false;
    
    // 检查IP是否被阻止
    if (IsIPBlocked(source_ip)) {
        result.error_message = "IP address is blocked due to too many failed attempts";
        audit_logger_->LogSecurityViolation("", "BLOCKED_IP_LOGIN_ATTEMPT", 
                                           "Login attempt from blocked IP", source_ip);
        return result;
    }
    
    // 执行JWT认证
    TokenInfo token_info = jwt_auth_->Authenticate(username, password);
    
    if (token_info.token.empty()) {
        result.error_message = "Invalid username or password";
        UpdateFailedLoginAttempts(source_ip, false);
        audit_logger_->LogLogin(username, source_ip, false, result.error_message);
        return result;
    }
    
    // 检查是否需要MFA
    if (token_info.token == "mfa_required") {
        result.error_message = "MFA verification required";
        result.user_id = token_info.user_id;
        return result;
    }
    
    // 创建会话
    std::string session_id = GenerateSessionId();
    
    {
        std::lock_guard<std::mutex> lock(sessions_mutex_);
        SessionInfo session;
        session.user_id = token_info.user_id;
        session.session_id = session_id;
        session.created_at = std::chrono::system_clock::now();
        session.last_activity = session.created_at;
        session.source_ip = source_ip;
        session.user_agent = user_agent;
        
        active_sessions_[token_info.token] = session;
    }
    
    // 获取用户角色
    result.roles = rbac_manager_->GetUserRoles(token_info.user_id);
    
    result.success = true;
    result.user_id = token_info.user_id;
    result.session_id = session_id;
    
    UpdateFailedLoginAttempts(source_ip, true);
    audit_logger_->LogLogin(token_info.user_id, source_ip, true);
    
    return result;
}

bool SecurityManager::AuthorizeAction(const std::string& user_id, const std::string& resource,
                                     const std::string& action, 
                                     const std::unordered_map<std::string, std::string>& context) {
    if (!initialized_) {
        return false;
    }
    
    // 映射到RBAC权限
    Permission permission = MapActionToPermission(action);
    ResourceType resource_type = MapResourceToType(resource);
    Action rbac_action = MapStringToAction(action);
    
    // 检查权限
    bool authorized = rbac_manager_->CheckPermission(user_id, permission, resource_type, rbac_action, context);
    
    // 记录权限检查
    audit_logger_->LogPermissionCheck(user_id, action, resource, authorized);
    
    if (!authorized) {
        audit_logger_->LogSecurityViolation(user_id, "UNAUTHORIZED_ACCESS", 
                                           "Attempted to access " + resource + " with action " + action);
    }
    
    return authorized;
}

bool SecurityManager::ValidateSession(const std::string& token, std::string& user_id) {
    if (!initialized_) {
        return false;
    }
    
    // 验证JWT令牌
    UserInfo user_info;
    if (!jwt_auth_->VerifyToken(token, user_info)) {
        return false;
    }
    
    // 检查会话是否存在
    {
        std::lock_guard<std::mutex> lock(sessions_mutex_);
        auto it = active_sessions_.find(token);
        if (it != active_sessions_.end()) {
            // 更新最后活动时间
            it->second.last_activity = std::chrono::system_clock::now();
            user_id = it->second.user_id;
            return true;
        }
    }
    
    return false;
}

void SecurityManager::LogoutUser(const std::string& user_id, const std::string& session_id) {
    if (!initialized_) {
        return;
    }
    
    // 查找并移除会话
    {
        std::lock_guard<std::mutex> lock(sessions_mutex_);
        for (auto it = active_sessions_.begin(); it != active_sessions_.end(); ++it) {
            if (it->second.user_id == user_id && it->second.session_id == session_id) {
                // 撤销JWT令牌
                jwt_auth_->RevokeToken(it->first);
                active_sessions_.erase(it);
                break;
            }
        }
    }
    
    audit_logger_->LogLogout(user_id, session_id);
}

bool SecurityManager::EncryptSensitiveData(const std::string& data, std::string& encrypted_data) {
    if (!initialized_ || !encryption_manager_) {
        return false;
    }
    
    std::vector<uint8_t> plaintext(data.begin(), data.end());
    std::vector<uint8_t> ciphertext;
    std::vector<uint8_t> iv;
    
    if (!encryption_manager_->EncryptData(plaintext, ciphertext, iv)) {
        return false;
    }
    
    // 组合IV和密文
    std::vector<uint8_t> combined;
    combined.insert(combined.end(), iv.begin(), iv.end());
    combined.insert(combined.end(), ciphertext.begin(), ciphertext.end());
    
    encrypted_data = std::string(combined.begin(), combined.end());
    return true;
}

bool SecurityManager::DecryptSensitiveData(const std::string& encrypted_data, std::string& data) {
    if (!initialized_ || !encryption_manager_) {
        return false;
    }
    
    if (encrypted_data.size() < 16) { // IV size
        return false;
    }
    
    // 分离IV和密文
    std::vector<uint8_t> iv(encrypted_data.begin(), encrypted_data.begin() + 16);
    std::vector<uint8_t> ciphertext(encrypted_data.begin() + 16, encrypted_data.end());
    std::vector<uint8_t> plaintext;
    
    if (!encryption_manager_->DecryptData(ciphertext, iv, plaintext)) {
        return false;
    }
    
    data = std::string(plaintext.begin(), plaintext.end());
    return true;
}

SecurityManager::SecurityStatus SecurityManager::GetSecurityStatus() {
    SecurityStatus status;
    
    status.tls_enabled = (tls_manager_ != nullptr);
    status.encryption_enabled = (encryption_manager_ != nullptr);
    status.jwt_enabled = (jwt_auth_ != nullptr);
    status.rbac_enabled = (rbac_manager_ != nullptr);
    status.audit_enabled = (audit_logger_ != nullptr);
    
    {
        std::lock_guard<std::mutex> lock(sessions_mutex_);
        status.active_sessions = active_sessions_.size();
    }
    
    // 统计最近一小时的失败登录次数
    {
        std::lock_guard<std::mutex> lock(security_stats_mutex_);
        status.failed_login_attempts_last_hour = 0;
        for (const auto& pair : failed_login_attempts_) {
            status.failed_login_attempts_last_hour += pair.second;
        }
    }
    
    // 获取最后安全事件时间
    if (audit_logger_) {
        AuditQuery query;
        query.event_type = AuditEventType::SECURITY_VIOLATION;
        query.limit = 1;
        
        auto events = audit_logger_->QueryEvents(query);
        if (!events.empty()) {
            status.last_security_incident = events[0].timestamp;
        }
    }
    
    return status;
}

void SecurityManager::HandleSecurityIncident(const std::string& incident_type, 
                                           const std::string& description,
                                           const std::string& user_id, 
                                           const std::string& source_ip) {
    if (!initialized_) {
        return;
    }
    
    // 记录安全事件
    audit_logger_->LogSecurityViolation(user_id, incident_type, description, source_ip);
    
    // 根据事件类型采取相应措施
    if (incident_type == "BLOCKED_IP_LOGIN_ATTEMPT" || 
        incident_type == "BRUTE_FORCE_ATTACK") {
        // 可以在这里实现IP阻止逻辑
    }
    
    if (incident_type == "UNAUTHORIZED_ACCESS") {
        // 可以在这里实现用户账户锁定逻辑
    }
    
    std::cout << "Security incident handled: " << incident_type << " - " << description << std::endl;
}

void SecurityManager::PerformSecurityMaintenance() {
    if (!initialized_) {
        return;
    }
    
    // 清理过期会话
    CleanupExpiredSessions();
    
    // 清理失败登录统计
    {
        std::lock_guard<std::mutex> lock(security_stats_mutex_);
        failed_login_attempts_.clear();
    }
    
    // 轮转加密密钥（如果需要）
    if (encryption_manager_) {
        // 检查密钥年龄，如果超过轮转周期则轮转
        // encryption_manager_->RotateKey();
    }
    
    // 轮转审计日志
    if (audit_logger_) {
        audit_logger_->RotateLog();
    }
    
    audit_logger_->LogEvent({
        .event_id = GenerateSessionId(),
        .event_type = AuditEventType::CONFIG_CHANGE,
        .level = AuditLevel::INFO,
        .user_id = "system",
        .action = "SECURITY_MAINTENANCE",
        .description = "Security maintenance performed",
        .timestamp = std::chrono::system_clock::now(),
        .success = true
    });
}

std::string SecurityManager::GenerateSessionId() {
    uuid_t uuid;
    uuid_generate(uuid);
    
    char uuid_str[37];
    uuid_unparse(uuid, uuid_str);
    
    return std::string(uuid_str);
}

void SecurityManager::CleanupExpiredSessions() {
    std::lock_guard<std::mutex> lock(sessions_mutex_);
    
    auto now = std::chrono::system_clock::now();
    auto session_timeout = std::chrono::hours(24); // 24小时超时
    
    for (auto it = active_sessions_.begin(); it != active_sessions_.end();) {
        if (now - it->second.last_activity > session_timeout) {
            jwt_auth_->RevokeToken(it->first);
            it = active_sessions_.erase(it);
        } else {
            ++it;
        }
    }
}

void SecurityManager::UpdateFailedLoginAttempts(const std::string& source_ip, bool success) {
    std::lock_guard<std::mutex> lock(security_stats_mutex_);
    
    if (success) {
        failed_login_attempts_.erase(source_ip);
    } else {
        failed_login_attempts_[source_ip]++;
    }
}

bool SecurityManager::IsIPBlocked(const std::string& source_ip) {
    std::lock_guard<std::mutex> lock(security_stats_mutex_);
    
    auto it = failed_login_attempts_.find(source_ip);
    if (it != failed_login_attempts_.end()) {
        return it->second >= 5; // 5次失败后阻止
    }
    
    return false;
}

Permission SecurityManager::MapActionToPermission(const std::string& action) {
    if (action == "read" || action == "READ") return Permission::READ_MARKET_DATA;
    if (action == "write" || action == "WRITE") return Permission::WRITE_MARKET_DATA;
    if (action == "export" || action == "EXPORT") return Permission::EXPORT_DATA;
    if (action == "manage_users") return Permission::MANAGE_USERS;
    if (action == "manage_roles") return Permission::MANAGE_ROLES;
    if (action == "system_config") return Permission::SYSTEM_CONFIG;
    if (action == "view_audit") return Permission::VIEW_AUDIT_LOGS;
    if (action == "api_access") return Permission::API_ACCESS;
    if (action == "websocket_access") return Permission::WEBSOCKET_ACCESS;
    if (action == "grpc_access") return Permission::GRPC_ACCESS;
    
    return Permission::READ_MARKET_DATA; // 默认权限
}

ResourceType SecurityManager::MapResourceToType(const std::string& resource) {
    if (resource.find("market_data") != std::string::npos) return ResourceType::MARKET_DATA;
    if (resource.find("historical_data") != std::string::npos) return ResourceType::HISTORICAL_DATA;
    if (resource.find("user") != std::string::npos) return ResourceType::USER_MANAGEMENT;
    if (resource.find("config") != std::string::npos) return ResourceType::SYSTEM_CONFIG;
    if (resource.find("audit") != std::string::npos) return ResourceType::AUDIT_LOGS;
    if (resource.find("api") != std::string::npos) return ResourceType::API_ENDPOINTS;
    if (resource.find("subscription") != std::string::npos) return ResourceType::SUBSCRIPTIONS;
    
    return ResourceType::MARKET_DATA; // 默认资源类型
}

Action SecurityManager::MapStringToAction(const std::string& action) {
    if (action == "read" || action == "READ") return Action::READ;
    if (action == "write" || action == "WRITE") return Action::WRITE;
    if (action == "delete" || action == "DELETE") return Action::DELETE;
    if (action == "execute" || action == "EXECUTE") return Action::EXECUTE;
    if (action == "manage" || action == "MANAGE") return Action::MANAGE;
    
    return Action::READ; // 默认操作
}

} // namespace security
} // namespace financial_data