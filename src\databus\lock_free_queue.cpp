#include "lock_free_queue.h"
#include <thread>
#include <chrono>

namespace financial_data {
namespace databus {

// 实现文件主要用于模板实例化和性能测试函数

/**
 * @brief 队列性能测试工具
 */
class QueuePerformanceTester {
public:
    /**
     * @brief 测试单生产者单消费者性能
     */
    template<typename QueueType>
    static void TestSPSCPerformance(QueueType& queue, size_t test_count) {
        using DataType = typename std::remove_reference_t<decltype(*queue.begin())>;
        
        std::atomic<bool> start_flag{false};
        std::atomic<size_t> produced{0};
        std::atomic<size_t> consumed{0};
        
        // 生产者线程
        std::thread producer([&]() {
            while (!start_flag.load()) {
                std::this_thread::yield();
            }
            
            for (size_t i = 0; i < test_count; ++i) {
                DataType data{};
                while (!queue.TryPush(data)) {
                    std::this_thread::yield();
                }
                produced.fetch_add(1);
            }
        });
        
        // 消费者线程
        std::thread consumer([&]() {
            while (!start_flag.load()) {
                std::this_thread::yield();
            }
            
            DataType data;
            size_t count = 0;
            while (count < test_count) {
                if (queue.TryPop(data)) {
                    ++count;
                    consumed.fetch_add(1);
                } else {
                    std::this_thread::yield();
                }
            }
        });
        
        // 开始测试
        auto start_time = std::chrono::high_resolution_clock::now();
        start_flag.store(true);
        
        producer.join();
        consumer.join();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time).count();
        
        double throughput = static_cast<double>(test_count) / duration * 1000000.0;
        
        printf("SPSC Performance Test Results:\n");
        printf("  Test Count: %zu\n", test_count);
        printf("  Duration: %ld microseconds\n", duration);
        printf("  Throughput: %.2f ops/sec\n", throughput);
        printf("  Produced: %zu, Consumed: %zu\n", 
               produced.load(), consumed.load());
    }
    
    /**
     * @brief 测试多生产者单消费者性能
     */
    template<typename QueueType>
    static void TestMPSCPerformance(QueueType& queue, size_t producer_count, 
                                   size_t test_count_per_producer) {
        using DataType = typename std::remove_reference_t<decltype(*queue.begin())>;
        
        std::atomic<bool> start_flag{false};
        std::atomic<size_t> total_produced{0};
        std::atomic<size_t> total_consumed{0};
        
        std::vector<std::thread> producers;
        producers.reserve(producer_count);
        
        // 创建生产者线程
        for (size_t p = 0; p < producer_count; ++p) {
            producers.emplace_back([&, p]() {
                while (!start_flag.load()) {
                    std::this_thread::yield();
                }
                
                for (size_t i = 0; i < test_count_per_producer; ++i) {
                    DataType data{};
                    while (!queue.TryPush(data)) {
                        std::this_thread::yield();
                    }
                    total_produced.fetch_add(1);
                }
            });
        }
        
        // 消费者线程
        const size_t total_expected = producer_count * test_count_per_producer;
        std::thread consumer([&]() {
            while (!start_flag.load()) {
                std::this_thread::yield();
            }
            
            DataType data;
            size_t count = 0;
            while (count < total_expected) {
                if (queue.TryPop(data)) {
                    ++count;
                    total_consumed.fetch_add(1);
                } else {
                    std::this_thread::yield();
                }
            }
        });
        
        // 开始测试
        auto start_time = std::chrono::high_resolution_clock::now();
        start_flag.store(true);
        
        for (auto& producer : producers) {
            producer.join();
        }
        consumer.join();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time).count();
        
        double throughput = static_cast<double>(total_expected) / duration * 1000000.0;
        
        printf("MPSC Performance Test Results:\n");
        printf("  Producers: %zu\n", producer_count);
        printf("  Test Count per Producer: %zu\n", test_count_per_producer);
        printf("  Total Expected: %zu\n", total_expected);
        printf("  Duration: %ld microseconds\n", duration);
        printf("  Throughput: %.2f ops/sec\n", throughput);
        printf("  Produced: %zu, Consumed: %zu\n", 
               total_produced.load(), total_consumed.load());
    }
};

// 显式实例化常用的队列类型
template class LockFreeQueue<StandardTick, 65536>;
template class LockFreeQueue<Level2Data, 32768>;
template class LockFreeQueue<MarketDataBatch, 4096>;
template class LockFreeQueue<MarketDataWrapper, 131072>;

template class MPSCQueue<StandardTick, 65536>;
template class MPSCQueue<MarketDataWrapper, 131072>;

} // namespace databus
} // namespace financial_data