#include "audit_logger.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <filesystem>
#include <json/json.h>
#include <uuid/uuid.h>

namespace financial_data {
namespace security {

AuditLogger::AuditLogger(const AuditConfig& config)
    : config_(config), initialized_(false) {
}

AuditLogger::~AuditLogger() {
    CloseLogFile();
}

bool AuditLogger::Initialize() {
    if (initialized_) {
        return true;
    }
    
    // 打开日志文件
    if (!OpenLogFile()) {
        std::cerr << "Failed to open audit log file" << std::endl;
        return false;
    }
    
    initialized_ = true;
    return true;
}

void AuditLogger::LogEvent(const AuditEvent& event) {
    if (!initialized_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(events_mutex_);
    
    // 添加到内存存储
    events_.push_back(event);
    
    // 写入文件
    WriteToFile(event);
    
    // 检查是否需要告警
    if (ShouldAlert(event)) {
        TriggerAlert(event);
    }
    
    // 管理内存使用
    ManageMemoryUsage();
}

void AuditLogger::LogLogin(const std::string& user_id, const std::string& source_ip,
                          bool success, const std::string& error_message) {
    AuditEvent event;
    event.event_id = GenerateEventId();
    event.event_type = AuditEventType::LOGIN;
    event.level = success ? AuditLevel::INFO : AuditLevel::WARNING;
    event.user_id = user_id;
    event.source_ip = source_ip;
    event.action = "LOGIN";
    event.description = success ? "User login successful" : "User login failed";
    event.timestamp = std::chrono::system_clock::now();
    event.success = success;
    event.error_message = error_message;
    
    if (!success) {
        event.details["failure_reason"] = error_message;
    }
    
    LogEvent(event);
}

void AuditLogger::LogLogout(const std::string& user_id, const std::string& session_id) {
    AuditEvent event;
    event.event_id = GenerateEventId();
    event.event_type = AuditEventType::LOGOUT;
    event.level = AuditLevel::INFO;
    event.user_id = user_id;
    event.session_id = session_id;
    event.action = "LOGOUT";
    event.description = "User logout";
    event.timestamp = std::chrono::system_clock::now();
    event.success = true;
    
    LogEvent(event);
}

void AuditLogger::LogDataAccess(const std::string& user_id, const std::string& resource,
                               const std::string& action, bool success) {
    AuditEvent event;
    event.event_id = GenerateEventId();
    event.event_type = AuditEventType::DATA_ACCESS;
    event.level = success ? AuditLevel::INFO : AuditLevel::WARNING;
    event.user_id = user_id;
    event.resource = resource;
    event.action = action;
    event.description = "Data access: " + action + " on " + resource;
    event.timestamp = std::chrono::system_clock::now();
    event.success = success;
    
    LogEvent(event);
}

void AuditLogger::LogConfigChange(const std::string& user_id, const std::string& config_item,
                                 const std::string& old_value, const std::string& new_value) {
    AuditEvent event;
    event.event_id = GenerateEventId();
    event.event_type = AuditEventType::CONFIG_CHANGE;
    event.level = AuditLevel::WARNING;
    event.user_id = user_id;
    event.resource = config_item;
    event.action = "CONFIG_CHANGE";
    event.description = "Configuration changed: " + config_item;
    event.timestamp = std::chrono::system_clock::now();
    event.success = true;
    
    event.details["old_value"] = old_value;
    event.details["new_value"] = new_value;
    
    LogEvent(event);
}

void AuditLogger::LogUserManagement(const std::string& admin_user_id, const std::string& target_user_id,
                                   const std::string& action, bool success) {
    AuditEvent event;
    event.event_id = GenerateEventId();
    event.event_type = AuditEventType::USER_MANAGEMENT;
    event.level = AuditLevel::WARNING;
    event.user_id = admin_user_id;
    event.resource = "user:" + target_user_id;
    event.action = action;
    event.description = "User management: " + action + " for user " + target_user_id;
    event.timestamp = std::chrono::system_clock::now();
    event.success = success;
    
    event.details["target_user"] = target_user_id;
    
    LogEvent(event);
}

void AuditLogger::LogPermissionCheck(const std::string& user_id, const std::string& permission,
                                    const std::string& resource, bool granted) {
    AuditEvent event;
    event.event_id = GenerateEventId();
    event.event_type = AuditEventType::PERMISSION_CHECK;
    event.level = granted ? AuditLevel::INFO : AuditLevel::WARNING;
    event.user_id = user_id;
    event.resource = resource;
    event.action = "PERMISSION_CHECK";
    event.description = "Permission check: " + permission + " on " + resource;
    event.timestamp = std::chrono::system_clock::now();
    event.success = granted;
    
    event.details["permission"] = permission;
    event.details["granted"] = granted ? "true" : "false";
    
    LogEvent(event);
}

void AuditLogger::LogAPICall(const std::string& user_id, const std::string& endpoint,
                            const std::string& method, int status_code) {
    AuditEvent event;
    event.event_id = GenerateEventId();
    event.event_type = AuditEventType::API_CALL;
    event.level = (status_code >= 200 && status_code < 300) ? AuditLevel::INFO : AuditLevel::WARNING;
    event.user_id = user_id;
    event.resource = endpoint;
    event.action = method;
    event.description = method + " " + endpoint;
    event.timestamp = std::chrono::system_clock::now();
    event.success = (status_code >= 200 && status_code < 300);
    
    event.details["status_code"] = std::to_string(status_code);
    
    LogEvent(event);
}

void AuditLogger::LogSecurityViolation(const std::string& user_id, const std::string& violation_type,
                                      const std::string& description, const std::string& source_ip) {
    AuditEvent event;
    event.event_id = GenerateEventId();
    event.event_type = AuditEventType::SECURITY_VIOLATION;
    event.level = AuditLevel::CRITICAL;
    event.user_id = user_id;
    event.source_ip = source_ip;
    event.action = violation_type;
    event.description = "Security violation: " + description;
    event.timestamp = std::chrono::system_clock::now();
    event.success = false;
    
    event.details["violation_type"] = violation_type;
    
    LogEvent(event);
}

std::vector<AuditEvent> AuditLogger::QueryEvents(const AuditQuery& query) {
    std::lock_guard<std::mutex> lock(events_mutex_);
    
    std::vector<AuditEvent> result;
    
    for (const auto& event : events_) {
        // 应用过滤条件
        if (!query.user_id.empty() && event.user_id != query.user_id) {
            continue;
        }
        
        if (query.event_type != static_cast<AuditEventType>(-1) && event.event_type != query.event_type) {
            continue;
        }
        
        if (event.level < query.min_level) {
            continue;
        }
        
        if (query.start_time != std::chrono::system_clock::time_point{} && 
            event.timestamp < query.start_time) {
            continue;
        }
        
        if (query.end_time != std::chrono::system_clock::time_point{} && 
            event.timestamp > query.end_time) {
            continue;
        }
        
        if (!query.source_ip.empty() && event.source_ip != query.source_ip) {
            continue;
        }
        
        if (!query.resource.empty() && event.resource.find(query.resource) == std::string::npos) {
            continue;
        }
        
        if (!query.action.empty() && event.action != query.action) {
            continue;
        }
        
        result.push_back(event);
    }
    
    // 按时间戳排序（最新的在前）
    std::sort(result.begin(), result.end(), 
              [](const AuditEvent& a, const AuditEvent& b) {
                  return a.timestamp > b.timestamp;
              });
    
    // 应用分页
    if (query.offset > 0 && query.offset < static_cast<int>(result.size())) {
        result.erase(result.begin(), result.begin() + query.offset);
    }
    
    if (query.limit > 0 && query.limit < static_cast<int>(result.size())) {
        result.resize(query.limit);
    }
    
    return result;
}

AuditLogger::AuditStatistics AuditLogger::GetStatistics(std::chrono::system_clock::time_point start_time,
                                                       std::chrono::system_clock::time_point end_time) {
    std::lock_guard<std::mutex> lock(events_mutex_);
    
    AuditStatistics stats = {};
    
    for (const auto& event : events_) {
        if (event.timestamp < start_time || event.timestamp > end_time) {
            continue;
        }
        
        stats.total_events++;
        
        if (event.event_type == AuditEventType::LOGIN) {
            stats.login_attempts++;
            if (!event.success) {
                stats.failed_logins++;
            }
        }
        
        if (event.event_type == AuditEventType::DATA_ACCESS) {
            stats.data_access_events++;
        }
        
        if (event.event_type == AuditEventType::SECURITY_VIOLATION) {
            stats.security_violations++;
        }
        
        stats.events_by_user[event.user_id]++;
        stats.events_by_ip[event.source_ip]++;
        stats.events_by_type[event.event_type]++;
    }
    
    return stats;
}

void AuditLogger::SetAlertCallback(std::function<void(const AuditEvent&)> callback) {
    alert_callback_ = callback;
}

void AuditLogger::RotateLog() {
    CloseLogFile();
    
    // 重命名当前日志文件
    std::string current_log = GetLogFileName();
    std::string rotated_log = GetRotatedLogFileName(1);
    
    try {
        std::filesystem::rename(current_log, rotated_log);
    } catch (const std::exception& e) {
        std::cerr << "Failed to rotate log file: " << e.what() << std::endl;
    }
    
    // 删除过期的轮转文件
    for (int i = config_.max_log_files; i > 1; --i) {
        std::string old_log = GetRotatedLogFileName(i);
        if (std::filesystem::exists(old_log)) {
            try {
                std::filesystem::remove(old_log);
            } catch (const std::exception& e) {
                std::cerr << "Failed to remove old log file: " << e.what() << std::endl;
            }
        }
    }
    
    // 重新打开日志文件
    OpenLogFile();
}

std::string AuditLogger::GenerateEventId() {
    uuid_t uuid;
    uuid_generate(uuid);
    
    char uuid_str[37];
    uuid_unparse(uuid, uuid_str);
    
    return std::string(uuid_str);
}

void AuditLogger::WriteToFile(const AuditEvent& event) {
    if (!log_file_.is_open()) {
        return;
    }
    
    std::string formatted_event = FormatEvent(event);
    log_file_ << formatted_event << std::endl;
    log_file_.flush();
    
    // 检查文件大小，如果超过限制则轮转
    log_file_.seekp(0, std::ios::end);
    size_t file_size = log_file_.tellp();
    
    if (file_size > static_cast<size_t>(config_.max_log_size_mb * 1024 * 1024)) {
        RotateLog();
    }
}

bool AuditLogger::ShouldAlert(const AuditEvent& event) {
    if (!config_.enable_real_time_alerts) {
        return false;
    }
    
    // 检查是否是敏感操作
    std::string event_type_str = EventTypeToString(event.event_type);
    auto it = std::find(config_.sensitive_operations.begin(), 
                       config_.sensitive_operations.end(), 
                       event_type_str);
    
    if (it != config_.sensitive_operations.end()) {
        return true;
    }
    
    // 检查严重级别
    if (event.level >= AuditLevel::ERROR) {
        return true;
    }
    
    // 检查失败事件
    if (!event.success && event.event_type == AuditEventType::LOGIN) {
        return true;
    }
    
    return false;
}

void AuditLogger::TriggerAlert(const AuditEvent& event) {
    if (alert_callback_) {
        alert_callback_(event);
    }
}

std::string AuditLogger::FormatEvent(const AuditEvent& event) {
    Json::Value json_event;
    
    json_event["event_id"] = event.event_id;
    json_event["event_type"] = EventTypeToString(event.event_type);
    json_event["level"] = LevelToString(event.level);
    json_event["user_id"] = event.user_id;
    json_event["session_id"] = event.session_id;
    json_event["source_ip"] = event.source_ip;
    json_event["user_agent"] = event.user_agent;
    json_event["resource"] = event.resource;
    json_event["action"] = event.action;
    json_event["description"] = event.description;
    json_event["success"] = event.success;
    json_event["error_message"] = event.error_message;
    
    // 时间戳格式化
    auto time_t = std::chrono::system_clock::to_time_t(event.timestamp);
    std::ostringstream timestamp_ss;
    timestamp_ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
    json_event["timestamp"] = timestamp_ss.str();
    
    // 详细信息
    Json::Value details_json;
    for (const auto& detail : event.details) {
        details_json[detail.first] = detail.second;
    }
    json_event["details"] = details_json;
    
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "";
    return Json::writeString(builder, json_event);
}

std::string AuditLogger::EventTypeToString(AuditEventType type) {
    switch (type) {
        case AuditEventType::LOGIN: return "LOGIN";
        case AuditEventType::LOGOUT: return "LOGOUT";
        case AuditEventType::DATA_ACCESS: return "DATA_ACCESS";
        case AuditEventType::CONFIG_CHANGE: return "CONFIG_CHANGE";
        case AuditEventType::USER_MANAGEMENT: return "USER_MANAGEMENT";
        case AuditEventType::ROLE_MANAGEMENT: return "ROLE_MANAGEMENT";
        case AuditEventType::PERMISSION_CHECK: return "PERMISSION_CHECK";
        case AuditEventType::API_CALL: return "API_CALL";
        case AuditEventType::WEBSOCKET_CONNECTION: return "WEBSOCKET_CONNECTION";
        case AuditEventType::GRPC_CALL: return "GRPC_CALL";
        case AuditEventType::ERROR_EVENT: return "ERROR_EVENT";
        case AuditEventType::SECURITY_VIOLATION: return "SECURITY_VIOLATION";
        default: return "UNKNOWN";
    }
}

std::string AuditLogger::LevelToString(AuditLevel level) {
    switch (level) {
        case AuditLevel::INFO: return "INFO";
        case AuditLevel::WARNING: return "WARNING";
        case AuditLevel::ERROR: return "ERROR";
        case AuditLevel::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

void AuditLogger::ManageMemoryUsage() {
    // 如果内存中的事件过多，删除最旧的事件
    const size_t max_memory_events = 10000;
    if (events_.size() > max_memory_events) {
        events_.erase(events_.begin(), events_.begin() + 1000);
    }
}

bool AuditLogger::OpenLogFile() {
    std::string log_file_path = GetLogFileName();
    log_file_.open(log_file_path, std::ios::app);
    return log_file_.is_open();
}

void AuditLogger::CloseLogFile() {
    if (log_file_.is_open()) {
        log_file_.close();
    }
}

std::string AuditLogger::GetLogFileName() {
    return config_.log_file;
}

std::string AuditLogger::GetRotatedLogFileName(int index) {
    return config_.log_file + "." + std::to_string(index);
}

} // namespace security
} // namespace financial_data