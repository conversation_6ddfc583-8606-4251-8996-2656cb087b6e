# 数据总线模块 CMakeLists.txt

# 添加数据总线库
add_library(databus
    lock_free_queue.cpp
    kafka_integration.cpp
    data_router.cpp
    backpressure_controller.cpp
    data_bus.cpp
)

# 设置包含目录
target_include_directories(databus PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src/proto
)

# 链接依赖库
target_link_libraries(databus
    proto
    ${CMAKE_THREAD_LIBS_INIT}
)

# 如果找到了Kafka库，链接它
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(RDKAFKA QUIET rdkafka++)
    if(RDKAFKA_FOUND)
        target_link_libraries(databus ${RDKAFKA_LIBRARIES})
        target_include_directories(databus PRIVATE ${RDKAFKA_INCLUDE_DIRS})
        target_compile_definitions(databus PRIVATE KAFKA_ENABLED)
    endif()
endif()

# 设置编译选项
target_compile_features(databus PUBLIC cxx_std_17)
target_compile_options(databus PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -O3 -march=native>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -O3 -march=native>
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /O2>
)