#pragma once

#include <unordered_map>
#include <unordered_set>
#include <string>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <chrono>
#include <memory>

namespace monitoring {

class AlertManager;

struct DataMessage {
    std::string symbol;
    uint64_t sequence_number;
    std::chrono::high_resolution_clock::time_point timestamp;
    std::string source;
    std::string message_type;
};

struct SequenceTracker {
    uint64_t last_sequence = 0;
    std::chrono::high_resolution_clock::time_point last_update;
    std::unordered_set<uint64_t> missing_sequences;
    uint64_t total_received = 0;
    uint64_t total_lost = 0;
    bool initialized = false;
};

class DataIntegrityChecker {
public:
    explicit DataIntegrityChecker(std::shared_ptr<AlertManager> alert_manager);
    ~DataIntegrityChecker();
    
    // Start/stop monitoring
    bool start();
    void stop();
    
    // Record data messages
    void recordMessage(const DataMessage& message);
    
    // Configuration
    void setMissingDataTimeout(std::chrono::seconds timeout) {
        missing_data_timeout_ = timeout;
    }
    void setAlertCooldown(std::chrono::seconds cooldown) {
        alert_cooldown_ = cooldown;
    }
    void setMaxSequenceGap(uint64_t max_gap) {
        max_sequence_gap_ = max_gap;
    }
    
    // Statistics
    struct IntegrityStats {
        uint64_t total_messages_received;
        uint64_t total_messages_lost;
        uint64_t total_sequence_gaps;
        double data_loss_rate;
        std::unordered_map<std::string, uint64_t> symbol_loss_count;
    };
    
    IntegrityStats getStatistics() const;
    void resetStatistics();
    
private:
    std::shared_ptr<AlertManager> alert_manager_;
    
    // Configuration
    std::chrono::seconds missing_data_timeout_{5}; // 5 seconds to detect missing data
    std::chrono::seconds alert_cooldown_{30}; // 30 seconds between alerts for same symbol
    uint64_t max_sequence_gap_{100}; // Maximum allowed sequence gap before alert
    
    // Threading
    std::atomic<bool> running_{false};
    std::thread processing_thread_;
    std::thread timeout_check_thread_;
    
    // Message queue
    std::queue<DataMessage> message_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // Sequence tracking per symbol
    std::unordered_map<std::string, SequenceTracker> sequence_trackers_;
    std::mutex trackers_mutex_;
    
    // Alert state per symbol
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> last_alert_times_;
    std::mutex alert_mutex_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::atomic<uint64_t> total_messages_received_{0};
    std::atomic<uint64_t> total_messages_lost_{0};
    std::atomic<uint64_t> total_sequence_gaps_{0};
    
    // Processing methods
    void processingLoop();
    void timeoutCheckLoop();
    void processMessage(const DataMessage& message);
    void checkSequenceIntegrity(const std::string& symbol, const DataMessage& message);
    void detectMissingSequences(const std::string& symbol, uint64_t current_sequence, uint64_t last_sequence);
    void checkForTimeouts();
    
    // Alert methods
    void sendDataLossAlert(const std::string& symbol, const std::vector<uint64_t>& missing_sequences);
    void sendSequenceGapAlert(const std::string& symbol, uint64_t gap_size, uint64_t expected, uint64_t received);
    void sendTimeoutAlert(const std::string& symbol, std::chrono::seconds timeout_duration);
    bool shouldSendAlert(const std::string& symbol);
    
    // Utility methods
    std::string formatMissingSequences(const std::vector<uint64_t>& sequences);
    void updatePrometheusMetrics(const std::string& symbol, uint64_t lost_count);
};

} // namespace monitoring