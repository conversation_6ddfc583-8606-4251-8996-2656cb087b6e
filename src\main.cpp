#include <iostream>
#include <memory>
#include <signal.h>
#include <thread>
#include <chrono>
#include <fstream>

// Core system components
#include "databus/data_bus.h"
#include "collectors/ctp_collector.h"
#include "interfaces/websocket_server.h"
#include "storage/storage_manager.h"
#include "monitoring/metrics_collector.h"
#include "security/security_manager.h"

// Logging and configuration
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <nlohmann/json.hpp>

using namespace financial_data;
using json = nlohmann::json;

// Global system components
std::unique_ptr<databus::DataBus> g_data_bus;
std::unique_ptr<CTPMarketDataCollector> g_ctp_collector;
std::unique_ptr<interfaces::WebSocketServer> g_websocket_server;
std::unique_ptr<StorageManager> g_storage_manager;
std::unique_ptr<monitoring::MetricsCollector> g_metrics_collector;

// System control flags
std::atomic<bool> g_shutdown_requested{false};
std::atomic<bool> g_system_healthy{true};

// Configuration
json g_config;

// Signal handler for graceful shutdown
void SignalHandler(int signal) {
    spdlog::info("Received signal {}, initiating graceful shutdown...", signal);
    g_shutdown_requested = true;
}

// Load system configuration
bool LoadConfiguration(const std::string& config_path = "config/app.json") {
    try {
        std::ifstream config_file(config_path);
        if (!config_file.is_open()) {
            spdlog::error("Failed to open configuration file: {}", config_path);
            return false;
        }
        
        config_file >> g_config;
        spdlog::info("Configuration loaded successfully from {}", config_path);
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to parse configuration: {}", e.what());
        return false;
    }
}

// Initialize logging system
bool InitializeLogging() {
    try {
        // Create console sink
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(spdlog::level::info);
        
        // Create file sink
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            g_config["logging"]["file"].get<std::string>(),
            1024 * 1024 * 100,  // 100MB
            10                  // 10 files
        );
        file_sink->set_level(spdlog::level::debug);
        
        // Create logger
        std::vector<spdlog::sink_ptr> sinks{console_sink, file_sink};
        auto logger = std::make_shared<spdlog::logger>("main", sinks.begin(), sinks.end());
        
        // Set log level
        std::string log_level = g_config["logging"]["level"].get<std::string>();
        if (log_level == "debug") {
            logger->set_level(spdlog::level::debug);
        } else if (log_level == "info") {
            logger->set_level(spdlog::level::info);
        } else if (log_level == "warn") {
            logger->set_level(spdlog::level::warn);
        } else if (log_level == "error") {
            logger->set_level(spdlog::level::err);
        }
        
        spdlog::set_default_logger(logger);
        spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%f] [%l] [%t] %v");
        
        spdlog::info("Logging system initialized successfully");
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize logging: " << e.what() << std::endl;
        return false;
    }
}

// Initialize data bus
bool InitializeDataBus() {
    try {
        databus::DataBusConfig config;
        config.worker_thread_count = g_config["server"]["threads"].get<size_t>();
        config.enable_kafka = true;
        config.enable_storage = true;  // 启用存储
        config.enable_monitoring = true;
        
        // 配置存储管理器
        if (g_storage_manager) {
            config.storage_config = g_storage_manager->GetConfig();
        }
        
        g_data_bus = std::make_unique<databus::DataBus>(config);
        
        if (!g_data_bus->Start()) {
            spdlog::error("Failed to start data bus");
            return false;
        }
        
        spdlog::info("Data bus initialized and started successfully");
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to initialize data bus: {}", e.what());
        return false;
    }
}

// Initialize storage manager
bool InitializeStorageManager() {
    try {
        StorageManagerConfig storage_config;
        
        // Redis配置
        storage_config.redis_config.host = g_config["redis"]["host"].get<std::string>();
        storage_config.redis_config.port = g_config["redis"]["port"].get<int>();
        storage_config.redis_config.database = g_config["redis"]["database"].get<int>();
        storage_config.redis_config.max_connections = g_config["redis"]["pool_size"].get<int>();
        
        if (g_config["redis"].contains("password") && !g_config["redis"]["password"].get<std::string>().empty()) {
            storage_config.redis_config.password = g_config["redis"]["password"].get<std::string>();
        }
        
        // ClickHouse配置
        if (g_config.contains("clickhouse")) {
            storage_config.clickhouse_config.host = g_config["clickhouse"]["host"].get<std::string>();
            storage_config.clickhouse_config.port = g_config["clickhouse"]["port"].get<uint16_t>();
            storage_config.clickhouse_config.database = g_config["clickhouse"]["database"].get<std::string>();
            storage_config.clickhouse_config.username = g_config["clickhouse"]["username"].get<std::string>();
            storage_config.clickhouse_config.password = g_config["clickhouse"]["password"].get<std::string>();
        }
        
        // 性能配置
        storage_config.worker_thread_count = g_config.value("storage_workers", 4);
        storage_config.enable_write_batching = g_config.value("enable_batching", true);
        storage_config.enable_auto_migration = g_config.value("enable_migration", true);
        
        g_storage_manager = std::make_unique<StorageManager>(storage_config);
        
        if (!g_storage_manager->Initialize()) {
            spdlog::error("Failed to initialize storage manager");
            return false;
        }
        
        spdlog::info("Storage manager initialized successfully");
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to initialize storage manager: {}", e.what());
        return false;
    }
}

// Initialize CTP collector
bool InitializeCTPCollector() {
    try {
        CTPConfig ctp_config;
        // Load CTP configuration from file or config
        ctp_config = CTPConfig::LoadFromFile("config/ctp_config.json");
        
        if (!ctp_config.IsValid()) {
            spdlog::warn("CTP configuration is invalid, CTP collector will not be started");
            return true;  // Not critical for system startup
        }
        
        g_ctp_collector = std::make_unique<CTPMarketDataCollector>();
        
        if (!g_ctp_collector->Initialize(ctp_config)) {
            spdlog::error("Failed to initialize CTP collector");
            return false;
        }
        
        // Set data callback to push data to data bus
        g_ctp_collector->SetDataCallback([](const MarketDataWrapper& data) {
            if (g_data_bus) {
                g_data_bus->PushData(data);
            }
        });
        
        // Set status callback for monitoring
        g_ctp_collector->SetStatusCallback([](ConnectionStatus status, const std::string& message) {
            spdlog::info("CTP connection status changed: {} - {}", static_cast<int>(status), message);
        });
        
        g_ctp_collector->Start();
        spdlog::info("CTP market data collector initialized and started successfully");
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to initialize CTP collector: {}", e.what());
        return false;
    }
}

// Initialize WebSocket server
bool InitializeWebSocketServer() {
    try {
        interfaces::WebSocketConfig ws_config;
        ws_config.port = g_config["server"]["port"].get<uint16_t>();
        ws_config.host = g_config["server"]["host"].get<std::string>();
        ws_config.max_connections = g_config["performance"]["max_concurrent_clients"].get<size_t>();
        
        g_websocket_server = std::make_unique<interfaces::WebSocketServer>(ws_config);
        
        if (!g_websocket_server->Initialize()) {
            spdlog::error("Failed to initialize WebSocket server");
            return false;
        }
        
        // Connect to data bus
        g_websocket_server->SetDataBus(g_data_bus);
        
        if (!g_websocket_server->Start()) {
            spdlog::error("Failed to start WebSocket server");
            return false;
        }
        
        spdlog::info("WebSocket server initialized and started on {}:{}", 
                    ws_config.host, ws_config.port);
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to initialize WebSocket server: {}", e.what());
        return false;
    }
}

// Initialize monitoring system
bool InitializeMonitoring() {
    try {
        monitoring::MetricsConfig metrics_config;
        metrics_config.prometheus_port = g_config["monitoring"]["prometheus_port"].get<uint16_t>();
        metrics_config.collection_interval_ms = g_config["monitoring"]["health_check_interval"].get<uint32_t>() * 1000;
        
        g_metrics_collector = std::make_unique<monitoring::MetricsCollector>(metrics_config);
        
        if (!g_metrics_collector->Initialize()) {
            spdlog::error("Failed to initialize metrics collector");
            return false;
        }
        
        g_metrics_collector->Start();
        spdlog::info("Monitoring system initialized successfully on port {}", metrics_config.prometheus_port);
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to initialize monitoring: {}", e.what());
        return false;
    }
}

// System health check
void PerformHealthCheck() {
    bool system_healthy = true;
    
    // Check data bus health
    if (g_data_bus && !g_data_bus->IsRunning()) {
        spdlog::warn("Data bus is not running");
        system_healthy = false;
    }
    
    // Check WebSocket server health
    if (g_websocket_server && !g_websocket_server->IsRunning()) {
        spdlog::warn("WebSocket server is not running");
        system_healthy = false;
    }
    
    // Check CTP collector health
    if (g_ctp_collector && !g_ctp_collector->IsHealthy()) {
        spdlog::warn("CTP collector is not healthy");
        // CTP issues are not critical for overall system health
    }
    
    // Check storage manager health
    if (g_storage_manager) {
        auto health = g_storage_manager->GetHealthStatus();
        if (!health.overall_healthy) {
            spdlog::warn("Storage manager is not healthy: {}", health.error_message);
            system_healthy = false;
        }
    }
    
    g_system_healthy = system_healthy;
    
    if (system_healthy) {
        spdlog::debug("System health check passed");
    } else {
        spdlog::warn("System health check failed");
    }
}

// Print system statistics
void PrintSystemStatistics() {
    spdlog::info("=== System Statistics ===");
    
    // Data bus statistics
    if (g_data_bus) {
        auto stats = g_data_bus->GetStatistics();
        spdlog::info("Data Bus - Messages: received={}, processed={}, sent={}, dropped={}", 
                    stats.total_messages_received.load(),
                    stats.total_messages_processed.load(),
                    stats.total_messages_sent.load(),
                    stats.total_messages_dropped.load());
        spdlog::info("Data Bus - Throughput: {} msg/sec, Latency: {} ns avg", 
                    stats.throughput_per_second.load(),
                    stats.avg_processing_latency_ns.load());
    }
    
    // WebSocket server statistics
    if (g_websocket_server) {
        auto stats = g_websocket_server->GetStatistics();
        spdlog::info("WebSocket - Connections: active={}, total={}", 
                    g_websocket_server->GetActiveConnectionCount(),
                    g_websocket_server->GetConnectionCount());
    }
    
    // CTP collector statistics
    if (g_ctp_collector) {
        auto stats = g_ctp_collector->GetStatistics();
        spdlog::info("CTP Collector - Messages: received={}, processed={}, errors={}", 
                    stats.total_received, stats.total_processed, stats.total_errors);
        spdlog::info("CTP Collector - Rate: {:.2f} msg/sec, Status: {}", 
                    stats.messages_per_second, static_cast<int>(stats.status));
    }
    
    // Storage manager statistics
    if (g_storage_manager) {
        auto stats = g_storage_manager->GetStatistics();
        spdlog::info("Storage Manager - Redis: success={}, failed={}, latency={:.2f}ms", 
                    stats.redis_writes_success.load(), stats.redis_writes_failed.load(), 
                    stats.redis_avg_latency_ms.load());
        spdlog::info("Storage Manager - ClickHouse: success={}, failed={}, latency={:.2f}ms", 
                    stats.clickhouse_writes_success.load(), stats.clickhouse_writes_failed.load(), 
                    stats.clickhouse_avg_latency_ms.load());
        spdlog::info("Storage Manager - Tasks: pending={}, completed={}, failed={}", 
                    stats.pending_tasks.load(), stats.completed_tasks.load(), stats.failed_tasks.load());
    }
    
    spdlog::info("========================");
}

// Graceful shutdown
void GracefulShutdown() {
    spdlog::info("Starting graceful shutdown...");
    
    // Stop accepting new connections
    if (g_websocket_server) {
        spdlog::info("Stopping WebSocket server...");
        g_websocket_server->Stop();
    }
    
    // Stop data collection
    if (g_ctp_collector) {
        spdlog::info("Stopping CTP collector...");
        g_ctp_collector->Stop();
    }
    
    // Stop monitoring
    if (g_metrics_collector) {
        spdlog::info("Stopping metrics collector...");
        g_metrics_collector->Stop();
    }
    
    // Allow some time for pending operations
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Stop data bus (this will flush remaining data)
    if (g_data_bus) {
        spdlog::info("Stopping data bus...");
        g_data_bus->Stop();
    }
    
    // Shutdown storage
    if (g_storage_manager) {
        spdlog::info("Shutting down storage manager...");
        g_storage_manager->Shutdown();
    }
    
    spdlog::info("Graceful shutdown completed");
}

int main(int argc, char* argv[]) {
    std::cout << "Financial Data Service System v1.0.0" << std::endl;
    std::cout << "Starting system initialization..." << std::endl;
    
    // Parse command line arguments
    std::string config_path = "config/app.json";
    if (argc > 1) {
        config_path = argv[1];
    }
    
    // Load configuration
    if (!LoadConfiguration(config_path)) {
        std::cerr << "Failed to load configuration, exiting..." << std::endl;
        return 1;
    }
    
    // Initialize logging
    if (!InitializeLogging()) {
        std::cerr << "Failed to initialize logging, exiting..." << std::endl;
        return 1;
    }
    
    // Set up signal handlers
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    spdlog::info("Financial Data Service System starting up...");
    spdlog::info("Configuration loaded from: {}", config_path);
    
    try {
        // Initialize core components in order
        if (!InitializeStorageManager()) {
            spdlog::error("Failed to initialize storage manager, exiting...");
            return 1;
        }
        
        if (!InitializeDataBus()) {
            spdlog::error("Failed to initialize data bus, exiting...");
            return 1;
        }
        
        if (!InitializeWebSocketServer()) {
            spdlog::error("Failed to initialize WebSocket server, exiting...");
            return 1;
        }
        
        if (!InitializeCTPCollector()) {
            spdlog::warn("CTP collector initialization failed, continuing without it...");
        }
        
        if (!InitializeMonitoring()) {
            spdlog::warn("Monitoring initialization failed, continuing without it...");
        }
        
        spdlog::info("All systems initialized successfully");
        spdlog::info("System is ready to serve requests");
        
        // Main service loop
        auto last_health_check = std::chrono::steady_clock::now();
        auto last_stats_print = std::chrono::steady_clock::now();
        
        while (!g_shutdown_requested) {
            auto now = std::chrono::steady_clock::now();
            
            // Perform periodic health checks
            if (now - last_health_check >= std::chrono::seconds(30)) {
                PerformHealthCheck();
                last_health_check = now;
            }
            
            // Print statistics periodically
            if (now - last_stats_print >= std::chrono::minutes(5)) {
                PrintSystemStatistics();
                last_stats_print = now;
            }
            
            // Sleep to avoid busy waiting
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
    } catch (const std::exception& e) {
        spdlog::error("Unhandled exception in main loop: {}", e.what());
        GracefulShutdown();
        return 1;
    }
    
    // Graceful shutdown
    GracefulShutdown();
    
    spdlog::info("Financial Data Service System shutdown completed");
    return 0;
}