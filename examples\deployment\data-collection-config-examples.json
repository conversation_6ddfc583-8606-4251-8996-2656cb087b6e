{"description": "Market Data Collection Configuration Examples", "examples": {"development_environment": {"description": "Configuration for development environment with reduced load", "config": {"collection": {"pytdx": {"enabled": true, "servers": [{"host": "**************", "port": 7709}], "batch_size": 100, "concurrent_requests": 2, "archive_enabled": true, "archive_batch_size": 1000, "connection_timeout_seconds": 30, "read_timeout_seconds": 60, "retry_attempts": 2, "retry_delay_seconds": 5}, "ctp": {"enabled": false, "config_path": "/app/config/ctp_config_dev.json", "failover_timeout": 30}, "coordination": {"priority_source": "pytdx", "overlap_tolerance_seconds": 300, "enable_data_merge": false, "data_validation_enabled": true}}, "storage": {"hot_storage": {"type": "redis", "retention_days": 1, "config": {"host": "localhost", "port": 6379, "db": 1, "max_connections": 10}}, "warm_storage": {"type": "clickhouse", "retention_days": 30, "config": {"host": "localhost", "port": 9000, "database": "market_data_dev", "username": "dev_user", "password": "dev_password"}}}, "scheduling": {"historical_update": {"cron": "0 9 * * *", "symbols": ["000001.SZ", "000002.SZ", "600000.SH"], "lookback_days": 1}}, "monitoring": {"enable_metrics": true, "logging": {"level": "DEBUG"}}}}, "production_environment": {"description": "Configuration for production environment with high performance", "config": {"collection": {"pytdx": {"enabled": true, "servers": [{"host": "**************", "port": 7709}, {"host": "************", "port": 7709}, {"host": "*************", "port": 7709}, {"host": "**************", "port": 7709}, {"host": "*************", "port": 7709}], "batch_size": 2000, "concurrent_requests": 8, "archive_enabled": true, "archive_batch_size": 10000, "connection_timeout_seconds": 15, "read_timeout_seconds": 30, "retry_attempts": 3, "retry_delay_seconds": 2}, "ctp": {"enabled": true, "config_path": "/app/config/ctp_config_prod.json", "failover_timeout": 15, "heartbeat_interval": 5, "reconnect_attempts": 5, "reconnect_delay_seconds": 5}, "coordination": {"priority_source": "ctp", "overlap_tolerance_seconds": 60, "enable_data_merge": true, "conflict_resolution_strategy": "timestamp_priority", "data_validation_enabled": true}}, "storage": {"hot_storage": {"type": "redis", "retention_days": 7, "config": {"host": "redis-cluster-service", "port": 6379, "db": 0, "max_connections": 200, "connection_timeout_seconds": 3, "socket_timeout_seconds": 5, "retry_on_timeout": true, "health_check_interval": 30}}, "warm_storage": {"type": "clickhouse", "retention_days": 730, "config": {"host": "clickhouse-cluster-service", "port": 9000, "database": "market_data", "username": "market_user", "password": "${CLICKHOUSE_PASSWORD}", "connection_timeout_seconds": 10, "query_timeout_seconds": 300, "max_connections": 100, "compression": "lz4"}}, "cold_storage": {"type": "s3", "config": {"endpoint": "https://s3.amazonaws.com", "access_key": "${S3_ACCESS_KEY}", "secret_key": "${S3_SECRET_KEY}", "bucket": "market-data-archive-prod", "region": "us-east-1", "secure": true, "compression": "gzip", "part_size_mb": 128, "max_concurrent_uploads": 20}}}, "scheduling": {"historical_update": {"cron": "0 2 * * *", "symbols": ["all"], "lookback_days": 1, "batch_size": 2000, "max_concurrent_tasks": 10, "timeout_minutes": 120}, "data_migration": {"cron": "0 3 * * *", "batch_size": 50000, "max_concurrent_migrations": 5, "timeout_minutes": 240}}, "monitoring": {"enable_metrics": true, "alert_thresholds": {"data_delay_seconds": 30, "error_rate_percent": 2.0, "memory_usage_percent": 85, "disk_usage_percent": 90}, "logging": {"level": "INFO", "format": "json"}}, "api": {"http": {"rate_limiting": {"enabled": true, "requests_per_minute": 10000, "burst_size": 1000}}}}}, "high_frequency_trading": {"description": "Configuration optimized for high-frequency trading scenarios", "config": {"collection": {"pytdx": {"enabled": true, "batch_size": 5000, "concurrent_requests": 12, "connection_timeout_seconds": 5, "read_timeout_seconds": 10, "retry_attempts": 1, "retry_delay_seconds": 1}, "ctp": {"enabled": true, "heartbeat_interval": 2, "reconnect_delay_seconds": 1}, "coordination": {"priority_source": "ctp", "overlap_tolerance_seconds": 10, "enable_data_merge": true}}, "storage": {"hot_storage": {"retention_days": 3, "config": {"max_connections": 500, "connection_timeout_seconds": 1, "socket_timeout_seconds": 2}}}, "api": {"http": {"rate_limiting": {"requests_per_minute": 50000, "burst_size": 5000}}, "websocket": {"max_connections": 10000, "ping_interval_seconds": 10}}}}, "research_environment": {"description": "Configuration for research and backtesting with historical data focus", "config": {"collection": {"pytdx": {"enabled": true, "batch_size": 1000, "concurrent_requests": 4, "archive_enabled": true, "archive_batch_size": 20000}, "ctp": {"enabled": false}}, "storage": {"hot_storage": {"retention_days": 30}, "warm_storage": {"retention_days": 1825, "config": {"compression": "zstd"}}, "cold_storage": {"config": {"compression": "bzip2"}}}, "scheduling": {"historical_update": {"cron": "0 1 * * *", "lookback_days": 7}, "data_migration": {"cron": "0 4 * * 0", "batch_size": 100000}}}}, "minimal_setup": {"description": "Minimal configuration for testing and small deployments", "config": {"collection": {"pytdx": {"enabled": true, "servers": [{"host": "**************", "port": 7709}], "batch_size": 500, "concurrent_requests": 2}, "ctp": {"enabled": false}}, "storage": {"hot_storage": {"type": "redis", "retention_days": 1, "config": {"host": "localhost", "port": 6379, "max_connections": 5}}}, "scheduling": {"historical_update": {"cron": "0 12 * * *", "symbols": ["000001.SZ", "600000.SH"], "lookback_days": 1}}, "monitoring": {"enable_metrics": false, "logging": {"level": "WARN"}}}}}, "configuration_templates": {"symbol_lists": {"shanghai_main_board": ["600000.SH", "600036.SH", "600519.SH", "600887.SH", "601318.SH", "601398.SH", "601857.SH", "601988.SH", "603259.SH", "603993.SH"], "shenzhen_main_board": ["000001.SZ", "000002.SZ", "000858.SZ", "002415.SZ", "002594.SZ", "300059.SZ", "300750.SZ", "300760.SZ", "300896.SZ", "301029.SZ"], "popular_etfs": ["510050.SH", "510300.SH", "510500.SH", "159919.SZ", "159915.SZ"]}, "cron_schedules": {"market_hours_only": {"morning_session": "30 9-11 * * 1-5", "afternoon_session": "0 13-15 * * 1-5"}, "after_market_hours": {"daily_update": "0 16 * * 1-5", "weekend_maintenance": "0 2 * * 0"}}, "storage_policies": {"aggressive_archiving": {"hot_to_warm_days": 1, "warm_to_cold_days": 30}, "conservative_archiving": {"hot_to_warm_days": 14, "warm_to_cold_days": 365}}}}