# PyTDX数据采集系统 - 增强版

基于原有系统的直接升级，实现了全面的数据类型支持和自动代码表管理。

## 🎯 升级内容

### 1. 自动代码表更新
- **股票代码表**: 自动获取深圳、上海所有股票代码
- **指数代码表**: 自动获取主要指数代码
- **期货代码表**: 自动获取期货合约代码
- **基金代码表**: 自动获取ETF等基金代码
- **债券代码表**: 自动获取债券代码

### 2. 全量数据更新
- 不再需要手动配置股票代码列表
- 根据代码表自动更新所有标的数据
- 支持股票、指数、期货、基金等多种数据类型
- 可配置更新频率和数据范围

### 3. 智能调度
- 每日自动更新代码表
- 分时段更新不同类型数据
- 交易时间内实时数据更新
- 故障自动重试

## 🚀 快速开始

### Windows用户 (推荐)
```batch
# 双击运行批处理文件
start_enhanced_scheduler.bat
```

### 命令行用户
```bash
# 直接运行Python脚本
python start_enhanced_scheduler.py
```

## ⚙️ 配置说明

主配置文件: `config/scheduler_config.json`

### 新增的调度任务

```json
{
  "tasks": {
    "update_symbol_lists": {
      "enabled": true,
      "cron": "0 8 * * 1-5",
      "description": "每日更新所有代码表",
      "parameters": {
        "symbol_types": ["stock", "index", "futures", "fund", "bond"]
      }
    },
    "stock_data_update": {
      "enabled": true,
      "cron": "0 18 * * 1-5", 
      "description": "股票数据全量更新",
      "symbols": "auto_from_list",
      "parameters": {
        "data_types": ["kline_D", "kline_60", "kline_30"],
        "symbol_source": "stock_list",
        "batch_size": 50
      }
    }
  }
}
```

### 代码表管理配置

```json
{
  "symbol_management": {
    "auto_update_enabled": true,
    "cache_expire_hours": 24,
    "supported_types": {
      "stock": {
        "enabled": true,
        "markets": [0, 1],
        "description": "股票代码表"
      },
      "index": {
        "enabled": true,
        "markets": [0, 1], 
        "filters": ["000", "399", "880"],
        "description": "指数代码表"
      }
    }
  }
}
```

## 📊 调度任务说明

### 代码表更新任务
- **时间**: 每日8:00 (工作日)
- **功能**: 更新所有类型的代码表
- **缓存**: 24小时有效期

### 数据更新任务
- **股票数据**: 每日18:00，更新所有股票的日线、60分钟、30分钟数据
- **指数数据**: 每日18:05，更新所有指数数据
- **期货数据**: 每日18:10，更新所有期货数据
- **基金数据**: 每日18:15，更新所有基金数据

### 实时数据任务
- **股票实时**: 交易时间内每5分钟更新
- **指数实时**: 交易时间内每5分钟更新
- **分钟线**: 交易时间内每15分钟更新

## 🔧 核心改进

### 1. PyTDX采集器增强
新增方法:
- `get_all_symbol_lists()`: 获取所有类型代码表
- `_is_stock_symbol()`: 判断股票代码
- `_is_index_symbol()`: 判断指数代码
- `_is_fund_symbol()`: 判断基金代码
- `_is_bond_symbol()`: 判断债券代码
- `_get_futures_symbols()`: 获取期货代码表

### 2. 调度器增强
新增任务执行方法:
- `_execute_symbol_list_update()`: 代码表更新
- `_execute_full_data_update()`: 全量数据更新
- `_execute_realtime_update()`: 实时数据更新

### 3. 配置增强
- 代码表管理配置
- 数据类型配置
- 自动化任务配置

## 📈 使用效果

### 之前
- 只能更新配置中指定的有限股票代码
- 需要手动维护股票代码列表
- 数据类型支持有限

### 现在
- 自动获取全市场所有股票、指数、期货、基金代码
- 无需手动维护代码列表
- 支持多种数据类型的全量更新
- 智能调度和自动重试

## 📝 日志和监控

- **主日志**: `logs/scheduler_service.log`
- **调度统计**: 每5分钟输出统计信息
- **任务状态**: 启动时显示所有任务状态
- **错误处理**: 自动重试和错误记录

## 🚨 注意事项

1. **首次运行**: 代码表更新可能需要较长时间
2. **网络要求**: 确保网络连接稳定
3. **存储空间**: 全量数据需要足够的存储空间
4. **服务器限制**: 注意PyTDX服务器的访问限制

## 🎉 升级完成

您的PyTDX数据采集系统已成功升级！现在可以：

✅ 自动获取全市场所有标的代码  
✅ 全量更新多种数据类型  
✅ 智能调度和故障恢复  
✅ 无需手动维护代码列表  

立即运行 `start_enhanced_scheduler.bat` 开始使用！