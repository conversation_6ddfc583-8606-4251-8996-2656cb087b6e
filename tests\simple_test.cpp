#include <iostream>
#include <thread>
#include <chrono>
#include "collectors/ctp_collector.h"

using namespace financial_data;

int main() {
    std::cout << "=== CTP Collector Simple Test ===" << std::endl;
    
    try {
        // 测试配置加载
        CTPConfig config;
        config.front_address = "tcp://127.0.0.1:10131";
        config.broker_id = "test_broker";
        config.user_id = "test_user";
        config.password = "test_password";
        config.flow_path = "./test_flow/";
        config.heartbeat_interval = 5;
        config.reconnect_interval = 2;
        config.max_reconnect_attempts = 3;
        config.enable_level2 = true;
        
        std::cout << "✓ Config creation successful" << std::endl;
        
        // 测试配置验证
        if (config.IsValid()) {
            std::cout << "✓ Config validation successful" << std::endl;
        } else {
            std::cout << "✗ Config validation failed" << std::endl;
            return 1;
        }
        
        // 测试采集器创建
        auto collector = std::make_unique<CTPMarketDataCollector>();
        std::cout << "✓ Collector creation successful" << std::endl;
        
        // 测试初始化
        if (collector->Initialize(config)) {
            std::cout << "✓ Collector initialization successful" << std::endl;
        } else {
            std::cout << "✗ Collector initialization failed" << std::endl;
            return 1;
        }
        
        // 测试状态检查
        auto status = collector->GetConnectionStatus();
        std::cout << "✓ Initial status: " << static_cast<int>(status) << std::endl;
        
        // 测试数据回调设置
        int callback_count = 0;
        collector->SetDataCallback([&callback_count](const MarketDataWrapper& data) {
            callback_count++;
            std::cout << "Data callback triggered: " << callback_count << std::endl;
        });
        std::cout << "✓ Data callback set" << std::endl;
        
        // 测试状态回调设置
        collector->SetStatusCallback([](ConnectionStatus status, const std::string& message) {
            std::cout << "Status changed: " << static_cast<int>(status) 
                      << " - " << message << std::endl;
        });
        std::cout << "✓ Status callback set" << std::endl;
        
        // 测试启动
        collector->Start();
        std::cout << "✓ Collector started" << std::endl;
        
        // 测试连接（模拟）
        if (collector->Connect()) {
            std::cout << "✓ Connection successful" << std::endl;
        } else {
            std::cout << "✗ Connection failed" << std::endl;
        }
        
        // 等待连接建立
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 测试订阅
        std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409"};
        if (collector->Subscribe(symbols)) {
            std::cout << "✓ Subscription successful" << std::endl;
        } else {
            std::cout << "✗ Subscription failed" << std::endl;
        }
        
        // 检查订阅列表
        auto subscribed = collector->GetSubscribedSymbols();
        std::cout << "✓ Subscribed symbols count: " << subscribed.size() << std::endl;
        
        // 模拟接收一些数据
        for (int i = 0; i < 5; ++i) {
            collector->OnRtnDepthMarketData(nullptr);
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 等待数据处理
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 检查统计信息
        auto stats = collector->GetStatistics();
        std::cout << "✓ Statistics - Received: " << stats.total_received 
                  << ", Processed: " << stats.total_processed 
                  << ", Errors: " << stats.total_errors << std::endl;
        
        // 测试健康检查
        if (collector->IsHealthy()) {
            std::cout << "✓ Collector is healthy" << std::endl;
        } else {
            std::cout << "⚠ Collector health check failed" << std::endl;
        }
        
        // 测试工具函数
        std::cout << "✓ Exchange for CU2409: " << ctp_utils::GetExchangeFromSymbol("CU2409") << std::endl;
        std::cout << "✓ Valid symbol CU2409: " << (ctp_utils::IsValidCTPSymbol("CU2409") ? "Yes" : "No") << std::endl;
        std::cout << "✓ Valid price 100.0: " << (ctp_utils::ValidatePrice(100.0) ? "Yes" : "No") << std::endl;
        
        // 测试关闭
        collector->Shutdown();
        std::cout << "✓ Collector shutdown successful" << std::endl;
        
        std::cout << "\n=== All Tests Passed! ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "✗ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}