#include "financial_data_sdk.h"
#include <grpcpp/grpcpp.h>
#include <thread>
#include <chrono>

namespace financial_data {
namespace sdk {

// Advanced connection pool with health monitoring and load balancing
class AdvancedConnectionPool {
public:
    struct ChannelInfo {
        std::shared_ptr<grpc::Channel> channel;
        std::atomic<int> active_requests{0};
        std::atomic<bool> healthy{true};
        std::chrono::steady_clock::time_point last_health_check;
        
        ChannelInfo(std::shared_ptr<grpc::Channel> ch) 
            : channel(std::move(ch))
            , last_health_check(std::chrono::steady_clock::now()) {}
    };

    explicit AdvancedConnectionPool(const ConnectionConfig& config, size_t pool_size = 4)
        : config_(config), pool_size_(pool_size), health_check_running_(false) {
        CreateChannels();
        StartHealthChecker();
    }

    ~AdvancedConnectionPool() {
        StopHealthChecker();
    }

    std::shared_ptr<grpc::Channel> GetChannel() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Find the channel with least active requests and is healthy
        ChannelInfo* best_channel = nullptr;
        int min_requests = std::numeric_limits<int>::max();
        
        for (auto& channel_info : channels_) {
            if (channel_info.healthy.load() && 
                channel_info.active_requests.load() < min_requests) {
                min_requests = channel_info.active_requests.load();
                best_channel = &channel_info;
            }
        }
        
        if (best_channel) {
            best_channel->active_requests++;
            return best_channel->channel;
        }
        
        // If no healthy channels, return the first one and mark for health check
        if (!channels_.empty()) {
            channels_[0].active_requests++;
            ScheduleHealthCheck(0);
            return channels_[0].channel;
        }
        
        return nullptr;
    }

    void ReleaseChannel(const std::shared_ptr<grpc::Channel>& channel) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        for (auto& channel_info : channels_) {
            if (channel_info.channel == channel) {
                channel_info.active_requests--;
                break;
            }
        }
    }

    size_t GetHealthyChannelCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return std::count_if(channels_.begin(), channels_.end(),
                           [](const ChannelInfo& info) { return info.healthy.load(); });
    }

    void MarkChannelUnhealthy(const std::shared_ptr<grpc::Channel>& channel) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        for (auto& channel_info : channels_) {
            if (channel_info.channel == channel) {
                channel_info.healthy = false;
                break;
            }
        }
    }

private:
    void CreateChannels() {
        grpc::ChannelArguments args;
        ConfigureChannelArgs(args);
        
        auto credentials = CreateCredentials();
        
        channels_.reserve(pool_size_);
        for (size_t i = 0; i < pool_size_; ++i) {
            auto channel = grpc::CreateChannel(config_.server_address, credentials, args);
            channels_.emplace_back(channel);
        }
    }

    void ConfigureChannelArgs(grpc::ChannelArguments& args) {
        args.SetMaxReceiveMessageSize(config_.max_receive_message_size);
        args.SetMaxSendMessageSize(config_.max_send_message_size);
        
        if (config_.enable_keepalive) {
            args.SetInt(GRPC_ARG_KEEPALIVE_TIME_MS, 
                       static_cast<int>(config_.keepalive_time.count() * 1000));
            args.SetInt(GRPC_ARG_KEEPALIVE_TIMEOUT_MS,
                       static_cast<int>(config_.keepalive_timeout.count() * 1000));
            args.SetInt(GRPC_ARG_KEEPALIVE_PERMIT_WITHOUT_CALLS, 1);
            args.SetInt(GRPC_ARG_HTTP2_BDP_PROBE, 1);
        }

        if (config_.enable_compression) {
            args.SetCompressionAlgorithm(GRPC_COMPRESS_GZIP);
        }

        // Performance optimizations
        args.SetInt(GRPC_ARG_USE_LOCAL_SUBCHANNEL_POOL, 1);
        args.SetInt(GRPC_ARG_HTTP2_MIN_RECV_PING_INTERVAL_WITHOUT_DATA_MS, 300000);
        args.SetInt(GRPC_ARG_HTTP2_MIN_SENT_PING_INTERVAL_WITHOUT_DATA_MS, 300000);
    }

    std::shared_ptr<grpc::ChannelCredentials> CreateCredentials() {
        if (!config_.auth_token.empty()) {
            auto ssl_opts = grpc::SslCredentialsOptions();
            auto ssl_creds = grpc::SslCredentials(ssl_opts);
            
            // Add JWT token authentication
            auto call_creds = grpc::AccessTokenCredentials(config_.auth_token);
            return grpc::CompositeChannelCredentials(ssl_creds, call_creds);
        }
        
        return grpc::InsecureChannelCredentials();
    }

    void StartHealthChecker() {
        health_check_running_ = true;
        health_check_thread_ = std::thread(&AdvancedConnectionPool::HealthCheckLoop, this);
    }

    void StopHealthChecker() {
        health_check_running_ = false;
        if (health_check_thread_.joinable()) {
            health_check_thread_.join();
        }
    }

    void HealthCheckLoop() {
        while (health_check_running_) {
            std::this_thread::sleep_for(std::chrono::seconds(30)); // Check every 30 seconds
            
            std::lock_guard<std::mutex> lock(mutex_);
            for (size_t i = 0; i < channels_.size(); ++i) {
                auto& channel_info = channels_[i];
                auto now = std::chrono::steady_clock::now();
                
                // Check if it's time for health check
                if (now - channel_info.last_health_check > std::chrono::seconds(30)) {
                    CheckChannelHealth(i);
                    channel_info.last_health_check = now;
                }
            }
        }
    }

    void CheckChannelHealth(size_t index) {
        auto& channel_info = channels_[index];
        auto state = channel_info.channel->GetState(false);
        
        bool is_healthy = (state == GRPC_CHANNEL_READY || state == GRPC_CHANNEL_IDLE);
        channel_info.healthy = is_healthy;
        
        if (!is_healthy && state == GRPC_CHANNEL_TRANSIENT_FAILURE) {
            // Try to reconnect
            channel_info.channel->GetState(true); // Force reconnection attempt
        }
    }

    void ScheduleHealthCheck(size_t index) {
        // Mark for immediate health check
        channels_[index].last_health_check = std::chrono::steady_clock::time_point{};
    }

    ConnectionConfig config_;
    size_t pool_size_;
    std::vector<ChannelInfo> channels_;
    mutable std::mutex mutex_;
    
    std::atomic<bool> health_check_running_;
    std::thread health_check_thread_;
};

} // namespace sdk
} // namespace financial_data