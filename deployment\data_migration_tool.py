#!/usr/bin/env python3
"""
Financial Data Service - Data Migration Tool
Handles migration of historical data from legacy systems to the new architecture
"""

import asyncio
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import redis
import clickhouse_connect
import boto3
from pathlib import Path
import argparse
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_migration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DataMigrationTool:
    def __init__(self, config_file: str):
        """Initialize the data migration tool with configuration"""
        self.config = self._load_config(config_file)
        self.redis_client = None
        self.clickhouse_client = None
        self.s3_client = None
        self.migration_stats = {
            'total_records': 0,
            'migrated_records': 0,
            'failed_records': 0,
            'start_time': None,
            'end_time': None
        }
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load migration configuration from file"""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load config file {config_file}: {e}")
            sys.exit(1)
    
    async def initialize_connections(self):
        """Initialize connections to all storage systems"""
        try:
            # Redis connection
            redis_config = self.config['redis']
            self.redis_client = redis.Redis(
                host=redis_config['host'],
                port=redis_config['port'],
                password=redis_config.get('password'),
                decode_responses=True
            )
            
            # ClickHouse connection
            ch_config = self.config['clickhouse']
            self.clickhouse_client = clickhouse_connect.get_client(
                host=ch_config['host'],
                port=ch_config['port'],
                username=ch_config['username'],
                password=ch_config['password'],
                database=ch_config['database']
            )
            
            # S3/MinIO connection
            s3_config = self.config['s3']
            self.s3_client = boto3.client(
                's3',
                endpoint_url=s3_config['endpoint'],
                aws_access_key_id=s3_config['access_key'],
                aws_secret_access_key=s3_config['secret_key']
            )
            
            logger.info("All storage connections initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize connections: {e}")
            raise
    
    def validate_data_integrity(self, data: Dict[str, Any]) -> bool:
        """Validate data integrity before migration"""
        required_fields = ['timestamp', 'symbol', 'exchange', 'last_price']
        
        # Check required fields
        for field in required_fields:
            if field not in data or data[field] is None:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Validate timestamp
        try:
            timestamp = pd.to_datetime(data['timestamp'])
            if timestamp > datetime.now():
                logger.warning(f"Future timestamp detected: {timestamp}")
                return False
        except:
            logger.warning(f"Invalid timestamp: {data['timestamp']}")
            return False
        
        # Validate price
        try:
            price = float(data['last_price'])
            if price <= 0:
                logger.warning(f"Invalid price: {price}")
                return False
        except:
            logger.warning(f"Invalid price format: {data['last_price']}")
            return False
        
        return True
    
    def transform_legacy_data(self, legacy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform legacy data format to new standardized format"""
        try:
            # Map legacy fields to new format
            transformed = {
                'timestamp': pd.to_datetime(legacy_data.get('time', legacy_data.get('timestamp'))).value,
                'symbol': legacy_data.get('code', legacy_data.get('symbol', '')).upper(),
                'exchange': self._map_exchange(legacy_data.get('market', legacy_data.get('exchange', ''))),
                'last_price': float(legacy_data.get('price', legacy_data.get('last_price', 0))),
                'volume': int(legacy_data.get('vol', legacy_data.get('volume', 0))),
                'turnover': float(legacy_data.get('amount', legacy_data.get('turnover', 0))),
                'open_interest': int(legacy_data.get('oi', legacy_data.get('open_interest', 0))),
                'sequence': int(legacy_data.get('seq', legacy_data.get('sequence', 0)))
            }
            
            # Handle bid/ask data if available
            if 'bid_prices' in legacy_data and 'ask_prices' in legacy_data:
                transformed['bids'] = [
                    {'price': p, 'volume': v} 
                    for p, v in zip(legacy_data['bid_prices'], legacy_data['bid_volumes'])
                ]
                transformed['asks'] = [
                    {'price': p, 'volume': v} 
                    for p, v in zip(legacy_data['ask_prices'], legacy_data['ask_volumes'])
                ]
            
            return transformed
            
        except Exception as e:
            logger.error(f"Failed to transform legacy data: {e}")
            return None
    
    def _map_exchange(self, legacy_exchange: str) -> str:
        """Map legacy exchange codes to standardized codes"""
        exchange_mapping = {
            'SH': 'SSE',    # Shanghai Stock Exchange
            'SZ': 'SZSE',   # Shenzhen Stock Exchange
            'CF': 'CFFEX',  # China Financial Futures Exchange
            'SHF': 'SHFE',  # Shanghai Futures Exchange
            'DCE': 'DCE',   # Dalian Commodity Exchange
            'CZC': 'CZCE',  # Zhengzhou Commodity Exchange
            'INE': 'INE'    # Shanghai International Energy Exchange
        }
        return exchange_mapping.get(legacy_exchange.upper(), legacy_exchange.upper())
    
    async def migrate_to_redis(self, data: Dict[str, Any]) -> bool:
        """Migrate data to Redis hot storage"""
        try:
            symbol = data['symbol']
            timestamp = data['timestamp']
            
            # Store latest tick
            latest_key = f"tick:{symbol}:latest"
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.hset, latest_key, data
            )
            
            # Store in time series
            ts_key = f"ts:{symbol}"
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.zadd, ts_key, {json.dumps(data): timestamp}
            )
            
            # Set expiration for hot data (7 days)
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.expire, latest_key, 7 * 24 * 3600
            )
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.expire, ts_key, 7 * 24 * 3600
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate to Redis: {e}")
            return False
    
    async def migrate_to_clickhouse(self, batch_data: List[Dict[str, Any]]) -> bool:
        """Migrate batch data to ClickHouse warm storage"""
        try:
            if not batch_data:
                return True
            
            # Prepare data for ClickHouse insertion
            df = pd.DataFrame(batch_data)
            
            # Convert timestamp to ClickHouse DateTime64 format
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Insert data into ClickHouse
            table_name = 'market_data.tick_data'
            await asyncio.get_event_loop().run_in_executor(
                None, self.clickhouse_client.insert_df, table_name, df
            )
            
            logger.info(f"Successfully migrated {len(batch_data)} records to ClickHouse")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate to ClickHouse: {e}")
            return False
    
    async def migrate_to_s3(self, data: List[Dict[str, Any]], date: str) -> bool:
        """Migrate data to S3/MinIO cold storage"""
        try:
            if not data:
                return True
            
            # Convert to Parquet format for efficient storage
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Create Parquet file in memory
            parquet_buffer = df.to_parquet(index=False, compression='snappy')
            
            # Upload to S3
            bucket_name = self.config['s3']['bucket']
            key = f"market_data/year={date[:4]}/month={date[5:7]}/day={date[8:10]}/data.parquet"
            
            await asyncio.get_event_loop().run_in_executor(
                None, 
                self.s3_client.put_object,
                {
                    'Bucket': bucket_name,
                    'Key': key,
                    'Body': parquet_buffer,
                    'ContentType': 'application/octet-stream'
                }
            )
            
            logger.info(f"Successfully migrated {len(data)} records to S3 for date {date}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate to S3: {e}")
            return False
    
    def calculate_checksum(self, data: Dict[str, Any]) -> str:
        """Calculate checksum for data integrity verification"""
        data_str = json.dumps(data, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()
    
    async def verify_migration(self, original_data: Dict[str, Any], storage_type: str) -> bool:
        """Verify that migrated data matches original data"""
        try:
            symbol = original_data['symbol']
            timestamp = original_data['timestamp']
            
            if storage_type == 'redis':
                # Verify Redis data
                latest_key = f"tick:{symbol}:latest"
                migrated_data = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.hgetall, latest_key
                )
                
                if not migrated_data:
                    return False
                
                # Compare key fields
                return (
                    migrated_data.get('symbol') == original_data['symbol'] and
                    float(migrated_data.get('last_price', 0)) == original_data['last_price']
                )
                
            elif storage_type == 'clickhouse':
                # Verify ClickHouse data
                query = f"""
                SELECT * FROM market_data.tick_data 
                WHERE symbol = '{symbol}' AND timestamp = '{pd.to_datetime(timestamp)}'
                LIMIT 1
                """
                result = await asyncio.get_event_loop().run_in_executor(
                    None, self.clickhouse_client.query, query
                )
                
                return len(result.result_rows) > 0
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to verify migration: {e}")
            return False
    
    async def process_file(self, file_path: str, target_storage: str) -> Dict[str, int]:
        """Process a single data file for migration"""
        stats = {'processed': 0, 'migrated': 0, 'failed': 0}
        batch_size = self.config.get('batch_size', 1000)
        batch_data = []
        
        try:
            logger.info(f"Processing file: {file_path}")
            
            # Determine file format and read data
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.endswith('.json'):
                with open(file_path, 'r') as f:
                    data = json.load(f)
                df = pd.DataFrame(data)
            elif file_path.endswith('.parquet'):
                df = pd.read_parquet(file_path)
            else:
                logger.error(f"Unsupported file format: {file_path}")
                return stats
            
            # Process each record
            for _, row in df.iterrows():
                stats['processed'] += 1
                
                # Transform legacy data
                transformed_data = self.transform_legacy_data(row.to_dict())
                if not transformed_data:
                    stats['failed'] += 1
                    continue
                
                # Validate data integrity
                if not self.validate_data_integrity(transformed_data):
                    stats['failed'] += 1
                    continue
                
                # Add to batch
                batch_data.append(transformed_data)
                
                # Process batch when full
                if len(batch_data) >= batch_size:
                    success = await self._process_batch(batch_data, target_storage)
                    if success:
                        stats['migrated'] += len(batch_data)
                    else:
                        stats['failed'] += len(batch_data)
                    batch_data = []
            
            # Process remaining batch
            if batch_data:
                success = await self._process_batch(batch_data, target_storage)
                if success:
                    stats['migrated'] += len(batch_data)
                else:
                    stats['failed'] += len(batch_data)
            
            logger.info(f"Completed processing {file_path}: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to process file {file_path}: {e}")
            stats['failed'] = stats['processed'] - stats['migrated']
            return stats
    
    async def _process_batch(self, batch_data: List[Dict[str, Any]], target_storage: str) -> bool:
        """Process a batch of data for migration"""
        try:
            if target_storage == 'redis':
                # Migrate to Redis (individual records)
                tasks = [self.migrate_to_redis(data) for data in batch_data]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                return all(isinstance(r, bool) and r for r in results)
                
            elif target_storage == 'clickhouse':
                # Migrate to ClickHouse (batch)
                return await self.migrate_to_clickhouse(batch_data)
                
            elif target_storage == 's3':
                # Migrate to S3 (batch by date)
                date_groups = {}
                for data in batch_data:
                    date = pd.to_datetime(data['timestamp']).strftime('%Y-%m-%d')
                    if date not in date_groups:
                        date_groups[date] = []
                    date_groups[date].append(data)
                
                tasks = [self.migrate_to_s3(data_list, date) for date, data_list in date_groups.items()]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                return all(isinstance(r, bool) and r for r in results)
                
            return False
            
        except Exception as e:
            logger.error(f"Failed to process batch: {e}")
            return False
    
    async def run_migration(self, source_path: str, target_storage: str):
        """Run the complete data migration process"""
        self.migration_stats['start_time'] = datetime.now()
        logger.info(f"Starting data migration from {source_path} to {target_storage}")
        
        try:
            # Initialize connections
            await self.initialize_connections()
            
            # Get list of files to process
            source_path_obj = Path(source_path)
            if source_path_obj.is_file():
                files = [str(source_path_obj)]
            elif source_path_obj.is_dir():
                files = [str(f) for f in source_path_obj.rglob('*') if f.is_file()]
            else:
                logger.error(f"Invalid source path: {source_path}")
                return
            
            logger.info(f"Found {len(files)} files to process")
            
            # Process files concurrently
            max_workers = self.config.get('max_workers', 4)
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all file processing tasks
                future_to_file = {
                    executor.submit(asyncio.run, self.process_file(file_path, target_storage)): file_path
                    for file_path in files
                }
                
                # Process completed tasks
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        stats = future.result()
                        self.migration_stats['total_records'] += stats['processed']
                        self.migration_stats['migrated_records'] += stats['migrated']
                        self.migration_stats['failed_records'] += stats['failed']
                        
                        logger.info(f"Completed {file_path}: {stats}")
                        
                    except Exception as e:
                        logger.error(f"Failed to process {file_path}: {e}")
            
            self.migration_stats['end_time'] = datetime.now()
            self._print_migration_summary()
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise
    
    def _print_migration_summary(self):
        """Print migration summary statistics"""
        stats = self.migration_stats
        duration = stats['end_time'] - stats['start_time']
        
        print("\n" + "="*60)
        print("DATA MIGRATION SUMMARY")
        print("="*60)
        print(f"Start Time: {stats['start_time']}")
        print(f"End Time: {stats['end_time']}")
        print(f"Duration: {duration}")
        print(f"Total Records: {stats['total_records']:,}")
        print(f"Migrated Records: {stats['migrated_records']:,}")
        print(f"Failed Records: {stats['failed_records']:,}")
        print(f"Success Rate: {(stats['migrated_records']/stats['total_records']*100):.2f}%")
        print(f"Throughput: {stats['migrated_records']/duration.total_seconds():.2f} records/second")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description='Financial Data Service Migration Tool')
    parser.add_argument('--config', required=True, help='Configuration file path')
    parser.add_argument('--source', required=True, help='Source data path (file or directory)')
    parser.add_argument('--target', required=True, choices=['redis', 'clickhouse', 's3'], 
                       help='Target storage system')
    parser.add_argument('--dry-run', action='store_true', help='Perform dry run without actual migration')
    
    args = parser.parse_args()
    
    # Create migration tool instance
    migration_tool = DataMigrationTool(args.config)
    
    if args.dry_run:
        logger.info("DRY RUN MODE - No actual migration will be performed")
        # Add dry run logic here
        return
    
    # Run migration
    try:
        asyncio.run(migration_tool.run_migration(args.source, args.target))
        logger.info("Migration completed successfully")
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()