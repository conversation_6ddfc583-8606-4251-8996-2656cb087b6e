"""
PyTDX采集器数据质量控制集成测试

测试PyTDX采集器与数据质量管理系统的集成，验证：
- 历史数据归档器的质量控制功能
- 数据验证、去重和完整性校验的集成
- 质量报告和统计信息的生成
"""

import asyncio
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from collectors.pytdx_collector import (
    PyTDXCollector,
    PyTDXConfig,
    ArchiverConfig,
    HistoricalDataArchiver,
    MockStorageManager
)

from collectors.data_quality_manager import DataQualityManager


class TestHistoricalDataArchiverQuality:
    """测试历史数据归档器的质量控制功能"""
    
    @pytest.fixture
    def archiver_config(self):
        """创建归档器配置"""
        return ArchiverConfig(
            enable_data_validation=True,
            enable_deduplication=True,
            archive_batch_size=100,
            max_retry_attempts=2,
            price_change_threshold=0.5,
            volume_threshold=0
        )
    
    @pytest.fixture
    def mock_storage_manager(self):
        """创建模拟存储管理器"""
        return MockStorageManager()
    
    @pytest.fixture
    def archiver(self, archiver_config, mock_storage_manager):
        """创建历史数据归档器"""
        return HistoricalDataArchiver(archiver_config, mock_storage_manager)
    
    @pytest.fixture
    def sample_k_data(self):
        """创建样本K线数据"""
        dates = pd.date_range(start='2024-01-01', periods=10, freq='D')
        data = {
            'open': np.random.uniform(10, 20, 10),
            'high': np.random.uniform(15, 25, 10),
            'low': np.random.uniform(8, 15, 10),
            'close': np.random.uniform(10, 20, 10),
            'volume': np.random.randint(1000, 10000, 10),
            'amount': np.random.uniform(10000, 100000, 10)
        }
        
        # 确保OHLC关系正确
        for i in range(10):
            data['high'][i] = max(data['open'][i], data['high'][i], data['low'][i], data['close'][i])
            data['low'][i] = min(data['open'][i], data['high'][i], data['low'][i], data['close'][i])
        
        return pd.DataFrame(data, index=dates)
    
    @pytest.fixture
    def invalid_k_data(self):
        """创建包含无效数据的K线数据"""
        dates = pd.date_range(start='2024-01-01', periods=10, freq='D')
        data = {
            'open': [10.0, -5.0, 11.0, 0.0, 11.2, 10.9, 11.5, 11.1, 10.7, 11.3],  # 包含负价格和零价格
            'high': [10.8, 11.2, 11.5, 11.3, 11.8, 11.4, 12.0, 11.6, 11.2, 11.9],
            'low': [12.0, 10.2, 10.7, 10.5, 10.9, 10.6, 11.2, 10.8, 10.4, 11.0],  # 部分low > high
            'close': [10.5, 11.0, 10.8, 11.2, 10.9, 11.5, 11.1, 10.7, 11.3, 11.6],
            'volume': [1000, -500, 800, 1500, 900, 1100, 1300, 950, 1050, 1250],  # 包含负成交量
            'amount': [10000, 55000, 8000, 15000, 9000, 11000, 13000, 9500, 10500, 12500]
        }
        return pd.DataFrame(data, index=dates)
    
    @pytest.fixture
    def duplicate_k_data(self):
        """创建包含重复数据的K线数据"""
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        # 创建重复的数据
        duplicate_dates = list(dates) + [dates[0], dates[1]]  # 重复前两个日期
        
        data = {
            'open': [10.0, 10.5, 11.0, 10.8, 11.2, 10.0, 10.5],  # 前两个重复
            'high': [10.8, 11.2, 11.5, 11.3, 11.8, 10.8, 11.2],
            'low': [9.8, 10.2, 10.7, 10.5, 10.9, 9.8, 10.2],
            'close': [10.5, 11.0, 10.8, 11.2, 10.9, 10.5, 11.0],
            'volume': [1000, 1200, 800, 1500, 900, 1000, 1200],
            'amount': [10000, 12000, 8000, 15000, 9000, 10000, 12000]
        }
        return pd.DataFrame(data, index=duplicate_dates)
    
    @pytest.fixture
    def sample_tick_data(self):
        """创建样本tick数据"""
        times = pd.date_range(start='2024-01-01 09:30:00', periods=100, freq='1S')
        data = {
            'price': np.random.uniform(10, 20, 100),
            'volume': np.random.randint(100, 1000, 100),
            'direction': np.random.choice([0, 1, 2], 100)
        }
        return pd.DataFrame(data, index=times)
    
    @pytest.mark.asyncio
    async def test_archive_k_data_with_quality_control(self, archiver, sample_k_data):
        """测试K线数据归档的质量控制"""
        symbol = 'TEST001'
        
        # 执行归档
        success = await archiver.archive_k_data(symbol, sample_k_data)
        
        assert success, "Archive should be successful"
        
        # 检查统计信息
        stats = archiver.get_stats()
        assert stats['successful_archives'] == 1
        assert stats['total_archived'] > 0
        assert stats['quality_control_enabled'] is True
        
        # 检查存储管理器是否接收到数据
        stored_data = archiver.storage_manager.get_stored_data()
        assert len(stored_data) > 0, "Data should be stored"
        
        # 验证存储的数据格式
        first_tick = stored_data[0]
        assert 'symbol' in first_tick
        assert 'last_price' in first_tick
        assert 'timestamp_ns' in first_tick
        assert first_tick['symbol'] == symbol
    
    @pytest.mark.asyncio
    async def test_archive_invalid_k_data(self, archiver, invalid_k_data):
        """测试归档无效K线数据"""
        symbol = 'TEST_INVALID'
        
        # 执行归档
        success = await archiver.archive_k_data(symbol, invalid_k_data)
        
        # 由于数据质量控制，部分无效数据应该被过滤
        # 但只要有部分有效数据，归档就应该成功
        stats = archiver.get_stats()
        
        # 检查是否有质量报告
        quality_reports = archiver.get_quality_reports(symbol)
        assert len(quality_reports) > 0, "Quality reports should be generated"
        
        quality_report = quality_reports[0]
        assert quality_report['symbol'] == symbol
        assert quality_report['original_count'] == len(invalid_k_data)
        assert quality_report['final_count'] < quality_report['original_count'], "Invalid data should be filtered"
        assert len(quality_report['validation_metrics']['quality_issues']) > 0, "Quality issues should be detected"
    
    @pytest.mark.asyncio
    async def test_archive_duplicate_k_data(self, archiver, duplicate_k_data):
        """测试归档重复K线数据"""
        symbol = 'TEST_DUPLICATE'
        
        # 执行归档
        success = await archiver.archive_k_data(symbol, duplicate_k_data)
        
        assert success, "Archive should be successful"
        
        # 检查去重统计
        stats = archiver.get_stats()
        assert stats['deduplication_removed'] > 0, "Duplicates should be removed"
        
        # 检查质量报告
        quality_reports = archiver.get_quality_reports(symbol)
        assert len(quality_reports) > 0
        
        quality_report = quality_reports[0]
        # Check that some records were removed (either by validation or deduplication)
        assert quality_report['final_count'] < quality_report['original_count'], "Final count should be less than original"
        
        # Check that duplicate issues were detected
        validation_metrics = quality_report.get('validation_metrics', {})
        quality_issues = validation_metrics.get('quality_issues', [])
        assert any('duplicate' in issue.lower() for issue in quality_issues), "Duplicate issues should be detected"
    
    @pytest.mark.asyncio
    async def test_archive_tick_data_with_quality_control(self, archiver, sample_tick_data):
        """测试tick数据归档的质量控制"""
        symbol = 'TEST_TICK'
        
        # 执行归档
        success = await archiver.archive_tick_data(symbol, sample_tick_data)
        
        assert success, "Archive should be successful"
        
        # 检查统计信息
        stats = archiver.get_stats()
        assert stats['successful_archives'] == 1
        assert stats['total_archived'] > 0
        
        # 检查质量报告
        quality_reports = archiver.get_quality_reports(symbol)
        assert len(quality_reports) > 0
        
        quality_report = quality_reports[0]
        assert quality_report['data_type'] == 'tick_data'
        assert quality_report['status'] == 'success'
        assert 'quality_score' in quality_report
    
    @pytest.mark.asyncio
    async def test_archive_empty_data(self, archiver):
        """测试归档空数据"""
        symbol = 'TEST_EMPTY'
        empty_data = pd.DataFrame()
        
        # 执行归档
        success = await archiver.archive_k_data(symbol, empty_data)
        
        assert success, "Empty data archive should return True"
        
        # 检查统计信息
        stats = archiver.get_stats()
        # 空数据不应该增加成功归档计数
        assert stats['successful_archives'] == 0
    
    def test_quality_reports_management(self, archiver, sample_k_data):
        """测试质量报告管理"""
        # 初始状态应该没有报告
        reports = archiver.get_quality_reports()
        assert len(reports) == 0
        
        # 执行一些归档操作后再测试
        # 这里我们直接测试报告管理功能
        
        # 测试按symbol过滤
        reports_filtered = archiver.get_quality_reports('TEST001')
        assert isinstance(reports_filtered, list)
        
        # 测试限制数量
        reports_limited = archiver.get_quality_reports(limit=5)
        assert len(reports_limited) <= 5
    
    def test_quality_summary_generation(self, archiver):
        """测试质量摘要生成"""
        summary = archiver.generate_quality_summary()
        
        assert isinstance(summary, dict)
        assert 'symbol' in summary
        assert 'generated_at' in summary
        
        # 测试特定symbol的摘要
        symbol_summary = archiver.generate_quality_summary('TEST001')
        assert symbol_summary['symbol'] == 'TEST001'
    
    def test_statistics_reset(self, archiver):
        """测试统计信息重置"""
        # 获取初始统计信息
        initial_stats = archiver.get_stats()
        
        # 重置统计信息
        archiver.reset_stats()
        
        # 检查重置后的统计信息
        reset_stats = archiver.get_stats()
        assert reset_stats['total_archived'] == 0
        assert reset_stats['successful_archives'] == 0
        assert reset_stats['failed_archives'] == 0
        assert reset_stats['validation_errors'] == 0
        assert reset_stats['deduplication_removed'] == 0
        assert reset_stats['last_archive_time'] is None


class TestPyTDXCollectorQualityIntegration:
    """测试PyTDX采集器与质量控制的集成"""
    
    @pytest.fixture
    def pytdx_config(self):
        """创建PyTDX配置"""
        archiver_config = ArchiverConfig(
            enable_data_validation=True,
            enable_deduplication=True,
            archive_batch_size=100,
            max_retry_attempts=2
        )
        
        return PyTDXConfig(
            batch_size=50,
            concurrent_requests=2,
            archive_enabled=True,
            archiver_config=archiver_config
        )
    
    @pytest.fixture
    def mock_storage_manager(self):
        """创建模拟存储管理器"""
        return MockStorageManager()
    
    @pytest.fixture
    def pytdx_collector(self, pytdx_config, mock_storage_manager):
        """创建PyTDX采集器"""
        return PyTDXCollector(pytdx_config, mock_storage_manager)
    
    def test_collector_initialization_with_quality_control(self, pytdx_collector):
        """测试采集器初始化时的质量控制设置"""
        assert pytdx_collector.archiver is not None, "Archiver should be initialized"
        assert hasattr(pytdx_collector.archiver, 'quality_manager'), "Quality manager should be available"
        
        # 检查质量管理器配置
        quality_manager = pytdx_collector.archiver.quality_manager
        assert isinstance(quality_manager, DataQualityManager)
        
        # 检查统计信息
        stats = pytdx_collector.archiver.get_stats()
        assert stats['quality_control_enabled'] is True
    
    def test_archiver_quality_features_access(self, pytdx_collector):
        """测试通过采集器访问质量控制功能"""
        archiver = pytdx_collector.archiver
        
        # 测试获取质量报告
        reports = archiver.get_quality_reports()
        assert isinstance(reports, list)
        
        # 测试生成质量摘要
        summary = archiver.generate_quality_summary()
        assert isinstance(summary, dict)
        
        # 测试获取统计信息
        stats = archiver.get_stats()
        assert 'quality_statistics' in stats or 'quality_control_enabled' in stats
    
    @pytest.mark.asyncio
    async def test_end_to_end_quality_control(self, pytdx_collector):
        """测试端到端的质量控制流程"""
        # 创建测试数据
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        test_data = pd.DataFrame({
            'open': [10.0, 10.5, 11.0, 10.8, 11.2],
            'high': [10.8, 11.2, 11.5, 11.3, 11.8],
            'low': [9.8, 10.2, 10.7, 10.5, 10.9],
            'close': [10.5, 11.0, 10.8, 11.2, 10.9],
            'volume': [1000, 1200, 800, 1500, 900],
            'amount': [10000, 12000, 8000, 15000, 9000]
        }, index=dates)
        
        # 执行归档
        success = await pytdx_collector.archiver.archive_k_data('TEST_E2E', test_data)
        
        assert success, "End-to-end archive should be successful"
        
        # 检查质量报告
        quality_reports = pytdx_collector.archiver.get_quality_reports('TEST_E2E')
        assert len(quality_reports) > 0, "Quality reports should be generated"
        
        quality_report = quality_reports[0]
        assert quality_report['status'] == 'success'
        assert quality_report['symbol'] == 'TEST_E2E'
        assert 'quality_score' in quality_report
        assert quality_report['quality_score'] > 0.8, "Quality score should be high for good data"
        
        # 检查存储的数据
        stored_data = pytdx_collector.storage_manager.get_stored_data()
        assert len(stored_data) > 0, "Data should be stored"
        
        # 验证数据格式
        for tick in stored_data:
            assert tick['symbol'] == 'TEST_E2E'
            assert tick['last_price'] > 0
            assert tick['timestamp_ns'] > 0
    
    def test_backward_compatibility(self, pytdx_collector):
        """测试向后兼容性"""
        archiver = pytdx_collector.archiver
        
        # 确保旧的接口仍然可用
        assert hasattr(archiver, 'data_validator'), "Old validator should still be available"
        assert hasattr(archiver, 'deduplicator'), "Old deduplicator should still be available"
        
        # 确保新的接口也可用
        assert hasattr(archiver, 'quality_manager'), "New quality manager should be available"
    
    def test_configuration_impact(self):
        """测试配置对质量控制的影响"""
        # 测试禁用质量控制的配置
        disabled_config = ArchiverConfig(
            enable_data_validation=False,
            enable_deduplication=False
        )
        
        pytdx_config = PyTDXConfig(
            archive_enabled=True,
            archiver_config=disabled_config
        )
        
        collector = PyTDXCollector(pytdx_config, MockStorageManager())
        
        # 即使禁用了质量控制，质量管理器仍应该存在，但不会被使用
        assert collector.archiver is not None
        assert hasattr(collector.archiver, 'quality_manager')
        
        # 检查配置是否正确传递
        assert collector.archiver.config.enable_data_validation is False
        assert collector.archiver.config.enable_deduplication is False


class TestQualityControlPerformance:
    """测试质量控制的性能"""
    
    @pytest.fixture
    def large_dataset(self):
        """创建大数据集用于性能测试"""
        dates = pd.date_range(start='2024-01-01', periods=1000, freq='1min')
        data = {
            'open': np.random.uniform(10, 20, 1000),
            'high': np.random.uniform(15, 25, 1000),
            'low': np.random.uniform(8, 15, 1000),
            'close': np.random.uniform(10, 20, 1000),
            'volume': np.random.randint(100, 10000, 1000),
            'amount': np.random.uniform(1000, 100000, 1000)
        }
        
        # 确保OHLC关系正确
        for i in range(1000):
            data['high'][i] = max(data['open'][i], data['high'][i], data['low'][i], data['close'][i])
            data['low'][i] = min(data['open'][i], data['high'][i], data['low'][i], data['close'][i])
        
        return pd.DataFrame(data, index=dates)
    
    @pytest.mark.asyncio
    async def test_large_dataset_processing_performance(self, large_dataset):
        """测试大数据集处理性能"""
        config = ArchiverConfig(
            enable_data_validation=True,
            enable_deduplication=True,
            archive_batch_size=500  # 较大的批次大小
        )
        
        archiver = HistoricalDataArchiver(config, MockStorageManager())
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 执行归档
        success = await archiver.archive_k_data('PERF_TEST', large_dataset)
        
        # 记录结束时间
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        assert success, "Large dataset processing should be successful"
        assert processing_time < 10.0, f"Processing should complete within 10 seconds, took {processing_time:.2f}s"
        
        # 检查处理结果
        stats = archiver.get_stats()
        assert stats['total_archived'] > 0, "Data should be archived"
        
        # 检查质量报告
        quality_reports = archiver.get_quality_reports('PERF_TEST')
        assert len(quality_reports) > 0, "Quality reports should be generated"
        
        quality_report = quality_reports[0]
        assert quality_report['final_count'] > 0, "Some data should pass quality control"
        
        print(f"Processed {len(large_dataset)} records in {processing_time:.2f} seconds")
        print(f"Quality score: {quality_report.get('quality_score', 'N/A')}")
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self):
        """测试并发处理性能"""
        config = ArchiverConfig(
            enable_data_validation=True,
            enable_deduplication=True,
            archive_batch_size=100
        )
        
        archiver = HistoricalDataArchiver(config, MockStorageManager())
        
        # 创建多个数据集
        datasets = []
        for i in range(5):
            dates = pd.date_range(start=f'2024-01-{i+1:02d}', periods=100, freq='1min')
            data = {
                'open': np.random.uniform(10, 20, 100),
                'high': np.random.uniform(15, 25, 100),
                'low': np.random.uniform(8, 15, 100),
                'close': np.random.uniform(10, 20, 100),
                'volume': np.random.randint(100, 1000, 100)
            }
            
            # 确保OHLC关系正确
            for j in range(100):
                data['high'][j] = max(data['open'][j], data['high'][j], data['low'][j], data['close'][j])
                data['low'][j] = min(data['open'][j], data['high'][j], data['low'][j], data['close'][j])
            
            datasets.append((f'CONCURRENT_TEST_{i}', pd.DataFrame(data, index=dates)))
        
        # 并发处理
        start_time = datetime.now()
        
        tasks = [
            archiver.archive_k_data(symbol, data)
            for symbol, data in datasets
        ]
        
        results = await asyncio.gather(*tasks)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # 检查结果
        assert all(results), "All concurrent processing should be successful"
        assert processing_time < 5.0, f"Concurrent processing should complete within 5 seconds, took {processing_time:.2f}s"
        
        # 检查统计信息
        stats = archiver.get_stats()
        assert stats['successful_archives'] == 5, "All 5 archives should be successful"
        
        print(f"Processed 5 datasets concurrently in {processing_time:.2f} seconds")


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])