#include "config_manager.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <regex>
#include <openssl/md5.h>
#include <iomanip>
#include <cstdlib>

namespace config {

ConfigManager& ConfigManager::Instance() {
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::Initialize(const std::string& config_file_path) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    config_file_path_ = config_file_path;
    
    // 重置统计信息
    {
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        stats_ = Statistics{};
    }
    
    return LoadConfig();
}

void ConfigManager::Shutdown() {
    StopFileWatcher();
    
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    // 清理监听器
    {
        std::lock_guard<std::mutex> listeners_lock(listeners_mutex_);
        listeners_.clear();
    }
    
    // 清理验证器
    {
        std::lock_guard<std::mutex> validators_lock(validators_mutex_);
        validators_.clear();
    }
    
    // 清理版本历史
    {
        std::lock_guard<std::mutex> version_lock(version_mutex_);
        version_history_.clear();
    }
}

bool ConfigManager::LoadConfig() {
    return LoadFromFile(config_file_path_);
}

bool ConfigManager::SaveConfig() {
    return SaveToFile(config_file_path_);
}

bool ConfigManager::LoadFromFile(const std::string& file_path) {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        nlohmann::json new_config;
        file >> new_config;
        file.close();
        
        // 处理环境变量
        if (env_vars_enabled_) {
            new_config = ProcessEnvironmentVariables(new_config);
        }
        
        // 验证配置
        auto validation_result = ValidateConfig();
        if (!validation_result.is_valid) {
            // 记录验证错误但仍然加载配置
            std::lock_guard<std::mutex> stats_lock(stats_mutex_);
            stats_.error_count++;
        }
        
        {
            std::unique_lock<std::shared_mutex> lock(config_mutex_);
            config_ = std::move(new_config);
            
            // 更新文件时间戳
            if (std::filesystem::exists(file_path)) {
                last_file_time_ = std::filesystem::last_write_time(file_path);
            }
        }
        
        // 更新统计信息
        {
            std::lock_guard<std::mutex> stats_lock(stats_mutex_);
            stats_.last_loaded = std::chrono::system_clock::now();
            stats_.total_keys = CountKeys(config_);
            stats_.total_sections = CountSections(config_);
        }
        
        // 通知监听器
        ConfigChangeEvent event;
        event.type = ConfigChangeType::RELOADED;
        event.timestamp = std::chrono::system_clock::now();
        NotifyListeners(event);
        
        return true;
    } catch (const std::exception&) {
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        stats_.error_count++;
        return false;
    }
}

bool ConfigManager::SaveToFile(const std::string& file_path) {
    try {
        std::shared_lock<std::shared_mutex> lock(config_mutex_);
        
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        file << config_.dump(4);  // 4空格缩进
        file.close();
        
        return true;
    } catch (const std::exception&) {
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        stats_.error_count++;
        return false;
    }
}

bool ConfigManager::HasKey(const std::string& key) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    
    try {
        nlohmann::json current = config_;
        std::istringstream iss(key);
        std::string token;
        
        while (std::getline(iss, token, '.')) {
            if (current.contains(token)) {
                current = current[token];
            } else {
                return false;
            }
        }
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool ConfigManager::RemoveKey(const std::string& key) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    try {
        nlohmann::json* current = &config_;
        std::istringstream iss(key);
        std::string token;
        std::vector<std::string> path_tokens;
        
        while (std::getline(iss, token, '.')) {
            path_tokens.push_back(token);
        }
        
        // 导航到父节点
        for (size_t i = 0; i < path_tokens.size() - 1; ++i) {
            if (!current->contains(path_tokens[i])) {
                return false;
            }
            current = &(*current)[path_tokens[i]];
        }
        
        const std::string& final_key = path_tokens.back();
        if (!current->contains(final_key)) {
            return false;
        }
        
        nlohmann::json old_value = (*current)[final_key];
        current->erase(final_key);
        
        // 通知监听器
        ConfigChangeEvent event;
        event.type = ConfigChangeType::DELETED;
        event.key = key;
        event.old_value = old_value;
        event.timestamp = std::chrono::system_clock::now();
        
        lock.unlock();
        NotifyListeners(event);
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

nlohmann::json ConfigManager::GetSection(const std::string& section) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    
    if (config_.contains(section)) {
        return config_[section];
    }
    
    return nlohmann::json::object();
}

bool ConfigManager::SetSection(const std::string& section, const nlohmann::json& config) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    try {
        nlohmann::json old_value;
        if (config_.contains(section)) {
            old_value = config_[section];
        }
        
        config_[section] = config;
        
        // 通知监听器
        ConfigChangeEvent event;
        event.type = old_value.is_null() ? ConfigChangeType::ADDED : ConfigChangeType::MODIFIED;
        event.section = section;
        event.old_value = old_value;
        event.new_value = config;
        event.timestamp = std::chrono::system_clock::now();
        
        lock.unlock();
        NotifyListeners(event);
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool ConfigManager::RemoveSection(const std::string& section) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    if (!config_.contains(section)) {
        return false;
    }
    
    nlohmann::json old_value = config_[section];
    config_.erase(section);
    
    // 通知监听器
    ConfigChangeEvent event;
    event.type = ConfigChangeType::DELETED;
    event.section = section;
    event.old_value = old_value;
    event.timestamp = std::chrono::system_clock::now();
    
    lock.unlock();
    NotifyListeners(event);
    
    return true;
}

std::vector<std::string> ConfigManager::GetSectionNames() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    
    std::vector<std::string> sections;
    for (auto it = config_.begin(); it != config_.end(); ++it) {
        if (it.value().is_object()) {
            sections.push_back(it.key());
        }
    }
    
    return sections;
}

ValidationResult ConfigManager::ValidateConfig() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    std::lock_guard<std::mutex> validators_lock(validators_mutex_);
    
    ValidationResult result;
    
    for (const auto& [section, validator] : validators_) {
        if (config_.contains(section)) {
            auto section_result = validator->Validate(config_[section]);
            
            // 合并结果
            if (!section_result.is_valid) {
                result.is_valid = false;
            }
            
            for (const auto& error : section_result.errors) {
                result.errors.push_back("[" + section + "] " + error);
            }
            
            for (const auto& warning : section_result.warnings) {
                result.warnings.push_back("[" + section + "] " + warning);
            }
        }
    }
    
    // 更新统计信息
    {
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        stats_.validation_count++;
        if (!result.is_valid) {
            stats_.error_count++;
        }
    }
    
    return result;
}

ValidationResult ConfigManager::ValidateSection(const std::string& section) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    std::lock_guard<std::mutex> validators_lock(validators_mutex_);
    
    ValidationResult result;
    
    auto it = validators_.find(section);
    if (it != validators_.end() && config_.contains(section)) {
        result = it->second->Validate(config_[section]);
    }
    
    return result;
}

bool ConfigManager::RegisterValidator(const std::string& section, 
                                    std::shared_ptr<ConfigValidator> validator) {
    std::lock_guard<std::mutex> lock(validators_mutex_);
    validators_[section] = validator;
    return true;
}

void ConfigManager::UnregisterValidator(const std::string& section) {
    std::lock_guard<std::mutex> lock(validators_mutex_);
    validators_.erase(section);
}

void ConfigManager::EnableHotReload(bool enable) {
    if (enable && !hot_reload_enabled_) {
        hot_reload_enabled_ = true;
        StartFileWatcher();
    } else if (!enable && hot_reload_enabled_) {
        hot_reload_enabled_ = false;
        StopFileWatcher();
    }
}

bool ConfigManager::IsHotReloadEnabled() const {
    return hot_reload_enabled_;
}

void ConfigManager::SetFileWatchInterval(std::chrono::milliseconds interval) {
    watch_interval_ = interval;
}

void ConfigManager::RegisterChangeListener(std::shared_ptr<ConfigChangeListener> listener) {
    std::lock_guard<std::mutex> lock(listeners_mutex_);
    listeners_.push_back(listener);
}

void ConfigManager::UnregisterChangeListener(std::shared_ptr<ConfigChangeListener> listener) {
    std::lock_guard<std::mutex> lock(listeners_mutex_);
    listeners_.erase(
        std::remove_if(listeners_.begin(), listeners_.end(),
                      [&listener](const std::weak_ptr<ConfigChangeListener>& weak_ptr) {
                          return weak_ptr.lock() == listener;
                      }),
        listeners_.end()
    );
}

std::string ConfigManager::CreateSnapshot(const std::string& description) {
    std::shared_lock<std::shared_mutex> config_lock(config_mutex_);
    std::lock_guard<std::mutex> version_lock(version_mutex_);
    
    ConfigVersion version;
    version.version_id = GenerateVersionId();
    version.timestamp = std::chrono::system_clock::now();
    version.description = description;
    version.config_snapshot = config_;
    version.checksum = CalculateChecksum(config_);
    
    version_history_.push_back(version);
    
    // 清理旧版本
    CleanupOldVersions();
    
    return version.version_id;
}

bool ConfigManager::RestoreFromSnapshot(const std::string& version_id) {
    std::lock_guard<std::mutex> version_lock(version_mutex_);
    
    auto it = std::find_if(version_history_.begin(), version_history_.end(),
                          [&version_id](const ConfigVersion& v) {
                              return v.version_id == version_id;
                          });
    
    if (it == version_history_.end()) {
        return false;
    }
    
    {
        std::unique_lock<std::shared_mutex> config_lock(config_mutex_);
        config_ = it->config_snapshot;
    }
    
    // 通知监听器
    ConfigChangeEvent event;
    event.type = ConfigChangeType::RELOADED;
    event.timestamp = std::chrono::system_clock::now();
    NotifyListeners(event);
    
    return true;
}

std::vector<ConfigVersion> ConfigManager::GetVersionHistory() const {
    std::lock_guard<std::mutex> lock(version_mutex_);
    return version_history_;
}

bool ConfigManager::DeleteSnapshot(const std::string& version_id) {
    std::lock_guard<std::mutex> lock(version_mutex_);
    
    auto it = std::find_if(version_history_.begin(), version_history_.end(),
                          [&version_id](const ConfigVersion& v) {
                              return v.version_id == version_id;
                          });
    
    if (it != version_history_.end()) {
        version_history_.erase(it);
        return true;
    }
    
    return false;
}

void ConfigManager::SetMaxVersionHistory(size_t max_versions) {
    std::lock_guard<std::mutex> lock(version_mutex_);
    max_version_history_ = max_versions;
    CleanupOldVersions();
}

bool ConfigManager::MergeConfig(const nlohmann::json& other_config, bool overwrite) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    try {
        if (overwrite) {
            config_.merge_patch(other_config);
        } else {
            // 只添加不存在的键
            for (auto it = other_config.begin(); it != other_config.end(); ++it) {
                if (!config_.contains(it.key())) {
                    config_[it.key()] = it.value();
                }
            }
        }
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool ConfigManager::MergeFromFile(const std::string& file_path, bool overwrite) {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        nlohmann::json other_config;
        file >> other_config;
        file.close();
        
        return MergeConfig(other_config, overwrite);
    } catch (const std::exception&) {
        return false;
    }
}

void ConfigManager::EnableEnvironmentVariables(bool enable) {
    env_vars_enabled_ = enable;
}

void ConfigManager::SetEnvironmentPrefix(const std::string& prefix) {
    env_prefix_ = prefix;
}

std::string ConfigManager::ExportToString(bool pretty_print) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    
    if (pretty_print) {
        return config_.dump(4);
    } else {
        return config_.dump();
    }
}

bool ConfigManager::ExportToFile(const std::string& file_path, bool pretty_print) const {
    try {
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        file << ExportToString(pretty_print);
        file.close();
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

ConfigManager::Statistics ConfigManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

// 私有方法实现
void ConfigManager::NotifyListeners(const ConfigChangeEvent& event) {
    std::lock_guard<std::mutex> lock(listeners_mutex_);
    
    for (auto it = listeners_.begin(); it != listeners_.end();) {
        if (auto listener = it->lock()) {
            try {
                listener->OnConfigChanged(event);
                ++it;
            } catch (const std::exception&) {
                // 移除出错的监听器
                it = listeners_.erase(it);
            }
        } else {
            // 移除已失效的监听器
            it = listeners_.erase(it);
        }
    }
}

void ConfigManager::StartFileWatcher() {
    if (file_watcher_running_) {
        return;
    }
    
    file_watcher_running_ = true;
    file_watcher_thread_ = std::thread(&ConfigManager::FileWatcherLoop, this);
}

void ConfigManager::StopFileWatcher() {
    if (!file_watcher_running_) {
        return;
    }
    
    file_watcher_running_ = false;
    if (file_watcher_thread_.joinable()) {
        file_watcher_thread_.join();
    }
}

void ConfigManager::FileWatcherLoop() {
    while (file_watcher_running_) {
        try {
            if (std::filesystem::exists(config_file_path_)) {
                auto current_time = std::filesystem::last_write_time(config_file_path_);
                
                if (current_time != last_file_time_) {
                    last_file_time_ = current_time;
                    LoadConfig();
                }
            }
        } catch (const std::exception&) {
            // 忽略文件系统错误
        }
        
        std::this_thread::sleep_for(watch_interval_);
    }
}

std::string ConfigManager::CalculateChecksum(const nlohmann::json& config) const {
    std::string config_str = config.dump();
    
    unsigned char hash[MD5_DIGEST_LENGTH];
    MD5(reinterpret_cast<const unsigned char*>(config_str.c_str()), 
        config_str.length(), hash);
    
    std::ostringstream oss;
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }
    
    return oss.str();
}

std::string ConfigManager::GenerateVersionId() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << "v" << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
        << "_" << std::setw(3) << std::setfill('0') << ms.count();
    
    return oss.str();
}

void ConfigManager::CleanupOldVersions() {
    while (version_history_.size() > max_version_history_) {
        version_history_.erase(version_history_.begin());
    }
}

std::string ConfigManager::ResolveEnvironmentVariables(const std::string& value) const {
    if (!env_vars_enabled_) {
        return value;
    }
    
    std::regex env_regex(R"(\$\{([^}]+)\})");
    std::string result = value;
    std::smatch match;
    
    while (std::regex_search(result, match, env_regex)) {
        std::string env_var = match[1].str();
        const char* env_value = std::getenv(env_var.c_str());
        
        if (env_value) {
            result.replace(match.position(), match.length(), env_value);
        } else {
            // 尝试使用前缀
            std::string prefixed_var = env_prefix_ + env_var;
            const char* prefixed_value = std::getenv(prefixed_var.c_str());
            
            if (prefixed_value) {
                result.replace(match.position(), match.length(), prefixed_value);
            } else {
                // 保留原始值
                break;
            }
        }
    }
    
    return result;
}

nlohmann::json ConfigManager::ProcessEnvironmentVariables(const nlohmann::json& config) const {
    if (!env_vars_enabled_) {
        return config;
    }
    
    nlohmann::json result = config;
    
    std::function<void(nlohmann::json&)> process_node = [&](nlohmann::json& node) {
        if (node.is_string()) {
            std::string str_value = node.get<std::string>();
            node = ResolveEnvironmentVariables(str_value);
        } else if (node.is_object()) {
            for (auto& [key, value] : node.items()) {
                process_node(value);
            }
        } else if (node.is_array()) {
            for (auto& item : node) {
                process_node(item);
            }
        }
    };
    
    process_node(result);
    return result;
}

size_t ConfigManager::CountKeys(const nlohmann::json& config) const {
    size_t count = 0;
    
    std::function<void(const nlohmann::json&)> count_keys = [&](const nlohmann::json& node) {
        if (node.is_object()) {
            count += node.size();
            for (const auto& [key, value] : node.items()) {
                if (value.is_object()) {
                    count_keys(value);
                }
            }
        }
    };
    
    count_keys(config);
    return count;
}

size_t ConfigManager::CountSections(const nlohmann::json& config) const {
    size_t count = 0;
    
    for (const auto& [key, value] : config.items()) {
        if (value.is_object()) {
            count++;
        }
    }
    
    return count;
}

} // namespace config