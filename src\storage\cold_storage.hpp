#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <future>
#include <aws/s3/S3Client.h>
#include <minio/client.h>
#include <arrow/api.h>
#include <parquet/arrow/writer.h>
#include <parquet/arrow/reader.h>

namespace financial_data {
namespace storage {

struct ColdStorageConfig {
    // MinIO配置
    std::string minio_endpoint;
    std::string minio_access_key;
    std::string minio_secret_key;
    std::string minio_bucket;
    bool minio_secure = true;
    
    // AWS S3配置
    std::string s3_region;
    std::string s3_access_key;
    std::string s3_secret_key;
    std::string s3_bucket;
    
    // 数据生命周期配置
    int archive_threshold_days = 730; // 2年
    int compression_level = 9;
    size_t batch_size = 100000;
    
    // 存储路径模板
    std::string path_template = "{exchange}/{symbol}/{year}/{month}/{day}";
};

struct TickDataBatch {
    std::vector<int64_t> timestamps;
    std::vector<std::string> symbols;
    std::vector<std::string> exchanges;
    std::vector<double> last_prices;
    std::vector<int64_t> volumes;
    std::vector<double> turnovers;
    std::vector<int64_t> open_interests;
    std::vector<std::vector<double>> bid_prices;
    std::vector<std::vector<int32_t>> bid_volumes;
    std::vector<std::vector<double>> ask_prices;
    std::vector<std::vector<int32_t>> ask_volumes;
    std::vector<uint32_t> sequences;
    
    size_t size() const { return timestamps.size(); }
    void clear();
    void reserve(size_t capacity);
};

struct ArchiveMetadata {
    std::string file_path;
    std::string symbol;
    std::string exchange;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    size_t record_count;
    size_t compressed_size;
    size_t original_size;
    double compression_ratio;
    std::string checksum;
    bool s3_backup_completed = false;
};

class ColdDataStorage {
public:
    explicit ColdDataStorage(const ColdStorageConfig& config);
    ~ColdDataStorage();
    
    // 初始化存储系统
    bool Initialize();
    
    // 数据归档接口
    std::future<bool> ArchiveData(const TickDataBatch& batch, 
                                  const std::string& symbol,
                                  const std::string& exchange,
                                  const std::chrono::system_clock::time_point& date);
    
    // 批量归档接口
    std::future<std::vector<bool>> ArchiveBatchData(
        const std::vector<TickDataBatch>& batches,
        const std::vector<std::string>& symbols,
        const std::vector<std::string>& exchanges,
        const std::vector<std::chrono::system_clock::time_point>& dates);
    
    // 数据检索接口
    std::future<TickDataBatch> RetrieveData(const std::string& symbol,
                                            const std::string& exchange,
                                            const std::chrono::system_clock::time_point& start_time,
                                            const std::chrono::system_clock::time_point& end_time);
    
    // 大批量历史数据查询
    std::future<std::vector<TickDataBatch>> BulkRetrieveData(
        const std::vector<std::string>& symbols,
        const std::vector<std::string>& exchanges,
        const std::chrono::system_clock::time_point& start_time,
        const std::chrono::system_clock::time_point& end_time);
    
    // 数据生命周期管理
    std::future<bool> MigrateOldData(int threshold_days = 730);
    std::future<bool> BackupToS3(const std::string& file_path);
    std::future<bool> VerifyDataIntegrity(const std::string& file_path);
    
    // 存储统计信息
    struct StorageStats {
        size_t total_files;
        size_t total_records;
        size_t total_compressed_size;
        size_t total_original_size;
        double average_compression_ratio;
        size_t s3_backup_files;
    };
    
    StorageStats GetStorageStats() const;
    
    // 清理过期数据
    std::future<bool> CleanupExpiredData(int retention_years = 10);

private:
    ColdStorageConfig config_;
    std::unique_ptr<minio::s3::Client> minio_client_;
    std::unique_ptr<Aws::S3::S3Client> s3_client_;
    
    // Parquet文件操作
    bool WriteParquetFile(const TickDataBatch& batch, const std::string& file_path);
    bool ReadParquetFile(const std::string& file_path, TickDataBatch& batch);
    
    // 路径生成
    std::string GenerateFilePath(const std::string& symbol,
                                const std::string& exchange,
                                const std::chrono::system_clock::time_point& date);
    
    // 压缩和校验
    std::string CalculateChecksum(const std::string& file_path);
    double CalculateCompressionRatio(size_t original_size, size_t compressed_size);
    
    // MinIO操作
    bool UploadToMinIO(const std::string& local_path, const std::string& object_name);
    bool DownloadFromMinIO(const std::string& object_name, const std::string& local_path);
    bool DeleteFromMinIO(const std::string& object_name);
    
    // S3操作
    bool UploadToS3(const std::string& local_path, const std::string& object_key);
    bool DownloadFromS3(const std::string& object_key, const std::string& local_path);
    
    // 元数据管理
    bool SaveArchiveMetadata(const ArchiveMetadata& metadata);
    std::vector<ArchiveMetadata> LoadArchiveMetadata(const std::string& symbol,
                                                    const std::string& exchange,
                                                    const std::chrono::system_clock::time_point& start_time,
                                                    const std::chrono::system_clock::time_point& end_time);
    
    // 线程池
    class ThreadPool;
    std::unique_ptr<ThreadPool> thread_pool_;
};

// 数据生命周期管理器
class DataLifecycleManager {
public:
    explicit DataLifecycleManager(std::shared_ptr<ColdDataStorage> cold_storage);
    
    // 启动自动迁移任务
    void StartAutomaticMigration();
    void StopAutomaticMigration();
    
    // 手动触发迁移
    std::future<bool> TriggerMigration();
    
    // 设置迁移策略
    void SetMigrationPolicy(int threshold_days, const std::string& cron_schedule);
    
private:
    std::shared_ptr<ColdDataStorage> cold_storage_;
    std::atomic<bool> running_;
    std::thread migration_thread_;
    
    void MigrationWorker();
    bool ShouldMigrateData(const std::chrono::system_clock::time_point& data_time);
};

} // namespace storage
} // namespace financial_data