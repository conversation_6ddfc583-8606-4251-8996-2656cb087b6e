#!/usr/bin/env python3
"""
端到端集成测试套件
测试完整的数据采集到存储流程，验证多数据源协调工作，测试故障转移和恢复机制
"""

import asyncio
import json
import logging
import os
import sys
import time
import unittest
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import redis
import requests
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.collectors.pytdx_collector import PytdxCollector
from src.collectors.historical_data_archiver import HistoricalDataArchiver
from src.collectors.scheduled_task_manager import ScheduledTaskManager
from src.collectors.incremental_data_updater import IncrementalDataUpdater
from src.collectors.task_failure_handler import TaskFailureHandler
from src.storage.unified_data_access import UnifiedDataAccessInterface
from src.storage.storage_layer_selector import StorageLayerSelector
from src.config.config_manager_python import ConfigManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EndToEndIntegrationTest(unittest.TestCase):
    """端到端集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.test_config = {
            "pytdx": {
                "servers": [
                    {"host": "**************", "port": 7709},
                    {"host": "************", "port": 7709}
                ],
                "timeout": 10,
                "retry_count": 3,
                "batch_size": 1000
            },
            "storage": {
                "redis": {
                    "host": "localhost",
                    "port": 6379,
                    "db": 1,  # 使用测试数据库
                    "password": None
                },
                "clickhouse": {
                    "host": "localhost",
                    "port": 8123,
                    "database": "test_market_data",
                    "user": "default",
                    "password": ""
                }
            },
            "archiver": {
                "batch_size": 5000,
                "validation_enabled": True,
                "deduplication_enabled": True,
                "storage_timeout": 30
            },
            "scheduler": {
                "max_concurrent_tasks": 3,
                "task_timeout": 300,
                "retry_delay": 60
            }
        }
        
        # 初始化组件
        cls.config_manager = ConfigManager()
        cls.config_manager.load_from_dict(cls.test_config)
        
        cls.pytdx_collector = PytdxCollector(cls.test_config["pytdx"])
        cls.archiver = HistoricalDataArchiver(cls.test_config["archiver"])
        cls.scheduler = ScheduledTaskManager(cls.test_config["scheduler"])
        cls.updater = IncrementalDataUpdater(cls.test_config)
        cls.failure_handler = TaskFailureHandler()
        cls.unified_access = UnifiedDataAccessInterface(cls.test_config["storage"])
        cls.storage_selector = StorageLayerSelector(cls.test_config["storage"])
        
        # 测试数据
        cls.test_symbols = ["000001", "000002", "600000"]
        cls.test_start_date = "2024-01-01"
        cls.test_end_date = "2024-01-31"
        
        logger.info("End-to-end integration test setup completed")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 清理测试数据
        try:
            # 清理Redis测试数据
            redis_client = redis.Redis(
                host=cls.test_config["storage"]["redis"]["host"],
                port=cls.test_config["storage"]["redis"]["port"],
                db=cls.test_config["storage"]["redis"]["db"]
            )
            redis_client.flushdb()
            
            # 停止调度器
            if hasattr(cls.scheduler, 'stop'):
                cls.scheduler.stop()
                
        except Exception as e:
            logger.warning(f"Cleanup warning: {e}")
        
        logger.info("End-to-end integration test cleanup completed")
    
    def setUp(self):
        """每个测试方法的初始化"""
        self.test_start_time = time.time()
        logger.info(f"Starting test: {self._testMethodName}")
    
    def tearDown(self):
        """每个测试方法的清理"""
        test_duration = time.time() - self.test_start_time
        logger.info(f"Test {self._testMethodName} completed in {test_duration:.2f}s")
    
    def test_01_complete_data_collection_flow(self):
        """测试完整的数据采集到存储流程"""
        logger.info("Testing complete data collection flow")
        
        # 1. 测试pytdx数据采集
        symbol = self.test_symbols[0]
        
        # 获取历史K线数据
        k_data = self.pytdx_collector.get_k_data(
            symbol, 
            start_date=self.test_start_date,
            end_date=self.test_end_date
        )
        
        self.assertIsNotNone(k_data, "K线数据采集失败")
        self.assertGreater(len(k_data), 0, "K线数据为空")
        logger.info(f"Successfully collected {len(k_data)} K-line records for {symbol}")
        
        # 2. 测试数据归档
        archive_result = asyncio.run(
            self.archiver.archive_k_data(symbol, k_data)
        )
        
        self.assertTrue(archive_result, "数据归档失败")
        logger.info(f"Successfully archived K-line data for {symbol}")
        
        # 3. 测试统一数据访问
        query_request = {
            "symbol": symbol,
            "data_type": "kline",
            "start_timestamp": int(datetime.strptime(self.test_start_date, "%Y-%m-%d").timestamp() * 1000),
            "end_timestamp": int(datetime.strptime(self.test_end_date, "%Y-%m-%d").timestamp() * 1000),
            "limit": 100
        }
        
        query_result = asyncio.run(
            self.unified_access.query_data(query_request)
        )
        
        self.assertIsNotNone(query_result, "统一数据访问查询失败")
        self.assertGreater(len(query_result.get("data", [])), 0, "查询结果为空")
        logger.info(f"Successfully queried {len(query_result.get('data', []))} records via unified access")
        
        # 4. 验证数据完整性
        original_count = len(k_data)
        stored_count = len(query_result.get("data", []))
        
        # 允许一定的数据差异（由于去重等处理）
        self.assertGreaterEqual(stored_count, original_count * 0.9, 
                               f"存储数据量过少: {stored_count} < {original_count * 0.9}")
        
        logger.info("Complete data collection flow test passed")
    
    def test_02_multi_source_coordination(self):
        """测试多数据源协调工作"""
        logger.info("Testing multi-source coordination")
        
        # 模拟多个数据源的数据采集
        symbols = self.test_symbols[:2]
        
        # 并发采集多个股票的数据
        async def collect_multiple_symbols():
            tasks = []
            for symbol in symbols:
                # 创建采集任务
                task = asyncio.create_task(
                    self._collect_and_archive_symbol(symbol)
                )
                tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return results
        
        results = asyncio.run(collect_multiple_symbols())
        
        # 验证所有采集任务都成功
        success_count = sum(1 for result in results if result is True)
        self.assertEqual(success_count, len(symbols), 
                        f"多数据源协调失败: {success_count}/{len(symbols)} 成功")
        
        logger.info(f"Multi-source coordination test passed: {success_count}/{len(symbols)} sources successful")
    
    async def _collect_and_archive_symbol(self, symbol: str) -> bool:
        """采集并归档单个股票数据"""
        try:
            # 采集数据
            k_data = self.pytdx_collector.get_k_data(
                symbol,
                start_date=self.test_start_date,
                end_date=self.test_end_date
            )
            
            if k_data is None or len(k_data) == 0:
                return False
            
            # 归档数据
            result = await self.archiver.archive_k_data(symbol, k_data)
            return result
            
        except Exception as e:
            logger.error(f"Failed to collect and archive {symbol}: {e}")
            return False
    
    def test_03_failover_and_recovery(self):
        """测试故障转移和恢复机制"""
        logger.info("Testing failover and recovery mechanisms")
        
        # 1. 测试任务失败处理
        symbol = self.test_symbols[0]
        
        # 模拟失败的任务
        def failing_task():
            raise Exception("Simulated task failure")
        
        # 测试失败处理机制
        task_id = f"test_task_{int(time.time())}"
        
        # 注册失败处理
        self.failure_handler.register_task(task_id, failing_task)
        
        # 执行任务（应该失败并触发重试）
        with self.assertLogs(level='ERROR') as log:
            result = self.failure_handler.execute_task_with_retry(
                task_id, max_retries=2, retry_delay=1
            )
        
        self.assertFalse(result, "失败任务应该返回False")
        self.assertTrue(any("Simulated task failure" in record.message for record in log.records),
                       "应该记录失败日志")
        
        logger.info("Failover mechanism test passed")
        
        # 2. 测试数据恢复
        # 模拟数据丢失场景
        test_symbol = "TEST001"
        
        # 先存储一些测试数据
        test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02'],
            'open': [10.0, 11.0],
            'high': [10.5, 11.5],
            'low': [9.5, 10.5],
            'close': [10.2, 11.2],
            'volume': [1000, 1100]
        })
        
        archive_result = asyncio.run(
            self.archiver.archive_k_data(test_symbol, test_data)
        )
        self.assertTrue(archive_result, "测试数据存储失败")
        
        # 验证数据恢复功能
        recovery_result = asyncio.run(
            self.updater.check_and_update_missing_data(
                test_symbol, 
                self.test_start_date, 
                self.test_end_date
            )
        )
        
        self.assertIsNotNone(recovery_result, "数据恢复检查失败")
        logger.info("Data recovery mechanism test passed")
    
    def test_04_scheduled_task_execution(self):
        """测试定时任务执行"""
        logger.info("Testing scheduled task execution")
        
        # 创建测试任务
        task_executed = {"count": 0}
        
        def test_task():
            task_executed["count"] += 1
            logger.info(f"Test task executed {task_executed['count']} times")
            return True
        
        # 注册定时任务（每2秒执行一次，用于测试）
        task_config = {
            "task_id": "test_scheduled_task",
            "function": test_task,
            "schedule": "*/2 * * * * *",  # 每2秒
            "max_retries": 1
        }
        
        # 启动调度器
        self.scheduler.start()
        
        # 添加任务
        self.scheduler.add_task(task_config)
        
        # 等待任务执行
        time.sleep(5)
        
        # 停止调度器
        self.scheduler.stop()
        
        # 验证任务至少执行了一次
        self.assertGreater(task_executed["count"], 0, "定时任务未执行")
        
        logger.info(f"Scheduled task execution test passed: executed {task_executed['count']} times")
    
    def test_05_storage_layer_routing(self):
        """测试存储层路由功能"""
        logger.info("Testing storage layer routing")
        
        # 测试不同时间范围的数据路由到不同存储层
        now = datetime.now()
        
        # 测试数据时间点
        test_cases = [
            {
                "name": "hot_storage",
                "timestamp": now - timedelta(days=1),  # 1天前 -> 热存储
                "expected_layer": "hot"
            },
            {
                "name": "warm_storage", 
                "timestamp": now - timedelta(days=30),  # 30天前 -> 温存储
                "expected_layer": "warm"
            },
            {
                "name": "cold_storage",
                "timestamp": now - timedelta(days=800),  # 800天前 -> 冷存储
                "expected_layer": "cold"
            }
        ]
        
        for case in test_cases:
            timestamp_ns = int(case["timestamp"].timestamp() * 1_000_000_000)
            
            # 测试存储层选择
            selected_layer = self.storage_selector.select_storage_layer(timestamp_ns)
            
            self.assertEqual(selected_layer, case["expected_layer"],
                           f"存储层路由错误: {case['name']} 应该路由到 {case['expected_layer']}, 实际路由到 {selected_layer}")
        
        logger.info("Storage layer routing test passed")
    
    def test_06_data_consistency_check(self):
        """测试数据一致性检查"""
        logger.info("Testing data consistency check")
        
        symbol = "CONSISTENCY_TEST"
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02', '2024-01-01'],  # 包含重复日期
            'open': [10.0, 11.0, 10.1],  # 重复日期的数据略有不同
            'high': [10.5, 11.5, 10.6],
            'low': [9.5, 10.5, 9.4],
            'close': [10.2, 11.2, 10.3],
            'volume': [1000, 1100, 1050]
        })
        
        # 归档数据（应该触发去重）
        archive_result = asyncio.run(
            self.archiver.archive_k_data(symbol, test_data)
        )
        
        self.assertTrue(archive_result, "数据归档失败")
        
        # 查询存储的数据
        query_request = {
            "symbol": symbol,
            "data_type": "kline",
            "start_timestamp": int(datetime(2024, 1, 1).timestamp() * 1000),
            "end_timestamp": int(datetime(2024, 1, 3).timestamp() * 1000),
            "limit": 100
        }
        
        query_result = asyncio.run(
            self.unified_access.query_data(query_request)
        )
        
        stored_data = query_result.get("data", [])
        
        # 验证去重效果：应该只有2条记录（重复的2024-01-01被去重）
        unique_dates = set(record.get("date") for record in stored_data)
        self.assertEqual(len(unique_dates), 2, 
                        f"数据去重失败: 期望2个唯一日期, 实际{len(unique_dates)}个")
        
        logger.info("Data consistency check test passed")
    
    def test_07_performance_baseline(self):
        """测试性能基线"""
        logger.info("Testing performance baseline")
        
        symbol = self.test_symbols[0]
        
        # 测试数据采集性能
        start_time = time.time()
        
        k_data = self.pytdx_collector.get_k_data(
            symbol,
            start_date=self.test_start_date,
            end_date=self.test_end_date
        )
        
        collection_time = time.time() - start_time
        
        self.assertIsNotNone(k_data, "数据采集失败")
        self.assertLess(collection_time, 30, f"数据采集时间过长: {collection_time:.2f}s")
        
        logger.info(f"Data collection performance: {collection_time:.2f}s for {len(k_data)} records")
        
        # 测试数据存储性能
        start_time = time.time()
        
        archive_result = asyncio.run(
            self.archiver.archive_k_data(symbol, k_data)
        )
        
        storage_time = time.time() - start_time
        
        self.assertTrue(archive_result, "数据存储失败")
        self.assertLess(storage_time, 60, f"数据存储时间过长: {storage_time:.2f}s")
        
        logger.info(f"Data storage performance: {storage_time:.2f}s for {len(k_data)} records")
        
        # 测试数据查询性能
        start_time = time.time()
        
        query_request = {
            "symbol": symbol,
            "data_type": "kline",
            "start_timestamp": int(datetime.strptime(self.test_start_date, "%Y-%m-%d").timestamp() * 1000),
            "end_timestamp": int(datetime.strptime(self.test_end_date, "%Y-%m-%d").timestamp() * 1000),
            "limit": 1000
        }
        
        query_result = asyncio.run(
            self.unified_access.query_data(query_request)
        )
        
        query_time = time.time() - start_time
        
        self.assertIsNotNone(query_result, "数据查询失败")
        self.assertLess(query_time, 10, f"数据查询时间过长: {query_time:.2f}s")
        
        logger.info(f"Data query performance: {query_time:.2f}s for {len(query_result.get('data', []))} records")
        
        logger.info("Performance baseline test passed")


def run_integration_tests():
    """运行集成测试"""
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(EndToEndIntegrationTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        buffer=True
    )
    
    result = runner.run(suite)
    
    # 生成测试报告
    generate_test_report(result)
    
    return result.wasSuccessful()


def generate_test_report(result):
    """生成测试报告"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "total_tests": result.testsRun,
        "failures": len(result.failures),
        "errors": len(result.errors),
        "success_rate": (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100,
        "details": {
            "failures": [{"test": str(test), "error": error} for test, error in result.failures],
            "errors": [{"test": str(test), "error": error} for test, error in result.errors]
        }
    }
    
    # 保存报告
    report_file = f"tests/integration/end_to_end_test_report_{int(time.time())}.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Test report saved to: {report_file}")
    logger.info(f"Test summary: {report['total_tests']} tests, "
               f"{report['success_rate']:.1f}% success rate")


if __name__ == "__main__":
    # 设置测试环境
    os.environ["TESTING"] = "1"
    
    # 运行集成测试
    success = run_integration_tests()
    
    # 退出码
    sys.exit(0 if success else 1)