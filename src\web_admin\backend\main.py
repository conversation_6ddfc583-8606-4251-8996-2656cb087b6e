#!/usr/bin/env python3
"""
金融数据服务系统 - Web管理界面后端
Financial Data Service System - Web Admin Backend
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import logging
import redis
from clickhouse_driver import Client as ClickHouseClient
import asyncpg
from pydantic import BaseModel
import jwt
from passlib.context import CryptContext
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="金融数据服务系统管理界面",
    description="Financial Data Service System Admin Interface",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全配置
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"

# 数据模型
class SystemMetrics(BaseModel):
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    active_connections: int
    data_throughput: float
    latency_p99: float
    error_rate: float

class AlertConfig(BaseModel):
    id: Optional[str] = None
    name: str
    metric: str
    threshold: float
    operator: str  # >, <, >=, <=, ==
    enabled: bool = True
    notification_channels: List[str]

class User(BaseModel):
    id: Optional[str] = None
    username: str
    email: str
    role: str  # admin, operator, viewer
    enabled: bool = True
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None

class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    role: str

class LoginRequest(BaseModel):
    username: str
    password: str

class SystemConfig(BaseModel):
    category: str
    key: str
    value: Any
    description: str
    requires_restart: bool = False

class DataQuery(BaseModel):
    symbol: str
    start_time: datetime
    end_time: datetime
    data_type: str  # tick, kline, level2
    format: str = "json"  # json, csv, parquet

# 全局变量
redis_client = None
db_pool = None
clickhouse_client: Optional[ClickHouseClient] = None
USE_MOCK = os.getenv("USE_MOCK", "false").lower() in {"1", "true", "yes", "on"}

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化连接"""
    global redis_client, db_pool, clickhouse_client
    
    # 初始化Redis连接
    redis_client = redis.Redis(
        host=os.getenv("REDIS_HOST", "localhost"),
        port=int(os.getenv("REDIS_PORT", 6379)),
        decode_responses=True
    )
    
    # 初始化PostgreSQL连接池
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", 5432)),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "password"),
        database=os.getenv("DB_NAME", "financial_data_admin"),
        min_size=5,
        max_size=20
    )
    # 初始化 ClickHouse 连接
    try:
        clickhouse_client = ClickHouseClient(
            host=os.getenv("CLICKHOUSE_HOST", "127.0.0.1"),
            port=int(os.getenv("CLICKHOUSE_PORT", 9000)),
            user=os.getenv("CLICKHOUSE_USER", "admin"),
            password=os.getenv("CLICKHOUSE_PASSWORD", "password123"),
            database=os.getenv("CLICKHOUSE_DB", "market_data"),
            settings={"use_numpy": False}
        )
        logger.info("ClickHouse connected")
    except Exception as e:
        clickhouse_client = None
        logger.warning(f"ClickHouse connection failed: {e}")

    logger.info("Web admin backend started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    global redis_client, db_pool, clickhouse_client
    
    if redis_client:
        redis_client.close()
    
    if db_pool:
        await db_pool.close()
    if clickhouse_client:
        try:
            clickhouse_client.disconnect()
        except Exception:
            pass
    
    logger.info("Web admin backend shutdown completed")

# 认证相关函数
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=24)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="Invalid token")
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    # 从数据库获取用户信息
    async with db_pool.acquire() as conn:
        user = await conn.fetchrow(
            "SELECT * FROM users WHERE username = $1 AND enabled = true",
            username
        )
        if user is None:
            raise HTTPException(status_code=401, detail="User not found")
    
    return dict(user)

# API路由

@app.post("/api/auth/login")
async def login(req: LoginRequest):
    """用户登录"""
    async with db_pool.acquire() as conn:
        user = await conn.fetchrow(
            "SELECT * FROM users WHERE username = $1 AND enabled = true",
            req.username
        )
        
        if not user or not verify_password(req.password, user['password_hash']):
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # 更新最后登录时间
        await conn.execute(
            "UPDATE users SET last_login = $1 WHERE id = $2",
            datetime.utcnow(), user['id']
        )
    
    access_token = create_access_token(data={"sub": req.username})
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "username": user['username'],
            "email": user['email'],
            "role": user['role']
        }
    }

@app.get("/api/system/metrics")
async def get_system_metrics(current_user: dict = Depends(get_current_user)):
    """获取系统实时性能指标"""
    try:
        # 从Redis获取实时指标
        metrics_data = redis_client.hgetall("system:metrics:current")
        
        if not metrics_data:
            if USE_MOCK:
                # 模拟数据（如未启用监控采集，可临时返回）
                metrics_data = {
                    "cpu_usage": "45.2",
                    "memory_usage": "67.8",
                    "disk_usage": "23.4",
                    "active_connections": "856",
                    "data_throughput": "987654.32",
                    "latency_p99": "42.5",
                    "error_rate": "0.001"
                }
            else:
                raise HTTPException(status_code=503, detail="No metrics available")
        
        return SystemMetrics(
            timestamp=datetime.utcnow(),
            cpu_usage=float(metrics_data.get("cpu_usage", 0)),
            memory_usage=float(metrics_data.get("memory_usage", 0)),
            disk_usage=float(metrics_data.get("disk_usage", 0)),
            network_io={
                "rx_bytes": float(metrics_data.get("network_rx", 0)),
                "tx_bytes": float(metrics_data.get("network_tx", 0))
            },
            active_connections=int(metrics_data.get("active_connections", 0)),
            data_throughput=float(metrics_data.get("data_throughput", 0)),
            latency_p99=float(metrics_data.get("latency_p99", 0)),
            error_rate=float(metrics_data.get("error_rate", 0))
        )
    except Exception as e:
        logger.error(f"Error getting system metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system metrics")

@app.get("/api/system/metrics/stream")
async def stream_system_metrics(current_user: dict = Depends(get_current_user)):
    """实时系统指标流"""
    async def generate_metrics():
        while True:
            try:
                metrics_data = redis_client.hgetall("system:metrics:current")
                if metrics_data:
                    yield f"data: {json.dumps(metrics_data)}\n\n"
                await asyncio.sleep(1)  # 每秒更新一次
            except Exception as e:
                logger.error(f"Error streaming metrics: {e}")
                break
    
    return StreamingResponse(
        generate_metrics(),
        media_type="text/plain",
        headers={"Cache-Control": "no-cache"}
    )

@app.get("/api/system/components")
async def get_system_components(current_user: dict = Depends(get_current_user)):
    """获取各组件状态列表：优先从 Redis 读取键 system:components:current（JSON数组）"""
    try:
        raw = redis_client.get("system:components:current")
        if not raw:
            if USE_MOCK:
                now = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
                data = [
                    {"name": "CTP数据采集器", "status": "online", "uptime": "2天 14小时 32分钟", "lastCheck": now, "details": "已连接上期所、大商所、郑商所"},
                    {"name": "Redis热数据存储", "status": "online", "uptime": "7天 2小时 15分钟", "lastCheck": now, "details": "集群状态正常，3个节点在线"},
                    {"name": "ClickHouse温数据存储", "status": "warning", "uptime": "5天 18小时 45分钟", "lastCheck": now, "details": "磁盘使用率高"}
                ]
                return data
            raise HTTPException(status_code=503, detail="No components status available")
        data = json.loads(raw)
        return data
    except Exception as e:
        logger.error(f"Error getting components: {e}")
        raise HTTPException(status_code=500, detail="Failed to get components")

@app.get("/api/alerts")
async def get_alerts(current_user: dict = Depends(get_current_user)) -> List[AlertConfig]:
    """获取告警配置列表"""
    async with db_pool.acquire() as conn:
        alerts = await conn.fetch("SELECT * FROM alert_configs ORDER BY name")
        return [AlertConfig(**dict(alert)) for alert in alerts]

@app.post("/api/alerts")
async def create_alert(
    alert: AlertConfig,
    current_user: dict = Depends(get_current_user)
):
    """创建告警配置"""
    if current_user['role'] not in ['admin', 'operator']:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    async with db_pool.acquire() as conn:
        alert_id = await conn.fetchval(
            """INSERT INTO alert_configs (name, metric, threshold, operator, enabled, notification_channels)
               VALUES ($1, $2, $3, $4, $5, $6) RETURNING id""",
            alert.name, alert.metric, alert.threshold, alert.operator,
            alert.enabled, json.dumps(alert.notification_channels)
        )
        
        return {"id": alert_id, "message": "Alert created successfully"}

@app.put("/api/alerts/{alert_id}")
async def update_alert(
    alert_id: str,
    alert: AlertConfig,
    current_user: dict = Depends(get_current_user)
):
    """更新告警配置"""
    if current_user['role'] not in ['admin', 'operator']:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    async with db_pool.acquire() as conn:
        result = await conn.execute(
            """UPDATE alert_configs SET name=$1, metric=$2, threshold=$3, 
               operator=$4, enabled=$5, notification_channels=$6 WHERE id=$7""",
            alert.name, alert.metric, alert.threshold, alert.operator,
            alert.enabled, json.dumps(alert.notification_channels), alert_id
        )
        
        if result == "UPDATE 0":
            raise HTTPException(status_code=404, detail="Alert not found")
        
        return {"message": "Alert updated successfully"}

@app.delete("/api/alerts/{alert_id}")
async def delete_alert(
    alert_id: str,
    current_user: dict = Depends(get_current_user)
):
    """删除告警配置"""
    if current_user['role'] != 'admin':
        raise HTTPException(status_code=403, detail="Admin access required")
    
    async with db_pool.acquire() as conn:
        result = await conn.execute("DELETE FROM alert_configs WHERE id=$1", alert_id)
        
        if result == "DELETE 0":
            raise HTTPException(status_code=404, detail="Alert not found")
        
        return {"message": "Alert deleted successfully"}

@app.get("/api/users")
async def get_users(current_user: dict = Depends(get_current_user)) -> List[User]:
    """获取用户列表"""
    if current_user['role'] != 'admin':
        raise HTTPException(status_code=403, detail="Admin access required")
    
    async with db_pool.acquire() as conn:
        users = await conn.fetch(
            "SELECT id, username, email, role, enabled, created_at, last_login FROM users ORDER BY username"
        )
        return [User(**dict(user)) for user in users]

@app.post("/api/users")
async def create_user(
    user: UserCreate,
    current_user: dict = Depends(get_current_user)
):
    """创建用户"""
    if current_user['role'] != 'admin':
        raise HTTPException(status_code=403, detail="Admin access required")
    
    password_hash = get_password_hash(user.password)
    
    async with db_pool.acquire() as conn:
        try:
            user_id = await conn.fetchval(
                """INSERT INTO users (username, email, password_hash, role, enabled, created_at)
                   VALUES ($1, $2, $3, $4, true, $5) RETURNING id""",
                user.username, user.email, password_hash, user.role, datetime.utcnow()
            )
            return {"id": user_id, "message": "User created successfully"}
        except asyncpg.UniqueViolationError:
            raise HTTPException(status_code=400, detail="Username or email already exists")

@app.get("/api/data/query")
async def query_data(
    symbol: str,
    start_time: datetime,
    end_time: datetime,
    data_type: str = "tick",
    format: str = "json",
    limit: int = 1000,
    current_user: dict = Depends(get_current_user)
):
    """查询历史数据（优先使用 ClickHouse，若 USE_MOCK 则生成样本数据）"""
    if data_type not in {"tick", "kline", "level2"}:
        raise HTTPException(status_code=400, detail="Unsupported data_type")

    # 优先尝试 ClickHouse
    if clickhouse_client and not USE_MOCK:
        try:
            if data_type == "tick":
                table = os.getenv("CLICKHOUSE_TICK_TABLE", "futures_tick")
                query = (
                    f"SELECT timestamp, symbol, last_price, volume, turnover "
                    f"FROM {table} WHERE symbol=%(symbol)s AND timestamp >= %(start)s AND timestamp < %(end)s "
                    f"ORDER BY timestamp ASC LIMIT %(limit)s"
                )
                rows = clickhouse_client.execute(
                    query,
                    {
                        "symbol": symbol,
                        "start": start_time,
                        "end": end_time,
                        "limit": limit,
                    },
                    types_check=True,
                )
                data = [
                    {
                        "timestamp": r[0].isoformat() if hasattr(r[0], "isoformat") else str(r[0]),
                        "symbol": r[1],
                        "last_price": float(r[2]),
                        "volume": int(r[3]),
                        "turnover": float(r[4]),
                    }
                    for r in rows
                ]
            else:
                # 可扩展 kline/level2 表
                data = []

            if format == "csv":
                import io
                import csv
                output = io.StringIO()
                if data:
                    writer = csv.DictWriter(output, fieldnames=data[0].keys())
                    writer.writeheader()
                    writer.writerows(data)
                return StreamingResponse(
                    io.BytesIO(output.getvalue().encode()),
                    media_type="text/csv",
                    headers={"Content-Disposition": f"attachment; filename={symbol}_{data_type}.csv"}
                )

            return {"data": data, "total": len(data)}
        except Exception as e:
            logger.error(f"ClickHouse query failed: {e}")
            if not USE_MOCK:
                raise HTTPException(status_code=500, detail="Query failed")

    # 回退：生成样本数据
    sample_data = []
    current_time = start_time
    while current_time < end_time and len(sample_data) < limit:
        sample_data.append({
            "timestamp": current_time.isoformat(),
            "symbol": symbol,
            "last_price": 78560.0 + (len(sample_data) % 100) * 0.1,
            "volume": 100 + (len(sample_data) % 50),
            "turnover": 7856000.0 + len(sample_data) * 100,
        })
        current_time += timedelta(seconds=1)

    if format == "csv":
        import io
        import csv
        output = io.StringIO()
        if sample_data:
            writer = csv.DictWriter(output, fieldnames=sample_data[0].keys())
            writer.writeheader()
            writer.writerows(sample_data)
        return StreamingResponse(
            io.BytesIO(output.getvalue().encode()),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={symbol}_{data_type}.csv"}
        )

    return {"data": sample_data, "total": len(sample_data)}

@app.get("/api/config")
async def get_system_config(current_user: dict = Depends(get_current_user)) -> List[SystemConfig]:
    """获取系统配置"""
    if current_user['role'] not in ['admin', 'operator']:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    async with db_pool.acquire() as conn:
        configs = await conn.fetch("SELECT * FROM system_configs ORDER BY category, key")
        return [SystemConfig(**dict(config)) for config in configs]

@app.put("/api/config")
async def update_system_config(
    configs: List[SystemConfig],
    current_user: dict = Depends(get_current_user)
):
    """更新系统配置"""
    if current_user['role'] != 'admin':
        raise HTTPException(status_code=403, detail="Admin access required")
    
    async with db_pool.acquire() as conn:
        async with conn.transaction():
            for config in configs:
                await conn.execute(
                    """INSERT INTO system_configs (category, key, value, description, requires_restart)
                       VALUES ($1, $2, $3, $4, $5)
                       ON CONFLICT (category, key) DO UPDATE SET
                       value = EXCLUDED.value,
                       description = EXCLUDED.description,
                       requires_restart = EXCLUDED.requires_restart""",
                    config.category, config.key, json.dumps(config.value),
                    config.description, config.requires_restart
                )
    
    return {"message": "Configuration updated successfully"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)