#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史数据采集脚本
根据代码表逐个采集历史行情数据
"""

import asyncio
import logging
import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from collectors.pytdx_collector import PyTDXCollector, PyTDXConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('historical_data_collection.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class HistoricalDataCollector:
    """历史数据采集器"""
    
    def __init__(self):
        self.config = PyTDXConfig()
        self.collector = PyTDXCollector(self.config)
        
        # 统计信息
        self.stats = {
            'symbols_processed': 0,
            'data_points_collected': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None,
            'success_symbols': [],
            'failed_symbols': []
        }
    
    async def initialize(self) -> bool:
        """初始化"""
        try:
            logger.info("初始化PyTDX连接...")
            success = await self.collector.initialize()
            if success:
                logger.info("✅ 连接成功")
                return True
            else:
                logger.error("❌ 连接失败")
                return False
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def load_symbol_list(self, file_path: str) -> List[Dict]:
        """从文件加载代码表"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                symbols = json.load(f)
            logger.info(f"✅ 加载代码表: {len(symbols)} 个标的")
            return symbols
        except Exception as e:
            logger.error(f"❌ 加载代码表失败: {e}")
            return []
    
    async def collect_stock_data(self, 
                               symbols: List[Dict], 
                               periods: List[str] = None,
                               days_back: int = 30,
                               max_symbols: Optional[int] = None) -> bool:
        """
        采集股票历史数据
        
        Args:
            symbols: 股票代码列表
            periods: K线周期列表
            days_back: 回溯天数
            max_symbols: 最大处理标的数量（用于测试）
        """
        if periods is None:
            periods = ['daily', '60min', '30min', '15min']
        
        if max_symbols:
            symbols = symbols[:max_symbols]
        
        logger.info(f"开始采集股票数据: {len(symbols)} 个标的, 周期: {periods}")
        
        success_count = 0
        error_count = 0
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        for i, symbol_info in enumerate(symbols):
            symbol = symbol_info.get('code', '')
            name = symbol_info.get('name', '')
            
            if not symbol:
                continue
            
            logger.info(f"[{i+1}/{len(symbols)}] 采集 {symbol} ({name})")
            
            try:
                symbol_data_points = 0
                
                # 采集不同周期的数据
                for period in periods:
                    try:
                        data = await self.collector.get_k_data(
                            symbol=symbol,
                            period=period,
                            start_date=start_date.strftime('%Y-%m-%d'),
                            end_date=end_date.strftime('%Y-%m-%d')
                        )
                        
                        if data is not None and not data.empty:
                            # 归档数据
                            if self.collector.archiver:
                                await self.collector.archiver.archive_k_data(
                                    symbol=symbol,
                                    data=data,
                                    data_type=f"stock_{period}"
                                )
                            
                            symbol_data_points += len(data)
                            logger.info(f"  ✅ {period:6s}: {len(data):4d} 条")
                        else:
                            logger.warning(f"  ⚠️ {period:6s}: 无数据")
                        
                        # 避免请求过于频繁
                        await asyncio.sleep(0.05)
                        
                    except Exception as e:
                        logger.error(f"  ❌ {period} 数据采集失败: {e}")
                        continue
                
                if symbol_data_points > 0:
                    success_count += 1
                    self.stats['success_symbols'].append(symbol)
                    self.stats['data_points_collected'] += symbol_data_points
                else:
                    error_count += 1
                    self.stats['failed_symbols'].append(symbol)
                
                self.stats['symbols_processed'] += 1
                
                # 每处理10个标的显示一次进度
                if (i + 1) % 10 == 0:
                    logger.info(f"进度: {i+1}/{len(symbols)}, 成功: {success_count}, 失败: {error_count}")
                
            except Exception as e:
                logger.error(f"采集 {symbol} 失败: {e}")
                error_count += 1
                self.stats['errors'] += 1
                self.stats['failed_symbols'].append(symbol)
                continue
        
        logger.info(f"✅ 股票数据采集完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0
    
    async def collect_index_data(self, 
                               symbols: List[Dict], 
                               periods: List[str] = None,
                               days_back: int = 30) -> bool:
        """采集指数历史数据"""
        if periods is None:
            periods = ['daily', '60min', '30min']
        
        logger.info(f"开始采集指数数据: {len(symbols)} 个标的, 周期: {periods}")
        
        success_count = 0
        error_count = 0
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        for i, symbol_info in enumerate(symbols):
            symbol = symbol_info.get('code', '')
            name = symbol_info.get('name', '')
            
            if not symbol:
                continue
            
            logger.info(f"[{i+1}/{len(symbols)}] 采集指数 {symbol} ({name})")
            
            try:
                symbol_data_points = 0
                
                for period in periods:
                    try:
                        data = await self.collector.get_index_data(
                            symbol=symbol,
                            period=period,
                            start_date=start_date.strftime('%Y-%m-%d'),
                            end_date=end_date.strftime('%Y-%m-%d')
                        )
                        
                        if data is not None and not data.empty:
                            # 归档数据
                            if self.collector.archiver:
                                await self.collector.archiver.archive_k_data(
                                    symbol=symbol,
                                    data=data,
                                    data_type=f"index_{period}"
                                )
                            
                            symbol_data_points += len(data)
                            logger.info(f"  ✅ {period:6s}: {len(data):4d} 条")
                        else:
                            logger.warning(f"  ⚠️ {period:6s}: 无数据")
                        
                        await asyncio.sleep(0.05)
                        
                    except Exception as e:
                        logger.error(f"  ❌ {period} 数据采集失败: {e}")
                        continue
                
                if symbol_data_points > 0:
                    success_count += 1
                    self.stats['success_symbols'].append(symbol)
                    self.stats['data_points_collected'] += symbol_data_points
                else:
                    error_count += 1
                    self.stats['failed_symbols'].append(symbol)
                
                self.stats['symbols_processed'] += 1
                
            except Exception as e:
                logger.error(f"采集指数 {symbol} 失败: {e}")
                error_count += 1
                self.stats['errors'] += 1
                self.stats['failed_symbols'].append(symbol)
                continue
        
        logger.info(f"✅ 指数数据采集完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0
    
    async def run_collection(self, 
                           symbol_files: Dict[str, str],
                           periods: List[str] = None,
                           days_back: int = 30,
                           max_symbols_per_type: Optional[int] = None) -> bool:
        """
        运行数据采集
        
        Args:
            symbol_files: 代码表文件路径字典 {'stock': 'path/to/stock.json', ...}
            periods: K线周期
            days_back: 回溯天数
            max_symbols_per_type: 每种类型最大处理数量
        """
        self.stats['start_time'] = datetime.now()
        
        try:
            logger.info("🚀 开始历史数据采集")
            logger.info("=" * 60)
            
            # 初始化
            if not await self.initialize():
                return False
            
            success = True
            
            # 采集股票数据
            if 'stock' in symbol_files:
                logger.info("\n📊 采集股票数据")
                stock_symbols = self.load_symbol_list(symbol_files['stock'])
                if stock_symbols:
                    stock_success = await self.collect_stock_data(
                        symbols=stock_symbols,
                        periods=periods,
                        days_back=days_back,
                        max_symbols=max_symbols_per_type
                    )
                    success = success and stock_success
            
            # 采集指数数据
            if 'index' in symbol_files:
                logger.info("\n📈 采集指数数据")
                index_symbols = self.load_symbol_list(symbol_files['index'])
                if index_symbols:
                    index_success = await self.collect_index_data(
                        symbols=index_symbols,
                        periods=periods,
                        days_back=days_back
                    )
                    success = success and index_success
            
            # 显示统计信息
            await self._show_statistics()
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 数据采集失败: {e}")
            return False
        finally:
            self.stats['end_time'] = datetime.now()
            await self.cleanup()
    
    async def _show_statistics(self):
        """显示统计信息"""
        logger.info("\n📈 采集统计")
        logger.info("=" * 60)
        logger.info(f"标的处理: {self.stats['symbols_processed']} 个")
        logger.info(f"数据点采集: {self.stats['data_points_collected']} 条")
        logger.info(f"成功标的: {len(self.stats['success_symbols'])} 个")
        logger.info(f"失败标的: {len(self.stats['failed_symbols'])} 个")
        logger.info(f"错误数量: {self.stats['errors']} 个")
        
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            logger.info(f"总耗时: {duration}")
        
        # 显示失败的标的（前10个）
        if self.stats['failed_symbols']:
            failed_sample = self.stats['failed_symbols'][:10]
            logger.info(f"失败标的样本: {failed_sample}")
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.collector.close()
            logger.info("✅ 资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


async def main():
    """主函数"""
    print("PyTDX历史数据采集工具")
    print("=" * 60)
    print("功能: 根据代码表采集历史行情数据")
    print("支持: 股票、指数的日线和分钟线数据")
    print()
    
    # 检查代码表文件
    symbol_files = {}
    
    # 查找最新的代码表文件
    symbols_dir = "data/symbols"
    if os.path.exists(symbols_dir):
        for data_type in ['stock', 'index']:
            # 查找最新的文件
            pattern = f"{data_type}_symbols_"
            files = [f for f in os.listdir(symbols_dir) if f.startswith(pattern) and f.endswith('.json')]
            if files:
                latest_file = sorted(files)[-1]  # 按文件名排序，取最新的
                symbol_files[data_type] = os.path.join(symbols_dir, latest_file)
                print(f"✅ 找到{data_type}代码表: {latest_file}")
    
    if not symbol_files:
        print("❌ 未找到代码表文件，请先运行 update_symbol_lists.py")
        return 1
    
    collector = HistoricalDataCollector()
    
    try:
        # 配置采集参数
        periods = ['daily', '60min', '30min']  # K线周期
        days_back = 30  # 回溯30天
        max_symbols_per_type = 50  # 测试时限制数量，生产环境可设为None
        
        print(f"采集配置:")
        print(f"  K线周期: {periods}")
        print(f"  回溯天数: {days_back}")
        print(f"  最大标的数: {max_symbols_per_type or '无限制'}")
        print()
        
        # 运行采集
        success = await collector.run_collection(
            symbol_files=symbol_files,
            periods=periods,
            days_back=days_back,
            max_symbols_per_type=max_symbols_per_type
        )
        
        if success:
            print("\n🎉 历史数据采集完成！")
            return 0
        else:
            print("\n❌ 历史数据采集失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断采集")
        return 1
    except Exception as e:
        print(f"\n❌ 采集过程出错: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)