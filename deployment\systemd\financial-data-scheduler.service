[Unit]
Description=Financial Data Service - Task Scheduler
Documentation=https://github.com/your-org/financial-data-service
After=network.target redis.service clickhouse-server.service
Wants=redis.service clickhouse-server.service

[Service]
Type=forking
User=financial-data
Group=financial-data
WorkingDirectory=/opt/financial-data-service
Environment=PYTHONPATH=/opt/financial-data-service/src
Environment=CONFIG_PATH=/etc/financial-data-service/scheduler_config.json
ExecStart=/opt/financial-data-service/scripts/start_scheduler.sh --daemon --config ${CONFIG_PATH}
ExecStop=/opt/financial-data-service/scripts/start_scheduler.sh --stop
ExecReload=/opt/financial-data-service/scripts/start_scheduler.sh --restart
PIDFile=/var/run/financial-data-service/scheduler.pid
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=financial-data-scheduler

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/financial-data-service /var/run/financial-data-service /opt/financial-data-service/logs
PrivateTmp=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

[Install]
WantedBy=multi-user.target