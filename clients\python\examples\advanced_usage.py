#!/usr/bin/env python3
"""
Advanced usage examples for Financial Data SDK
"""

import asyncio
import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from concurrent.futures import ThreadPoolExecutor, as_completed
from financial_data_sdk import FinancialDataClient, AsyncFinancialDataClient
from financial_data_sdk.indicators import TechnicalIndicators, IndicatorAnalyzer
from financial_data_sdk.cache import PersistentCache, BatchCache
from financial_data_sdk.utils import PerformanceProfiler, DataConverter
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AdvancedTradingStrategy:
    """
    Advanced trading strategy using multiple indicators
    """
    
    def __init__(self, client: FinancialDataClient):
        self.client = client
        self.positions = {}
        self.performance_metrics = {}
    
    def calculate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate complex trading signals"""
        
        # Calculate multiple indicators
        df['sma_20'] = TechnicalIndicators.sma(df['close'], 20)
        df['sma_50'] = TechnicalIndicators.sma(df['close'], 50)
        df['ema_12'] = TechnicalIndicators.ema(df['close'], 12)
        df['rsi'] = TechnicalIndicators.rsi(df['close'])
        
        macd_line, signal_line, histogram = TechnicalIndicators.macd(df['close'])
        df['macd'] = macd_line
        df['macd_signal'] = signal_line
        df['macd_histogram'] = histogram
        
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['close'])
        df['bb_upper'] = bb_upper
        df['bb_middle'] = bb_middle
        df['bb_lower'] = bb_lower
        
        # Complex signal generation
        df['trend_signal'] = np.where(
            (df['sma_20'] > df['sma_50']) & (df['close'] > df['ema_12']), 1,
            np.where((df['sma_20'] < df['sma_50']) & (df['close'] < df['ema_12']), -1, 0)
        )
        
        df['momentum_signal'] = np.where(
            (df['rsi'] < 30) & (df['macd'] > df['macd_signal']), 1,
            np.where((df['rsi'] > 70) & (df['macd'] < df['macd_signal']), -1, 0)
        )
        
        df['volatility_signal'] = np.where(
            df['close'] < df['bb_lower'], 1,
            np.where(df['close'] > df['bb_upper'], -1, 0)
        )
        
        # Combined signal
        df['combined_signal'] = (
            df['trend_signal'] + 
            df['momentum_signal'] + 
            df['volatility_signal']
        )
        
        # Generate buy/sell signals
        df['buy_signal'] = df['combined_signal'] >= 2
        df['sell_signal'] = df['combined_signal'] <= -2
        
        return df
    
    def backtest_strategy(self, symbols: list, start_date: str, end_date: str):
        """Backtest the strategy on multiple symbols"""
        
        results = {}
        
        for symbol in symbols:
            try:
                # Get historical data
                df = self.client.get_kline_data(
                    symbol=symbol,
                    period="1h",
                    start_time=start_date,
                    end_time=end_date,
                    limit=10000,
                    as_dataframe=True
                )
                
                if df.empty:
                    continue
                
                # Calculate signals
                df = self.calculate_signals(df)
                
                # Run backtest
                backtest_result = IndicatorAnalyzer.backtest_strategy(
                    df,
                    df['buy_signal'],
                    df['sell_signal'],
                    initial_capital=100000
                )
                
                results[symbol] = {
                    'backtest': backtest_result,
                    'data': df,
                    'sharpe_ratio': self._calculate_sharpe_ratio(df),
                    'max_drawdown': self._calculate_max_drawdown(df)
                }
                
                logger.info(f"Backtested {symbol}: Return {backtest_result['total_return']:.2%}")
                
            except Exception as e:
                logger.error(f"Failed to backtest {symbol}: {e}")
        
        return results
    
    def _calculate_sharpe_ratio(self, df: pd.DataFrame, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        returns = df['close'].pct_change().dropna()
        excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
        
        if returns.std() == 0:
            return 0
        
        return (excess_returns.mean() / returns.std()) * np.sqrt(252)
    
    def _calculate_max_drawdown(self, df: pd.DataFrame) -> float:
        """Calculate maximum drawdown"""
        cumulative = (1 + df['close'].pct_change()).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()


class RealTimeDataProcessor:
    """
    Real-time data processing with advanced features
    """
    
    def __init__(self, client: AsyncFinancialDataClient):
        self.client = client
        self.data_buffer = {}
        self.indicators_cache = {}
        self.alert_thresholds = {}
    
    async def process_real_time_stream(self, symbols: list, duration_seconds: int = 60):
        """Process real-time data stream with indicators"""
        
        print(f"Starting real-time processing for {symbols} for {duration_seconds} seconds...")
        
        # Create tasks for each symbol
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(self._process_symbol_stream(symbol))
            tasks.append(task)
        
        # Run for specified duration
        try:
            await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=duration_seconds
            )
        except asyncio.TimeoutError:
            print("Real-time processing completed")
        
        # Cancel remaining tasks
        for task in tasks:
            if not task.done():
                task.cancel()
    
    async def _process_symbol_stream(self, symbol: str):
        """Process stream for a single symbol"""
        
        self.data_buffer[symbol] = []
        
        try:
            async for tick in self.client.stream_tick_data([symbol]):
                # Add to buffer
                self.data_buffer[symbol].append(tick)
                
                # Keep only last 100 ticks for indicators
                if len(self.data_buffer[symbol]) > 100:
                    self.data_buffer[symbol] = self.data_buffer[symbol][-100:]
                
                # Calculate real-time indicators every 10 ticks
                if len(self.data_buffer[symbol]) % 10 == 0:
                    await self._update_indicators(symbol)
                
                # Check alerts
                await self._check_alerts(symbol, tick)
                
        except Exception as e:
            logger.error(f"Error processing stream for {symbol}: {e}")
    
    async def _update_indicators(self, symbol: str):
        """Update indicators for symbol"""
        
        if len(self.data_buffer[symbol]) < 20:
            return
        
        # Convert to price series
        prices = pd.Series([tick.last_price for tick in self.data_buffer[symbol]])
        
        # Calculate indicators
        sma_20 = TechnicalIndicators.sma(prices, 20).iloc[-1]
        rsi = TechnicalIndicators.rsi(prices).iloc[-1]
        
        self.indicators_cache[symbol] = {
            'sma_20': sma_20,
            'rsi': rsi,
            'last_price': prices.iloc[-1],
            'timestamp': time.time()
        }
        
        print(f"{symbol}: Price={prices.iloc[-1]:.2f}, SMA20={sma_20:.2f}, RSI={rsi:.1f}")
    
    async def _check_alerts(self, symbol: str, tick):
        """Check for alert conditions"""
        
        if symbol not in self.indicators_cache:
            return
        
        indicators = self.indicators_cache[symbol]
        
        # Price alerts
        if tick.last_price > indicators['sma_20'] * 1.05:
            print(f"🚨 ALERT: {symbol} price {tick.last_price:.2f} is 5% above SMA20")
        
        # RSI alerts
        if indicators['rsi'] > 80:
            print(f"🚨 ALERT: {symbol} RSI {indicators['rsi']:.1f} is overbought")
        elif indicators['rsi'] < 20:
            print(f"🚨 ALERT: {symbol} RSI {indicators['rsi']:.1f} is oversold")


class PerformanceAnalyzer:
    """
    Performance analysis and optimization tools
    """
    
    def __init__(self):
        self.profiler = PerformanceProfiler()
    
    def benchmark_operations(self, client: FinancialDataClient):
        """Benchmark various SDK operations"""
        
        print("Running performance benchmarks...")
        
        # Benchmark data retrieval
        with self.profiler.time_operation("get_tick_data"):
            for _ in range(10):
                client.get_tick_data("AAPL", limit=100)
        
        with self.profiler.time_operation("get_kline_data"):
            for _ in range(10):
                client.get_kline_data("AAPL", period="1m", limit=100)
        
        # Benchmark batch operations
        requests = [
            {'type': 'tick', 'symbol': 'AAPL', 'limit': 50},
            {'type': 'tick', 'symbol': 'GOOGL', 'limit': 50},
            {'type': 'kline', 'symbol': 'MSFT', 'period': '1m', 'limit': 50}
        ]
        
        with self.profiler.time_operation("batch_requests"):
            for _ in range(5):
                client.get_batch_data(requests, parallel=True)
        
        # Benchmark indicators
        sample_data = pd.Series(np.random.randn(1000).cumsum() + 100)
        
        with self.profiler.time_operation("calculate_sma"):
            for _ in range(100):
                TechnicalIndicators.sma(sample_data, 20)
        
        with self.profiler.time_operation("calculate_rsi"):
            for _ in range(100):
                TechnicalIndicators.rsi(sample_data)
        
        # Print results
        stats = self.profiler.get_stats()
        print("\nPerformance Results:")
        print("-" * 50)
        
        for operation, timing_stats in stats['timings'].items():
            print(f"{operation}:")
            print(f"  Average: {timing_stats['average']*1000:.2f}ms")
            print(f"  P95: {timing_stats['p95']*1000:.2f}ms")
            print(f"  Count: {timing_stats['count']}")
    
    def analyze_cache_performance(self, client: FinancialDataClient):
        """Analyze cache performance"""
        
        if not client.cache:
            print("Cache is disabled")
            return
        
        print("\nCache Performance Analysis:")
        print("-" * 30)
        
        # Clear cache and measure cold performance
        client.clear_cache()
        
        start_time = time.time()
        for i in range(50):
            client.get_tick_data(f"SYMBOL_{i%5}", limit=100)
        cold_time = time.time() - start_time
        
        # Measure warm cache performance
        start_time = time.time()
        for i in range(50):
            client.get_tick_data(f"SYMBOL_{i%5}", limit=100)
        warm_time = time.time() - start_time
        
        cache_stats = client.cache.stats()
        
        print(f"Cold cache time: {cold_time:.2f}s")
        print(f"Warm cache time: {warm_time:.2f}s")
        print(f"Cache hit rate: {cache_stats['hit_rate']:.2%}")
        print(f"Cache size: {cache_stats['size']}/{cache_stats['max_size']}")


class DataVisualization:
    """
    Data visualization utilities
    """
    
    @staticmethod
    def plot_price_with_indicators(df: pd.DataFrame, symbol: str):
        """Plot price data with technical indicators"""
        
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10), 
                                           gridspec_kw={'height_ratios': [3, 1, 1]})
        
        # Price and moving averages
        ax1.plot(df.index, df['close'], label='Close Price', linewidth=1)
        if 'sma_20' in df.columns:
            ax1.plot(df.index, df['sma_20'], label='SMA 20', alpha=0.7)
        if 'sma_50' in df.columns:
            ax1.plot(df.index, df['sma_50'], label='SMA 50', alpha=0.7)
        
        # Bollinger Bands
        if all(col in df.columns for col in ['bb_upper', 'bb_lower']):
            ax1.fill_between(df.index, df['bb_upper'], df['bb_lower'], 
                           alpha=0.2, label='Bollinger Bands')
        
        ax1.set_title(f'{symbol} Price Chart with Indicators')
        ax1.set_ylabel('Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # RSI
        if 'rsi' in df.columns:
            ax2.plot(df.index, df['rsi'], label='RSI', color='orange')
            ax2.axhline(y=70, color='r', linestyle='--', alpha=0.7)
            ax2.axhline(y=30, color='g', linestyle='--', alpha=0.7)
            ax2.set_ylabel('RSI')
            ax2.set_ylim(0, 100)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # MACD
        if all(col in df.columns for col in ['macd', 'macd_signal']):
            ax3.plot(df.index, df['macd'], label='MACD', color='blue')
            ax3.plot(df.index, df['macd_signal'], label='Signal', color='red')
            if 'macd_histogram' in df.columns:
                ax3.bar(df.index, df['macd_histogram'], alpha=0.3, label='Histogram')
            ax3.set_ylabel('MACD')
            ax3.set_xlabel('Time')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def plot_backtest_results(results: dict):
        """Plot backtest results for multiple symbols"""
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        symbols = list(results.keys())
        returns = [results[s]['backtest']['total_return'] for s in symbols]
        sharpe_ratios = [results[s]['sharpe_ratio'] for s in symbols]
        max_drawdowns = [results[s]['max_drawdown'] for s in symbols]
        
        # Returns comparison
        ax1.bar(symbols, returns)
        ax1.set_title('Total Returns by Symbol')
        ax1.set_ylabel('Return')
        ax1.tick_params(axis='x', rotation=45)
        
        # Sharpe ratios
        ax2.bar(symbols, sharpe_ratios)
        ax2.set_title('Sharpe Ratios by Symbol')
        ax2.set_ylabel('Sharpe Ratio')
        ax2.tick_params(axis='x', rotation=45)
        
        # Max drawdowns
        ax3.bar(symbols, max_drawdowns)
        ax3.set_title('Maximum Drawdowns by Symbol')
        ax3.set_ylabel('Max Drawdown')
        ax3.tick_params(axis='x', rotation=45)
        
        # Risk-Return scatter
        ax4.scatter(max_drawdowns, returns)
        for i, symbol in enumerate(symbols):
            ax4.annotate(symbol, (max_drawdowns[i], returns[i]))
        ax4.set_xlabel('Max Drawdown')
        ax4.set_ylabel('Total Return')
        ax4.set_title('Risk-Return Profile')
        
        plt.tight_layout()
        plt.show()


async def advanced_async_example():
    """Advanced async usage example"""
    
    print("=== Advanced Async Example ===")
    
    servers = ["localhost:50051", "localhost:50052"]
    
    async with AsyncFinancialDataClient(servers) as client:
        
        # Real-time data processing
        processor = RealTimeDataProcessor(client)
        
        print("Starting real-time data processing...")
        await processor.process_real_time_stream(
            symbols=["AAPL", "GOOGL", "MSFT"],
            duration_seconds=30
        )
        
        print("Real-time processing completed")


def advanced_sync_example():
    """Advanced sync usage example"""
    
    print("=== Advanced Sync Example ===")
    
    servers = ["localhost:50051", "localhost:50052"]
    client = FinancialDataClient(servers, enable_cache=True)
    
    try:
        # Advanced trading strategy
        strategy = AdvancedTradingStrategy(client)
        
        symbols = ["AAPL", "GOOGL", "MSFT", "TSLA"]
        backtest_results = strategy.backtest_strategy(
            symbols=symbols,
            start_date="2024-01-01",
            end_date="2024-02-01"
        )
        
        if backtest_results:
            print(f"\nBacktest completed for {len(backtest_results)} symbols")
            
            # Visualize results
            DataVisualization.plot_backtest_results(backtest_results)
            
            # Plot individual symbol
            for symbol, result in list(backtest_results.items())[:1]:  # First symbol only
                DataVisualization.plot_price_with_indicators(result['data'], symbol)
        
        # Performance analysis
        analyzer = PerformanceAnalyzer()
        analyzer.benchmark_operations(client)
        analyzer.analyze_cache_performance(client)
        
    except Exception as e:
        logger.error(f"Advanced sync example error: {e}")
    
    finally:
        client.close()


def persistent_cache_example():
    """Example using persistent cache"""
    
    print("=== Persistent Cache Example ===")
    
    # Create persistent cache
    persistent_cache = PersistentCache(cache_dir="./data_cache", max_size=1000)
    
    # Test cache operations
    test_data = pd.DataFrame({
        'timestamp': range(100),
        'price': np.random.randn(100).cumsum() + 100
    })
    
    # Store data
    persistent_cache.put("test_data", test_data)
    print("Data stored in persistent cache")
    
    # Retrieve data
    retrieved_data = persistent_cache.get("test_data")
    if retrieved_data is not None:
        print(f"Retrieved data shape: {retrieved_data.shape}")
    
    # Cache statistics
    stats = persistent_cache.stats()
    print(f"Cache stats: {stats}")
    
    # Cleanup
    persistent_cache.clear()
    print("Cache cleared")


def main():
    """Run advanced examples"""
    
    print("Financial Data SDK - Advanced Examples")
    print("=" * 60)
    
    # Run advanced sync example
    advanced_sync_example()
    
    # Run advanced async example
    asyncio.run(advanced_async_example())
    
    # Run persistent cache example
    persistent_cache_example()
    
    print("\n" + "=" * 60)
    print("Advanced examples completed!")


if __name__ == "__main__":
    main()