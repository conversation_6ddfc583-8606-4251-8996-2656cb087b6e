#include "data_sync.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <random>
#include <zlib.h>

namespace financial_data {

DataSyncManager::DataSyncManager(const DataSyncConfig& config)
    : config_(config)
    , logger_(spdlog::get("data_sync") ? spdlog::get("data_sync") : spdlog::default_logger())
    , running_(false)
    , operation_counter_(0) {
    
    logger_->info("Data Sync Manager initialized with {} peer nodes", config_.peer_nodes.size());
    
    // 初始化节点状态
    std::lock_guard<std::mutex> lock(node_status_mutex_);
    for (const auto& node : config_.peer_nodes) {
        node_sync_status_[node] = SyncStatus::IDLE;
    }
}

DataSyncManager::~DataSyncManager() {
    Stop();
    logger_->info("Data Sync Manager destroyed");
}

bool DataSyncManager::Start() {
    if (running_.load()) {
        logger_->warn("Data Sync Manager already running");
        return true;
    }
    
    logger_->info("Starting Data Sync Manager");
    
    running_ = true;
    
    // 启动同步工作线程
    sync_worker_thread_ = std::thread(&DataSyncManager::SyncWorkerThread, this);
    
    logger_->info("Data Sync Manager started successfully");
    return true;
}

void DataSyncManager::Stop() {
    if (!running_.load()) {
        return;
    }
    
    logger_->info("Stopping Data Sync Manager");
    
    running_ = false;
    
    // 等待工作线程结束
    if (sync_worker_thread_.joinable()) {
        sync_worker_thread_.join();
    }
    
    logger_->info("Data Sync Manager stopped");
}

void DataSyncManager::RegisterDataProvider(const std::string& data_type, DataProvider provider) {
    std::lock_guard<std::mutex> lock(providers_mutex_);
    data_providers_[data_type] = provider;
    logger_->info("Registered data provider for type: {}", data_type);
}

void DataSyncManager::RegisterDataConsumer(const std::string& data_type, DataConsumer consumer) {
    std::lock_guard<std::mutex> lock(providers_mutex_);
    data_consumers_[data_type] = consumer;
    logger_->info("Registered data consumer for type: {}", data_type);
}

bool DataSyncManager::RequestSync(const std::string& target_node, const std::string& data_type) {
    if (!running_.load()) {
        logger_->error("Data Sync Manager not running");
        return false;
    }
    
    // 检查队列大小
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        if (sync_queue_.size() >= static_cast<size_t>(config_.max_queue_size)) {
            logger_->error("Sync queue is full, cannot add new operation");
            return false;
        }
    }
    
    // 获取数据
    std::vector<uint8_t> data;
    {
        std::lock_guard<std::mutex> lock(providers_mutex_);
        auto it = data_providers_.find(data_type);
        if (it == data_providers_.end()) {
            logger_->error("No data provider registered for type: {}", data_type);
            return false;
        }
        
        try {
            data = it->second(data_type);
        } catch (const std::exception& e) {
            logger_->error("Error getting data from provider: {}", e.what());
            return false;
        }
    }
    
    // 创建同步操作
    SyncOperation operation;
    operation.operation_id = GenerateOperationId();
    operation.target_node = target_node;
    operation.data_type = data_type;
    operation.data = std::move(data);
    operation.timestamp = std::chrono::steady_clock::now();
    operation.status = SyncStatus::IDLE;
    
    // 添加到队列
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        sync_queue_.push(operation);
    }
    
    logger_->info("Sync operation {} queued for node {} (data type: {})", 
                 operation.operation_id, target_node, data_type);
    
    return true;
}

bool DataSyncManager::RequestSyncToAll(const std::string& data_type) {
    bool all_success = true;
    
    for (const auto& node : config_.peer_nodes) {
        if (!RequestSync(node, data_type)) {
            all_success = false;
        }
    }
    
    return all_success;
}

bool DataSyncManager::TriggerFullSync() {
    logger_->info("Triggering full sync to all nodes");
    
    bool all_success = true;
    
    // 获取所有注册的数据类型
    std::vector<std::string> data_types;
    {
        std::lock_guard<std::mutex> lock(providers_mutex_);
        for (const auto& [type, provider] : data_providers_) {
            data_types.push_back(type);
        }
    }
    
    // 同步所有数据类型到所有节点
    for (const auto& data_type : data_types) {
        if (!RequestSyncToAll(data_type)) {
            all_success = false;
        }
    }
    
    return all_success;
}

SyncStats DataSyncManager::GetSyncStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    SyncStats stats = stats_;
    
    // 更新待处理操作数量
    {
        std::lock_guard<std::mutex> queue_lock(queue_mutex_);
        stats.pending_operations = static_cast<int>(sync_queue_.size());
    }
    
    return stats;
}

int DataSyncManager::GetPendingOperations() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return static_cast<int>(sync_queue_.size());
}

SyncStatus DataSyncManager::GetNodeSyncStatus(const std::string& node_id) const {
    std::lock_guard<std::mutex> lock(node_status_mutex_);
    auto it = node_sync_status_.find(node_id);
    if (it != node_sync_status_.end()) {
        return it->second;
    }
    return SyncStatus::IDLE;
}

void DataSyncManager::CleanupFailedOperations() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    std::queue<SyncOperation> cleaned_queue;
    int cleaned_count = 0;
    
    while (!sync_queue_.empty()) {
        SyncOperation operation = sync_queue_.front();
        sync_queue_.pop();
        
        if (operation.status != SyncStatus::FAILED || 
            operation.retry_count < config_.max_retry_attempts) {
            cleaned_queue.push(operation);
        } else {
            cleaned_count++;
        }
    }
    
    sync_queue_ = std::move(cleaned_queue);
    
    if (cleaned_count > 0) {
        logger_->info("Cleaned up {} failed sync operations", cleaned_count);
    }
}

void DataSyncManager::SyncWorkerThread() {
    logger_->info("Sync worker thread started");
    
    while (running_.load()) {
        try {
            SyncOperation operation;
            bool has_operation = false;
            
            // 从队列获取操作
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);
                if (!sync_queue_.empty()) {
                    operation = sync_queue_.front();
                    sync_queue_.pop();
                    has_operation = true;
                }
            }
            
            if (has_operation) {
                ProcessSyncOperation(operation);
            } else {
                // 没有操作时短暂休眠
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
            
        } catch (const std::exception& e) {
            logger_->error("Error in sync worker thread: {}", e.what());
        }
    }
    
    logger_->info("Sync worker thread stopped");
}

void DataSyncManager::ProcessSyncOperation(SyncOperation& operation) {
    auto start_time = std::chrono::steady_clock::now();
    
    logger_->debug("Processing sync operation {} to node {}", 
                  operation.operation_id, operation.target_node);
    
    // 更新节点状态
    {
        std::lock_guard<std::mutex> lock(node_status_mutex_);
        node_sync_status_[operation.target_node] = SyncStatus::SYNCING;
    }
    
    operation.status = SyncStatus::SYNCING;
    
    try {
        // 压缩数据（如果启用）
        std::vector<uint8_t> processed_data = operation.data;
        if (config_.enable_compression) {
            processed_data = CompressData(processed_data);
        }
        
        // 加密数据（如果启用）
        if (config_.enable_encryption) {
            processed_data = EncryptData(processed_data);
        }
        
        // 更新操作数据
        operation.data = std::move(processed_data);
        
        // 发送数据
        bool success = SendDataToNode(operation.target_node, operation);
        
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        if (success) {
            operation.status = SyncStatus::SUCCESS;
            logger_->info("Sync operation {} completed successfully in {}ms", 
                         operation.operation_id, duration);
        } else {
            operation.status = SyncStatus::FAILED;
            operation.retry_count++;
            
            if (operation.retry_count < config_.max_retry_attempts) {
                logger_->warn("Sync operation {} failed, will retry ({}/{})", 
                             operation.operation_id, operation.retry_count, config_.max_retry_attempts);
                
                // 重新加入队列进行重试
                std::this_thread::sleep_for(std::chrono::milliseconds(config_.retry_delay_ms));
                std::lock_guard<std::mutex> lock(queue_mutex_);
                sync_queue_.push(operation);
            } else {
                logger_->error("Sync operation {} failed after {} attempts", 
                              operation.operation_id, config_.max_retry_attempts);
            }
        }
        
        // 更新统计信息
        UpdateStats(operation, success, static_cast<double>(duration));
        
        // 更新节点状态
        {
            std::lock_guard<std::mutex> lock(node_status_mutex_);
            node_sync_status_[operation.target_node] = success ? SyncStatus::SUCCESS : SyncStatus::FAILED;
        }
        
        // 调用回调函数
        if (sync_callback_) {
            sync_callback_(operation);
        }
        
    } catch (const std::exception& e) {
        operation.status = SyncStatus::FAILED;
        operation.error_message = e.what();
        
        logger_->error("Sync operation {} failed with exception: {}", 
                      operation.operation_id, e.what());
        
        // 更新节点状态
        {
            std::lock_guard<std::mutex> lock(node_status_mutex_);
            node_sync_status_[operation.target_node] = SyncStatus::FAILED;
        }
        
        // 更新统计信息
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        UpdateStats(operation, false, static_cast<double>(duration));
    }
}

bool DataSyncManager::SendDataToNode(const std::string& target_node, const SyncOperation& operation) {
    // 在实际实现中，这里会通过网络发送数据到目标节点
    // 现在只是模拟发送过程
    
    logger_->debug("Sending {} bytes to node {} (operation: {})", 
                  operation.data.size(), target_node, operation.operation_id);
    
    // 模拟网络延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // 模拟成功率（90%成功）
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_real_distribution<> dis(0.0, 1.0);
    
    bool success = dis(gen) < 0.9;  // 90%成功率
    
    if (success) {
        logger_->debug("Data sent successfully to node {}", target_node);
    } else {
        logger_->warn("Failed to send data to node {}", target_node);
    }
    
    return success;
}

bool DataSyncManager::ReceiveDataFromNode(const std::string& source_node, const SyncOperation& operation) {
    logger_->debug("Receiving data from node {} (operation: {})", 
                  source_node, operation.operation_id);
    
    try {
        // 解密数据（如果启用）
        std::vector<uint8_t> processed_data = operation.data;
        if (config_.enable_encryption) {
            processed_data = DecryptData(processed_data);
        }
        
        // 解压数据（如果启用）
        if (config_.enable_compression) {
            processed_data = DecompressData(processed_data);
        }
        
        // 找到对应的数据消费者
        std::lock_guard<std::mutex> lock(providers_mutex_);
        auto it = data_consumers_.find(operation.data_type);
        if (it == data_consumers_.end()) {
            logger_->error("No data consumer registered for type: {}", operation.data_type);
            return false;
        }
        
        // 消费数据
        bool success = it->second(operation.data_type, processed_data);
        
        if (success) {
            logger_->debug("Data received and processed successfully from node {}", source_node);
        } else {
            logger_->error("Failed to process received data from node {}", source_node);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        logger_->error("Error receiving data from node {}: {}", source_node, e.what());
        return false;
    }
}

std::vector<uint8_t> DataSyncManager::CompressData(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return data;
    }
    
    // 使用zlib进行压缩
    uLongf compressed_size = compressBound(static_cast<uLong>(data.size()));
    std::vector<uint8_t> compressed_data(compressed_size);
    
    int result = compress(compressed_data.data(), &compressed_size, 
                         data.data(), static_cast<uLong>(data.size()));
    
    if (result != Z_OK) {
        logger_->warn("Data compression failed, using original data");
        return data;
    }
    
    compressed_data.resize(compressed_size);
    
    logger_->debug("Data compressed from {} to {} bytes (ratio: {:.2f})", 
                  data.size(), compressed_size, 
                  static_cast<double>(compressed_size) / data.size());
    
    return compressed_data;
}

std::vector<uint8_t> DataSyncManager::DecompressData(const std::vector<uint8_t>& compressed_data) {
    if (compressed_data.empty()) {
        return compressed_data;
    }
    
    // 估算解压后的大小（假设压缩比为50%）
    uLongf decompressed_size = compressed_data.size() * 2;
    std::vector<uint8_t> decompressed_data(decompressed_size);
    
    int result = uncompress(decompressed_data.data(), &decompressed_size,
                           compressed_data.data(), static_cast<uLong>(compressed_data.size()));
    
    if (result != Z_OK) {
        logger_->error("Data decompression failed");
        return {};
    }
    
    decompressed_data.resize(decompressed_size);
    
    logger_->debug("Data decompressed from {} to {} bytes", 
                  compressed_data.size(), decompressed_size);
    
    return decompressed_data;
}

std::vector<uint8_t> DataSyncManager::EncryptData(const std::vector<uint8_t>& data) {
    // 简单的XOR加密（仅用于演示）
    std::vector<uint8_t> encrypted_data = data;
    const uint8_t key = 0xAB;  // 简单的密钥
    
    for (auto& byte : encrypted_data) {
        byte ^= key;
    }
    
    return encrypted_data;
}

std::vector<uint8_t> DataSyncManager::DecryptData(const std::vector<uint8_t>& encrypted_data) {
    // XOR解密（与加密相同）
    return EncryptData(encrypted_data);
}

std::string DataSyncManager::GenerateOperationId() {
    auto now = std::chrono::steady_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
        now.time_since_epoch()).count();
    
    int counter = operation_counter_.fetch_add(1);
    
    std::stringstream ss;
    ss << "sync_" << timestamp << "_" << counter;
    return ss.str();
}

void DataSyncManager::UpdateStats(const SyncOperation& operation, bool success, double duration_ms) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    stats_.total_operations++;
    if (success) {
        stats_.successful_operations++;
    } else {
        stats_.failed_operations++;
    }
    
    stats_.last_sync_time = std::chrono::steady_clock::now();
    stats_.total_bytes_synced += static_cast<int64_t>(operation.data.size());
    
    // 更新平均同步时间
    if (stats_.total_operations > 0) {
        stats_.average_sync_time_ms = 
            (stats_.average_sync_time_ms * (stats_.total_operations - 1) + duration_ms) / 
            stats_.total_operations;
    }
}

// MemoryDataSync 实现
std::vector<uint8_t> MemoryDataSync::SerializeTickData(const std::vector<uint8_t>& tick_data) {
    // 简单的序列化实现
    std::vector<uint8_t> serialized;
    
    // 添加数据长度头部
    uint32_t size = static_cast<uint32_t>(tick_data.size());
    serialized.resize(sizeof(uint32_t) + tick_data.size());
    
    std::memcpy(serialized.data(), &size, sizeof(uint32_t));
    std::memcpy(serialized.data() + sizeof(uint32_t), tick_data.data(), tick_data.size());
    
    return serialized;
}

std::vector<uint8_t> MemoryDataSync::DeserializeTickData(const std::vector<uint8_t>& serialized_data) {
    if (serialized_data.size() < sizeof(uint32_t)) {
        return {};
    }
    
    uint32_t size;
    std::memcpy(&size, serialized_data.data(), sizeof(uint32_t));
    
    if (serialized_data.size() < sizeof(uint32_t) + size) {
        return {};
    }
    
    std::vector<uint8_t> tick_data(size);
    std::memcpy(tick_data.data(), serialized_data.data() + sizeof(uint32_t), size);
    
    return tick_data;
}

std::vector<uint8_t> MemoryDataSync::SerializeLevel2Data(const std::vector<uint8_t>& level2_data) {
    return SerializeTickData(level2_data);  // 使用相同的序列化方法
}

std::vector<uint8_t> MemoryDataSync::DeserializeLevel2Data(const std::vector<uint8_t>& serialized_data) {
    return DeserializeTickData(serialized_data);  // 使用相同的反序列化方法
}

std::vector<uint8_t> MemoryDataSync::SerializeConfiguration(const std::string& config_json) {
    std::vector<uint8_t> serialized(config_json.begin(), config_json.end());
    return serialized;
}

std::string MemoryDataSync::DeserializeConfiguration(const std::vector<uint8_t>& serialized_data) {
    return std::string(serialized_data.begin(), serialized_data.end());
}

} // namespace financial_data