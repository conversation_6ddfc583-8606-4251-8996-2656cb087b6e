"""
Storage Integration Demo

This script demonstrates the integration between PyTDX collector and the storage manager,
showing how historical data is automatically converted to StandardTick format and stored.
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from collectors.pytdx_collector import (
    PyTDXCollector, 
    PyTDXConfig, 
    ArchiverConfig,
    MockStorageManager
)


async def demo_k_data_storage():
    """Demonstrate K-line data storage integration"""
    print("=== K-line Data Storage Demo ===")
    
    # Create storage manager
    storage_manager = MockStorageManager()
    
    # Create configuration
    archiver_config = ArchiverConfig(
        enable_data_validation=True,
        enable_deduplication=True,
        archive_batch_size=1000
    )
    
    config = PyTDXConfig(
        batch_size=100,
        archive_enabled=False,  # Use direct storage instead of archiver
        archiver_config=archiver_config
    )
    
    # Create collector
    collector = PyTDXCollector(config, storage_manager)
    
    # Create sample K-line data for multiple symbols
    dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
    
    symbols = ['000001', '000002', '000858']
    data_list = []
    
    for i, symbol in enumerate(symbols):
        # Generate realistic K-line data
        base_price = 10 + i * 5
        price_data = np.random.normal(base_price, 1, 30)
        price_data = np.maximum(price_data, 1)  # Ensure positive prices
        
        k_data = pd.DataFrame({
            'open': price_data + np.random.normal(0, 0.1, 30),
            'high': price_data + np.abs(np.random.normal(0.5, 0.2, 30)),
            'low': price_data - np.abs(np.random.normal(0.5, 0.2, 30)),
            'close': price_data,
            'volume': np.random.randint(1000, 50000, 30),
            'amount': price_data * np.random.randint(1000, 50000, 30)
        }, index=dates)
        
        # Ensure OHLC relationships are valid
        for j in range(30):
            k_data.iloc[j, k_data.columns.get_loc('high')] = max(
                k_data.iloc[j]['open'], k_data.iloc[j]['high'], 
                k_data.iloc[j]['low'], k_data.iloc[j]['close']
            )
            k_data.iloc[j, k_data.columns.get_loc('low')] = min(
                k_data.iloc[j]['open'], k_data.iloc[j]['high'], 
                k_data.iloc[j]['low'], k_data.iloc[j]['close']
            )
        
        data_list.append(k_data)
    
    # Store data using batch storage
    print(f"Storing K-line data for {len(symbols)} symbols...")
    success = await collector.batch_store_data_async(data_list, symbols, 'kline_D')
    
    if success:
        print("✓ K-line data stored successfully")
        
        # Get stored data
        stored_data = storage_manager.get_stored_data()
        print(f"✓ Total records stored: {len(stored_data)}")
        
        # Show statistics by symbol
        symbol_counts = {}
        for tick in stored_data:
            symbol = tick['symbol']
            symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
        
        print("✓ Records per symbol:")
        for symbol, count in symbol_counts.items():
            print(f"  - {symbol}: {count} records")
        
        # Show sample record
        if stored_data:
            sample_record = stored_data[0]
            print(f"✓ Sample record structure:")
            for key, value in sample_record.items():
                if key in ['timestamp_ns', 'collection_timestamp_ns']:
                    # Convert timestamp to readable format
                    dt = datetime.fromtimestamp(value / 1_000_000_000)
                    print(f"  - {key}: {value} ({dt})")
                else:
                    print(f"  - {key}: {value}")
    else:
        print("✗ Failed to store K-line data")
    
    # Get storage statistics
    stats = collector.get_storage_statistics()
    print(f"✓ Storage statistics: {stats}")
    
    print()


async def demo_realtime_quotes_storage():
    """Demonstrate realtime quotes storage integration"""
    print("=== Realtime Quotes Storage Demo ===")
    
    storage_manager = MockStorageManager()
    config = PyTDXConfig(archive_enabled=False)
    collector = PyTDXCollector(config, storage_manager)
    
    # Create sample realtime quotes
    quotes = []
    symbols = ['000001', '000002', '000858', '600000', '600036']
    
    for i, symbol in enumerate(symbols):
        base_price = 15 + i * 2
        quote = {
            'code': symbol,
            'name': f'Stock {symbol}',
            'price': base_price + np.random.normal(0, 0.5),
            'last_close': base_price - 0.5 + np.random.normal(0, 0.2),
            'open': base_price - 0.3 + np.random.normal(0, 0.3),
            'high': base_price + 1 + np.random.normal(0, 0.3),
            'low': base_price - 1 + np.random.normal(0, 0.3),
            'volume': np.random.randint(10000, 100000),
            'amount': (base_price + np.random.normal(0, 0.5)) * np.random.randint(10000, 100000),
            'bid1': base_price - 0.01,
            'ask1': base_price + 0.01,
            'bid1_vol': np.random.randint(1000, 10000),
            'ask1_vol': np.random.randint(1000, 10000),
            'timestamp': datetime.now().isoformat()
        }
        quotes.append(quote)
    
    print(f"Storing realtime quotes for {len(quotes)} symbols...")
    success = await collector.store_realtime_data_async(quotes)
    
    if success:
        print("✓ Realtime quotes stored successfully")
        
        stored_data = storage_manager.get_stored_data()
        print(f"✓ Total quotes stored: {len(stored_data)}")
        
        # Show sample realtime quote
        if stored_data:
            sample_quote = stored_data[0]
            print(f"✓ Sample realtime quote:")
            print(f"  - Symbol: {sample_quote['symbol']}")
            print(f"  - Last Price: {sample_quote['last_price']}")
            print(f"  - Bid/Ask: {sample_quote['bid_price']}/{sample_quote['ask_price']}")
            print(f"  - Volume: {sample_quote['volume']}")
            print(f"  - Data Type: {sample_quote['data_type']}")
            print(f"  - Storage Layer: {sample_quote['storage_layer']}")
    else:
        print("✗ Failed to store realtime quotes")
    
    print()


async def demo_data_quality_features():
    """Demonstrate data quality and validation features"""
    print("=== Data Quality Features Demo ===")
    
    storage_manager = MockStorageManager()
    config = PyTDXConfig(archive_enabled=False)
    collector = PyTDXCollector(config, storage_manager)
    
    # Create data with quality issues
    dates = pd.date_range(start='2024-01-01', periods=10, freq='D')
    
    problematic_data = pd.DataFrame({
        'open': [10, -5, 15, 0, 12, 18, 20, 22, 25, 28],  # Negative and zero prices
        'high': [15, 20, 18, 10, 15, 22, 25, 28, 30, 32],
        'low': [8, 3, 12, 5, 10, 16, 18, 20, 23, 26],
        'close': [12, 18, 16, 8, 14, 20, 23, 26, 28, 30],
        'volume': [1000, -100, 2000, 500, 1500, 3000, 2500, 4000, 3500, 5000],  # Negative volume
        'amount': [15000, 25000, 20000, 8000, 18000, 35000, 40000, 50000, 45000, 60000]
    }, index=dates)
    
    # Fix OHLC relationships for valid rows
    for i in range(10):
        if problematic_data.iloc[i]['open'] > 0:
            row = problematic_data.iloc[i]
            prices = [row['open'], row['high'], row['low'], row['close']]
            valid_prices = [p for p in prices if p > 0]
            if valid_prices:
                problematic_data.iloc[i, problematic_data.columns.get_loc('high')] = max(valid_prices)
                problematic_data.iloc[i, problematic_data.columns.get_loc('low')] = min(valid_prices)
    
    print("Storing data with quality issues...")
    print(f"Original data points: {len(problematic_data)}")
    
    success = await collector.batch_store_data_async([problematic_data], ['TEST001'], 'kline_D')
    
    if success:
        stored_data = storage_manager.get_stored_data()
        print(f"✓ Valid data points stored: {len(stored_data)}")
        print(f"✓ Filtered out {len(problematic_data) - len(stored_data)} invalid records")
        
        # Verify all stored data is valid
        all_valid = True
        for tick in stored_data:
            if tick['last_price'] <= 0 or tick['volume'] < 0:
                all_valid = False
                break
        
        if all_valid:
            print("✓ All stored data passed validation")
        else:
            print("✗ Some invalid data was stored")
    else:
        print("✗ Failed to store data")
    
    print()


async def demo_performance_features():
    """Demonstrate performance and batch processing features"""
    print("=== Performance Features Demo ===")
    
    storage_manager = MockStorageManager()
    config = PyTDXConfig(archive_enabled=False)
    collector = PyTDXCollector(config, storage_manager)
    
    # Create large dataset
    print("Creating large dataset for performance testing...")
    
    symbols = [f'00000{i:02d}' for i in range(10)]  # 10 symbols
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')  # 100 days
    
    data_list = []
    for symbol in symbols:
        # Generate random but realistic data
        base_price = np.random.uniform(10, 50)
        prices = base_price + np.cumsum(np.random.normal(0, 0.1, 100))
        prices = np.maximum(prices, 1)  # Ensure positive
        
        k_data = pd.DataFrame({
            'open': prices + np.random.normal(0, 0.05, 100),
            'high': prices + np.abs(np.random.normal(0.2, 0.1, 100)),
            'low': prices - np.abs(np.random.normal(0.2, 0.1, 100)),
            'close': prices,
            'volume': np.random.randint(1000, 100000, 100),
            'amount': prices * np.random.randint(1000, 100000, 100)
        }, index=dates)
        
        # Fix OHLC relationships
        for i in range(100):
            k_data.iloc[i, k_data.columns.get_loc('high')] = max(
                k_data.iloc[i]['open'], k_data.iloc[i]['high'], 
                k_data.iloc[i]['low'], k_data.iloc[i]['close']
            )
            k_data.iloc[i, k_data.columns.get_loc('low')] = min(
                k_data.iloc[i]['open'], k_data.iloc[i]['high'], 
                k_data.iloc[i]['low'], k_data.iloc[i]['close']
            )
        
        data_list.append(k_data)
    
    # Measure performance
    start_time = datetime.now()
    
    print(f"Batch storing data for {len(symbols)} symbols, {len(dates)} days each...")
    success = await collector.batch_store_data_async(data_list, symbols, 'kline_D')
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    if success:
        stored_data = storage_manager.get_stored_data()
        total_records = len(stored_data)
        
        print(f"✓ Performance Results:")
        print(f"  - Total records processed: {total_records}")
        print(f"  - Processing time: {duration:.2f} seconds")
        print(f"  - Records per second: {total_records/duration:.0f}")
        print(f"  - Symbols processed: {len(symbols)}")
        print(f"  - Average records per symbol: {total_records/len(symbols):.0f}")
    else:
        print("✗ Performance test failed")
    
    print()


async def main():
    """Run all demos"""
    print("PyTDX Storage Integration Demo")
    print("=" * 50)
    print()
    
    try:
        await demo_k_data_storage()
        await demo_realtime_quotes_storage()
        await demo_data_quality_features()
        await demo_performance_features()
        
        print("=" * 50)
        print("All demos completed successfully! ✓")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())