# Market Data Collection Enhancement - Troubleshooting Guide

## Overview

This guide provides comprehensive troubleshooting information for the Market Data Collection Enhancement system. It covers common issues, diagnostic procedures, and resolution steps.

## Quick Diagnostic Commands

### System Health Check
```bash
# Check overall system health
curl http://localhost:8080/health

# Check Kubernetes deployment status
kubectl get pods -n market-data
kubectl get services -n market-data
kubectl describe deployment market-data-collector -n market-data

# Check logs
kubectl logs -n market-data deployment/market-data-collector --tail=100
```

### Storage Health Check
```bash
# Redis health
kubectl exec -n market-data deployment/redis -- redis-cli ping

# ClickHouse health
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SELECT 1"

# MinIO health
kubectl exec -n market-data deployment/minio -- mc admin info local
```

## Common Issues and Solutions

### 1. Application Won't Start

#### Symptoms
- Pod stuck in `CrashLoopBackOff` state
- Application exits immediately
- Health check failures

#### Diagnostic Steps
```bash
# Check pod status
kubectl get pods -n market-data

# Check pod events
kubectl describe pod <pod-name> -n market-data

# Check application logs
kubectl logs <pod-name> -n market-data --previous

# Check configuration
kubectl get configmap market-data-config -n market-data -o yaml
```

#### Common Causes and Solutions

**Configuration Errors**
```bash
# Validate configuration
kubectl exec -n market-data <pod-name> -- /app/bin/financial_data_service --validate-config

# Check for missing required fields
kubectl logs <pod-name> -n market-data | grep -i "config\|error"
```

**Missing Dependencies**
```bash
# Check if storage services are running
kubectl get pods -n market-data | grep -E "(redis|clickhouse|minio)"

# Check service connectivity
kubectl exec -n market-data <app-pod> -- nc -zv redis-service 6379
kubectl exec -n market-data <app-pod> -- nc -zv clickhouse-service 9000
kubectl exec -n market-data <app-pod> -- nc -zv minio-service 9000
```

**Resource Constraints**
```bash
# Check resource usage
kubectl top pods -n market-data

# Check resource limits
kubectl describe pod <pod-name> -n market-data | grep -A 10 "Limits\|Requests"

# Increase resource limits if needed
kubectl patch deployment market-data-collector -n market-data -p '{"spec":{"template":{"spec":{"containers":[{"name":"market-data-collector","resources":{"limits":{"memory":"4Gi","cpu":"2000m"}}}]}}}}'
```

### 2. Data Collection Issues

#### Symptoms
- No new data being collected
- High error rates in collection
- Data gaps or inconsistencies

#### Diagnostic Steps
```bash
# Check collector status
curl http://localhost:8080/api/v1/collectors/status

# Check collection metrics
curl http://localhost:8080/api/v1/metrics/summary

# Check collector logs
kubectl logs -n market-data deployment/market-data-collector | grep -i "collector\|pytdx\|ctp"
```

#### pytdx Collection Issues

**Connection Problems**
```bash
# Test pytdx server connectivity
kubectl exec -n market-data <pod-name> -- python3 -c "
import socket
servers = [('**************', 7709), ('************', 7709)]
for host, port in servers:
    try:
        sock = socket.create_connection((host, port), timeout=10)
        print(f'{host}:{port} - OK')
        sock.close()
    except Exception as e:
        print(f'{host}:{port} - ERROR: {e}')
"
```

**Data Format Issues**
```bash
# Check data validation logs
kubectl logs -n market-data deployment/market-data-collector | grep -i "validation\|format\|parse"

# Test data parsing
kubectl exec -n market-data <pod-name> -- python3 -c "
from src.collectors.pytdx_collector import PytdxCollector
collector = PytdxCollector()
# Test with a known symbol
data = collector.get_k_data('000001.SZ', start='2024-01-01', end='2024-01-02')
print(f'Retrieved {len(data)} records')
"
```

#### CTP Collection Issues

**Authentication Problems**
```bash
# Check CTP configuration
kubectl exec -n market-data <pod-name> -- cat /app/config/ctp_config.json

# Check CTP connection logs
kubectl logs -n market-data deployment/market-data-collector | grep -i "ctp\|login\|auth"
```

**Market Hours Issues**
```bash
# Check if market is open
kubectl exec -n market-data <pod-name> -- python3 -c "
import datetime
now = datetime.datetime.now()
print(f'Current time: {now}')
# Check if within trading hours (9:30-15:00, 21:00-02:30)
"
```

### 3. Storage Issues

#### Redis (Hot Storage) Issues

**Connection Problems**
```bash
# Test Redis connection
kubectl exec -n market-data deployment/redis -- redis-cli ping

# Check Redis configuration
kubectl exec -n market-data deployment/redis -- redis-cli CONFIG GET "*"

# Check Redis memory usage
kubectl exec -n market-data deployment/redis -- redis-cli INFO memory
```

**Memory Issues**
```bash
# Check Redis memory usage
kubectl exec -n market-data deployment/redis -- redis-cli INFO memory | grep used_memory_human

# Check Redis configuration
kubectl exec -n market-data deployment/redis -- redis-cli CONFIG GET maxmemory

# Clear Redis cache if needed (CAUTION: This will delete data)
kubectl exec -n market-data deployment/redis -- redis-cli FLUSHDB
```

**Performance Issues**
```bash
# Check Redis slow queries
kubectl exec -n market-data deployment/redis -- redis-cli SLOWLOG GET 10

# Monitor Redis operations
kubectl exec -n market-data deployment/redis -- redis-cli MONITOR
```

#### ClickHouse (Warm Storage) Issues

**Connection Problems**
```bash
# Test ClickHouse connection
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SELECT 1"

# Check ClickHouse server status
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SELECT * FROM system.processes"

# Check database and tables
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SHOW DATABASES"
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SHOW TABLES FROM market_data"
```

**Query Performance Issues**
```bash
# Check running queries
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SELECT * FROM system.processes WHERE elapsed > 10"

# Check query log
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SELECT * FROM system.query_log WHERE event_time > now() - INTERVAL 1 HOUR ORDER BY event_time DESC LIMIT 10"

# Optimize tables
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "OPTIMIZE TABLE market_data.standard_ticks"
```

**Storage Space Issues**
```bash
# Check disk usage
kubectl exec -n market-data deployment/clickhouse -- df -h /var/lib/clickhouse

# Check table sizes
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "
SELECT 
    table,
    formatReadableSize(sum(bytes)) as size,
    sum(rows) as rows
FROM system.parts 
WHERE database = 'market_data' 
GROUP BY table
"

# Clean old partitions
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "
ALTER TABLE market_data.standard_ticks 
DROP PARTITION '202312'  -- Drop December 2023 partition
"
```

#### MinIO (Cold Storage) Issues

**Connection Problems**
```bash
# Test MinIO connection
kubectl exec -n market-data deployment/minio -- mc admin info local

# Check bucket access
kubectl exec -n market-data deployment/minio -- mc ls local/market-data-archive

# Test upload/download
kubectl exec -n market-data deployment/minio -- mc cp /tmp/test.txt local/market-data-archive/test.txt
kubectl exec -n market-data deployment/minio -- mc rm local/market-data-archive/test.txt
```

**Storage Space Issues**
```bash
# Check MinIO disk usage
kubectl exec -n market-data deployment/minio -- df -h /data

# Check bucket sizes
kubectl exec -n market-data deployment/minio -- mc du local/market-data-archive
```

### 4. Performance Issues

#### High CPU Usage
```bash
# Check CPU usage
kubectl top pods -n market-data

# Check application profiling
curl http://localhost:8080/debug/pprof/profile?seconds=30 > cpu_profile.prof

# Analyze with go tool pprof (if applicable)
go tool pprof cpu_profile.prof
```

#### High Memory Usage
```bash
# Check memory usage
kubectl top pods -n market-data

# Check memory profiling
curl http://localhost:8080/debug/pprof/heap > heap_profile.prof

# Check for memory leaks in logs
kubectl logs -n market-data deployment/market-data-collector | grep -i "memory\|leak\|oom"
```

#### Slow Query Performance
```bash
# Check query metrics
curl http://localhost:8080/api/v1/metrics/summary | jq '.performance'

# Enable query logging
kubectl patch configmap market-data-config -n market-data --patch '{"data":{"unified_config.json":"...\"monitoring\":{\"logging\":{\"level\":\"DEBUG\"}}..."}}'

# Analyze slow queries
kubectl logs -n market-data deployment/market-data-collector | grep -i "slow\|timeout\|query.*ms"
```

### 5. Network Issues

#### Service Discovery Problems
```bash
# Check service endpoints
kubectl get endpoints -n market-data

# Test service connectivity
kubectl exec -n market-data <pod-name> -- nslookup redis-service
kubectl exec -n market-data <pod-name> -- nslookup clickhouse-service
kubectl exec -n market-data <pod-name> -- nslookup minio-service
```

#### DNS Resolution Issues
```bash
# Check DNS configuration
kubectl exec -n market-data <pod-name> -- cat /etc/resolv.conf

# Test DNS resolution
kubectl exec -n market-data <pod-name> -- nslookup kubernetes.default.svc.cluster.local
```

#### Network Policies
```bash
# Check network policies
kubectl get networkpolicies -n market-data

# Test network connectivity
kubectl exec -n market-data <pod-name> -- nc -zv <target-service> <port>
```

### 6. Authentication and Authorization Issues

#### API Authentication Problems
```bash
# Test API without authentication
curl -v http://localhost:8080/health

# Test API with authentication
curl -v -H "Authorization: Bearer <token>" http://localhost:8080/api/v1/ticks/000001.SZ

# Check authentication logs
kubectl logs -n market-data deployment/market-data-collector | grep -i "auth\|token\|unauthorized"
```

#### Service Account Issues
```bash
# Check service account
kubectl get serviceaccount -n market-data

# Check RBAC permissions
kubectl auth can-i --list --as=system:serviceaccount:market-data:default -n market-data
```

## Monitoring and Alerting

### Prometheus Metrics
```bash
# Check if metrics are being exported
curl http://localhost:9090/metrics | grep market_data

# Common metrics to monitor
curl http://localhost:9090/metrics | grep -E "(data_collection_rate|error_rate|storage_usage|query_latency)"
```

### Log Analysis
```bash
# Aggregate error logs
kubectl logs -n market-data deployment/market-data-collector --since=1h | grep -i error | sort | uniq -c | sort -nr

# Check for specific error patterns
kubectl logs -n market-data deployment/market-data-collector | grep -E "(timeout|connection|failed|error)" | tail -20

# Monitor real-time logs
kubectl logs -n market-data deployment/market-data-collector -f
```

### Health Check Endpoints
```bash
# Application health
curl http://localhost:8080/health

# Readiness check
curl http://localhost:8080/ready

# Detailed component status
curl http://localhost:8080/api/v1/status/detailed
```

## Recovery Procedures

### 1. Application Recovery

**Restart Application**
```bash
# Restart deployment
kubectl rollout restart deployment/market-data-collector -n market-data

# Wait for rollout to complete
kubectl rollout status deployment/market-data-collector -n market-data
```

**Rollback to Previous Version**
```bash
# Check rollout history
kubectl rollout history deployment/market-data-collector -n market-data

# Rollback to previous version
kubectl rollout undo deployment/market-data-collector -n market-data
```

### 2. Data Recovery

**Redis Data Recovery**
```bash
# Check if Redis has persistence enabled
kubectl exec -n market-data deployment/redis -- redis-cli CONFIG GET save

# Restore from backup (if available)
kubectl exec -n market-data deployment/redis -- redis-cli DEBUG RESTART
```

**ClickHouse Data Recovery**
```bash
# Check for backup tables
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SHOW TABLES FROM market_data LIKE '%backup%'"

# Restore from backup
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "
INSERT INTO market_data.standard_ticks 
SELECT * FROM market_data.standard_ticks_backup 
WHERE timestamp_ns > (SELECT max(timestamp_ns) FROM market_data.standard_ticks)
"
```

**MinIO Data Recovery**
```bash
# List available backups
kubectl exec -n market-data deployment/minio -- mc ls local/market-data-backup

# Restore from backup
kubectl exec -n market-data deployment/minio -- mc cp --recursive local/market-data-backup/2024-01-15/ local/market-data-archive/
```

### 3. Configuration Recovery

**Restore Configuration**
```bash
# Restore from backup
kubectl create configmap market-data-config --from-file=config/unified_config.json.backup -n market-data --dry-run=client -o yaml | kubectl replace -f -

# Restart application to apply configuration
kubectl rollout restart deployment/market-data-collector -n market-data
```

## Performance Tuning

### 1. Collection Performance

**Optimize pytdx Collection**
```json
{
  "collection": {
    "pytdx": {
      "batch_size": 2000,           // Increase batch size
      "concurrent_requests": 8,     // Increase concurrency
      "connection_timeout_seconds": 15,  // Reduce timeout
      "archive_batch_size": 10000   // Increase archive batch size
    }
  }
}
```

**Optimize CTP Collection**
```json
{
  "collection": {
    "ctp": {
      "heartbeat_interval": 5,      // Reduce heartbeat interval
      "reconnect_delay_seconds": 5  // Reduce reconnect delay
    }
  }
}
```

### 2. Storage Performance

**Redis Optimization**
```bash
# Increase memory limit
kubectl patch deployment redis -n market-data -p '{"spec":{"template":{"spec":{"containers":[{"name":"redis","resources":{"limits":{"memory":"4Gi"}}}]}}}}'

# Enable Redis persistence
kubectl exec -n market-data deployment/redis -- redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

**ClickHouse Optimization**
```sql
-- Optimize table settings
ALTER TABLE market_data.standard_ticks 
MODIFY SETTING index_granularity = 8192,
                merge_max_block_size = 8192;

-- Create additional indexes
ALTER TABLE market_data.standard_ticks 
ADD INDEX idx_symbol_time (symbol, timestamp_ns) TYPE minmax GRANULARITY 1;
```

### 3. Query Performance

**Enable Query Caching**
```json
{
  "api": {
    "caching": {
      "enabled": true,
      "ttl_seconds": 300,
      "max_size_mb": 512
    }
  }
}
```

**Optimize Query Patterns**
```bash
# Use appropriate time ranges
curl "http://localhost:8080/api/v1/ticks/000001.SZ?start_time=2024-01-15T09:00:00Z&end_time=2024-01-15T10:00:00Z"

# Use pagination for large results
curl "http://localhost:8080/api/v1/ticks/000001.SZ?limit=1000&cursor=eyJ0aW1lc3RhbXAiOjE3MDUzMTI4MDB9"
```

## Emergency Procedures

### 1. System Overload

**Immediate Actions**
```bash
# Scale up application
kubectl scale deployment market-data-collector --replicas=3 -n market-data

# Increase resource limits
kubectl patch deployment market-data-collector -n market-data -p '{"spec":{"template":{"spec":{"containers":[{"name":"market-data-collector","resources":{"limits":{"memory":"8Gi","cpu":"4000m"}}}]}}}}'

# Enable rate limiting
curl -X PUT http://localhost:8080/api/v1/config -d '{"api":{"http":{"rate_limiting":{"enabled":true,"requests_per_minute":500}}}}'
```

### 2. Data Corruption

**Immediate Actions**
```bash
# Stop data collection
curl -X POST http://localhost:8080/api/v1/collectors/pytdx/stop
curl -X POST http://localhost:8080/api/v1/collectors/ctp/stop

# Backup current data
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "CREATE TABLE market_data.standard_ticks_emergency_backup AS market_data.standard_ticks"

# Analyze corruption extent
kubectl exec -n market-data deployment/clickhouse -- clickhouse-client --query "SELECT count(*) FROM market_data.standard_ticks WHERE price <= 0 OR volume <= 0"
```

### 3. Security Incident

**Immediate Actions**
```bash
# Disable external access
kubectl patch service market-data-collector-service -n market-data -p '{"spec":{"type":"ClusterIP"}}'

# Enable authentication
curl -X PUT http://localhost:8080/api/v1/config -d '{"security":{"authentication":{"enabled":true}}}'

# Check for suspicious activity
kubectl logs -n market-data deployment/market-data-collector | grep -E "(unauthorized|failed.*auth|suspicious)"
```

## Getting Help

### 1. Log Collection
```bash
# Collect all relevant logs
mkdir -p troubleshooting-$(date +%Y%m%d-%H%M%S)
cd troubleshooting-$(date +%Y%m%d-%H%M%S)

# Application logs
kubectl logs -n market-data deployment/market-data-collector --previous > app-logs-previous.txt
kubectl logs -n market-data deployment/market-data-collector > app-logs-current.txt

# Storage logs
kubectl logs -n market-data deployment/redis > redis-logs.txt
kubectl logs -n market-data deployment/clickhouse > clickhouse-logs.txt
kubectl logs -n market-data deployment/minio > minio-logs.txt

# System information
kubectl get all -n market-data > k8s-resources.txt
kubectl describe deployment market-data-collector -n market-data > deployment-details.txt
kubectl get events -n market-data --sort-by='.lastTimestamp' > events.txt
```

### 2. Configuration Export
```bash
# Export current configuration
kubectl get configmap market-data-config -n market-data -o yaml > current-config.yaml

# Export secrets (sanitized)
kubectl get secrets -n market-data -o yaml | sed 's/data:.*/data: <REDACTED>/' > secrets-sanitized.yaml
```

### 3. Performance Data
```bash
# Collect performance metrics
curl http://localhost:9090/metrics > metrics.txt
curl http://localhost:8080/api/v1/metrics/summary > summary-metrics.json

# Resource usage
kubectl top pods -n market-data > resource-usage.txt
kubectl top nodes > node-usage.txt
```

### 4. Contact Information

For additional support:
- Check the project documentation in `docs/`
- Review the API documentation at `docs/deployment/API_DOCUMENTATION.md`
- Check configuration guide at `docs/deployment/CONFIGURATION_GUIDE.md`
- Submit issues with collected logs and configuration details