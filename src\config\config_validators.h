#pragma once

#include "config_manager.h"
#include <regex>

namespace config {

// 基础配置验证器
class BaseConfigValidator : public ConfigValidator {
protected:
    bool ValidateRequired(const nlohmann::json& config, 
                         const std::vector<std::string>& required_keys,
                         ValidationResult& result) const;
    
    bool ValidateType(const nlohmann::json& config, 
                     const std::string& key,
                     nlohmann::json::value_t expected_type,
                     ValidationResult& result) const;
    
    bool ValidateRange(const nlohmann::json& config,
                      const std::string& key,
                      double min_value, double max_value,
                      ValidationResult& result) const;
    
    bool ValidateRegex(const nlohmann::json& config,
                      const std::string& key,
                      const std::string& pattern,
                      ValidationResult& result) const;
};

// 服务器配置验证器
class ServerConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "ServerConfigValidator"; }
};

// Redis配置验证器
class RedisConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "RedisConfigValidator"; }
};

// ClickHouse配置验证器
class ClickHouseConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "ClickHouseConfigValidator"; }
};

// CTP配置验证器
class CTPConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "CTPConfigValidator"; }
};

// 采集配置验证器
class CollectionConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "CollectionConfigValidator"; }
};

// 存储配置验证器
class StorageConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "StorageConfigValidator"; }
};

// 监控配置验证器
class MonitoringConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "MonitoringConfigValidator"; }
};

// 调度配置验证器
class SchedulingConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "SchedulingConfigValidator"; }
    
private:
    bool ValidateCronExpression(const std::string& cron_expr) const;
};

// 性能配置验证器
class PerformanceConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "PerformanceConfigValidator"; }
};

// 日志配置验证器
class LoggingConfigValidator : public BaseConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override { return "LoggingConfigValidator"; }
};

// 注册所有验证器的辅助函数
void RegisterAllValidators(ConfigManager& config_manager);

} // namespace config