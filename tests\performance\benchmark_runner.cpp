/**
 * @file benchmark_runner.cpp
 * @brief Main performance testing and benchmarking suite runner
 */

#include "benchmark_runner.h"
#include "latency_test.h"
#include "throughput_test.h"
#include "concurrent_test.h"
#include "data_integrity_test.h"
#include "failover_test.h"
#include <iostream>
#include <chrono>
#include <iomanip>

namespace performance_tests {

BenchmarkRunner::BenchmarkRunner() : results_() {}

void BenchmarkRunner::RunAllTests() {
    std::cout << "=== Financial Data Service Performance Test Suite ===" << std::endl;
    std::cout << "Starting comprehensive performance benchmarks..." << std::endl << std::endl;
    
    // Run latency tests
    std::cout << "1. Running Latency Tests..." << std::endl;
    RunLatencyTests();
    
    // Run throughput tests
    std::cout << "\n2. Running Throughput Tests..." << std::endl;
    RunThroughputTests();
    
    // Run concurrent client tests
    std::cout << "\n3. Running Concurrent Client Tests..." << std::endl;
    RunConcurrentTests();
    
    // Run data integrity tests
    std::cout << "\n4. Running Data Integrity Tests..." << std::endl;
    RunDataIntegrityTests();
    
    // Run failover tests
    std::cout << "\n5. Running Failover Tests..." << std::endl;
    RunFailoverTests();
    
    // Print summary
    PrintSummary();
}

void BenchmarkRunner::RunLatencyTests() {
    LatencyTest latency_test;
    
    // Test end-to-end latency
    auto e2e_result = latency_test.TestEndToEndLatency();
    results_.latency_results.end_to_end_latency = e2e_result;
    
    std::cout << "  End-to-end latency: " << e2e_result.mean_latency_us << "μs (avg), "
              << e2e_result.p99_latency_us << "μs (p99)" << std::endl;
    
    if (e2e_result.mean_latency_us > 50.0) {
        std::cout << "  ❌ FAILED: Average latency exceeds 50μs requirement" << std::endl;
        results_.failed_tests++;
    } else {
        std::cout << "  ✅ PASSED: Latency requirement met" << std::endl;
        results_.passed_tests++;
    }
    
    // Test WebSocket latency
    auto ws_result = latency_test.TestWebSocketLatency();
    results_.latency_results.websocket_latency = ws_result;
    
    std::cout << "  WebSocket latency: " << ws_result.mean_latency_us << "μs (avg)" << std::endl;
    
    // Test gRPC latency
    auto grpc_result = latency_test.TestGrpcLatency();
    results_.latency_results.grpc_latency = grpc_result;
    
    std::cout << "  gRPC latency: " << grpc_result.mean_latency_us << "μs (avg)" << std::endl;
}

void BenchmarkRunner::RunThroughputTests() {
    ThroughputTest throughput_test;
    
    // Test data ingestion throughput
    auto ingestion_result = throughput_test.TestDataIngestionThroughput();
    results_.throughput_results.ingestion_throughput = ingestion_result;
    
    std::cout << "  Data ingestion: " << ingestion_result.messages_per_second 
              << " msg/s" << std::endl;
    
    if (ingestion_result.messages_per_second < 1000000) {
        std::cout << "  ❌ FAILED: Throughput below 1M msg/s requirement" << std::endl;
        results_.failed_tests++;
    } else {
        std::cout << "  ✅ PASSED: Throughput requirement met" << std::endl;
        results_.passed_tests++;
    }
    
    // Test WebSocket broadcast throughput
    auto broadcast_result = throughput_test.TestWebSocketBroadcastThroughput();
    results_.throughput_results.broadcast_throughput = broadcast_result;
    
    std::cout << "  WebSocket broadcast: " << broadcast_result.messages_per_second 
              << " msg/s" << std::endl;
    
    // Test storage write throughput
    auto storage_result = throughput_test.TestStorageWriteThroughput();
    results_.throughput_results.storage_throughput = storage_result;
    
    std::cout << "  Storage write: " << storage_result.messages_per_second 
              << " msg/s" << std::endl;
}

void BenchmarkRunner::RunConcurrentTests() {
    ConcurrentTest concurrent_test;
    
    // Test 1000 concurrent WebSocket connections
    auto ws_result = concurrent_test.TestConcurrentWebSocketConnections(1000);
    results_.concurrent_results.websocket_connections = ws_result;
    
    std::cout << "  1000 concurrent WebSocket connections: ";
    if (ws_result.success_rate >= 0.99) {
        std::cout << "✅ PASSED (" << (ws_result.success_rate * 100) << "% success)" << std::endl;
        results_.passed_tests++;
    } else {
        std::cout << "❌ FAILED (" << (ws_result.success_rate * 100) << "% success)" << std::endl;
        results_.failed_tests++;
    }
    
    // Test concurrent gRPC clients
    auto grpc_result = concurrent_test.TestConcurrentGrpcClients(500);
    results_.concurrent_results.grpc_clients = grpc_result;
    
    std::cout << "  500 concurrent gRPC clients: ";
    if (grpc_result.success_rate >= 0.99) {
        std::cout << "✅ PASSED (" << (grpc_result.success_rate * 100) << "% success)" << std::endl;
        results_.passed_tests++;
    } else {
        std::cout << "❌ FAILED (" << (grpc_result.success_rate * 100) << "% success)" << std::endl;
        results_.failed_tests++;
    }
    
    // Test memory usage under load
    auto memory_result = concurrent_test.TestMemoryUsageUnderLoad();
    results_.concurrent_results.memory_usage = memory_result;
    
    std::cout << "  Memory usage under load: " << memory_result.peak_memory_mb 
              << " MB (peak)" << std::endl;
}

void BenchmarkRunner::RunDataIntegrityTests() {
    DataIntegrityTest integrity_test;
    
    // Test zero data loss
    auto loss_result = integrity_test.TestZeroDataLoss();
    results_.integrity_results.data_loss = loss_result;
    
    std::cout << "  Zero data loss test: ";
    if (loss_result.messages_lost == 0) {
        std::cout << "✅ PASSED (0 messages lost)" << std::endl;
        results_.passed_tests++;
    } else {
        std::cout << "❌ FAILED (" << loss_result.messages_lost << " messages lost)" << std::endl;
        results_.failed_tests++;
    }
    
    // Test sequence number continuity
    auto sequence_result = integrity_test.TestSequenceNumberContinuity();
    results_.integrity_results.sequence_continuity = sequence_result;
    
    std::cout << "  Sequence continuity: ";
    if (sequence_result.gaps_found == 0) {
        std::cout << "✅ PASSED (no gaps)" << std::endl;
        results_.passed_tests++;
    } else {
        std::cout << "❌ FAILED (" << sequence_result.gaps_found << " gaps found)" << std::endl;
        results_.failed_tests++;
    }
    
    // Test data consistency across storage layers
    auto consistency_result = integrity_test.TestDataConsistencyAcrossLayers();
    results_.integrity_results.data_consistency = consistency_result;
    
    std::cout << "  Data consistency: ";
    if (consistency_result.consistency_rate >= 0.9999) {
        std::cout << "✅ PASSED (" << (consistency_result.consistency_rate * 100) 
                  << "% consistent)" << std::endl;
        results_.passed_tests++;
    } else {
        std::cout << "❌ FAILED (" << (consistency_result.consistency_rate * 100) 
                  << "% consistent)" << std::endl;
        results_.failed_tests++;
    }
}

void BenchmarkRunner::RunFailoverTests() {
    FailoverTest failover_test;
    
    // Test primary server failover
    auto primary_result = failover_test.TestPrimaryServerFailover();
    results_.failover_results.primary_failover = primary_result;
    
    std::cout << "  Primary server failover: " << primary_result.failover_time_ms << "ms";
    if (primary_result.failover_time_ms <= 5000) {
        std::cout << " ✅ PASSED" << std::endl;
        results_.passed_tests++;
    } else {
        std::cout << " ❌ FAILED (exceeds 5s limit)" << std::endl;
        results_.failed_tests++;
    }
    
    // Test database failover
    auto db_result = failover_test.TestDatabaseFailover();
    results_.failover_results.database_failover = db_result;
    
    std::cout << "  Database failover: " << db_result.failover_time_ms << "ms";
    if (db_result.failover_time_ms <= 5000) {
        std::cout << " ✅ PASSED" << std::endl;
        results_.passed_tests++;
    } else {
        std::cout << " ❌ FAILED (exceeds 5s limit)" << std::endl;
        results_.failed_tests++;
    }
    
    // Test data recovery after failover
    auto recovery_result = failover_test.TestDataRecoveryAfterFailover();
    results_.failover_results.data_recovery = recovery_result;
    
    std::cout << "  Data recovery: ";
    if (recovery_result.recovery_success) {
        std::cout << "✅ PASSED (" << recovery_result.recovered_messages 
                  << " messages recovered)" << std::endl;
        results_.passed_tests++;
    } else {
        std::cout << "❌ FAILED" << std::endl;
        results_.failed_tests++;
    }
}

void BenchmarkRunner::PrintSummary() {
    std::cout << "\n=== Performance Test Summary ===" << std::endl;
    std::cout << "Total tests: " << (results_.passed_tests + results_.failed_tests) << std::endl;
    std::cout << "Passed: " << results_.passed_tests << std::endl;
    std::cout << "Failed: " << results_.failed_tests << std::endl;
    
    double success_rate = static_cast<double>(results_.passed_tests) / 
                         (results_.passed_tests + results_.failed_tests) * 100.0;
    std::cout << "Success rate: " << std::fixed << std::setprecision(1) 
              << success_rate << "%" << std::endl;
    
    // Key performance metrics summary
    std::cout << "\n=== Key Performance Metrics ===" << std::endl;
    std::cout << "End-to-end latency: " << results_.latency_results.end_to_end_latency.mean_latency_us 
              << "μs (target: <50μs)" << std::endl;
    std::cout << "Data ingestion throughput: " << results_.throughput_results.ingestion_throughput.messages_per_second 
              << " msg/s (target: >1M msg/s)" << std::endl;
    std::cout << "Concurrent connections: " << (results_.concurrent_results.websocket_connections.success_rate * 100) 
              << "% success rate (target: >99%)" << std::endl;
    std::cout << "Data loss: " << results_.integrity_results.data_loss.messages_lost 
              << " messages (target: 0)" << std::endl;
    std::cout << "Failover time: " << results_.failover_results.primary_failover.failover_time_ms 
              << "ms (target: <5000ms)" << std::endl;
    
    if (results_.failed_tests == 0) {
        std::cout << "\n🎉 All performance requirements met!" << std::endl;
    } else {
        std::cout << "\n⚠️  Some performance requirements not met. Review failed tests." << std::endl;
    }
}

} // namespace performance_tests

int main() {
    performance_tests::BenchmarkRunner runner;
    runner.RunAllTests();
    return 0;
}