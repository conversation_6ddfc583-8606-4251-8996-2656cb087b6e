#include <gtest/gtest.h>
#include <iostream>
#include <memory>
#include "../src/proto/data_types.h"

using namespace financial_data;

// Mock Redis测试，验证数据结构和序列化逻辑
class RedisMockTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 不需要真实的Redis连接
    }
    
    StandardTick CreateTestTick(const std::string& symbol, double price = 100.0) {
        StandardTick tick;
        tick.timestamp_ns = StandardTick::GetCurrentTimestampNs();
        tick.symbol = symbol;
        tick.exchange = "SHFE";
        tick.last_price = price;
        tick.volume = 1000;
        tick.turnover = price * 1000;
        tick.open_interest = 5000;
        tick.sequence = 12345;
        tick.trade_flag = "buy_open";
        
        // 设置买卖盘数据
        for (int i = 0; i < 5; ++i) {
            tick.bids[i] = PriceLevel(price - (i + 1) * 0.1, 100 + i * 10, i + 1);
            tick.asks[i] = PriceLevel(price + (i + 1) * 0.1, 100 + i * 10, i + 1);
        }
        
        return tick;
    }
    
    Level2Data CreateTestLevel2(const std::string& symbol, double base_price = 100.0) {
        Level2Data level2;
        level2.timestamp_ns = StandardTick::GetCurrentTimestampNs();
        level2.symbol = symbol;
        level2.exchange = "SHFE";
        level2.sequence = 12345;
        
        // 添加10档买卖盘数据
        for (int i = 0; i < 10; ++i) {
            level2.bids.emplace_back(base_price - (i + 1) * 0.1, 100 + i * 10, i + 1);
            level2.asks.emplace_back(base_price + (i + 1) * 0.1, 100 + i * 10, i + 1);
        }
        
        return level2;
    }
};

// 测试StandardTick数据结构
TEST_F(RedisMockTest, StandardTickValidation) {
    StandardTick tick = CreateTestTick("CU2409", 78560.0);
    
    // 验证数据有效性
    EXPECT_TRUE(tick.IsValid());
    EXPECT_EQ(tick.symbol, "CU2409");
    EXPECT_EQ(tick.exchange, "SHFE");
    EXPECT_DOUBLE_EQ(tick.last_price, 78560.0);
    EXPECT_EQ(tick.volume, 1000);
    EXPECT_GT(tick.timestamp_ns, 0);
    
    // 验证买卖盘数据
    for (int i = 0; i < 5; ++i) {
        EXPECT_GT(tick.bids[i].price, 0);
        EXPECT_GT(tick.bids[i].volume, 0);
        EXPECT_GT(tick.asks[i].price, 0);
        EXPECT_GT(tick.asks[i].volume, 0);
        EXPECT_LT(tick.bids[i].price, tick.asks[i].price);
    }
}

// 测试Level2Data数据结构
TEST_F(RedisMockTest, Level2DataValidation) {
    Level2Data level2 = CreateTestLevel2("AL2409", 19000.0);
    
    // 验证数据有效性
    EXPECT_TRUE(level2.IsValid());
    EXPECT_EQ(level2.symbol, "AL2409");
    EXPECT_EQ(level2.exchange, "SHFE");
    EXPECT_EQ(level2.bids.size(), 10);
    EXPECT_EQ(level2.asks.size(), 10);
    EXPECT_GT(level2.timestamp_ns, 0);
    
    // 验证价格排序（买盘从高到低，卖盘从低到高）
    for (size_t i = 1; i < level2.bids.size(); ++i) {
        EXPECT_GT(level2.bids[i-1].price, level2.bids[i].price);
    }
    
    for (size_t i = 1; i < level2.asks.size(); ++i) {
        EXPECT_LT(level2.asks[i-1].price, level2.asks[i].price);
    }
}

// 测试时间戳功能
TEST_F(RedisMockTest, TimestampFunctionality) {
    auto timestamp1 = StandardTick::GetCurrentTimestampNs();
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    auto timestamp2 = StandardTick::GetCurrentTimestampNs();
    
    EXPECT_GT(timestamp2, timestamp1);
    EXPECT_GT(timestamp1, 0);
    
    StandardTick tick;
    tick.SetCurrentTimestamp();
    EXPECT_GT(tick.timestamp_ns, 0);
}

// 测试MarketDataWrapper
TEST_F(RedisMockTest, MarketDataWrapper) {
    StandardTick tick = CreateTestTick("TEST", 1000.0);
    Level2Data level2 = CreateTestLevel2("TEST", 1000.0);
    
    // 测试Tick包装
    MarketDataWrapper tick_wrapper(tick);
    EXPECT_EQ(tick_wrapper.type, MarketDataWrapper::DataType::TICK);
    EXPECT_TRUE(tick_wrapper.IsValid());
    EXPECT_EQ(tick_wrapper.tick_data.symbol, "TEST");
    EXPECT_GT(tick_wrapper.receive_time_ns, 0);
    
    // 测试Level2包装
    MarketDataWrapper level2_wrapper(level2);
    EXPECT_EQ(level2_wrapper.type, MarketDataWrapper::DataType::LEVEL2);
    EXPECT_TRUE(level2_wrapper.IsValid());
    EXPECT_EQ(level2_wrapper.level2_data.symbol, "TEST");
    EXPECT_GT(level2_wrapper.receive_time_ns, 0);
}

// 测试MarketDataBatch
TEST_F(RedisMockTest, MarketDataBatch) {
    MarketDataBatch batch;
    EXPECT_TRUE(batch.IsEmpty());
    EXPECT_EQ(batch.Size(), 0);
    
    // 添加Tick数据
    StandardTick tick1 = CreateTestTick("CU2409", 78560.0);
    StandardTick tick2 = CreateTestTick("AL2409", 19000.0);
    
    batch.AddTick(tick1);
    batch.AddTick(tick2);
    
    EXPECT_FALSE(batch.IsEmpty());
    EXPECT_EQ(batch.Size(), 2);
    EXPECT_GT(batch.batch_timestamp_ns, 0);
    
    // 添加Level2数据
    Level2Data level2 = CreateTestLevel2("ZN2409", 25000.0);
    batch.AddLevel2(level2);
    
    EXPECT_EQ(batch.Size(), 3);
    
    // 验证数据类型
    EXPECT_EQ(batch.data[0].type, MarketDataWrapper::DataType::TICK);
    EXPECT_EQ(batch.data[1].type, MarketDataWrapper::DataType::TICK);
    EXPECT_EQ(batch.data[2].type, MarketDataWrapper::DataType::LEVEL2);
    
    // 清空批次
    batch.Clear();
    EXPECT_TRUE(batch.IsEmpty());
    EXPECT_EQ(batch.Size(), 0);
}

// 测试性能要求
TEST_F(RedisMockTest, PerformanceRequirements) {
    const int test_count = 10000;
    std::vector<StandardTick> ticks;
    
    // 创建大量测试数据
    auto create_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < test_count; ++i) {
        ticks.push_back(CreateTestTick("PERF_TEST", 1000.0 + i));
    }
    
    auto create_end = std::chrono::high_resolution_clock::now();
    auto create_duration = std::chrono::duration_cast<std::chrono::microseconds>(create_end - create_start);
    
    std::cout << "Created " << test_count << " ticks in " << create_duration.count() << " μs" << std::endl;
    std::cout << "Average creation time: " << (create_duration.count() / test_count) << " μs per tick" << std::endl;
    
    // 验证数据创建性能满足要求
    double creation_ops_per_sec = (test_count * 1000000.0) / create_duration.count();
    EXPECT_GT(creation_ops_per_sec, 100000) << "Data creation performance requirement not met";
    
    // 验证所有数据都有效
    for (const auto& tick : ticks) {
        EXPECT_TRUE(tick.IsValid());
    }
}

// 测试数据一致性
TEST_F(RedisMockTest, DataConsistency) {
    StandardTick original = CreateTestTick("CONSISTENCY_TEST", 12345.67);
    
    // 验证数据字段一致性
    EXPECT_EQ(original.symbol, "CONSISTENCY_TEST");
    EXPECT_DOUBLE_EQ(original.last_price, 12345.67);
    EXPECT_EQ(original.exchange, "SHFE");
    EXPECT_EQ(original.volume, 1000);
    EXPECT_DOUBLE_EQ(original.turnover, 12345.67 * 1000);
    EXPECT_EQ(original.open_interest, 5000);
    EXPECT_EQ(original.sequence, 12345);
    EXPECT_EQ(original.trade_flag, "buy_open");
    
    // 验证买卖盘数据一致性
    for (int i = 0; i < 5; ++i) {
        EXPECT_DOUBLE_EQ(original.bids[i].price, 12345.67 - (i + 1) * 0.1);
        EXPECT_EQ(original.bids[i].volume, 100 + i * 10);
        EXPECT_EQ(original.bids[i].order_count, i + 1);
        
        EXPECT_DOUBLE_EQ(original.asks[i].price, 12345.67 + (i + 1) * 0.1);
        EXPECT_EQ(original.asks[i].volume, 100 + i * 10);
        EXPECT_EQ(original.asks[i].order_count, i + 1);
    }
}

// 测试边界条件
TEST_F(RedisMockTest, BoundaryConditions) {
    // 测试无效数据
    StandardTick invalid_tick;
    EXPECT_FALSE(invalid_tick.IsValid());
    
    // 测试最小有效数据
    StandardTick minimal_tick;
    minimal_tick.timestamp_ns = 1;
    minimal_tick.symbol = "MIN";
    minimal_tick.exchange = "TEST";
    minimal_tick.last_price = 0.01;
    EXPECT_TRUE(minimal_tick.IsValid());
    
    // 测试空Level2数据
    Level2Data empty_level2;
    EXPECT_FALSE(empty_level2.IsValid());
    
    // 测试最小有效Level2数据
    Level2Data minimal_level2;
    minimal_level2.timestamp_ns = 1;
    minimal_level2.symbol = "MIN";
    minimal_level2.exchange = "TEST";
    minimal_level2.bids.emplace_back(100.0, 1, 1);
    minimal_level2.asks.emplace_back(100.1, 1, 1);
    EXPECT_TRUE(minimal_level2.IsValid());
}

// 测试内存使用
TEST_F(RedisMockTest, MemoryUsage) {
    const int large_batch_size = 100000;
    std::vector<StandardTick> large_batch;
    large_batch.reserve(large_batch_size);
    
    // 创建大批量数据
    for (int i = 0; i < large_batch_size; ++i) {
        large_batch.push_back(CreateTestTick("MEMORY_TEST", 1000.0 + i));
    }
    
    EXPECT_EQ(large_batch.size(), large_batch_size);
    
    // 验证数据完整性
    for (size_t i = 0; i < large_batch.size(); ++i) {
        EXPECT_TRUE(large_batch[i].IsValid());
        EXPECT_DOUBLE_EQ(large_batch[i].last_price, 1000.0 + i);
    }
    
    std::cout << "Successfully created and validated " << large_batch_size << " ticks in memory" << std::endl;
}