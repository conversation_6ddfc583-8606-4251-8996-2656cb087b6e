// Generated gRPC service implementation for market_data_service.proto
#include "market_data_service.grpc.pb.h"

namespace financial_data {

static const char* MarketDataService_method_names[] = {
    "/financial_data.MarketDataService/StreamTickData",
    "/financial_data.MarketDataService/GetHistoricalTickData", 
    "/financial_data.MarketDataService/StreamKlineData",
    "/financial_data.MarketDataService/StreamLevel2Data",
    "/financial_data.MarketDataService/HealthCheck",
};

std::unique_ptr<MarketDataService::Stub> MarketDataService::NewStub(
    const std::shared_ptr<grpc::ChannelInterface>& channel,
    const grpc::StubOptions& options) {
    (void)options;
    std::unique_ptr<MarketDataService::Stub> stub(new MarketDataService::Stub(channel));
    return stub;
}

MarketDataService::Stub::Stub(const std::shared_ptr<grpc::ChannelInterface>& channel)
    : channel_(channel),
      rpcmethod_StreamTickData_(MarketDataService_method_names[0], grpc::internal::RpcMethod::SERVER_STREAMING, channel),
      rpcmethod_GetHistoricalTickData_(MarketDataService_method_names[1], grpc::internal::RpcMethod::SERVER_STREAMING, channel),
      rpcmethod_StreamKlineData_(MarketDataService_method_names[2], grpc::internal::RpcMethod::SERVER_STREAMING, channel),
      rpcmethod_StreamLevel2Data_(MarketDataService_method_names[3], grpc::internal::RpcMethod::SERVER_STREAMING, channel),
      rpcmethod_HealthCheck_(MarketDataService_method_names[4], grpc::internal::RpcMethod::NORMAL_RPC, channel) {}

grpc::Status MarketDataService::Stub::StreamTickData(grpc::ClientContext* context,
                                                    const TickDataRequest& request,
                                                    grpc::ClientReader<TickDataResponse>* reader) {
    return grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_StreamTickData_, context, request, reader);
}

std::unique_ptr<grpc::ClientReader<TickDataResponse>> MarketDataService::Stub::StreamTickData(
    grpc::ClientContext* context, const TickDataRequest& request) {
    return std::unique_ptr<grpc::ClientReader<TickDataResponse>>(
        grpc::internal::ClientReaderFactory<TickDataResponse>::Create(
            channel_.get(), rpcmethod_StreamTickData_, context, request));
}

grpc::Status MarketDataService::Stub::GetHistoricalTickData(grpc::ClientContext* context,
                                                           const HistoricalTickDataRequest& request,
                                                           grpc::ClientReader<TickDataResponse>* reader) {
    return grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_GetHistoricalTickData_, context, request, reader);
}

std::unique_ptr<grpc::ClientReader<TickDataResponse>> MarketDataService::Stub::GetHistoricalTickData(
    grpc::ClientContext* context, const HistoricalTickDataRequest& request) {
    return std::unique_ptr<grpc::ClientReader<TickDataResponse>>(
        grpc::internal::ClientReaderFactory<TickDataResponse>::Create(
            channel_.get(), rpcmethod_GetHistoricalTickData_, context, request));
}

grpc::Status MarketDataService::Stub::StreamKlineData(grpc::ClientContext* context,
                                                     const KlineDataRequest& request,
                                                     grpc::ClientReader<KlineDataResponse>* reader) {
    return grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_StreamKlineData_, context, request, reader);
}

std::unique_ptr<grpc::ClientReader<KlineDataResponse>> MarketDataService::Stub::StreamKlineData(
    grpc::ClientContext* context, const KlineDataRequest& request) {
    return std::unique_ptr<grpc::ClientReader<KlineDataResponse>>(
        grpc::internal::ClientReaderFactory<KlineDataResponse>::Create(
            channel_.get(), rpcmethod_StreamKlineData_, context, request));
}

grpc::Status MarketDataService::Stub::StreamLevel2Data(grpc::ClientContext* context,
                                                      const Level2DataRequest& request,
                                                      grpc::ClientReader<Level2DataResponse>* reader) {
    return grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_StreamLevel2Data_, context, request, reader);
}

std::unique_ptr<grpc::ClientReader<Level2DataResponse>> MarketDataService::Stub::StreamLevel2Data(
    grpc::ClientContext* context, const Level2DataRequest& request) {
    return std::unique_ptr<grpc::ClientReader<Level2DataResponse>>(
        grpc::internal::ClientReaderFactory<Level2DataResponse>::Create(
            channel_.get(), rpcmethod_StreamLevel2Data_, context, request));
}

grpc::Status MarketDataService::Stub::HealthCheck(grpc::ClientContext* context,
                                                 const HealthCheckRequest& request,
                                                 HealthCheckResponse* response) {
    return grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_HealthCheck_, context, request, response);
}

MarketDataService::Service::Service() {
    AddMethod(new grpc::internal::RpcServiceMethod(
        MarketDataService_method_names[0],
        grpc::internal::RpcMethod::SERVER_STREAMING,
        new grpc::internal::ServerStreamingHandler<MarketDataService::Service, TickDataRequest, TickDataResponse>(
            [](MarketDataService::Service* service,
               grpc::ServerContext* ctx,
               const TickDataRequest* req,
               grpc::ServerWriter<TickDataResponse>* writer) {
                return service->StreamTickData(ctx, req, writer);
            }, this)));
    
    AddMethod(new grpc::internal::RpcServiceMethod(
        MarketDataService_method_names[1],
        grpc::internal::RpcMethod::SERVER_STREAMING,
        new grpc::internal::ServerStreamingHandler<MarketDataService::Service, HistoricalTickDataRequest, TickDataResponse>(
            [](MarketDataService::Service* service,
               grpc::ServerContext* ctx,
               const HistoricalTickDataRequest* req,
               grpc::ServerWriter<TickDataResponse>* writer) {
                return service->GetHistoricalTickData(ctx, req, writer);
            }, this)));
    
    AddMethod(new grpc::internal::RpcServiceMethod(
        MarketDataService_method_names[2],
        grpc::internal::RpcMethod::SERVER_STREAMING,
        new grpc::internal::ServerStreamingHandler<MarketDataService::Service, KlineDataRequest, KlineDataResponse>(
            [](MarketDataService::Service* service,
               grpc::ServerContext* ctx,
               const KlineDataRequest* req,
               grpc::ServerWriter<KlineDataResponse>* writer) {
                return service->StreamKlineData(ctx, req, writer);
            }, this)));
    
    AddMethod(new grpc::internal::RpcServiceMethod(
        MarketDataService_method_names[3],
        grpc::internal::RpcMethod::SERVER_STREAMING,
        new grpc::internal::ServerStreamingHandler<MarketDataService::Service, Level2DataRequest, Level2DataResponse>(
            [](MarketDataService::Service* service,
               grpc::ServerContext* ctx,
               const Level2DataRequest* req,
               grpc::ServerWriter<Level2DataResponse>* writer) {
                return service->StreamLevel2Data(ctx, req, writer);
            }, this)));
    
    AddMethod(new grpc::internal::RpcServiceMethod(
        MarketDataService_method_names[4],
        grpc::internal::RpcMethod::NORMAL_RPC,
        new grpc::internal::RpcMethodHandler<MarketDataService::Service, HealthCheckRequest, HealthCheckResponse>(
            [](MarketDataService::Service* service,
               grpc::ServerContext* ctx,
               const HealthCheckRequest* req,
               HealthCheckResponse* resp) {
                return service->HealthCheck(ctx, req, resp);
            }, this)));
}

MarketDataService::Service::~Service() {
}

grpc::Status MarketDataService::Service::StreamTickData(grpc::ServerContext* context,
                                                       const TickDataRequest* request,
                                                       grpc::ServerWriter<TickDataResponse>* writer) {
    (void) context;
    (void) request;
    (void) writer;
    return grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "");
}

grpc::Status MarketDataService::Service::GetHistoricalTickData(grpc::ServerContext* context,
                                                              const HistoricalTickDataRequest* request,
                                                              grpc::ServerWriter<TickDataResponse>* writer) {
    (void) context;
    (void) request;
    (void) writer;
    return grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "");
}

grpc::Status MarketDataService::Service::StreamKlineData(grpc::ServerContext* context,
                                                        const KlineDataRequest* request,
                                                        grpc::ServerWriter<KlineDataResponse>* writer) {
    (void) context;
    (void) request;
    (void) writer;
    return grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "");
}

grpc::Status MarketDataService::Service::StreamLevel2Data(grpc::ServerContext* context,
                                                         const Level2DataRequest* request,
                                                         grpc::ServerWriter<Level2DataResponse>* writer) {
    (void) context;
    (void) request;
    (void) writer;
    return grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "");
}

grpc::Status MarketDataService::Service::HealthCheck(grpc::ServerContext* context,
                                                    const HealthCheckRequest* request,
                                                    HealthCheckResponse* response) {
    (void) context;
    (void) request;
    (void) response;
    return grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "");
}

} // namespace financial_data