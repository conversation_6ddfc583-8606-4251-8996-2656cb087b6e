#pragma once

#include "security_config.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>

namespace financial_data {
namespace security {

// 权限定义
enum class Permission {
    READ_MARKET_DATA,
    WRITE_MARKET_DATA,
    READ_HISTORICAL_DATA,
    EXPORT_DATA,
    MANAGE_USERS,
    MANAGE_ROLES,
    SYSTEM_CONFIG,
    VIEW_AUDIT_LOGS,
    MANAGE_SUBSCRIPTIONS,
    API_ACCESS,
    WEBSOCKET_ACCESS,
    GRPC_ACCESS
};

// 资源类型
enum class ResourceType {
    MARKET_DATA,
    HISTORICAL_DATA,
    USER_MANAGEMENT,
    SYSTEM_CONFIG,
    AUDIT_LOGS,
    API_ENDPOINTS,
    SUBSCRIPTIONS
};

// 操作类型
enum class Action {
    READ,
    WRITE,
    DELETE,
    EXECUTE,
    MANAGE
};

// 权限规则
struct PermissionRule {
    Permission permission;
    ResourceType resource;
    Action action;
    std::vector<std::string> conditions; // 额外条件，如时间限制、IP限制等
};

// 角色定义
struct Role {
    std::string role_id;
    std::string name;
    std::string description;
    std::vector<PermissionRule> permissions;
    std::vector<std::string> parent_roles; // 角色继承
    bool is_active;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point updated_at;
};

// 用户角色分配
struct UserRoleAssignment {
    std::string user_id;
    std::string role_id;
    std::chrono::system_clock::time_point assigned_at;
    std::chrono::system_clock::time_point expires_at;
    bool is_active;
    std::string assigned_by;
};

class RBACManager {
public:
    explicit RBACManager(const RBACConfig& config);
    ~RBACManager();

    // 初始化RBAC系统
    bool Initialize();
    
    // 角色管理
    bool CreateRole(const Role& role);
    bool UpdateRole(const Role& role);
    bool DeleteRole(const std::string& role_id);
    Role GetRole(const std::string& role_id);
    std::vector<Role> GetAllRoles();
    
    // 用户角色分配
    bool AssignRoleToUser(const std::string& user_id, const std::string& role_id, 
                         const std::string& assigned_by, 
                         std::chrono::system_clock::time_point expires_at = {});
    bool RevokeRoleFromUser(const std::string& user_id, const std::string& role_id);
    std::vector<std::string> GetUserRoles(const std::string& user_id);
    std::vector<std::string> GetRoleUsers(const std::string& role_id);
    
    // 权限检查
    bool CheckPermission(const std::string& user_id, Permission permission, 
                        ResourceType resource, Action action,
                        const std::unordered_map<std::string, std::string>& context = {});
    
    // 获取用户权限
    std::vector<PermissionRule> GetUserPermissions(const std::string& user_id);
    
    // 权限缓存管理
    void InvalidateUserCache(const std::string& user_id);
    void ClearAllCache();
    
    // 动态权限管理
    bool AddDynamicPermission(const std::string& user_id, const PermissionRule& rule,
                             std::chrono::system_clock::time_point expires_at);
    bool RemoveDynamicPermission(const std::string& user_id, Permission permission);
    
    // 权限审计
    struct PermissionCheck {
        std::string user_id;
        Permission permission;
        ResourceType resource;
        Action action;
        bool granted;
        std::chrono::system_clock::time_point timestamp;
        std::string context;
    };
    
    std::vector<PermissionCheck> GetPermissionAuditLog(const std::string& user_id = "",
                                                      std::chrono::system_clock::time_point from = {},
                                                      std::chrono::system_clock::time_point to = {});

private:
    RBACConfig config_;
    std::unordered_map<std::string, Role> roles_;
    std::unordered_map<std::string, std::vector<UserRoleAssignment>> user_roles_;
    std::unordered_map<std::string, std::vector<PermissionRule>> user_permissions_cache_;
    std::unordered_map<std::string, std::vector<PermissionRule>> dynamic_permissions_;
    std::vector<PermissionCheck> permission_audit_log_;
    bool initialized_;
    
    // 权限解析
    std::vector<PermissionRule> ResolveUserPermissions(const std::string& user_id);
    std::vector<PermissionRule> ResolveRolePermissions(const std::string& role_id);
    
    // 条件检查
    bool CheckConditions(const std::vector<std::string>& conditions,
                        const std::unordered_map<std::string, std::string>& context);
    
    // 缓存管理
    bool IsCacheValid(const std::string& user_id);
    void UpdateCache(const std::string& user_id, const std::vector<PermissionRule>& permissions);
    
    // 数据持久化
    bool LoadRoles();
    bool SaveRoles();
    bool LoadUserRoles();
    bool SaveUserRoles();
    
    // 默认角色创建
    void CreateDefaultRoles();
    
    // 权限字符串转换
    std::string PermissionToString(Permission permission);
    Permission StringToPermission(const std::string& permission_str);
    std::string ResourceTypeToString(ResourceType resource);
    ResourceType StringToResourceType(const std::string& resource_str);
    std::string ActionToString(Action action);
    Action StringToAction(const std::string& action_str);
};

} // namespace security
} // namespace financial_data