#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <atomic>
#include <mutex>
#include <thread>
#include <queue>
#include <unordered_set>
#include <unordered_map>
#include <condition_variable>
#include <chrono>

#include <spdlog/spdlog.h>
#include <nlohmann/json.hpp>

#include "ctp_headers.h"
#include "ctp_api_wrapper.h"
#include "../data_models.h"

namespace financial_data {

// CTP配置结构
struct CTPConfig {
    std::string front_address;
    std::string broker_id;
    std::string user_id;
    std::string password;
    std::string flow_path;
    int heartbeat_interval;
    int reconnect_interval;
    int max_reconnect_attempts;
    bool enable_level2;
    
    // 从文件加载配置
    static CTPConfig LoadFromFile(const std::string& config_path);
    
    // 验证配置有效性
    bool IsValid() const;
};



// CTP行情数据采集器
class CTPMarketDataCollector {
public:
    // 回调函数类型定义
    using MarketDataCallback = std::function<void(const MarketDataWrapper&)>;
    using ConnectionStatusCallback = std::function<void(ConnectionStatus, const std::string&)>;
    
    // 统计信息结构
    struct Statistics {
        uint64_t total_received{0};
        uint64_t total_processed{0};
        uint64_t total_errors{0};
        ConnectionStatus status{ConnectionStatus::DISCONNECTED};
        uint32_t reconnect_attempts{0};
        std::chrono::steady_clock::time_point last_heartbeat;
        double uptime_seconds{0.0};
        double messages_per_second{0.0};
    };

public:
    CTPMarketDataCollector();
    ~CTPMarketDataCollector();
    
    // 禁用拷贝和移动
    CTPMarketDataCollector(const CTPMarketDataCollector&) = delete;
    CTPMarketDataCollector& operator=(const CTPMarketDataCollector&) = delete;
    
    // 初始化
    bool Initialize(const CTPConfig& config);
    bool Initialize(const std::string& config_path);
    
    // 连接管理
    bool Connect();
    void Disconnect();
    bool IsConnected() const;
    ConnectionStatus GetConnectionStatus() const;
    
    // 订阅管理
    bool Subscribe(const std::vector<std::string>& symbols);
    bool Subscribe(const std::string& symbol);
    bool Unsubscribe(const std::vector<std::string>& symbols);
    bool Unsubscribe(const std::string& symbol);
    std::vector<std::string> GetSubscribedSymbols() const;
    void ClearSubscriptions();
    
    // 回调设置
    void SetDataCallback(const MarketDataCallback& callback);
    void SetStatusCallback(const ConnectionStatusCallback& callback);
    
    // 生命周期管理
    void Start();
    void Stop();
    void Shutdown();
    
    // 统计和监控
    Statistics GetStatistics() const;
    void ResetStatistics();
    bool IsHealthy() const;
    std::string GetHealthStatus() const;

private:
    // 配置和状态
    CTPConfig config_;
    std::atomic<ConnectionStatus> connection_status_;
    std::atomic<bool> running_;
    std::atomic<bool> shutdown_requested_;
    std::atomic<uint32_t> reconnect_attempts_;
    
    // CTP API包装器
    std::unique_ptr<CTPApiWrapper> ctp_api_;
    
    // 回调函数
    MarketDataCallback data_callback_;
    ConnectionStatusCallback status_callback_;
    
    // 订阅管理
    std::unordered_set<std::string> subscribed_symbols_;
    mutable std::mutex subscription_mutex_;
    
    // 数据处理队列
    std::queue<MarketDataWrapper> data_queue_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // 工作线程
    std::unique_ptr<std::thread> heartbeat_thread_;
    std::unique_ptr<std::thread> reconnect_thread_;
    std::unique_ptr<std::thread> data_processor_thread_;
    
    // 重连控制
    std::mutex reconnect_mutex_;
    std::condition_variable reconnect_cv_;
    
    // 心跳管理
    std::atomic<std::chrono::steady_clock::time_point> last_heartbeat_;
    
    // 序列号管理
    std::atomic<uint32_t> sequence_counter_;
    std::unordered_map<std::string, uint32_t> symbol_sequences_;
    std::mutex sequence_mutex_;
    
    // 统计信息
    std::atomic<uint64_t> total_received_;
    std::atomic<uint64_t> total_processed_;
    std::atomic<uint64_t> total_errors_;
    std::chrono::steady_clock::time_point start_time_;
    
    // 日志记录器
    std::shared_ptr<spdlog::logger> logger_;

private:
    // 初始化方法
    void InitializeLogger();
    
    // 连接管理
    bool EstablishConnection();
    void HandleDisconnection(const std::string& reason);
    void StartReconnectProcess();
    void ReconnectLoop();
    
    // 心跳管理
    void StartHeartbeat();
    void HeartbeatLoop();
    void UpdateHeartbeat();
    bool IsHeartbeatTimeout() const;
    
    // 数据处理
    void StartDataProcessor();
    void DataProcessorLoop();
    void ProcessMarketData(const MarketDataWrapper& data);
    
    // 序列号管理
    uint32_t GetNextSequence();
    uint32_t GetSymbolSequence(const std::string& symbol);
    void UpdateSymbolSequence(const std::string& symbol, uint32_t sequence);
    
    // 数据转换
    StandardTick ConvertToStandardTick(CThostFtdcDepthMarketDataField* ctp_data);
    Level2Data ConvertToLevel2Data(CThostFtdcDepthMarketDataField* ctp_data);
    
    // 数据验证
    bool ValidateMarketData(const MarketDataWrapper& data);
    
    // 错误处理
    void HandleError(const std::string& error_msg);
    void LogError(const std::string& error_msg);
    
    // 状态通知
    void NotifyStatusChange(ConnectionStatus status, const std::string& message);
    
    // 辅助方法
    std::string GetExchangeCode(const std::string& symbol) const;
    bool IsValidSymbol(const std::string& symbol) const;
    int64_t GetCurrentTimestampNs() const;
    
    // CTP API回调处理方法
    void HandleFrontConnected();
    void HandleFrontDisconnected(int reason);
    void HandleUserLogin(bool success, const std::string& error_msg);
    void HandleMarketData(CThostFtdcDepthMarketDataField* depth_market_data);
    void HandleError(int error_id, const std::string& error_msg);
    
    // 数据回补相关方法
    void RequestMissedData(const std::string& symbol, int64_t from_time, int64_t to_time);
    bool HasDataGap(const std::string& symbol, uint32_t current_seq, uint32_t expected_seq);
    void HandleDataGap(const std::string& symbol, uint32_t missing_start, uint32_t missing_end);
    
    // 模拟CTP API回调方法（用于测试）
    void OnFrontConnected();
    void OnFrontDisconnected(int reason);
    void OnRspUserLogin(void* rsp_user_login, void* rsp_info, int request_id, bool is_last);
    void OnRspSubMarketData(void* specific_instrument, void* rsp_info, int request_id, bool is_last);
    void OnRtnDepthMarketData(void* depth_market_data);
    void OnRspError(void* rsp_info, int request_id, bool is_last);
};

// CTP采集器工厂类
class CTPCollectorFactory {
public:
    static std::unique_ptr<CTPMarketDataCollector> Create(const CTPConfig& config);
    static std::unique_ptr<CTPMarketDataCollector> CreateFromFile(const std::string& config_path);
    static bool ValidateCTPEnvironment();
    static std::string GetCTPVersion();
};

// CTP工具函数命名空间
namespace ctp_utils {
    std::string GetExchangeFromSymbol(const std::string& symbol);
    bool IsValidCTPSymbol(const std::string& symbol);
    int64_t CTPTimeToNanoseconds(const std::string& update_time, int update_millisec);
    std::string NanosecondsToCTPTime(int64_t timestamp_ns);
    bool ValidatePrice(double price);
    bool ValidateVolume(uint64_t volume);
    bool ValidateTurnover(double turnover);
    std::string GetErrorMessage(int error_code);
    bool IsRecoverableError(int error_code);
}

} // namespace financial_data