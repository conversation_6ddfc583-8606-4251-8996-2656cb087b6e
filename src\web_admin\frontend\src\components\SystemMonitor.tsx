import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Table, Tag, Progress, Statistic, Switch, Button, message } from 'antd';
import { ReloadOutlined, PauseOutlined, PlayCircleOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import axios from 'axios';
import dayjs from 'dayjs';

interface ComponentStatus {
  name: string;
  status: 'online' | 'warning' | 'error';
  uptime: string;
  lastCheck: string;
  details: string;
}

const SystemMonitor: React.FC = () => {
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);
  const [components, setComponents] = useState<ComponentStatus[]>([]);
  const [metricsHistory, setMetricsHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchComponentStatus();
    fetchMetricsHistory();
    
    let interval: NodeJS.Timeout;
    if (realTimeEnabled) {
      interval = setInterval(() => {
        fetchComponentStatus();
        fetchMetricsHistory();
      }, 2000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [realTimeEnabled]);

  const fetchComponentStatus = async () => {
    try {
      const { data } = await axios.get('/api/system/components');
      setComponents(data || []);
    } catch (error) {
      console.error('Error fetching component status:', error);
      setComponents([]);
    }
  };

  const fetchMetricsHistory = async () => {
    try {
      const { data } = await axios.get('/api/system/metrics');
      const ts = dayjs(data.timestamp).format('HH:mm');
      const point = {
        timestamp: ts,
        cpu: data.cpu_usage,
        memory: data.memory_usage,
        disk: data.disk_usage,
        network_rx: data.network_io?.rx_bytes || 0,
        network_tx: data.network_io?.tx_bytes || 0,
        connections: data.active_connections,
        latency: data.latency_p99,
        throughput: data.data_throughput,
      } as any;
      setMetricsHistory(prev => {
        const arr = [...prev, point];
        return arr.slice(-60);
      });
    } catch (error) {
      console.error('Error fetching metrics history:', error);
    }
  };

  const handleRefresh = async () => {
    setLoading(true);
    await Promise.all([fetchComponentStatus(), fetchMetricsHistory()]);
    setLoading(false);
    message.success('数据已刷新');
  };

  const getResourceUsageChartOption = () => {
    return {
      title: {
        text: '系统资源使用率',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' }
      },
      legend: {
        data: ['CPU', '内存', '磁盘'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: metricsHistory.map(m => m.timestamp)
      },
      yAxis: {
        type: 'value',
        name: '使用率 (%)',
        max: 100
      },
      series: [
        {
          name: 'CPU',
          type: 'line',
          data: metricsHistory.map(m => m.cpu.toFixed(1)),
          itemStyle: { color: '#1890ff' }
        },
        {
          name: '内存',
          type: 'line',
          data: metricsHistory.map(m => m.memory.toFixed(1)),
          itemStyle: { color: '#52c41a' }
        },
        {
          name: '磁盘',
          type: 'line',
          data: metricsHistory.map(m => m.disk.toFixed(1)),
          itemStyle: { color: '#faad14' }
        }
      ]
    };
  };

  const getNetworkChartOption = () => {
    return {
      title: {
        text: '网络IO',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' }
      },
      legend: {
        data: ['接收', '发送'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: metricsHistory.map(m => m.timestamp)
      },
      yAxis: {
        type: 'value',
        name: 'MB/s'
      },
      series: [
        {
          name: '接收',
          type: 'area',
          data: metricsHistory.map(m => m.network_rx.toFixed(1)),
          itemStyle: { color: '#722ed1' },
          areaStyle: { opacity: 0.3 }
        },
        {
          name: '发送',
          type: 'area',
          data: metricsHistory.map(m => m.network_tx.toFixed(1)),
          itemStyle: { color: '#eb2f96' },
          areaStyle: { opacity: 0.3 }
        }
      ]
    };
  };

  const getPerformanceChartOption = () => {
    return {
      title: {
        text: '性能指标',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' }
      },
      legend: {
        data: ['延迟 (μs)', '吞吐量 (万条/秒)'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: metricsHistory.map(m => m.timestamp)
      },
      yAxis: [
        {
          type: 'value',
          name: '延迟 (μs)',
          position: 'left'
        },
        {
          type: 'value',
          name: '吞吐量 (万条/秒)',
          position: 'right'
        }
      ],
      series: [
        {
          name: '延迟 (μs)',
          type: 'line',
          yAxisIndex: 0,
          data: metricsHistory.map(m => m.latency.toFixed(1)),
          itemStyle: { color: '#f5222d' }
        },
        {
          name: '吞吐量 (万条/秒)',
          type: 'bar',
          yAxisIndex: 1,
          data: metricsHistory.map(m => (m.throughput / 10000).toFixed(1)),
          itemStyle: { color: '#52c41a' }
        }
      ]
    };
  };

  const componentColumns = [
    {
      title: '组件名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const config = {
          online: { color: 'green', text: '在线' },
          warning: { color: 'orange', text: '警告' },
          error: { color: 'red', text: '错误' }
        };
        const { color, text } = config[status as keyof typeof config];
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '运行时间',
      dataIndex: 'uptime',
      key: 'uptime'
    },
    {
      title: '最后检查',
      dataIndex: 'lastCheck',
      key: 'lastCheck'
    },
    {
      title: '详细信息',
      dataIndex: 'details',
      key: 'details'
    }
  ];

  const onlineCount = components.filter(c => c.status === 'online').length;
  const warningCount = components.filter(c => c.status === 'warning').length;
  const errorCount = components.filter(c => c.status === 'error').length;

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={18}>
          <Card>
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="在线组件"
                  value={onlineCount}
                  suffix={`/ ${components.length}`}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="警告组件"
                  value={warningCount}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="错误组件"
                  value={errorCount}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <div>实时监控</div>
                <Switch
                  checked={realTimeEnabled}
                  onChange={setRealTimeEnabled}
                  checkedChildren={<PlayCircleOutlined />}
                  unCheckedChildren={<PauseOutlined />}
                />
              </div>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card title="组件状态">
            <Table
              columns={componentColumns}
              dataSource={components}
              rowKey="name"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card>
            <ReactECharts option={getResourceUsageChartOption()} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <ReactECharts option={getNetworkChartOption()} style={{ height: '300px' }} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card>
            <ReactECharts option={getPerformanceChartOption()} style={{ height: '300px' }} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SystemMonitor;