"""
定时任务调度器测试模块

测试覆盖：
- 任务调度和取消调度
- <PERSON>ron表达式解析和执行时间计算
- 任务优先级和依赖关系管理
- 任务执行和重试机制
- 错误处理和状态监控
- 统计信息和历史记录
"""

import asyncio
import pytest
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加src路径以便导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'collectors'))

from scheduled_task_manager import (
    ScheduledTaskManager,
    TaskConfig,
    TaskType,
    TaskStatus,
    TaskPriority,
    TaskExecution,
    TaskExecutor,
    DefaultTaskExecutor
)


class MockTaskExecutor(TaskExecutor):
    """模拟任务执行器用于测试"""
    
    def __init__(self):
        self.executed_tasks = []
        self.execution_results = {}
        self.execution_delays = {}
        self.should_fail = set()
    
    async def execute(self, task_config: TaskConfig, execution: TaskExecution) -> dict:
        """执行任务"""
        self.executed_tasks.append((task_config, execution))
        
        # 模拟执行延迟
        delay = self.execution_delays.get(execution.task_id, 0.1)
        await asyncio.sleep(delay)
        
        # 检查是否应该失败
        if execution.task_id in self.should_fail:
            raise Exception(f"Mock failure for task {execution.task_id}")
        
        # 返回预设结果或默认结果
        return self.execution_results.get(execution.task_id, {
            "status": "success",
            "message": f"Mock execution of {execution.task_id}",
            "records_processed": 100
        })
    
    def get_task_types(self) -> list:
        """获取支持的任务类型"""
        return list(TaskType)
    
    def set_execution_result(self, task_id: str, result: dict):
        """设置任务执行结果"""
        self.execution_results[task_id] = result
    
    def set_execution_delay(self, task_id: str, delay: float):
        """设置任务执行延迟"""
        self.execution_delays[task_id] = delay
    
    def set_should_fail(self, task_id: str, should_fail: bool = True):
        """设置任务是否应该失败"""
        if should_fail:
            self.should_fail.add(task_id)
        else:
            self.should_fail.discard(task_id)


class TestTaskConfig:
    """测试TaskConfig类"""
    
    def test_valid_config_creation(self):
        """测试有效配置创建"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            symbols=["AAPL", "GOOGL"],
            parameters={"lookback_days": 1},
            priority=TaskPriority.HIGH,
            max_retries=5,
            retry_delay_seconds=30,
            timeout_seconds=1800,
            dependencies=["task1"],
            enabled=True,
            description="Daily historical data update"
        )
        
        assert config.task_type == TaskType.HISTORICAL_UPDATE
        assert config.cron_expression == "0 2 * * *"
        assert config.symbols == ["AAPL", "GOOGL"]
        assert config.priority == TaskPriority.HIGH
        assert config.max_retries == 5
        assert config.enabled is True
    
    def test_invalid_cron_expression(self):
        """测试无效的cron表达式"""
        with pytest.raises(ValueError, match="Invalid cron expression"):
            TaskConfig(
                task_type=TaskType.HISTORICAL_UPDATE,
                cron_expression="invalid cron",
            )
    
    def test_invalid_retry_config(self):
        """测试无效的重试配置"""
        with pytest.raises(ValueError, match="max_retries must be non-negative"):
            TaskConfig(
                task_type=TaskType.HISTORICAL_UPDATE,
                cron_expression="0 2 * * *",
                max_retries=-1
            )
        
        with pytest.raises(ValueError, match="retry_delay_seconds must be non-negative"):
            TaskConfig(
                task_type=TaskType.HISTORICAL_UPDATE,
                cron_expression="0 2 * * *",
                retry_delay_seconds=-1
            )
        
        with pytest.raises(ValueError, match="timeout_seconds must be positive"):
            TaskConfig(
                task_type=TaskType.HISTORICAL_UPDATE,
                cron_expression="0 2 * * *",
                timeout_seconds=0
            )


class TestTaskExecution:
    """测试TaskExecution类"""
    
    def test_execution_creation(self):
        """测试执行记录创建"""
        execution = TaskExecution(
            task_id="test_task",
            execution_id="exec_123",
            task_type=TaskType.HISTORICAL_UPDATE,
            status=TaskStatus.SCHEDULED,
            scheduled_time=datetime.now()
        )
        
        assert execution.task_id == "test_task"
        assert execution.execution_id == "exec_123"
        assert execution.task_type == TaskType.HISTORICAL_UPDATE
        assert execution.status == TaskStatus.SCHEDULED
        assert not execution.is_running
        assert not execution.is_completed
    
    def test_execution_status_properties(self):
        """测试执行状态属性"""
        execution = TaskExecution(
            task_id="test_task",
            execution_id="exec_123",
            task_type=TaskType.HISTORICAL_UPDATE,
            status=TaskStatus.RUNNING,
            scheduled_time=datetime.now()
        )
        
        assert execution.is_running
        assert not execution.is_completed
        
        execution.status = TaskStatus.COMPLETED
        assert not execution.is_running
        assert execution.is_completed
        
        execution.status = TaskStatus.FAILED
        assert not execution.is_running
        assert execution.is_completed


class TestDefaultTaskExecutor:
    """测试DefaultTaskExecutor类"""
    
    @pytest.mark.asyncio
    async def test_historical_update_execution(self):
        """测试历史数据更新任务执行"""
        executor = DefaultTaskExecutor()
        
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            symbols=["AAPL", "GOOGL"],
            parameters={"expected_records": 2000}
        )
        
        execution = TaskExecution(
            task_id="test_task",
            execution_id="exec_123",
            task_type=TaskType.HISTORICAL_UPDATE,
            status=TaskStatus.RUNNING,
            scheduled_time=datetime.now()
        )
        
        result = await executor.execute(config, execution)
        
        assert result["symbols_processed"] == 2
        assert result["records_updated"] == 2000
        assert result["update_type"] == "historical"
        assert "message" in result
    
    @pytest.mark.asyncio
    async def test_data_migration_execution(self):
        """测试数据迁移任务执行"""
        executor = DefaultTaskExecutor()
        
        config = TaskConfig(
            task_type=TaskType.DATA_MIGRATION,
            cron_expression="0 3 * * *",
            parameters={"batch_size": 50000, "source": "hot", "target": "warm"}
        )
        
        execution = TaskExecution(
            task_id="migration_task",
            execution_id="exec_456",
            task_type=TaskType.DATA_MIGRATION,
            status=TaskStatus.RUNNING,
            scheduled_time=datetime.now()
        )
        
        result = await executor.execute(config, execution)
        
        assert result["records_migrated"] == 50000
        assert result["source_storage"] == "hot"
        assert result["target_storage"] == "warm"
        assert "message" in result
    
    @pytest.mark.asyncio
    async def test_unsupported_task_type(self):
        """测试不支持的任务类型"""
        executor = DefaultTaskExecutor()
        
        # 创建一个无效的任务类型（通过直接设置）
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *"
        )
        
        execution = TaskExecution(
            task_id="test_task",
            execution_id="exec_123",
            task_type=TaskType.HISTORICAL_UPDATE,
            status=TaskStatus.RUNNING,
            scheduled_time=datetime.now()
        )
        
        # 修改任务类型为无效值（模拟不支持的类型）
        with patch.object(config, 'task_type', 'INVALID_TYPE'):
            with pytest.raises(ValueError, match="Unsupported task type"):
                await executor.execute(config, execution)


class TestScheduledTaskManager:
    """测试ScheduledTaskManager类"""
    
    def setup_method(self):
        """测试前设置"""
        self.mock_executor = MockTaskExecutor()
        self.manager = ScheduledTaskManager(self.mock_executor)
    
    def teardown_method(self):
        """测试后清理"""
        if self.manager.running:
            self.manager.stop()
    
    def test_task_scheduling(self):
        """测试任务调度"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            symbols=["AAPL"],
            description="Test task"
        )
        
        result = self.manager.schedule_task("test_task", config)
        assert result is True
        assert "test_task" in self.manager.tasks
        assert self.manager.stats["total_scheduled"] == 1
    
    def test_duplicate_task_scheduling(self):
        """测试重复任务调度"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *"
        )
        
        # 第一次调度
        result1 = self.manager.schedule_task("test_task", config)
        assert result1 is True
        
        # 第二次调度（应该更新配置）
        config.description = "Updated task"
        result2 = self.manager.schedule_task("test_task", config)
        assert result2 is True
        assert self.manager.tasks["test_task"].description == "Updated task"
    
    def test_task_unscheduling(self):
        """测试任务取消调度"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *"
        )
        
        # 先调度任务
        self.manager.schedule_task("test_task", config)
        assert "test_task" in self.manager.tasks
        
        # 取消调度
        result = self.manager.unschedule_task("test_task")
        assert result is True
        assert "test_task" not in self.manager.tasks
    
    def test_unschedule_nonexistent_task(self):
        """测试取消不存在的任务"""
        result = self.manager.unschedule_task("nonexistent_task")
        assert result is False
    
    def test_disabled_task_scheduling(self):
        """测试禁用任务的调度"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            enabled=False
        )
        
        result = self.manager.schedule_task("disabled_task", config)
        assert result is True
        assert "disabled_task" in self.manager.tasks
        assert not self.manager.tasks["disabled_task"].enabled
    
    def test_task_dependency_validation(self):
        """测试任务依赖关系验证"""
        # 先创建依赖任务
        dep_config = TaskConfig(
            task_type=TaskType.DATA_CLEANUP,
            cron_expression="0 1 * * *"
        )
        self.manager.schedule_task("dependency_task", dep_config)
        
        # 创建有依赖的任务
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            dependencies=["dependency_task"]
        )
        
        result = self.manager.schedule_task("dependent_task", config)
        assert result is True
        assert "dependent_task" in self.manager.dependency_graph
        assert "dependency_task" in self.manager.dependency_graph["dependent_task"]
    
    def test_invalid_dependency(self):
        """测试无效的依赖关系"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            dependencies=["nonexistent_task"]
        )
        
        result = self.manager.schedule_task("dependent_task", config)
        assert result is False
        assert "dependent_task" not in self.manager.tasks
    
    def test_manager_start_stop(self):
        """测试调度器启动和停止"""
        assert not self.manager.running
        
        # 启动调度器
        self.manager.start()
        assert self.manager.running
        assert self.manager.scheduler_thread is not None
        
        # 停止调度器
        self.manager.stop()
        assert not self.manager.running
    
    def test_task_status_retrieval(self):
        """测试任务状态获取"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            symbols=["AAPL"],
            description="Test task"
        )
        
        self.manager.schedule_task("test_task", config)
        
        # 获取所有任务状态
        all_status = self.manager.get_all_task_status()
        assert len(all_status) == 1
        assert all_status[0]["task_id"] == "test_task"
        assert all_status[0]["task_type"] == TaskType.HISTORICAL_UPDATE.value
        assert all_status[0]["status"] == "never_run"
        
        # 获取特定任务状态
        task_status = self.manager.get_task_status("test_task")
        assert task_status is not None
        assert task_status["task_id"] == "test_task"
        
        # 获取不存在任务的状态
        nonexistent_status = self.manager.get_task_status("nonexistent")
        assert nonexistent_status is None
    
    def test_task_pause_resume(self):
        """测试任务暂停和恢复"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *"
        )
        
        self.manager.schedule_task("test_task", config)
        assert self.manager.tasks["test_task"].enabled
        
        # 暂停任务
        result = self.manager.pause_task("test_task")
        assert result is True
        assert not self.manager.tasks["test_task"].enabled
        
        # 恢复任务
        result = self.manager.resume_task("test_task")
        assert result is True
        assert self.manager.tasks["test_task"].enabled
        
        # 暂停不存在的任务
        result = self.manager.pause_task("nonexistent")
        assert result is False
    
    def test_statistics(self):
        """测试统计信息"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *"
        )
        
        self.manager.schedule_task("test_task", config)
        
        stats = self.manager.get_statistics()
        assert stats["total_tasks"] == 1
        assert stats["enabled_tasks"] == 1
        assert stats["running_tasks"] == 0
        assert stats["scheduler_running"] is False
        assert stats["total_scheduled"] == 1
    
    def test_execution_history(self):
        """测试执行历史记录"""
        # 初始状态下没有执行历史
        history = self.manager.get_execution_history()
        assert len(history) == 0
        
        # 手动添加执行记录进行测试
        execution = TaskExecution(
            task_id="test_task",
            execution_id="exec_123",
            task_type=TaskType.HISTORICAL_UPDATE,
            status=TaskStatus.COMPLETED,
            scheduled_time=datetime.now(),
            start_time=datetime.now(),
            end_time=datetime.now()
        )
        
        self.manager.execution_history.append(execution)
        
        # 获取执行历史
        history = self.manager.get_execution_history()
        assert len(history) == 1
        assert history[0]["task_id"] == "test_task"
        assert history[0]["status"] == TaskStatus.COMPLETED.value
        
        # 获取特定任务的执行历史
        task_history = self.manager.get_execution_history("test_task")
        assert len(task_history) == 1
        assert task_history[0]["task_id"] == "test_task"
        
        # 获取不存在任务的执行历史
        empty_history = self.manager.get_execution_history("nonexistent")
        assert len(empty_history) == 0
    
    def test_configuration_export_import(self):
        """测试配置导出和导入"""
        # 创建一些任务
        config1 = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            symbols=["AAPL"],
            description="Task 1"
        )
        
        config2 = TaskConfig(
            task_type=TaskType.DATA_MIGRATION,
            cron_expression="0 3 * * *",
            priority=TaskPriority.HIGH,
            description="Task 2"
        )
        
        self.manager.schedule_task("task1", config1)
        self.manager.schedule_task("task2", config2)
        
        # 导出配置
        exported_config = self.manager.export_configuration()
        assert "tasks" in exported_config
        assert "statistics" in exported_config
        assert "export_time" in exported_config
        assert len(exported_config["tasks"]) == 2
        
        # 创建新的管理器并导入配置
        new_manager = ScheduledTaskManager(self.mock_executor)
        result = new_manager.import_configuration(exported_config)
        assert result is True
        assert len(new_manager.tasks) == 2
        assert "task1" in new_manager.tasks
        assert "task2" in new_manager.tasks
        
        # 验证导入的任务配置
        imported_task1 = new_manager.tasks["task1"]
        assert imported_task1.task_type == TaskType.HISTORICAL_UPDATE
        assert imported_task1.symbols == ["AAPL"]
        assert imported_task1.description == "Task 1"
        
        imported_task2 = new_manager.tasks["task2"]
        assert imported_task2.task_type == TaskType.DATA_MIGRATION
        assert imported_task2.priority == TaskPriority.HIGH
        assert imported_task2.description == "Task 2"
    
    def test_task_config_update(self):
        """测试任务配置更新"""
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            description="Original task"
        )
        
        self.manager.schedule_task("test_task", config)
        
        # 更新配置
        new_config = TaskConfig(
            task_type=TaskType.DATA_MIGRATION,
            cron_expression="0 3 * * *",
            description="Updated task"
        )
        
        result = self.manager.update_task_config("test_task", new_config)
        assert result is True
        
        updated_task = self.manager.tasks["test_task"]
        assert updated_task.task_type == TaskType.DATA_MIGRATION
        assert updated_task.cron_expression == "0 3 * * *"
        assert updated_task.description == "Updated task"
        
        # 更新不存在的任务
        result = self.manager.update_task_config("nonexistent", new_config)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_task_execution_with_mock_executor(self):
        """测试使用模拟执行器的任务执行"""
        # 设置模拟执行器的结果
        self.mock_executor.set_execution_result("test_task", {
            "status": "success",
            "records_processed": 500,
            "message": "Test execution completed"
        })
        
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            symbols=["AAPL"]
        )
        
        execution = TaskExecution(
            task_id="test_task",
            execution_id="exec_123",
            task_type=TaskType.HISTORICAL_UPDATE,
            status=TaskStatus.SCHEDULED,
            scheduled_time=datetime.now()
        )
        
        # 直接调用异步执行方法进行测试
        await self.manager._execute_task_async(config, execution)
        
        # 验证执行结果
        assert execution.status == TaskStatus.COMPLETED
        assert execution.result is not None
        assert execution.result["records_processed"] == 500
        assert execution.execution_duration_seconds > 0
        
        # 验证模拟执行器被调用
        assert len(self.mock_executor.executed_tasks) == 1
        executed_config, executed_execution = self.mock_executor.executed_tasks[0]
        assert executed_config.task_type == TaskType.HISTORICAL_UPDATE
        assert executed_execution.task_id == "test_task"
    
    @pytest.mark.asyncio
    async def test_task_execution_with_retry(self):
        """测试任务执行重试机制"""
        # 设置任务失败
        self.mock_executor.set_should_fail("test_task", True)
        
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            max_retries=2,
            retry_delay_seconds=0.1  # 快速重试用于测试
        )
        
        execution = TaskExecution(
            task_id="test_task",
            execution_id="exec_123",
            task_type=TaskType.HISTORICAL_UPDATE,
            status=TaskStatus.SCHEDULED,
            scheduled_time=datetime.now()
        )
        
        # 执行任务（应该失败并重试）
        await self.manager._execute_task_async(config, execution)
        
        # 验证重试次数
        assert execution.retry_count == 2  # 最大重试次数
        assert execution.status == TaskStatus.FAILED
        assert "Mock failure" in execution.error_message
        
        # 验证执行器被调用了3次（初始执行 + 2次重试）
        assert len(self.mock_executor.executed_tasks) == 3
    
    @pytest.mark.asyncio
    async def test_task_execution_timeout(self):
        """测试任务执行超时"""
        # 设置长延迟模拟超时
        self.mock_executor.set_execution_delay("test_task", 2.0)
        
        config = TaskConfig(
            task_type=TaskType.HISTORICAL_UPDATE,
            cron_expression="0 2 * * *",
            timeout_seconds=0.5,  # 短超时时间
            max_retries=1,
            retry_delay_seconds=0.1
        )
        
        execution = TaskExecution(
            task_id="test_task",
            execution_id="exec_123",
            task_type=TaskType.HISTORICAL_UPDATE,
            status=TaskStatus.SCHEDULED,
            scheduled_time=datetime.now()
        )
        
        # 执行任务（应该超时）
        await self.manager._execute_task_async(config, execution)
        
        # 验证超时处理
        assert execution.status == TaskStatus.FAILED
        assert "timed out" in execution.error_message
        assert execution.retry_count == 1  # 应该重试一次
    
    def test_callback_functions(self):
        """测试回调函数"""
        start_callback = Mock()
        complete_callback = Mock()
        error_callback = Mock()
        
        self.manager.set_task_callbacks(
            start_callback=start_callback,
            complete_callback=complete_callback,
            error_callback=error_callback
        )
        
        assert self.manager.task_start_callback == start_callback
        assert self.manager.task_complete_callback == complete_callback
        assert self.manager.task_error_callback == error_callback


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])