# Build directories
build/
cmake-build-*/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Compiled Object files
*.o
*.obj

# Executables
*.exe
*.out
*.app
financial_data_service
financial_data_tests

# Libraries
*.lib
*.a
*.la
*.lo
*.dll
*.so
*.dylib

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake
!CMakeLists.txt

# Testing
Testing/
CTestTestfile.cmake

# Logs
*.log
logs/

# Configuration files with secrets
config/secrets.json
config/production.json

# Docker volumes
docker-volumes/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package files
*.tar.gz
*.zip
*.deb
*.rpm