#!/usr/bin/env python3
"""
Development server runner for Financial Data Service API
"""

import uvicorn
import logging
import sys
import os

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from api.config import APIConfig
from api.main import app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """Run the development server"""
    config = APIConfig()
    
    logger.info("Starting Financial Data Service API...")
    logger.info(f"Server will run on {config.api_host}:{config.api_port}")
    logger.info(f"Debug mode: {config.debug}")
    logger.info(f"API documentation available at: http://{config.api_host}:{config.api_port}/docs")
    
    # Run the server
    uvicorn.run(
        "api.main:app",
        host=config.api_host,
        port=config.api_port,
        reload=config.debug,
        log_level="info" if not config.debug else "debug",
        access_log=True
    )


if __name__ == "__main__":
    main()