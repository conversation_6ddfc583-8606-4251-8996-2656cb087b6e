global
    daemon
    maxconn 4096
    log stdout local0
    
    # SSL Configuration
    ssl-default-bind-ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384
    ssl-default-bind-options no-sslv3 no-tlsv10 no-tlsv11
    
    # Performance tuning
    tune.ssl.default-dh-param 2048
    tune.bufsize 32768
    tune.maxrewrite 1024

defaults
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    option httplog
    option dontlognull
    option redispatch
    retries 3
    
    # Health check
    option httpchk GET /health
    
    # Load balancing algorithm
    balance roundrobin

# Statistics interface
stats enable
stats uri /stats
stats refresh 30s
stats admin if TRUE

# Frontend for HTTP traffic
frontend financial_http_frontend
    bind *:80
    bind *:443 ssl crt /etc/ssl/certs/financial-data-service.pem
    
    # Redirect HTTP to HTTPS
    redirect scheme https if !{ ssl_fc }
    
    # HSTS header
    http-response set-header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Security headers
    http-response set-header X-Frame-Options DENY
    http-response set-header X-Content-Type-Options nosniff
    http-response set-header X-XSS-Protection "1; mode=block"
    
    # Route to appropriate backend
    use_backend financial_api_backend if { path_beg /api }
    use_backend financial_websocket_backend if { hdr(upgrade) -i websocket }
    use_backend financial_web_backend
    
# Frontend for WebSocket traffic
frontend financial_websocket_frontend
    bind *:8080
    bind *:8443 ssl crt /etc/ssl/certs/financial-data-service.pem
    mode http
    option httplog
    
    # WebSocket upgrade handling
    acl is_websocket hdr(Upgrade) -i websocket
    acl is_websocket_key hdr_cnt(Sec-WebSocket-Key) eq 1
    
    use_backend financial_websocket_backend if is_websocket is_websocket_key
    default_backend financial_websocket_backend

# Frontend for gRPC traffic
frontend financial_grpc_frontend
    bind *:50051
    mode tcp
    option tcplog
    
    default_backend financial_grpc_backend

# Backend for API services
backend financial_api_backend
    mode http
    option httpchk GET /health
    
    # Sticky sessions for API
    cookie SERVERID insert indirect nocache
    
    server financial-app-1 financial-app-1:8080 check cookie s1 weight 100
    server financial-app-2 financial-app-2:8080 check cookie s2 weight 100 backup

# Backend for WebSocket services
backend financial_websocket_backend
    mode http
    option httpchk GET /ws/health
    
    # WebSocket specific settings
    timeout tunnel 3600s
    timeout server 3600s
    
    # Sticky sessions for WebSocket connections
    stick-table type ip size 100k expire 30m
    stick on src
    
    server financial-app-1 financial-app-1:8080 check weight 100
    server financial-app-2 financial-app-2:8080 check weight 100 backup

# Backend for gRPC services
backend financial_grpc_backend
    mode tcp
    option tcp-check
    
    # gRPC health check
    tcp-check connect
    tcp-check send-binary 00000000 # gRPC health check frame
    tcp-check expect binary 00000000
    
    server financial-app-1 financial-app-1:50051 check weight 100
    server financial-app-2 financial-app-2:50051 check weight 100 backup

# Backend for web interface
backend financial_web_backend
    mode http
    option httpchk GET /health
    
    server financial-app-1 financial-app-1:3000 check weight 100
    server financial-app-2 financial-app-2:3000 check weight 100 backup

# Backend for monitoring (Grafana)
backend monitoring_backend
    mode http
    option httpchk GET /api/health
    
    server grafana financial-grafana:3000 check

# Rate limiting configuration
frontend rate_limit_frontend
    bind *:8081
    
    # Rate limiting: 1000 requests per minute per IP
    stick-table type ip size 100k expire 60s store http_req_rate(60s)
    http-request track-sc0 src
    http-request deny if { sc_http_req_rate(0) gt 1000 }
    
    default_backend financial_api_backend

# Health check endpoints
listen health_check
    bind *:8082
    mode http
    
    # Simple health check response
    monitor-uri /health
    
    # Detailed health check
    stats enable
    stats uri /stats
    stats refresh 5s