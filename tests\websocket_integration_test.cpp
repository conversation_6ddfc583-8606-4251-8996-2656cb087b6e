#include <gtest/gtest.h>
#include <thread>
#include <chrono>
#include <vector>
#include <atomic>
#include <future>
#include <websocketpp/config/asio_no_tls_client.hpp>
#include <websocketpp/client.hpp>

#include "../src/interfaces/websocket_server.h"
#include "../src/interfaces/websocket_handler.h"
#include "../src/databus/data_bus.h"
#include "../src/proto/data_types.h"

using namespace financial_data;
using namespace financial_data::interfaces;
using namespace financial_data::databus;

// WebSocket客户端类型定义
using WebSocketClient = websocketpp::client<websocketpp::config::asio_client>;
using ClientConnectionHdl = websocketpp::connection_hdl;
using ClientMessagePtr = WebSocketClient::message_ptr;

/**
 * @brief WebSocket测试客户端
 */
class TestWebSocketClient {
private:
    WebSocketClient client_;
    std::thread client_thread_;
    std::atomic<bool> connected_{false};
    std::atomic<bool> running_{false};
    
    std::vector<std::string> received_messages_;
    std::mutex messages_mutex_;
    
    std::string server_uri_;
    ClientConnectionHdl connection_hdl_;
    
    std::atomic<uint64_t> messages_received_{0};
    std::atomic<uint64_t> bytes_received_{0};
    std::chrono::steady_clock::time_point connect_time_;
    std::chrono::steady_clock::time_point last_message_time_;

public:
    explicit TestWebSocketClient(const std::string& uri) : server_uri_(uri) {
        client_.set_access_channels(websocketpp::log::alevel::none);
        client_.set_error_channels(websocketpp::log::elevel::none);
        client_.init_asio();
        
        // 设置事件处理器
        client_.set_open_handler([this](ClientConnectionHdl hdl) {
            connected_ = true;
            connection_hdl_ = hdl;
            connect_time_ = std::chrono::steady_clock::now();
        });
        
        client_.set_close_handler([this](ClientConnectionHdl hdl) {
            connected_ = false;
        });
        
        client_.set_message_handler([this](ClientConnectionHdl hdl, ClientMessagePtr msg) {
            std::lock_guard<std::mutex> lock(messages_mutex_);
            received_messages_.push_back(msg->get_payload());
            messages_received_++;
            bytes_received_ += msg->get_payload().size();
            last_message_time_ = std::chrono::steady_clock::now();
        });
    }
    
    ~TestWebSocketClient() {
        Disconnect();
    }
    
    bool Connect() {
        try {
            websocketpp::lib::error_code ec;
            auto con = client_.get_connection(server_uri_, ec);
            if (ec) {
                return false;
            }
            
            client_.connect(con);
            
            running_ = true;
            client_thread_ = std::thread([this]() {
                client_.run();
            });
            
            // 等待连接建立
            for (int i = 0; i < 50 && !connected_; ++i) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
            
            return connected_;
            
        } catch (const std::exception& e) {
            return false;
        }
    }
    
    void Disconnect() {
        if (running_) {
            running_ = false;
            
            if (connected_) {
                try {
                    client_.close(connection_hdl_, websocketpp::close::status::normal, "Test completed");
                } catch (...) {
                    // 忽略关闭错误
                }
            }
            
            client_.stop();
            
            if (client_thread_.joinable()) {
                client_thread_.join();
            }
        }
    }
    
    bool SendMessage(const std::string& message) {
        if (!connected_) {
            return false;
        }
        
        try {
            websocketpp::lib::error_code ec;
            client_.send(connection_hdl_, message, websocketpp::frame::opcode::text, ec);
            return !ec;
        } catch (const std::exception& e) {
            return false;
        }
    }
    
    bool IsConnected() const {
        return connected_;
    }
    
    size_t GetReceivedMessageCount() const {
        return messages_received_;
    }
    
    size_t GetReceivedBytesCount() const {
        return bytes_received_;
    }
    
    std::vector<std::string> GetReceivedMessages() const {
        std::lock_guard<std::mutex> lock(messages_mutex_);
        return received_messages_;
    }
    
    void ClearReceivedMessages() {
        std::lock_guard<std::mutex> lock(messages_mutex_);
        received_messages_.clear();
        messages_received_ = 0;
        bytes_received_ = 0;
    }
    
    std::chrono::milliseconds GetConnectionDuration() const {
        if (!connected_) {
            return std::chrono::milliseconds(0);
        }
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - connect_time_);
    }
    
    std::chrono::milliseconds GetTimeSinceLastMessage() const {
        if (messages_received_ == 0) {
            return std::chrono::milliseconds(0);
        }
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - last_message_time_);
    }
};

/**
 * @brief WebSocket集成测试类
 */
class WebSocketIntegrationTest : public ::testing::Test {
protected:
    std::unique_ptr<WebSocketServer> server_;
    std::shared_ptr<DataBus> data_bus_;
    uint16_t server_port_;
    
    void SetUp() override {
        // 使用随机端口避免冲突
        server_port_ = 18080 + (rand() % 1000);
        
        // 创建数据总线
        DataBusConfig bus_config;
        bus_config.enable_kafka = false;  // 测试时禁用Kafka
        data_bus_ = std::make_unique<DataBus>(bus_config);
        ASSERT_TRUE(data_bus_->Start());
        
        // 创建WebSocket服务器
        WebSocketConfig ws_config;
        ws_config.port = server_port_;
        ws_config.max_connections = 1000;
        ws_config.enable_compression = true;
        ws_config.enable_batching = true;
        ws_config.batch_size = 10;
        ws_config.batch_timeout_ms = 100;
        ws_config.enable_heartbeat = true;
        ws_config.heartbeat_interval_ms = 5000;
        ws_config.heartbeat_timeout_ms = 10000;
        
        server_ = std::make_unique<WebSocketServer>(ws_config);
        server_->SetDataBus(data_bus_);
        
        ASSERT_TRUE(server_->Initialize());
        
        // 在后台线程启动服务器
        std::thread([this]() {
            server_->Start();
        }).detach();
        
        // 等待服务器启动
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    void TearDown() override {
        if (server_) {
            server_->Stop();
        }
        if (data_bus_) {
            data_bus_->Stop();
        }
    }
    
    std::string GetServerUri() const {
        return "ws://localhost:" + std::to_string(server_port_);
    }
    
    StandardTick CreateTestTick(const std::string& symbol = "CU2409", double price = 78560.0) {
        StandardTick tick;
        tick.SetCurrentTimestamp();
        tick.symbol = symbol;
        tick.exchange = "SHFE";
        tick.last_price = price;
        tick.volume = 1000;
        tick.turnover = price * 1000;
        tick.sequence = rand() % 1000000;
        
        // 设置买卖盘数据
        for (int i = 0; i < 5; ++i) {
            tick.bids[i] = PriceLevel(price - (i + 1) * 10, 100 + i * 10, i + 1);
            tick.asks[i] = PriceLevel(price + (i + 1) * 10, 100 + i * 10, i + 1);
        }
        
        return tick;
    }
};

/**
 * @brief 测试基本连接功能
 */
TEST_F(WebSocketIntegrationTest, BasicConnection) {
    TestWebSocketClient client(GetServerUri());
    
    // 测试连接
    ASSERT_TRUE(client.Connect());
    EXPECT_TRUE(client.IsConnected());
    
    // 等待一段时间确保连接稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    EXPECT_TRUE(client.IsConnected());
    
    // 检查服务器统计
    auto stats = server_->GetStatistics();
    EXPECT_GE(stats.active_connections.load(), 1);
    
    client.Disconnect();
    
    // 等待断开连接处理
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    stats = server_->GetStatistics();
    EXPECT_EQ(stats.active_connections.load(), 0);
}

/**
 * @brief 测试订阅功能
 */
TEST_F(WebSocketIntegrationTest, SubscriptionFunctionality) {
    TestWebSocketClient client(GetServerUri());
    ASSERT_TRUE(client.Connect());
    
    // 发送订阅请求
    nlohmann::json subscribe_msg;
    subscribe_msg["type"] = static_cast<int>(MessageType::SUBSCRIBE);
    subscribe_msg["payload"]["symbols"] = std::vector<std::string>{"CU2409", "AL2409"};
    subscribe_msg["payload"]["exchanges"] = std::vector<std::string>{"SHFE"};
    subscribe_msg["payload"]["data_types"] = std::vector<std::string>{"tick"};
    
    ASSERT_TRUE(client.SendMessage(subscribe_msg.dump()));
    
    // 等待订阅响应
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    auto messages = client.GetReceivedMessages();
    EXPECT_GT(messages.size(), 0);
    
    // 验证订阅响应
    bool subscription_confirmed = false;
    for (const auto& msg : messages) {
        try {
            auto json_msg = nlohmann::json::parse(msg);
            if (json_msg.contains("type") && json_msg["type"] == "subscription_response") {
                EXPECT_TRUE(json_msg["success"]);
                subscription_confirmed = true;
                break;
            }
        } catch (...) {
            // 忽略解析错误
        }
    }
    
    EXPECT_TRUE(subscription_confirmed);
    
    // 检查订阅管理器统计
    auto sub_manager = server_->GetSubscriptionManager();
    ASSERT_NE(sub_manager, nullptr);
    
    auto sub_stats = sub_manager->GetStatistics();
    EXPECT_GT(sub_stats.active_subscriptions, 0);
}

/**
 * @brief 测试市场数据分发
 */
TEST_F(WebSocketIntegrationTest, MarketDataDistribution) {
    TestWebSocketClient client(GetServerUri());
    ASSERT_TRUE(client.Connect());
    
    // 订阅数据
    nlohmann::json subscribe_msg;
    subscribe_msg["type"] = static_cast<int>(MessageType::SUBSCRIBE);
    subscribe_msg["payload"]["symbols"] = std::vector<std::string>{"CU2409"};
    subscribe_msg["payload"]["data_types"] = std::vector<std::string>{"tick"};
    
    ASSERT_TRUE(client.SendMessage(subscribe_msg.dump()));
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    client.ClearReceivedMessages();
    
    // 发送测试数据到数据总线
    auto test_tick = CreateTestTick("CU2409", 78560.0);
    ASSERT_TRUE(data_bus_->PushTick(test_tick));
    
    // 等待数据分发
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 验证客户端收到数据
    EXPECT_GT(client.GetReceivedMessageCount(), 0);
    
    auto messages = client.GetReceivedMessages();
    bool market_data_received = false;
    
    for (const auto& msg : messages) {
        try {
            auto json_msg = nlohmann::json::parse(msg);
            if (json_msg.contains("messages") || json_msg.contains("type")) {
                market_data_received = true;
                break;
            }
        } catch (...) {
            // 忽略解析错误
        }
    }
    
    EXPECT_TRUE(market_data_received);
}

/**
 * @brief 测试并发连接
 */
TEST_F(WebSocketIntegrationTest, ConcurrentConnections) {
    const size_t num_clients = 50;  // 测试50个并发连接
    std::vector<std::unique_ptr<TestWebSocketClient>> clients;
    
    // 创建并连接客户端
    for (size_t i = 0; i < num_clients; ++i) {
        auto client = std::make_unique<TestWebSocketClient>(GetServerUri());
        ASSERT_TRUE(client->Connect());
        clients.push_back(std::move(client));
    }
    
    // 等待所有连接建立
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 验证所有客户端都已连接
    size_t connected_count = 0;
    for (const auto& client : clients) {
        if (client->IsConnected()) {
            connected_count++;
        }
    }
    
    EXPECT_EQ(connected_count, num_clients);
    
    // 检查服务器统计
    auto stats = server_->GetStatistics();
    EXPECT_GE(stats.active_connections.load(), num_clients);
    
    // 测试并发订阅
    for (size_t i = 0; i < clients.size(); ++i) {
        nlohmann::json subscribe_msg;
        subscribe_msg["type"] = static_cast<int>(MessageType::SUBSCRIBE);
        subscribe_msg["payload"]["symbols"] = std::vector<std::string>{"TEST" + std::to_string(i % 10)};
        subscribe_msg["payload"]["data_types"] = std::vector<std::string>{"tick"};
        
        clients[i]->SendMessage(subscribe_msg.dump());
    }
    
    // 等待订阅处理
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 发送测试数据
    for (int i = 0; i < 10; ++i) {
        auto test_tick = CreateTestTick("TEST" + std::to_string(i), 100.0 + i);
        data_bus_->PushTick(test_tick);
    }
    
    // 等待数据分发
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 验证客户端收到数据
    size_t clients_with_data = 0;
    for (const auto& client : clients) {
        if (client->GetReceivedMessageCount() > 0) {
            clients_with_data++;
        }
    }
    
    EXPECT_GT(clients_with_data, 0);
    
    // 断开所有连接
    clients.clear();
    
    // 等待断开连接处理
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    stats = server_->GetStatistics();
    EXPECT_EQ(stats.active_connections.load(), 0);
}

/**
 * @brief 测试心跳机制
 */
TEST_F(WebSocketIntegrationTest, HeartbeatMechanism) {
    TestWebSocketClient client(GetServerUri());
    ASSERT_TRUE(client.Connect());
    
    // 发送心跳消息
    nlohmann::json heartbeat_msg;
    heartbeat_msg["type"] = static_cast<int>(MessageType::HEARTBEAT);
    heartbeat_msg["timestamp_ns"] = StandardTick::GetCurrentTimestampNs();
    
    ASSERT_TRUE(client.SendMessage(heartbeat_msg.dump()));
    
    // 等待心跳响应
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    auto messages = client.GetReceivedMessages();
    bool heartbeat_response_received = false;
    
    for (const auto& msg : messages) {
        try {
            auto json_msg = nlohmann::json::parse(msg);
            if (json_msg.contains("type") && json_msg["type"] == "heartbeat_response") {
                heartbeat_response_received = true;
                break;
            }
        } catch (...) {
            // 忽略解析错误
        }
    }
    
    EXPECT_TRUE(heartbeat_response_received);
    
    // 检查心跳管理器
    auto heartbeat_manager = server_->GetHeartbeatManager();
    ASSERT_NE(heartbeat_manager, nullptr);
    
    auto hb_stats = heartbeat_manager->GetStatistics();
    EXPECT_GT(hb_stats.total_heartbeats.load(), 0);
}

/**
 * @brief 测试消息压缩
 */
TEST_F(WebSocketIntegrationTest, MessageCompression) {
    TestWebSocketClient client(GetServerUri());
    ASSERT_TRUE(client.Connect());
    
    // 订阅数据
    nlohmann::json subscribe_msg;
    subscribe_msg["type"] = static_cast<int>(MessageType::SUBSCRIBE);
    subscribe_msg["payload"]["symbols"] = std::vector<std::string>{"COMPRESS_TEST"};
    subscribe_msg["payload"]["data_types"] = std::vector<std::string>{"tick"};
    
    ASSERT_TRUE(client.SendMessage(subscribe_msg.dump()));
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    client.ClearReceivedMessages();
    
    // 发送大量数据触发压缩
    for (int i = 0; i < 100; ++i) {
        auto test_tick = CreateTestTick("COMPRESS_TEST", 100.0 + i);
        data_bus_->PushTick(test_tick);
    }
    
    // 等待数据处理和压缩
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    // 验证收到数据
    EXPECT_GT(client.GetReceivedMessageCount(), 0);
    
    // 检查压缩统计
    auto stats = server_->GetStatistics();
    if (stats.compressed_messages.load() > 0) {
        EXPECT_GT(stats.compression_ratio_percent.load(), 0);
        EXPECT_LT(stats.compression_ratio_percent.load(), 100);
    }
}

/**
 * @brief 测试批量消息处理
 */
TEST_F(WebSocketIntegrationTest, BatchMessageProcessing) {
    TestWebSocketClient client(GetServerUri());
    ASSERT_TRUE(client.Connect());
    
    // 订阅数据
    nlohmann::json subscribe_msg;
    subscribe_msg["type"] = static_cast<int>(MessageType::SUBSCRIBE);
    subscribe_msg["payload"]["symbols"] = std::vector<std::string>{"BATCH_TEST"};
    subscribe_msg["payload"]["data_types"] = std::vector<std::string>{"tick"};
    
    ASSERT_TRUE(client.SendMessage(subscribe_msg.dump()));
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    client.ClearReceivedMessages();
    
    // 快速发送多条数据触发批处理
    for (int i = 0; i < 20; ++i) {
        auto test_tick = CreateTestTick("BATCH_TEST", 100.0 + i);
        data_bus_->PushTick(test_tick);
    }
    
    // 等待批处理
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 验证收到批量消息
    auto messages = client.GetReceivedMessages();
    bool batch_message_received = false;
    
    for (const auto& msg : messages) {
        try {
            auto json_msg = nlohmann::json::parse(msg);
            if (json_msg.contains("batch_timestamp_ns") && json_msg.contains("messages")) {
                batch_message_received = true;
                EXPECT_GT(json_msg["messages"].size(), 1);  // 批量消息应该包含多条数据
                break;
            }
        } catch (...) {
            // 忽略解析错误
        }
    }
    
    EXPECT_TRUE(batch_message_received);
}

/**
 * @brief 测试延迟性能
 */
TEST_F(WebSocketIntegrationTest, LatencyPerformance) {
    TestWebSocketClient client(GetServerUri());
    ASSERT_TRUE(client.Connect());
    
    // 订阅数据
    nlohmann::json subscribe_msg;
    subscribe_msg["type"] = static_cast<int>(MessageType::SUBSCRIBE);
    subscribe_msg["payload"]["symbols"] = std::vector<std::string>{"LATENCY_TEST"};
    subscribe_msg["payload"]["data_types"] = std::vector<std::string>{"tick"};
    
    ASSERT_TRUE(client.SendMessage(subscribe_msg.dump()));
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    client.ClearReceivedMessages();
    
    // 测量端到端延迟
    const int test_count = 100;
    std::vector<uint64_t> latencies;
    latencies.reserve(test_count);
    
    for (int i = 0; i < test_count; ++i) {
        auto start_time = std::chrono::high_resolution_clock::now();
        
        auto test_tick = CreateTestTick("LATENCY_TEST", 100.0 + i);
        test_tick.sequence = i;  // 用于标识消息
        
        ASSERT_TRUE(data_bus_->PushTick(test_tick));
        
        // 等待消息到达
        size_t initial_count = client.GetReceivedMessageCount();
        while (client.GetReceivedMessageCount() <= initial_count) {
            std::this_thread::sleep_for(std::chrono::microseconds(10));
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto latency_us = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time).count();
        
        latencies.push_back(latency_us);
        
        // 短暂休眠避免过快发送
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 计算延迟统计
    uint64_t total_latency = 0;
    uint64_t max_latency = 0;
    uint64_t min_latency = UINT64_MAX;
    
    for (uint64_t latency : latencies) {
        total_latency += latency;
        max_latency = std::max(max_latency, latency);
        min_latency = std::min(min_latency, latency);
    }
    
    double avg_latency = static_cast<double>(total_latency) / latencies.size();
    
    std::cout << "Latency Performance Results:" << std::endl;
    std::cout << "  Average: " << avg_latency << " μs" << std::endl;
    std::cout << "  Min: " << min_latency << " μs" << std::endl;
    std::cout << "  Max: " << max_latency << " μs" << std::endl;
    
    // 验证延迟要求（50微秒内）
    EXPECT_LT(avg_latency, 50000);  // 50毫秒作为宽松的测试要求
    
    // 验证收到所有消息
    EXPECT_EQ(client.GetReceivedMessageCount(), test_count);
}

/**
 * @brief 测试错误处理
 */
TEST_F(WebSocketIntegrationTest, ErrorHandling) {
    TestWebSocketClient client(GetServerUri());
    ASSERT_TRUE(client.Connect());
    
    // 发送无效的订阅请求
    nlohmann::json invalid_msg;
    invalid_msg["type"] = static_cast<int>(MessageType::SUBSCRIBE);
    // 缺少必要的payload字段
    
    ASSERT_TRUE(client.SendMessage(invalid_msg.dump()));
    
    // 等待错误响应
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    auto messages = client.GetReceivedMessages();
    bool error_response_received = false;
    
    for (const auto& msg : messages) {
        try {
            auto json_msg = nlohmann::json::parse(msg);
            if (json_msg.contains("type") && json_msg["type"] == "error") {
                error_response_received = true;
                break;
            }
        } catch (...) {
            // 忽略解析错误
        }
    }
    
    EXPECT_TRUE(error_response_received);
    
    // 检查错误统计
    auto stats = server_->GetStatistics();
    EXPECT_GT(stats.protocol_errors.load(), 0);
}

/**
 * @brief 测试服务器健康状态
 */
TEST_F(WebSocketIntegrationTest, ServerHealthStatus) {
    // 检查初始健康状态
    auto health = server_->GetHealthStatus();
    EXPECT_TRUE(health.overall_healthy);
    EXPECT_EQ(health.active_connections, 0);
    
    // 连接一些客户端
    std::vector<std::unique_ptr<TestWebSocketClient>> clients;
    for (int i = 0; i < 10; ++i) {
        auto client = std::make_unique<TestWebSocketClient>(GetServerUri());
        ASSERT_TRUE(client->Connect());
        clients.push_back(std::move(client));
    }
    
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 检查健康状态
    health = server_->GetHealthStatus();
    EXPECT_TRUE(health.overall_healthy);
    EXPECT_EQ(health.active_connections, 10);
    EXPECT_LT(health.connection_usage, 1.0);  // 使用率应该小于100%
    
    // 清理
    clients.clear();
}

/**
 * @brief 性能基准测试
 */
TEST_F(WebSocketIntegrationTest, PerformanceBenchmark) {
    // 运行服务器性能测试
    auto perf_result = server_->RunPerformanceTest(10, 100, 5000);
    
    EXPECT_TRUE(perf_result.success);
    if (perf_result.success) {
        std::cout << "Performance Benchmark Results:" << std::endl;
        std::cout << "  Messages Sent: " << perf_result.total_messages_sent << std::endl;
        std::cout << "  Clients Connected: " << perf_result.total_clients_connected << std::endl;
        std::cout << "  Average Latency: " << perf_result.avg_latency_ms << " ms" << std::endl;
        std::cout << "  Max Latency: " << perf_result.max_latency_ms << " ms" << std::endl;
        std::cout << "  Throughput: " << perf_result.throughput_msg_per_sec << " msg/s" << std::endl;
        std::cout << "  Test Duration: " << perf_result.test_duration_ms << " ms" << std::endl;
        
        // 验证性能指标
        EXPECT_GT(perf_result.throughput_msg_per_sec, 1000);  // 至少1000 msg/s
        EXPECT_LT(perf_result.avg_latency_ms, 100);           // 平均延迟小于100ms
    } else {
        FAIL() << "Performance test failed: " << perf_result.error_message;
    }
}