#!/usr/bin/env python3
"""
Docker部署测试脚本
验证使用国内镜像源的Docker服务部署效果
"""

import asyncio
import subprocess
import time
import json
import sys
import os
from datetime import datetime

def print_header():
    print("=" * 60)
    print("    Docker镜像源部署测试")
    print("=" * 60)
    print()

def print_info(msg):
    print(f"[INFO] {msg}")

def print_success(msg):
    print(f"[SUCCESS] ✅ {msg}")

def print_error(msg):
    print(f"[ERROR] ❌ {msg}")

def print_warning(msg):
    print(f"[WARNING] ⚠️  {msg}")

def check_docker_mirrors():
    """检查Docker镜像源配置"""
    print_info("检查Docker镜像源配置...")
    
    try:
        with open('/etc/docker/daemon.json', 'r') as f:
            config = json.load(f)
            mirrors = config.get('registry-mirrors', [])
            
        print_success(f"已配置 {len(mirrors)} 个镜像源:")
        for i, mirror in enumerate(mirrors, 1):
            print(f"  {i}. {mirror}")
        
        return True
    except Exception as e:
        print_error(f"读取Docker配置失败: {e}")
        return False

def check_container_status():
    """检查容器状态"""
    print_info("检查容器状态...")
    
    try:
        result = subprocess.run(['docker-compose', '-f', 'docker-compose.simple.yml', 'ps'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 2:  # 有容器在运行
                print_success("容器状态:")
                for line in lines:
                    if 'financial-' in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            name = parts[0]
                            state = parts[2]
                            print(f"  • {name}: {state}")
                return True
            else:
                print_warning("没有运行的容器")
                return False
        else:
            print_error("获取容器状态失败")
            return False
            
    except Exception as e:
        print_error(f"检查容器状态异常: {e}")
        return False

def test_redis_connection():
    """测试Redis连接"""
    print_info("测试Redis连接...")
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
        
        # 测试连接
        response = r.ping()
        if response:
            print_success("Redis连接正常")
            
            # 测试基本操作
            test_key = f"docker_test_{int(time.time())}"
            r.set(test_key, "Docker镜像源测试", ex=60)
            value = r.get(test_key)
            
            if value and value.decode('utf-8') == "Docker镜像源测试":
                print_success("Redis读写操作正常")
                r.delete(test_key)
                return True
            else:
                print_error("Redis读写操作失败")
                return False
        else:
            print_error("Redis连接失败")
            return False
            
    except ImportError:
        print_error("Redis模块未安装")
        return False
    except Exception as e:
        print_error(f"Redis连接异常: {e}")
        return False

def test_clickhouse_connection():
    """测试ClickHouse连接"""
    print_info("测试ClickHouse连接...")
    
    try:
        # 测试HTTP接口
        result = subprocess.run(['curl', '-s', 'http://localhost:8123/ping'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0 and 'Ok' in result.stdout:
            print_success("ClickHouse HTTP接口正常")
            
            # 测试查询
            query_result = subprocess.run(['curl', '-s', 'http://localhost:8123/', 
                                         '-d', 'SELECT version()'], 
                                        capture_output=True, text=True, timeout=10)
            
            if query_result.returncode == 0 and query_result.stdout.strip():
                version = query_result.stdout.strip()
                print_success(f"ClickHouse查询正常 - 版本: {version}")
                return True
            else:
                print_warning("ClickHouse查询测试失败")
                return False
        else:
            print_error("ClickHouse连接失败")
            return False
            
    except Exception as e:
        print_error(f"ClickHouse连接异常: {e}")
        return False

def test_image_pull_speed():
    """测试镜像拉取速度"""
    print_info("测试镜像拉取速度...")
    
    test_images = [
        "alpine:latest",
        "nginx:alpine",
        "postgres:13-alpine"
    ]
    
    results = []
    
    for image in test_images:
        print_info(f"测试拉取: {image}")
        
        # 删除可能存在的镜像
        subprocess.run(['docker', 'rmi', image], capture_output=True)
        
        # 测试拉取速度
        start_time = time.time()
        result = subprocess.run(['docker', 'pull', image], 
                              capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        if result.returncode == 0:
            duration = end_time - start_time
            print_success(f"✅ {image} 拉取成功 ({duration:.1f}秒)")
            results.append({"image": image, "success": True, "time": duration})
        else:
            print_error(f"❌ {image} 拉取失败")
            results.append({"image": image, "success": False, "time": 0})
        
        # 清理镜像
        subprocess.run(['docker', 'rmi', image], capture_output=True)
    
    # 统计结果
    successful = [r for r in results if r["success"]]
    if successful:
        avg_time = sum(r["time"] for r in successful) / len(successful)
        print_success(f"镜像拉取成功率: {len(successful)}/{len(results)}")
        print_success(f"平均拉取时间: {avg_time:.1f}秒")
        return True
    else:
        print_error("所有镜像拉取失败")
        return False

async def test_integrated_service():
    """测试集成服务"""
    print_info("测试集成服务...")
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
        
        # 模拟数据写入
        test_data = {
            'timestamp': datetime.now().isoformat(),
            'service': 'docker_test',
            'data': {
                'symbol': '000001',
                'price': 12.34,
                'volume': 1000000
            }
        }
        
        # 写入Redis
        key = f"docker_integration_test:{int(time.time())}"
        r.setex(key, 300, json.dumps(test_data))
        
        # 读取验证
        stored_data = r.get(key)
        if stored_data:
            parsed_data = json.loads(stored_data.decode('utf-8'))
            if parsed_data['service'] == 'docker_test':
                print_success("集成服务测试通过")
                r.delete(key)
                return True
        
        print_error("集成服务测试失败")
        return False
        
    except Exception as e:
        print_error(f"集成服务测试异常: {e}")
        return False

def show_deployment_summary():
    """显示部署总结"""
    print()
    print("=" * 60)
    print("Docker镜像源部署总结")
    print("=" * 60)
    
    # 显示镜像源信息
    try:
        with open('/etc/docker/daemon.json', 'r') as f:
            config = json.load(f)
            mirrors = config.get('registry-mirrors', [])
        
        print("✅ 已配置的镜像源:")
        for mirror in mirrors:
            print(f"  • {mirror}")
        
    except:
        print("❌ 无法读取镜像源配置")
    
    # 显示容器信息
    try:
        result = subprocess.run(['docker', 'ps', '--format', 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("\n✅ 运行中的容器:")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'financial-' in line:
                    print(f"  • {line}")
    except:
        print("\n❌ 无法获取容器信息")
    
    # 显示服务端口
    print("\n✅ 可用服务:")
    print("  • Redis: localhost:6379")
    print("  • ClickHouse HTTP: localhost:8123")
    print("  • ClickHouse Native: localhost:9000")
    
    print("\n🎯 使用建议:")
    print("  • 镜像拉取: docker pull <image>")
    print("  • 服务管理: docker-compose -f docker-compose.simple.yml [up|down|ps]")
    print("  • 数据连接: redis-cli 或 curl http://localhost:8123/")

async def main():
    """主函数"""
    print_header()
    
    test_results = []
    
    # Docker镜像源配置检查
    test_results.append(("镜像源配置", check_docker_mirrors()))
    
    # 容器状态检查
    test_results.append(("容器状态", check_container_status()))
    
    # Redis连接测试
    test_results.append(("Redis连接", test_redis_connection()))
    
    # ClickHouse连接测试
    test_results.append(("ClickHouse连接", test_clickhouse_connection()))
    
    # 镜像拉取速度测试
    test_results.append(("镜像拉取速度", test_image_pull_speed()))
    
    # 集成服务测试
    test_results.append(("集成服务", await test_integrated_service()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed >= total * 0.8:  # 80%通过率
        print_success("🎉 Docker镜像源部署测试成功！")
        print("\n✅ 国内镜像源配置有效，Docker服务运行正常")
        show_deployment_summary()
    else:
        print_warning("部分测试失败，请检查配置")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print_error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)