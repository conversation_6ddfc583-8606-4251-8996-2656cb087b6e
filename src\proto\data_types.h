#pragma once

#include <string>
#include <vector>
#include <cstdint>
#include <chrono>
#include <array>

namespace financial_data {

// 价格档位结构
struct PriceLevel {
    double price = 0.0;
    uint32_t volume = 0;
    uint32_t order_count = 0;
    
    PriceLevel() = default;
    PriceLevel(double p, uint32_t v, uint32_t oc = 0) 
        : price(p), volume(v), order_count(oc) {}
};

// 标准化Tick数据结构
struct StandardTick {
    int64_t timestamp_ns = 0;           // 纳秒级时间戳
    std::string symbol;                 // 合约代码
    std::string exchange;               // 交易所代码
    double last_price = 0.0;           // 最新价
    uint64_t volume = 0;               // 成交量
    double turnover = 0.0;             // 成交额
    uint64_t open_interest = 0;        // 持仓量
    uint32_t sequence = 0;             // 序列号
    std::string trade_flag;            // 成交标志
    
    // 五档买卖盘数据
    std::array<PriceLevel, 5> bids;
    std::array<PriceLevel, 5> asks;
    
    // 获取当前时间戳（纳秒）
    static int64_t GetCurrentTimestampNs() {
        auto now = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::nanoseconds>(
            now.time_since_epoch()).count();
    }
    
    // 设置当前时间戳
    void SetCurrentTimestamp() {
        timestamp_ns = GetCurrentTimestampNs();
    }
    
    // 验证数据有效性
    bool IsValid() const {
        return timestamp_ns > 0 && 
               !symbol.empty() && 
               !exchange.empty() && 
               last_price > 0.0;
    }
};

// Level2深度数据结构
struct Level2Data {
    int64_t timestamp_ns = 0;           // 纳秒级时间戳
    std::string symbol;                 // 合约代码
    std::string exchange;               // 交易所代码
    std::vector<PriceLevel> bids;       // 买盘档位（最多10档）
    std::vector<PriceLevel> asks;       // 卖盘档位（最多10档）
    uint32_t sequence = 0;             // 序列号
    
    Level2Data() {
        bids.reserve(10);
        asks.reserve(10);
    }
    
    void SetCurrentTimestamp() {
        timestamp_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    bool IsValid() const {
        return timestamp_ns > 0 && 
               !symbol.empty() && 
               !exchange.empty() && 
               !bids.empty() && 
               !asks.empty();
    }
};

// 市场数据包装器
struct MarketDataWrapper {
    enum class DataType {
        TICK,
        LEVEL2,
        UNKNOWN
    };
    
    DataType type = DataType::UNKNOWN;
    StandardTick tick_data;
    Level2Data level2_data;
    int64_t receive_time_ns = 0;        // 接收时间戳
    std::string source;                 // 数据源标识
    
    MarketDataWrapper() = default;
    
    explicit MarketDataWrapper(const StandardTick& tick) 
        : type(DataType::TICK), tick_data(tick) {
        receive_time_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    explicit MarketDataWrapper(const Level2Data& level2) 
        : type(DataType::LEVEL2), level2_data(level2) {
        receive_time_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    bool IsValid() const {
        switch (type) {
            case DataType::TICK:
                return tick_data.IsValid();
            case DataType::LEVEL2:
                return level2_data.IsValid();
            default:
                return false;
        }
    }
};

// 数据批次结构
struct MarketDataBatch {
    std::vector<MarketDataWrapper> data;
    int64_t batch_timestamp_ns = 0;
    uint32_t batch_sequence = 0;
    
    MarketDataBatch() {
        data.reserve(1000);  // 预分配空间
        batch_timestamp_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    void AddData(const MarketDataWrapper& market_data) {
        data.push_back(market_data);
    }
    
    void AddTick(const StandardTick& tick) {
        data.emplace_back(tick);
    }
    
    void AddLevel2(const Level2Data& level2) {
        data.emplace_back(level2);
    }
    
    size_t Size() const {
        return data.size();
    }
    
    bool IsEmpty() const {
        return data.empty();
    }
    
    void Clear() {
        data.clear();
        batch_timestamp_ns = StandardTick::GetCurrentTimestampNs();
    }
};

} // namespace financial_data