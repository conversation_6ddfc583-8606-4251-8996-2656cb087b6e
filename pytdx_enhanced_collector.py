#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTDX增强版数据采集器
支持配置文件、多种输出格式、数据质量控制
"""

import asyncio
import logging
import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from collectors.pytdx_collector import PyTDXCollector, PyTDXConfig


class EnhancedPyTDXCollector:
    """增强版PyTDX数据采集器"""
    
    def __init__(self, config_file: str = "pytdx_config.json"):
        # 加载配置
        self.config_data = self.load_config(config_file)
        
        # 设置日志
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 创建PyTDX配置
        self.pytdx_config = self.create_pytdx_config()
        
        # 创建采集器
        self.collector = PyTDXCollector(self.pytdx_config)
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'end_time': None,
            'symbol_lists_updated': 0,
            'symbols_processed': 0,
            'data_points_collected': 0,
            'files_saved': 0,
            'errors': 0,
            'success_symbols': [],
            'failed_symbols': []
        }
    
    def load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_file}")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            # 返回默认配置
            return {
                "collection": {
                    "symbol_types": ["stock", "index"],
                    "data_types": ["stock", "index"],
                    "periods": ["daily", "60min"],
                    "days_back": 30
                }
            }
    
    def setup_logging(self):
        """设置日志"""
        log_config = self.config_data.get('logging', {})
        
        # 创建日志目录
        log_file = log_config.get('file', 'logs/pytdx_collection.log')
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # 配置日志
        level = getattr(logging, log_config.get('level', 'INFO'))
        format_str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        handlers = []
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(format_str))
        handlers.append(file_handler)
        
        # 控制台处理器
        if log_config.get('console', True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(format_str))
            handlers.append(console_handler)
        
        logging.basicConfig(
            level=level,
            format=format_str,
            handlers=handlers,
            force=True
        )
    
    def create_pytdx_config(self) -> PyTDXConfig:
        """创建PyTDX配置"""
        config = PyTDXConfig()
        
        # 服务器配置
        servers_config = self.config_data.get('servers', {})
        if 'hq_servers' in servers_config:
            config.hq_servers = servers_config['hq_servers']
        if 'exhq_servers' in servers_config:
            config.exhq_servers = servers_config['exhq_servers']
        
        # 性能配置
        perf_config = self.config_data.get('performance', {})
        config.concurrent_requests = perf_config.get('max_concurrent_requests', 5)
        config.connect_timeout = perf_config.get('connection_timeout', 30)
        config.max_retries = perf_config.get('max_retries', 3)
        config.heartbeat_interval = perf_config.get('heartbeat_interval', 60)
        
        # 采集配置
        collection_config = self.config_data.get('collection', {})
        config.batch_size = collection_config.get('batch_size', 800)
        config.request_interval = collection_config.get('request_interval', 0.05)
        
        return config
    
    async def initialize(self) -> bool:
        """初始化采集器"""
        try:
            self.logger.info("初始化PyTDX数据采集器...")
            success = await self.collector.initialize()
            if success:
                self.logger.info("✅ 初始化成功")
                return True
            else:
                self.logger.error("❌ 初始化失败")
                return False
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            return False
    
    async def update_symbol_lists(self) -> Dict[str, List[Dict]]:
        """更新代码表"""
        collection_config = self.config_data.get('collection', {})
        symbol_types = collection_config.get('symbol_types', ['stock', 'index'])
        
        self.logger.info(f"开始更新代码表: {symbol_types}")
        
        try:
            symbol_lists = await self.collector.get_all_symbol_lists(symbol_types)
            
            # 保存代码表
            await self.save_symbol_lists(symbol_lists)
            
            # 统计
            total_symbols = sum(len(symbols) for symbols in symbol_lists.values())
            self.stats['symbol_lists_updated'] = len(symbol_lists)
            
            self.logger.info(f"✅ 代码表更新完成，共 {total_symbols} 个标的")
            return symbol_lists
            
        except Exception as e:
            self.logger.error(f"❌ 代码表更新失败: {e}")
            self.stats['errors'] += 1
            raise
    
    async def save_symbol_lists(self, symbol_lists: Dict[str, List[Dict]]):
        """保存代码表到文件"""
        storage_config = self.config_data.get('storage', {})
        if not storage_config.get('save_to_files', True):
            return
        
        output_dir = storage_config.get('output_directory', 'data/historical')
        symbols_dir = os.path.join(output_dir, 'symbols')
        os.makedirs(symbols_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_formats = storage_config.get('file_formats', ['json'])
        
        for symbol_type, symbols in symbol_lists.items():
            if not symbols:
                continue
            
            # 保存为不同格式
            for fmt in file_formats:
                if fmt == 'json':
                    file_path = os.path.join(symbols_dir, f"{symbol_type}_symbols_{timestamp}.json")
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(symbols, f, ensure_ascii=False, indent=2)
                
                elif fmt == 'csv':
                    file_path = os.path.join(symbols_dir, f"{symbol_type}_symbols_{timestamp}.csv")
                    df = pd.DataFrame(symbols)
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')
                
                elif fmt == 'parquet':
                    file_path = os.path.join(symbols_dir, f"{symbol_type}_symbols_{timestamp}.parquet")
                    df = pd.DataFrame(symbols)
                    df.to_parquet(file_path, index=False)
                
                self.stats['files_saved'] += 1
                self.logger.info(f"✅ 保存 {symbol_type} 代码表: {file_path}")
    
    async def collect_historical_data(self, symbol_lists: Dict[str, List[Dict]]) -> bool:
        """采集历史数据"""
        collection_config = self.config_data.get('collection', {})
        data_types = collection_config.get('data_types', ['stock', 'index'])
        periods = collection_config.get('periods', ['daily', '60min'])
        days_back = collection_config.get('days_back', 30)
        max_symbols = collection_config.get('max_symbols_per_type')
        
        self.logger.info(f"开始采集历史数据: {data_types}, 周期: {periods}")
        
        success = True
        
        for data_type in data_types:
            if data_type not in symbol_lists:
                self.logger.warning(f"代码表中没有 {data_type} 类型数据")
                continue
            
            symbols = symbol_lists[data_type]
            if max_symbols:
                symbols = symbols[:max_symbols]
            
            self.logger.info(f"采集 {data_type} 数据: {len(symbols)} 个标的")
            
            type_success = await self._collect_data_by_type(
                data_type, symbols, periods, days_back
            )
            success = success and type_success
        
        return success
    
    async def _collect_data_by_type(self, 
                                  data_type: str, 
                                  symbols: List[Dict], 
                                  periods: List[str], 
                                  days_back: int) -> bool:
        """按类型采集数据"""
        success_count = 0
        error_count = 0
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        for i, symbol_info in enumerate(symbols):
            symbol = symbol_info.get('code', '')
            name = symbol_info.get('name', '')
            
            if not symbol:
                continue
            
            self.logger.info(f"[{i+1}/{len(symbols)}] 采集 {symbol} ({name})")
            
            try:
                symbol_data = {}
                symbol_data_points = 0
                
                # 采集不同周期的数据
                for period in periods:
                    try:
                        # 根据数据类型选择采集方法
                        if data_type == 'stock':
                            data = await self.collector.get_k_data(
                                symbol=symbol,
                                period=period,
                                start_date=start_date.strftime('%Y-%m-%d'),
                                end_date=end_date.strftime('%Y-%m-%d')
                            )
                        elif data_type == 'index':
                            data = await self.collector.get_index_data(
                                symbol=symbol,
                                period=period,
                                start_date=start_date.strftime('%Y-%m-%d'),
                                end_date=end_date.strftime('%Y-%m-%d')
                            )
                        else:
                            self.logger.warning(f"不支持的数据类型: {data_type}")
                            continue
                        
                        if data is not None and not data.empty:
                            symbol_data[period] = data
                            symbol_data_points += len(data)
                            
                            # 归档数据
                            if self.collector.archiver:
                                await self.collector.archiver.archive_k_data(
                                    symbol=symbol,
                                    data=data,
                                    data_type=f"{data_type}_{period}"
                                )
                            
                            self.logger.info(f"  ✅ {period:6s}: {len(data):4d} 条")
                        else:
                            self.logger.warning(f"  ⚠️ {period:6s}: 无数据")
                        
                        # 控制请求频率
                        collection_config = self.config_data.get('collection', {})
                        interval = collection_config.get('request_interval', 0.05)
                        await asyncio.sleep(interval)
                        
                    except Exception as e:
                        self.logger.error(f"  ❌ {period} 数据采集失败: {e}")
                        continue
                
                # 保存数据到文件
                if symbol_data:
                    await self.save_symbol_data(symbol, data_type, symbol_data)
                    success_count += 1
                    self.stats['success_symbols'].append(symbol)
                    self.stats['data_points_collected'] += symbol_data_points
                else:
                    error_count += 1
                    self.stats['failed_symbols'].append(symbol)
                
                self.stats['symbols_processed'] += 1
                
                # 显示进度
                if (i + 1) % 10 == 0:
                    self.logger.info(f"进度: {i+1}/{len(symbols)}, 成功: {success_count}, 失败: {error_count}")
                
            except Exception as e:
                self.logger.error(f"采集 {symbol} 失败: {e}")
                error_count += 1
                self.stats['errors'] += 1
                self.stats['failed_symbols'].append(symbol)
                continue
        
        self.logger.info(f"✅ {data_type} 数据采集完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0
    
    async def save_symbol_data(self, symbol: str, data_type: str, symbol_data: Dict[str, pd.DataFrame]):
        """保存标的数据到文件"""
        storage_config = self.config_data.get('storage', {})
        if not storage_config.get('save_to_files', True):
            return
        
        output_dir = storage_config.get('output_directory', 'data/historical')
        data_dir = os.path.join(output_dir, data_type, symbol)
        os.makedirs(data_dir, exist_ok=True)
        
        file_formats = storage_config.get('file_formats', ['json'])
        
        for period, data in symbol_data.items():
            if data.empty:
                continue
            
            for fmt in file_formats:
                if fmt == 'json':
                    file_path = os.path.join(data_dir, f"{symbol}_{period}.json")
                    data.to_json(file_path, orient='index', date_format='iso', force_ascii=False, indent=2)
                
                elif fmt == 'csv':
                    file_path = os.path.join(data_dir, f"{symbol}_{period}.csv")
                    data.to_csv(file_path, encoding='utf-8-sig')
                
                elif fmt == 'parquet':
                    file_path = os.path.join(data_dir, f"{symbol}_{period}.parquet")
                    data.to_parquet(file_path)
                
                self.stats['files_saved'] += 1
    
    async def run_full_collection(self) -> bool:
        """运行完整的数据采集流程"""
        self.stats['start_time'] = datetime.now()
        
        try:
            self.logger.info("🚀 开始PyTDX增强版数据采集")
            self.logger.info("=" * 80)
            
            # 1. 初始化
            if not await self.initialize():
                return False
            
            # 2. 更新代码表
            self.logger.info("\n📋 第一步: 更新代码表")
            symbol_lists = await self.update_symbol_lists()
            
            # 3. 采集历史数据
            self.logger.info("\n📊 第二步: 采集历史数据")
            success = await self.collect_historical_data(symbol_lists)
            
            # 4. 显示统计信息
            await self._show_statistics()
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ 数据采集失败: {e}")
            return False
        finally:
            self.stats['end_time'] = datetime.now()
            await self.cleanup()
    
    async def _show_statistics(self):
        """显示统计信息"""
        self.logger.info("\n📈 采集统计")
        self.logger.info("=" * 80)
        self.logger.info(f"代码表更新: {self.stats['symbol_lists_updated']} 个")
        self.logger.info(f"标的处理: {self.stats['symbols_processed']} 个")
        self.logger.info(f"数据点采集: {self.stats['data_points_collected']} 条")
        self.logger.info(f"文件保存: {self.stats['files_saved']} 个")
        self.logger.info(f"成功标的: {len(self.stats['success_symbols'])} 个")
        self.logger.info(f"失败标的: {len(self.stats['failed_symbols'])} 个")
        self.logger.info(f"错误数量: {self.stats['errors']} 个")
        
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            self.logger.info(f"总耗时: {duration}")
        
        # 显示采集器统计
        collector_stats = self.collector.get_stats()
        self.logger.info(f"采集器统计: {collector_stats}")
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.collector.close()
            self.logger.info("✅ 资源清理完成")
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")


async def main():
    """主函数"""
    print("PyTDX增强版数据采集系统")
    print("=" * 80)
    print("功能:")
    print("✅ 支持配置文件管理")
    print("✅ 多种输出格式 (JSON/CSV/Parquet)")
    print("✅ 数据质量控制和去重")
    print("✅ 详细的日志和统计")
    print("✅ 自动存储到数据库")
    print()
    
    # 检查配置文件
    config_file = "pytdx_config.json"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        print("请确保配置文件存在或使用默认配置")
        return 1
    
    collector = EnhancedPyTDXCollector(config_file)
    
    try:
        # 运行采集
        success = await collector.run_full_collection()
        
        if success:
            print("\n🎉 数据采集完成！")
            return 0
        else:
            print("\n❌ 数据采集失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断采集")
        return 1
    except Exception as e:
        print(f"\n❌ 采集过程出错: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)