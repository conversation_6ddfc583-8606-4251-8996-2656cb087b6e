#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include "../src/storage/redis_storage.h"
#include "../src/storage/redis_config_loader.h"

using namespace financial_data;

void DemonstrateBasicOperations() {
    std::cout << "\n=== Basic Operations Demo ===" << std::endl;
    
    // 创建Redis热数据存储实例
    RedisConfig config = RedisConfigLoader::GetDefaultConfig();
    RedisHotStorage storage(config);
    
    if (!storage.Initialize()) {
        std::cerr << "Failed to initialize Redis storage" << std::endl;
        return;
    }
    
    // 创建测试数据
    StandardTick tick;
    tick.timestamp_ns = StandardTick::GetCurrentTimestampNs();
    tick.symbol = "CU2409";
    tick.exchange = "SHFE";
    tick.last_price = 78560.0;
    tick.volume = 1000;
    tick.turnover = 78560000.0;
    tick.open_interest = 50000;
    tick.sequence = 12345;
    tick.trade_flag = "buy_open";
    
    // 设置买卖盘数据
    for (int i = 0; i < 5; ++i) {
        tick.bids[i] = PriceLevel(78560.0 - (i + 1) * 10, 100 + i * 10, i + 1);
        tick.asks[i] = PriceLevel(78560.0 + (i + 1) * 10, 100 + i * 10, i + 1);
    }
    
    // 存储数据
    std::cout << "Storing tick data for " << tick.symbol << std::endl;
    if (storage.StoreTick(tick)) {
        std::cout << "✓ Tick data stored successfully" << std::endl;
    } else {
        std::cout << "✗ Failed to store tick data" << std::endl;
        return;
    }
    
    // 查询最新数据
    StandardTick retrieved_tick;
    if (storage.GetLatestTick(tick.symbol, retrieved_tick)) {
        std::cout << "✓ Retrieved latest tick:" << std::endl;
        std::cout << "  Symbol: " << retrieved_tick.symbol << std::endl;
        std::cout << "  Price: " << retrieved_tick.last_price << std::endl;
        std::cout << "  Volume: " << retrieved_tick.volume << std::endl;
        std::cout << "  Timestamp: " << retrieved_tick.timestamp_ns << std::endl;
    } else {
        std::cout << "✗ Failed to retrieve tick data" << std::endl;
    }
    
    storage.Shutdown();
}

void DemonstrateBatchOperations() {
    std::cout << "\n=== Batch Operations Demo ===" << std::endl;
    
    RedisConfig config = RedisConfigLoader::GetDefaultConfig();
    RedisHotStorage storage(config);
    
    if (!storage.Initialize()) {
        std::cerr << "Failed to initialize Redis storage" << std::endl;
        return;
    }
    
    // 创建批量测试数据
    std::vector<StandardTick> ticks;
    const int batch_size = 100;
    
    for (int i = 0; i < batch_size; ++i) {
        StandardTick tick;
        tick.timestamp_ns = StandardTick::GetCurrentTimestampNs() + i * 1000000;  // 每条数据间隔1ms
        tick.symbol = "AL2409";
        tick.exchange = "SHFE";
        tick.last_price = 19000.0 + i;
        tick.volume = 1000 + i * 10;
        tick.turnover = tick.last_price * tick.volume;
        tick.open_interest = 30000 + i * 100;
        tick.sequence = i + 1;
        tick.trade_flag = (i % 2 == 0) ? "buy_open" : "sell_close";
        
        // 设置买卖盘数据
        for (int j = 0; j < 5; ++j) {
            tick.bids[j] = PriceLevel(tick.last_price - (j + 1) * 5, 50 + j * 5, j + 1);
            tick.asks[j] = PriceLevel(tick.last_price + (j + 1) * 5, 50 + j * 5, j + 1);
        }
        
        ticks.push_back(tick);
    }
    
    // 批量存储
    auto start_time = std::chrono::high_resolution_clock::now();
    
    if (storage.StoreBatch(ticks)) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        
        std::cout << "✓ Batch stored " << batch_size << " ticks in " << duration.count() << " μs" << std::endl;
        std::cout << "  Average: " << (duration.count() / batch_size) << " μs per tick" << std::endl;
        
        // 验证最后一条数据
        StandardTick latest_tick;
        if (storage.GetLatestTick("AL2409", latest_tick)) {
            std::cout << "✓ Latest tick price: " << latest_tick.last_price << std::endl;
        }
    } else {
        std::cout << "✗ Batch storage failed" << std::endl;
    }
    
    storage.Shutdown();
}

void DemonstrateAsyncOperations() {
    std::cout << "\n=== Async Operations Demo ===" << std::endl;
    
    RedisConfig config = RedisConfigLoader::GetDefaultConfig();
    RedisHotStorage storage(config);
    
    if (!storage.Initialize()) {
        std::cerr << "Failed to initialize Redis storage" << std::endl;
        return;
    }
    
    // 异步存储多个合约的数据
    std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409", "RB2409", "AU2412"};
    std::vector<std::future<bool>> futures;
    
    for (size_t i = 0; i < symbols.size(); ++i) {
        StandardTick tick;
        tick.timestamp_ns = StandardTick::GetCurrentTimestampNs();
        tick.symbol = symbols[i];
        tick.exchange = "SHFE";
        tick.last_price = 1000.0 + i * 100;
        tick.volume = 1000;
        tick.turnover = tick.last_price * tick.volume;
        tick.open_interest = 10000;
        tick.sequence = i + 1;
        tick.trade_flag = "buy_open";
        
        // 异步存储
        futures.push_back(storage.StoreTickAsync(tick));
    }
    
    // 等待所有异步操作完成
    int success_count = 0;
    for (auto& future : futures) {
        if (future.get()) {
            success_count++;
        }
    }
    
    std::cout << "✓ Async stored " << success_count << "/" << symbols.size() << " ticks" << std::endl;
    
    // 批量查询验证
    auto results = storage.GetLatestTicks(symbols);
    std::cout << "✓ Batch query returned " << results.size() << " results:" << std::endl;
    
    for (const auto& [symbol, tick] : results) {
        std::cout << "  " << symbol << ": " << tick.last_price << std::endl;
    }
    
    storage.Shutdown();
}

void DemonstratePerformanceTest() {
    std::cout << "\n=== Performance Test Demo ===" << std::endl;
    
    RedisConfig config = RedisConfigLoader::GetDefaultConfig();
    config.write_worker_count = 8;  // 增加工作线程数
    config.max_connections = 20;    // 增加连接数
    
    RedisHotStorage storage(config);
    
    if (!storage.Initialize()) {
        std::cerr << "Failed to initialize Redis storage" << std::endl;
        return;
    }
    
    const int test_count = 10000;
    std::cout << "Running performance test with " << test_count << " operations..." << std::endl;
    
    // 写入性能测试
    auto write_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < test_count; ++i) {
        StandardTick tick;
        tick.timestamp_ns = StandardTick::GetCurrentTimestampNs() + i;
        tick.symbol = "PERF_TEST";
        tick.exchange = "TEST";
        tick.last_price = 1000.0 + i * 0.01;
        tick.volume = 100;
        tick.turnover = tick.last_price * tick.volume;
        tick.sequence = i + 1;
        
        if (!storage.StoreTick(tick)) {
            std::cout << "Write failed at iteration " << i << std::endl;
            break;
        }
    }
    
    auto write_end = std::chrono::high_resolution_clock::now();
    auto write_duration = std::chrono::duration_cast<std::chrono::microseconds>(write_end - write_start);
    
    double write_ops_per_sec = (test_count * 1000000.0) / write_duration.count();
    std::cout << "✓ Write performance: " << write_ops_per_sec << " ops/sec" << std::endl;
    
    // 读取性能测试
    auto read_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < 1000; ++i) {
        StandardTick tick;
        if (!storage.GetLatestTick("PERF_TEST", tick)) {
            std::cout << "Read failed at iteration " << i << std::endl;
            break;
        }
    }
    
    auto read_end = std::chrono::high_resolution_clock::now();
    auto read_duration = std::chrono::duration_cast<std::chrono::microseconds>(read_end - read_start);
    
    double avg_read_time = read_duration.count() / 1000.0;
    std::cout << "✓ Read performance: " << avg_read_time << " μs per query" << std::endl;
    
    // 显示统计信息
    auto stats = storage.GetStats();
    std::cout << "\nStorage Statistics:" << std::endl;
    std::cout << "  Total ticks stored: " << stats.total_ticks_stored << std::endl;
    std::cout << "  Total queries: " << stats.total_queries << std::endl;
    std::cout << "  Avg write latency: " << stats.avg_write_latency_us << " μs" << std::endl;
    std::cout << "  Avg query latency: " << stats.avg_query_latency_us << " μs" << std::endl;
    std::cout << "  Active connections: " << stats.active_connections << std::endl;
    
    storage.Shutdown();
}

int main() {
    std::cout << "Redis Hot Storage Demo" << std::endl;
    std::cout << "=====================" << std::endl;
    
    try {
        DemonstrateBasicOperations();
        DemonstrateBatchOperations();
        DemonstrateAsyncOperations();
        DemonstratePerformanceTest();
        
        std::cout << "\n✓ All demos completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Demo failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}