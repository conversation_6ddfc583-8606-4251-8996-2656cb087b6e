# Redis configuration for financial data service hot storage
port 6379
bind 127.0.0.1

# Memory configuration for hot data (7 days)
maxmemory 8gb
maxmemory-policy allkeys-lru

# Persistence configuration - optimized for performance
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error no

# Performance optimizations
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Hot data specific settings
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Enable keyspace notifications for expiration events
notify-keyspace-events Ex

# Cluster configuration (for future cluster setup)
# cluster-enabled yes
# cluster-config-file nodes.conf
# cluster-node-timeout 15000
# cluster-announce-ip 127.0.0.1
# cluster-announce-port 6379
# cluster-announce-bus-port 16379

# Logging
loglevel notice
logfile ""

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128