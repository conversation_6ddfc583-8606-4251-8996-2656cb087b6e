# Collectors library

add_library(collectors
    ctp_collector.cpp
    ctp_collector.h
    ctp_api_wrapper.cpp
    ctp_api_wrapper.h
    ctp_headers.h
)

target_include_directories(collectors PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src
)

target_link_libraries(collectors
    proto
    Threads::Threads
)

target_compile_features(collectors PUBLIC cxx_std_17)

# Add compiler definitions for debugging
target_compile_definitions(collectors PRIVATE
    $<$<CONFIG:Debug>:DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)