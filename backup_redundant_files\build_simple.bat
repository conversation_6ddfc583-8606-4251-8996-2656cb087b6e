@echo off
echo ========================================
echo Building Financial Data Service System
echo ========================================
echo.

REM 设置Visual Studio环境
call "d:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"

echo.
echo [1/3] Compiling main_minimal.cpp (no dependencies)...
cl /std:c++17 /EHsc src\main_minimal.cpp /Fe:financial_data_service_minimal.exe
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to compile main_minimal.cpp
    pause
    exit /b 1
)
echo [SUCCESS] financial_data_service_minimal.exe created

echo.
echo [2/3] Compiling main_simple.cpp (with JSON support)...
cl /std:c++17 /EHsc /I"./vcpkg_installed/x64-windows/include" src\main_simple.cpp /Fe:financial_data_service_simple.exe
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to compile main_simple.cpp
    pause
    exit /b 1
)
echo [SUCCESS] financial_data_service_simple.exe created

echo.
echo [3/3] Compiling main_mock.cpp (full mock implementation)...
cl /std:c++17 /EHsc src\main_mock.cpp /Fe:financial_data_service_mock.exe
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to compile main_mock.cpp
    pause
    exit /b 1
)
echo [SUCCESS] financial_data_service_mock.exe created

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Available executables:
echo.
echo 1. financial_data_service_minimal.exe
echo    - Basic version with no external dependencies
echo    - Simple startup/shutdown simulation
echo.
echo 2. financial_data_service_simple.exe  
echo    - With JSON configuration support
echo    - Reads config/app.json
echo    - Health checks and statistics
echo.
echo 3. financial_data_service_mock.exe
echo    - Full system architecture simulation
echo    - All components mocked (DataBus, Redis, WebSocket, Monitoring)
echo    - Complete logging and graceful shutdown
echo.
echo Usage:
echo   .\financial_data_service_minimal.exe
echo   .\financial_data_service_simple.exe
echo   .\financial_data_service_mock.exe
echo.
echo Press any key to exit...
pause >nul