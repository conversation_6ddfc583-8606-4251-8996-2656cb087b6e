# 构建和部署脚本

本目录包含用于构建、测试和部署金融数据服务系统的脚本。

## 脚本说明

### Windows 脚本

- `build.bat` - 构建项目和运行测试
- `setup-dev.bat` - 设置开发环境（启动Docker服务）

### Linux/macOS 脚本

- `setup-dev.sh` - 设置开发环境（启动Docker服务）

## 使用方法

### Windows 开发环境设置

```cmd
scripts\setup-dev.bat
```

### Windows 项目构建

```cmd
scripts\build.bat
```

### Linux/macOS 开发环境设置

```bash
chmod +x scripts/setup-dev.sh
./scripts/setup-dev.sh
```

## 前置条件

- Docker Desktop (Windows) 或 Docker + Docker Compose (Linux/macOS)
- CMake 3.15+
- C++17 兼容编译器 (MSVC 2019+, GCC 8+, Clang 7+)

## 服务端口

开发环境启动后，以下服务将可用：

- Redis: `localhost:6379`
- ClickHouse HTTP: `localhost:8123`
- ClickHouse TCP: `localhost:9000`
- Kafka: `localhost:9092`
- MinIO Console: `localhost:9001`
- MinIO API: `localhost:9002`
- Prometheus: `localhost:9090`
- Grafana: `localhost:3000` (admin/admin123)