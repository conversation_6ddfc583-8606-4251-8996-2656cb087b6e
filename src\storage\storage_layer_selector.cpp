#include "storage_layer_selector.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <algorithm>
#include <random>

namespace financial_data {

StorageLayerSelector::StorageLayerSelector(const StorageSelectionConfig& config)
    : config_(config) {
    InitializeLogger();
    InitializeLayerStatus();
}

StorageLayerSelector::~StorageLayerSelector() {
    Shutdown();
}

void StorageLayerSelector::InitializeLogger() {
    logger_ = spdlog::get("storage_layer_selector");
    if (!logger_) {
        logger_ = spdlog::stdout_color_mt("storage_layer_selector");
        logger_->set_level(spdlog::level::info);
    }
}

void StorageLayerSelector::InitializeLayerStatus() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    // 初始化所有存储层状态
    for (int i = 1; i <= 3; ++i) {
        StorageLayer layer = static_cast<StorageLayer>(i);
        StorageLayerStatus status;
        status.layer = layer;
        status.health = StorageLayerHealth::UNKNOWN;
        status.last_health_check = std::chrono::steady_clock::now();
        layer_status_[layer] = status;
    }
}

bool StorageLayerSelector::Initialize() {
    if (running_.load()) {
        logger_->warn("StorageLayerSelector already initialized");
        return true;
    }
    
    // 启动健康检查线程
    health_check_thread_ = std::thread([this]() {
        while (!shutdown_requested_.load()) {
            PerformHealthCheck();
            std::this_thread::sleep_for(config_.health_check_interval);
        }
    });
    
    running_.store(true);
    logger_->info("StorageLayerSelector initialized successfully");
    return true;
}

void StorageLayerSelector::Shutdown() {
    if (!running_.load()) {
        return;
    }
    
    shutdown_requested_.store(true);
    
    // 等待健康检查线程结束
    if (health_check_thread_.joinable()) {
        health_check_thread_.join();
    }
    
    running_.store(false);
    logger_->info("StorageLayerSelector shutdown completed");
}

StorageLayer StorageLayerSelector::SelectStorageLayer(int64_t timestamp_ns) const {
    statistics_.total_selections++;
    
    StorageLayer selected_layer;
    
    switch (config_.strategy) {
        case StorageSelectionStrategy::TIME_BASED:
            selected_layer = SelectByTime(timestamp_ns);
            break;
        case StorageSelectionStrategy::PERFORMANCE_BASED:
            selected_layer = SelectByPerformance(timestamp_ns);
            break;
        case StorageSelectionStrategy::LOAD_BALANCED:
            selected_layer = SelectByLoadBalance(timestamp_ns);
            break;
        case StorageSelectionStrategy::FAILOVER_ONLY:
            selected_layer = SelectByTime(timestamp_ns);
            break;
        default:
            selected_layer = SelectByTime(timestamp_ns);
            break;
    }
    
    // 检查选中的存储层是否可用
    if (!IsLayerHealthy(selected_layer) && config_.enable_automatic_failover) {
        selected_layer = SelectWithFailover(timestamp_ns, {selected_layer});
        UpdateSelectionStatistics(selected_layer, true);
    } else {
        UpdateSelectionStatistics(selected_layer, false);
    }
    
    return selected_layer;
}

std::vector<StorageLayer> StorageLayerSelector::SelectStorageLayers(
    int64_t start_timestamp_ns, int64_t end_timestamp_ns) const {
    
    std::vector<StorageLayer> layers;
    
    // 确定时间范围覆盖的存储层
    StorageLayer start_layer = SelectByTime(start_timestamp_ns);
    StorageLayer end_layer = SelectByTime(end_timestamp_ns);
    
    if (start_layer == end_layer) {
        layers.push_back(start_layer);
    } else {
        // 跨层查询，添加所有相关存储层
        int start_level = static_cast<int>(start_layer);
        int end_level = static_cast<int>(end_layer);
        
        for (int level = std::min(start_level, end_level); 
             level <= std::max(start_level, end_level); ++level) {
            StorageLayer layer = static_cast<StorageLayer>(level);
            if (IsLayerHealthy(layer)) {
                layers.push_back(layer);
            } else if (config_.enable_automatic_failover) {
                // 尝试找到故障转移目标
                StorageLayer failover_layer = FindFailoverTarget(layer);
                if (failover_layer != layer && 
                    std::find(layers.begin(), layers.end(), failover_layer) == layers.end()) {
                    layers.push_back(failover_layer);
                }
            }
        }
    }
    
    return layers;
}

StorageLayer StorageLayerSelector::SelectWithFailover(
    int64_t timestamp_ns, const std::vector<StorageLayer>& failed_layers) const {
    
    // 首先尝试正常选择
    StorageLayer primary_layer = SelectByTime(timestamp_ns);
    
    // 检查主要存储层是否在失败列表中
    if (std::find(failed_layers.begin(), failed_layers.end(), primary_layer) == failed_layers.end() &&
        IsLayerHealthy(primary_layer)) {
        return primary_layer;
    }
    
    // 寻找故障转移目标
    StorageLayer failover_layer = FindFailoverTarget(primary_layer, failed_layers);
    
    if (failover_layer != primary_layer) {
        logger_->warn("Failover from {} to {} for timestamp {}",
                     static_cast<int>(primary_layer), 
                     static_cast<int>(failover_layer), 
                     timestamp_ns);
        return failover_layer;
    }
    
    // 如果没有可用的故障转移目标，返回原始选择
    logger_->error("No healthy failover target found for timestamp {}", timestamp_ns);
    return primary_layer;
}

StorageLayer StorageLayerSelector::SelectOptimalLayer(int64_t timestamp_ns) const {
    // 获取时间范围内的候选存储层
    std::vector<StorageLayer> candidates;
    
    if (IsTimestampInHotRange(timestamp_ns)) {
        candidates = {StorageLayer::HOT, StorageLayer::WARM, StorageLayer::COLD};
    } else if (IsTimestampInWarmRange(timestamp_ns)) {
        candidates = {StorageLayer::WARM, StorageLayer::COLD};
    } else {
        candidates = {StorageLayer::COLD};
    }
    
    // 根据性能指标选择最优存储层
    StorageLayer optimal_layer = StorageLayer::HOT;
    double best_score = -1.0;
    
    std::lock_guard<std::mutex> lock(status_mutex_);
    for (StorageLayer layer : candidates) {
        auto it = layer_status_.find(layer);
        if (it != layer_status_.end() && it->second.IsAvailable()) {
            double score = it->second.GetPriorityScore();
            if (score > best_score) {
                best_score = score;
                optimal_layer = layer;
            }
        }
    }
    
    statistics_.optimal_selections++;
    return optimal_layer;
}

void StorageLayerSelector::UpdateLayerMetrics(StorageLayer layer, bool success, double response_time_ms) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    auto it = layer_status_.find(layer);
    if (it != layer_status_.end()) {
        it->second.metrics.UpdateMetrics(success, response_time_ms);
        
        // 更新健康状态
        StorageLayerHealth old_health = it->second.health;
        it->second.health = it->second.DetermineHealth();
        
        // 如果健康状态发生变化，记录日志
        if (old_health != it->second.health) {
            logger_->info("Storage layer {} health changed from {} to {}",
                         static_cast<int>(layer),
                         static_cast<int>(old_health),
                         static_cast<int>(it->second.health));
        }
        
        // 重置连续失败计数
        if (success) {
            std::lock_guard<std::mutex> failover_lock(failover_mutex_);
            consecutive_failures_[layer] = 0;
        }
    }
}

void StorageLayerSelector::ReportLayerFailure(StorageLayer layer, const std::string& error_message) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    auto it = layer_status_.find(layer);
    if (it != layer_status_.end()) {
        it->second.error_message = error_message;
        it->second.health = StorageLayerHealth::UNHEALTHY;
        
        // 更新连续失败计数
        std::lock_guard<std::mutex> failover_lock(failover_mutex_);
        consecutive_failures_[layer]++;
        
        // 检查是否需要触发故障转移
        if (consecutive_failures_[layer] >= config_.max_consecutive_failures) {
            failover_timestamps_[layer] = std::chrono::steady_clock::now();
            logger_->error("Storage layer {} entered failover mode after {} consecutive failures: {}",
                          static_cast<int>(layer),
                          consecutive_failures_[layer],
                          error_message);
        }
    }
}

void StorageLayerSelector::ReportLayerRecovery(StorageLayer layer) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    auto it = layer_status_.find(layer);
    if (it != layer_status_.end()) {
        it->second.health = StorageLayerHealth::HEALTHY;
        it->second.error_message.clear();
        
        // 清除故障转移状态
        std::lock_guard<std::mutex> failover_lock(failover_mutex_);
        consecutive_failures_[layer] = 0;
        failover_timestamps_.erase(layer);
        
        logger_->info("Storage layer {} recovered and is now healthy", static_cast<int>(layer));
    }
}

StorageLayer StorageLayerSelector::SelectByTime(int64_t timestamp_ns) const {
    if (IsTimestampInHotRange(timestamp_ns)) {
        return StorageLayer::HOT;
    } else if (IsTimestampInWarmRange(timestamp_ns)) {
        return StorageLayer::WARM;
    } else {
        return StorageLayer::COLD;
    }
}

StorageLayer StorageLayerSelector::SelectByPerformance(int64_t timestamp_ns) const {
    // 首先确定时间范围内的候选存储层
    std::vector<StorageLayer> candidates;
    
    if (IsTimestampInHotRange(timestamp_ns)) {
        candidates = {StorageLayer::HOT};
        // 如果热存储不健康，可以考虑温存储
        if (!IsLayerHealthy(StorageLayer::HOT)) {
            candidates.push_back(StorageLayer::WARM);
        }
    } else if (IsTimestampInWarmRange(timestamp_ns)) {
        candidates = {StorageLayer::WARM};
        // 如果温存储不健康，可以考虑冷存储
        if (!IsLayerHealthy(StorageLayer::WARM)) {
            candidates.push_back(StorageLayer::COLD);
        }
    } else {
        candidates = {StorageLayer::COLD};
    }
    
    // 从候选存储层中选择性能最好的
    StorageLayer best_layer = candidates[0];
    double best_score = -1.0;
    
    std::lock_guard<std::mutex> lock(status_mutex_);
    for (StorageLayer layer : candidates) {
        auto it = layer_status_.find(layer);
        if (it != layer_status_.end() && it->second.IsAvailable()) {
            double score = it->second.GetPriorityScore();
            if (score > best_score) {
                best_score = score;
                best_layer = layer;
            }
        }
    }
    
    return best_layer;
}

StorageLayer StorageLayerSelector::SelectByLoadBalance(int64_t timestamp_ns) const {
    // 基于负载均衡的选择策略
    StorageLayer time_based_layer = SelectByTime(timestamp_ns);
    
    std::lock_guard<std::mutex> lock(status_mutex_);
    auto it = layer_status_.find(time_based_layer);
    if (it == layer_status_.end()) {
        return time_based_layer;
    }
    
    // 检查当前存储层的负载
    double current_load = 1.0 - it->second.metrics.success_rate.load();
    
    if (current_load > config_.load_balance_threshold) {
        // 当前存储层负载过高，尝试找到负载较低的替代存储层
        std::vector<StorageLayer> alternatives;
        
        if (time_based_layer == StorageLayer::HOT) {
            alternatives = {StorageLayer::WARM};
        } else if (time_based_layer == StorageLayer::WARM) {
            alternatives = {StorageLayer::HOT, StorageLayer::COLD};
        } else {
            alternatives = {StorageLayer::WARM};
        }
        
        for (StorageLayer alt_layer : alternatives) {
            auto alt_it = layer_status_.find(alt_layer);
            if (alt_it != layer_status_.end() && alt_it->second.IsAvailable()) {
                double alt_load = 1.0 - alt_it->second.metrics.success_rate.load();
                if (alt_load < current_load * 0.8) { // 负载显著更低
                    logger_->debug("Load balancing: switching from {} to {} (load: {:.2f} -> {:.2f})",
                                  static_cast<int>(time_based_layer),
                                  static_cast<int>(alt_layer),
                                  current_load, alt_load);
                    return alt_layer;
                }
            }
        }
    }
    
    return time_based_layer;
}

void StorageLayerSelector::PerformHealthCheck() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    for (auto& [layer, status] : layer_status_) {
        CheckLayerHealth(layer);
        status.last_health_check = std::chrono::steady_clock::now();
    }
}

void StorageLayerSelector::CheckLayerHealth(StorageLayer layer) {
    auto callback_it = health_check_callbacks_.find(layer);
    if (callback_it != health_check_callbacks_.end()) {
        try {
            bool is_healthy = callback_it->second(layer);
            
            auto status_it = layer_status_.find(layer);
            if (status_it != layer_status_.end()) {
                StorageLayerHealth old_health = status_it->second.health;
                
                if (is_healthy) {
                    if (old_health == StorageLayerHealth::UNHEALTHY) {
                        status_it->second.health = StorageLayerHealth::DEGRADED;
                    } else if (old_health == StorageLayerHealth::DEGRADED) {
                        status_it->second.health = StorageLayerHealth::HEALTHY;
                    }
                } else {
                    status_it->second.health = StorageLayerHealth::UNHEALTHY;
                }
                
                if (old_health != status_it->second.health) {
                    logger_->info("Health check: Storage layer {} changed from {} to {}",
                                 static_cast<int>(layer),
                                 static_cast<int>(old_health),
                                 static_cast<int>(status_it->second.health));
                }
            }
        } catch (const std::exception& e) {
            logger_->error("Health check failed for storage layer {}: {}",
                          static_cast<int>(layer), e.what());
            
            auto status_it = layer_status_.find(layer);
            if (status_it != layer_status_.end()) {
                status_it->second.health = StorageLayerHealth::UNHEALTHY;
                status_it->second.error_message = e.what();
            }
        }
    }
}

bool StorageLayerSelector::IsLayerHealthy(StorageLayer layer) const {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    auto it = layer_status_.find(layer);
    if (it != layer_status_.end()) {
        return it->second.IsAvailable();
    }
    
    return false; // 未知状态视为不健康
}

StorageLayer StorageLayerSelector::FindFailoverTarget(
    StorageLayer failed_layer, const std::vector<StorageLayer>& excluded) const {
    
    // 定义故障转移优先级
    std::vector<StorageLayer> failover_candidates;
    
    switch (failed_layer) {
        case StorageLayer::HOT:
            failover_candidates = {StorageLayer::WARM, StorageLayer::COLD};
            break;
        case StorageLayer::WARM:
            failover_candidates = {StorageLayer::HOT, StorageLayer::COLD};
            break;
        case StorageLayer::COLD:
            failover_candidates = {StorageLayer::WARM, StorageLayer::HOT};
            break;
        default:
            break;
    }
    
    // 从候选列表中选择第一个健康且未被排除的存储层
    for (StorageLayer candidate : failover_candidates) {
        if (std::find(excluded.begin(), excluded.end(), candidate) == excluded.end() &&
            IsLayerHealthy(candidate)) {
            return candidate;
        }
    }
    
    // 如果没有找到合适的故障转移目标，返回原始存储层
    return failed_layer;
}

bool StorageLayerSelector::IsLayerInFailover(StorageLayer layer) const {
    std::lock_guard<std::mutex> lock(failover_mutex_);
    
    auto it = failover_timestamps_.find(layer);
    if (it != failover_timestamps_.end()) {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - it->second);
        return elapsed < config_.failover_cooldown;
    }
    
    return false;
}

void StorageLayerSelector::RegisterHealthCheckCallback(StorageLayer layer, HealthCheckCallback callback) {
    health_check_callbacks_[layer] = callback;
}

StorageLayerStatus StorageLayerSelector::GetLayerStatus(StorageLayer layer) const {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    auto it = layer_status_.find(layer);
    if (it != layer_status_.end()) {
        return it->second;
    }
    
    // 返回默认状态
    StorageLayerStatus default_status;
    default_status.layer = layer;
    default_status.health = StorageLayerHealth::UNKNOWN;
    return default_status;
}

std::vector<StorageLayerStatus> StorageLayerSelector::GetAllLayerStatus() const {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    std::vector<StorageLayerStatus> all_status;
    for (const auto& [layer, status] : layer_status_) {
        all_status.push_back(status);
    }
    
    return all_status;
}

void StorageLayerSelector::EnableLayer(StorageLayer layer) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    auto it = layer_status_.find(layer);
    if (it != layer_status_.end()) {
        it->second.health = StorageLayerHealth::HEALTHY;
        it->second.error_message.clear();
        
        // 清除故障转移状态
        std::lock_guard<std::mutex> failover_lock(failover_mutex_);
        consecutive_failures_[layer] = 0;
        failover_timestamps_.erase(layer);
        
        logger_->info("Storage layer {} manually enabled", static_cast<int>(layer));
    }
}

void StorageLayerSelector::DisableLayer(StorageLayer layer, const std::string& reason) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    auto it = layer_status_.find(layer);
    if (it != layer_status_.end()) {
        it->second.health = StorageLayerHealth::UNHEALTHY;
        it->second.error_message = reason.empty() ? "Manually disabled" : reason;
        
        logger_->warn("Storage layer {} manually disabled: {}", 
                     static_cast<int>(layer), it->second.error_message);
    }
}

bool StorageLayerSelector::UpdateConfig(const StorageSelectionConfig& config) {
    config_ = config;
    logger_->info("StorageLayerSelector configuration updated");
    return true;
}

StorageLayerSelector::SelectionStatistics StorageLayerSelector::GetStatistics() const {
    return statistics_;
}

void StorageLayerSelector::ResetStatistics() {
    statistics_.Reset();
}

void StorageLayerSelector::UpdateSelectionStatistics(StorageLayer layer, bool is_failover) const {
    switch (layer) {
        case StorageLayer::HOT:
            statistics_.hot_selections++;
            break;
        case StorageLayer::WARM:
            statistics_.warm_selections++;
            break;
        case StorageLayer::COLD:
            statistics_.cold_selections++;
            break;
        default:
            break;
    }
    
    if (is_failover) {
        statistics_.failover_selections++;
    }
}

bool StorageLayerSelector::IsTimestampInHotRange(int64_t timestamp_ns) const {
    int64_t current_time = GetCurrentTimestampNs();
    int64_t hot_threshold = current_time - DaysToNanoseconds(config_.hot_storage_days);
    return timestamp_ns >= hot_threshold;
}

bool StorageLayerSelector::IsTimestampInWarmRange(int64_t timestamp_ns) const {
    int64_t current_time = GetCurrentTimestampNs();
    int64_t hot_threshold = current_time - DaysToNanoseconds(config_.hot_storage_days);
    int64_t warm_threshold = current_time - DaysToNanoseconds(config_.warm_storage_days);
    return timestamp_ns >= warm_threshold && timestamp_ns < hot_threshold;
}

int64_t StorageLayerSelector::GetCurrentTimestampNs() const {
    auto now = std::chrono::high_resolution_clock::now();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(
        now.time_since_epoch()).count();
}

int64_t StorageLayerSelector::DaysToNanoseconds(int days) const {
    return static_cast<int64_t>(days) * 24 * 3600 * 1000000000LL;
}

// 工厂方法实现
std::unique_ptr<StorageLayerSelector> StorageLayerSelectorFactory::CreateDefault() {
    StorageSelectionConfig config;
    return std::make_unique<StorageLayerSelector>(config);
}

std::unique_ptr<StorageLayerSelector> StorageLayerSelectorFactory::CreateHighAvailability() {
    StorageSelectionConfig config;
    config.strategy = StorageSelectionStrategy::PERFORMANCE_BASED;
    config.enable_automatic_failover = true;
    config.max_consecutive_failures = 2;
    config.failover_cooldown = std::chrono::seconds(30);
    config.health_check_interval = std::chrono::seconds(15);
    return std::make_unique<StorageLayerSelector>(config);
}

std::unique_ptr<StorageLayerSelector> StorageLayerSelectorFactory::CreatePerformanceOptimized() {
    StorageSelectionConfig config;
    config.strategy = StorageSelectionStrategy::LOAD_BALANCED;
    config.enable_load_balancing = true;
    config.load_balance_threshold = 0.7;
    config.max_acceptable_response_time_ms = 500.0;
    config.min_acceptable_success_rate = 0.95;
    return std::make_unique<StorageLayerSelector>(config);
}

} // namespace financial_data