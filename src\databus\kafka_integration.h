#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <unordered_map>
#include "data_types.h"
#include "lock_free_queue.h"

#ifdef KAFKA_ENABLED
#include <librdkafka/rdkafkacpp.h>
#endif

namespace financial_data {
namespace databus {

/**
 * @brief Kafka配置结构
 */
struct KafkaConfig {
    std::string brokers = "localhost:9092";
    std::string topic_prefix = "market_data";
    int32_t partition_count = 8;
    int32_t replication_factor = 3;
    int32_t batch_size = 1000;
    int32_t linger_ms = 1;              // 批次延迟时间
    int32_t buffer_memory = 33554432;   // 32MB缓冲区
    std::string compression_type = "snappy";
    bool enable_idempotence = true;
    int32_t acks = 1;                   // 确认级别
    int32_t retries = 3;
    int32_t retry_backoff_ms = 100;
    
    // 消费者配置
    std::string group_id = "market_data_consumer";
    std::string auto_offset_reset = "latest";
    bool enable_auto_commit = false;
    int32_t session_timeout_ms = 30000;
    int32_t heartbeat_interval_ms = 3000;
    int32_t max_poll_records = 1000;
};

/**
 * @brief Kafka生产者包装类
 */
class KafkaProducer {
public:
    using DeliveryCallback = std::function<void(const std::string& topic, 
                                               int32_t partition, 
                                               int64_t offset, 
                                               const std::string& error)>;

private:
#ifdef KAFKA_ENABLED
    std::unique_ptr<RdKafka::Producer> producer_;
    std::unique_ptr<RdKafka::Conf> conf_;
    std::unique_ptr<RdKafka::Topic> topic_;
#endif
    
    KafkaConfig config_;
    std::atomic<bool> running_{false};
    std::thread poll_thread_;
    DeliveryCallback delivery_callback_;
    
    // 性能统计
    std::atomic<uint64_t> messages_sent_{0};
    std::atomic<uint64_t> messages_failed_{0};
    std::atomic<uint64_t> bytes_sent_{0};

public:
    explicit KafkaProducer(const KafkaConfig& config);
    ~KafkaProducer();

    /**
     * @brief 初始化Kafka生产者
     */
    bool Initialize();

    /**
     * @brief 关闭生产者
     */
    void Shutdown();

    /**
     * @brief 发送标准化Tick数据
     */
    bool SendTick(const StandardTick& tick, const std::string& topic_suffix = "tick");

    /**
     * @brief 发送Level2数据
     */
    bool SendLevel2(const Level2Data& level2, const std::string& topic_suffix = "level2");

    /**
     * @brief 发送批量数据
     */
    bool SendBatch(const MarketDataBatch& batch, const std::string& topic_suffix = "batch");

    /**
     * @brief 发送原始消息
     */
    bool SendMessage(const std::string& topic, 
                    const std::string& key,
                    const void* payload, 
                    size_t payload_size,
                    int32_t partition = RdKafka::Topic::PARTITION_UA);

    /**
     * @brief 设置投递回调
     */
    void SetDeliveryCallback(DeliveryCallback callback) {
        delivery_callback_ = std::move(callback);
    }

    /**
     * @brief 获取性能统计
     */
    struct Statistics {
        uint64_t messages_sent;
        uint64_t messages_failed;
        uint64_t bytes_sent;
        double throughput_msg_per_sec;
        double throughput_mb_per_sec;
    };
    
    Statistics GetStatistics() const;

    /**
     * @brief 刷新缓冲区
     */
    void Flush(int timeout_ms = 5000);

private:
    void PollLoop();
    std::string SerializeTick(const StandardTick& tick);
    std::string SerializeLevel2(const Level2Data& level2);
    std::string SerializeBatch(const MarketDataBatch& batch);
    std::string GetTopicName(const std::string& suffix) const;
};

/**
 * @brief Kafka消费者包装类
 */
class KafkaConsumer {
public:
    using MessageCallback = std::function<void(const std::string& topic,
                                              int32_t partition,
                                              int64_t offset,
                                              const std::string& key,
                                              const void* payload,
                                              size_t payload_size)>;

private:
#ifdef KAFKA_ENABLED
    std::unique_ptr<RdKafka::KafkaConsumer> consumer_;
    std::unique_ptr<RdKafka::Conf> conf_;
#endif
    
    KafkaConfig config_;
    std::vector<std::string> topics_;
    std::atomic<bool> running_{false};
    std::thread consume_thread_;
    MessageCallback message_callback_;
    
    // 性能统计
    std::atomic<uint64_t> messages_received_{0};
    std::atomic<uint64_t> messages_processed_{0};
    std::atomic<uint64_t> bytes_received_{0};

public:
    explicit KafkaConsumer(const KafkaConfig& config);
    ~KafkaConsumer();

    /**
     * @brief 初始化Kafka消费者
     */
    bool Initialize();

    /**
     * @brief 订阅主题
     */
    bool Subscribe(const std::vector<std::string>& topics);

    /**
     * @brief 开始消费
     */
    bool StartConsuming();

    /**
     * @brief 停止消费
     */
    void StopConsuming();

    /**
     * @brief 设置消息回调
     */
    void SetMessageCallback(MessageCallback callback) {
        message_callback_ = std::move(callback);
    }

    /**
     * @brief 手动提交偏移量
     */
    bool CommitOffset();

    /**
     * @brief 获取性能统计
     */
    struct Statistics {
        uint64_t messages_received;
        uint64_t messages_processed;
        uint64_t bytes_received;
        double throughput_msg_per_sec;
        double throughput_mb_per_sec;
    };
    
    Statistics GetStatistics() const;

private:
    void ConsumeLoop();
    StandardTick DeserializeTick(const void* payload, size_t size);
    Level2Data DeserializeLevel2(const void* payload, size_t size);
    MarketDataBatch DeserializeBatch(const void* payload, size_t size);
};

/**
 * @brief Kafka集成管理器
 * 
 * 统一管理生产者和消费者，提供高级接口
 */
class KafkaIntegration {
private:
    KafkaConfig config_;
    std::unique_ptr<KafkaProducer> producer_;
    std::unordered_map<std::string, std::unique_ptr<KafkaConsumer>> consumers_;
    
    // 数据队列
    std::unique_ptr<TickQueue> tick_queue_;
    std::unique_ptr<Level2Queue> level2_queue_;
    std::unique_ptr<BatchQueue> batch_queue_;
    
    std::atomic<bool> running_{false};
    std::vector<std::thread> worker_threads_;

public:
    explicit KafkaIntegration(const KafkaConfig& config);
    ~KafkaIntegration();

    /**
     * @brief 初始化Kafka集成
     */
    bool Initialize();

    /**
     * @brief 关闭Kafka集成
     */
    void Shutdown();

    /**
     * @brief 获取生产者
     */
    KafkaProducer* GetProducer() { return producer_.get(); }

    /**
     * @brief 创建消费者
     */
    KafkaConsumer* CreateConsumer(const std::string& consumer_id);

    /**
     * @brief 启动数据处理工作线程
     */
    void StartWorkers();

    /**
     * @brief 停止数据处理工作线程
     */
    void StopWorkers();

    /**
     * @brief 推送Tick数据到队列
     */
    bool PushTick(const StandardTick& tick);

    /**
     * @brief 推送Level2数据到队列
     */
    bool PushLevel2(const Level2Data& level2);

    /**
     * @brief 推送批量数据到队列
     */
    bool PushBatch(const MarketDataBatch& batch);

private:
    void TickWorker();
    void Level2Worker();
    void BatchWorker();
};

} // namespace databus
} // namespace financial_data