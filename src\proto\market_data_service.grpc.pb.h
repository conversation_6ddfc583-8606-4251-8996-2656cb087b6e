// Generated gRPC service header for market_data_service.proto
#pragma once

#include <grpcpp/grpcpp.h>
#include "market_data_service.pb.h"

namespace financial_data {

class MarketDataService final {
public:
    class StubInterface {
    public:
        virtual ~StubInterface() {}
        
        virtual grpc::Status StreamTickData(grpc::ClientContext* context, 
                                           const TickDataRequest& request, 
                                           grpc::ClientReader<TickDataResponse>* reader) = 0;
        
        virtual std::unique_ptr<grpc::ClientReader<TickDataResponse>> StreamTickData(
            grpc::ClientContext* context, const TickDataRequest& request) = 0;
        
        virtual grpc::Status GetHistoricalTickData(grpc::ClientContext* context,
                                                  const HistoricalTickDataRequest& request,
                                                  grpc::ClientReader<TickDataResponse>* reader) = 0;
        
        virtual std::unique_ptr<grpc::ClientReader<TickDataResponse>> GetHistoricalTickData(
            grpc::ClientContext* context, const HistoricalTickDataRequest& request) = 0;
        
        virtual grpc::Status StreamKlineData(grpc::ClientContext* context,
                                            const KlineDataRequest& request,
                                            grpc::ClientReader<KlineDataResponse>* reader) = 0;
        
        virtual std::unique_ptr<grpc::ClientReader<KlineDataResponse>> StreamKlineData(
            grpc::ClientContext* context, const KlineDataRequest& request) = 0;
        
        virtual grpc::Status StreamLevel2Data(grpc::ClientContext* context,
                                             const Level2DataRequest& request,
                                             grpc::ClientReader<Level2DataResponse>* reader) = 0;
        
        virtual std::unique_ptr<grpc::ClientReader<Level2DataResponse>> StreamLevel2Data(
            grpc::ClientContext* context, const Level2DataRequest& request) = 0;
        
        virtual grpc::Status HealthCheck(grpc::ClientContext* context,
                                        const HealthCheckRequest& request,
                                        HealthCheckResponse* response) = 0;
    };
    
    class Stub final : public StubInterface {
    public:
        Stub(const std::shared_ptr<grpc::ChannelInterface>& channel);
        
        grpc::Status StreamTickData(grpc::ClientContext* context,
                                   const TickDataRequest& request,
                                   grpc::ClientReader<TickDataResponse>* reader) override;
        
        std::unique_ptr<grpc::ClientReader<TickDataResponse>> StreamTickData(
            grpc::ClientContext* context, const TickDataRequest& request) override;
        
        grpc::Status GetHistoricalTickData(grpc::ClientContext* context,
                                          const HistoricalTickDataRequest& request,
                                          grpc::ClientReader<TickDataResponse>* reader) override;
        
        std::unique_ptr<grpc::ClientReader<TickDataResponse>> GetHistoricalTickData(
            grpc::ClientContext* context, const HistoricalTickDataRequest& request) override;
        
        grpc::Status StreamKlineData(grpc::ClientContext* context,
                                    const KlineDataRequest& request,
                                    grpc::ClientReader<KlineDataResponse>* reader) override;
        
        std::unique_ptr<grpc::ClientReader<KlineDataResponse>> StreamKlineData(
            grpc::ClientContext* context, const KlineDataRequest& request) override;
        
        grpc::Status StreamLevel2Data(grpc::ClientContext* context,
                                     const Level2DataRequest& request,
                                     grpc::ClientReader<Level2DataResponse>* reader) override;
        
        std::unique_ptr<grpc::ClientReader<Level2DataResponse>> StreamLevel2Data(
            grpc::ClientContext* context, const Level2DataRequest& request) override;
        
        grpc::Status HealthCheck(grpc::ClientContext* context,
                                const HealthCheckRequest& request,
                                HealthCheckResponse* response) override;
        
    private:
        std::shared_ptr<grpc::ChannelInterface> channel_;
        const grpc::internal::RpcMethod rpcmethod_StreamTickData_;
        const grpc::internal::RpcMethod rpcmethod_GetHistoricalTickData_;
        const grpc::internal::RpcMethod rpcmethod_StreamKlineData_;
        const grpc::internal::RpcMethod rpcmethod_StreamLevel2Data_;
        const grpc::internal::RpcMethod rpcmethod_HealthCheck_;
    };
    
    static std::unique_ptr<Stub> NewStub(const std::shared_ptr<grpc::ChannelInterface>& channel,
                                        const grpc::StubOptions& options = grpc::StubOptions());
    
    class Service : public grpc::Service {
    public:
        Service();
        virtual ~Service();
        
        virtual grpc::Status StreamTickData(grpc::ServerContext* context,
                                           const TickDataRequest* request,
                                           grpc::ServerWriter<TickDataResponse>* writer);
        
        virtual grpc::Status GetHistoricalTickData(grpc::ServerContext* context,
                                                  const HistoricalTickDataRequest* request,
                                                  grpc::ServerWriter<TickDataResponse>* writer);
        
        virtual grpc::Status StreamKlineData(grpc::ServerContext* context,
                                            const KlineDataRequest* request,
                                            grpc::ServerWriter<KlineDataResponse>* writer);
        
        virtual grpc::Status StreamLevel2Data(grpc::ServerContext* context,
                                             const Level2DataRequest* request,
                                             grpc::ServerWriter<Level2DataResponse>* writer);
        
        virtual grpc::Status HealthCheck(grpc::ServerContext* context,
                                        const HealthCheckRequest* request,
                                        HealthCheckResponse* response);
    };
    
    template <class BaseClass>
    class WithAsyncMethod_StreamTickData : public BaseClass {
    private:
        void BaseClassMustBeDerivedFromService(const Service* service) {}
    public:
        WithAsyncMethod_StreamTickData() {
            ::grpc::Service::MarkMethodAsync(0);
        }
        ~WithAsyncMethod_StreamTickData() override {
            BaseClassMustBeDerivedFromService(this);
        }
        
        grpc::Status StreamTickData(grpc::ServerContext* context,
                                   const TickDataRequest* request,
                                   grpc::ServerWriter<TickDataResponse>* writer) override {
            abort();
            return grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "");
        }
        
        void RequestStreamTickData(grpc::ServerContext* context,
                                  TickDataRequest* request,
                                  grpc::ServerAsyncWriter<TickDataResponse>* writer,
                                  grpc::CompletionQueue* new_call_cq,
                                  grpc::ServerCompletionQueue* notification_cq,
                                  void* tag) {
            ::grpc::Service::RequestAsyncServerStreaming(0, context, request, writer, new_call_cq, notification_cq, tag);
        }
    };
};

} // namespace financial_data