#!/bin/bash

echo "========================================"
echo "PyTDX数据采集系统"
echo "========================================"
echo

echo "第一步: 更新代码表"
echo "----------------------------------------"
python3 update_symbol_lists.py
if [ $? -ne 0 ]; then
    echo "❌ 代码表更新失败"
    exit 1
fi

echo
echo "第二步: 采集历史数据"
echo "----------------------------------------"
python3 collect_historical_data.py
if [ $? -ne 0 ]; then
    echo "❌ 历史数据采集失败"
    exit 1
fi

echo
echo "✅ 数据采集完成！"
echo "数据已保存到数据库和本地文件"
echo