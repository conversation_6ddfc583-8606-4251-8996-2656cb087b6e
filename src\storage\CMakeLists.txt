# Storage library

add_library(storage
    redis_storage.cpp
    redis_storage.h
    cold_storage.cpp
    cold_storage.hpp
    lifecycle_manager.cpp
    lifecycle_manager.hpp
    archive_interface.cpp
    archive_interface.hpp
)

target_include_directories(storage PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Find hiredis library
find_path(HIREDIS_INCLUDE_DIR hiredis/hiredis.h)
find_library(HIREDIS_LIBRARY hiredis)

if(HIREDIS_INCLUDE_DIR AND HIREDIS_LIBRARY)
    target_include_directories(storage PRIVATE ${HIREDIS_INCLUDE_DIR})
    target_link_libraries(storage PRIVATE ${HIREDIS_LIBRARY})
    target_compile_definitions(storage PRIVATE HAVE_HIREDIS)
else()
    message(WARNING "hiredis not found, Redis functionality will be limited")
endif()

# Find required libraries for cold storage
find_package(PkgConfig REQUIRED)

# MinIO C++ SDK
find_path(MINIO_INCLUDE_DIR minio/client.h)
find_library(MINIO_LIBRARY miniocpp)

# AWS SDK
find_package(AWSSDK REQUIRED COMPONENTS s3)

# Arrow and Parquet
find_package(Arrow REQUIRED)
find_package(Parquet REQUIRED)

# OpenSSL for checksums
find_package(OpenSSL REQUIRED)

# JSON library
find_package(PkgConfig REQUIRED)
pkg_check_modules(JSONCPP jsoncpp)

# ZSTD for compression
find_library(ZSTD_LIBRARY zstd)

target_link_libraries(storage
    proto
)

# Link cold storage dependencies
if(MINIO_INCLUDE_DIR AND MINIO_LIBRARY)
    target_include_directories(storage PRIVATE ${MINIO_INCLUDE_DIR})
    target_link_libraries(storage PRIVATE ${MINIO_LIBRARY})
    target_compile_definitions(storage PRIVATE HAVE_MINIO)
endif()

if(AWSSDK_FOUND)
    target_link_libraries(storage PRIVATE ${AWSSDK_LINK_LIBRARIES})
    target_compile_definitions(storage PRIVATE HAVE_AWS_SDK)
endif()

if(Arrow_FOUND AND Parquet_FOUND)
    target_link_libraries(storage PRIVATE arrow parquet)
    target_compile_definitions(storage PRIVATE HAVE_ARROW_PARQUET)
endif()

if(OpenSSL_FOUND)
    target_link_libraries(storage PRIVATE OpenSSL::SSL OpenSSL::Crypto)
endif()

if(JSONCPP_FOUND)
    target_include_directories(storage PRIVATE ${JSONCPP_INCLUDE_DIRS})
    target_link_libraries(storage PRIVATE ${JSONCPP_LIBRARIES})
endif()

if(ZSTD_LIBRARY)
    target_link_libraries(storage PRIVATE ${ZSTD_LIBRARY})
    target_compile_definitions(storage PRIVATE HAVE_ZSTD)
endif()

target_compile_features(storage PUBLIC cxx_std_17)