"""
定时任务调度器 - 管理数据更新任务的调度和执行

支持功能：
- Cron表达式任务调度
- 任务优先级和依赖关系管理
- 任务状态监控和日志记录
- 失败重试和错误处理
- 任务执行统计和性能监控
"""

import asyncio
import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Union, Set
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid
from concurrent.futures import ThreadPoolExecutor, Future
import croniter
from abc import ABC, abstractmethod

# Import the task failure handler
from task_failure_handler import TaskFailureHandler, RetryConfig

logger = logging.getLogger(__name__)


class TaskType(Enum):
    """任务类型枚举"""
    HISTORICAL_UPDATE = "historical_update"
    DATA_MIGRATION = "data_migration"
    DATA_CLEANUP = "data_cleanup"
    HEALTH_CHECK = "health_check"
    QUALITY_CHECK = "quality_check"
    ARCHIVE_DATA = "archive_data"
    INCREMENTAL_UPDATE = "incremental_update"


class TaskStatus(Enum):
    """任务状态枚举"""
    SCHEDULED = "scheduled"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class TaskConfig:
    """任务配置类"""
    task_type: TaskType
    cron_expression: str
    symbols: List[str] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    max_retries: int = 3
    retry_delay_seconds: int = 60
    timeout_seconds: int = 3600  # 1小时超时
    dependencies: List[str] = field(default_factory=list)  # 依赖的任务ID
    enabled: bool = True
    description: str = ""
    
    def __post_init__(self):
        """验证配置有效性"""
        try:
            # 验证cron表达式
            croniter.croniter(self.cron_expression)
        except Exception as e:
            raise ValueError(f"Invalid cron expression '{self.cron_expression}': {e}")
        
        if self.max_retries < 0:
            raise ValueError("max_retries must be non-negative")
        
        if self.retry_delay_seconds < 0:
            raise ValueError("retry_delay_seconds must be non-negative")
        
        if self.timeout_seconds <= 0:
            raise ValueError("timeout_seconds must be positive")


@dataclass
class TaskExecution:
    """任务执行记录"""
    task_id: str
    execution_id: str
    task_type: TaskType
    status: TaskStatus
    scheduled_time: datetime
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    error_message: str = ""
    result: Optional[Dict[str, Any]] = None
    execution_duration_seconds: float = 0.0
    
    @property
    def is_running(self) -> bool:
        return self.status == TaskStatus.RUNNING
    
    @property
    def is_completed(self) -> bool:
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]


class TaskExecutor(ABC):
    """任务执行器抽象基类"""
    
    @abstractmethod
    async def execute(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行任务并返回结果"""
        pass
    
    @abstractmethod
    def get_task_types(self) -> List[TaskType]:
        """获取支持的任务类型"""
        pass


class DefaultTaskExecutor(TaskExecutor):
    """默认任务执行器实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.DefaultTaskExecutor")
    
    async def execute(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行任务"""
        self.logger.info(f"Executing task {execution.task_id} of type {task_config.task_type}")
        
        try:
            if task_config.task_type == TaskType.HISTORICAL_UPDATE:
                return await self._execute_historical_update(task_config, execution)
            elif task_config.task_type == TaskType.DATA_MIGRATION:
                return await self._execute_data_migration(task_config, execution)
            elif task_config.task_type == TaskType.DATA_CLEANUP:
                return await self._execute_data_cleanup(task_config, execution)
            elif task_config.task_type == TaskType.HEALTH_CHECK:
                return await self._execute_health_check(task_config, execution)
            elif task_config.task_type == TaskType.QUALITY_CHECK:
                return await self._execute_quality_check(task_config, execution)
            elif task_config.task_type == TaskType.ARCHIVE_DATA:
                return await self._execute_archive_data(task_config, execution)
            elif task_config.task_type == TaskType.INCREMENTAL_UPDATE:
                return await self._execute_incremental_update(task_config, execution)
            else:
                raise ValueError(f"Unsupported task type: {task_config.task_type}")
        
        except Exception as e:
            self.logger.error(f"Task execution failed: {e}")
            raise
    
    def get_task_types(self) -> List[TaskType]:
        """获取支持的任务类型"""
        return list(TaskType)
    
    async def _execute_historical_update(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行历史数据更新任务"""
        symbols = task_config.symbols or ["default"]
        parameters = task_config.parameters
        
        # 模拟历史数据更新
        await asyncio.sleep(1)  # 模拟处理时间
        
        return {
            "symbols_processed": len(symbols),
            "records_updated": parameters.get("expected_records", 1000),
            "update_type": "historical",
            "message": f"Updated historical data for {len(symbols)} symbols"
        }
    
    async def _execute_data_migration(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行数据迁移任务"""
        parameters = task_config.parameters
        
        # 模拟数据迁移
        await asyncio.sleep(2)  # 模拟处理时间
        
        return {
            "records_migrated": parameters.get("batch_size", 10000),
            "source_storage": parameters.get("source", "hot"),
            "target_storage": parameters.get("target", "warm"),
            "message": "Data migration completed successfully"
        }
    
    async def _execute_data_cleanup(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行数据清理任务"""
        parameters = task_config.parameters
        
        # 模拟数据清理
        await asyncio.sleep(0.5)  # 模拟处理时间
        
        return {
            "records_cleaned": parameters.get("cleanup_count", 500),
            "cleanup_type": parameters.get("cleanup_type", "expired_data"),
            "message": "Data cleanup completed successfully"
        }
    
    async def _execute_health_check(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行健康检查任务"""
        # 模拟健康检查
        await asyncio.sleep(0.2)  # 模拟处理时间
        
        return {
            "components_checked": ["storage", "collectors", "api"],
            "healthy_components": 3,
            "total_components": 3,
            "status": "healthy",
            "message": "All components are healthy"
        }
    
    async def _execute_quality_check(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行数据质量检查任务"""
        symbols = task_config.symbols or ["default"]
        
        # 模拟质量检查
        await asyncio.sleep(1.5)  # 模拟处理时间
        
        return {
            "symbols_checked": len(symbols),
            "quality_score": 0.95,
            "issues_found": 2,
            "message": f"Quality check completed for {len(symbols)} symbols"
        }
    
    async def _execute_archive_data(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行数据归档任务"""
        symbols = task_config.symbols or ["default"]
        parameters = task_config.parameters
        
        # 模拟数据归档
        await asyncio.sleep(3)  # 模拟处理时间
        
        return {
            "symbols_archived": len(symbols),
            "records_archived": parameters.get("archive_count", 50000),
            "archive_type": parameters.get("archive_type", "daily"),
            "message": f"Archived data for {len(symbols)} symbols"
        }
    
    async def _execute_incremental_update(self, task_config: TaskConfig, execution: TaskExecution) -> Dict[str, Any]:
        """执行增量数据更新任务"""
        symbols = task_config.symbols or ["default"]
        parameters = task_config.parameters
        
        # 模拟增量更新
        await asyncio.sleep(0.8)  # 模拟处理时间
        
        return {
            "symbols_updated": len(symbols),
            "new_records": parameters.get("new_records", 200),
            "update_type": "incremental",
            "last_update_time": datetime.now().isoformat(),
            "message": f"Incremental update completed for {len(symbols)} symbols"
        }


class ScheduledTaskManager:
    """定时任务调度器"""
    
    def __init__(self, executor: Optional[TaskExecutor] = None, 
                 failure_handler: Optional[TaskFailureHandler] = None,
                 retry_config: Optional[RetryConfig] = None):
        self.executor = executor or DefaultTaskExecutor()
        self.tasks: Dict[str, TaskConfig] = {}
        self.executions: Dict[str, TaskExecution] = {}
        self.execution_history: List[TaskExecution] = []
        
        # 任务失败处理器
        if failure_handler:
            self.failure_handler = failure_handler
        else:
            # 使用默认配置创建失败处理器
            default_retry_config = retry_config or RetryConfig(
                max_retries=3,
                base_delay_seconds=60,
                max_delay_seconds=3600
            )
            self.failure_handler = TaskFailureHandler(default_retry_config)
        
        # 调度器状态
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # 任务队列和执行管理
        self.task_queue = asyncio.Queue()
        self.running_tasks: Dict[str, Future] = {}
        self.thread_pool = ThreadPoolExecutor(max_workers=5)
        
        # 统计信息
        self.stats = {
            'total_scheduled': 0,
            'total_executed': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'cancelled_executions': 0,
            'average_execution_time': 0.0,
            'last_execution_time': None
        }
        
        self.logger = logging.getLogger(f"{__name__}.ScheduledTaskManager")
        
        # 任务依赖图
        self.dependency_graph: Dict[str, Set[str]] = {}
        
        # 回调函数
        self.task_start_callback: Optional[Callable] = None
        self.task_complete_callback: Optional[Callable] = None
        self.task_error_callback: Optional[Callable] = None
    
    def set_task_callbacks(self, 
                          start_callback: Optional[Callable] = None,
                          complete_callback: Optional[Callable] = None,
                          error_callback: Optional[Callable] = None):
        """设置任务回调函数"""
        self.task_start_callback = start_callback
        self.task_complete_callback = complete_callback
        self.task_error_callback = error_callback
    
    def schedule_task(self, task_id: str, task_config: TaskConfig) -> bool:
        """调度任务"""
        try:
            if task_id in self.tasks:
                self.logger.warning(f"Task {task_id} already exists, updating configuration")
            
            # 验证任务配置
            if not task_config.enabled:
                self.logger.info(f"Task {task_id} is disabled, skipping schedule")
                return True
            
            # 验证依赖关系
            for dep_id in task_config.dependencies:
                if dep_id not in self.tasks:
                    raise ValueError(f"Dependency task {dep_id} not found")
            
            self.tasks[task_id] = task_config
            self.dependency_graph[task_id] = set(task_config.dependencies)
            
            self.stats['total_scheduled'] += 1
            self.logger.info(f"Scheduled task {task_id} with cron '{task_config.cron_expression}'")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to schedule task {task_id}: {e}")
            return False
    
    def unschedule_task(self, task_id: str) -> bool:
        """取消调度任务"""
        try:
            if task_id not in self.tasks:
                self.logger.warning(f"Task {task_id} not found")
                return False
            
            # 取消正在运行的任务
            if task_id in self.running_tasks:
                future = self.running_tasks[task_id]
                future.cancel()
                del self.running_tasks[task_id]
                self.logger.info(f"Cancelled running task {task_id}")
            
            # 移除任务配置
            del self.tasks[task_id]
            if task_id in self.dependency_graph:
                del self.dependency_graph[task_id]
            
            # 移除其他任务对此任务的依赖
            for deps in self.dependency_graph.values():
                deps.discard(task_id)
            
            self.logger.info(f"Unscheduled task {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to unschedule task {task_id}: {e}")
            return False
    
    def start(self):
        """启动调度器"""
        if self.running:
            self.logger.warning("Scheduler is already running")
            return
        
        self.running = True
        self.stop_event.clear()
        
        # 启动调度器线程
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("Task scheduler started")
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            self.logger.warning("Scheduler is not running")
            return
        
        self.running = False
        self.stop_event.set()
        
        # 等待调度器线程结束
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        # 取消所有正在运行的任务
        for task_id, future in list(self.running_tasks.items()):
            future.cancel()
            self.logger.info(f"Cancelled task {task_id}")
        
        self.running_tasks.clear()
        
        # 关闭线程池
        self.thread_pool.shutdown(wait=True)
        
        self.logger.info("Task scheduler stopped")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        self.logger.info("Scheduler loop started")
        
        while not self.stop_event.is_set():
            try:
                current_time = datetime.now()
                
                # 检查需要执行的任务
                for task_id, task_config in self.tasks.items():
                    if not task_config.enabled:
                        continue
                    
                    # 检查任务是否应该执行
                    if self._should_execute_task(task_id, task_config, current_time):
                        # 检查依赖关系
                        if self._check_dependencies(task_id):
                            self._schedule_task_execution(task_id, task_config, current_time)
                
                # 清理已完成的任务
                self._cleanup_completed_tasks()
                
                # 等待下一次检查
                self.stop_event.wait(timeout=10)  # 每10秒检查一次
                
            except Exception as e:
                self.logger.error(f"Error in scheduler loop: {e}")
                self.stop_event.wait(timeout=5)  # 错误后等待5秒
        
        self.logger.info("Scheduler loop ended")
    
    def _should_execute_task(self, task_id: str, task_config: TaskConfig, current_time: datetime) -> bool:
        """检查任务是否应该执行"""
        try:
            # 检查任务是否已在运行
            if task_id in self.running_tasks:
                return False
            
            # 使用croniter检查是否到了执行时间
            cron = croniter.croniter(task_config.cron_expression, current_time)
            next_run = cron.get_prev(datetime)
            
            # 检查是否在最近的调度窗口内（允许1分钟的误差）
            time_diff = (current_time - next_run).total_seconds()
            
            # 如果在0-60秒内，且没有最近的执行记录，则应该执行
            if 0 <= time_diff <= 60:
                # 检查是否已经在这个时间窗口内执行过
                recent_executions = [
                    exec for exec in self.execution_history
                    if exec.task_id == task_id and 
                    exec.scheduled_time and
                    (current_time - exec.scheduled_time).total_seconds() < 120
                ]
                
                return len(recent_executions) == 0
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking task schedule for {task_id}: {e}")
            return False
    
    def _check_dependencies(self, task_id: str) -> bool:
        """检查任务依赖关系"""
        dependencies = self.dependency_graph.get(task_id, set())
        
        if not dependencies:
            return True
        
        # 检查所有依赖任务是否已成功完成
        for dep_id in dependencies:
            # 查找最近的依赖任务执行记录
            recent_executions = [
                exec for exec in self.execution_history
                if exec.task_id == dep_id and 
                exec.end_time and
                (datetime.now() - exec.end_time).total_seconds() < 3600  # 1小时内
            ]
            
            if not recent_executions:
                self.logger.debug(f"Dependency {dep_id} has no recent successful execution")
                return False
            
            # 检查最近的执行是否成功
            latest_execution = max(recent_executions, key=lambda x: x.end_time)
            if latest_execution.status != TaskStatus.COMPLETED:
                self.logger.debug(f"Dependency {dep_id} latest execution status: {latest_execution.status}")
                return False
        
        return True
    
    def _schedule_task_execution(self, task_id: str, task_config: TaskConfig, scheduled_time: datetime):
        """调度任务执行"""
        try:
            # 创建执行记录
            execution = TaskExecution(
                task_id=task_id,
                execution_id=str(uuid.uuid4()),
                task_type=task_config.task_type,
                status=TaskStatus.SCHEDULED,
                scheduled_time=scheduled_time
            )
            
            self.executions[execution.execution_id] = execution
            
            # 提交任务到线程池
            future = self.thread_pool.submit(self._execute_task_wrapper, task_config, execution)
            self.running_tasks[task_id] = future
            
            self.logger.info(f"Scheduled execution of task {task_id} (execution_id: {execution.execution_id})")
            
        except Exception as e:
            self.logger.error(f"Failed to schedule task execution for {task_id}: {e}")
    
    def _execute_task_wrapper(self, task_config: TaskConfig, execution: TaskExecution):
        """任务执行包装器（在线程池中运行）"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 运行异步任务
                result = loop.run_until_complete(self._execute_task_async(task_config, execution))
                return result
            finally:
                loop.close()
                
        except Exception as e:
            self.logger.error(f"Error in task execution wrapper: {e}")
            execution.status = TaskStatus.FAILED
            execution.error_message = str(e)
            execution.end_time = datetime.now()
            
            if execution.start_time:
                execution.execution_duration_seconds = (execution.end_time - execution.start_time).total_seconds()
            
            # 移动到历史记录
            self.execution_history.append(execution)
            self.stats['failed_executions'] += 1
            
            # 调用错误回调
            if self.task_error_callback:
                try:
                    self.task_error_callback(execution.task_id, str(e))
                except Exception as callback_error:
                    self.logger.error(f"Error in task error callback: {callback_error}")
    
    async def _execute_task_async(self, task_config: TaskConfig, execution: TaskExecution):
        """异步执行任务 - 使用TaskFailureHandler处理失败和重试"""
        try:
            # 更新执行状态
            execution.status = TaskStatus.RUNNING
            execution.start_time = datetime.now()
            
            self.stats['total_executed'] += 1
            
            # 调用开始回调
            if self.task_start_callback:
                try:
                    self.task_start_callback(execution.task_id, execution.execution_id)
                except Exception as callback_error:
                    self.logger.error(f"Error in task start callback: {callback_error}")
            
            self.logger.info(f"Starting task execution: {execution.task_id}")
            
            # 执行任务（带超时）
            result = await asyncio.wait_for(
                self.executor.execute(task_config, execution),
                timeout=task_config.timeout_seconds
            )
            
            # 任务成功完成
            execution.status = TaskStatus.COMPLETED
            execution.end_time = datetime.now()
            execution.result = result
            execution.execution_duration_seconds = (execution.end_time - execution.start_time).total_seconds()
            
            # 标记任务成功（用于失败处理器统计）
            self.failure_handler.mark_task_success(execution.task_id, execution.execution_id)
            
            # 更新统计信息
            self.stats['successful_executions'] += 1
            self.stats['last_execution_time'] = execution.end_time.isoformat()
            
            # 更新平均执行时间
            total_successful = self.stats['successful_executions']
            current_avg = self.stats['average_execution_time']
            new_avg = ((current_avg * (total_successful - 1)) + execution.execution_duration_seconds) / total_successful
            self.stats['average_execution_time'] = new_avg
            
            self.logger.info(f"Task {execution.task_id} completed successfully in {execution.execution_duration_seconds:.2f}s")
            
            # 调用完成回调
            if self.task_complete_callback:
                try:
                    self.task_complete_callback(execution.task_id, result)
                except Exception as callback_error:
                    self.logger.error(f"Error in task complete callback: {callback_error}")
                    
        except Exception as e:
            # 使用TaskFailureHandler处理失败
            context = {
                'task_id': execution.task_id,
                'execution_id': execution.execution_id,
                'task_type': task_config.task_type.value,
                'symbols': task_config.symbols,
                'parameters': task_config.parameters
            }
            
            should_retry = await self.failure_handler.handle_task_failure(
                execution.task_id, execution.execution_id, e, context
            )
            
            if should_retry:
                # 任务将被重试，保持运行状态
                execution.status = TaskStatus.RUNNING
                self.logger.info(f"Task {execution.task_id} will be retried by failure handler")
                
                # 递归调用自己进行重试
                await self._execute_task_async(task_config, execution)
                return
            else:
                # 任务最终失败
                execution.status = TaskStatus.FAILED
                execution.error_message = str(e)
                execution.end_time = datetime.now()
                execution.execution_duration_seconds = (execution.end_time - execution.start_time).total_seconds()
                self.stats['failed_executions'] += 1
                
                self.logger.error(f"Task {execution.task_id} failed permanently: {str(e)}")
                
                # 调用错误回调
                if self.task_error_callback:
                    try:
                        self.task_error_callback(execution.task_id, str(e))
                    except Exception as callback_error:
                        self.logger.error(f"Error in task error callback: {callback_error}")
        
        # 移动到历史记录
        self.execution_history.append(execution)
        
        # 限制历史记录数量
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-500:]  # 保留最近500条记录
    
    def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        completed_tasks = []
        
        for task_id, future in list(self.running_tasks.items()):
            if future.done():
                completed_tasks.append(task_id)
        
        for task_id in completed_tasks:
            del self.running_tasks[task_id]
    
    def get_all_task_status(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        status_list = []
        
        for task_id, task_config in self.tasks.items():
            # 获取最近的执行记录
            recent_executions = [
                exec for exec in self.execution_history
                if exec.task_id == task_id
            ]
            
            if recent_executions:
                latest_execution = max(recent_executions, key=lambda x: x.scheduled_time)
                last_run = latest_execution.end_time or latest_execution.start_time
                status = latest_execution.status.value
                error_message = latest_execution.error_message
                retry_count = latest_execution.retry_count
            else:
                last_run = None
                status = "never_run"
                error_message = ""
                retry_count = 0
            
            # 计算下次运行时间
            try:
                cron = croniter.croniter(task_config.cron_expression, datetime.now())
                next_run = cron.get_next(datetime)
            except Exception:
                next_run = None
            
            status_info = {
                "task_id": task_id,
                "task_type": task_config.task_type.value,
                "status": status,
                "enabled": task_config.enabled,
                "cron_expression": task_config.cron_expression,
                "priority": task_config.priority.value,
                "last_run": last_run.isoformat() if last_run else None,
                "next_run": next_run.isoformat() if next_run else None,
                "retry_count": retry_count,
                "error_message": error_message,
                "dependencies": task_config.dependencies,
                "description": task_config.description
            }
            
            status_list.append(status_info)
        
        # 按优先级和下次运行时间排序
        status_list.sort(key=lambda x: (
            -x.get("priority", 0),
            x.get("next_run") or "9999-12-31T23:59:59"
        ))
        
        return status_list
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取特定任务状态"""
        all_status = self.get_all_task_status()
        for status in all_status:
            if status["task_id"] == task_id:
                return status
        return None
    
    def get_execution_history(self, task_id: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取任务执行历史"""
        executions = self.execution_history
        
        if task_id:
            executions = [exec for exec in executions if exec.task_id == task_id]
        
        # 按时间倒序排列
        executions = sorted(executions, key=lambda x: x.scheduled_time, reverse=True)
        
        # 限制返回数量
        executions = executions[:limit]
        
        # 转换为字典格式
        history = []
        for exec in executions:
            history.append({
                "task_id": exec.task_id,
                "execution_id": exec.execution_id,
                "task_type": exec.task_type.value,
                "status": exec.status.value,
                "scheduled_time": exec.scheduled_time.isoformat(),
                "start_time": exec.start_time.isoformat() if exec.start_time else None,
                "end_time": exec.end_time.isoformat() if exec.end_time else None,
                "execution_duration_seconds": exec.execution_duration_seconds,
                "retry_count": exec.retry_count,
                "error_message": exec.error_message,
                "result": exec.result
            })
        
        return history
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        stats = self.stats.copy()
        stats.update({
            "total_tasks": len(self.tasks),
            "enabled_tasks": len([t for t in self.tasks.values() if t.enabled]),
            "running_tasks": len(self.running_tasks),
            "scheduler_running": self.running,
            "execution_history_size": len(self.execution_history)
        })
        return stats
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        if task_id not in self.tasks:
            return False
        
        self.tasks[task_id].enabled = False
        self.logger.info(f"Paused task {task_id}")
        return True
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        if task_id not in self.tasks:
            return False
        
        self.tasks[task_id].enabled = True
        self.logger.info(f"Resumed task {task_id}")
        return True
    
    def cancel_running_task(self, task_id: str) -> bool:
        """取消正在运行的任务"""
        if task_id not in self.running_tasks:
            return False
        
        future = self.running_tasks[task_id]
        cancelled = future.cancel()
        
        if cancelled:
            del self.running_tasks[task_id]
            self.logger.info(f"Cancelled running task {task_id}")
            self.stats['cancelled_executions'] += 1
        
        return cancelled
    
    def update_task_config(self, task_id: str, task_config: TaskConfig) -> bool:
        """更新任务配置"""
        try:
            if task_id not in self.tasks:
                return False
            
            # 如果任务正在运行，先取消
            if task_id in self.running_tasks:
                self.cancel_running_task(task_id)
            
            # 更新配置
            self.tasks[task_id] = task_config
            self.dependency_graph[task_id] = set(task_config.dependencies)
            
            self.logger.info(f"Updated configuration for task {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update task configuration for {task_id}: {e}")
            return False
    
    def export_configuration(self) -> Dict[str, Any]:
        """导出任务配置"""
        config = {
            "tasks": {},
            "statistics": self.get_statistics(),
            "export_time": datetime.now().isoformat()
        }
        
        for task_id, task_config in self.tasks.items():
            config["tasks"][task_id] = {
                "task_type": task_config.task_type.value,
                "cron_expression": task_config.cron_expression,
                "symbols": task_config.symbols,
                "parameters": task_config.parameters,
                "priority": task_config.priority.value,
                "max_retries": task_config.max_retries,
                "retry_delay_seconds": task_config.retry_delay_seconds,
                "timeout_seconds": task_config.timeout_seconds,
                "dependencies": task_config.dependencies,
                "enabled": task_config.enabled,
                "description": task_config.description
            }
        
        return config
    
    def import_configuration(self, config: Dict[str, Any]) -> bool:
        """导入任务配置"""
        try:
            tasks_config = config.get("tasks", {})
            
            for task_id, task_data in tasks_config.items():
                task_config = TaskConfig(
                    task_type=TaskType(task_data["task_type"]),
                    cron_expression=task_data["cron_expression"],
                    symbols=task_data.get("symbols", []),
                    parameters=task_data.get("parameters", {}),
                    priority=TaskPriority(task_data.get("priority", TaskPriority.NORMAL.value)),
                    max_retries=task_data.get("max_retries", 3),
                    retry_delay_seconds=task_data.get("retry_delay_seconds", 60),
                    timeout_seconds=task_data.get("timeout_seconds", 3600),
                    dependencies=task_data.get("dependencies", []),
                    enabled=task_data.get("enabled", True),
                    description=task_data.get("description", "")
                )
                
                self.schedule_task(task_id, task_config)
            
            self.logger.info(f"Imported configuration for {len(tasks_config)} tasks")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to import configuration: {e}")
            return False
    
    # TaskFailureHandler integration methods
    
    def get_failed_tasks(self) -> List[Dict[str, Any]]:
        """获取失败的任务（通过失败处理器）"""
        failed_states = self.failure_handler.get_failed_tasks()
        return [state.to_dict() for state in failed_states]
    
    def get_retrying_tasks(self) -> List[Dict[str, Any]]:
        """获取正在重试的任务（通过失败处理器）"""
        retrying_states = self.failure_handler.get_retrying_tasks()
        return [state.to_dict() for state in retrying_states]
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return self.failure_handler.get_error_statistics()
    
    def get_error_history(self, task_id: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取错误历史"""
        return self.failure_handler.get_error_history(task_id=task_id, limit=limit)
    
    def analyze_error_patterns(self) -> Dict[str, Any]:
        """分析错误模式"""
        return self.failure_handler.analyze_error_patterns()
    
    def export_error_report(self, output_path: str) -> bool:
        """导出错误报告"""
        return self.failure_handler.export_error_report(output_path)
    
    def cleanup_old_task_states(self, max_age_days: int = 7) -> int:
        """清理旧的任务状态"""
        return self.failure_handler.cleanup_old_states(max_age_days)
    
    def reset_error_statistics(self):
        """重置错误统计信息"""
        self.failure_handler.reset_statistics()
        self.logger.info("Error statistics reset completed")
    
    def set_failure_handler_callbacks(self, 
                                    error_callback: Optional[Callable] = None,
                                    retry_callback: Optional[Callable] = None,
                                    recovery_callback: Optional[Callable] = None):
        """设置失败处理器回调函数"""
        self.failure_handler.set_callbacks(
            error_callback=error_callback,
            retry_callback=retry_callback,
            recovery_callback=recovery_callback
        )