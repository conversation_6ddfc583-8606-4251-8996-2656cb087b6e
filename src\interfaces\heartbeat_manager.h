#pragma once

#include <string>
#include <unordered_map>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <chrono>
#include <functional>
#include <spdlog/spdlog.h>
#include "websocket_types.h"

namespace financial_data {
namespace interfaces {

/**
 * @brief 心跳状态
 */
enum class HeartbeatStatus {
    ACTIVE,      // 活跃状态
    WARNING,     // 警告状态（接近超时）
    TIMEOUT,     // 超时状态
    DISCONNECTED // 已断开连接
};

/**
 * @brief 客户端心跳信息
 */
struct ClientHeartbeatInfo {
    std::string client_id;
    HeartbeatStatus status = HeartbeatStatus::ACTIVE;
    std::chrono::steady_clock::time_point last_heartbeat;
    std::chrono::steady_clock::time_point last_ping_sent;
    std::chrono::steady_clock::time_point connect_time;
    
    std::atomic<uint64_t> heartbeat_count{0};
    std::atomic<uint64_t> ping_count{0};
    std::atomic<uint64_t> pong_count{0};
    std::atomic<uint64_t> timeout_count{0};
    
    // 延迟统计
    std::atomic<uint64_t> avg_rtt_ms{0};
    std::atomic<uint64_t> max_rtt_ms{0};
    std::atomic<uint64_t> min_rtt_ms{UINT64_MAX};
    
    ClientHeartbeatInfo() {
        auto now = std::chrono::steady_clock::now();
        last_heartbeat = now;
        last_ping_sent = now;
        connect_time = now;
    }
    
    void UpdateHeartbeat() {
        last_heartbeat = std::chrono::steady_clock::now();
        heartbeat_count++;
        status = HeartbeatStatus::ACTIVE;
    }
    
    void UpdatePing() {
        last_ping_sent = std::chrono::steady_clock::now();
        ping_count++;
    }
    
    void UpdatePong(uint64_t rtt_ms) {
        pong_count++;
        
        // 更新RTT统计
        uint64_t current_min = min_rtt_ms.load();
        while (rtt_ms < current_min && !min_rtt_ms.compare_exchange_weak(current_min, rtt_ms)) {
            current_min = min_rtt_ms.load();
        }
        
        uint64_t current_max = max_rtt_ms.load();
        while (rtt_ms > current_max && !max_rtt_ms.compare_exchange_weak(current_max, rtt_ms)) {
            current_max = max_rtt_ms.load();
        }
        
        // 简单的移动平均
        uint64_t current_avg = avg_rtt_ms.load();
        uint64_t new_avg = (current_avg * 7 + rtt_ms) / 8;  // 7/8权重的移动平均
        avg_rtt_ms = new_avg;
    }
    
    std::chrono::milliseconds GetTimeSinceLastHeartbeat() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - last_heartbeat);
    }
    
    std::chrono::milliseconds GetConnectionDuration() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - connect_time);
    }
    
    double GetHeartbeatRate() const {
        auto duration = GetConnectionDuration();
        return duration.count() > 0 ? 
               static_cast<double>(heartbeat_count.load()) / (duration.count() / 1000.0) : 0.0;
    }
};

/**
 * @brief 心跳管理器配置
 */
struct HeartbeatConfig {
    // 心跳间隔配置
    uint32_t heartbeat_interval_ms = 30000;    // 30秒心跳间隔
    uint32_t heartbeat_timeout_ms = 60000;     // 60秒心跳超时
    uint32_t warning_threshold_ms = 45000;     // 45秒警告阈值
    
    // Ping/Pong配置
    bool enable_ping_pong = true;
    uint32_t ping_interval_ms = 10000;         // 10秒ping间隔
    uint32_t pong_timeout_ms = 5000;           // 5秒pong超时
    
    // 检查间隔
    uint32_t check_interval_ms = 5000;         // 5秒检查间隔
    
    // 重连配置
    bool enable_auto_reconnect = true;
    uint32_t max_reconnect_attempts = 3;
    uint32_t reconnect_delay_ms = 1000;
    
    // 统计配置
    bool enable_statistics = true;
    uint32_t statistics_interval_ms = 60000;   // 60秒统计间隔
    
    // 清理配置
    bool enable_auto_cleanup = true;
    uint32_t cleanup_interval_ms = 300000;     // 5分钟清理间隔
    uint32_t disconnected_retention_ms = 600000; // 10分钟保留断开连接的客户端信息
};

/**
 * @brief 心跳统计信息
 */
struct HeartbeatStatistics {
    std::atomic<uint64_t> total_clients{0};
    std::atomic<uint64_t> active_clients{0};
    std::atomic<uint64_t> warning_clients{0};
    std::atomic<uint64_t> timeout_clients{0};
    std::atomic<uint64_t> disconnected_clients{0};
    
    std::atomic<uint64_t> total_heartbeats{0};
    std::atomic<uint64_t> total_pings{0};
    std::atomic<uint64_t> total_pongs{0};
    std::atomic<uint64_t> total_timeouts{0};
    
    std::atomic<uint64_t> avg_rtt_ms{0};
    std::atomic<uint64_t> max_rtt_ms{0};
    std::atomic<uint64_t> min_rtt_ms{UINT64_MAX};
    
    void Reset() {
        total_clients = 0;
        active_clients = 0;
        warning_clients = 0;
        timeout_clients = 0;
        disconnected_clients = 0;
        total_heartbeats = 0;
        total_pings = 0;
        total_pongs = 0;
        total_timeouts = 0;
        avg_rtt_ms = 0;
        max_rtt_ms = 0;
        min_rtt_ms = UINT64_MAX;
    }
    
    double GetActiveRate() const {
        uint64_t total = total_clients.load();
        return total > 0 ? static_cast<double>(active_clients.load()) / total : 0.0;
    }
    
    double GetTimeoutRate() const {
        uint64_t total_hb = total_heartbeats.load();
        return total_hb > 0 ? static_cast<double>(total_timeouts.load()) / total_hb : 0.0;
    }
    
    double GetPongRate() const {
        uint64_t total_ping = total_pings.load();
        return total_ping > 0 ? static_cast<double>(total_pongs.load()) / total_ping : 0.0;
    }
};

/**
 * @brief 心跳事件类型
 */
enum class HeartbeatEvent {
    CLIENT_CONNECTED,
    CLIENT_DISCONNECTED,
    HEARTBEAT_RECEIVED,
    HEARTBEAT_TIMEOUT,
    PING_SENT,
    PONG_RECEIVED,
    STATUS_CHANGED
};

/**
 * @brief 心跳事件处理器
 */
using HeartbeatEventHandler = std::function<void(HeartbeatEvent, const std::string&, const ClientHeartbeatInfo&)>;

/**
 * @brief 心跳管理器
 * 
 * 负责管理WebSocket客户端的心跳检测、连接状态监控和自动重连
 */
class HeartbeatManager {
private:
    HeartbeatConfig config_;
    HeartbeatStatistics statistics_;
    
    // 客户端心跳信息
    std::unordered_map<std::string, std::shared_ptr<ClientHeartbeatInfo>> client_heartbeats_;
    mutable std::mutex heartbeats_mutex_;
    
    // 工作线程
    std::thread heartbeat_thread_;
    std::thread statistics_thread_;
    std::thread cleanup_thread_;
    
    std::atomic<bool> running_{false};
    std::condition_variable cv_;
    std::mutex cv_mutex_;
    
    // 事件处理器
    std::vector<HeartbeatEventHandler> event_handlers_;
    std::mutex event_handlers_mutex_;
    
    std::shared_ptr<spdlog::logger> logger_;

public:
    explicit HeartbeatManager(const HeartbeatConfig& config = HeartbeatConfig{});
    ~HeartbeatManager();

    /**
     * @brief 启动心跳管理器
     */
    bool Start();

    /**
     * @brief 停止心跳管理器
     */
    void Stop();

    /**
     * @brief 检查是否正在运行
     */
    bool IsRunning() const { return running_.load(); }

    /**
     * @brief 添加客户端
     */
    bool AddClient(const std::string& client_id);

    /**
     * @brief 移除客户端
     */
    bool RemoveClient(const std::string& client_id);

    /**
     * @brief 更新客户端心跳
     */
    bool UpdateHeartbeat(const std::string& client_id);

    /**
     * @brief 处理Ping响应
     */
    bool HandlePong(const std::string& client_id, uint64_t ping_timestamp);

    /**
     * @brief 获取客户端心跳信息
     */
    std::shared_ptr<ClientHeartbeatInfo> GetClientInfo(const std::string& client_id) const;

    /**
     * @brief 获取所有客户端心跳信息
     */
    std::vector<std::shared_ptr<ClientHeartbeatInfo>> GetAllClientInfo() const;

    /**
     * @brief 获取指定状态的客户端列表
     */
    std::vector<std::string> GetClientsByStatus(HeartbeatStatus status) const;

    /**
     * @brief 检查客户端是否活跃
     */
    bool IsClientActive(const std::string& client_id) const;

    /**
     * @brief 获取客户端状态
     */
    HeartbeatStatus GetClientStatus(const std::string& client_id) const;

    /**
     * @brief 强制检查所有客户端状态
     */
    void CheckAllClients();

    /**
     * @brief 发送Ping到指定客户端
     */
    using PingSender = std::function<bool(const std::string& client_id, uint64_t timestamp)>;
    void SetPingSender(PingSender sender) { ping_sender_ = sender; }

    /**
     * @brief 断开连接回调
     */
    using DisconnectHandler = std::function<void(const std::string& client_id, const std::string& reason)>;
    void SetDisconnectHandler(DisconnectHandler handler) { disconnect_handler_ = handler; }

    /**
     * @brief 添加事件处理器
     */
    void AddEventHandler(HeartbeatEventHandler handler);

    /**
     * @brief 移除事件处理器
     */
    void RemoveEventHandler(HeartbeatEventHandler handler);

    /**
     * @brief 获取配置
     */
    HeartbeatConfig GetConfig() const { return config_; }

    /**
     * @brief 更新配置
     */
    bool UpdateConfig(const HeartbeatConfig& config);

    /**
     * @brief 获取统计信息
     */
    HeartbeatStatistics GetStatistics() const { return statistics_; }

    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

    /**
     * @brief 获取统计摘要
     */
    std::string GetStatisticsSummary() const;

    /**
     * @brief 获取客户端摘要
     */
    std::string GetClientSummary() const;

    /**
     * @brief 清理断开连接的客户端
     */
    size_t CleanupDisconnectedClients();

    /**
     * @brief 获取健康状态
     */
    struct HealthStatus {
        bool overall_healthy;
        uint64_t active_clients;
        uint64_t timeout_clients;
        double active_rate;
        double timeout_rate;
        std::string status_message;
    };
    
    HealthStatus GetHealthStatus() const;

private:
    PingSender ping_sender_;
    DisconnectHandler disconnect_handler_;

    /**
     * @brief 心跳检查线程主循环
     */
    void HeartbeatLoop();

    /**
     * @brief 统计线程主循环
     */
    void StatisticsLoop();

    /**
     * @brief 清理线程主循环
     */
    void CleanupLoop();

    /**
     * @brief 检查单个客户端状态
     */
    void CheckClientStatus(const std::string& client_id, std::shared_ptr<ClientHeartbeatInfo> info);

    /**
     * @brief 发送Ping消息
     */
    void SendPing(const std::string& client_id, std::shared_ptr<ClientHeartbeatInfo> info);

    /**
     * @brief 处理客户端超时
     */
    void HandleClientTimeout(const std::string& client_id, std::shared_ptr<ClientHeartbeatInfo> info);

    /**
     * @brief 更新统计信息
     */
    void UpdateStatistics();

    /**
     * @brief 触发事件
     */
    void TriggerEvent(HeartbeatEvent event, const std::string& client_id, const ClientHeartbeatInfo& info);

    /**
     * @brief 计算客户端状态
     */
    HeartbeatStatus CalculateClientStatus(const ClientHeartbeatInfo& info) const;

    /**
     * @brief 获取当前时间戳（毫秒）
     */
    uint64_t GetCurrentTimestampMs() const;
};

} // namespace interfaces
} // namespace financial_data