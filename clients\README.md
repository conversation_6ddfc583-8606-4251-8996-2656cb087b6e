# 金融数据服务gRPC客户端SDK

本目录包含多语言的gRPC客户端SDK，支持流式数据订阅、负载均衡和故障转移。

## 功能特性

- **流式数据传输**: 支持实时tick数据、K线数据和Level2深度数据流
- **负载均衡**: 自动选择最佳服务器，基于延迟和健康状态
- **故障转移**: 自动检测服务器故障并切换到备用服务器
- **流量控制**: 内置背压处理机制，防止客户端缓冲区溢出
- **多语言支持**: 提供Python、Go和Java客户端SDK

## 目录结构

```
clients/
├── python/          # Python客户端SDK
│   ├── grpc_client.py
│   └── requirements.txt
├── go/              # Go客户端SDK
│   ├── client.go
│   └── go.mod
├── java/            # Java客户端SDK
│   ├── src/main/java/com/financialdata/client/
│   │   └── FinancialDataClient.java
│   └── pom.xml
└── README.md        # 本文件
```

## Python客户端

### 安装依赖

```bash
cd python
pip install -r requirements.txt
```

### 使用示例

```python
from grpc_client import FinancialDataClient

# 服务器列表（支持负载均衡）
servers = ["localhost:50051", "localhost:50052", "localhost:50053"]

# 创建客户端
client = FinancialDataClient(servers)

# 定义数据处理回调
def tick_handler(response):
    for tick in response.ticks:
        print(f"Tick: {tick.symbol} @ {tick.last_price}")

# 订阅实时数据
future = client.stream_tick_data(
    symbols=["AAPL", "GOOGL"],
    exchange="NASDAQ",
    callback=tick_handler,
    buffer_size=1000
)

# 等待数据流
time.sleep(30)
client.close()
```

### 主要功能

- `stream_tick_data()`: 订阅实时tick数据流
- `stream_kline_data()`: 订阅K线数据流
- `stream_level2_data()`: 订阅Level2深度数据流
- `get_historical_tick_data()`: 获取历史tick数据
- `health_check()`: 服务健康检查

## Go客户端

### 安装依赖

```bash
cd go
go mod tidy
```

### 使用示例

```go
package main

import (
    "context"
    "log"
    "time"
)

func main() {
    servers := []string{"localhost:50051", "localhost:50052"}
    client, err := NewFinancialDataClient(servers, 3)
    if err != nil {
        log.Fatal(err)
    }
    defer client.Close()

    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    // 订阅tick数据
    tickCallback := func(response *pb.TickDataResponse) {
        for _, tick := range response.Ticks {
            log.Printf("Tick: %s @ %.2f", tick.Symbol, tick.LastPrice)
        }
    }

    err = client.StreamTickData(ctx, []string{"AAPL"}, "NASDAQ", tickCallback, 1000)
    if err != nil {
        log.Printf("Stream error: %v", err)
    }
}
```

### 主要功能

- `StreamTickData()`: 订阅实时tick数据流
- `StreamKlineData()`: 订阅K线数据流
- `StreamLevel2Data()`: 订阅Level2深度数据流
- `GetHistoricalTickData()`: 获取历史tick数据
- `HealthCheck()`: 服务健康检查

## Java客户端

### 构建项目

```bash
cd java
mvn clean compile
```

### 使用示例

```java
import com.financialdata.client.FinancialDataClient;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

public class Example {
    public static void main(String[] args) {
        List<String> servers = Arrays.asList(
            "localhost:50051", 
            "localhost:50052"
        );
        
        FinancialDataClient client = new FinancialDataClient(servers, 3);
        
        try {
            // 订阅tick数据
            CompletableFuture<Void> future = client.streamTickData(
                Arrays.asList("AAPL", "GOOGL"),
                "NASDAQ",
                response -> {
                    response.getTicksList().forEach(tick -> 
                        System.out.printf("Tick: %s @ %.2f%n", 
                            tick.getSymbol(), tick.getLastPrice())
                    );
                },
                1000
            );
            
            Thread.sleep(30000); // 等待30秒
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            client.close();
        }
    }
}
```

### 主要功能

- `streamTickData()`: 订阅实时tick数据流
- `streamKlineData()`: 订阅K线数据流
- `streamLevel2Data()`: 订阅Level2深度数据流
- `getHistoricalTickData()`: 获取历史tick数据
- `healthCheck()`: 服务健康检查

## 负载均衡和故障转移

所有客户端SDK都内置了以下功能：

### 负载均衡策略
- 基于服务器延迟选择最佳服务器
- 考虑服务器健康状态和错误计数
- 支持多服务器配置

### 故障转移机制
- 自动检测服务器故障
- 快速切换到备用服务器
- 指数退避重试策略
- 服务器健康状态恢复

### 流量控制
- 客户端缓冲区管理
- 背压处理机制
- 自适应流量控制
- 消息丢弃策略

## 配置参数

### 通用参数
- `buffer_size`: 客户端缓冲区大小（默认1000）
- `max_retries`: 最大重试次数（默认3）
- `keepalive_time`: 心跳间隔（默认30秒）
- `keepalive_timeout`: 心跳超时（默认5秒）

### 流量控制参数
- 背压激活阈值：缓冲区使用率80%
- 背压释放阈值：缓冲区使用率50%
- 服务器不健康阈值：连续错误3次

## 错误处理

客户端SDK提供了完善的错误处理机制：

1. **连接错误**: 自动重试和故障转移
2. **流错误**: 重新建立流连接
3. **超时错误**: 可配置的超时和重试
4. **数据错误**: 回调函数异常处理

## 性能优化

- 使用连接池管理gRPC连接
- 异步消息处理避免阻塞
- 内存高效的数据结构
- 可配置的缓冲区大小

## 监控和日志

- 详细的连接状态日志
- 延迟和错误统计
- 流量控制状态监控
- 故障转移事件记录

## 注意事项

1. 确保服务器地址格式正确（host:port）
2. 根据网络环境调整超时参数
3. 监控客户端内存使用情况
4. 合理设置缓冲区大小
5. 处理回调函数中的异常