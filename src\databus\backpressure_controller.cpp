#include "backpressure_controller.h"
#include <iostream>
#include <algorithm>
#include <thread>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#else
#include <sys/resource.h>
#include <unistd.h>
#include <fstream>
#endif

namespace financial_data {
namespace databus {

BackpressureController::BackpressureController(const BackpressureConfig& config) 
    : config_(config) {
    statistics_.Reset();
    last_adjustment_time_ = std::chrono::steady_clock::now();
    last_throughput_check_ = std::chrono::steady_clock::now();
}

BackpressureController::~BackpressureController() {
    Stop();
}

bool BackpressureController::Start() {
    if (running_) {
        return true;
    }
    
    running_ = true;
    monitor_thread_ = std::thread(&BackpressureController::MonitorLoop, this);
    
    std::cout << "BackpressureController started with strategy: " 
              << static_cast<int>(config_.strategy) << std::endl;
    return true;
}

void BackpressureController::Stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    if (monitor_thread_.joinable()) {
        monitor_thread_.join();
    }
    
    std::cout << "BackpressureController stopped" << std::endl;
}

void BackpressureController::AddMonitoredQueue(std::shared_ptr<QueueMonitor> queue) {
    std::lock_guard<std::mutex> lock(queues_mutex_);
    monitored_queues_.push_back(std::move(queue));
}

void BackpressureController::RemoveMonitoredQueue(std::shared_ptr<QueueMonitor> queue) {
    std::lock_guard<std::mutex> lock(queues_mutex_);
    
    auto it = std::find(monitored_queues_.begin(), monitored_queues_.end(), queue);
    if (it != monitored_queues_.end()) {
        monitored_queues_.erase(it);
    }
}

bool BackpressureController::ShouldDropMessage() {
    BackpressureState state = current_state_.load();
    
    switch (config_.strategy) {
        case BackpressureStrategy::DROP_OLDEST:
        case BackpressureStrategy::DROP_NEWEST:
            return state == BackpressureState::CRITICAL || state == BackpressureState::EMERGENCY;
            
        case BackpressureStrategy::ADAPTIVE:
            return state == BackpressureState::EMERGENCY || 
                   (state == BackpressureState::CRITICAL && statistics_.GetDropRate() < 0.1);
            
        default:
            return false;
    }
}

bool BackpressureController::ShouldThrottle() {
    if (!throttling_enabled_.load()) {
        return false;
    }
    
    BackpressureState state = current_state_.load();
    
    switch (config_.strategy) {
        case BackpressureStrategy::THROTTLE_PRODUCER:
            return state >= BackpressureState::WARNING;
            
        case BackpressureStrategy::ADAPTIVE:
            return state >= BackpressureState::WARNING && statistics_.GetThrottleRate() < 0.2;
            
        default:
            return false;
    }
}

bool BackpressureController::ShouldBlock() {
    if (!blocking_enabled_.load()) {
        return false;
    }
    
    BackpressureState state = current_state_.load();
    
    switch (config_.strategy) {
        case BackpressureStrategy::BLOCK_PRODUCER:
            return state >= BackpressureState::CRITICAL;
            
        case BackpressureStrategy::ADAPTIVE:
            return state == BackpressureState::EMERGENCY;
            
        default:
            return false;
    }
}

bool BackpressureController::WaitForBackpressureRelief(uint32_t timeout_ms) {
    auto start_time = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::milliseconds(timeout_ms);
    
    while (ShouldBlock() || ShouldThrottle()) {
        auto elapsed = std::chrono::steady_clock::now() - start_time;
        if (elapsed >= timeout_duration) {
            return false;  // 超时
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    return true;
}

void BackpressureController::RecordMessage(uint64_t processing_latency_us) {
    statistics_.total_messages++;
    
    if (processing_latency_us > 0) {
        uint64_t current_avg = statistics_.avg_latency_us.load();
        uint64_t new_avg = (current_avg + processing_latency_us) / 2;
        statistics_.avg_latency_us = new_avg;
        
        uint64_t current_max = statistics_.max_latency_us.load();
        while (processing_latency_us > current_max && 
               !statistics_.max_latency_us.compare_exchange_weak(current_max, processing_latency_us)) {
            // CAS循环
        }
        
        if (processing_latency_us > config_.latency_threshold_us) {
            statistics_.latency_overflow_count++;
        }
    }
}

void BackpressureController::RecordDroppedMessage() {
    statistics_.dropped_messages++;
}

void BackpressureController::RecordThrottledMessage() {
    statistics_.throttled_messages++;
}

void BackpressureController::RecordBlockedOperation() {
    statistics_.blocked_operations++;
}

BackpressureStatistics BackpressureController::GetStatistics() const {
    return statistics_;
}

void BackpressureController::ResetStatistics() {
    statistics_.Reset();
}

void BackpressureController::UpdateConfig(const BackpressureConfig& config) {
    config_ = config;
    std::cout << "BackpressureController configuration updated" << std::endl;
}

void BackpressureController::TriggerBackpressure(BackpressureState state) {
    UpdateState(state);
    ApplyBackpressureStrategy(state);
}

void BackpressureController::RecoverFromBackpressure() {
    UpdateState(BackpressureState::NORMAL);
    throttling_enabled_ = false;
    blocking_enabled_ = false;
}

void BackpressureController::MonitorLoop() {
    while (running_) {
        try {
            BackpressureState new_state = EvaluateSystemState();
            
            if (new_state != current_state_.load()) {
                UpdateState(new_state);
                ApplyBackpressureStrategy(new_state);
            }
            
            // 自适应调整
            if (config_.strategy == BackpressureStrategy::ADAPTIVE) {
                AdaptiveAdjustment();
            }
            
            // 调用统计回调
            if (statistics_callback_) {
                statistics_callback_(statistics_);
            }
            
        } catch (const std::exception& e) {
            std::cerr << "BackpressureController monitor error: " << e.what() << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(config_.monitor_interval_ms));
    }
}

BackpressureState BackpressureController::EvaluateSystemState() {
    size_t avg_queue_usage, max_queue_size;
    bool queue_pressure = CheckQueuePressure(avg_queue_usage, max_queue_size);
    bool memory_pressure = CheckMemoryPressure();
    bool latency_pressure = CheckLatencyPressure();
    bool throughput_pressure = CheckThroughputPressure();
    
    // 更新统计信息
    statistics_.avg_queue_size = avg_queue_usage;
    statistics_.max_queue_size = max_queue_size;
    
    // 根据压力情况确定状态
    int pressure_count = 0;
    if (queue_pressure) pressure_count++;
    if (memory_pressure) pressure_count++;
    if (latency_pressure) pressure_count++;
    if (throughput_pressure) pressure_count++;
    
    // 检查临界条件
    if (avg_queue_usage >= config_.queue_critical_watermark ||
        statistics_.avg_latency_us.load() >= config_.latency_critical_us ||
        statistics_.memory_usage_mb.load() >= config_.memory_critical_watermark_mb) {
        return BackpressureState::EMERGENCY;
    }
    
    // 根据压力数量确定状态
    if (pressure_count >= 3) {
        return BackpressureState::CRITICAL;
    } else if (pressure_count >= 2) {
        return BackpressureState::WARNING;
    } else if (pressure_count >= 1) {
        return BackpressureState::WARNING;
    } else {
        return BackpressureState::NORMAL;
    }
}

bool BackpressureController::CheckQueuePressure(size_t& avg_queue_usage, size_t& max_queue_size) {
    std::lock_guard<std::mutex> lock(queues_mutex_);
    
    if (monitored_queues_.empty()) {
        avg_queue_usage = 0;
        max_queue_size = 0;
        return false;
    }
    
    size_t total_usage = 0;
    size_t total_capacity = 0;
    max_queue_size = 0;
    
    for (const auto& queue : monitored_queues_) {
        if (!queue) continue;
        
        size_t queue_size = queue->GetQueueSize();
        size_t queue_capacity = queue->GetQueueCapacity();
        
        total_usage += queue_size;
        total_capacity += queue_capacity;
        max_queue_size = std::max(max_queue_size, queue_size);
        
        if (queue_size > statistics_.max_queue_size.load()) {
            statistics_.max_queue_size = queue_size;
        }
    }
    
    if (total_capacity == 0) {
        avg_queue_usage = 0;
        return false;
    }
    
    avg_queue_usage = (total_usage * 100) / total_capacity;
    
    bool high_pressure = avg_queue_usage >= config_.queue_high_watermark;
    if (high_pressure) {
        statistics_.queue_overflow_count++;
    }
    
    return high_pressure;
}

bool BackpressureController::CheckMemoryPressure() {
    size_t memory_usage = GetSystemMemoryUsage();
    statistics_.memory_usage_mb = memory_usage;
    
    if (memory_usage > statistics_.peak_memory_usage_mb.load()) {
        statistics_.peak_memory_usage_mb = memory_usage;
    }
    
    bool high_pressure = memory_usage >= config_.memory_high_watermark_mb;
    if (high_pressure) {
        statistics_.memory_overflow_count++;
    }
    
    return high_pressure;
}

bool BackpressureController::CheckLatencyPressure() {
    uint64_t avg_latency = statistics_.avg_latency_us.load();
    return avg_latency >= config_.latency_threshold_us;
}

bool BackpressureController::CheckThroughputPressure() {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_throughput_check_);
    
    if (elapsed.count() >= 1) {  // 每秒检查一次
        uint64_t current_messages = statistics_.total_messages.load();
        uint64_t messages_per_sec = current_messages - last_message_count_.load();
        
        last_message_count_ = current_messages;
        last_throughput_check_ = now;
        
        return messages_per_sec >= config_.throughput_threshold_per_sec;
    }
    
    return false;
}

void BackpressureController::UpdateState(BackpressureState new_state) {
    BackpressureState old_state = current_state_.exchange(new_state);
    
    if (old_state != new_state) {
        statistics_.current_state = new_state;
        statistics_.last_state_change = std::chrono::steady_clock::now();
        
        std::cout << "BackpressureController state changed from " 
                  << static_cast<int>(old_state) << " to " 
                  << static_cast<int>(new_state) << std::endl;
        
        if (state_change_callback_) {
            state_change_callback_(old_state, new_state);
        }
    }
}

void BackpressureController::ApplyBackpressureStrategy(BackpressureState state) {
    switch (state) {
        case BackpressureState::NORMAL:
            throttling_enabled_ = false;
            blocking_enabled_ = false;
            break;
            
        case BackpressureState::WARNING:
            if (config_.strategy == BackpressureStrategy::THROTTLE_PRODUCER ||
                config_.strategy == BackpressureStrategy::ADAPTIVE) {
                throttling_enabled_ = true;
            }
            break;
            
        case BackpressureState::CRITICAL:
            throttling_enabled_ = true;
            if (config_.strategy == BackpressureStrategy::BLOCK_PRODUCER ||
                config_.strategy == BackpressureStrategy::ADAPTIVE) {
                blocking_enabled_ = true;
            }
            CleanupExpiredData();
            break;
            
        case BackpressureState::EMERGENCY:
            throttling_enabled_ = true;
            blocking_enabled_ = true;
            CleanupExpiredData();
            break;
    }
}

void BackpressureController::AdaptiveAdjustment() {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_adjustment_time_);
    
    if (elapsed.count() < 5) {  // 每5秒调整一次
        return;
    }
    
    last_adjustment_time_ = now;
    
    double drop_rate = statistics_.GetDropRate();
    double throttle_rate = statistics_.GetThrottleRate();
    
    // 根据丢弃率和限流率调整阈值
    if (drop_rate > 0.05) {  // 丢弃率超过5%
        adaptive_threshold_factor_ = std::min(1.5, adaptive_threshold_factor_.load() * 1.1);
    } else if (drop_rate < 0.01 && throttle_rate < 0.05) {  // 丢弃率和限流率都很低
        adaptive_threshold_factor_ = std::max(0.5, adaptive_threshold_factor_.load() * 0.95);
    }
    
    std::cout << "Adaptive adjustment: factor=" << adaptive_threshold_factor_.load() 
              << ", drop_rate=" << drop_rate << ", throttle_rate=" << throttle_rate << std::endl;
}

size_t BackpressureController::GetSystemMemoryUsage() const {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        return pmc.WorkingSetSize / (1024 * 1024);  // 转换为MB
    }
    return 0;
#else
    // Linux系统
    std::ifstream status_file("/proc/self/status");
    std::string line;
    
    while (std::getline(status_file, line)) {
        if (line.substr(0, 6) == "VmRSS:") {
            size_t pos = line.find_first_of("0123456789");
            if (pos != std::string::npos) {
                size_t kb = std::stoull(line.substr(pos));
                return kb / 1024;  // 转换为MB
            }
        }
    }
    
    return 0;
#endif
}

double BackpressureController::CalculateQueueUsage(size_t queue_size, size_t queue_capacity) const {
    if (queue_capacity == 0) {
        return 0.0;
    }
    return static_cast<double>(queue_size) / queue_capacity * 100.0;
}

void BackpressureController::ApplyThrottling() {
    if (throttling_enabled_.load()) {
        std::this_thread::sleep_for(std::chrono::microseconds(config_.throttle_sleep_us));
        RecordThrottledMessage();
    }
}

void BackpressureController::CleanupExpiredData() {
    std::lock_guard<std::mutex> lock(queues_mutex_);
    
    for (const auto& queue : monitored_queues_) {
        if (queue && queue->GetQueueSize() > queue->GetQueueCapacity() * 0.8) {
            // 当队列使用率超过80%时，清理部分数据
            // 这里只是示例，实际实现需要根据具体队列类型来处理
            std::cout << "Cleaning up queue data due to high usage" << std::endl;
        }
    }
}

// BackpressureControllerFactory实现
std::unique_ptr<BackpressureController> BackpressureControllerFactory::CreateDefault() {
    BackpressureConfig config;
    config.strategy = BackpressureStrategy::ADAPTIVE;
    config.queue_high_watermark = 80;
    config.memory_high_watermark_mb = 1024;
    config.latency_threshold_us = 1000;
    
    return std::make_unique<BackpressureController>(config);
}

std::unique_ptr<BackpressureController> BackpressureControllerFactory::CreateHighPerformance() {
    BackpressureConfig config;
    config.strategy = BackpressureStrategy::DROP_OLDEST;
    config.queue_high_watermark = 90;
    config.memory_high_watermark_mb = 2048;
    config.latency_threshold_us = 500;
    config.throughput_threshold_per_sec = 1000000;
    
    return std::make_unique<BackpressureController>(config);
}

std::unique_ptr<BackpressureController> BackpressureControllerFactory::CreateLowLatency() {
    BackpressureConfig config;
    config.strategy = BackpressureStrategy::THROTTLE_PRODUCER;
    config.queue_high_watermark = 60;
    config.memory_high_watermark_mb = 512;
    config.latency_threshold_us = 100;
    config.monitor_interval_ms = 50;
    
    return std::make_unique<BackpressureController>(config);
}

std::unique_ptr<BackpressureController> BackpressureControllerFactory::CreateMemorySensitive() {
    BackpressureConfig config;
    config.strategy = BackpressureStrategy::ADAPTIVE;
    config.queue_high_watermark = 70;
    config.memory_high_watermark_mb = 256;
    config.memory_critical_watermark_mb = 512;
    config.latency_threshold_us = 2000;
    
    return std::make_unique<BackpressureController>(config);
}

} // namespace databus
} // namespace financial_data