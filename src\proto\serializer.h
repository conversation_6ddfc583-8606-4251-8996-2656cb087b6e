#pragma once

#include "data_types.h"
#include "market_data.pb.h"
#include <memory>
#include <string_view>
#include <span>

namespace financial_data {

// 高性能序列化器，支持零拷贝操作
class Serializer {
public:
    // 序列化StandardTick到Protocol Buffer
    static bool SerializeTick(const StandardTick& tick, TickData* pb_tick);
    
    // 序列化Level2Data到Protocol Buffer
    static bool SerializeLevel2(const Level2Data& level2, Level2Data* pb_level2);
    
    // 序列化MarketDataWrapper到Protocol Buffer
    static bool SerializeMarketData(const MarketDataWrapper& wrapper, MarketData* pb_data);
    
    // 序列化批量数据
    static bool SerializeBatch(const MarketDataBatch& batch, MarketDataBatch* pb_batch);
    
    // 反序列化Protocol Buffer到StandardTick
    static bool DeserializeTick(const TickData& pb_tick, StandardTick* tick);
    
    // 反序列化Protocol Buffer到Level2Data
    static bool DeserializeLevel2(const Level2Data& pb_level2, Level2Data* level2);
    
    // 反序列化Protocol Buffer到MarketDataWrapper
    static bool DeserializeMarketData(const MarketData& pb_data, MarketDataWrapper* wrapper);
    
    // 反序列化批量数据
    static bool DeserializeBatch(const MarketDataBatch& pb_batch, MarketDataBatch* batch);
};

// 零拷贝缓冲区管理器
class ZeroCopyBuffer {
private:
    std::unique_ptr<char[]> buffer_;
    size_t capacity_;
    size_t size_;
    
public:
    explicit ZeroCopyBuffer(size_t initial_capacity = 64 * 1024);  // 64KB默认
    ~ZeroCopyBuffer() = default;
    
    // 禁止拷贝，允许移动
    ZeroCopyBuffer(const ZeroCopyBuffer&) = delete;
    ZeroCopyBuffer& operator=(const ZeroCopyBuffer&) = delete;
    ZeroCopyBuffer(ZeroCopyBuffer&&) = default;
    ZeroCopyBuffer& operator=(ZeroCopyBuffer&&) = default;
    
    // 获取缓冲区指针
    char* Data() { return buffer_.get(); }
    const char* Data() const { return buffer_.get(); }
    
    // 获取容量和大小
    size_t Capacity() const { return capacity_; }
    size_t Size() const { return size_; }
    
    // 设置数据大小
    void SetSize(size_t size) { size_ = size; }
    
    // 确保容量足够
    bool EnsureCapacity(size_t required_size);
    
    // 重置缓冲区
    void Reset() { size_ = 0; }
    
    // 获取字符串视图（零拷贝）
    std::string_view GetStringView() const {
        return std::string_view(buffer_.get(), size_);
    }
    
    // 获取字节跨度（零拷贝）
    std::span<const char> GetSpan() const {
        return std::span<const char>(buffer_.get(), size_);
    }
};

// 高性能序列化工具类
class FastSerializer {
private:
    ZeroCopyBuffer buffer_;
    
public:
    FastSerializer() = default;
    
    // 序列化到缓冲区
    bool SerializeToBuffer(const MarketDataWrapper& data);
    bool SerializeToBuffer(const MarketDataBatch& batch);
    
    // 从缓冲区反序列化
    bool DeserializeFromBuffer(std::string_view data, MarketDataWrapper* wrapper);
    bool DeserializeFromBuffer(std::string_view data, MarketDataBatch* batch);
    
    // 获取序列化后的数据（零拷贝）
    std::string_view GetSerializedData() const {
        return buffer_.GetStringView();
    }
    
    // 获取缓冲区大小
    size_t GetBufferSize() const {
        return buffer_.Size();
    }
    
    // 重置序列化器
    void Reset() {
        buffer_.Reset();
    }
};

// 内存池分配器，用于减少内存分配开销
template<typename T>
class MemoryPool {
private:
    std::vector<std::unique_ptr<T>> pool_;
    std::vector<T*> available_;
    size_t pool_size_;
    
public:
    explicit MemoryPool(size_t initial_size = 1000) : pool_size_(initial_size) {
        pool_.reserve(initial_size);
        available_.reserve(initial_size);
        
        // 预分配对象
        for (size_t i = 0; i < initial_size; ++i) {
            auto obj = std::make_unique<T>();
            available_.push_back(obj.get());
            pool_.push_back(std::move(obj));
        }
    }
    
    // 获取对象
    T* Acquire() {
        if (available_.empty()) {
            // 扩展池大小
            size_t old_size = pool_.size();
            size_t new_size = old_size * 2;
            pool_.reserve(new_size);
            available_.reserve(new_size);
            
            for (size_t i = old_size; i < new_size; ++i) {
                auto obj = std::make_unique<T>();
                available_.push_back(obj.get());
                pool_.push_back(std::move(obj));
            }
        }
        
        T* obj = available_.back();
        available_.pop_back();
        return obj;
    }
    
    // 释放对象
    void Release(T* obj) {
        if (obj) {
            // 重置对象状态
            *obj = T{};
            available_.push_back(obj);
        }
    }
    
    // 获取池统计信息
    size_t TotalSize() const { return pool_.size(); }
    size_t AvailableSize() const { return available_.size(); }
    size_t UsedSize() const { return pool_.size() - available_.size(); }
};

} // namespace financial_data