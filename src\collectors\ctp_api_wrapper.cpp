#include "ctp_api_wrapper.h"
#include <spdlog/spdlog.h>
#include <thread>
#include <chrono>
#include <cstring>

namespace financial_data {

CTPApiWrapper::CTPApiWrapper() 
    : md_api_(nullptr)
    , connected_(false)
    , logged_in_(false)
    , request_id_(0) {
}

CTPApiWrapper::~CTPApiWrapper() {
    Release();
}

bool CTPApiWrapper::Initialize(const std::string& flow_path) {
    if (md_api_ != nullptr) {
        spdlog::warn("CTP API already initialized");
        return true;
    }
    
    flow_path_ = flow_path;
    
    // 创建CTP行情API实例
    md_api_ = CThostFtdcMdApi::CreateFtdcMdApi(flow_path.c_str());
    if (!md_api_) {
        spdlog::error("Failed to create CTP MD API instance");
        return false;
    }
    
    // 注册回调接口
    md_api_->RegisterSpi(this);
    
    spdlog::info("CTP API initialized with flow path: {}", flow_path);
    return true;
}

bool CTPApiWrapper::RegisterFront(const std::string& front_address) {
    if (!md_api_) {
        spdlog::error("CTP API not initialized");
        return false;
    }
    
    // 注册前置机地址
    char* address = new char[front_address.length() + 1];
    strcpy(address, front_address.c_str());
    md_api_->RegisterFront(address);
    
    spdlog::info("Registered CTP front server: {}", front_address);
    return true;
}

void CTPApiWrapper::Init() {
    if (!md_api_) {
        spdlog::error("CTP API not initialized");
        return;
    }
    
    // 初始化API，开始连接
    md_api_->Init();
    spdlog::info("CTP API initialization started");
}

void CTPApiWrapper::Release() {
    if (md_api_) {
        logged_in_ = false;
        connected_ = false;
        
        // 释放API实例
        md_api_->RegisterSpi(nullptr);
        md_api_->Release();
        md_api_ = nullptr;
        
        spdlog::info("CTP API released");
    }
}

bool CTPApiWrapper::Login(const std::string& broker_id, const std::string& user_id, const std::string& password) {
    if (!md_api_ || !connected_) {
        spdlog::error("CTP API not connected");
        return false;
    }
    
    if (logged_in_) {
        spdlog::warn("Already logged in");
        return true;
    }
    
    // 构造登录请求
    CThostFtdcReqUserLoginField login_req = {};
    strncpy(login_req.BrokerID, broker_id.c_str(), sizeof(login_req.BrokerID) - 1);
    strncpy(login_req.UserID, user_id.c_str(), sizeof(login_req.UserID) - 1);
    strncpy(login_req.Password, password.c_str(), sizeof(login_req.Password) - 1);
    
    // 发送登录请求
    int result = md_api_->ReqUserLogin(&login_req, GetNextRequestId());
    if (result != 0) {
        spdlog::error("Failed to send login request, error code: {}", result);
        return false;
    }
    
    spdlog::info("Login request sent for user: {}", user_id);
    return true;
}

void CTPApiWrapper::Logout() {
    if (!md_api_ || !logged_in_) {
        return;
    }
    
    // 构造登出请求
    CThostFtdcUserLogoutField logout_req = {};
    strncpy(logout_req.BrokerID, "", sizeof(logout_req.BrokerID) - 1);
    strncpy(logout_req.UserID, "", sizeof(logout_req.UserID) - 1);
    
    // 发送登出请求
    int result = md_api_->ReqUserLogout(&logout_req, GetNextRequestId());
    if (result != 0) {
        spdlog::error("Failed to send logout request, error code: {}", result);
    }
    
    logged_in_ = false;
    spdlog::info("Logout request sent");
}

bool CTPApiWrapper::SubscribeMarketData(const std::vector<std::string>& symbols) {
    if (!md_api_ || !logged_in_) {
        spdlog::error("CTP API not logged in");
        return false;
    }
    
    if (symbols.empty()) {
        spdlog::warn("No symbols to subscribe");
        return true;
    }
    
    // 准备合约代码数组
    std::vector<char*> instrument_ids;
    instrument_ids.reserve(symbols.size());
    
    for (const auto& symbol : symbols) {
        char* id = new char[symbol.length() + 1];
        strcpy(id, symbol.c_str());
        instrument_ids.push_back(id);
    }
    
    // 发送订阅请求
    int result = md_api_->SubscribeMarketData(instrument_ids.data(), static_cast<int>(instrument_ids.size()));
    
    // 清理内存
    for (char* id : instrument_ids) {
        delete[] id;
    }
    
    if (result != 0) {
        spdlog::error("Failed to subscribe market data, error code: {}", result);
        return false;
    }
    
    // 更新订阅状态
    {
        std::lock_guard<std::mutex> lock(subscription_mutex_);
        for (const auto& symbol : symbols) {
            subscription_status_[symbol] = true;
        }
    }
    
    spdlog::info("Subscribed to {} symbols", symbols.size());
    return true;
}

bool CTPApiWrapper::UnsubscribeMarketData(const std::vector<std::string>& symbols) {
    if (!md_api_ || !logged_in_) {
        spdlog::error("CTP API not logged in");
        return false;
    }
    
    if (symbols.empty()) {
        spdlog::warn("No symbols to unsubscribe");
        return true;
    }
    
    // 准备合约代码数组
    std::vector<char*> instrument_ids;
    instrument_ids.reserve(symbols.size());
    
    for (const auto& symbol : symbols) {
        char* id = new char[symbol.length() + 1];
        strcpy(id, symbol.c_str());
        instrument_ids.push_back(id);
    }
    
    // 发送退订请求
    int result = md_api_->UnSubscribeMarketData(instrument_ids.data(), static_cast<int>(instrument_ids.size()));
    
    // 清理内存
    for (char* id : instrument_ids) {
        delete[] id;
    }
    
    if (result != 0) {
        spdlog::error("Failed to unsubscribe market data, error code: {}", result);
        return false;
    }
    
    // 更新订阅状态
    {
        std::lock_guard<std::mutex> lock(subscription_mutex_);
        for (const auto& symbol : symbols) {
            subscription_status_.erase(symbol);
        }
    }
    
    spdlog::info("Unsubscribed from {} symbols", symbols.size());
    return true;
}

// CTP API回调方法实现
void CTPApiWrapper::OnFrontConnected() {
    connected_ = true;
    spdlog::info("CTP front server connected");
    
    if (on_connected_) {
        on_connected_();
    }
}

void CTPApiWrapper::OnFrontDisconnected(int nReason) {
    connected_ = false;
    logged_in_ = false;
    spdlog::warn("CTP front server disconnected, reason: {}", nReason);
    
    if (on_disconnected_) {
        on_disconnected_(nReason);
    }
}

void CTPApiWrapper::OnRspUserLogin(CThostFtdcRspUserLoginField* pRspUserLogin, 
                                  CThostFtdcRspInfoField* pRspInfo, 
                                  int nRequestID, 
                                  bool bIsLast) {
    if (!IsErrorRspInfo(pRspInfo)) {
        logged_in_ = true;
        spdlog::info("User login successful");
        
        if (on_login_) {
            on_login_(true, "");
        }
    } else {
        logged_in_ = false;
        std::string error_msg = GetErrorMessage(pRspInfo);
        spdlog::error("User login failed: {}", error_msg);
        
        if (on_login_) {
            on_login_(false, error_msg);
        }
    }
}

void CTPApiWrapper::OnRspUserLogout(CThostFtdcUserLogoutField* pUserLogout, 
                                   CThostFtdcRspInfoField* pRspInfo, 
                                   int nRequestID, 
                                   bool bIsLast) {
    logged_in_ = false;
    spdlog::info("User logout successful");
}

void CTPApiWrapper::OnRspSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, 
                                      CThostFtdcRspInfoField* pRspInfo, 
                                      int nRequestID, 
                                      bool bIsLast) {
    if (!IsErrorRspInfo(pRspInfo)) {
        if (pSpecificInstrument) {
            spdlog::debug("Market data subscription successful for: {}", pSpecificInstrument->InstrumentID);
        }
    } else {
        std::string error_msg = GetErrorMessage(pRspInfo);
        spdlog::error("Market data subscription failed: {}", error_msg);
        
        if (on_error_) {
            on_error_(pRspInfo->ErrorID, error_msg);
        }
    }
}

void CTPApiWrapper::OnRspUnSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, 
                                        CThostFtdcRspInfoField* pRspInfo, 
                                        int nRequestID, 
                                        bool bIsLast) {
    if (!IsErrorRspInfo(pRspInfo)) {
        if (pSpecificInstrument) {
            spdlog::debug("Market data unsubscription successful for: {}", pSpecificInstrument->InstrumentID);
        }
    } else {
        std::string error_msg = GetErrorMessage(pRspInfo);
        spdlog::error("Market data unsubscription failed: {}", error_msg);
        
        if (on_error_) {
            on_error_(pRspInfo->ErrorID, error_msg);
        }
    }
}

void CTPApiWrapper::OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* pDepthMarketData) {
    if (pDepthMarketData && on_market_data_) {
        on_market_data_(pDepthMarketData);
    }
}

void CTPApiWrapper::OnRspError(CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) {
    if (pRspInfo) {
        std::string error_msg = GetErrorMessage(pRspInfo);
        spdlog::error("CTP API error: {}", error_msg);
        
        if (on_error_) {
            on_error_(pRspInfo->ErrorID, error_msg);
        }
    }
}

// 私有辅助方法
bool CTPApiWrapper::IsErrorRspInfo(CThostFtdcRspInfoField* pRspInfo) {
    return pRspInfo && pRspInfo->ErrorID != 0;
}

std::string CTPApiWrapper::GetErrorMessage(CThostFtdcRspInfoField* pRspInfo) {
    if (!pRspInfo) {
        return "Unknown error";
    }
    
    std::string error_msg = "ErrorID: " + std::to_string(pRspInfo->ErrorID);
    if (pRspInfo->ErrorMsg[0] != '\0') {
        error_msg += ", ErrorMsg: " + std::string(pRspInfo->ErrorMsg);
    }
    
    return error_msg;
}

} // namespace financial_data