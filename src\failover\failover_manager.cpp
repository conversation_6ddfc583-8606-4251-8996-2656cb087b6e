#include "failover_manager.h"
#include <algorithm>
#include <random>
#include <sstream>

namespace financial_data {

FailoverManager::FailoverManager(const FailoverConfig& config)
    : config_(config)
    , logger_(spdlog::get("failover") ? spdlog::get("failover") : spdlog::default_logger())
    , current_role_(NodeRole::UNKNOWN)
    , current_status_(NodeStatus::UNKNOWN)
    , running_(false)
    , failover_in_progress_(false)
    , failover_attempts_(0) {
    
    logger_->info("Failover Manager initialized for cluster: {}", config_.cluster_name);
    
    // 初始化本地节点信息
    NodeInfo local_node;
    local_node.node_id = config_.local_node_id;
    local_node.role = NodeRole::SECONDARY;  // 默认为备节点
    local_node.status = NodeStatus::HEALTHY;
    local_node.last_heartbeat = std::chrono::steady_clock::now();
    local_node.priority = 1;  // 默认优先级
    
    std::lock_guard<std::mutex> lock(nodes_mutex_);
    cluster_nodes_[config_.local_node_id] = local_node;
    
    // 初始化对等节点
    for (const auto& peer : config_.peer_nodes) {
        NodeInfo peer_node;
        peer_node.node_id = peer;
        peer_node.role = NodeRole::UNKNOWN;
        peer_node.status = NodeStatus::UNKNOWN;
        peer_node.priority = 1;
        cluster_nodes_[peer] = peer_node;
    }
}

FailoverManager::~FailoverManager() {
    Stop();
    logger_->info("Failover Manager destroyed");
}

bool FailoverManager::Start() {
    if (running_.load()) {
        logger_->warn("Failover Manager already running");
        return true;
    }
    
    logger_->info("Starting Failover Manager");
    
    running_ = true;
    current_status_ = NodeStatus::HEALTHY;
    
    // 启动心跳线程
    heartbeat_thread_ = std::thread(&FailoverManager::HeartbeatThread, this);
    
    // 启动故障检测线程
    failure_detection_thread_ = std::thread(&FailoverManager::FailureDetectionThread, this);
    
    // 如果是第一个启动的节点，自动成为主节点
    if (cluster_nodes_.size() == 1) {
        current_role_ = NodeRole::PRIMARY;
        logger_->info("Node {} became PRIMARY (first node in cluster)", config_.local_node_id);
        NotifyRoleChange(NodeRole::UNKNOWN, NodeRole::PRIMARY);
    } else {
        current_role_ = NodeRole::SECONDARY;
        logger_->info("Node {} started as SECONDARY", config_.local_node_id);
    }
    
    logger_->info("Failover Manager started successfully");
    return true;
}

void FailoverManager::Stop() {
    if (!running_.load()) {
        return;
    }
    
    logger_->info("Stopping Failover Manager");
    
    running_ = false;
    current_status_ = NodeStatus::DISCONNECTED;
    
    // 等待线程结束
    if (heartbeat_thread_.joinable()) {
        heartbeat_thread_.join();
    }
    
    if (failure_detection_thread_.joinable()) {
        failure_detection_thread_.join();
    }
    
    logger_->info("Failover Manager stopped");
}

std::vector<NodeInfo> FailoverManager::GetClusterStatus() const {
    std::lock_guard<std::mutex> lock(nodes_mutex_);
    std::vector<NodeInfo> status;
    status.reserve(cluster_nodes_.size());
    
    for (const auto& [node_id, info] : cluster_nodes_) {
        status.push_back(info);
    }
    
    return status;
}

bool FailoverManager::TriggerFailover(const std::string& reason) {
    if (failover_in_progress_.load()) {
        logger_->warn("Failover already in progress");
        return false;
    }
    
    logger_->info("Manual failover triggered: {}", reason);
    return ExecuteFailover(reason);
}

void FailoverManager::UpdateNodeHealth(const std::string& node_id, NodeStatus status) {
    std::lock_guard<std::mutex> lock(nodes_mutex_);
    auto it = cluster_nodes_.find(node_id);
    if (it != cluster_nodes_.end()) {
        it->second.status = status;
        it->second.last_heartbeat = std::chrono::steady_clock::now();
        logger_->debug("Updated node {} health status to {}", node_id, static_cast<int>(status));
    }
}

NodeInfo FailoverManager::GetPrimaryNode() const {
    std::lock_guard<std::mutex> lock(nodes_mutex_);
    for (const auto& [node_id, info] : cluster_nodes_) {
        if (info.role == NodeRole::PRIMARY) {
            return info;
        }
    }
    return NodeInfo{};  // 返回空的NodeInfo表示没有找到主节点
}

void FailoverManager::HeartbeatThread() {
    logger_->info("Heartbeat thread started");
    
    while (running_.load()) {
        try {
            SendHeartbeat();
            std::this_thread::sleep_for(std::chrono::milliseconds(config_.heartbeat_interval_ms));
        } catch (const std::exception& e) {
            logger_->error("Error in heartbeat thread: {}", e.what());
        }
    }
    
    logger_->info("Heartbeat thread stopped");
}

void FailoverManager::FailureDetectionThread() {
    logger_->info("Failure detection thread started");
    
    while (running_.load()) {
        try {
            auto now = std::chrono::steady_clock::now();
            bool primary_failed = false;
            std::string failed_primary_id;
            
            {
                std::lock_guard<std::mutex> lock(nodes_mutex_);
                for (auto& [node_id, info] : cluster_nodes_) {
                    if (node_id == config_.local_node_id) {
                        continue;  // 跳过本地节点
                    }
                    
                    auto time_since_heartbeat = std::chrono::duration_cast<std::chrono::milliseconds>(
                        now - info.last_heartbeat).count();
                    
                    if (time_since_heartbeat > config_.heartbeat_timeout_ms) {
                        if (info.status != NodeStatus::DISCONNECTED) {
                            logger_->warn("Node {} heartbeat timeout ({}ms)", node_id, time_since_heartbeat);
                            info.status = NodeStatus::DISCONNECTED;
                            
                            if (info.role == NodeRole::PRIMARY) {
                                primary_failed = true;
                                failed_primary_id = node_id;
                            }
                        }
                    }
                }
            }
            
            // 如果主节点失败且当前节点是备节点，尝试故障转移
            if (primary_failed && current_role_.load() == NodeRole::SECONDARY) {
                logger_->warn("Primary node {} failed, initiating failover", failed_primary_id);
                ExecuteFailover("Primary node failure detected");
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(config_.heartbeat_interval_ms));
        } catch (const std::exception& e) {
            logger_->error("Error in failure detection thread: {}", e.what());
        }
    }
    
    logger_->info("Failure detection thread stopped");
}

bool FailoverManager::ExecuteFailover(const std::string& reason) {
    if (failover_in_progress_.exchange(true)) {
        logger_->warn("Failover already in progress");
        return false;
    }
    
    auto start_time = std::chrono::steady_clock::now();
    logger_->info("Starting failover process: {}", reason);
    
    try {
        // 检查是否可以执行故障转移
        if (current_role_.load() != NodeRole::SECONDARY) {
            logger_->error("Cannot failover: current role is not SECONDARY");
            failover_in_progress_ = false;
            return false;
        }
        
        // 检查健康状态
        if (health_check_callback_ && !health_check_callback_()) {
            logger_->error("Cannot failover: local node health check failed");
            failover_in_progress_ = false;
            return false;
        }
        
        // 选举新的主节点（在这个简单实现中，当前节点成为主节点）
        std::string new_primary = config_.local_node_id;
        
        // 同步数据
        if (data_sync_callback_) {
            logger_->info("Syncing data before failover");
            bool sync_success = true;
            for (const auto& peer : config_.peer_nodes) {
                if (!data_sync_callback_(peer)) {
                    logger_->warn("Data sync failed for peer: {}", peer);
                    sync_success = false;
                }
            }
            
            if (!sync_success) {
                logger_->warn("Data sync partially failed, but continuing with failover");
            }
        }
        
        // 更新角色
        NodeRole old_role = current_role_.load();
        current_role_ = NodeRole::PRIMARY;
        
        {
            std::lock_guard<std::mutex> lock(nodes_mutex_);
            cluster_nodes_[config_.local_node_id].role = NodeRole::PRIMARY;
        }
        
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start_time).count();
        
        logger_->info("Failover completed successfully in {}ms, node {} is now PRIMARY", 
                     elapsed, config_.local_node_id);
        
        // 通知角色变更
        NotifyRoleChange(old_role, NodeRole::PRIMARY);
        
        last_failover_time_ = std::chrono::steady_clock::now();
        failover_attempts_ = 0;  // 重置失败计数
        
        failover_in_progress_ = false;
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failover failed with exception: {}", e.what());
        failover_in_progress_ = false;
        failover_attempts_++;
        return false;
    }
}

bool FailoverManager::ExecuteFailback() {
    if (!config_.enable_auto_failback) {
        return false;
    }
    
    logger_->info("Starting failback process");
    
    // 检查是否有原主节点恢复
    std::string recovered_primary;
    {
        std::lock_guard<std::mutex> lock(nodes_mutex_);
        for (const auto& [node_id, info] : cluster_nodes_) {
            if (node_id != config_.local_node_id && 
                info.status == NodeStatus::HEALTHY && 
                info.priority > cluster_nodes_[config_.local_node_id].priority) {
                recovered_primary = node_id;
                break;
            }
        }
    }
    
    if (!recovered_primary.empty()) {
        logger_->info("Higher priority node {} recovered, initiating failback", recovered_primary);
        
        // 同步数据到恢复的节点
        if (data_sync_callback_ && !data_sync_callback_(recovered_primary)) {
            logger_->error("Failback data sync failed");
            return false;
        }
        
        // 切换角色
        NodeRole old_role = current_role_.load();
        current_role_ = NodeRole::SECONDARY;
        
        {
            std::lock_guard<std::mutex> lock(nodes_mutex_);
            cluster_nodes_[config_.local_node_id].role = NodeRole::SECONDARY;
            cluster_nodes_[recovered_primary].role = NodeRole::PRIMARY;
        }
        
        logger_->info("Failback completed, node {} is now SECONDARY", config_.local_node_id);
        NotifyRoleChange(old_role, NodeRole::SECONDARY);
        
        return true;
    }
    
    return false;
}

void FailoverManager::SendHeartbeat() {
    // 在实际实现中，这里会通过网络发送心跳消息
    // 现在只是更新本地节点的心跳时间
    std::lock_guard<std::mutex> lock(nodes_mutex_);
    cluster_nodes_[config_.local_node_id].last_heartbeat = std::chrono::steady_clock::now();
    cluster_nodes_[config_.local_node_id].status = current_status_.load();
    cluster_nodes_[config_.local_node_id].role = current_role_.load();
    
    logger_->debug("Heartbeat sent from node {}", config_.local_node_id);
}

void FailoverManager::HandleHeartbeat(const std::string& node_id, const NodeInfo& info) {
    std::lock_guard<std::mutex> lock(nodes_mutex_);
    auto it = cluster_nodes_.find(node_id);
    if (it != cluster_nodes_.end()) {
        it->second.status = info.status;
        it->second.role = info.role;
        it->second.last_heartbeat = std::chrono::steady_clock::now();
        logger_->debug("Received heartbeat from node {}", node_id);
    }
}

bool FailoverManager::CheckNodeHealth(const std::string& node_id) {
    // 在实际实现中，这里会执行具体的健康检查
    // 现在只是检查心跳超时
    std::lock_guard<std::mutex> lock(nodes_mutex_);
    auto it = cluster_nodes_.find(node_id);
    if (it == cluster_nodes_.end()) {
        return false;
    }
    
    auto now = std::chrono::steady_clock::now();
    auto time_since_heartbeat = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - it->second.last_heartbeat).count();
    
    return time_since_heartbeat <= config_.heartbeat_timeout_ms;
}

std::string FailoverManager::ElectNewPrimary() {
    std::lock_guard<std::mutex> lock(nodes_mutex_);
    
    std::string best_candidate;
    int highest_priority = -1;
    
    for (const auto& [node_id, info] : cluster_nodes_) {
        if (info.status == NodeStatus::HEALTHY && info.priority > highest_priority) {
            best_candidate = node_id;
            highest_priority = info.priority;
        }
    }
    
    return best_candidate;
}

bool FailoverManager::SyncDataToPeers() {
    if (!data_sync_callback_) {
        return true;  // 没有同步回调，认为成功
    }
    
    bool all_success = true;
    for (const auto& peer : config_.peer_nodes) {
        if (!data_sync_callback_(peer)) {
            logger_->error("Data sync failed for peer: {}", peer);
            all_success = false;
        }
    }
    
    return all_success;
}

void FailoverManager::NotifyRoleChange(NodeRole old_role, NodeRole new_role) {
    if (failover_callback_) {
        try {
            failover_callback_(old_role, new_role);
        } catch (const std::exception& e) {
            logger_->error("Error in failover callback: {}", e.what());
        }
    }
}

} // namespace financial_data