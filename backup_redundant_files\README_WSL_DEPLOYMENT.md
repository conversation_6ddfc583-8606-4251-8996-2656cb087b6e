# WSL环境部署说明

## 🚀 快速开始

### Windows用户 (推荐)

1. **双击运行批处理文件**:
   ```
   deploy_wsl.bat
   ```

2. **选择部署选项**:
   - 选择 `1` - 快速测试环境
   - 选择 `2` - 完整部署服务

### Linux/WSL用户

1. **运行快速测试**:
   ```bash
   python3 quick_test_wsl.py
   ```

2. **完整部署**:
   ```bash
   chmod +x deploy_wsl_test.sh
   ./deploy_wsl_test.sh
   ```

## 📋 部署内容

### 自动安装的组件

- ✅ Python 3.8+ 环境
- ✅ Redis 数据缓存服务
- ✅ ClickHouse 时序数据库 (Docker)
- ✅ 金融数据采集器 (PyTDX)
- ✅ 任务调度器服务
- ✅ 监控和日志系统

### 创建的目录结构

```
financial-data-service/
├── venv/                   # Python虚拟环境
├── logs/                   # 日志文件
├── config/                 # 配置文件
│   └── wsl_test_config.json
├── data/                   # 数据存储
├── deploy_wsl_test.sh      # Linux部署脚本
├── deploy_wsl.bat          # Windows批处理
├── quick_test_wsl.py       # 快速测试脚本
└── docker-compose.wsl.yml  # Docker配置
```

## 🔧 服务管理

### 使用批处理文件 (Windows)

```batch
deploy_wsl.bat
```

选择对应的操作：
- `1` - 快速测试
- `2` - 完整部署  
- `3` - 查看状态
- `4` - 停止服务
- `5` - 重启服务
- `6` - 进入WSL终端

### 使用命令行 (Linux/WSL)

```bash
# 查看服务状态
./deploy_wsl_test.sh --status

# 停止所有服务
./deploy_wsl_test.sh --stop

# 重启所有服务
./deploy_wsl_test.sh --restart

# 查看帮助
./deploy_wsl_test.sh --help
```

## 📊 服务访问

| 服务 | 地址 | 用途 |
|------|------|------|
| Redis | `localhost:6379` | 数据缓存 |
| ClickHouse | `http://localhost:8123` | 数据库Web界面 |
| ClickHouse Native | `localhost:9000` | 数据库原生连接 |
| 监控面板 | `http://localhost:9090` | Prometheus监控 |

## 🧪 功能测试

### 1. 环境测试
```bash
python3 quick_test_wsl.py
```

### 2. 数据采集测试
```bash
source venv/bin/activate
python3 test_enhanced_features.py
```

### 3. 调度器测试
```bash
source venv/bin/activate
python3 start_enhanced_scheduler.py
```

## 📝 日志查看

### 实时日志
```bash
# 调度器日志
tail -f logs/scheduler_service.log

# WSL测试日志
tail -f logs/wsl_test.log

# Docker服务日志
docker-compose -f docker-compose.wsl.yml logs -f
```

### 日志位置
- 调度器日志: `logs/scheduler_service.log`
- 测试日志: `logs/wsl_test.log`
- 系统日志: `/var/log/syslog`

## ⚠️ 故障排除

### 常见问题

1. **WSL未安装**
   ```
   错误: WSL未安装或不可用
   解决: 安装WSL2 - https://docs.microsoft.com/en-us/windows/wsl/install
   ```

2. **Python模块缺失**
   ```bash
   # 重新安装依赖
   source venv/bin/activate
   pip install --upgrade -r requirements.txt
   ```

3. **Redis连接失败**
   ```bash
   # 启动Redis服务
   sudo service redis-server start
   
   # 测试连接
   redis-cli ping
   ```

4. **Docker权限问题**
   ```bash
   # 添加用户到docker组
   sudo usermod -aG docker $USER
   newgrp docker
   ```

5. **端口占用**
   ```bash
   # 查看端口占用
   netstat -tlnp | grep :6379
   
   # 释放端口
   sudo fuser -k 6379/tcp
   ```

### 性能优化

1. **WSL内存限制**
   
   在Windows中创建 `%USERPROFILE%\.wslconfig`:
   ```ini
   [wsl2]
   memory=4GB
   processors=2
   swap=2GB
   ```

2. **Redis配置优化**
   ```bash
   # 编辑配置文件
   sudo nano /etc/redis/redis.conf
   
   # 添加内存限制
   maxmemory 1gb
   maxmemory-policy allkeys-lru
   ```

## 🔄 更新和维护

### 更新代码
```bash
git pull origin main
source venv/bin/activate
pip install --upgrade -r requirements.txt
./deploy_wsl_test.sh --restart
```

### 清理数据
```bash
# 清理日志
rm -rf logs/*

# 清理Docker数据
docker-compose -f docker-compose.wsl.yml down -v

# 重建虚拟环境
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

## 📈 监控和告警

### 系统监控
```bash
# CPU和内存使用
htop

# 磁盘使用
df -h

# 网络连接
netstat -tlnp
```

### 服务监控
```bash
# 检查服务状态
./deploy_wsl_test.sh --status

# 查看进程
ps aux | grep python

# 检查端口
lsof -i :6379
```

## 🎯 下一步

部署成功后，你可以：

1. **开发自定义数据采集器**
2. **配置更多数据源**
3. **设置定时任务**
4. **集成监控告警**
5. **扩展到生产环境**

详细的开发指南请参考项目主README文件。

---

**支持**: 如遇问题，请查看日志文件或提交Issue。