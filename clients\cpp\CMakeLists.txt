cmake_minimum_required(VERSION 3.16)
project(FinancialDataSDK VERSION 1.0.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(Protobuf REQUIRED)
find_package(gRPC REQUIRED)
find_package(Threads REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../src/proto)

# Source files
set(SDK_SOURCES
    src/market_data_client.cpp
    src/connection_pool.cpp
    src/async_client.cpp
    src/error_handler.cpp
    src/subscription_manager.cpp
    src/data_callback_manager.cpp
    ../../src/proto/market_data_service.grpc.pb.cc
    ../../src/proto/market_data_service.pb.cc
    ../../src/proto/market_data.pb.cc
)

# Create static library
add_library(financial_data_sdk STATIC ${SDK_SOURCES})

# Link libraries
target_link_libraries(financial_data_sdk
    gRPC::grpc++
    protobuf::libprotobuf
    Threads::Threads
)

# Set compile options for performance
target_compile_options(financial_data_sdk PRIVATE
    -O3
    -march=native
    -DNDEBUG
    -ffast-math
    -funroll-loops
)

# Install headers
install(DIRECTORY include/ DESTINATION include)

# Install library
install(TARGETS financial_data_sdk
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Examples
add_subdirectory(examples)

# Tests
enable_testing()
add_subdirectory(tests)