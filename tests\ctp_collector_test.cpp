#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <thread>
#include <chrono>

#include "collectors/ctp_collector.h"

using namespace financial_data;
using namespace testing;

class CTPCollectorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建flow目录
        system("mkdir flow 2>nul");
        
        // 设置测试配置
        config_.front_address = "tcp://***************:10131";
        config_.broker_id = "9999";
        config_.user_id = "test_user";
        config_.password = "test_password";
        config_.flow_path = "./flow/";
        config_.heartbeat_interval = 30;
        config_.reconnect_interval = 5;
        config_.max_reconnect_attempts = 3;
        config_.enable_level2 = true;
    }
    
    void TearDown() override {
        if (collector_) {
            collector_->Shutdown();
        }
    }
    
    CTPConfig config_;
    std::unique_ptr<CTPMarketDataCollector> collector_;
};

TEST_F(CTPCollectorTest, ConfigValidation) {
    // 测试有效配置
    EXPECT_TRUE(config_.IsValid());
    
    // 测试无效配置
    CTPConfig invalid_config;
    EXPECT_FALSE(invalid_config.IsValid());
    
    // 测试部分无效配置
    invalid_config = config_;
    invalid_config.front_address = "";
    EXPECT_FALSE(invalid_config.IsValid());
}

TEST_F(CTPCollectorTest, Initialization) {
    collector_ = std::make_unique<CTPMarketDataCollector>();
    
    // 测试初始化
    EXPECT_TRUE(collector_->Initialize(config_));
    
    // 测试初始状态
    EXPECT_EQ(collector_->GetConnectionStatus(), ConnectionStatus::DISCONNECTED);
    EXPECT_FALSE(collector_->IsConnected());
    
    // 测试统计信息
    auto stats = collector_->GetStatistics();
    EXPECT_EQ(stats.total_received, 0);
    EXPECT_EQ(stats.total_processed, 0);
    EXPECT_EQ(stats.total_errors, 0);
}

TEST_F(CTPCollectorTest, SubscriptionManagement) {
    collector_ = std::make_unique<CTPMarketDataCollector>();
    ASSERT_TRUE(collector_->Initialize(config_));
    
    // 测试未连接时的订阅
    EXPECT_FALSE(collector_->Subscribe("CU2409"));
    
    // 测试订阅列表管理
    EXPECT_TRUE(collector_->GetSubscribedSymbols().empty());
    
    // 测试清空订阅
    collector_->ClearSubscriptions();
    EXPECT_TRUE(collector_->GetSubscribedSymbols().empty());
}

TEST_F(CTPCollectorTest, CallbackSetting) {
    collector_ = std::make_unique<CTPMarketDataCollector>();
    ASSERT_TRUE(collector_->Initialize(config_));
    
    bool data_callback_called = false;
    bool status_callback_called = false;
    
    // 设置数据回调
    collector_->SetDataCallback([&](const MarketDataWrapper& data) {
        data_callback_called = true;
    });
    
    // 设置状态回调
    collector_->SetStatusCallback([&](ConnectionStatus status, const std::string& message) {
        status_callback_called = true;
    });
    
    // 回调设置不应该抛出异常
    EXPECT_NO_THROW(collector_->SetDataCallback(nullptr));
    EXPECT_NO_THROW(collector_->SetStatusCallback(nullptr));
}

TEST_F(CTPCollectorTest, LifecycleManagement) {
    collector_ = std::make_unique<CTPMarketDataCollector>();
    ASSERT_TRUE(collector_->Initialize(config_));
    
    // 测试启动和停止
    EXPECT_NO_THROW(collector_->Start());
    EXPECT_NO_THROW(collector_->Stop());
    
    // 测试重复启动和停止
    EXPECT_NO_THROW(collector_->Start());
    EXPECT_NO_THROW(collector_->Start()); // 应该忽略重复启动
    EXPECT_NO_THROW(collector_->Stop());
    EXPECT_NO_THROW(collector_->Stop());  // 应该忽略重复停止
}

TEST_F(CTPCollectorTest, StatisticsReset) {
    collector_ = std::make_unique<CTPMarketDataCollector>();
    ASSERT_TRUE(collector_->Initialize(config_));
    
    // 重置统计信息
    collector_->ResetStatistics();
    
    auto stats = collector_->GetStatistics();
    EXPECT_EQ(stats.total_received, 0);
    EXPECT_EQ(stats.total_processed, 0);
    EXPECT_EQ(stats.total_errors, 0);
}

TEST_F(CTPCollectorTest, HealthCheck) {
    collector_ = std::make_unique<CTPMarketDataCollector>();
    ASSERT_TRUE(collector_->Initialize(config_));
    
    // 未连接时应该不健康
    EXPECT_FALSE(collector_->IsHealthy());
    
    // 获取健康状态字符串
    std::string health_status = collector_->GetHealthStatus();
    EXPECT_FALSE(health_status.empty());
    EXPECT_THAT(health_status, HasSubstr("Status"));
}

TEST_F(CTPCollectorTest, SymbolValidation) {
    collector_ = std::make_unique<CTPMarketDataCollector>();
    ASSERT_TRUE(collector_->Initialize(config_));
    
    // 这些测试依赖于ctp_utils命名空间中的函数
    // 在实际实现中，这些函数应该正确验证CTP合约代码格式
}

TEST_F(CTPCollectorTest, ConfigFromFile) {
    // 创建临时配置文件
    std::string config_content = R"({
        "ctp": {
            "front_address": "tcp://test.server.com:10131",
            "broker_id": "test_broker",
            "user_id": "test_user",
            "password": "test_password",
            "flow_path": "./test_flow/",
            "heartbeat_interval": 60,
            "reconnect_interval": 10,
            "max_reconnect_attempts": 5,
            "enable_level2": false
        }
    })";
    
    std::ofstream config_file("test_config.json");
    config_file << config_content;
    config_file.close();
    
    // 从文件加载配置
    auto loaded_config = CTPConfig::LoadFromFile("test_config.json");
    
    EXPECT_EQ(loaded_config.front_address, "tcp://test.server.com:10131");
    EXPECT_EQ(loaded_config.broker_id, "test_broker");
    EXPECT_EQ(loaded_config.user_id, "test_user");
    EXPECT_EQ(loaded_config.password, "test_password");
    EXPECT_EQ(loaded_config.flow_path, "./test_flow/");
    EXPECT_EQ(loaded_config.heartbeat_interval, 60);
    EXPECT_EQ(loaded_config.reconnect_interval, 10);
    EXPECT_EQ(loaded_config.max_reconnect_attempts, 5);
    EXPECT_FALSE(loaded_config.enable_level2);
    
    // 清理
    std::remove("test_config.json");
}

TEST_F(CTPCollectorTest, FactoryCreation) {
    // 测试工厂方法创建
    auto collector = CTPCollectorFactory::Create(config_);
    EXPECT_NE(collector, nullptr);
    
    // 测试环境验证
    EXPECT_TRUE(CTPCollectorFactory::ValidateCTPEnvironment());
    
    // 测试版本获取
    std::string version = CTPCollectorFactory::GetCTPVersion();
    EXPECT_FALSE(version.empty());
}

// 性能测试（可选）
TEST_F(CTPCollectorTest, DISABLED_PerformanceTest) {
    collector_ = std::make_unique<CTPMarketDataCollector>();
    ASSERT_TRUE(collector_->Initialize(config_));
    
    int callback_count = 0;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    collector_->SetDataCallback([&](const MarketDataWrapper& data) {
        callback_count++;
    });
    
    collector_->Start();
    
    // 模拟数据处理
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    collector_->Stop();
    
    std::cout << "Processed " << callback_count << " callbacks in " 
              << duration.count() << "ms" << std::endl;
}

// 主函数
int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}