// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: market_data.proto
// Protobuf C++ Version: 4.25.3

#ifndef GOOGLE_PROTOBUF_INCLUDED_market_5fdata_2eproto_2epb_2eh
#define GOOGLE_PROTOBUF_INCLUDED_market_5fdata_2eproto_2epb_2eh

#include <limits>
#include <string>
#include <type_traits>
#include <utility>

#include "google/protobuf/port_def.inc"
#if PROTOBUF_VERSION < 4025000
#error "This file was generated by a newer version of protoc which is"
#error "incompatible with your Protocol Buffer headers. Please update"
#error "your headers."
#endif  // PROTOBUF_VERSION

#if 4025003 < PROTOBUF_MIN_PROTOC_VERSION
#error "This file was generated by an older version of protoc which is"
#error "incompatible with your Protocol Buffer headers. Please"
#error "regenerate this file with a newer version of protoc."
#endif  // PROTOBUF_MIN_PROTOC_VERSION
#include "google/protobuf/port_undef.inc"
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/arena.h"
#include "google/protobuf/arenastring.h"
#include "google/protobuf/generated_message_tctable_decl.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/metadata_lite.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/message.h"
#include "google/protobuf/repeated_field.h"  // IWYU pragma: export
#include "google/protobuf/extension_set.h"  // IWYU pragma: export
#include "google/protobuf/unknown_field_set.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"

#define PROTOBUF_INTERNAL_EXPORT_market_5fdata_2eproto

namespace google {
namespace protobuf {
namespace internal {
class AnyMetadata;
}  // namespace internal
}  // namespace protobuf
}  // namespace google

// Internal implementation detail -- do not use these members.
struct TableStruct_market_5fdata_2eproto {
  static const ::uint32_t offsets[];
};
extern const ::google::protobuf::internal::DescriptorTable
    descriptor_table_market_5fdata_2eproto;
namespace financial_data {
class ProtoLevel2Data;
struct ProtoLevel2DataDefaultTypeInternal;
extern ProtoLevel2DataDefaultTypeInternal _ProtoLevel2Data_default_instance_;
class ProtoMarketData;
struct ProtoMarketDataDefaultTypeInternal;
extern ProtoMarketDataDefaultTypeInternal _ProtoMarketData_default_instance_;
class ProtoMarketDataBatch;
struct ProtoMarketDataBatchDefaultTypeInternal;
extern ProtoMarketDataBatchDefaultTypeInternal _ProtoMarketDataBatch_default_instance_;
class ProtoPriceLevel;
struct ProtoPriceLevelDefaultTypeInternal;
extern ProtoPriceLevelDefaultTypeInternal _ProtoPriceLevel_default_instance_;
class ProtoSubscriptionRequest;
struct ProtoSubscriptionRequestDefaultTypeInternal;
extern ProtoSubscriptionRequestDefaultTypeInternal _ProtoSubscriptionRequest_default_instance_;
class ProtoSubscriptionResponse;
struct ProtoSubscriptionResponseDefaultTypeInternal;
extern ProtoSubscriptionResponseDefaultTypeInternal _ProtoSubscriptionResponse_default_instance_;
class ProtoTickData;
struct ProtoTickDataDefaultTypeInternal;
extern ProtoTickDataDefaultTypeInternal _ProtoTickData_default_instance_;
}  // namespace financial_data
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google

namespace financial_data {

// ===================================================================


// -------------------------------------------------------------------

class ProtoTickData final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.ProtoTickData) */ {
 public:
  inline ProtoTickData() : ProtoTickData(nullptr) {}
  ~ProtoTickData() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR ProtoTickData(::google::protobuf::internal::ConstantInitialized);

  inline ProtoTickData(const ProtoTickData& from)
      : ProtoTickData(nullptr, from) {}
  ProtoTickData(ProtoTickData&& from) noexcept
    : ProtoTickData() {
    *this = ::std::move(from);
  }

  inline ProtoTickData& operator=(const ProtoTickData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtoTickData& operator=(ProtoTickData&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtoTickData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtoTickData* internal_default_instance() {
    return reinterpret_cast<const ProtoTickData*>(
               &_ProtoTickData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ProtoTickData& a, ProtoTickData& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtoTickData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtoTickData* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtoTickData* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtoTickData>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProtoTickData& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const ProtoTickData& from) {
    ProtoTickData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(ProtoTickData* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.ProtoTickData";
  }
  protected:
  explicit ProtoTickData(::google::protobuf::Arena* arena);
  ProtoTickData(::google::protobuf::Arena* arena, const ProtoTickData& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSymbolFieldNumber = 2,
    kExchangeFieldNumber = 3,
    kTradeFlagFieldNumber = 9,
    kTimestampNsFieldNumber = 1,
    kLastPriceFieldNumber = 4,
    kVolumeFieldNumber = 5,
    kTurnoverFieldNumber = 6,
    kOpenInterestFieldNumber = 7,
    kSequenceFieldNumber = 8,
  };
  // string symbol = 2;
  void clear_symbol() ;
  const std::string& symbol() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_symbol(Arg_&& arg, Args_... args);
  std::string* mutable_symbol();
  PROTOBUF_NODISCARD std::string* release_symbol();
  void set_allocated_symbol(std::string* value);

  private:
  const std::string& _internal_symbol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_symbol(
      const std::string& value);
  std::string* _internal_mutable_symbol();

  public:
  // string exchange = 3;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // string trade_flag = 9;
  void clear_trade_flag() ;
  const std::string& trade_flag() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_trade_flag(Arg_&& arg, Args_... args);
  std::string* mutable_trade_flag();
  PROTOBUF_NODISCARD std::string* release_trade_flag();
  void set_allocated_trade_flag(std::string* value);

  private:
  const std::string& _internal_trade_flag() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_trade_flag(
      const std::string& value);
  std::string* _internal_mutable_trade_flag();

  public:
  // int64 timestamp_ns = 1;
  void clear_timestamp_ns() ;
  ::int64_t timestamp_ns() const;
  void set_timestamp_ns(::int64_t value);

  private:
  ::int64_t _internal_timestamp_ns() const;
  void _internal_set_timestamp_ns(::int64_t value);

  public:
  // double last_price = 4;
  void clear_last_price() ;
  double last_price() const;
  void set_last_price(double value);

  private:
  double _internal_last_price() const;
  void _internal_set_last_price(double value);

  public:
  // uint64 volume = 5;
  void clear_volume() ;
  ::uint64_t volume() const;
  void set_volume(::uint64_t value);

  private:
  ::uint64_t _internal_volume() const;
  void _internal_set_volume(::uint64_t value);

  public:
  // double turnover = 6;
  void clear_turnover() ;
  double turnover() const;
  void set_turnover(double value);

  private:
  double _internal_turnover() const;
  void _internal_set_turnover(double value);

  public:
  // uint64 open_interest = 7;
  void clear_open_interest() ;
  ::uint64_t open_interest() const;
  void set_open_interest(::uint64_t value);

  private:
  ::uint64_t _internal_open_interest() const;
  void _internal_set_open_interest(::uint64_t value);

  public:
  // uint32 sequence = 8;
  void clear_sequence() ;
  ::uint32_t sequence() const;
  void set_sequence(::uint32_t value);

  private:
  ::uint32_t _internal_sequence() const;
  void _internal_set_sequence(::uint32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.ProtoTickData)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      4, 9, 0,
      69, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::ArenaStringPtr symbol_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    ::google::protobuf::internal::ArenaStringPtr trade_flag_;
    ::int64_t timestamp_ns_;
    double last_price_;
    ::uint64_t volume_;
    double turnover_;
    ::uint64_t open_interest_;
    ::uint32_t sequence_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_2eproto;
};// -------------------------------------------------------------------

class ProtoSubscriptionResponse final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.ProtoSubscriptionResponse) */ {
 public:
  inline ProtoSubscriptionResponse() : ProtoSubscriptionResponse(nullptr) {}
  ~ProtoSubscriptionResponse() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR ProtoSubscriptionResponse(::google::protobuf::internal::ConstantInitialized);

  inline ProtoSubscriptionResponse(const ProtoSubscriptionResponse& from)
      : ProtoSubscriptionResponse(nullptr, from) {}
  ProtoSubscriptionResponse(ProtoSubscriptionResponse&& from) noexcept
    : ProtoSubscriptionResponse() {
    *this = ::std::move(from);
  }

  inline ProtoSubscriptionResponse& operator=(const ProtoSubscriptionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtoSubscriptionResponse& operator=(ProtoSubscriptionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtoSubscriptionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtoSubscriptionResponse* internal_default_instance() {
    return reinterpret_cast<const ProtoSubscriptionResponse*>(
               &_ProtoSubscriptionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ProtoSubscriptionResponse& a, ProtoSubscriptionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtoSubscriptionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtoSubscriptionResponse* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtoSubscriptionResponse* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtoSubscriptionResponse>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProtoSubscriptionResponse& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const ProtoSubscriptionResponse& from) {
    ProtoSubscriptionResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(ProtoSubscriptionResponse* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.ProtoSubscriptionResponse";
  }
  protected:
  explicit ProtoSubscriptionResponse(::google::protobuf::Arena* arena);
  ProtoSubscriptionResponse(::google::protobuf::Arena* arena, const ProtoSubscriptionResponse& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSubscriptionIdFieldNumber = 3,
    kSuccessFieldNumber = 1,
  };
  // string message = 2;
  void clear_message() ;
  const std::string& message() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_message(Arg_&& arg, Args_... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* value);

  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(
      const std::string& value);
  std::string* _internal_mutable_message();

  public:
  // string subscription_id = 3;
  void clear_subscription_id() ;
  const std::string& subscription_id() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_subscription_id(Arg_&& arg, Args_... args);
  std::string* mutable_subscription_id();
  PROTOBUF_NODISCARD std::string* release_subscription_id();
  void set_allocated_subscription_id(std::string* value);

  private:
  const std::string& _internal_subscription_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_subscription_id(
      const std::string& value);
  std::string* _internal_mutable_subscription_id();

  public:
  // bool success = 1;
  void clear_success() ;
  bool success() const;
  void set_success(bool value);

  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.ProtoSubscriptionResponse)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 0,
      71, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::ArenaStringPtr message_;
    ::google::protobuf::internal::ArenaStringPtr subscription_id_;
    bool success_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_2eproto;
};// -------------------------------------------------------------------

class ProtoSubscriptionRequest final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.ProtoSubscriptionRequest) */ {
 public:
  inline ProtoSubscriptionRequest() : ProtoSubscriptionRequest(nullptr) {}
  ~ProtoSubscriptionRequest() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR ProtoSubscriptionRequest(::google::protobuf::internal::ConstantInitialized);

  inline ProtoSubscriptionRequest(const ProtoSubscriptionRequest& from)
      : ProtoSubscriptionRequest(nullptr, from) {}
  ProtoSubscriptionRequest(ProtoSubscriptionRequest&& from) noexcept
    : ProtoSubscriptionRequest() {
    *this = ::std::move(from);
  }

  inline ProtoSubscriptionRequest& operator=(const ProtoSubscriptionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtoSubscriptionRequest& operator=(ProtoSubscriptionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtoSubscriptionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtoSubscriptionRequest* internal_default_instance() {
    return reinterpret_cast<const ProtoSubscriptionRequest*>(
               &_ProtoSubscriptionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ProtoSubscriptionRequest& a, ProtoSubscriptionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtoSubscriptionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtoSubscriptionRequest* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtoSubscriptionRequest* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtoSubscriptionRequest>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProtoSubscriptionRequest& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const ProtoSubscriptionRequest& from) {
    ProtoSubscriptionRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(ProtoSubscriptionRequest* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.ProtoSubscriptionRequest";
  }
  protected:
  explicit ProtoSubscriptionRequest(::google::protobuf::Arena* arena);
  ProtoSubscriptionRequest(::google::protobuf::Arena* arena, const ProtoSubscriptionRequest& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSymbolsFieldNumber = 1,
    kDataTypesFieldNumber = 2,
    kStartTimeNsFieldNumber = 4,
    kIncludeHistoryFieldNumber = 3,
  };
  // repeated string symbols = 1;
  int symbols_size() const;
  private:
  int _internal_symbols_size() const;

  public:
  void clear_symbols() ;
  const std::string& symbols(int index) const;
  std::string* mutable_symbols(int index);
  void set_symbols(int index, const std::string& value);
  void set_symbols(int index, std::string&& value);
  void set_symbols(int index, const char* value);
  void set_symbols(int index, const char* value, std::size_t size);
  void set_symbols(int index, absl::string_view value);
  std::string* add_symbols();
  void add_symbols(const std::string& value);
  void add_symbols(std::string&& value);
  void add_symbols(const char* value);
  void add_symbols(const char* value, std::size_t size);
  void add_symbols(absl::string_view value);
  const ::google::protobuf::RepeatedPtrField<std::string>& symbols() const;
  ::google::protobuf::RepeatedPtrField<std::string>* mutable_symbols();

  private:
  const ::google::protobuf::RepeatedPtrField<std::string>& _internal_symbols() const;
  ::google::protobuf::RepeatedPtrField<std::string>* _internal_mutable_symbols();

  public:
  // repeated string data_types = 2;
  int data_types_size() const;
  private:
  int _internal_data_types_size() const;

  public:
  void clear_data_types() ;
  const std::string& data_types(int index) const;
  std::string* mutable_data_types(int index);
  void set_data_types(int index, const std::string& value);
  void set_data_types(int index, std::string&& value);
  void set_data_types(int index, const char* value);
  void set_data_types(int index, const char* value, std::size_t size);
  void set_data_types(int index, absl::string_view value);
  std::string* add_data_types();
  void add_data_types(const std::string& value);
  void add_data_types(std::string&& value);
  void add_data_types(const char* value);
  void add_data_types(const char* value, std::size_t size);
  void add_data_types(absl::string_view value);
  const ::google::protobuf::RepeatedPtrField<std::string>& data_types() const;
  ::google::protobuf::RepeatedPtrField<std::string>* mutable_data_types();

  private:
  const ::google::protobuf::RepeatedPtrField<std::string>& _internal_data_types() const;
  ::google::protobuf::RepeatedPtrField<std::string>* _internal_mutable_data_types();

  public:
  // int64 start_time_ns = 4;
  void clear_start_time_ns() ;
  ::int64_t start_time_ns() const;
  void set_start_time_ns(::int64_t value);

  private:
  ::int64_t _internal_start_time_ns() const;
  void _internal_set_start_time_ns(::int64_t value);

  public:
  // bool include_history = 3;
  void clear_include_history() ;
  bool include_history() const;
  void set_include_history(bool value);

  private:
  bool _internal_include_history() const;
  void _internal_set_include_history(bool value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.ProtoSubscriptionRequest)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 0,
      65, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::RepeatedPtrField<std::string> symbols_;
    ::google::protobuf::RepeatedPtrField<std::string> data_types_;
    ::int64_t start_time_ns_;
    bool include_history_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_2eproto;
};// -------------------------------------------------------------------

class ProtoPriceLevel final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.ProtoPriceLevel) */ {
 public:
  inline ProtoPriceLevel() : ProtoPriceLevel(nullptr) {}
  ~ProtoPriceLevel() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR ProtoPriceLevel(::google::protobuf::internal::ConstantInitialized);

  inline ProtoPriceLevel(const ProtoPriceLevel& from)
      : ProtoPriceLevel(nullptr, from) {}
  ProtoPriceLevel(ProtoPriceLevel&& from) noexcept
    : ProtoPriceLevel() {
    *this = ::std::move(from);
  }

  inline ProtoPriceLevel& operator=(const ProtoPriceLevel& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtoPriceLevel& operator=(ProtoPriceLevel&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtoPriceLevel& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtoPriceLevel* internal_default_instance() {
    return reinterpret_cast<const ProtoPriceLevel*>(
               &_ProtoPriceLevel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ProtoPriceLevel& a, ProtoPriceLevel& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtoPriceLevel* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtoPriceLevel* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtoPriceLevel* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtoPriceLevel>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProtoPriceLevel& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const ProtoPriceLevel& from) {
    ProtoPriceLevel::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(ProtoPriceLevel* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.ProtoPriceLevel";
  }
  protected:
  explicit ProtoPriceLevel(::google::protobuf::Arena* arena);
  ProtoPriceLevel(::google::protobuf::Arena* arena, const ProtoPriceLevel& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPriceFieldNumber = 1,
    kVolumeFieldNumber = 2,
    kOrderCountFieldNumber = 3,
  };
  // double price = 1;
  void clear_price() ;
  double price() const;
  void set_price(double value);

  private:
  double _internal_price() const;
  void _internal_set_price(double value);

  public:
  // uint32 volume = 2;
  void clear_volume() ;
  ::uint32_t volume() const;
  void set_volume(::uint32_t value);

  private:
  ::uint32_t _internal_volume() const;
  void _internal_set_volume(::uint32_t value);

  public:
  // uint32 order_count = 3;
  void clear_order_count() ;
  ::uint32_t order_count() const;
  void set_order_count(::uint32_t value);

  private:
  ::uint32_t _internal_order_count() const;
  void _internal_set_order_count(::uint32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.ProtoPriceLevel)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 0,
      0, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    double price_;
    ::uint32_t volume_;
    ::uint32_t order_count_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_2eproto;
};// -------------------------------------------------------------------

class ProtoLevel2Data final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.ProtoLevel2Data) */ {
 public:
  inline ProtoLevel2Data() : ProtoLevel2Data(nullptr) {}
  ~ProtoLevel2Data() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR ProtoLevel2Data(::google::protobuf::internal::ConstantInitialized);

  inline ProtoLevel2Data(const ProtoLevel2Data& from)
      : ProtoLevel2Data(nullptr, from) {}
  ProtoLevel2Data(ProtoLevel2Data&& from) noexcept
    : ProtoLevel2Data() {
    *this = ::std::move(from);
  }

  inline ProtoLevel2Data& operator=(const ProtoLevel2Data& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtoLevel2Data& operator=(ProtoLevel2Data&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtoLevel2Data& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtoLevel2Data* internal_default_instance() {
    return reinterpret_cast<const ProtoLevel2Data*>(
               &_ProtoLevel2Data_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ProtoLevel2Data& a, ProtoLevel2Data& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtoLevel2Data* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtoLevel2Data* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtoLevel2Data* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtoLevel2Data>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProtoLevel2Data& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const ProtoLevel2Data& from) {
    ProtoLevel2Data::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(ProtoLevel2Data* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.ProtoLevel2Data";
  }
  protected:
  explicit ProtoLevel2Data(::google::protobuf::Arena* arena);
  ProtoLevel2Data(::google::protobuf::Arena* arena, const ProtoLevel2Data& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBidsFieldNumber = 4,
    kAsksFieldNumber = 5,
    kSymbolFieldNumber = 2,
    kExchangeFieldNumber = 3,
    kTimestampNsFieldNumber = 1,
    kSequenceFieldNumber = 6,
  };
  // repeated .financial_data.ProtoPriceLevel bids = 4;
  int bids_size() const;
  private:
  int _internal_bids_size() const;

  public:
  void clear_bids() ;
  ::financial_data::ProtoPriceLevel* mutable_bids(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoPriceLevel >*
      mutable_bids();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>& _internal_bids() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>* _internal_mutable_bids();
  public:
  const ::financial_data::ProtoPriceLevel& bids(int index) const;
  ::financial_data::ProtoPriceLevel* add_bids();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoPriceLevel >&
      bids() const;
  // repeated .financial_data.ProtoPriceLevel asks = 5;
  int asks_size() const;
  private:
  int _internal_asks_size() const;

  public:
  void clear_asks() ;
  ::financial_data::ProtoPriceLevel* mutable_asks(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoPriceLevel >*
      mutable_asks();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>& _internal_asks() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>* _internal_mutable_asks();
  public:
  const ::financial_data::ProtoPriceLevel& asks(int index) const;
  ::financial_data::ProtoPriceLevel* add_asks();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoPriceLevel >&
      asks() const;
  // string symbol = 2;
  void clear_symbol() ;
  const std::string& symbol() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_symbol(Arg_&& arg, Args_... args);
  std::string* mutable_symbol();
  PROTOBUF_NODISCARD std::string* release_symbol();
  void set_allocated_symbol(std::string* value);

  private:
  const std::string& _internal_symbol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_symbol(
      const std::string& value);
  std::string* _internal_mutable_symbol();

  public:
  // string exchange = 3;
  void clear_exchange() ;
  const std::string& exchange() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_exchange(Arg_&& arg, Args_... args);
  std::string* mutable_exchange();
  PROTOBUF_NODISCARD std::string* release_exchange();
  void set_allocated_exchange(std::string* value);

  private:
  const std::string& _internal_exchange() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchange(
      const std::string& value);
  std::string* _internal_mutable_exchange();

  public:
  // int64 timestamp_ns = 1;
  void clear_timestamp_ns() ;
  ::int64_t timestamp_ns() const;
  void set_timestamp_ns(::int64_t value);

  private:
  ::int64_t _internal_timestamp_ns() const;
  void _internal_set_timestamp_ns(::int64_t value);

  public:
  // uint32 sequence = 6;
  void clear_sequence() ;
  ::uint32_t sequence() const;
  void set_sequence(::uint32_t value);

  private:
  ::uint32_t _internal_sequence() const;
  void _internal_set_sequence(::uint32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.ProtoLevel2Data)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 6, 2,
      53, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoPriceLevel > bids_;
    ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoPriceLevel > asks_;
    ::google::protobuf::internal::ArenaStringPtr symbol_;
    ::google::protobuf::internal::ArenaStringPtr exchange_;
    ::int64_t timestamp_ns_;
    ::uint32_t sequence_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_2eproto;
};// -------------------------------------------------------------------

class ProtoMarketData final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.ProtoMarketData) */ {
 public:
  inline ProtoMarketData() : ProtoMarketData(nullptr) {}
  ~ProtoMarketData() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR ProtoMarketData(::google::protobuf::internal::ConstantInitialized);

  inline ProtoMarketData(const ProtoMarketData& from)
      : ProtoMarketData(nullptr, from) {}
  ProtoMarketData(ProtoMarketData&& from) noexcept
    : ProtoMarketData() {
    *this = ::std::move(from);
  }

  inline ProtoMarketData& operator=(const ProtoMarketData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtoMarketData& operator=(ProtoMarketData&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtoMarketData& default_instance() {
    return *internal_default_instance();
  }
  enum DataTypeCase {
    kTick = 1,
    kLevel2 = 2,
    DATA_TYPE_NOT_SET = 0,
  };

  static inline const ProtoMarketData* internal_default_instance() {
    return reinterpret_cast<const ProtoMarketData*>(
               &_ProtoMarketData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ProtoMarketData& a, ProtoMarketData& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtoMarketData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtoMarketData* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtoMarketData* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtoMarketData>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProtoMarketData& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const ProtoMarketData& from) {
    ProtoMarketData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(ProtoMarketData* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.ProtoMarketData";
  }
  protected:
  explicit ProtoMarketData(::google::protobuf::Arena* arena);
  ProtoMarketData(::google::protobuf::Arena* arena, const ProtoMarketData& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceFieldNumber = 4,
    kReceiveTimeNsFieldNumber = 3,
    kTickFieldNumber = 1,
    kLevel2FieldNumber = 2,
  };
  // string source = 4;
  void clear_source() ;
  const std::string& source() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_source(Arg_&& arg, Args_... args);
  std::string* mutable_source();
  PROTOBUF_NODISCARD std::string* release_source();
  void set_allocated_source(std::string* value);

  private:
  const std::string& _internal_source() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_source(
      const std::string& value);
  std::string* _internal_mutable_source();

  public:
  // int64 receive_time_ns = 3;
  void clear_receive_time_ns() ;
  ::int64_t receive_time_ns() const;
  void set_receive_time_ns(::int64_t value);

  private:
  ::int64_t _internal_receive_time_ns() const;
  void _internal_set_receive_time_ns(::int64_t value);

  public:
  // .financial_data.ProtoTickData tick = 1;
  bool has_tick() const;
  private:
  bool _internal_has_tick() const;

  public:
  void clear_tick() ;
  const ::financial_data::ProtoTickData& tick() const;
  PROTOBUF_NODISCARD ::financial_data::ProtoTickData* release_tick();
  ::financial_data::ProtoTickData* mutable_tick();
  void set_allocated_tick(::financial_data::ProtoTickData* value);
  void unsafe_arena_set_allocated_tick(::financial_data::ProtoTickData* value);
  ::financial_data::ProtoTickData* unsafe_arena_release_tick();

  private:
  const ::financial_data::ProtoTickData& _internal_tick() const;
  ::financial_data::ProtoTickData* _internal_mutable_tick();

  public:
  // .financial_data.ProtoLevel2Data level2 = 2;
  bool has_level2() const;
  private:
  bool _internal_has_level2() const;

  public:
  void clear_level2() ;
  const ::financial_data::ProtoLevel2Data& level2() const;
  PROTOBUF_NODISCARD ::financial_data::ProtoLevel2Data* release_level2();
  ::financial_data::ProtoLevel2Data* mutable_level2();
  void set_allocated_level2(::financial_data::ProtoLevel2Data* value);
  void unsafe_arena_set_allocated_level2(::financial_data::ProtoLevel2Data* value);
  ::financial_data::ProtoLevel2Data* unsafe_arena_release_level2();

  private:
  const ::financial_data::ProtoLevel2Data& _internal_level2() const;
  ::financial_data::ProtoLevel2Data* _internal_mutable_level2();

  public:
  void clear_data_type();
  DataTypeCase data_type_case() const;
  // @@protoc_insertion_point(class_scope:financial_data.ProtoMarketData)
 private:
  class _Internal;
  void set_has_tick();
  void set_has_level2();

  inline bool has_data_type() const;
  inline void clear_has_data_type();

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 4, 2,
      45, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::internal::ArenaStringPtr source_;
    ::int64_t receive_time_ns_;
    union DataTypeUnion {
      constexpr DataTypeUnion() : _constinit_{} {}
        ::google::protobuf::internal::ConstantInitialized _constinit_;
      ::financial_data::ProtoTickData* tick_;
      ::financial_data::ProtoLevel2Data* level2_;
    } data_type_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    ::uint32_t _oneof_case_[1];

    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_2eproto;
};// -------------------------------------------------------------------

class ProtoMarketDataBatch final :
    public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:financial_data.ProtoMarketDataBatch) */ {
 public:
  inline ProtoMarketDataBatch() : ProtoMarketDataBatch(nullptr) {}
  ~ProtoMarketDataBatch() override;
  template<typename = void>
  explicit PROTOBUF_CONSTEXPR ProtoMarketDataBatch(::google::protobuf::internal::ConstantInitialized);

  inline ProtoMarketDataBatch(const ProtoMarketDataBatch& from)
      : ProtoMarketDataBatch(nullptr, from) {}
  ProtoMarketDataBatch(ProtoMarketDataBatch&& from) noexcept
    : ProtoMarketDataBatch() {
    *this = ::std::move(from);
  }

  inline ProtoMarketDataBatch& operator=(const ProtoMarketDataBatch& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtoMarketDataBatch& operator=(ProtoMarketDataBatch&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtoMarketDataBatch& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtoMarketDataBatch* internal_default_instance() {
    return reinterpret_cast<const ProtoMarketDataBatch*>(
               &_ProtoMarketDataBatch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ProtoMarketDataBatch& a, ProtoMarketDataBatch& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtoMarketDataBatch* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr &&
        GetArena() == other->GetArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtoMarketDataBatch* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtoMarketDataBatch* New(::google::protobuf::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtoMarketDataBatch>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProtoMarketDataBatch& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom( const ProtoMarketDataBatch& from) {
    ProtoMarketDataBatch::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  ::size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::google::protobuf::internal::ParseContext* ctx) final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target, ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  ::google::protobuf::internal::CachedSize* AccessCachedSize() const final;
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(ProtoMarketDataBatch* other);

  private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() {
    return "financial_data.ProtoMarketDataBatch";
  }
  protected:
  explicit ProtoMarketDataBatch(::google::protobuf::Arena* arena);
  ProtoMarketDataBatch(::google::protobuf::Arena* arena, const ProtoMarketDataBatch& from);
  public:

  static const ClassData _class_data_;
  const ::google::protobuf::Message::ClassData*GetClassData() const final;

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kBatchTimestampNsFieldNumber = 2,
    kBatchSequenceFieldNumber = 3,
  };
  // repeated .financial_data.ProtoMarketData data = 1;
  int data_size() const;
  private:
  int _internal_data_size() const;

  public:
  void clear_data() ;
  ::financial_data::ProtoMarketData* mutable_data(int index);
  ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoMarketData >*
      mutable_data();
  private:
  const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoMarketData>& _internal_data() const;
  ::google::protobuf::RepeatedPtrField<::financial_data::ProtoMarketData>* _internal_mutable_data();
  public:
  const ::financial_data::ProtoMarketData& data(int index) const;
  ::financial_data::ProtoMarketData* add_data();
  const ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoMarketData >&
      data() const;
  // int64 batch_timestamp_ns = 2;
  void clear_batch_timestamp_ns() ;
  ::int64_t batch_timestamp_ns() const;
  void set_batch_timestamp_ns(::int64_t value);

  private:
  ::int64_t _internal_batch_timestamp_ns() const;
  void _internal_set_batch_timestamp_ns(::int64_t value);

  public:
  // uint32 batch_sequence = 3;
  void clear_batch_sequence() ;
  ::uint32_t batch_sequence() const;
  void set_batch_sequence(::uint32_t value);

  private:
  ::uint32_t _internal_batch_sequence() const;
  void _internal_set_batch_sequence(::uint32_t value);

  public:
  // @@protoc_insertion_point(class_scope:financial_data.ProtoMarketDataBatch)
 private:
  class _Internal;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 1,
      0, 2>
      _table_;
  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {

        inline explicit constexpr Impl_(
            ::google::protobuf::internal::ConstantInitialized) noexcept;
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena);
        inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                              ::google::protobuf::Arena* arena, const Impl_& from);
    ::google::protobuf::RepeatedPtrField< ::financial_data::ProtoMarketData > data_;
    ::int64_t batch_timestamp_ns_;
    ::uint32_t batch_sequence_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_market_5fdata_2eproto;
};

// ===================================================================




// ===================================================================


#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// ProtoPriceLevel

// double price = 1;
inline void ProtoPriceLevel::clear_price() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.price_ = 0;
}
inline double ProtoPriceLevel::price() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoPriceLevel.price)
  return _internal_price();
}
inline void ProtoPriceLevel::set_price(double value) {
  _internal_set_price(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoPriceLevel.price)
}
inline double ProtoPriceLevel::_internal_price() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.price_;
}
inline void ProtoPriceLevel::_internal_set_price(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.price_ = value;
}

// uint32 volume = 2;
inline void ProtoPriceLevel::clear_volume() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.volume_ = 0u;
}
inline ::uint32_t ProtoPriceLevel::volume() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoPriceLevel.volume)
  return _internal_volume();
}
inline void ProtoPriceLevel::set_volume(::uint32_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoPriceLevel.volume)
}
inline ::uint32_t ProtoPriceLevel::_internal_volume() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.volume_;
}
inline void ProtoPriceLevel::_internal_set_volume(::uint32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.volume_ = value;
}

// uint32 order_count = 3;
inline void ProtoPriceLevel::clear_order_count() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.order_count_ = 0u;
}
inline ::uint32_t ProtoPriceLevel::order_count() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoPriceLevel.order_count)
  return _internal_order_count();
}
inline void ProtoPriceLevel::set_order_count(::uint32_t value) {
  _internal_set_order_count(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoPriceLevel.order_count)
}
inline ::uint32_t ProtoPriceLevel::_internal_order_count() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.order_count_;
}
inline void ProtoPriceLevel::_internal_set_order_count(::uint32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.order_count_ = value;
}

// -------------------------------------------------------------------

// ProtoTickData

// int64 timestamp_ns = 1;
inline void ProtoTickData::clear_timestamp_ns() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.timestamp_ns_ = ::int64_t{0};
}
inline ::int64_t ProtoTickData::timestamp_ns() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.timestamp_ns)
  return _internal_timestamp_ns();
}
inline void ProtoTickData::set_timestamp_ns(::int64_t value) {
  _internal_set_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.timestamp_ns)
}
inline ::int64_t ProtoTickData::_internal_timestamp_ns() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.timestamp_ns_;
}
inline void ProtoTickData::_internal_set_timestamp_ns(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.timestamp_ns_ = value;
}

// string symbol = 2;
inline void ProtoTickData::clear_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.ClearToEmpty();
}
inline const std::string& ProtoTickData::symbol() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.symbol)
  return _internal_symbol();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProtoTickData::set_symbol(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.symbol)
}
inline std::string* ProtoTickData::mutable_symbol() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_symbol();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoTickData.symbol)
  return _s;
}
inline const std::string& ProtoTickData::_internal_symbol() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbol_.Get();
}
inline void ProtoTickData::_internal_set_symbol(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(value, GetArena());
}
inline std::string* ProtoTickData::_internal_mutable_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.symbol_.Mutable( GetArena());
}
inline std::string* ProtoTickData::release_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ProtoTickData.symbol)
  return _impl_.symbol_.Release();
}
inline void ProtoTickData::set_allocated_symbol(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.symbol_.IsDefault()) {
          _impl_.symbol_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoTickData.symbol)
}

// string exchange = 3;
inline void ProtoTickData::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& ProtoTickData::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProtoTickData::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.exchange)
}
inline std::string* ProtoTickData::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoTickData.exchange)
  return _s;
}
inline const std::string& ProtoTickData::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void ProtoTickData::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* ProtoTickData::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* ProtoTickData::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ProtoTickData.exchange)
  return _impl_.exchange_.Release();
}
inline void ProtoTickData::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoTickData.exchange)
}

// double last_price = 4;
inline void ProtoTickData::clear_last_price() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.last_price_ = 0;
}
inline double ProtoTickData::last_price() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.last_price)
  return _internal_last_price();
}
inline void ProtoTickData::set_last_price(double value) {
  _internal_set_last_price(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.last_price)
}
inline double ProtoTickData::_internal_last_price() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.last_price_;
}
inline void ProtoTickData::_internal_set_last_price(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.last_price_ = value;
}

// uint64 volume = 5;
inline void ProtoTickData::clear_volume() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.volume_ = ::uint64_t{0u};
}
inline ::uint64_t ProtoTickData::volume() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.volume)
  return _internal_volume();
}
inline void ProtoTickData::set_volume(::uint64_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.volume)
}
inline ::uint64_t ProtoTickData::_internal_volume() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.volume_;
}
inline void ProtoTickData::_internal_set_volume(::uint64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.volume_ = value;
}

// double turnover = 6;
inline void ProtoTickData::clear_turnover() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.turnover_ = 0;
}
inline double ProtoTickData::turnover() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.turnover)
  return _internal_turnover();
}
inline void ProtoTickData::set_turnover(double value) {
  _internal_set_turnover(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.turnover)
}
inline double ProtoTickData::_internal_turnover() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.turnover_;
}
inline void ProtoTickData::_internal_set_turnover(double value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.turnover_ = value;
}

// uint64 open_interest = 7;
inline void ProtoTickData::clear_open_interest() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.open_interest_ = ::uint64_t{0u};
}
inline ::uint64_t ProtoTickData::open_interest() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.open_interest)
  return _internal_open_interest();
}
inline void ProtoTickData::set_open_interest(::uint64_t value) {
  _internal_set_open_interest(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.open_interest)
}
inline ::uint64_t ProtoTickData::_internal_open_interest() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.open_interest_;
}
inline void ProtoTickData::_internal_set_open_interest(::uint64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.open_interest_ = value;
}

// uint32 sequence = 8;
inline void ProtoTickData::clear_sequence() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.sequence_ = 0u;
}
inline ::uint32_t ProtoTickData::sequence() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.sequence)
  return _internal_sequence();
}
inline void ProtoTickData::set_sequence(::uint32_t value) {
  _internal_set_sequence(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.sequence)
}
inline ::uint32_t ProtoTickData::_internal_sequence() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.sequence_;
}
inline void ProtoTickData::_internal_set_sequence(::uint32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.sequence_ = value;
}

// string trade_flag = 9;
inline void ProtoTickData::clear_trade_flag() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.trade_flag_.ClearToEmpty();
}
inline const std::string& ProtoTickData::trade_flag() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoTickData.trade_flag)
  return _internal_trade_flag();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProtoTickData::set_trade_flag(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.trade_flag_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ProtoTickData.trade_flag)
}
inline std::string* ProtoTickData::mutable_trade_flag() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_trade_flag();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoTickData.trade_flag)
  return _s;
}
inline const std::string& ProtoTickData::_internal_trade_flag() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.trade_flag_.Get();
}
inline void ProtoTickData::_internal_set_trade_flag(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.trade_flag_.Set(value, GetArena());
}
inline std::string* ProtoTickData::_internal_mutable_trade_flag() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.trade_flag_.Mutable( GetArena());
}
inline std::string* ProtoTickData::release_trade_flag() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ProtoTickData.trade_flag)
  return _impl_.trade_flag_.Release();
}
inline void ProtoTickData::set_allocated_trade_flag(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.trade_flag_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.trade_flag_.IsDefault()) {
          _impl_.trade_flag_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoTickData.trade_flag)
}

// -------------------------------------------------------------------

// ProtoLevel2Data

// int64 timestamp_ns = 1;
inline void ProtoLevel2Data::clear_timestamp_ns() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.timestamp_ns_ = ::int64_t{0};
}
inline ::int64_t ProtoLevel2Data::timestamp_ns() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoLevel2Data.timestamp_ns)
  return _internal_timestamp_ns();
}
inline void ProtoLevel2Data::set_timestamp_ns(::int64_t value) {
  _internal_set_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoLevel2Data.timestamp_ns)
}
inline ::int64_t ProtoLevel2Data::_internal_timestamp_ns() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.timestamp_ns_;
}
inline void ProtoLevel2Data::_internal_set_timestamp_ns(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.timestamp_ns_ = value;
}

// string symbol = 2;
inline void ProtoLevel2Data::clear_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.ClearToEmpty();
}
inline const std::string& ProtoLevel2Data::symbol() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoLevel2Data.symbol)
  return _internal_symbol();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProtoLevel2Data::set_symbol(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ProtoLevel2Data.symbol)
}
inline std::string* ProtoLevel2Data::mutable_symbol() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_symbol();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoLevel2Data.symbol)
  return _s;
}
inline const std::string& ProtoLevel2Data::_internal_symbol() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbol_.Get();
}
inline void ProtoLevel2Data::_internal_set_symbol(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.symbol_.Set(value, GetArena());
}
inline std::string* ProtoLevel2Data::_internal_mutable_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.symbol_.Mutable( GetArena());
}
inline std::string* ProtoLevel2Data::release_symbol() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ProtoLevel2Data.symbol)
  return _impl_.symbol_.Release();
}
inline void ProtoLevel2Data::set_allocated_symbol(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbol_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.symbol_.IsDefault()) {
          _impl_.symbol_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoLevel2Data.symbol)
}

// string exchange = 3;
inline void ProtoLevel2Data::clear_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.ClearToEmpty();
}
inline const std::string& ProtoLevel2Data::exchange() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoLevel2Data.exchange)
  return _internal_exchange();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProtoLevel2Data::set_exchange(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ProtoLevel2Data.exchange)
}
inline std::string* ProtoLevel2Data::mutable_exchange() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_exchange();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoLevel2Data.exchange)
  return _s;
}
inline const std::string& ProtoLevel2Data::_internal_exchange() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.exchange_.Get();
}
inline void ProtoLevel2Data::_internal_set_exchange(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.exchange_.Set(value, GetArena());
}
inline std::string* ProtoLevel2Data::_internal_mutable_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.exchange_.Mutable( GetArena());
}
inline std::string* ProtoLevel2Data::release_exchange() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ProtoLevel2Data.exchange)
  return _impl_.exchange_.Release();
}
inline void ProtoLevel2Data::set_allocated_exchange(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.exchange_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.exchange_.IsDefault()) {
          _impl_.exchange_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoLevel2Data.exchange)
}

// repeated .financial_data.ProtoPriceLevel bids = 4;
inline int ProtoLevel2Data::_internal_bids_size() const {
  return _internal_bids().size();
}
inline int ProtoLevel2Data::bids_size() const {
  return _internal_bids_size();
}
inline void ProtoLevel2Data::clear_bids() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.bids_.Clear();
}
inline ::financial_data::ProtoPriceLevel* ProtoLevel2Data::mutable_bids(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoLevel2Data.bids)
  return _internal_mutable_bids()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>* ProtoLevel2Data::mutable_bids()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.ProtoLevel2Data.bids)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_bids();
}
inline const ::financial_data::ProtoPriceLevel& ProtoLevel2Data::bids(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoLevel2Data.bids)
  return _internal_bids().Get(index);
}
inline ::financial_data::ProtoPriceLevel* ProtoLevel2Data::add_bids() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::ProtoPriceLevel* _add = _internal_mutable_bids()->Add();
  // @@protoc_insertion_point(field_add:financial_data.ProtoLevel2Data.bids)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>& ProtoLevel2Data::bids() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.ProtoLevel2Data.bids)
  return _internal_bids();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>&
ProtoLevel2Data::_internal_bids() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.bids_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>*
ProtoLevel2Data::_internal_mutable_bids() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.bids_;
}

// repeated .financial_data.ProtoPriceLevel asks = 5;
inline int ProtoLevel2Data::_internal_asks_size() const {
  return _internal_asks().size();
}
inline int ProtoLevel2Data::asks_size() const {
  return _internal_asks_size();
}
inline void ProtoLevel2Data::clear_asks() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.asks_.Clear();
}
inline ::financial_data::ProtoPriceLevel* ProtoLevel2Data::mutable_asks(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoLevel2Data.asks)
  return _internal_mutable_asks()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>* ProtoLevel2Data::mutable_asks()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.ProtoLevel2Data.asks)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_asks();
}
inline const ::financial_data::ProtoPriceLevel& ProtoLevel2Data::asks(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoLevel2Data.asks)
  return _internal_asks().Get(index);
}
inline ::financial_data::ProtoPriceLevel* ProtoLevel2Data::add_asks() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::ProtoPriceLevel* _add = _internal_mutable_asks()->Add();
  // @@protoc_insertion_point(field_add:financial_data.ProtoLevel2Data.asks)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>& ProtoLevel2Data::asks() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.ProtoLevel2Data.asks)
  return _internal_asks();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>&
ProtoLevel2Data::_internal_asks() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.asks_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::ProtoPriceLevel>*
ProtoLevel2Data::_internal_mutable_asks() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.asks_;
}

// uint32 sequence = 6;
inline void ProtoLevel2Data::clear_sequence() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.sequence_ = 0u;
}
inline ::uint32_t ProtoLevel2Data::sequence() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoLevel2Data.sequence)
  return _internal_sequence();
}
inline void ProtoLevel2Data::set_sequence(::uint32_t value) {
  _internal_set_sequence(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoLevel2Data.sequence)
}
inline ::uint32_t ProtoLevel2Data::_internal_sequence() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.sequence_;
}
inline void ProtoLevel2Data::_internal_set_sequence(::uint32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.sequence_ = value;
}

// -------------------------------------------------------------------

// ProtoMarketData

// .financial_data.ProtoTickData tick = 1;
inline bool ProtoMarketData::has_tick() const {
  return data_type_case() == kTick;
}
inline bool ProtoMarketData::_internal_has_tick() const {
  return data_type_case() == kTick;
}
inline void ProtoMarketData::set_has_tick() {
  _impl_._oneof_case_[0] = kTick;
}
inline void ProtoMarketData::clear_tick() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (data_type_case() == kTick) {
    if (GetArena() == nullptr) {
      delete _impl_.data_type_.tick_;
    }
    clear_has_data_type();
  }
}
inline ::financial_data::ProtoTickData* ProtoMarketData::release_tick() {
  // @@protoc_insertion_point(field_release:financial_data.ProtoMarketData.tick)
  if (data_type_case() == kTick) {
    clear_has_data_type();
    auto* temp = _impl_.data_type_.tick_;
    if (GetArena() != nullptr) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_type_.tick_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::financial_data::ProtoTickData& ProtoMarketData::_internal_tick() const {
  return data_type_case() == kTick ? *_impl_.data_type_.tick_ : reinterpret_cast<::financial_data::ProtoTickData&>(::financial_data::_ProtoTickData_default_instance_);
}
inline const ::financial_data::ProtoTickData& ProtoMarketData::tick() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoMarketData.tick)
  return _internal_tick();
}
inline ::financial_data::ProtoTickData* ProtoMarketData::unsafe_arena_release_tick() {
  // @@protoc_insertion_point(field_unsafe_arena_release:financial_data.ProtoMarketData.tick)
  if (data_type_case() == kTick) {
    clear_has_data_type();
    auto* temp = _impl_.data_type_.tick_;
    _impl_.data_type_.tick_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ProtoMarketData::unsafe_arena_set_allocated_tick(::financial_data::ProtoTickData* value) {
  // We rely on the oneof clear method to free the earlier contents
  // of this oneof. We can directly use the pointer we're given to
  // set the new value.
  clear_data_type();
  if (value) {
    set_has_tick();
    _impl_.data_type_.tick_ = value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:financial_data.ProtoMarketData.tick)
}
inline ::financial_data::ProtoTickData* ProtoMarketData::_internal_mutable_tick() {
  if (data_type_case() != kTick) {
    clear_data_type();
    set_has_tick();
    _impl_.data_type_.tick_ = CreateMaybeMessage<::financial_data::ProtoTickData>(GetArena());
  }
  return _impl_.data_type_.tick_;
}
inline ::financial_data::ProtoTickData* ProtoMarketData::mutable_tick() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::financial_data::ProtoTickData* _msg = _internal_mutable_tick();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoMarketData.tick)
  return _msg;
}

// .financial_data.ProtoLevel2Data level2 = 2;
inline bool ProtoMarketData::has_level2() const {
  return data_type_case() == kLevel2;
}
inline bool ProtoMarketData::_internal_has_level2() const {
  return data_type_case() == kLevel2;
}
inline void ProtoMarketData::set_has_level2() {
  _impl_._oneof_case_[0] = kLevel2;
}
inline void ProtoMarketData::clear_level2() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  if (data_type_case() == kLevel2) {
    if (GetArena() == nullptr) {
      delete _impl_.data_type_.level2_;
    }
    clear_has_data_type();
  }
}
inline ::financial_data::ProtoLevel2Data* ProtoMarketData::release_level2() {
  // @@protoc_insertion_point(field_release:financial_data.ProtoMarketData.level2)
  if (data_type_case() == kLevel2) {
    clear_has_data_type();
    auto* temp = _impl_.data_type_.level2_;
    if (GetArena() != nullptr) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_type_.level2_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::financial_data::ProtoLevel2Data& ProtoMarketData::_internal_level2() const {
  return data_type_case() == kLevel2 ? *_impl_.data_type_.level2_ : reinterpret_cast<::financial_data::ProtoLevel2Data&>(::financial_data::_ProtoLevel2Data_default_instance_);
}
inline const ::financial_data::ProtoLevel2Data& ProtoMarketData::level2() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoMarketData.level2)
  return _internal_level2();
}
inline ::financial_data::ProtoLevel2Data* ProtoMarketData::unsafe_arena_release_level2() {
  // @@protoc_insertion_point(field_unsafe_arena_release:financial_data.ProtoMarketData.level2)
  if (data_type_case() == kLevel2) {
    clear_has_data_type();
    auto* temp = _impl_.data_type_.level2_;
    _impl_.data_type_.level2_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ProtoMarketData::unsafe_arena_set_allocated_level2(::financial_data::ProtoLevel2Data* value) {
  // We rely on the oneof clear method to free the earlier contents
  // of this oneof. We can directly use the pointer we're given to
  // set the new value.
  clear_data_type();
  if (value) {
    set_has_level2();
    _impl_.data_type_.level2_ = value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:financial_data.ProtoMarketData.level2)
}
inline ::financial_data::ProtoLevel2Data* ProtoMarketData::_internal_mutable_level2() {
  if (data_type_case() != kLevel2) {
    clear_data_type();
    set_has_level2();
    _impl_.data_type_.level2_ = CreateMaybeMessage<::financial_data::ProtoLevel2Data>(GetArena());
  }
  return _impl_.data_type_.level2_;
}
inline ::financial_data::ProtoLevel2Data* ProtoMarketData::mutable_level2() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::financial_data::ProtoLevel2Data* _msg = _internal_mutable_level2();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoMarketData.level2)
  return _msg;
}

// int64 receive_time_ns = 3;
inline void ProtoMarketData::clear_receive_time_ns() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.receive_time_ns_ = ::int64_t{0};
}
inline ::int64_t ProtoMarketData::receive_time_ns() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoMarketData.receive_time_ns)
  return _internal_receive_time_ns();
}
inline void ProtoMarketData::set_receive_time_ns(::int64_t value) {
  _internal_set_receive_time_ns(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoMarketData.receive_time_ns)
}
inline ::int64_t ProtoMarketData::_internal_receive_time_ns() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.receive_time_ns_;
}
inline void ProtoMarketData::_internal_set_receive_time_ns(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.receive_time_ns_ = value;
}

// string source = 4;
inline void ProtoMarketData::clear_source() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.source_.ClearToEmpty();
}
inline const std::string& ProtoMarketData::source() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoMarketData.source)
  return _internal_source();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProtoMarketData::set_source(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.source_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ProtoMarketData.source)
}
inline std::string* ProtoMarketData::mutable_source() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_source();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoMarketData.source)
  return _s;
}
inline const std::string& ProtoMarketData::_internal_source() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.source_.Get();
}
inline void ProtoMarketData::_internal_set_source(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.source_.Set(value, GetArena());
}
inline std::string* ProtoMarketData::_internal_mutable_source() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.source_.Mutable( GetArena());
}
inline std::string* ProtoMarketData::release_source() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ProtoMarketData.source)
  return _impl_.source_.Release();
}
inline void ProtoMarketData::set_allocated_source(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.source_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.source_.IsDefault()) {
          _impl_.source_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoMarketData.source)
}

inline bool ProtoMarketData::has_data_type() const {
  return data_type_case() != DATA_TYPE_NOT_SET;
}
inline void ProtoMarketData::clear_has_data_type() {
  _impl_._oneof_case_[0] = DATA_TYPE_NOT_SET;
}
inline ProtoMarketData::DataTypeCase ProtoMarketData::data_type_case() const {
  return ProtoMarketData::DataTypeCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// ProtoMarketDataBatch

// repeated .financial_data.ProtoMarketData data = 1;
inline int ProtoMarketDataBatch::_internal_data_size() const {
  return _internal_data().size();
}
inline int ProtoMarketDataBatch::data_size() const {
  return _internal_data_size();
}
inline void ProtoMarketDataBatch::clear_data() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.data_.Clear();
}
inline ::financial_data::ProtoMarketData* ProtoMarketDataBatch::mutable_data(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoMarketDataBatch.data)
  return _internal_mutable_data()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::ProtoMarketData>* ProtoMarketDataBatch::mutable_data()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.ProtoMarketDataBatch.data)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_data();
}
inline const ::financial_data::ProtoMarketData& ProtoMarketDataBatch::data(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoMarketDataBatch.data)
  return _internal_data().Get(index);
}
inline ::financial_data::ProtoMarketData* ProtoMarketDataBatch::add_data() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::financial_data::ProtoMarketData* _add = _internal_mutable_data()->Add();
  // @@protoc_insertion_point(field_add:financial_data.ProtoMarketDataBatch.data)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoMarketData>& ProtoMarketDataBatch::data() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.ProtoMarketDataBatch.data)
  return _internal_data();
}
inline const ::google::protobuf::RepeatedPtrField<::financial_data::ProtoMarketData>&
ProtoMarketDataBatch::_internal_data() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.data_;
}
inline ::google::protobuf::RepeatedPtrField<::financial_data::ProtoMarketData>*
ProtoMarketDataBatch::_internal_mutable_data() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.data_;
}

// int64 batch_timestamp_ns = 2;
inline void ProtoMarketDataBatch::clear_batch_timestamp_ns() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.batch_timestamp_ns_ = ::int64_t{0};
}
inline ::int64_t ProtoMarketDataBatch::batch_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoMarketDataBatch.batch_timestamp_ns)
  return _internal_batch_timestamp_ns();
}
inline void ProtoMarketDataBatch::set_batch_timestamp_ns(::int64_t value) {
  _internal_set_batch_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoMarketDataBatch.batch_timestamp_ns)
}
inline ::int64_t ProtoMarketDataBatch::_internal_batch_timestamp_ns() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.batch_timestamp_ns_;
}
inline void ProtoMarketDataBatch::_internal_set_batch_timestamp_ns(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.batch_timestamp_ns_ = value;
}

// uint32 batch_sequence = 3;
inline void ProtoMarketDataBatch::clear_batch_sequence() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.batch_sequence_ = 0u;
}
inline ::uint32_t ProtoMarketDataBatch::batch_sequence() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoMarketDataBatch.batch_sequence)
  return _internal_batch_sequence();
}
inline void ProtoMarketDataBatch::set_batch_sequence(::uint32_t value) {
  _internal_set_batch_sequence(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoMarketDataBatch.batch_sequence)
}
inline ::uint32_t ProtoMarketDataBatch::_internal_batch_sequence() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.batch_sequence_;
}
inline void ProtoMarketDataBatch::_internal_set_batch_sequence(::uint32_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.batch_sequence_ = value;
}

// -------------------------------------------------------------------

// ProtoSubscriptionRequest

// repeated string symbols = 1;
inline int ProtoSubscriptionRequest::_internal_symbols_size() const {
  return _internal_symbols().size();
}
inline int ProtoSubscriptionRequest::symbols_size() const {
  return _internal_symbols_size();
}
inline void ProtoSubscriptionRequest::clear_symbols() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.symbols_.Clear();
}
inline std::string* ProtoSubscriptionRequest::add_symbols()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  std::string* _s = _internal_mutable_symbols()->Add();
  // @@protoc_insertion_point(field_add_mutable:financial_data.ProtoSubscriptionRequest.symbols)
  return _s;
}
inline const std::string& ProtoSubscriptionRequest::symbols(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoSubscriptionRequest.symbols)
  return _internal_symbols().Get(index);
}
inline std::string* ProtoSubscriptionRequest::mutable_symbols(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoSubscriptionRequest.symbols)
  return _internal_mutable_symbols()->Mutable(index);
}
inline void ProtoSubscriptionRequest::set_symbols(int index, const std::string& value) {
  _internal_mutable_symbols()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::set_symbols(int index, std::string&& value) {
  _internal_mutable_symbols()->Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::set_symbols(int index, const char* value) {
  ABSL_DCHECK(value != nullptr);
  _internal_mutable_symbols()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::set_symbols(int index, const char* value,
                              std::size_t size) {
  _internal_mutable_symbols()->Mutable(index)->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::set_symbols(int index, absl::string_view value) {
  _internal_mutable_symbols()->Mutable(index)->assign(value.data(),
                                                     value.size());
  // @@protoc_insertion_point(field_set_string_piece:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::add_symbols(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value);
  // @@protoc_insertion_point(field_add:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::add_symbols(std::string&& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add(std::move(value));
  // @@protoc_insertion_point(field_add:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::add_symbols(const char* value) {
  ABSL_DCHECK(value != nullptr);
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::add_symbols(const char* value, std::size_t size) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:financial_data.ProtoSubscriptionRequest.symbols)
}
inline void ProtoSubscriptionRequest::add_symbols(absl::string_view value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_symbols()->Add()->assign(value.data(), value.size());
  // @@protoc_insertion_point(field_add_string_piece:financial_data.ProtoSubscriptionRequest.symbols)
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
ProtoSubscriptionRequest::symbols() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.ProtoSubscriptionRequest.symbols)
  return _internal_symbols();
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
ProtoSubscriptionRequest::mutable_symbols() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.ProtoSubscriptionRequest.symbols)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_symbols();
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
ProtoSubscriptionRequest::_internal_symbols() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.symbols_;
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
ProtoSubscriptionRequest::_internal_mutable_symbols() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.symbols_;
}

// repeated string data_types = 2;
inline int ProtoSubscriptionRequest::_internal_data_types_size() const {
  return _internal_data_types().size();
}
inline int ProtoSubscriptionRequest::data_types_size() const {
  return _internal_data_types_size();
}
inline void ProtoSubscriptionRequest::clear_data_types() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.data_types_.Clear();
}
inline std::string* ProtoSubscriptionRequest::add_data_types()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  std::string* _s = _internal_mutable_data_types()->Add();
  // @@protoc_insertion_point(field_add_mutable:financial_data.ProtoSubscriptionRequest.data_types)
  return _s;
}
inline const std::string& ProtoSubscriptionRequest::data_types(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoSubscriptionRequest.data_types)
  return _internal_data_types().Get(index);
}
inline std::string* ProtoSubscriptionRequest::mutable_data_types(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoSubscriptionRequest.data_types)
  return _internal_mutable_data_types()->Mutable(index);
}
inline void ProtoSubscriptionRequest::set_data_types(int index, const std::string& value) {
  _internal_mutable_data_types()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::set_data_types(int index, std::string&& value) {
  _internal_mutable_data_types()->Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::set_data_types(int index, const char* value) {
  ABSL_DCHECK(value != nullptr);
  _internal_mutable_data_types()->Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::set_data_types(int index, const char* value,
                              std::size_t size) {
  _internal_mutable_data_types()->Mutable(index)->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::set_data_types(int index, absl::string_view value) {
  _internal_mutable_data_types()->Mutable(index)->assign(value.data(),
                                                     value.size());
  // @@protoc_insertion_point(field_set_string_piece:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::add_data_types(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_data_types()->Add()->assign(value);
  // @@protoc_insertion_point(field_add:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::add_data_types(std::string&& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_data_types()->Add(std::move(value));
  // @@protoc_insertion_point(field_add:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::add_data_types(const char* value) {
  ABSL_DCHECK(value != nullptr);
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_data_types()->Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::add_data_types(const char* value, std::size_t size) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_data_types()->Add()->assign(
      reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:financial_data.ProtoSubscriptionRequest.data_types)
}
inline void ProtoSubscriptionRequest::add_data_types(absl::string_view value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _internal_mutable_data_types()->Add()->assign(value.data(), value.size());
  // @@protoc_insertion_point(field_add_string_piece:financial_data.ProtoSubscriptionRequest.data_types)
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
ProtoSubscriptionRequest::data_types() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:financial_data.ProtoSubscriptionRequest.data_types)
  return _internal_data_types();
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
ProtoSubscriptionRequest::mutable_data_types() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:financial_data.ProtoSubscriptionRequest.data_types)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  return _internal_mutable_data_types();
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
ProtoSubscriptionRequest::_internal_data_types() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.data_types_;
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
ProtoSubscriptionRequest::_internal_mutable_data_types() {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return &_impl_.data_types_;
}

// bool include_history = 3;
inline void ProtoSubscriptionRequest::clear_include_history() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.include_history_ = false;
}
inline bool ProtoSubscriptionRequest::include_history() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoSubscriptionRequest.include_history)
  return _internal_include_history();
}
inline void ProtoSubscriptionRequest::set_include_history(bool value) {
  _internal_set_include_history(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionRequest.include_history)
}
inline bool ProtoSubscriptionRequest::_internal_include_history() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.include_history_;
}
inline void ProtoSubscriptionRequest::_internal_set_include_history(bool value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.include_history_ = value;
}

// int64 start_time_ns = 4;
inline void ProtoSubscriptionRequest::clear_start_time_ns() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.start_time_ns_ = ::int64_t{0};
}
inline ::int64_t ProtoSubscriptionRequest::start_time_ns() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoSubscriptionRequest.start_time_ns)
  return _internal_start_time_ns();
}
inline void ProtoSubscriptionRequest::set_start_time_ns(::int64_t value) {
  _internal_set_start_time_ns(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionRequest.start_time_ns)
}
inline ::int64_t ProtoSubscriptionRequest::_internal_start_time_ns() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.start_time_ns_;
}
inline void ProtoSubscriptionRequest::_internal_set_start_time_ns(::int64_t value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.start_time_ns_ = value;
}

// -------------------------------------------------------------------

// ProtoSubscriptionResponse

// bool success = 1;
inline void ProtoSubscriptionResponse::clear_success() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.success_ = false;
}
inline bool ProtoSubscriptionResponse::success() const {
  // @@protoc_insertion_point(field_get:financial_data.ProtoSubscriptionResponse.success)
  return _internal_success();
}
inline void ProtoSubscriptionResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionResponse.success)
}
inline bool ProtoSubscriptionResponse::_internal_success() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.success_;
}
inline void ProtoSubscriptionResponse::_internal_set_success(bool value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.success_ = value;
}

// string message = 2;
inline void ProtoSubscriptionResponse::clear_message() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.message_.ClearToEmpty();
}
inline const std::string& ProtoSubscriptionResponse::message() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoSubscriptionResponse.message)
  return _internal_message();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProtoSubscriptionResponse::set_message(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.message_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionResponse.message)
}
inline std::string* ProtoSubscriptionResponse::mutable_message() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoSubscriptionResponse.message)
  return _s;
}
inline const std::string& ProtoSubscriptionResponse::_internal_message() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.message_.Get();
}
inline void ProtoSubscriptionResponse::_internal_set_message(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.message_.Set(value, GetArena());
}
inline std::string* ProtoSubscriptionResponse::_internal_mutable_message() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.message_.Mutable( GetArena());
}
inline std::string* ProtoSubscriptionResponse::release_message() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ProtoSubscriptionResponse.message)
  return _impl_.message_.Release();
}
inline void ProtoSubscriptionResponse::set_allocated_message(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.message_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.message_.IsDefault()) {
          _impl_.message_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoSubscriptionResponse.message)
}

// string subscription_id = 3;
inline void ProtoSubscriptionResponse::clear_subscription_id() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.subscription_id_.ClearToEmpty();
}
inline const std::string& ProtoSubscriptionResponse::subscription_id() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:financial_data.ProtoSubscriptionResponse.subscription_id)
  return _internal_subscription_id();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProtoSubscriptionResponse::set_subscription_id(Arg_&& arg,
                                                     Args_... args) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.subscription_id_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:financial_data.ProtoSubscriptionResponse.subscription_id)
}
inline std::string* ProtoSubscriptionResponse::mutable_subscription_id() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_subscription_id();
  // @@protoc_insertion_point(field_mutable:financial_data.ProtoSubscriptionResponse.subscription_id)
  return _s;
}
inline const std::string& ProtoSubscriptionResponse::_internal_subscription_id() const {
  PROTOBUF_TSAN_READ(&_impl_._tsan_detect_race);
  return _impl_.subscription_id_.Get();
}
inline void ProtoSubscriptionResponse::_internal_set_subscription_id(const std::string& value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  _impl_.subscription_id_.Set(value, GetArena());
}
inline std::string* ProtoSubscriptionResponse::_internal_mutable_subscription_id() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ;
  return _impl_.subscription_id_.Mutable( GetArena());
}
inline std::string* ProtoSubscriptionResponse::release_subscription_id() {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  // @@protoc_insertion_point(field_release:financial_data.ProtoSubscriptionResponse.subscription_id)
  return _impl_.subscription_id_.Release();
}
inline void ProtoSubscriptionResponse::set_allocated_subscription_id(std::string* value) {
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  _impl_.subscription_id_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.subscription_id_.IsDefault()) {
          _impl_.subscription_id_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoSubscriptionResponse.subscription_id)
}

#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)
}  // namespace financial_data


// @@protoc_insertion_point(global_scope)

#include "google/protobuf/port_undef.inc"

#endif  // GOOGLE_PROTOBUF_INCLUDED_market_5fdata_2eproto_2epb_2eh
