# WSL兼容的依赖包版本
# FastAPI and web server dependencies
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
pydantic>=2.0.0

# Database and storage dependencies
redis>=4.0.0
asyncpg>=0.25.0

# Data collection dependencies
pytdx>=1.70
pandas>=2.0.0,<2.1.0
numpy>=1.20.0

# Additional dependencies
aiohttp>=3.8.0
python-multipart>=0.0.5
watchdog>=3.0.0
croniter>=1.3.0

# Comprehensive data manager dependencies
dataclasses-json>=0.5.0

# Testing dependencies
pytest>=7.0.0
pytest-asyncio>=0.20.0
httpx>=0.24.0

# Development dependencies
black>=22.0.0