#include "redis_storage.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cstring>
#include <cassert>

// Redis C client headers (假设已安装hiredis)
extern "C" {
#include <hiredis/hiredis.h>
}

namespace financial_data {

// RedisConnectionPool 实现
RedisConnectionPool::RedisConnectionPool(const RedisConfig& config) : config_(config) {}

RedisConnectionPool::~RedisConnectionPool() {
    Shutdown();
}

bool RedisConnectionPool::Initialize() {
    for (int i = 0; i < config_.max_connections; ++i) {
        redisContext* conn = redisConnect(config_.host.c_str(), config_.port);
        if (!conn || conn->err) {
            std::cerr << "Redis connection failed: " << (conn ? conn->errstr : "allocation error") << std::endl;
            if (conn) redisFree(conn);
            return false;
        }
        
        // 设置超时
        struct timeval timeout = {config_.command_timeout_ms / 1000, (config_.command_timeout_ms % 1000) * 1000};
        redisSetTimeout(conn, timeout);
        
        // 认证（如果需要）
        if (!config_.password.empty()) {
            redisReply* reply = (redisReply*)redisCommand(conn, "AUTH %s", config_.password.c_str());
            if (!reply || reply->type == REDIS_REPLY_ERROR) {
                std::cerr << "Redis authentication failed" << std::endl;
                if (reply) freeReplyObject(reply);
                redisFree(conn);
                return false;
            }
            freeReplyObject(reply);
        }
        
        // 选择数据库
        if (config_.database != 0) {
            redisReply* reply = (redisReply*)redisCommand(conn, "SELECT %d", config_.database);
            if (!reply || reply->type == REDIS_REPLY_ERROR) {
                std::cerr << "Redis database selection failed" << std::endl;
                if (reply) freeReplyObject(reply);
                redisFree(conn);
                return false;
            }
            freeReplyObject(reply);
        }
        
        available_connections_.push(conn);
        active_connections_++;
    }
    
    return true;
}

redisContext* RedisConnectionPool::GetConnection() {
    std::unique_lock<std::mutex> lock(pool_mutex_);
    pool_cv_.wait(lock, [this] { return !available_connections_.empty() || shutdown_flag_; });
    
    if (shutdown_flag_) {
        return nullptr;
    }
    
    redisContext* conn = available_connections_.front();
    available_connections_.pop();
    return conn;
}

void RedisConnectionPool::ReturnConnection(redisContext* conn) {
    if (!conn || shutdown_flag_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(pool_mutex_);
    available_connections_.push(conn);
    pool_cv_.notify_one();
}

void RedisConnectionPool::Shutdown() {
    shutdown_flag_ = true;
    
    std::lock_guard<std::mutex> lock(pool_mutex_);
    while (!available_connections_.empty()) {
        redisContext* conn = available_connections_.front();
        available_connections_.pop();
        redisFree(conn);
        active_connections_--;
    }
    
    pool_cv_.notify_all();
}

// RedisHotStorage 实现
RedisHotStorage::RedisHotStorage(const RedisConfig& config) : config_(config) {}

RedisHotStorage::~RedisHotStorage() {
    Shutdown();
}

bool RedisHotStorage::Initialize() {
    // 初始化连接池
    connection_pool_ = std::make_unique<RedisConnectionPool>(config_);
    if (!connection_pool_->Initialize()) {
        std::cerr << "Failed to initialize Redis connection pool" << std::endl;
        return false;
    }
    
    // 如果启用集群模式，初始化集群
    if (config_.enable_cluster && !InitializeCluster()) {
        std::cerr << "Failed to initialize Redis cluster" << std::endl;
        return false;
    }
    
    // 优化热数据存储配置
    if (!OptimizeForHotData()) {
        std::cerr << "Failed to optimize for hot data storage" << std::endl;
        return false;
    }
    
    // 设置过期策略
    if (!SetupExpirationPolicy()) {
        std::cerr << "Failed to setup expiration policy" << std::endl;
        return false;
    }
    
    // 配置内存策略
    if (!ConfigureMemoryPolicy()) {
        std::cerr << "Failed to configure memory policy" << std::endl;
        return false;
    }
    
    // 启动异步写入工作线程
    shutdown_flag_ = false;
    for (int i = 0; i < config_.write_worker_count; ++i) {
        write_workers_.emplace_back(&RedisHotStorage::AsyncWriteWorker, this);
    }
    
    std::cout << "Redis hot storage initialized successfully" << std::endl;
    std::cout << "Configuration: " << std::endl;
    std::cout << "  Cluster mode: " << (config_.enable_cluster ? "enabled" : "disabled") << std::endl;
    std::cout << "  TTL: " << config_.hot_data_ttl_seconds << " seconds" << std::endl;
    std::cout << "  Worker threads: " << config_.write_worker_count << std::endl;
    std::cout << "  Max connections: " << config_.max_connections << std::endl;
    
    return true;
}

void RedisHotStorage::Shutdown() {
    shutdown_flag_ = true;
    
    // 通知所有写入线程退出
    write_queue_cv_.notify_all();
    
    // 等待所有写入线程完成
    for (auto& worker : write_workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    // 关闭连接池
    if (connection_pool_) {
        connection_pool_->Shutdown();
    }
    
    std::cout << "Redis hot storage shutdown completed" << std::endl;
}

// 同步写入接口实现
bool RedisHotStorage::StoreTick(const StandardTick& tick) {
    if (!tick.IsValid()) {
        return false;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    bool result = StoreTickInternal(conn, tick);
    connection_pool_->ReturnConnection(conn);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    UpdateWriteStats(latency_ns);
    
    return result;
}

bool RedisHotStorage::StoreLevel2(const Level2Data& level2) {
    if (!level2.IsValid()) {
        return false;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    bool result = StoreLevel2Internal(conn, level2);
    connection_pool_->ReturnConnection(conn);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    UpdateWriteStats(latency_ns);
    
    return result;
}

bool RedisHotStorage::StoreBatch(const std::vector<StandardTick>& ticks) {
    if (ticks.empty()) {
        return true;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    bool result = true;
    for (const auto& tick : ticks) {
        if (!StoreTickInternal(conn, tick)) {
            result = false;
            break;
        }
    }
    
    connection_pool_->ReturnConnection(conn);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    UpdateWriteStats(latency_ns);
    
    return result;
}

// 异步写入接口实现
std::future<bool> RedisHotStorage::StoreTickAsync(const StandardTick& tick) {
    BatchWriteTask task;
    task.ticks.push_back(tick);
    auto future = task.promise.get_future();
    
    {
        std::lock_guard<std::mutex> lock(write_queue_mutex_);
        if (write_queue_.size() >= MAX_QUEUE_SIZE) {
            task.promise.set_value(false);
            return future;
        }
        write_queue_.push(std::move(task));
    }
    
    write_queue_cv_.notify_one();
    return future;
}

std::future<bool> RedisHotStorage::StoreBatchAsync(const std::vector<StandardTick>& ticks) {
    BatchWriteTask task;
    task.ticks = ticks;
    auto future = task.promise.get_future();
    
    {
        std::lock_guard<std::mutex> lock(write_queue_mutex_);
        if (write_queue_.size() >= MAX_QUEUE_SIZE) {
            task.promise.set_value(false);
            return future;
        }
        write_queue_.push(std::move(task));
    }
    
    write_queue_cv_.notify_one();
    return future;
}

// 查询接口实现
bool RedisHotStorage::GetLatestTick(const std::string& symbol, StandardTick& tick) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    bool result = GetLatestTickInternal(conn, symbol, tick);
    connection_pool_->ReturnConnection(conn);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    UpdateQueryStats(latency_ns);
    
    return result;
}

QueryResult RedisHotStorage::QueryTicks(const std::string& symbol, const QueryOptions& options) {
    QueryResult result;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return result;
    }
    
    // 使用ZRANGEBYSCORE查询时间范围内的数据
    std::string ts_key = GetTimeSeriesKey(symbol, "tick");
    std::string start_score = std::to_string(options.start_time_ns);
    std::string end_score = std::to_string(options.end_time_ns);
    
    redisReply* reply = (redisReply*)redisCommand(conn, 
        "ZRANGEBYSCORE %s %s %s LIMIT 0 %d", 
        ts_key.c_str(), start_score.c_str(), end_score.c_str(), options.limit);
    
    if (reply && reply->type == REDIS_REPLY_ARRAY) {
        for (size_t i = 0; i < reply->elements; ++i) {
            std::string tick_key = reply->element[i]->str;
            
            // 获取具体的tick数据
            redisReply* tick_reply = (redisReply*)redisCommand(conn, "GET %s", tick_key.c_str());
            if (tick_reply && tick_reply->type == REDIS_REPLY_STRING) {
                StandardTick tick;
                if (DeserializeTick(tick_reply->str, tick)) {
                    result.ticks.push_back(tick);
                }
            }
            if (tick_reply) freeReplyObject(tick_reply);
        }
        
        result.has_more = (reply->elements == static_cast<size_t>(options.limit));
    }
    
    if (reply) freeReplyObject(reply);
    connection_pool_->ReturnConnection(conn);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.query_time_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    UpdateQueryStats(result.query_time_ns);
    
    return result;
}

// 批量查询接口实现
std::unordered_map<std::string, StandardTick> RedisHotStorage::GetLatestTicks(const std::vector<std::string>& symbols) {
    std::unordered_map<std::string, StandardTick> results;
    
    if (symbols.empty()) {
        return results;
    }
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return results;
    }
    
    // 构建批量查询命令
    std::vector<std::string> keys;
    for (const auto& symbol : symbols) {
        keys.push_back(GetLatestKey(symbol, "tick"));
    }
    
    // 使用MGET进行批量查询
    std::ostringstream cmd;
    cmd << "MGET";
    for (const auto& key : keys) {
        cmd << " " << key;
    }
    
    redisReply* reply = (redisReply*)redisCommand(conn, cmd.str().c_str());
    if (reply && reply->type == REDIS_REPLY_ARRAY) {
        for (size_t i = 0; i < reply->elements && i < symbols.size(); ++i) {
            if (reply->element[i]->type == REDIS_REPLY_STRING) {
                StandardTick tick;
                if (DeserializeTick(reply->element[i]->str, tick)) {
                    results[symbols[i]] = tick;
                }
            }
        }
    }
    
    if (reply) freeReplyObject(reply);
    connection_pool_->ReturnConnection(conn);
    
    return results;
}

// 内部实现方法
bool RedisHotStorage::StoreTickInternal(redisContext* conn, const StandardTick& tick) {
    std::string tick_data = SerializeTick(tick);
    std::string latest_key = GetLatestKey(tick.symbol, "tick");
    std::string ts_key = GetTimeSeriesKey(tick.symbol, "tick");
    std::string tick_key = GetTickKey(tick.symbol) + ":" + std::to_string(tick.timestamp_ns);
    
    int ttl = config_.hot_data_ttl_seconds;
    
    if (config_.enable_pipelining) {
        // 使用管道提高性能
        redisAppendCommand(conn, "SETEX %s %d %s", tick_key.c_str(), ttl, tick_data.c_str());
        redisAppendCommand(conn, "SETEX %s %d %s", latest_key.c_str(), ttl, tick_data.c_str());
        redisAppendCommand(conn, "ZADD %s %lld %s", ts_key.c_str(), tick.timestamp_ns, tick_key.c_str());
        redisAppendCommand(conn, "EXPIRE %s %d", ts_key.c_str(), ttl);
        
        // 获取所有回复
        for (int i = 0; i < 4; ++i) {
            redisReply* reply;
            if (redisGetReply(conn, (void**)&reply) != REDIS_OK) {
                return false;
            }
            if (reply) freeReplyObject(reply);
        }
    } else {
        // 逐个执行命令
        redisReply* reply;
        
        reply = (redisReply*)redisCommand(conn, "SETEX %s %d %s", tick_key.c_str(), ttl, tick_data.c_str());
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            if (reply) freeReplyObject(reply);
            return false;
        }
        freeReplyObject(reply);
        
        reply = (redisReply*)redisCommand(conn, "SETEX %s %d %s", latest_key.c_str(), ttl, tick_data.c_str());
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            if (reply) freeReplyObject(reply);
            return false;
        }
        freeReplyObject(reply);
        
        reply = (redisReply*)redisCommand(conn, "ZADD %s %lld %s", ts_key.c_str(), tick.timestamp_ns, tick_key.c_str());
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            if (reply) freeReplyObject(reply);
            return false;
        }
        freeReplyObject(reply);
        
        reply = (redisReply*)redisCommand(conn, "EXPIRE %s %d", ts_key.c_str(), ttl);
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            if (reply) freeReplyObject(reply);
            return false;
        }
        freeReplyObject(reply);
    }
    
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.total_ticks_stored++;
    }
    
    return true;
}

bool RedisHotStorage::GetLatestTickInternal(redisContext* conn, const std::string& symbol, StandardTick& tick) {
    std::string latest_key = GetLatestKey(symbol, "tick");
    
    redisReply* reply = (redisReply*)redisCommand(conn, "GET %s", latest_key.c_str());
    if (!reply) {
        return false;
    }
    
    bool result = false;
    if (reply->type == REDIS_REPLY_STRING) {
        result = DeserializeTick(reply->str, tick);
    }
    
    freeReplyObject(reply);
    
    if (result) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.total_queries++;
    }
    
    return result;
}

// 序列化方法实现
std::string RedisHotStorage::SerializeTick(const StandardTick& tick) {
    std::ostringstream oss;
    oss << tick.timestamp_ns << "|"
        << tick.symbol << "|"
        << tick.exchange << "|"
        << tick.last_price << "|"
        << tick.volume << "|"
        << tick.turnover << "|"
        << tick.open_interest << "|"
        << tick.sequence << "|"
        << tick.trade_flag;
    
    // 序列化买卖盘数据
    for (int i = 0; i < 5; ++i) {
        oss << "|" << tick.bids[i].price << "," << tick.bids[i].volume << "," << tick.bids[i].order_count;
    }
    for (int i = 0; i < 5; ++i) {
        oss << "|" << tick.asks[i].price << "," << tick.asks[i].volume << "," << tick.asks[i].order_count;
    }
    
    return oss.str();
}

bool RedisHotStorage::DeserializeTick(const std::string& data, StandardTick& tick) {
    std::istringstream iss(data);
    std::string token;
    std::vector<std::string> tokens;
    
    while (std::getline(iss, token, '|')) {
        tokens.push_back(token);
    }
    
    if (tokens.size() < 19) {  // 基本字段 + 10个档位数据
        return false;
    }
    
    try {
        tick.timestamp_ns = std::stoll(tokens[0]);
        tick.symbol = tokens[1];
        tick.exchange = tokens[2];
        tick.last_price = std::stod(tokens[3]);
        tick.volume = std::stoull(tokens[4]);
        tick.turnover = std::stod(tokens[5]);
        tick.open_interest = std::stoull(tokens[6]);
        tick.sequence = std::stoul(tokens[7]);
        tick.trade_flag = tokens[8];
        
        // 反序列化买卖盘数据
        for (int i = 0; i < 5; ++i) {
            std::istringstream bid_iss(tokens[9 + i]);
            std::string price_str, volume_str, count_str;
            
            if (std::getline(bid_iss, price_str, ',') &&
                std::getline(bid_iss, volume_str, ',') &&
                std::getline(bid_iss, count_str)) {
                tick.bids[i].price = std::stod(price_str);
                tick.bids[i].volume = std::stoul(volume_str);
                tick.bids[i].order_count = std::stoul(count_str);
            }
        }
        
        for (int i = 0; i < 5; ++i) {
            std::istringstream ask_iss(tokens[14 + i]);
            std::string price_str, volume_str, count_str;
            
            if (std::getline(ask_iss, price_str, ',') &&
                std::getline(ask_iss, volume_str, ',') &&
                std::getline(ask_iss, count_str)) {
                tick.asks[i].price = std::stod(price_str);
                tick.asks[i].volume = std::stoul(volume_str);
                tick.asks[i].order_count = std::stoul(count_str);
            }
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Deserialization error: " << e.what() << std::endl;
        return false;
    }
}

// Redis键生成方法
std::string RedisHotStorage::GetTickKey(const std::string& symbol) {
    return "hot:tick:" + symbol;
}

std::string RedisHotStorage::GetLevel2Key(const std::string& symbol) {
    return "hot:level2:" + symbol;
}

std::string RedisHotStorage::GetTimeSeriesKey(const std::string& symbol, const std::string& type) {
    return "hot:ts:" + type + ":" + symbol;
}

std::string RedisHotStorage::GetLatestKey(const std::string& symbol, const std::string& type) {
    return "hot:latest:" + type + ":" + symbol;
}

// 异步写入工作线程
void RedisHotStorage::AsyncWriteWorker() {
    while (!shutdown_flag_) {
        std::unique_lock<std::mutex> lock(write_queue_mutex_);
        write_queue_cv_.wait(lock, [this] { return !write_queue_.empty() || shutdown_flag_; });
        
        if (shutdown_flag_) {
            break;
        }
        
        BatchWriteTask task = std::move(write_queue_.front());
        write_queue_.pop();
        lock.unlock();
        
        ProcessBatchWriteTask(task);
    }
}

void RedisHotStorage::ProcessBatchWriteTask(const BatchWriteTask& task) {
    bool result = true;
    
    if (!task.ticks.empty()) {
        result = StoreBatch(task.ticks);
    } else if (!task.level2_data.empty()) {
        result = StoreBatch(task.level2_data);
    }
    
    // 设置promise结果
    const_cast<BatchWriteTask&>(task).promise.set_value(result);
}

// 统计方法实现
RedisHotStorage::StorageStats RedisHotStorage::GetStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    StorageStats stats = stats_;
    stats.active_connections = connection_pool_ ? connection_pool_->active_connections_ : 0;
    return stats;
}

void RedisHotStorage::ResetStats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = StorageStats{};
}

void RedisHotStorage::UpdateWriteStats(int64_t latency_ns) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    double latency_us = latency_ns / 1000.0;
    stats_.avg_write_latency_us = (stats_.avg_write_latency_us * stats_.total_ticks_stored + latency_us) / 
                                  (stats_.total_ticks_stored + 1);
}

void RedisHotStorage::UpdateQueryStats(int64_t latency_ns) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    double latency_us = latency_ns / 1000.0;
    stats_.avg_query_latency_us = (stats_.avg_query_latency_us * stats_.total_queries + latency_us) / 
                                  (stats_.total_queries + 1);
}

// 数据过期管理实现
bool RedisHotStorage::SetupExpirationPolicy() {
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    // 设置键空间通知，监听过期事件
    redisReply* reply = (redisReply*)redisCommand(conn, "CONFIG SET notify-keyspace-events Ex");
    bool result = (reply && reply->type != REDIS_REPLY_ERROR);
    
    if (reply) freeReplyObject(reply);
    connection_pool_->ReturnConnection(conn);
    
    return result;
}

uint64_t RedisHotStorage::CleanupExpiredData() {
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return 0;
    }
    
    // 获取过期键的数量（这是一个估算）
    redisReply* reply = (redisReply*)redisCommand(conn, "INFO keyspace");
    uint64_t expired_count = 0;
    
    if (reply && reply->type == REDIS_REPLY_STRING) {
        // 解析INFO keyspace输出来估算过期键数量
        // 这里简化处理，实际应该解析具体的统计信息
        expired_count = 0;
    }
    
    if (reply) freeReplyObject(reply);
    connection_pool_->ReturnConnection(conn);
    
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.expired_keys += expired_count;
    }
    
    return expired_count;
}

// Level2相关方法的简化实现（与Tick类似）
bool RedisHotStorage::StoreLevel2Internal(redisContext* conn, const Level2Data& level2) {
    std::string level2_data = SerializeLevel2(level2);
    std::string latest_key = GetLatestKey(level2.symbol, "level2");
    std::string ts_key = GetTimeSeriesKey(level2.symbol, "level2");
    std::string level2_key = GetLevel2Key(level2.symbol) + ":" + std::to_string(level2.timestamp_ns);
    
    int ttl = config_.hot_data_ttl_seconds;
    
    if (config_.enable_pipelining) {
        redisAppendCommand(conn, "SETEX %s %d %s", level2_key.c_str(), ttl, level2_data.c_str());
        redisAppendCommand(conn, "SETEX %s %d %s", latest_key.c_str(), ttl, level2_data.c_str());
        redisAppendCommand(conn, "ZADD %s %lld %s", ts_key.c_str(), level2.timestamp_ns, level2_key.c_str());
        redisAppendCommand(conn, "EXPIRE %s %d", ts_key.c_str(), ttl);
        
        for (int i = 0; i < 4; ++i) {
            redisReply* reply;
            if (redisGetReply(conn, (void**)&reply) != REDIS_OK) {
                return false;
            }
            if (reply) freeReplyObject(reply);
        }
    } else {
        // 逐个执行命令（非管道模式）
        redisReply* reply;
        
        reply = (redisReply*)redisCommand(conn, "SETEX %s %d %s", level2_key.c_str(), ttl, level2_data.c_str());
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            if (reply) freeReplyObject(reply);
            return false;
        }
        freeReplyObject(reply);
        
        reply = (redisReply*)redisCommand(conn, "SETEX %s %d %s", latest_key.c_str(), ttl, level2_data.c_str());
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            if (reply) freeReplyObject(reply);
            return false;
        }
        freeReplyObject(reply);
        
        reply = (redisReply*)redisCommand(conn, "ZADD %s %lld %s", ts_key.c_str(), level2.timestamp_ns, level2_key.c_str());
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            if (reply) freeReplyObject(reply);
            return false;
        }
        freeReplyObject(reply);
        
        reply = (redisReply*)redisCommand(conn, "EXPIRE %s %d", ts_key.c_str(), ttl);
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            if (reply) freeReplyObject(reply);
            return false;
        }
        freeReplyObject(reply);
    }
    
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.total_level2_stored++;
    }
    
    return true;
}

std::string RedisHotStorage::SerializeLevel2(const Level2Data& level2) {
    std::ostringstream oss;
    oss << level2.timestamp_ns << "|"
        << level2.symbol << "|"
        << level2.exchange << "|"
        << level2.sequence;
    
    // 序列化买盘数据
    oss << "|" << level2.bids.size();
    for (const auto& bid : level2.bids) {
        oss << "|" << bid.price << "," << bid.volume << "," << bid.order_count;
    }
    
    // 序列化卖盘数据
    oss << "|" << level2.asks.size();
    for (const auto& ask : level2.asks) {
        oss << "|" << ask.price << "," << ask.volume << "," << ask.order_count;
    }
    
    return oss.str();
}

bool RedisHotStorage::DeserializeLevel2(const std::string& data, Level2Data& level2) {
    std::istringstream iss(data);
    std::string token;
    std::vector<std::string> tokens;
    
    while (std::getline(iss, token, '|')) {
        tokens.push_back(token);
    }
    
    if (tokens.size() < 4) {
        return false;
    }
    
    try {
        level2.timestamp_ns = std::stoll(tokens[0]);
        level2.symbol = tokens[1];
        level2.exchange = tokens[2];
        level2.sequence = std::stoul(tokens[3]);
        
        // 解析买盘数据
        if (tokens.size() > 4) {
            size_t bid_count = std::stoul(tokens[4]);
            level2.bids.clear();
            level2.bids.reserve(bid_count);
            
            for (size_t i = 0; i < bid_count && (5 + i) < tokens.size(); ++i) {
                std::istringstream bid_iss(tokens[5 + i]);
                std::string price_str, volume_str, count_str;
                
                if (std::getline(bid_iss, price_str, ',') &&
                    std::getline(bid_iss, volume_str, ',') &&
                    std::getline(bid_iss, count_str)) {
                    PriceLevel bid;
                    bid.price = std::stod(price_str);
                    bid.volume = std::stoul(volume_str);
                    bid.order_count = std::stoul(count_str);
                    level2.bids.push_back(bid);
                }
            }
            
            // 解析卖盘数据
            size_t ask_start_index = 5 + bid_count;
            if (ask_start_index < tokens.size()) {
                size_t ask_count = std::stoul(tokens[ask_start_index]);
                level2.asks.clear();
                level2.asks.reserve(ask_count);
                
                for (size_t i = 0; i < ask_count && (ask_start_index + 1 + i) < tokens.size(); ++i) {
                    std::istringstream ask_iss(tokens[ask_start_index + 1 + i]);
                    std::string price_str, volume_str, count_str;
                    
                    if (std::getline(ask_iss, price_str, ',') &&
                        std::getline(ask_iss, volume_str, ',') &&
                        std::getline(ask_iss, count_str)) {
                        PriceLevel ask;
                        ask.price = std::stod(price_str);
                        ask.volume = std::stoul(volume_str);
                        ask.order_count = std::stoul(count_str);
                        level2.asks.push_back(ask);
                    }
                }
            }
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Level2 deserialization error: " << e.what() << std::endl;
        return false;
    }
}

bool RedisHotStorage::GetLatestLevel2Internal(redisContext* conn, const std::string& symbol, Level2Data& level2) {
    std::string latest_key = GetLatestKey(symbol, "level2");
    
    redisReply* reply = (redisReply*)redisCommand(conn, "GET %s", latest_key.c_str());
    if (!reply) {
        return false;
    }
    
    bool result = false;
    if (reply->type == REDIS_REPLY_STRING) {
        result = DeserializeLevel2(reply->str, level2);
    }
    
    freeReplyObject(reply);
    
    if (result) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.total_queries++;
    }
    
    return result;
}

bool RedisHotStorage::GetLatestLevel2(const std::string& symbol, Level2Data& level2) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    bool result = GetLatestLevel2Internal(conn, symbol, level2);
    connection_pool_->ReturnConnection(conn);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    UpdateQueryStats(latency_ns);
    
    return result;
}

QueryResult RedisHotStorage::QueryLevel2(const std::string& symbol, const QueryOptions& options) {
    QueryResult result;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return result;
    }
    
    // 使用ZRANGEBYSCORE查询时间范围内的Level2数据
    std::string ts_key = GetTimeSeriesKey(symbol, "level2");
    std::string start_score = std::to_string(options.start_time_ns);
    std::string end_score = std::to_string(options.end_time_ns);
    
    redisReply* reply = (redisReply*)redisCommand(conn, 
        "ZRANGEBYSCORE %s %s %s LIMIT 0 %d", 
        ts_key.c_str(), start_score.c_str(), end_score.c_str(), options.limit);
    
    if (reply && reply->type == REDIS_REPLY_ARRAY) {
        for (size_t i = 0; i < reply->elements; ++i) {
            std::string level2_key = reply->element[i]->str;
            
            // 获取具体的Level2数据
            redisReply* level2_reply = (redisReply*)redisCommand(conn, "GET %s", level2_key.c_str());
            if (level2_reply && level2_reply->type == REDIS_REPLY_STRING) {
                Level2Data level2;
                if (DeserializeLevel2(level2_reply->str, level2)) {
                    result.level2_data.push_back(level2);
                }
            }
            if (level2_reply) freeReplyObject(level2_reply);
        }
        
        result.has_more = (reply->elements == static_cast<size_t>(options.limit));
    }
    
    if (reply) freeReplyObject(reply);
    connection_pool_->ReturnConnection(conn);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.query_time_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    UpdateQueryStats(result.query_time_ns);
    
    return result;
}

std::unordered_map<std::string, Level2Data> RedisHotStorage::GetLatestLevel2Data(const std::vector<std::string>& symbols) {
    std::unordered_map<std::string, Level2Data> results;
    
    if (symbols.empty()) {
        return results;
    }
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return results;
    }
    
    // 构建批量查询命令
    std::vector<std::string> keys;
    for (const auto& symbol : symbols) {
        keys.push_back(GetLatestKey(symbol, "level2"));
    }
    
    // 使用MGET进行批量查询
    std::ostringstream cmd;
    cmd << "MGET";
    for (const auto& key : keys) {
        cmd << " " << key;
    }
    
    redisReply* reply = (redisReply*)redisCommand(conn, cmd.str().c_str());
    if (reply && reply->type == REDIS_REPLY_ARRAY) {
        for (size_t i = 0; i < reply->elements && i < symbols.size(); ++i) {
            if (reply->element[i]->type == REDIS_REPLY_STRING) {
                Level2Data level2;
                if (DeserializeLevel2(reply->element[i]->str, level2)) {
                    results[symbols[i]] = level2;
                }
            }
        }
    }
    
    if (reply) freeReplyObject(reply);
    connection_pool_->ReturnConnection(conn);
    
    return results;
}

std::future<bool> RedisHotStorage::StoreLevel2Async(const Level2Data& level2) {
    BatchWriteTask task;
    task.level2_data.push_back(level2);
    auto future = task.promise.get_future();
    
    {
        std::lock_guard<std::mutex> lock(write_queue_mutex_);
        if (write_queue_.size() >= MAX_QUEUE_SIZE) {
            task.promise.set_value(false);
            return future;
        }
        write_queue_.push(std::move(task));
    }
    
    write_queue_cv_.notify_one();
    return future;
}

std::future<bool> RedisHotStorage::StoreBatchAsync(const std::vector<Level2Data>& level2_data) {
    BatchWriteTask task;
    task.level2_data = level2_data;
    auto future = task.promise.get_future();
    
    {
        std::lock_guard<std::mutex> lock(write_queue_mutex_);
        if (write_queue_.size() >= MAX_QUEUE_SIZE) {
            task.promise.set_value(false);
            return future;
        }
        write_queue_.push(std::move(task));
    }
    
    write_queue_cv_.notify_one();
    return future;
}

bool RedisHotStorage::StoreBatch(const std::vector<Level2Data>& level2_data) {
    if (level2_data.empty()) {
        return true;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    bool result = true;
    for (const auto& level2 : level2_data) {
        if (!StoreLevel2Internal(conn, level2)) {
            result = false;
            break;
        }
    }
    
    connection_pool_->ReturnConnection(conn);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
    UpdateWriteStats(latency_ns);
    
    return result;
}

// 性能优化方法实现
bool RedisHotStorage::OptimizeForHotData() {
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    // 设置热数据优化参数
    std::vector<std::pair<std::string, std::string>> optimizations = {
        {"hash-max-ziplist-entries", "512"},
        {"hash-max-ziplist-value", "64"},
        {"zset-max-ziplist-entries", "128"},
        {"zset-max-ziplist-value", "64"},
        {"list-max-ziplist-size", "-2"},
        {"set-max-intset-entries", "512"}
    };
    
    bool success = true;
    for (const auto& opt : optimizations) {
        redisReply* reply = (redisReply*)redisCommand(conn, 
            "CONFIG SET %s %s", opt.first.c_str(), opt.second.c_str());
        if (!reply || reply->type == REDIS_REPLY_ERROR) {
            std::cerr << "Failed to set " << opt.first << " to " << opt.second << std::endl;
            success = false;
        }
        if (reply) freeReplyObject(reply);
    }
    
    connection_pool_->ReturnConnection(conn);
    return success;
}

bool RedisHotStorage::SetupDataSharding() {
    if (!config_.enable_cluster) {
        return true;  // 单节点模式不需要分片
    }
    
    // 集群模式下的数据分片已由Redis Cluster自动处理
    // 这里可以添加自定义的分片逻辑（如果需要）
    return true;
}

bool RedisHotStorage::ConfigureMemoryPolicy() {
    redisContext* conn = connection_pool_->GetConnection();
    if (!conn) {
        return false;
    }
    
    // 设置内存策略为LRU，适合热数据场景
    redisReply* reply = (redisReply*)redisCommand(conn, "CONFIG SET maxmemory-policy allkeys-lru");
    bool success = (reply && reply->type != REDIS_REPLY_ERROR);
    if (reply) freeReplyObject(reply);
    
    // 设置内存采样数量，提高LRU精度
    reply = (redisReply*)redisCommand(conn, "CONFIG SET maxmemory-samples 10");
    if (reply && reply->type == REDIS_REPLY_ERROR) {
        success = false;
    }
    if (reply) freeReplyObject(reply);
    
    connection_pool_->ReturnConnection(conn);
    return success;
}

// 集群支持方法实现
bool RedisHotStorage::InitializeCluster() {
    if (config_.cluster_nodes.empty()) {
        std::cerr << "No cluster nodes specified" << std::endl;
        return false;
    }
    
    // 验证集群节点连接
    for (const auto& node : config_.cluster_nodes) {
        size_t colon_pos = node.find(':');
        if (colon_pos == std::string::npos) {
            std::cerr << "Invalid cluster node format: " << node << std::endl;
            return false;
        }
        
        std::string host = node.substr(0, colon_pos);
        int port = std::stoi(node.substr(colon_pos + 1));
        
        redisContext* conn = redisConnect(host.c_str(), port);
        if (!conn || conn->err) {
            std::cerr << "Failed to connect to cluster node: " << node << std::endl;
            if (conn) redisFree(conn);
            return false;
        }
        
        // 验证节点是否在集群模式
        redisReply* reply = (redisReply*)redisCommand(conn, "CLUSTER INFO");
        bool is_cluster_enabled = false;
        if (reply && reply->type == REDIS_REPLY_STRING) {
            std::string info(reply->str);
            is_cluster_enabled = (info.find("cluster_state:ok") != std::string::npos);
        }
        if (reply) freeReplyObject(reply);
        
        redisFree(conn);
        
        if (!is_cluster_enabled) {
            std::cerr << "Node " << node << " is not in cluster mode" << std::endl;
            return false;
        }
    }
    
    std::cout << "Redis cluster initialized with " << config_.cluster_nodes.size() << " nodes" << std::endl;
    return true;
}

std::string RedisHotStorage::GetClusterNodeForKey(const std::string& key) {
    if (!config_.enable_cluster || config_.cluster_nodes.empty()) {
        return config_.host + ":" + std::to_string(config_.port);
    }
    
    // 简化的一致性哈希实现
    std::hash<std::string> hasher;
    size_t hash_value = hasher(key);
    size_t node_index = hash_value % config_.cluster_nodes.size();
    
    return config_.cluster_nodes[node_index];
}

bool RedisHotStorage::IsClusterHealthy() {
    if (!config_.enable_cluster) {
        return true;  // 单节点模式
    }
    
    int healthy_nodes = 0;
    for (const auto& node : config_.cluster_nodes) {
        size_t colon_pos = node.find(':');
        if (colon_pos == std::string::npos) continue;
        
        std::string host = node.substr(0, colon_pos);
        int port = std::stoi(node.substr(colon_pos + 1));
        
        redisContext* conn = redisConnect(host.c_str(), port);
        if (conn && !conn->err) {
            redisReply* reply = (redisReply*)redisCommand(conn, "PING");
            if (reply && reply->type == REDIS_REPLY_STATUS && 
                std::string(reply->str) == "PONG") {
                healthy_nodes++;
            }
            if (reply) freeReplyObject(reply);
        }
        if (conn) redisFree(conn);
    }
    
    // 至少一半节点健康才认为集群健康
    return healthy_nodes >= static_cast<int>(config_.cluster_nodes.size()) / 2;
}

} // namespace financial_data