@echo off
REM ClickHouse Warm Storage Cluster Setup Script
REM This script sets up a 3-node ClickHouse cluster for financial data storage

echo ========================================
echo ClickHouse Warm Storage Cluster Setup
echo ========================================

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo Step 1: Stopping existing ClickHouse containers...
docker-compose down clickhouse-1 clickhouse-2 clickhouse-3 2>nul

echo Step 2: Creating ClickHouse data directories...
if not exist "data\clickhouse-1" mkdir data\clickhouse-1
if not exist "data\clickhouse-2" mkdir data\clickhouse-2
if not exist "data\clickhouse-3" mkdir data\clickhouse-3

echo Step 3: Starting ClickHouse cluster nodes...
docker-compose up -d clickhouse-1 clickhouse-2 clickhouse-3

echo Step 4: Waiting for ClickHouse nodes to start...
timeout /t 30 /nobreak >nul

echo Step 5: Checking cluster health...
:check_health
echo Checking ClickHouse-1...
docker exec financial-clickhouse-1 clickhouse-client --query "SELECT 1" >nul 2>&1
if %errorlevel% neq 0 (
    echo ClickHouse-1 not ready, waiting...
    timeout /t 5 /nobreak >nul
    goto check_health
)

echo Checking ClickHouse-2...
docker exec financial-clickhouse-2 clickhouse-client --query "SELECT 1" >nul 2>&1
if %errorlevel% neq 0 (
    echo ClickHouse-2 not ready, waiting...
    timeout /t 5 /nobreak >nul
    goto check_health
)

echo Checking ClickHouse-3...
docker exec financial-clickhouse-3 clickhouse-client --query "SELECT 1" >nul 2>&1
if %errorlevel% neq 0 (
    echo ClickHouse-3 not ready, waiting...
    timeout /t 5 /nobreak >nul
    goto check_health
)

echo Step 6: Initializing database schema...
docker exec financial-clickhouse-1 clickhouse-client --multiquery < config/clickhouse-init.sql
if %errorlevel% neq 0 (
    echo ERROR: Failed to initialize database schema
    pause
    exit /b 1
)

echo Step 7: Verifying cluster configuration...
echo Checking cluster nodes:
docker exec financial-clickhouse-1 clickhouse-client --query "SELECT host_name, port FROM system.clusters WHERE cluster = 'financial_cluster'"

echo Step 8: Creating test data for verification...
docker exec financial-clickhouse-1 clickhouse-client --query "
USE market_data;
INSERT INTO futures_tick VALUES 
(now64(9), 'CU2409', 'SHFE', 75000.0, 100, 7500000.0, 50000, [74990.0, 74980.0], [10, 15], [75010.0, 75020.0], [8, 12], 1, 'buy_open', 75000.0, 74950.0, 74980.0, 49900);
"

echo Step 9: Verifying data insertion...
docker exec financial-clickhouse-1 clickhouse-client --query "
USE market_data;
SELECT count() as record_count, symbol, exchange FROM futures_tick GROUP BY symbol, exchange;
"

echo Step 10: Setting up monitoring queries...
echo Creating performance monitoring views...
docker exec financial-clickhouse-1 clickhouse-client --query "
USE market_data;
CREATE VIEW IF NOT EXISTS performance_metrics AS
SELECT 
    toStartOfMinute(now()) as timestamp,
    'futures_tick' as table_name,
    count() as total_records,
    max(timestamp) as latest_timestamp,
    min(timestamp) as earliest_timestamp
FROM futures_tick
WHERE timestamp >= now() - INTERVAL 1 HOUR;
"

echo Step 11: Configuring data retention policies...
docker exec financial-clickhouse-1 clickhouse-client --query "
USE market_data;
-- Verify TTL settings are applied
SELECT name, engine, partition_key, sorting_key, ttl_expression 
FROM system.tables 
WHERE database = 'market_data' AND name LIKE '%_tick';
"

echo Step 12: Testing cluster replication...
echo Inserting test data on node 1...
docker exec financial-clickhouse-1 clickhouse-client --query "
USE market_data;
INSERT INTO futures_tick VALUES 
(now64(9), 'AL2409', 'SHFE', 18500.0, 200, 3700000.0, 75000, [18490.0, 18480.0], [20, 25], [18510.0, 18520.0], [18, 22], 2, 'sell_close', 18500.0, 18450.0, 18480.0, 74900);
"

echo Checking replication on node 2...
timeout /t 5 /nobreak >nul
docker exec financial-clickhouse-2 clickhouse-client --query "
USE market_data;
SELECT count() as replicated_records FROM futures_tick WHERE symbol = 'AL2409';
"

echo Step 13: Setting up backup configuration...
echo Creating backup directory...
if not exist "backups\clickhouse" mkdir backups\clickhouse

echo Step 14: Performance optimization...
echo Optimizing table partitions...
docker exec financial-clickhouse-1 clickhouse-client --query "
USE market_data;
OPTIMIZE TABLE futures_tick_local;
OPTIMIZE TABLE stock_tick_local;
OPTIMIZE TABLE options_tick_local;
OPTIMIZE TABLE forex_tick_local;
"

echo Step 15: Final cluster status check...
echo Cluster Status:
docker-compose ps clickhouse-1 clickhouse-2 clickhouse-3

echo Database Status:
docker exec financial-clickhouse-1 clickhouse-client --query "
SELECT 
    database,
    count() as tables,
    sum(total_rows) as total_rows,
    formatReadableSize(sum(total_bytes)) as total_size
FROM system.tables 
WHERE database IN ('market_data', 'metadata')
GROUP BY database;
"

echo Memory Usage:
docker exec financial-clickhouse-1 clickhouse-client --query "
SELECT 
    formatReadableSize(value) as memory_usage
FROM system.asynchronous_metrics 
WHERE metric = 'MemoryTracking';
"

echo ========================================
echo ClickHouse Cluster Setup Complete!
echo ========================================
echo.
echo Cluster Information:
echo - Node 1: localhost:8123 (HTTP), localhost:9000 (TCP)
echo - Node 2: localhost:8124 (HTTP), localhost:9001 (TCP)  
echo - Node 3: localhost:8125 (HTTP), localhost:9002 (TCP)
echo.
echo Database: market_data
echo Username: admin
echo Password: password123
echo.
echo You can now connect to the cluster and start using warm storage!
echo.
echo To test the connection:
echo docker exec financial-clickhouse-1 clickhouse-client --user admin --password password123
echo.
echo To view cluster status:
echo docker exec financial-clickhouse-1 clickhouse-client --query "SELECT * FROM system.clusters WHERE cluster = 'financial_cluster'"
echo.

pause