#include "jwt_auth.h"
#include <openssl/hmac.h>
#include <openssl/sha.h>
#include <openssl/rand.h>
#include <json/json.h>
#include <base64.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>

namespace financial_data {
namespace security {

JWTAuth::JW<PERSON>uth(const JWTConfig& config)
    : config_(config), initialized_(false) {
}

JWTAuth::~JWTAuth() {
    if (initialized_) {
        SaveUsers();
    }
}

bool JWTAuth::Initialize() {
    if (initialized_) {
        return true;
    }
    
    // 加载用户数据
    LoadUsers();
    
    // 创建默认管理员用户（如果不存在）
    if (users_.find("admin") == users_.end()) {
        UserInfo admin_user;
        admin_user.user_id = "admin";
        admin_user.username = "admin";
        admin_user.email = "<EMAIL>";
        admin_user.roles = {"admin", "user"};
        admin_user.created_at = std::chrono::system_clock::now();
        admin_user.mfa_enabled = false;
        
        CreateUser(admin_user, "admin123");
    }
    
    initialized_ = true;
    return true;
}

TokenInfo JWTAuth::Authenticate(const std::string& username, const std::string& password) {
    TokenInfo token_info;
    
    // 查找用户
    auto user_it = std::find_if(users_.begin(), users_.end(),
        [&username](const auto& pair) {
            return pair.second.username == username;
        });
    
    if (user_it == users_.end()) {
        std::cerr << "User not found: " << username << std::endl;
        return token_info;
    }
    
    const UserInfo& user = user_it->second;
    
    // 验证密码
    auto pass_it = password_hashes_.find(user.user_id);
    if (pass_it == password_hashes_.end() || !VerifyPassword(password, pass_it->second)) {
        std::cerr << "Invalid password for user: " << username << std::endl;
        return token_info;
    }
    
    // 如果启用了MFA，需要额外验证
    if (user.mfa_enabled) {
        // 这里返回一个临时令牌，需要MFA验证
        token_info.token = "mfa_required";
        token_info.user_id = user.user_id;
        return token_info;
    }
    
    // 生成访问令牌
    std::vector<std::string> scopes = {"read", "write"};
    token_info.token = GenerateToken(user, scopes);
    token_info.user_id = user.user_id;
    token_info.expires_at = std::chrono::system_clock::now() + config_.token_expiry;
    token_info.scopes = scopes;
    
    // 生成刷新令牌
    std::unordered_map<std::string, std::string> refresh_claims;
    refresh_claims["user_id"] = user.user_id;
    refresh_claims["type"] = "refresh";
    refresh_claims["exp"] = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
        (std::chrono::system_clock::now() + config_.refresh_expiry).time_since_epoch()).count());
    
    token_info.refresh_token = CreateJWT(refresh_claims);
    token_info.refresh_expires_at = std::chrono::system_clock::now() + config_.refresh_expiry;
    
    // 保存活跃令牌
    active_tokens_[token_info.token] = token_info;
    
    // 更新最后登录时间
    users_[user.user_id].last_login = std::chrono::system_clock::now();
    
    return token_info;
}

bool JWTAuth::VerifyMFA(const std::string& user_id, const std::string& mfa_code) {
    auto user_it = users_.find(user_id);
    if (user_it == users_.end() || !user_it->second.mfa_enabled) {
        return false;
    }
    
    return VerifyTOTP(user_it->second.mfa_secret, mfa_code);
}

bool JWTAuth::VerifyToken(const std::string& token, UserInfo& user_info) {
    // 检查令牌是否被撤销
    if (IsTokenRevoked(token)) {
        return false;
    }
    
    // 验证JWT签名和格式
    if (!ValidateJWT(token)) {
        return false;
    }
    
    // 解析令牌
    std::unordered_map<std::string, std::string> claims;
    if (!ParseToken(token, claims)) {
        return false;
    }
    
    // 检查过期时间
    auto exp_it = claims.find("exp");
    if (exp_it != claims.end()) {
        uint64_t exp_time = std::stoull(exp_it->second);
        uint64_t current_time = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        if (current_time >= exp_time) {
            return false;
        }
    }
    
    // 获取用户信息
    auto user_id_it = claims.find("user_id");
    if (user_id_it != claims.end()) {
        auto user_it = users_.find(user_id_it->second);
        if (user_it != users_.end()) {
            user_info = user_it->second;
            return true;
        }
    }
    
    return false;
}

TokenInfo JWTAuth::RefreshToken(const std::string& refresh_token) {
    TokenInfo new_token_info;
    
    // 验证刷新令牌
    std::unordered_map<std::string, std::string> claims;
    if (!ParseToken(refresh_token, claims) || !ValidateJWT(refresh_token)) {
        return new_token_info;
    }
    
    // 检查令牌类型
    auto type_it = claims.find("type");
    if (type_it == claims.end() || type_it->second != "refresh") {
        return new_token_info;
    }
    
    // 检查过期时间
    auto exp_it = claims.find("exp");
    if (exp_it != claims.end()) {
        uint64_t exp_time = std::stoull(exp_it->second);
        uint64_t current_time = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        if (current_time >= exp_time) {
            return new_token_info;
        }
    }
    
    // 获取用户ID
    auto user_id_it = claims.find("user_id");
    if (user_id_it == claims.end()) {
        return new_token_info;
    }
    
    auto user_it = users_.find(user_id_it->second);
    if (user_it == users_.end()) {
        return new_token_info;
    }
    
    // 生成新的访问令牌
    std::vector<std::string> scopes = {"read", "write"};
    new_token_info.token = GenerateToken(user_it->second, scopes);
    new_token_info.user_id = user_it->second.user_id;
    new_token_info.expires_at = std::chrono::system_clock::now() + config_.token_expiry;
    new_token_info.scopes = scopes;
    new_token_info.refresh_token = refresh_token;
    new_token_info.refresh_expires_at = std::chrono::system_clock::now() + config_.refresh_expiry;
    
    // 保存新令牌
    active_tokens_[new_token_info.token] = new_token_info;
    
    return new_token_info;
}

bool JWTAuth::RevokeToken(const std::string& token) {
    revoked_tokens_[token] = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count());
    
    active_tokens_.erase(token);
    return true;
}

std::string JWTAuth::GenerateToken(const UserInfo& user_info, const std::vector<std::string>& scopes) {
    std::unordered_map<std::string, std::string> claims;
    
    claims["iss"] = config_.issuer;
    claims["user_id"] = user_info.user_id;
    claims["username"] = user_info.username;
    claims["email"] = user_info.email;
    
    // 添加角色
    Json::Value roles_json(Json::arrayValue);
    for (const auto& role : user_info.roles) {
        roles_json.append(role);
    }
    Json::StreamWriterBuilder builder;
    claims["roles"] = Json::writeString(builder, roles_json);
    
    // 添加权限范围
    Json::Value scopes_json(Json::arrayValue);
    for (const auto& scope : scopes) {
        scopes_json.append(scope);
    }
    claims["scopes"] = Json::writeString(builder, scopes_json);
    
    // 设置时间戳
    uint64_t now = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    uint64_t exp = std::chrono::duration_cast<std::chrono::seconds>(
        (std::chrono::system_clock::now() + config_.token_expiry).time_since_epoch()).count();
    
    claims["iat"] = std::to_string(now);
    claims["exp"] = std::to_string(exp);
    
    return CreateJWT(claims);
}

bool JWTAuth::ParseToken(const std::string& token, std::unordered_map<std::string, std::string>& claims) {
    // JWT格式: header.payload.signature
    size_t first_dot = token.find('.');
    size_t second_dot = token.find('.', first_dot + 1);
    
    if (first_dot == std::string::npos || second_dot == std::string::npos) {
        return false;
    }
    
    std::string payload = token.substr(first_dot + 1, second_dot - first_dot - 1);
    
    // Base64解码
    std::string decoded_payload;
    // 这里需要实现Base64解码，简化处理
    // decoded_payload = base64_decode(payload);
    
    // 解析JSON
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(decoded_payload, root)) {
        return false;
    }
    
    // 提取声明
    for (const auto& key : root.getMemberNames()) {
        claims[key] = root[key].asString();
    }
    
    return true;
}

std::string JWTAuth::CreateJWT(const std::unordered_map<std::string, std::string>& claims) {
    // 创建头部
    Json::Value header;
    header["alg"] = "HS256";
    header["typ"] = "JWT";
    
    Json::StreamWriterBuilder builder;
    std::string header_str = Json::writeString(builder, header);
    
    // 创建载荷
    Json::Value payload;
    for (const auto& claim : claims) {
        payload[claim.first] = claim.second;
    }
    std::string payload_str = Json::writeString(builder, payload);
    
    // Base64编码
    // std::string encoded_header = base64_encode(header_str);
    // std::string encoded_payload = base64_encode(payload_str);
    std::string encoded_header = header_str; // 简化处理
    std::string encoded_payload = payload_str; // 简化处理
    
    // 创建签名
    std::string message = encoded_header + "." + encoded_payload;
    unsigned char signature[EVP_MAX_MD_SIZE];
    unsigned int signature_len;
    
    HMAC(EVP_sha256(), config_.secret_key.c_str(), config_.secret_key.length(),
         reinterpret_cast<const unsigned char*>(message.c_str()), message.length(),
         signature, &signature_len);
    
    // Base64编码签名
    // std::string encoded_signature = base64_encode(std::string(reinterpret_cast<char*>(signature), signature_len));
    std::string encoded_signature(reinterpret_cast<char*>(signature), signature_len); // 简化处理
    
    return message + "." + encoded_signature;
}

bool JWTAuth::ValidateJWT(const std::string& token) {
    size_t first_dot = token.find('.');
    size_t second_dot = token.find('.', first_dot + 1);
    
    if (first_dot == std::string::npos || second_dot == std::string::npos) {
        return false;
    }
    
    std::string message = token.substr(0, second_dot);
    std::string signature = token.substr(second_dot + 1);
    
    // 重新计算签名
    unsigned char expected_signature[EVP_MAX_MD_SIZE];
    unsigned int signature_len;
    
    HMAC(EVP_sha256(), config_.secret_key.c_str(), config_.secret_key.length(),
         reinterpret_cast<const unsigned char*>(message.c_str()), message.length(),
         expected_signature, &signature_len);
    
    std::string expected_signature_str(reinterpret_cast<char*>(expected_signature), signature_len);
    
    return signature == expected_signature_str;
}

bool JWTAuth::CreateUser(const UserInfo& user_info, const std::string& password) {
    if (users_.find(user_info.user_id) != users_.end()) {
        return false; // 用户已存在
    }
    
    users_[user_info.user_id] = user_info;
    
    // 哈希密码
    std::string salt = GenerateSalt();
    password_hashes_[user_info.user_id] = HashPassword(password, salt);
    
    return true;
}

std::string JWTAuth::HashPassword(const std::string& password, const std::string& salt) {
    std::string salted_password = password + salt;
    
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256(reinterpret_cast<const unsigned char*>(salted_password.c_str()),
           salted_password.length(), hash);
    
    std::ostringstream oss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }
    
    return salt + ":" + oss.str();
}

std::string JWTAuth::GenerateSalt() {
    unsigned char salt[16];
    RAND_bytes(salt, sizeof(salt));
    
    std::ostringstream oss;
    for (int i = 0; i < 16; ++i) {
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(salt[i]);
    }
    
    return oss.str();
}

bool JWTAuth::VerifyPassword(const std::string& password, const std::string& hash) {
    size_t colon_pos = hash.find(':');
    if (colon_pos == std::string::npos) {
        return false;
    }
    
    std::string salt = hash.substr(0, colon_pos);
    std::string expected_hash = hash.substr(colon_pos + 1);
    
    std::string computed_hash = HashPassword(password, salt);
    return computed_hash.substr(colon_pos + 1) == expected_hash;
}

std::string JWTAuth::GenerateTOTP(const std::string& secret, uint64_t timestamp) {
    // 简化的TOTP实现
    uint64_t time_step = timestamp / 30; // 30秒时间窗口
    
    unsigned char hmac_result[EVP_MAX_MD_SIZE];
    unsigned int hmac_len;
    
    HMAC(EVP_sha1(), secret.c_str(), secret.length(),
         reinterpret_cast<const unsigned char*>(&time_step), sizeof(time_step),
         hmac_result, &hmac_len);
    
    int offset = hmac_result[hmac_len - 1] & 0x0f;
    int code = ((hmac_result[offset] & 0x7f) << 24) |
               ((hmac_result[offset + 1] & 0xff) << 16) |
               ((hmac_result[offset + 2] & 0xff) << 8) |
               (hmac_result[offset + 3] & 0xff);
    
    code %= 1000000; // 6位数字
    
    std::ostringstream oss;
    oss << std::setw(6) << std::setfill('0') << code;
    return oss.str();
}

bool JWTAuth::VerifyTOTP(const std::string& secret, const std::string& code) {
    uint64_t current_time = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // 检查当前时间窗口和前后各一个时间窗口
    for (int i = -1; i <= 1; ++i) {
        uint64_t test_time = current_time + (i * 30);
        std::string expected_code = GenerateTOTP(secret, test_time);
        if (expected_code == code) {
            return true;
        }
    }
    
    return false;
}

bool JWTAuth::IsTokenRevoked(const std::string& token) {
    return revoked_tokens_.find(token) != revoked_tokens_.end();
}

bool JWTAuth::LoadUsers() {
    // 简化实现，实际应该从数据库或文件加载
    return true;
}

bool JWTAuth::SaveUsers() {
    // 简化实现，实际应该保存到数据库或文件
    return true;
}

} // namespace security
} // namespace financial_data