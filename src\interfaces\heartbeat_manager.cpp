#include "heartbeat_manager.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace financial_data {
namespace interfaces {

// HeartbeatManager类实现 - 心跳检测和自动重连机制
HeartbeatManager::HeartbeatManager(const HeartbeatConfig& config) 
    : config_(config), logger_(spdlog::get("heartbeat_manager")) {
    if (!logger_) {
        logger_ = spdlog::default_logger();
    }
    
    logger_->info("HeartbeatManager initialized with interval: {}ms, timeout: {}ms",
                 config_.heartbeat_interval_ms, config_.heartbeat_timeout_ms);
}

HeartbeatManager::~HeartbeatManager() {
    Stop();
}

bool HeartbeatManager::Start() {
    if (running_.load()) {
        logger_->warn("HeartbeatManager is already running");
        return false;
    }
    
    running_ = true;
    
    try {
        // 启动心跳检查线程
        heartbeat_thread_ = std::thread(&HeartbeatManager::HeartbeatLoop, this);
        
        // 启动统计线程
        if (config_.enable_statistics) {
            statistics_thread_ = std::thread(&HeartbeatManager::StatisticsLoop, this);
        }
        
        // 启动清理线程
        if (config_.enable_auto_cleanup) {
            cleanup_thread_ = std::thread(&HeartbeatManager::CleanupLoop, this);
        }
        
        logger_->info("HeartbeatManager started successfully");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to start HeartbeatManager: {}", e.what());
        running_ = false;
        return false;
    }
}

void HeartbeatManager::Stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    cv_.notify_all();
    
    // 等待线程结束
    if (heartbeat_thread_.joinable()) {
        heartbeat_thread_.join();
    }
    
    if (statistics_thread_.joinable()) {
        statistics_thread_.join();
    }
    
    if (cleanup_thread_.joinable()) {
        cleanup_thread_.join();
    }
    
    logger_->info("HeartbeatManager stopped");
}

bool HeartbeatManager::AddClient(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    
    if (client_heartbeats_.find(client_id) != client_heartbeats_.end()) {
        logger_->warn("Client {} already exists in heartbeat manager", client_id);
        return false;
    }
    
    auto info = std::make_shared<ClientHeartbeatInfo>();
    info->client_id = client_id;
    client_heartbeats_[client_id] = info;
    
    statistics_.total_clients++;
    statistics_.active_clients++;
    
    TriggerEvent(HeartbeatEvent::CLIENT_CONNECTED, client_id, *info);
    
    logger_->debug("Added client {} to heartbeat manager", client_id);
    return true;
}

bool HeartbeatManager::RemoveClient(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    
    auto it = client_heartbeats_.find(client_id);
    if (it == client_heartbeats_.end()) {
        logger_->warn("Client {} not found in heartbeat manager", client_id);
        return false;
    }
    
    auto info = it->second;
    info->status = HeartbeatStatus::DISCONNECTED;
    
    // 更新统计
    switch (info->status) {
        case HeartbeatStatus::ACTIVE:
            if (statistics_.active_clients > 0) statistics_.active_clients--;
            break;
        case HeartbeatStatus::WARNING:
            if (statistics_.warning_clients > 0) statistics_.warning_clients--;
            break;
        case HeartbeatStatus::TIMEOUT:
            if (statistics_.timeout_clients > 0) statistics_.timeout_clients--;
            break;
        default:
            break;
    }
    
    statistics_.disconnected_clients++;
    
    TriggerEvent(HeartbeatEvent::CLIENT_DISCONNECTED, client_id, *info);
    
    // 如果不启用自动清理，立即删除
    if (!config_.enable_auto_cleanup) {
        client_heartbeats_.erase(it);
    }
    
    logger_->debug("Removed client {} from heartbeat manager", client_id);
    return true;
}

bool HeartbeatManager::UpdateHeartbeat(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    
    auto it = client_heartbeats_.find(client_id);
    if (it == client_heartbeats_.end()) {
        logger_->warn("Client {} not found for heartbeat update", client_id);
        return false;
    }
    
    auto info = it->second;
    auto old_status = info->status;
    
    info->UpdateHeartbeat();
    statistics_.total_heartbeats++;
    
    // 检查状态变化
    auto new_status = CalculateClientStatus(*info);
    if (new_status != old_status) {
        // 更新统计计数
        switch (old_status) {
            case HeartbeatStatus::ACTIVE:
                if (statistics_.active_clients > 0) statistics_.active_clients--;
                break;
            case HeartbeatStatus::WARNING:
                if (statistics_.warning_clients > 0) statistics_.warning_clients--;
                break;
            case HeartbeatStatus::TIMEOUT:
                if (statistics_.timeout_clients > 0) statistics_.timeout_clients--;
                break;
            default:
                break;
        }
        
        switch (new_status) {
            case HeartbeatStatus::ACTIVE:
                statistics_.active_clients++;
                break;
            case HeartbeatStatus::WARNING:
                statistics_.warning_clients++;
                break;
            case HeartbeatStatus::TIMEOUT:
                statistics_.timeout_clients++;
                break;
            default:
                break;
        }
        
        info->status = new_status;
        TriggerEvent(HeartbeatEvent::STATUS_CHANGED, client_id, *info);
    }
    
    TriggerEvent(HeartbeatEvent::HEARTBEAT_RECEIVED, client_id, *info);
    
    logger_->trace("Updated heartbeat for client {}", client_id);
    return true;
}

bool HeartbeatManager::HandlePong(const std::string& client_id, uint64_t ping_timestamp) {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    
    auto it = client_heartbeats_.find(client_id);
    if (it == client_heartbeats_.end()) {
        logger_->warn("Client {} not found for pong handling", client_id);
        return false;
    }
    
    auto info = it->second;
    uint64_t current_time = GetCurrentTimestampMs();
    uint64_t rtt_ms = current_time - ping_timestamp;
    
    info->UpdatePong(rtt_ms);
    statistics_.total_pongs++;
    
    // 更新全局RTT统计
    uint64_t current_min = statistics_.min_rtt_ms.load();
    while (rtt_ms < current_min && !statistics_.min_rtt_ms.compare_exchange_weak(current_min, rtt_ms)) {
        current_min = statistics_.min_rtt_ms.load();
    }
    
    uint64_t current_max = statistics_.max_rtt_ms.load();
    while (rtt_ms > current_max && !statistics_.max_rtt_ms.compare_exchange_weak(current_max, rtt_ms)) {
        current_max = statistics_.max_rtt_ms.load();
    }
    
    uint64_t current_avg = statistics_.avg_rtt_ms.load();
    uint64_t new_avg = (current_avg * 7 + rtt_ms) / 8;
    statistics_.avg_rtt_ms = new_avg;
    
    TriggerEvent(HeartbeatEvent::PONG_RECEIVED, client_id, *info);
    
    logger_->trace("Handled pong from client {}, RTT: {}ms", client_id, rtt_ms);
    return true;
}

std::shared_ptr<ClientHeartbeatInfo> HeartbeatManager::GetClientInfo(const std::string& client_id) const {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    auto it = client_heartbeats_.find(client_id);
    return (it != client_heartbeats_.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<ClientHeartbeatInfo>> HeartbeatManager::GetAllClientInfo() const {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    std::vector<std::shared_ptr<ClientHeartbeatInfo>> result;
    result.reserve(client_heartbeats_.size());
    
    for (const auto& [client_id, info] : client_heartbeats_) {
        result.push_back(info);
    }
    
    return result;
}

std::vector<std::string> HeartbeatManager::GetClientsByStatus(HeartbeatStatus status) const {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    std::vector<std::string> result;
    
    for (const auto& [client_id, info] : client_heartbeats_) {
        if (info->status == status) {
            result.push_back(client_id);
        }
    }
    
    return result;
}

bool HeartbeatManager::IsClientActive(const std::string& client_id) const {
    auto info = GetClientInfo(client_id);
    return info && info->status == HeartbeatStatus::ACTIVE;
}

HeartbeatStatus HeartbeatManager::GetClientStatus(const std::string& client_id) const {
    auto info = GetClientInfo(client_id);
    return info ? info->status : HeartbeatStatus::DISCONNECTED;
}

void HeartbeatManager::CheckAllClients() {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    
    for (const auto& [client_id, info] : client_heartbeats_) {
        CheckClientStatus(client_id, info);
    }
}

void HeartbeatManager::AddEventHandler(HeartbeatEventHandler handler) {
    std::lock_guard<std::mutex> lock(event_handlers_mutex_);
    event_handlers_.push_back(handler);
}

void HeartbeatManager::RemoveEventHandler(HeartbeatEventHandler handler) {
    std::lock_guard<std::mutex> lock(event_handlers_mutex_);
    // 注意：这里简化实现，实际应该比较函数对象
    // event_handlers_.erase(std::remove(event_handlers_.begin(), event_handlers_.end(), handler), event_handlers_.end());
}

bool HeartbeatManager::UpdateConfig(const HeartbeatConfig& config) {
    config_ = config;
    logger_->info("HeartbeatManager config updated");
    return true;
}

void HeartbeatManager::ResetStatistics() {
    statistics_.Reset();
}

std::string HeartbeatManager::GetStatisticsSummary() const {
    std::ostringstream oss;
    auto stats = statistics_;
    
    oss << "HeartbeatManager Statistics:\n";
    oss << "  Total Clients: " << stats.total_clients.load() << "\n";
    oss << "  Active Clients: " << stats.active_clients.load() << "\n";
    oss << "  Warning Clients: " << stats.warning_clients.load() << "\n";
    oss << "  Timeout Clients: " << stats.timeout_clients.load() << "\n";
    oss << "  Disconnected Clients: " << stats.disconnected_clients.load() << "\n";
    oss << "  Active Rate: " << std::fixed << std::setprecision(2) 
        << (stats.GetActiveRate() * 100) << "%\n";
    oss << "  Timeout Rate: " << std::fixed << std::setprecision(2) 
        << (stats.GetTimeoutRate() * 100) << "%\n";
    oss << "  Total Heartbeats: " << stats.total_heartbeats.load() << "\n";
    oss << "  Total Pings: " << stats.total_pings.load() << "\n";
    oss << "  Total Pongs: " << stats.total_pongs.load() << "\n";
    oss << "  Pong Rate: " << std::fixed << std::setprecision(2) 
        << (stats.GetPongRate() * 100) << "%\n";
    oss << "  Average RTT: " << stats.avg_rtt_ms.load() << "ms\n";
    oss << "  Min RTT: " << stats.min_rtt_ms.load() << "ms\n";
    oss << "  Max RTT: " << stats.max_rtt_ms.load() << "ms\n";
    
    return oss.str();
}

std::string HeartbeatManager::GetClientSummary() const {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    std::ostringstream oss;
    
    oss << "Client Summary (" << client_heartbeats_.size() << " clients):\n";
    
    for (const auto& [client_id, info] : client_heartbeats_) {
        oss << "  " << client_id << ": ";
        
        switch (info->status) {
            case HeartbeatStatus::ACTIVE:
                oss << "ACTIVE";
                break;
            case HeartbeatStatus::WARNING:
                oss << "WARNING";
                break;
            case HeartbeatStatus::TIMEOUT:
                oss << "TIMEOUT";
                break;
            case HeartbeatStatus::DISCONNECTED:
                oss << "DISCONNECTED";
                break;
        }
        
        oss << " (last: " << info->GetTimeSinceLastHeartbeat().count() << "ms ago, ";
        oss << "RTT: " << info->avg_rtt_ms.load() << "ms, ";
        oss << "HB: " << info->heartbeat_count.load() << ")\n";
    }
    
    return oss.str();
}

size_t HeartbeatManager::CleanupDisconnectedClients() {
    std::lock_guard<std::mutex> lock(heartbeats_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    auto retention_duration = std::chrono::milliseconds(config_.disconnected_retention_ms);
    
    size_t cleaned_count = 0;
    auto it = client_heartbeats_.begin();
    
    while (it != client_heartbeats_.end()) {
        if (it->second->status == HeartbeatStatus::DISCONNECTED &&
            (now - it->second->last_heartbeat) > retention_duration) {
            
            logger_->debug("Cleaning up disconnected client: {}", it->first);
            it = client_heartbeats_.erase(it);
            cleaned_count++;
        } else {
            ++it;
        }
    }
    
    if (cleaned_count > 0) {
        logger_->info("Cleaned up {} disconnected clients", cleaned_count);
    }
    
    return cleaned_count;
}

HeartbeatManager::HealthStatus HeartbeatManager::GetHealthStatus() const {
    HealthStatus status;
    auto stats = statistics_;
    
    status.active_clients = stats.active_clients.load();
    status.timeout_clients = stats.timeout_clients.load();
    status.active_rate = stats.GetActiveRate();
    status.timeout_rate = stats.GetTimeoutRate();
    
    // 判断整体健康状态
    if (status.active_rate >= 0.9 && status.timeout_rate <= 0.1) {
        status.overall_healthy = true;
        status.status_message = "Healthy";
    } else if (status.active_rate >= 0.7 && status.timeout_rate <= 0.3) {
        status.overall_healthy = true;
        status.status_message = "Warning";
    } else {
        status.overall_healthy = false;
        status.status_message = "Unhealthy";
    }
    
    return status;
}

void HeartbeatManager::HeartbeatLoop() {
    logger_->info("Heartbeat check loop started");
    
    while (running_.load()) {
        try {
            CheckAllClients();
            
            // 发送Ping消息
            if (config_.enable_ping_pong && ping_sender_) {
                std::lock_guard<std::mutex> lock(heartbeats_mutex_);
                auto now = std::chrono::steady_clock::now();
                auto ping_interval = std::chrono::milliseconds(config_.ping_interval_ms);
                
                for (const auto& [client_id, info] : client_heartbeats_) {
                    if (info->status == HeartbeatStatus::ACTIVE &&
                        (now - info->last_ping_sent) > ping_interval) {
                        SendPing(client_id, info);
                    }
                }
            }
            
        } catch (const std::exception& e) {
            logger_->error("Error in heartbeat loop: {}", e.what());
        }
        
        // 等待下次检查
        std::unique_lock<std::mutex> lock(cv_mutex_);
        cv_.wait_for(lock, std::chrono::milliseconds(config_.check_interval_ms),
                    [this] { return !running_.load(); });
    }
    
    logger_->info("Heartbeat check loop stopped");
}

void HeartbeatManager::StatisticsLoop() {
    logger_->info("Statistics loop started");
    
    while (running_.load()) {
        try {
            UpdateStatistics();
            
            if (logger_->should_log(spdlog::level::debug)) {
                logger_->debug("Heartbeat statistics updated: {}", GetStatisticsSummary());
            }
            
        } catch (const std::exception& e) {
            logger_->error("Error in statistics loop: {}", e.what());
        }
        
        // 等待下次统计
        std::unique_lock<std::mutex> lock(cv_mutex_);
        cv_.wait_for(lock, std::chrono::milliseconds(config_.statistics_interval_ms),
                    [this] { return !running_.load(); });
    }
    
    logger_->info("Statistics loop stopped");
}

void HeartbeatManager::CleanupLoop() {
    logger_->info("Cleanup loop started");
    
    while (running_.load()) {
        try {
            size_t cleaned = CleanupDisconnectedClients();
            if (cleaned > 0) {
                logger_->info("Cleaned up {} disconnected clients", cleaned);
            }
            
        } catch (const std::exception& e) {
            logger_->error("Error in cleanup loop: {}", e.what());
        }
        
        // 等待下次清理
        std::unique_lock<std::mutex> lock(cv_mutex_);
        cv_.wait_for(lock, std::chrono::milliseconds(config_.cleanup_interval_ms),
                    [this] { return !running_.load(); });
    }
    
    logger_->info("Cleanup loop stopped");
}

void HeartbeatManager::CheckClientStatus(const std::string& client_id, std::shared_ptr<ClientHeartbeatInfo> info) {
    auto now = std::chrono::steady_clock::now();
    auto time_since_heartbeat = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - info->last_heartbeat).count();
    
    HeartbeatStatus old_status = info->status;
    HeartbeatStatus new_status = old_status;
    
    if (time_since_heartbeat >= config_.heartbeat_timeout_ms) {
        new_status = HeartbeatStatus::TIMEOUT;
        if (old_status != HeartbeatStatus::TIMEOUT) {
            HandleClientTimeout(client_id, info);
        }
    } else if (time_since_heartbeat >= config_.warning_threshold_ms) {
        new_status = HeartbeatStatus::WARNING;
    } else {
        new_status = HeartbeatStatus::ACTIVE;
    }
    
    if (new_status != old_status) {
        // 更新统计计数
        switch (old_status) {
            case HeartbeatStatus::ACTIVE:
                if (statistics_.active_clients > 0) statistics_.active_clients--;
                break;
            case HeartbeatStatus::WARNING:
                if (statistics_.warning_clients > 0) statistics_.warning_clients--;
                break;
            case HeartbeatStatus::TIMEOUT:
                if (statistics_.timeout_clients > 0) statistics_.timeout_clients--;
                break;
            default:
                break;
        }
        
        switch (new_status) {
            case HeartbeatStatus::ACTIVE:
                statistics_.active_clients++;
                break;
            case HeartbeatStatus::WARNING:
                statistics_.warning_clients++;
                break;
            case HeartbeatStatus::TIMEOUT:
                statistics_.timeout_clients++;
                break;
            default:
                break;
        }
        
        info->status = new_status;
        TriggerEvent(HeartbeatEvent::STATUS_CHANGED, client_id, *info);
        
        logger_->debug("Client {} status changed from {} to {}", client_id, 
                      static_cast<int>(old_status), static_cast<int>(new_status));
    }
}

void HeartbeatManager::SendPing(const std::string& client_id, std::shared_ptr<ClientHeartbeatInfo> info) {
    if (ping_sender_) {
        uint64_t timestamp = GetCurrentTimestampMs();
        if (ping_sender_(client_id, timestamp)) {
            info->UpdatePing();
            statistics_.total_pings++;
            TriggerEvent(HeartbeatEvent::PING_SENT, client_id, *info);
            logger_->trace("Sent ping to client {}", client_id);
        } else {
            logger_->warn("Failed to send ping to client {}", client_id);
        }
    }
}

void HeartbeatManager::HandleClientTimeout(const std::string& client_id, std::shared_ptr<ClientHeartbeatInfo> info) {
    info->timeout_count++;
    statistics_.total_timeouts++;
    
    TriggerEvent(HeartbeatEvent::HEARTBEAT_TIMEOUT, client_id, *info);
    
    logger_->warn("Client {} heartbeat timeout ({}ms since last heartbeat)", 
                 client_id, info->GetTimeSinceLastHeartbeat().count());
    
    // 如果配置了断开连接处理器，调用它
    if (disconnect_handler_) {
        disconnect_handler_(client_id, "Heartbeat timeout");
    }
}

void HeartbeatManager::UpdateStatistics() {
    // 统计信息在其他方法中实时更新，这里可以做一些额外的计算
    // 例如计算平均值、趋势等
}

void HeartbeatManager::TriggerEvent(HeartbeatEvent event, const std::string& client_id, const ClientHeartbeatInfo& info) {
    std::lock_guard<std::mutex> lock(event_handlers_mutex_);
    
    for (const auto& handler : event_handlers_) {
        try {
            handler(event, client_id, info);
        } catch (const std::exception& e) {
            logger_->error("Error in heartbeat event handler: {}", e.what());
        }
    }
}

HeartbeatStatus HeartbeatManager::CalculateClientStatus(const ClientHeartbeatInfo& info) const {
    auto time_since_heartbeat = info.GetTimeSinceLastHeartbeat().count();
    
    if (time_since_heartbeat >= config_.heartbeat_timeout_ms) {
        return HeartbeatStatus::TIMEOUT;
    } else if (time_since_heartbeat >= config_.warning_threshold_ms) {
        return HeartbeatStatus::WARNING;
    } else {
        return HeartbeatStatus::ACTIVE;
    }
}

uint64_t HeartbeatManager::GetCurrentTimestampMs() const {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
}

} // namespace interfaces
} // namespace financial_data