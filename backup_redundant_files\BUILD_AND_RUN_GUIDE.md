# 金融数据服务系统 - 编译和运行指南

## 概述

本项目成功实现了金融数据服务系统的主程序入口，提供了三个不同复杂度的版本，从简单的演示版本到完整的系统架构模拟。

## 系统要求

- **操作系统**: Windows 10/11
- **编译器**: Visual Studio 2022 Professional (安装路径: `d:\Program Files\Microsoft Visual Studio\2022\Professional\`)
- **C++标准**: C++17
- **依赖库**: nlohmann/json (通过vcpkg安装)

## 编译方法

### 自动编译（推荐）

运行编译脚本：
```cmd
.\build_simple.bat
```

### 手动编译

1. 设置Visual Studio环境：
```cmd
call "d:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
```

2. 编译各个版本：
```cmd
# 基础版本
cl /std:c++17 /EHsc src\main_minimal.cpp /Fe:financial_data_service_minimal.exe

# 简化版本（支持JSON配置）
cl /std:c++17 /EHsc /I"./vcpkg_installed/x64-windows/include" src\main_simple.cpp /Fe:financial_data_service_simple.exe

# 完整模拟版本
cl /std:c++17 /EHsc src\main_mock.cpp /Fe:financial_data_service_mock.exe
```

## 可执行文件说明

### 1. financial_data_service_minimal.exe
- **特点**: 最简版本，无外部依赖
- **功能**: 基本的启动/关闭流程模拟
- **适用**: 快速演示和测试

**运行示例**:
```
Financial Data Service System v1.0.0
High-Performance Market Data Platform
========================================
Initializing Financial Data Service...
- Initializing data bus...
- Initializing storage layer...
- Initializing network interfaces...
- Initializing monitoring...
Financial Data Service initialized successfully!
Financial Data Service started!
Server listening on 0.0.0.0:8080
Monitoring available on port 8081
Press Ctrl+C to shutdown gracefully
```

### 2. financial_data_service_simple.exe
- **特点**: 支持JSON配置文件
- **功能**: 读取`config/app.json`，健康检查，统计信息
- **适用**: 配置管理和系统监控演示

**运行示例**:
```
Financial Data Service System v1.0.0 (Simplified)
Starting system initialization...
Configuration loaded successfully from config/app.json
Redis config - Host: localhost, Port: 6379
Server config - Host: 0.0.0.0, Port: 8080
Monitoring port: 8081
All systems initialized successfully
System is ready to serve requests
```

### 3. financial_data_service_mock.exe
- **特点**: 完整系统架构模拟
- **功能**: 
  - 数据总线 (DataBus)
  - Redis热存储
  - WebSocket服务器
  - 监控系统
  - 完整的日志记录
  - 优雅关闭流程
- **适用**: 完整系统演示和架构验证

**运行示例**:
```
Financial Data Service System v1.0.0 (Mock Implementation)
[INFO] Configuration loaded successfully
[INFO] Data bus initialized and started successfully
[INFO] Redis hot storage initialized successfully
[INFO] WebSocket server initialized and started successfully
[INFO] Monitoring system initialized successfully
[INFO] All systems initialized successfully
[INFO] System is ready to serve requests
[INFO] === System Statistics ===
[INFO] Data Bus - Messages: received=10000, processed=9950, sent=9950, dropped=50
[INFO] WebSocket - Connections: active=10, total=15
[INFO] Redis Storage - Stored: ticks=1000, level2=500, queries=200
```

## 运行方法

```cmd
# 运行基础版本
.\financial_data_service_minimal.exe

# 运行简化版本
.\financial_data_service_simple.exe

# 运行完整模拟版本
.\financial_data_service_mock.exe

# 使用自定义配置文件
.\financial_data_service_simple.exe path/to/custom/config.json
```

## 系统特性

### 核心功能
- ✅ 系统初始化和配置加载
- ✅ 多组件协调启动
- ✅ 实时健康检查
- ✅ 系统统计信息
- ✅ 优雅关闭流程
- ✅ 信号处理 (Ctrl+C)

### 模拟组件
- **数据总线**: 高性能消息路由系统
- **Redis存储**: 热数据存储层
- **WebSocket服务器**: 实时数据分发
- **监控系统**: Prometheus指标收集
- **CTP采集器**: 市场数据采集（可选）

### 配置支持
- JSON配置文件 (`config/app.json`)
- 服务器设置 (主机、端口、线程数)
- Redis连接配置
- 监控端口配置
- 性能参数调优

## 故障排除

### 编译问题
1. **找不到编译器**: 确认Visual Studio 2022安装路径正确
2. **缺少头文件**: 检查vcpkg安装和路径配置
3. **链接错误**: 确保所有依赖库正确安装

### 运行问题
1. **配置文件错误**: 检查`config/app.json`格式和内容
2. **端口占用**: 修改配置文件中的端口设置
3. **权限问题**: 以管理员身份运行

## 下一步开发

1. **集成真实依赖**: 替换模拟组件为实际的库实现
2. **完善错误处理**: 增加更详细的错误信息和恢复机制
3. **性能优化**: 实现真正的高性能数据处理
4. **单元测试**: 添加完整的测试覆盖
5. **文档完善**: 添加API文档和部署指南

## 项目结构

```
src/
├── main.cpp              # 原始完整版本（需要真实依赖）
├── main_minimal.cpp      # 最简版本
├── main_simple.cpp       # 简化版本（JSON支持）
└── main_mock.cpp         # 完整模拟版本

config/
└── app.json              # 系统配置文件

build_simple.bat          # 自动编译脚本
run_financial_service.bat # 批处理运行脚本
```

## 总结

通过这三个版本的实现，我们成功展示了金融数据服务系统的完整架构和运行流程。从简单的概念验证到复杂的系统模拟，为后续的真实实现奠定了坚实的基础。