#include "financial_data_sdk.h"
#include <iostream>
#include <vector>
#include <chrono>
#include <iomanip>

using namespace financial_data::sdk;

void PrintTick(const StandardTick& tick) {
    std::cout << "Symbol: " << tick.symbol 
              << ", Exchange: " << tick.exchange
              << ", Price: " << std::fixed << std::setprecision(2) << tick.last_price
              << ", Volume: " << tick.volume
              << ", Timestamp: " << tick.timestamp_ns << std::endl;
    
    // Print bid/ask levels
    std::cout << "  Bids: ";
    for (size_t i = 0; i < 5 && tick.bids[i].volume > 0; ++i) {
        std::cout << tick.bids[i].price << "(" << tick.bids[i].volume << ") ";
    }
    std::cout << std::endl;
    
    std::cout << "  Asks: ";
    for (size_t i = 0; i < 5 && tick.asks[i].volume > 0; ++i) {
        std::cout << tick.asks[i].price << "(" << tick.asks[i].volume << ") ";
    }
    std::cout << std::endl << std::endl;
}

int main() {
    std::cout << "Financial Data SDK - Basic Usage Example" << std::endl;
    std::cout << "=========================================" << std::endl;

    // Configure connection
    ConnectionConfig config;
    config.server_address = "localhost:50051";
    config.connect_timeout = std::chrono::milliseconds(5000);
    config.request_timeout = std::chrono::milliseconds(10000);
    config.max_retry_attempts = 3;
    config.enable_compression = true;
    config.enable_keepalive = true;

    // Create SDK instance
    FinancialDataSDK sdk(config);

    // Set error callback
    sdk.SetErrorCallback([](const ErrorInfo& error) {
        std::cerr << "Error [" << static_cast<int>(error.code) << "]: " 
                  << error.message << std::endl;
    });

    // Set connection status callback
    sdk.SetConnectionStatusCallback([](bool connected) {
        std::cout << "Connection status: " << (connected ? "Connected" : "Disconnected") 
                  << std::endl;
    });

    try {
        // Connect to server
        std::cout << "Connecting to server..." << std::endl;
        if (!sdk.Connect()) {
            std::cerr << "Failed to connect to server" << std::endl;
            return 1;
        }

        std::cout << "Connected successfully!" << std::endl;

        // Health check
        std::cout << "Performing health check..." << std::endl;
        if (sdk.HealthCheck()) {
            std::cout << "Server is healthy" << std::endl;
        } else {
            std::cout << "Server health check failed" << std::endl;
        }

        // Get latest ticks for multiple symbols
        std::cout << "\nGetting latest ticks..." << std::endl;
        std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409"};
        
        auto start_time = std::chrono::high_resolution_clock::now();
        auto ticks = sdk.GetLatestTicks(symbols, "SHFE");
        auto end_time = std::chrono::high_resolution_clock::now();
        
        auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time);
        
        std::cout << "Received " << ticks.size() << " ticks in " 
                  << latency.count() << " microseconds" << std::endl;

        for (const auto& tick : ticks) {
            PrintTick(tick);
        }

        // Get historical data
        std::cout << "\nGetting historical data..." << std::endl;
        auto now = std::chrono::system_clock::now();
        auto one_hour_ago = now - std::chrono::hours(1);
        
        int64_t start_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            one_hour_ago.time_since_epoch()).count();
        int64_t end_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            now.time_since_epoch()).count();

        auto historical_ticks = sdk.GetHistoricalTicks("CU2409", "SHFE", 
                                                      start_timestamp, end_timestamp, 100);
        
        std::cout << "Received " << historical_ticks.size() << " historical ticks" << std::endl;
        
        if (!historical_ticks.empty()) {
            std::cout << "First tick:" << std::endl;
            PrintTick(historical_ticks.front());
            
            std::cout << "Last tick:" << std::endl;
            PrintTick(historical_ticks.back());
        }

        // Display statistics
        auto stats = sdk.GetStatistics();
        std::cout << "\nSDK Statistics:" << std::endl;
        std::cout << "Messages received: " << stats.messages_received << std::endl;
        std::cout << "Messages sent: " << stats.messages_sent << std::endl;
        std::cout << "Connection count: " << stats.connection_count << std::endl;
        std::cout << "Average latency: " << stats.avg_latency.count() << " μs" << std::endl;
        std::cout << "Max latency: " << stats.max_latency.count() << " μs" << std::endl;

        // Disconnect
        std::cout << "\nDisconnecting..." << std::endl;
        sdk.Disconnect();

    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "Example completed successfully!" << std::endl;
    return 0;
}