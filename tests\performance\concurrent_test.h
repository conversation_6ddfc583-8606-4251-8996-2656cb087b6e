/**
 * @file concurrent_test.h
 * @brief Concurrent connection testing for financial data service
 */

#pragma once

#include "benchmark_runner.h"
#include <memory>

namespace performance_tests {

class TestUtils;

/**
 * @class ConcurrentTest
 * @brief Test system stability under concurrent client load
 * 
 * Tests the system's ability to handle 1000+ concurrent connections
 */
class ConcurrentTest {
public:
    ConcurrentTest();
    ~ConcurrentTest();
    
    /**
     * @brief Test concurrent WebSocket connections
     * @param target_connections Number of concurrent connections to test
     * @return ConcurrentResult with connection success rate and performance metrics
     * 
     * Requirement: Support 1000 concurrent WebSocket connections (Req 3.3)
     */
    ConcurrentResult TestConcurrentWebSocketConnections(uint32_t target_connections);
    
    /**
     * @brief Test concurrent gRPC clients
     * @param target_clients Number of concurrent gRPC clients to test
     * @return ConcurrentResult with client performance metrics
     */
    ConcurrentResult TestConcurrentGrpcClients(uint32_t target_clients);
    
    /**
     * @brief Test concurrent REST API clients
     * @param target_clients Number of concurrent REST API clients to test
     * @return ConcurrentResult with API performance metrics
     */
    ConcurrentResult TestConcurrentRestApiClients(uint32_t target_clients);
    
    /**
     * @brief Test memory usage under concurrent load
     * @return ConcurrentResult with memory and CPU usage statistics
     */
    ConcurrentResult TestMemoryUsageUnderLoad();

private:
    std::unique_ptr<TestUtils> test_utils_;
};

} // namespace performance_tests