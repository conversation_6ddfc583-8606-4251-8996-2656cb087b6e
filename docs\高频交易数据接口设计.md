# 高频交易数据接口设计

## 1. 接口架构设计

### 1.1 多层次接口体系
```
┌─────────────────────────────────────────────────────────┐
│                   应用层接口                              │
├─────────────────┬─────────────────┬─────────────────────┤
│   实时行情接口   │   历史数据接口   │    订阅管理接口       │
│   (WebSocket)   │   (REST/gRPC)   │    (TCP/UDP)        │
├─────────────────┼─────────────────┼─────────────────────┤
│   低延迟接口    │   批量接口      │    管理接口         │
│   (<10μs)      │   (高吞吐)      │    (配置/监控)       │
└─────────────────┴─────────────────┴─────────────────────┘
                            │
                    ┌───────┴───────┐
                    │   数据总线层   │
                    │   (共享内存)  │
                    └───────┬───────┘
                            │
                    ┌───────┴───────┐
                    │   采集层      │
                    │   (多源适配)  │
                    └─────────────────┘
```

### 1.2 接口分类与定位

#### 1.2.1 实时行情接口 (Real-time API)
- **目标用户**: 高频交易、做市商、套利策略
- **延迟要求**: 端到端 < 50μs
- **协议**: WebSocket、TCP、UDP组播
- **数据格式**: 二进制、Protocol Buffers

#### 1.2.2 历史数据接口 (Historical API)
- **目标用户**: 回测系统、量化研究、策略开发
- **延迟要求**: 查询 < 100ms
- **协议**: RESTful、gRPC、批量下载
- **数据格式**: Parquet、CSV、HDF5

#### 1.2.3 订阅管理接口 (Subscription API)
- **目标用户**: 策略管理系统、风控系统
- **功能**: 合约订阅、参数配置、状态监控
- **协议**: TCP、HTTP/2
- **特性**: 动态订阅、批量操作

## 2. 实时行情接口设计

### 2.1 低延迟TCP接口

#### 2.1.1 二进制协议格式
```cpp
// 协议头结构
struct PacketHeader {
    uint32_t magic;        // 0xA1B2C3D4
    uint16_t version;      // 协议版本
    uint16_t type;         // 消息类型
    uint32_t length;       // 消息长度
    uint64_t timestamp;    // 纳秒级时间戳
} __attribute__((packed));

// 行情数据消息
struct MarketDataMessage {
    PacketHeader header;
    char symbol[32];       // 合约代码
    double price;          // 最新价
    int64_t volume;        // 成交量
    double turnover;       // 成交额
    double bid_price[5];   // 买1-买5
    int32_t bid_volume[5]; // 买1量-买5量
    double ask_price[5];   // 卖1-卖5
    int32_t ask_volume[5]; // 卖1量-卖5量
    uint32_t sequence;     // 序列号
} __attribute__((packed));
```

#### 2.1.2 零拷贝实现
```cpp
class LowLatencyTCPServer {
private:
    int epoll_fd_;
    int server_fd_;
    std::array<char, 65536> buffer_;
    
public:
    void SendMarketData(int client_fd, const MarketDataMessage& data) {
        // 使用sendmsg实现零拷贝
        struct iovec iov[1];
        iov[0].iov_base = (void*)&data;
        iov[0].iov_len = sizeof(data);
        
        struct msghdr msg;
        msg.msg_name = nullptr;
        msg.msg_namelen = 0;
        msg.msg_iov = iov;
        msg.msg_iovlen = 1;
        msg.msg_control = nullptr;
        msg.msg_controllen = 0;
        
        sendmsg(client_fd, &msg, MSG_NOSIGNAL);
    }
};
```

### 2.2 WebSocket实时推送

#### 2.2.1 协议升级流程
```javascript
// 客户端连接示例
const ws = new WebSocket('ws://localhost:8080/marketdata');

ws.onopen = function() {
    // 订阅合约
    const subscribeMsg = {
        action: 'subscribe',
        symbols: ['CU2409', 'AL2409'],
        levels: 5,
        fields: ['price', 'volume', 'bid', 'ask']
    };
    ws.send(JSON.stringify(subscribeMsg));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Market data:', data);
};
```

#### 2.2.2 消息压缩
```python
import gzip
import json

class WebSocketHandler:
    def send_compressed(self, client, data):
        # 压缩消息
        compressed = gzip.compress(json.dumps(data).encode())
        
        # 发送压缩消息
        client.send(compressed, binary=True)
        
    def receive_compressed(self, message):
        # 解压消息
        decompressed = gzip.decompress(message).decode()
        return json.loads(decompressed)
```

### 2.3 UDP组播接口

#### 2.3.1 组播配置
```cpp
class MulticastPublisher {
private:
    int socket_;
    struct sockaddr_in group_addr_;
    
public:
    bool Initialize(const std::string& multicast_ip, int port) {
        socket_ = socket(AF_INET, SOCK_DGRAM, 0);
        
        // 设置组播TTL
        unsigned char ttl = 1;
        setsockopt(socket_, IPPROTO_IP, IP_MULTICAST_TTL, &ttl, sizeof(ttl));
        
        // 设置缓冲区大小
        int send_buffer_size = 1024 * 1024; // 1MB
        setsockopt(socket_, SOL_SOCKET, SO_SNDBUF, &send_buffer_size, sizeof(send_buffer_size));
        
        // 配置组播地址
        group_addr_.sin_family = AF_INET;
        group_addr_.sin_port = htons(port);
        inet_pton(AF_INET, multicast_ip.c_str(), &group_addr_.sin_addr);
        
        return true;
    }
    
    void Publish(const MarketDataMessage& data) {
        sendto(socket_, &data, sizeof(data), 0,
               (struct sockaddr*)&group_addr_, sizeof(group_addr_));
    }
};
```

#### 2.3.2 客户端订阅
```cpp
class MulticastSubscriber {
private:
    int socket_;
    struct sockaddr_in local_addr_;
    
public:
    bool Subscribe(const std::string& multicast_ip, int port, const std::string& interface_ip) {
        socket_ = socket(AF_INET, SOCK_DGRAM, 0);
        
        // 绑定本地地址
        local_addr_.sin_family = AF_INET;
        local_addr_.sin_port = htons(port);
        local_addr_.sin_addr.s_addr = INADDR_ANY;
        bind(socket_, (struct sockaddr*)&local_addr_, sizeof(local_addr_));
        
        // 加入组播组
        struct ip_mreq mreq;
        inet_pton(AF_INET, multicast_ip.c_str(), &mreq.imr_multiaddr);
        inet_pton(AF_INET, interface_ip.c_str(), &mreq.imr_interface);
        setsockopt(socket_, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq));
        
        return true;
    }
    
    MarketDataMessage Receive() {
        MarketDataMessage data;
        recv(socket_, &data, sizeof(data), 0);
        return data;
    }
};
```

## 3. 历史数据接口设计

### 3.1 RESTful API设计

#### 3.1.1 接口规范
```yaml
openapi: 3.0.0
info:
  title: Market Data API
  version: 1.0.0

paths:
  /api/v1/marketdata/{symbol}/tick:
    get:
      summary: 获取Tick数据
      parameters:
        - name: symbol
          in: path
          required: true
          schema:
            type: string
        - name: start_time
          in: query
          required: true
          schema:
            type: string
            format: date-time
        - name: end_time
          in: query
          required: true
          schema:
            type: string
            format: date-time
        - name: limit
          in: query
          schema:
            type: integer
            default: 1000
            maximum: 100000
      responses:
        '200':
          description: 成功返回数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/TickData'
                  total:
                    type: integer
                  next_cursor:
                    type: string

components:
  schemas:
    TickData:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
        symbol:
          type: string
        last_price:
          type: number
        volume:
          type: integer
        bid_price:
          type: number
        ask_price:
          type: number
```

#### 3.1.2 分页查询实现
```python
from fastapi import FastAPI, Query
from typing import Optional
import redis
from datetime import datetime

app = FastAPI()
redis_client = redis.Redis(host='localhost', port=6379, db=0)

@app.get("/api/v1/marketdata/{symbol}/tick")
async def get_tick_data(
    symbol: str,
    start_time: datetime,
    end_time: datetime,
    limit: int = Query(1000, le=100000),
    cursor: Optional[str] = None
):
    # 使用游标分页
    start_key = f"{symbol}:{start_time.isoformat()}"
    end_key = f"{symbol}:{end_time.isoformat()}"
    
    if cursor:
        start_key = cursor
    
    # 从Redis获取数据
    data = redis_client.zrangebyscore(
        f"{symbol}:ticks",
        min=start_key,
        max=end_key,
        start=0,
        num=limit
    )
    
    # 解析数据
    result = [json.loads(item) for item in data]
    
    # 计算下一页游标
    next_cursor = None
    if len(result) == limit and result:
        next_cursor = result[-1]['timestamp']
    
    return {
        "data": result,
        "total": redis_client.zcard(f"{symbol}:ticks"),
        "next_cursor": next_cursor
    }
```

### 3.2 批量数据下载

#### 3.2.1 分块下载接口
```python
class BatchDownloadService:
    def __init__(self):
        self.chunk_size = 1000000  # 每块100万条记录
        
    def create_download_task(self, symbol, start_date, end_date):
        # 计算总记录数
        total_records = self.count_records(symbol, start_date, end_date)
        
        # 创建分块任务
        chunks = []
        for i in range(0, total_records, self.chunk_size):
            chunk = {
                'symbol': symbol,
                'start_date': start_date,
                'end_date': end_date,
                'offset': i,
                'limit': min(self.chunk_size, total_records - i),
                'chunk_id': f"{symbol}_{start_date}_{end_date}_{i}"
            }
            chunks.append(chunk)
        
        return {
            'task_id': str(uuid.uuid4()),
            'total_chunks': len(chunks),
            'chunks': chunks
        }
    
    def download_chunk(self, chunk_id):
        # 从ClickHouse读取数据块
        query = f"""
        SELECT * FROM market_data.futures_tick
        WHERE symbol = '{symbol}'
          AND timestamp >= '{start_date}'
          AND timestamp < '{end_date}'
        ORDER BY timestamp
        LIMIT {limit} OFFSET {offset}
        """
        
        data = self.clickhouse_client.execute(query)
        
        # 转换为Parquet格式
        df = pd.DataFrame(data, columns=[...])
        buffer = io.BytesIO()
        df.to_parquet(buffer, compression='zstd')
        
        return buffer.getvalue()
```

### 3.3 gRPC流式接口

#### 3.3.1 Protocol Buffer定义
```protobuf
syntax = "proto3";

package marketdata;

service MarketDataService {
    rpc GetTickDataStream(TickDataRequest) returns (stream TickDataResponse);
    rpc GetLevel2DataStream(Level2DataRequest) returns (stream Level2DataResponse);
    rpc GetHistoricalData(HistoricalDataRequest) returns (stream HistoricalDataResponse);
}

message TickDataRequest {
    string symbol = 1;
    int64 start_timestamp = 2;
    int64 end_timestamp = 3;
    int32 batch_size = 4;
}

message TickDataResponse {
    repeated TickData data = 1;
    bool has_more = 2;
    int64 next_timestamp = 3;
}

message TickData {
    int64 timestamp = 1;
    string symbol = 2;
    double last_price = 3;
    int64 volume = 4;
    double turnover = 5;
    double bid_price = 6;
    int32 bid_volume = 7;
    double ask_price = 8;
    int32 ask_volume = 9;
}
```

#### 3.3.2 服务端实现
```python
from concurrent import futures
import grpc
import marketdata_pb2
import marketdata_pb2_grpc

class MarketDataServicer(marketdata_pb2_grpc.MarketDataServiceServicer):
    def GetTickDataStream(self, request, context):
        symbol = request.symbol
        start_ts = request.start_timestamp
        end_ts = request.end_timestamp
        batch_size = request.batch_size
        
        # 从ClickHouse查询数据
        query = f"""
        SELECT * FROM market_data.futures_tick
        WHERE symbol = '{symbol}'
          AND timestamp >= {start_ts}
          AND timestamp < {end_ts}
        ORDER BY timestamp
        """
        
        cursor = self.clickhouse_client.execute_iter(query)
        
        batch = []
        for row in cursor:
            tick_data = marketdata_pb2.TickData(
                timestamp=row[0],
                symbol=row[1],
                last_price=row[2],
                volume=row[3],
                # ... 其他字段
            )
            batch.append(tick_data)
            
            if len(batch) >= batch_size:
                yield marketdata_pb2.TickDataResponse(
                    data=batch,
                    has_more=True,
                    next_timestamp=batch[-1].timestamp
                )
                batch = []
        
        if batch:
            yield marketdata_pb2.TickDataResponse(
                data=batch,
                has_more=False
            )
```

## 4. 订阅管理接口

### 4.1 动态订阅系统

#### 4.1.1 订阅管理API
```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional

app = FastAPI()

class SubscriptionRequest(BaseModel):
    symbols: List[str]
    fields: List[str] = ["price", "volume", "bid", "ask"]
    levels: int = 5
    callback_url: Optional[str] = None

class SubscriptionResponse(BaseModel):
    subscription_id: str
    status: str
    subscribed_symbols: List[str]
    active_symbols: List[str]

class SubscriptionManager:
    def __init__(self):
        self.subscriptions = {}
        self.symbol_subscribers = defaultdict(set)
    
    def create_subscription(self, request: SubscriptionRequest) -> SubscriptionResponse:
        subscription_id = str(uuid.uuid4())
        
        # 验证合约代码
        valid_symbols = [s for s in request.symbols if self.validate_symbol(s)]
        
        # 创建订阅
        self.subscriptions[subscription_id] = {
            'symbols': valid_symbols,
            'fields': request.fields,
            'levels': request.levels,
            'callback_url': request.callback_url,
            'created_at': datetime.now(),
            'status': 'active'
        }
        
        # 更新符号订阅映射
        for symbol in valid_symbols:
            self.symbol_subscribers[symbol].add(subscription_id)
        
        return SubscriptionResponse(
            subscription_id=subscription_id,
            status='active',
            subscribed_symbols=valid_symbols,
            active_symbols=valid_symbols
        )
    
    def update_subscription(self, subscription_id: str, symbols: List[str]) -> SubscriptionResponse:
        if subscription_id not in self.subscriptions:
            raise HTTPException(status_code=404, detail="Subscription not found")
        
        old_symbols = self.subscriptions[subscription_id]['symbols']
        new_symbols = [s for s in symbols if self.validate_symbol(s)]
        
        # 更新符号订阅映射
        for symbol in old_symbols:
            self.symbol_subscribers[symbol].discard(subscription_id)
        
        for symbol in new_symbols:
            self.symbol_subscribers[symbol].add(subscription_id)
        
        self.subscriptions[subscription_id]['symbols'] = new_symbols
        
        return SubscriptionResponse(
            subscription_id=subscription_id,
            status='active',
            subscribed_symbols=new_symbols,
            active_symbols=new_symbols
        )
```

### 4.2 心跳与重连机制

#### 4.2.1 WebSocket心跳
```javascript
class HeartbeatManager {
    constructor(ws, interval = 30000) {
        this.ws = ws;
        this.interval = interval;
        this.timeout = null;
        this.serverTimeout = null;
    }
    
    start() {
        this.timeout = setInterval(() => {
            if (this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
            }
        }, this.interval);
        
        this.serverTimeout = setTimeout(() => {
            this.ws.close();
        }, this.interval + 10000);
    }
    
    handlePong(data) {
        clearTimeout(this.serverTimeout);
        this.serverTimeout = setTimeout(() => {
            this.ws.close();
        }, this.interval + 10000);
    }
    
    stop() {
        clearInterval(this.timeout);
        clearTimeout(this.serverTimeout);
    }
}
```

#### 4.2.2 TCP连接重连
```cpp
class ConnectionManager {
private:
    std::atomic<bool> connected_{false};
    std::thread reconnect_thread_;
    
public:
    void StartReconnect(const std::string& host, int port) {
        reconnect_thread_ = std::thread([this, host, port]() {
            while (!connected_.load()) {
                try {
                    Connect(host, port);
                    connected_.store(true);
                    break;
                } catch (const std::exception& e) {
                    std::this_thread::sleep_for(std::chrono::seconds(5));
                }
            }
        });
    }
    
    void OnDisconnected() {
        connected_.store(false);
        StartReconnect(host_, port_);
    }
};
```

## 5. 性能优化

### 5.1 连接池管理

#### 5.1.1 连接池实现
```cpp
class ConnectionPool {
private:
    std::queue<int> available_connections_;
    std::mutex mutex_;
    std::condition_variable cv_;
    int max_connections_;
    
public:
    int GetConnection() {
        std::unique_lock<std::mutex> lock(mutex_);
        cv_.wait(lock, [this]() { return !available_connections_.empty(); });
        
        int conn = available_connections_.front();
        available_connections_.pop();
        return conn;
    }
    
    void ReleaseConnection(int conn) {
        std::lock_guard<std::mutex> lock(mutex_);
        available_connections_.push(conn);
        cv_.notify_one();
    }
};
```

### 5.2 缓存策略

#### 5.2.1 多级缓存设计
```python
class MultiLevelCache:
    def __init__(self):
        self.l1_cache = {}  # 进程内存缓存
        self.l2_cache = redis.Redis()  # Redis缓存
        self.l3_cache = None  # 磁盘缓存
    
    def get(self, key):
        # L1缓存
        if key in self.l1_cache:
            return self.l1_cache[key]
        
        # L2缓存
        value = self.l2_cache.get(key)
        if value:
            self.l1_cache[key] = value
            return value
        
        # L3缓存
        value = self.load_from_disk(key)
        if value:
            self.l2_cache.setex(key, 3600, value)  # 1小时过期
            self.l1_cache[key] = value
            return value
        
        return None
```

### 5.3 批量处理优化

#### 5.3.1 消息批处理
```cpp
class BatchProcessor {
private:
    std::vector<MarketDataMessage> buffer_;
    size_t batch_size_ = 1000;
    
public:
    void AddMessage(const MarketDataMessage& msg) {
        buffer_.push_back(msg);
        
        if (buffer_.size() >= batch_size_) {
            Flush();
        }
    }
    
    void Flush() {
        if (!buffer_.empty()) {
            // 批量发送
            send_batch(buffer_.data(), buffer_.size());
            buffer_.clear();
        }
    }
};
```

## 6. 监控与告警

### 6.1 接口性能监控

#### 6.1.1 延迟监控
```python
import time
import prometheus_client

# 定义指标
request_latency = prometheus_client.Histogram(
    'api_request_duration_seconds',
    'API request latency',
    ['method', 'endpoint']
)

request_count = prometheus_client.Counter(
    'api_requests_total',
    'Total API requests',
    ['method', 'endpoint', 'status']
)

class APIMetrics:
    def __init__(self, app):
        self.app = app
        self.setup_middleware()
    
    def setup_middleware(self):
        @self.app.middleware("http")
        async def add_process_time_header(request, call_next):
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            
            # 记录指标
            request_latency.labels(
                method=request.method,
                endpoint=request.url.path
            ).observe(process_time)
            
            request_count.labels(
                method=request.method,
                endpoint=request.url.path,
                status=response.status_code
            ).inc()
            
            return response
```

### 6.2 异常监控

#### 6.2.1 错误追踪
```python
import logging
import traceback
from datetime import datetime

class ErrorTracker:
    def __init__(self):
        self.logger = logging.getLogger('api_errors')
        
    def track_error(self, error, context=None):
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {}
        }
        
        self.logger.error(json.dumps(error_info))
        
        # 发送到监控系统
        self.send_to_monitoring(error_info)
```

## 7. 安全设计

### 7.1 认证授权

#### 7.1.1 JWT认证
```python
from fastapi import Depends, HTTPException
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt

security = HTTPBearer()

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(
            credentials.credentials,
            SECRET_KEY,
            algorithms=["HS256"]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

@app.get("/api/v1/protected")
async def protected_endpoint(current_user: dict = Depends(verify_token)):
    return {"message": "Access granted", "user": current_user}
```

### 7.2 限流防护

#### 7.2.1 令牌桶限流
```python
from collections import defaultdict
import time

class RateLimiter:
    def __init__(self, rate, per):
        self.rate = rate  # 每秒允许的请求数
        self.per = per    # 时间窗口
        self.clients = defaultdict(lambda: {'tokens': rate, 'last_check': time.time()})
    
    def is_allowed(self, client_id):
        current = time.time()
        client = self.clients[client_id]
        
        # 添加新令牌
        time_passed = current - client['last_check']
        client['tokens'] += time_passed * (self.rate / self.per)
        client['tokens'] = min(client['tokens'], self.rate)
        client['last_check'] = current
        
        # 检查是否有足够的令牌
        if client['tokens'] >= 1:
            client['tokens'] -= 1
            return True
        
        return False
```

## 8. 客户端SDK设计

### 8.1 C++ SDK

#### 8.1.1 简洁接口设计
```cpp
class MarketDataClient {
public:
    // 初始化连接
    bool Connect(const std::string& host, int port);
    
    // 订阅行情
    bool Subscribe(const std::vector<std::string>& symbols);
    
    // 设置回调
    void SetCallback(std::function<void(const MarketData&)> callback);
    
    // 获取历史数据
    std::vector<MarketData> GetHistoricalData(
        const std::string& symbol,
        int64_t start_time,
        int64_t end_time
    );
    
    // 断开连接
    void Disconnect();
};

// 使用示例
MarketDataClient client;
client.Connect("localhost", 8080);
client.SetCallback([](const MarketData& data) {
    std::cout << "Received: " << data.symbol << " " << data.price << std::endl;
});
client.Subscribe({"CU2409", "AL2409"});
```

### 8.2 Python SDK

#### 8.2.1 异步接口设计
```python
import asyncio
import aiohttp
from typing import List, Callable

class MarketDataClient:
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.session = None
        self.websocket = None
    
    async def connect(self):
        self.session = aiohttp.ClientSession()
        self.websocket = await self.session.ws_connect(
            f"ws://{self.host}:{self.port}/marketdata"
        )
    
    async def subscribe(self, symbols: List[str]):
        message = {
            "action": "subscribe",
            "symbols": symbols
        }
        await self.websocket.send_json(message)
    
    async def stream_data(self, callback: Callable):
        async for msg in self.websocket:
            if msg.type == aiohttp.WSMsgType.TEXT:
                data = msg.json()
                await callback(data)
    
    async def get_historical_data(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime
    ) -> pd.DataFrame:
        params = {
            'symbol': symbol,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat()
        }
        
        async with self.session.get(
            f"http://{self.host}:{self.port}/api/v1/marketdata/{symbol}/tick",
            params=params
        ) as response:
            data = await response.json()
            return pd.DataFrame(data['data'])
    
    async def close(self):
        if self.websocket:
            await self.websocket.close()
        if self.session:
            await self.session.close()

# 使用示例
async def main():
    client = MarketDataClient("localhost", 8080)
    await client.connect()
    await client.subscribe(["CU2409", "AL2409"])
    
    async def handle_data(data):
        print(f"Received: {data}")
    
    await client.stream_data(handle_data)

asyncio.run(main())
```

通过以上设计，可以为量化交易和高频交易提供高性能、低延迟、高可用的数据接口服务。