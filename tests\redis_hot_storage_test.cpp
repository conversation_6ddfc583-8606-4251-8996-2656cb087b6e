#include <gtest/gtest.h>
#include <chrono>
#include <thread>
#include <vector>
#include <random>
#include "../src/storage/redis_storage.h"
#include "../src/storage/redis_config_loader.h"

using namespace financial_data;

class RedisHotStorageTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 使用默认配置
        config_ = RedisConfigLoader::GetDefaultConfig();
        config_.max_connections = 10;  // 测试环境使用较少连接
        config_.write_worker_count = 2;  // 测试环境使用较少工作线程
        
        storage_ = std::make_unique<RedisHotStorage>(config_);
        ASSERT_TRUE(storage_->Initialize()) << "Failed to initialize Redis hot storage";
    }
    
    void TearDown() override {
        if (storage_) {
            storage_->Shutdown();
        }
    }
    
    StandardTick CreateTestTick(const std::string& symbol, double price = 100.0) {
        StandardTick tick;
        tick.timestamp_ns = StandardTick::GetCurrentTimestampNs();
        tick.symbol = symbol;
        tick.exchange = "SHFE";
        tick.last_price = price;
        tick.volume = 1000;
        tick.turnover = price * 1000;
        tick.open_interest = 5000;
        tick.sequence = sequence_counter_++;
        tick.trade_flag = "buy_open";
        
        // 设置买卖盘数据
        for (int i = 0; i < 5; ++i) {
            tick.bids[i] = PriceLevel(price - (i + 1) * 0.1, 100 + i * 10, i + 1);
            tick.asks[i] = PriceLevel(price + (i + 1) * 0.1, 100 + i * 10, i + 1);
        }
        
        return tick;
    }
    
    RedisConfig config_;
    std::unique_ptr<RedisHotStorage> storage_;
    static uint32_t sequence_counter_;
};

uint32_t RedisHotStorageTest::sequence_counter_ = 1;

// 测试基本的存储和查询功能
TEST_F(RedisHotStorageTest, BasicStoreAndRetrieve) {
    StandardTick original_tick = CreateTestTick("CU2409", 78560.0);
    
    // 存储tick数据
    ASSERT_TRUE(storage_->StoreTick(original_tick));
    
    // 查询最新tick数据
    StandardTick retrieved_tick;
    ASSERT_TRUE(storage_->GetLatestTick("CU2409", retrieved_tick));
    
    // 验证数据一致性
    EXPECT_EQ(original_tick.symbol, retrieved_tick.symbol);
    EXPECT_EQ(original_tick.exchange, retrieved_tick.exchange);
    EXPECT_DOUBLE_EQ(original_tick.last_price, retrieved_tick.last_price);
    EXPECT_EQ(original_tick.volume, retrieved_tick.volume);
    EXPECT_EQ(original_tick.sequence, retrieved_tick.sequence);
}

// 测试批量存储功能
TEST_F(RedisHotStorageTest, BatchStore) {
    std::vector<StandardTick> ticks;
    const int batch_size = 100;
    
    // 创建批量测试数据
    for (int i = 0; i < batch_size; ++i) {
        ticks.push_back(CreateTestTick("AL2409", 19000.0 + i));
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 批量存储
    ASSERT_TRUE(storage_->StoreBatch(ticks));
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    std::cout << "Batch store " << batch_size << " ticks took: " << duration.count() << " microseconds" << std::endl;
    
    // 验证最后一条数据
    StandardTick latest_tick;
    ASSERT_TRUE(storage_->GetLatestTick("AL2409", latest_tick));
    EXPECT_DOUBLE_EQ(latest_tick.last_price, 19000.0 + batch_size - 1);
}

// 测试异步存储功能
TEST_F(RedisHotStorageTest, AsyncStore) {
    StandardTick tick = CreateTestTick("ZN2409", 25000.0);
    
    // 异步存储
    auto future = storage_->StoreTickAsync(tick);
    
    // 等待完成
    ASSERT_TRUE(future.get());
    
    // 验证数据已存储
    StandardTick retrieved_tick;
    ASSERT_TRUE(storage_->GetLatestTick("ZN2409", retrieved_tick));
    EXPECT_DOUBLE_EQ(retrieved_tick.last_price, 25000.0);
}

// 测试查询性能（要求1毫秒内响应）
TEST_F(RedisHotStorageTest, QueryPerformance) {
    // 先存储一些测试数据
    StandardTick tick = CreateTestTick("RB2409", 4000.0);
    ASSERT_TRUE(storage_->StoreTick(tick));
    
    const int query_count = 1000;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 执行多次查询
    for (int i = 0; i < query_count; ++i) {
        StandardTick retrieved_tick;
        ASSERT_TRUE(storage_->GetLatestTick("RB2409", retrieved_tick));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    auto avg_duration = total_duration.count() / query_count;
    
    std::cout << "Average query time: " << avg_duration << " microseconds" << std::endl;
    
    // 验证平均查询时间小于1毫秒（1000微秒）
    EXPECT_LT(avg_duration, 1000) << "Query performance requirement not met";
}

// 测试批量查询功能
TEST_F(RedisHotStorageTest, BatchQuery) {
    std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409", "RB2409", "AU2412"};
    
    // 为每个合约存储数据
    for (size_t i = 0; i < symbols.size(); ++i) {
        StandardTick tick = CreateTestTick(symbols[i], 1000.0 + i * 100);
        ASSERT_TRUE(storage_->StoreTick(tick));
    }
    
    // 批量查询
    auto results = storage_->GetLatestTicks(symbols);
    
    // 验证结果
    EXPECT_EQ(results.size(), symbols.size());
    for (size_t i = 0; i < symbols.size(); ++i) {
        ASSERT_TRUE(results.find(symbols[i]) != results.end());
        EXPECT_DOUBLE_EQ(results[symbols[i]].last_price, 1000.0 + i * 100);
    }
}

// 测试时间序列查询
TEST_F(RedisHotStorageTest, TimeSeriesQuery) {
    const std::string symbol = "CU2409";
    std::vector<StandardTick> test_ticks;
    
    // 创建时间序列数据
    for (int i = 0; i < 10; ++i) {
        StandardTick tick = CreateTestTick(symbol, 78000.0 + i * 10);
        test_ticks.push_back(tick);
        ASSERT_TRUE(storage_->StoreTick(tick));
        
        // 确保时间戳不同
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    // 查询时间范围内的数据
    QueryOptions options;
    options.start_time_ns = test_ticks[2].timestamp_ns;
    options.end_time_ns = test_ticks[7].timestamp_ns;
    options.limit = 100;
    
    QueryResult result = storage_->QueryTicks(symbol, options);
    
    // 验证查询结果
    EXPECT_GE(result.ticks.size(), 4);  // 至少应该有4条数据
    EXPECT_LT(result.query_time_ns, 1000000);  // 查询时间小于1毫秒
}

// 测试并发访问
TEST_F(RedisHotStorageTest, ConcurrentAccess) {
    const int thread_count = 10;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    // 启动多个线程并发写入
    for (int t = 0; t < thread_count; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &success_count]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                std::string symbol = "TEST" + std::to_string(t) + "_" + std::to_string(i);
                StandardTick tick = CreateTestTick(symbol, 1000.0 + t * 100 + i);
                
                if (storage_->StoreTick(tick)) {
                    success_count++;
                }
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    // 验证所有操作都成功
    EXPECT_EQ(success_count.load(), thread_count * operations_per_thread);
}

// 测试统计信息
TEST_F(RedisHotStorageTest, Statistics) {
    // 执行一些操作
    StandardTick tick = CreateTestTick("STAT_TEST", 5000.0);
    ASSERT_TRUE(storage_->StoreTick(tick));
    
    StandardTick retrieved_tick;
    ASSERT_TRUE(storage_->GetLatestTick("STAT_TEST", retrieved_tick));
    
    // 获取统计信息
    auto stats = storage_->GetStats();
    
    // 验证统计信息
    EXPECT_GT(stats.total_ticks_stored, 0);
    EXPECT_GT(stats.total_queries, 0);
    EXPECT_GE(stats.avg_write_latency_us, 0);
    EXPECT_GE(stats.avg_query_latency_us, 0);
    
    std::cout << "Storage Statistics:" << std::endl;
    std::cout << "  Total ticks stored: " << stats.total_ticks_stored << std::endl;
    std::cout << "  Total queries: " << stats.total_queries << std::endl;
    std::cout << "  Avg write latency: " << stats.avg_write_latency_us << " μs" << std::endl;
    std::cout << "  Avg query latency: " << stats.avg_query_latency_us << " μs" << std::endl;
    std::cout << "  Active connections: " << stats.active_connections << std::endl;
}

// 测试数据过期功能（简化测试）
TEST_F(RedisHotStorageTest, DataExpiration) {
    // 设置过期策略
    ASSERT_TRUE(storage_->SetupExpirationPolicy());
    
    // 存储测试数据
    StandardTick tick = CreateTestTick("EXPIRE_TEST", 6000.0);
    ASSERT_TRUE(storage_->StoreTick(tick));
    
    // 验证数据存在
    StandardTick retrieved_tick;
    ASSERT_TRUE(storage_->GetLatestTick("EXPIRE_TEST", retrieved_tick));
    
    // 清理过期数据（这里只是测试接口调用）
    uint64_t expired_count = storage_->CleanupExpiredData();
    EXPECT_GE(expired_count, 0);
}

// 性能基准测试
TEST_F(RedisHotStorageTest, PerformanceBenchmark) {
    const int total_operations = 10000;
    std::vector<StandardTick> ticks;
    
    // 准备测试数据
    for (int i = 0; i < total_operations; ++i) {
        ticks.push_back(CreateTestTick("PERF_TEST", 1000.0 + i));
    }
    
    // 测试写入性能
    auto write_start = std::chrono::high_resolution_clock::now();
    
    for (const auto& tick : ticks) {
        ASSERT_TRUE(storage_->StoreTick(tick));
    }
    
    auto write_end = std::chrono::high_resolution_clock::now();
    auto write_duration = std::chrono::duration_cast<std::chrono::microseconds>(write_end - write_start);
    
    double write_ops_per_sec = (total_operations * 1000000.0) / write_duration.count();
    
    std::cout << "Write Performance:" << std::endl;
    std::cout << "  Total operations: " << total_operations << std::endl;
    std::cout << "  Total time: " << write_duration.count() << " μs" << std::endl;
    std::cout << "  Operations per second: " << write_ops_per_sec << std::endl;
    
    // 验证写入性能满足要求（每秒100万条）
    EXPECT_GT(write_ops_per_sec, 100000) << "Write performance requirement not met";
    
    // 测试读取性能
    auto read_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < 1000; ++i) {
        StandardTick retrieved_tick;
        ASSERT_TRUE(storage_->GetLatestTick("PERF_TEST", retrieved_tick));
    }
    
    auto read_end = std::chrono::high_resolution_clock::now();
    auto read_duration = std::chrono::duration_cast<std::chrono::microseconds>(read_end - read_start);
    
    double avg_read_time = read_duration.count() / 1000.0;
    
    std::cout << "Read Performance:" << std::endl;
    std::cout << "  Average read time: " << avg_read_time << " μs" << std::endl;
    
    // 验证读取性能满足要求（1毫秒内）
    EXPECT_LT(avg_read_time, 1000) << "Read performance requirement not met";
}