{"scheduler": {"name": "WSL测试调度器", "version": "1.0.0", "timezone": "Asia/Shanghai", "max_workers": 2, "task_timeout": 300, "retry_attempts": 3, "retry_delay": 60}, "data_sources": {"pytdx": {"enabled": true, "servers": [{"host": "**************", "port": 7709, "name": "通达信1"}, {"host": "*************", "port": 7709, "name": "通达信2"}], "timeout": 30, "retry_count": 3, "connection_pool_size": 5}}, "storage": {"redis": {"host": "localhost", "port": 6379, "db": 0, "password": null, "max_connections": 10, "socket_timeout": 30, "socket_connect_timeout": 30}, "clickhouse": {"host": "localhost", "port": 9000, "database": "financial_data", "user": "admin", "password": "password123", "timeout": 60, "max_connections": 5}}, "tasks": {"symbol_list_update": {"enabled": true, "schedule": "0 8 * * *", "description": "更新代码表", "timeout": 600, "data_types": ["stock", "index", "fund"]}, "daily_data_update": {"enabled": true, "schedule": "0 18 * * 1-5", "description": "日线数据更新", "timeout": 1800, "data_types": ["stock", "index"], "lookback_days": 30}, "realtime_data_update": {"enabled": false, "schedule": "*/5 9-15 * * 1-5", "description": "实时数据更新", "timeout": 300, "data_types": ["stock"], "max_symbols": 100}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/wsl_test.log", "max_size": "10MB", "backup_count": 5, "console": true}, "monitoring": {"enabled": true, "metrics_port": 8000, "health_check_interval": 60, "performance_tracking": true}, "features": {"auto_symbol_discovery": true, "data_validation": true, "error_recovery": true, "performance_optimization": true, "concurrent_processing": true}, "limits": {"max_concurrent_tasks": 3, "max_memory_usage": "1GB", "max_disk_usage": "5GB", "rate_limit_per_second": 10}}