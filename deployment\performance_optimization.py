#!/usr/bin/env python3
"""
Financial Data Service - Performance Optimization Tool
Automated system tuning and optimization for production deployment
"""

import asyncio
import logging
import json
import psutil
import docker
import subprocess
import time
import sys
from typing import Dict, List, Any, Tuple
import redis
import clickhouse_connect
from pathlib import Path
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    def __init__(self, config_file: str):
        """Initialize performance optimizer"""
        self.config = self._load_config(config_file)
        self.docker_client = docker.from_env()
        self.optimization_results = {}
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load optimization configuration"""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            sys.exit(1)
    
    def analyze_system_resources(self) -> Dict[str, Any]:
        """Analyze current system resource usage"""
        logger.info("Analyzing system resources...")
        
        # CPU information
        cpu_info = {
            'count': psutil.cpu_count(),
            'usage_percent': psutil.cpu_percent(interval=1),
            'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            'frequency': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
        }
        
        # Memory information
        memory = psutil.virtual_memory()
        memory_info = {
            'total': memory.total,
            'available': memory.available,
            'used': memory.used,
            'usage_percent': memory.percent,
            'free': memory.free
        }
        
        # Disk information
        disk = psutil.disk_usage('/')
        disk_info = {
            'total': disk.total,
            'used': disk.used,
            'free': disk.free,
            'usage_percent': (disk.used / disk.total) * 100
        }
        
        # Network information
        network = psutil.net_io_counters()
        network_info = {
            'bytes_sent': network.bytes_sent,
            'bytes_recv': network.bytes_recv,
            'packets_sent': network.packets_sent,
            'packets_recv': network.packets_recv
        }
        
        system_info = {
            'cpu': cpu_info,
            'memory': memory_info,
            'disk': disk_info,
            'network': network_info,
            'timestamp': time.time()
        }
        
        logger.info(f"System analysis complete: CPU {cpu_info['usage_percent']}%, "
                   f"Memory {memory_info['usage_percent']}%, "
                   f"Disk {disk_info['usage_percent']:.1f}%")
        
        return system_info
    
    def optimize_docker_containers(self) -> Dict[str, Any]:
        """Optimize Docker container configurations"""
        logger.info("Optimizing Docker containers...")
        
        optimization_results = {}
        
        try:
            containers = self.docker_client.containers.list()
            
            for container in containers:
                container_name = container.name
                logger.info(f"Optimizing container: {container_name}")
                
                # Get container stats
                stats = container.stats(stream=False)
                
                # Calculate resource usage
                cpu_usage = self._calculate_cpu_usage(stats)
                memory_usage = stats['memory_stats']['usage'] / stats['memory_stats']['limit'] * 100
                
                # Optimization recommendations
                recommendations = []
                
                # CPU optimization
                if cpu_usage > 80:
                    recommendations.append({
                        'type': 'cpu',
                        'issue': 'High CPU usage',
                        'recommendation': 'Increase CPU limits or scale horizontally',
                        'current_usage': cpu_usage
                    })
                
                # Memory optimization
                if memory_usage > 85:
                    recommendations.append({
                        'type': 'memory',
                        'issue': 'High memory usage',
                        'recommendation': 'Increase memory limits or optimize application',
                        'current_usage': memory_usage
                    })
                
                # Apply optimizations based on container type
                if 'redis' in container_name.lower():
                    redis_opts = self._optimize_redis_container(container)
                    recommendations.extend(redis_opts)
                elif 'clickhouse' in container_name.lower():
                    ch_opts = self._optimize_clickhouse_container(container)
                    recommendations.extend(ch_opts)
                elif 'kafka' in container_name.lower():
                    kafka_opts = self._optimize_kafka_container(container)
                    recommendations.extend(kafka_opts)
                
                optimization_results[container_name] = {
                    'cpu_usage': cpu_usage,
                    'memory_usage': memory_usage,
                    'recommendations': recommendations
                }
                
        except Exception as e:
            logger.error(f"Failed to optimize containers: {e}")
        
        return optimization_results
    
    def _calculate_cpu_usage(self, stats: Dict) -> float:
        """Calculate CPU usage percentage from container stats"""
        try:
            cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                       stats['precpu_stats']['cpu_usage']['total_usage']
            system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                          stats['precpu_stats']['system_cpu_usage']
            
            if system_delta > 0:
                cpu_usage = (cpu_delta / system_delta) * \
                           len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100
                return cpu_usage
        except (KeyError, ZeroDivisionError):
            pass
        return 0.0
    
    def _optimize_redis_container(self, container) -> List[Dict]:
        """Optimize Redis container configuration"""
        recommendations = []
        
        try:
            # Check Redis configuration
            redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
            info = redis_client.info()
            
            # Memory optimization
            used_memory = info.get('used_memory', 0)
            max_memory = info.get('maxmemory', 0)
            
            if max_memory > 0 and (used_memory / max_memory) > 0.8:
                recommendations.append({
                    'type': 'redis_memory',
                    'issue': 'High Redis memory usage',
                    'recommendation': 'Increase maxmemory or enable memory eviction policies',
                    'current_usage': (used_memory / max_memory) * 100
                })
            
            # Connection optimization
            connected_clients = info.get('connected_clients', 0)
            if connected_clients > 1000:
                recommendations.append({
                    'type': 'redis_connections',
                    'issue': 'High number of Redis connections',
                    'recommendation': 'Implement connection pooling',
                    'current_connections': connected_clients
                })
            
        except Exception as e:
            logger.warning(f"Failed to analyze Redis: {e}")
        
        return recommendations
    
    def _optimize_clickhouse_container(self, container) -> List[Dict]:
        """Optimize ClickHouse container configuration"""
        recommendations = []
        
        try:
            # Check ClickHouse configuration
            ch_client = clickhouse_connect.get_client(
                host='localhost',
                port=9000,
                username='admin',
                password='password123'
            )
            
            # Query performance analysis
            result = ch_client.query("SELECT * FROM system.query_log ORDER BY event_time DESC LIMIT 100")
            
            slow_queries = 0
            for row in result.result_rows:
                if len(row) > 3 and row[3] > 1.0:  # Query duration > 1 second
                    slow_queries += 1
            
            if slow_queries > 10:
                recommendations.append({
                    'type': 'clickhouse_performance',
                    'issue': 'Slow queries detected',
                    'recommendation': 'Optimize queries and add appropriate indexes',
                    'slow_query_count': slow_queries
                })
            
            # Memory usage analysis
            memory_result = ch_client.query("SELECT * FROM system.metrics WHERE metric LIKE '%Memory%'")
            for row in memory_result.result_rows:
                if 'MemoryTracking' in row[0] and row[1] > 8 * 1024 * 1024 * 1024:  # > 8GB
                    recommendations.append({
                        'type': 'clickhouse_memory',
                        'issue': 'High ClickHouse memory usage',
                        'recommendation': 'Increase memory limits or optimize queries',
                        'memory_usage': row[1]
                    })
            
        except Exception as e:
            logger.warning(f"Failed to analyze ClickHouse: {e}")
        
        return recommendations
    
    def _optimize_kafka_container(self, container) -> List[Dict]:
        """Optimize Kafka container configuration"""
        recommendations = []
        
        try:
            # Check Kafka JVM settings
            container_info = container.attrs
            env_vars = container_info.get('Config', {}).get('Env', [])
            
            kafka_heap_opts = None
            for env in env_vars:
                if env.startswith('KAFKA_HEAP_OPTS'):
                    kafka_heap_opts = env
                    break
            
            if not kafka_heap_opts or '-Xmx' not in kafka_heap_opts:
                recommendations.append({
                    'type': 'kafka_memory',
                    'issue': 'Kafka heap size not optimized',
                    'recommendation': 'Set appropriate KAFKA_HEAP_OPTS for JVM',
                    'current_setting': kafka_heap_opts or 'Not set'
                })
            
            # Check for log retention settings
            log_retention_found = False
            for env in env_vars:
                if 'log.retention' in env:
                    log_retention_found = True
                    break
            
            if not log_retention_found:
                recommendations.append({
                    'type': 'kafka_retention',
                    'issue': 'Log retention not configured',
                    'recommendation': 'Configure log.retention.hours and log.retention.bytes',
                    'current_setting': 'Default'
                })
            
        except Exception as e:
            logger.warning(f"Failed to analyze Kafka: {e}")
        
        return recommendations
    
    def optimize_system_parameters(self) -> Dict[str, Any]:
        """Optimize system-level parameters"""
        logger.info("Optimizing system parameters...")
        
        optimizations = {}
        
        try:
            # Network optimizations
            network_opts = self._optimize_network_parameters()
            optimizations['network'] = network_opts
            
            # File system optimizations
            fs_opts = self._optimize_filesystem_parameters()
            optimizations['filesystem'] = fs_opts
            
            # Kernel optimizations
            kernel_opts = self._optimize_kernel_parameters()
            optimizations['kernel'] = kernel_opts
            
        except Exception as e:
            logger.error(f"Failed to optimize system parameters: {e}")
        
        return optimizations
    
    def _optimize_network_parameters(self) -> Dict[str, Any]:
        """Optimize network parameters for high-frequency trading"""
        optimizations = {
            'applied': [],
            'recommendations': []
        }
        
        network_params = {
            'net.core.rmem_max': '134217728',  # 128MB
            'net.core.wmem_max': '134217728',  # 128MB
            'net.core.rmem_default': '65536',
            'net.core.wmem_default': '65536',
            'net.core.netdev_max_backlog': '5000',
            'net.ipv4.tcp_rmem': '4096 65536 134217728',
            'net.ipv4.tcp_wmem': '4096 65536 134217728',
            'net.ipv4.tcp_congestion_control': 'bbr',
            'net.ipv4.tcp_slow_start_after_idle': '0'
        }
        
        for param, value in network_params.items():
            try:
                # Check current value
                result = subprocess.run(['sysctl', param], capture_output=True, text=True)
                if result.returncode == 0:
                    current_value = result.stdout.split('=')[1].strip()
                    if current_value != value:
                        optimizations['recommendations'].append({
                            'parameter': param,
                            'current': current_value,
                            'recommended': value,
                            'description': f'Optimize {param} for high-frequency trading'
                        })
            except Exception as e:
                logger.warning(f"Failed to check {param}: {e}")
        
        return optimizations
    
    def _optimize_filesystem_parameters(self) -> Dict[str, Any]:
        """Optimize filesystem parameters"""
        optimizations = {
            'applied': [],
            'recommendations': []
        }
        
        # Check mount options
        try:
            with open('/proc/mounts', 'r') as f:
                mounts = f.read()
            
            # Recommend noatime for data directories
            if 'noatime' not in mounts:
                optimizations['recommendations'].append({
                    'type': 'mount_option',
                    'recommendation': 'Add noatime mount option to reduce disk I/O',
                    'description': 'Prevents access time updates on file reads'
                })
            
        except Exception as e:
            logger.warning(f"Failed to check filesystem parameters: {e}")
        
        return optimizations
    
    def _optimize_kernel_parameters(self) -> Dict[str, Any]:
        """Optimize kernel parameters"""
        optimizations = {
            'applied': [],
            'recommendations': []
        }
        
        kernel_params = {
            'vm.swappiness': '1',  # Minimize swapping
            'vm.dirty_ratio': '15',  # Dirty page cache ratio
            'vm.dirty_background_ratio': '5',  # Background dirty ratio
            'kernel.sched_migration_cost_ns': '5000000',  # CPU migration cost
            'kernel.sched_autogroup_enabled': '0'  # Disable auto grouping
        }
        
        for param, value in kernel_params.items():
            try:
                result = subprocess.run(['sysctl', param], capture_output=True, text=True)
                if result.returncode == 0:
                    current_value = result.stdout.split('=')[1].strip()
                    if current_value != value:
                        optimizations['recommendations'].append({
                            'parameter': param,
                            'current': current_value,
                            'recommended': value,
                            'description': f'Optimize {param} for low-latency performance'
                        })
            except Exception as e:
                logger.warning(f"Failed to check {param}: {e}")
        
        return optimizations
    
    def benchmark_system_performance(self) -> Dict[str, Any]:
        """Run comprehensive system performance benchmarks"""
        logger.info("Running system performance benchmarks...")
        
        benchmarks = {}
        
        # CPU benchmark
        benchmarks['cpu'] = self._benchmark_cpu()
        
        # Memory benchmark
        benchmarks['memory'] = self._benchmark_memory()
        
        # Disk I/O benchmark
        benchmarks['disk'] = self._benchmark_disk()
        
        # Network benchmark
        benchmarks['network'] = self._benchmark_network()
        
        return benchmarks
    
    def _benchmark_cpu(self) -> Dict[str, Any]:
        """Benchmark CPU performance"""
        logger.info("Running CPU benchmark...")
        
        start_time = time.time()
        
        # Simple CPU-intensive calculation
        result = 0
        for i in range(10000000):
            result += i * i
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            'duration_seconds': duration,
            'operations_per_second': 10000000 / duration,
            'score': 1000 / duration  # Higher is better
        }
    
    def _benchmark_memory(self) -> Dict[str, Any]:
        """Benchmark memory performance"""
        logger.info("Running memory benchmark...")
        
        # Memory allocation and access test
        start_time = time.time()
        
        # Allocate and access large array
        size = 100000000  # 100M integers
        data = list(range(size))
        total = sum(data)
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            'duration_seconds': duration,
            'throughput_mb_per_second': (size * 4) / (1024 * 1024) / duration,  # 4 bytes per int
            'score': 1000 / duration
        }
    
    def _benchmark_disk(self) -> Dict[str, Any]:
        """Benchmark disk I/O performance"""
        logger.info("Running disk I/O benchmark...")
        
        test_file = '/tmp/disk_benchmark_test'
        file_size = 100 * 1024 * 1024  # 100MB
        
        try:
            # Write test
            start_time = time.time()
            with open(test_file, 'wb') as f:
                f.write(b'0' * file_size)
            write_time = time.time() - start_time
            
            # Read test
            start_time = time.time()
            with open(test_file, 'rb') as f:
                data = f.read()
            read_time = time.time() - start_time
            
            # Cleanup
            Path(test_file).unlink(missing_ok=True)
            
            return {
                'write_time_seconds': write_time,
                'read_time_seconds': read_time,
                'write_throughput_mb_per_second': (file_size / (1024 * 1024)) / write_time,
                'read_throughput_mb_per_second': (file_size / (1024 * 1024)) / read_time
            }
            
        except Exception as e:
            logger.error(f"Disk benchmark failed: {e}")
            return {'error': str(e)}
    
    def _benchmark_network(self) -> Dict[str, Any]:
        """Benchmark network performance"""
        logger.info("Running network benchmark...")
        
        try:
            # Simple localhost ping test
            result = subprocess.run(
                ['ping', '-c', '10', 'localhost'],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                # Parse ping results
                lines = result.stdout.split('\n')
                stats_line = [line for line in lines if 'min/avg/max' in line]
                
                if stats_line:
                    stats = stats_line[0].split('=')[1].strip()
                    min_time, avg_time, max_time = stats.split('/')[:3]
                    
                    return {
                        'ping_min_ms': float(min_time),
                        'ping_avg_ms': float(avg_time),
                        'ping_max_ms': float(max_time),
                        'latency_score': 100 / float(avg_time)  # Lower latency = higher score
                    }
            
        except Exception as e:
            logger.error(f"Network benchmark failed: {e}")
        
        return {'error': 'Network benchmark failed'}
    
    def generate_optimization_report(self) -> str:
        """Generate comprehensive optimization report"""
        logger.info("Generating optimization report...")
        
        # Collect all optimization data
        system_info = self.analyze_system_resources()
        container_opts = self.optimize_docker_containers()
        system_opts = self.optimize_system_parameters()
        benchmarks = self.benchmark_system_performance()
        
        # Generate report
        report = {
            'timestamp': time.time(),
            'system_analysis': system_info,
            'container_optimizations': container_opts,
            'system_optimizations': system_opts,
            'performance_benchmarks': benchmarks,
            'summary': self._generate_summary(system_info, container_opts, system_opts, benchmarks)
        }
        
        # Save report to file
        report_file = f"optimization_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Optimization report saved to {report_file}")
        return report_file
    
    def _generate_summary(self, system_info, container_opts, system_opts, benchmarks) -> Dict[str, Any]:
        """Generate optimization summary"""
        total_recommendations = 0
        critical_issues = 0
        
        # Count container recommendations
        for container, opts in container_opts.items():
            total_recommendations += len(opts.get('recommendations', []))
            for rec in opts.get('recommendations', []):
                if rec.get('current_usage', 0) > 90:
                    critical_issues += 1
        
        # Count system recommendations
        for category, opts in system_opts.items():
            total_recommendations += len(opts.get('recommendations', []))
        
        # Performance score calculation
        cpu_score = benchmarks.get('cpu', {}).get('score', 0)
        memory_score = benchmarks.get('memory', {}).get('score', 0)
        overall_score = (cpu_score + memory_score) / 2
        
        return {
            'total_recommendations': total_recommendations,
            'critical_issues': critical_issues,
            'performance_score': overall_score,
            'system_health': 'Good' if critical_issues == 0 else 'Needs Attention',
            'optimization_priority': 'High' if critical_issues > 0 else 'Medium'
        }

def main():
    parser = argparse.ArgumentParser(description='Financial Data Service Performance Optimizer')
    parser.add_argument('--config', required=True, help='Configuration file path')
    parser.add_argument('--report-only', action='store_true', help='Generate report without applying optimizations')
    parser.add_argument('--benchmark', action='store_true', help='Run performance benchmarks')
    
    args = parser.parse_args()
    
    # Create optimizer instance
    optimizer = PerformanceOptimizer(args.config)
    
    try:
        if args.benchmark:
            logger.info("Running performance benchmarks...")
            benchmarks = optimizer.benchmark_system_performance()
            print(json.dumps(benchmarks, indent=2))
        else:
            # Generate optimization report
            report_file = optimizer.generate_optimization_report()
            logger.info(f"Optimization analysis complete. Report saved to {report_file}")
            
    except Exception as e:
        logger.error(f"Optimization failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()