#!/usr/bin/env python3
"""
金融数据服务系统 - 统一部署管理器
Financial Data Service - Unified Deployment Manager

这是一个统一的部署管理器，支持多环境部署：
- 开发环境 (development)
- 测试环境 (testing)
- 生产环境 (production)

使用方法:
    python deploy.py --env dev --action start       # 启动开发环境
    python deploy.py --env test --action deploy     # 部署测试环境
    python deploy.py --env prod --action deploy     # 部署生产环境
    python deploy.py --env dev --action stop        # 停止开发环境
    python deploy.py --env prod --action status     # 查看生产环境状态
    python deploy.py --env dev --action logs        # 查看开发环境日志
"""

import os
import sys
import json
import yaml
import argparse
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Environment(Enum):
    """环境类型枚举"""
    DEVELOPMENT = "dev"
    TESTING = "test"
    PRODUCTION = "prod"


class Action(Enum):
    """部署动作枚举"""
    START = "start"
    STOP = "stop"
    RESTART = "restart"
    DEPLOY = "deploy"
    STATUS = "status"
    LOGS = "logs"
    CLEAN = "clean"


@dataclass
class ServiceConfig:
    """服务配置"""
    name: str
    image: str
    ports: List[str]
    environment: Dict[str, str]
    volumes: List[str]
    depends_on: List[str] = None
    healthcheck: Dict = None


class UnifiedDeploymentManager:
    """统一部署管理器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root or os.getcwd())
        self.config_dir = self.project_root / "config"
        self.deployment_dir = self.project_root / "deployment"
        
        # 环境配置映射
        self.env_configs = {
            Environment.DEVELOPMENT: {
                "compose_file": "docker-compose.dev.yml",
                "config_file": "config/app.json",
                "services": ["redis", "clickhouse", "financial-app"],
                "ports": {
                    "redis": "6379:6379",
                    "clickhouse": "8123:8123,9000:9000",
                    "financial-app": "8080:8080"
                }
            },
            Environment.TESTING: {
                "compose_file": "docker-compose.test.yml",
                "config_file": "config/test_config.json",
                "services": ["redis", "clickhouse", "kafka", "financial-app"],
                "ports": {
                    "redis": "6379:6379",
                    "clickhouse": "8123:8123,9000:9000",
                    "kafka": "9092:9092",
                    "financial-app": "8080:8080"
                }
            },
            Environment.PRODUCTION: {
                "compose_file": "deployment/production/docker-compose.prod.yml",
                "config_file": "config/unified_config.json",
                "services": ["redis-cluster", "clickhouse-cluster", "kafka-cluster", "minio-cluster", "financial-app", "load-balancer", "monitoring"],
                "ports": {
                    "load-balancer": "80:80,443:443",
                    "monitoring": "3000:3000,9090:9090"
                }
            }
        }
    
    def check_prerequisites(self, env: Environment) -> bool:
        """检查部署前提条件"""
        logger.info(f"检查 {env.value} 环境的前提条件...")
        
        # 检查Docker
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("Docker未安装或不可用")
                return False
            logger.info(f"Docker版本: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.error("Docker未找到")
            return False
        
        # 检查Docker Compose
        try:
            result = subprocess.run(["docker-compose", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("Docker Compose未安装或不可用")
                return False
            logger.info(f"Docker Compose版本: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.error("Docker Compose未找到")
            return False
        
        # 检查配置文件
        config = self.env_configs[env]
        compose_file = self.project_root / config["compose_file"]
        if not compose_file.exists():
            logger.error(f"Docker Compose文件不存在: {compose_file}")
            return False
        
        config_file = self.project_root / config["config_file"]
        if not config_file.exists():
            logger.error(f"配置文件不存在: {config_file}")
            return False
        
        return True
    
    def generate_compose_file(self, env: Environment) -> str:
        """生成Docker Compose文件"""
        config = self.env_configs[env]
        compose_file = self.project_root / config["compose_file"]
        
        if env == Environment.DEVELOPMENT:
            compose_content = self._generate_dev_compose()
        elif env == Environment.TESTING:
            compose_content = self._generate_test_compose()
        else:  # PRODUCTION
            compose_content = self._generate_prod_compose()
        
        # 确保目录存在
        compose_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        with open(compose_file, 'w', encoding='utf-8') as f:
            yaml.dump(compose_content, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"生成Docker Compose文件: {compose_file}")
        return str(compose_file)
    
    def _generate_dev_compose(self) -> Dict:
        """生成开发环境Docker Compose配置"""
        return {
            "version": "3.8",
            "services": {
                "redis": {
                    "image": "redis:7-alpine",
                    "ports": ["6379:6379"],
                    "volumes": ["redis_data:/data"],
                    "command": "redis-server --appendonly yes"
                },
                "clickhouse": {
                    "image": "clickhouse/clickhouse-server:latest",
                    "ports": ["8123:8123", "9000:9000"],
                    "volumes": [
                        "clickhouse_data:/var/lib/clickhouse",
                        "./config/clickhouse.xml:/etc/clickhouse-server/config.xml"
                    ],
                    "environment": {
                        "CLICKHOUSE_DB": "market_data",
                        "CLICKHOUSE_USER": "admin",
                        "CLICKHOUSE_PASSWORD": "password123"
                    }
                },
                "financial-app": {
                    "build": ".",
                    "ports": ["8080:8080"],
                    "volumes": [
                        "./config:/app/config",
                        "./logs:/app/logs"
                    ],
                    "depends_on": ["redis", "clickhouse"],
                    "environment": {
                        "ENVIRONMENT": "development",
                        "REDIS_HOST": "redis",
                        "CLICKHOUSE_HOST": "clickhouse"
                    }
                }
            },
            "volumes": {
                "redis_data": {},
                "clickhouse_data": {}
            }
        }
    
    def _generate_test_compose(self) -> Dict:
        """生成测试环境Docker Compose配置"""
        dev_compose = self._generate_dev_compose()
        
        # 添加Kafka用于测试
        dev_compose["services"]["kafka"] = {
            "image": "confluentinc/cp-kafka:latest",
            "ports": ["9092:9092"],
            "environment": {
                "KAFKA_BROKER_ID": "1",
                "KAFKA_ZOOKEEPER_CONNECT": "zookeeper:2181",
                "KAFKA_ADVERTISED_LISTENERS": "PLAINTEXT://localhost:9092",
                "KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR": "1"
            },
            "depends_on": ["zookeeper"]
        }
        
        dev_compose["services"]["zookeeper"] = {
            "image": "confluentinc/cp-zookeeper:latest",
            "environment": {
                "ZOOKEEPER_CLIENT_PORT": "2181",
                "ZOOKEEPER_TICK_TIME": "2000"
            }
        }
        
        # 更新应用配置
        dev_compose["services"]["financial-app"]["environment"]["KAFKA_BROKERS"] = "kafka:9092"
        dev_compose["services"]["financial-app"]["depends_on"].append("kafka")
        
        return dev_compose
    
    def _generate_prod_compose(self) -> Dict:
        """生成生产环境Docker Compose配置"""
        return {
            "version": "3.8",
            "services": {
                # Redis集群
                "redis-cluster": {
                    "image": "redis:7-alpine",
                    "deploy": {
                        "replicas": 6,
                        "resources": {
                            "limits": {"memory": "1G", "cpus": "0.5"}
                        }
                    },
                    "volumes": ["redis_cluster_data:/data"],
                    "networks": ["financial_network"]
                },
                
                # ClickHouse集群
                "clickhouse-cluster": {
                    "image": "clickhouse/clickhouse-server:latest",
                    "deploy": {
                        "replicas": 3,
                        "resources": {
                            "limits": {"memory": "4G", "cpus": "2"}
                        }
                    },
                    "volumes": [
                        "clickhouse_cluster_data:/var/lib/clickhouse",
                        "./config/clickhouse-cluster.xml:/etc/clickhouse-server/config.xml"
                    ],
                    "networks": ["financial_network"]
                },
                
                # 应用服务
                "financial-app": {
                    "image": "financial-data-service:latest",
                    "deploy": {
                        "replicas": 3,
                        "resources": {
                            "limits": {"memory": "2G", "cpus": "1"}
                        }
                    },
                    "volumes": [
                        "./config:/app/config",
                        "./logs:/app/logs"
                    ],
                    "networks": ["financial_network"],
                    "environment": {
                        "ENVIRONMENT": "production",
                        "REDIS_CLUSTER": "redis-cluster",
                        "CLICKHOUSE_CLUSTER": "clickhouse-cluster"
                    }
                },
                
                # 负载均衡器
                "load-balancer": {
                    "image": "haproxy:latest",
                    "ports": ["80:80", "443:443"],
                    "volumes": [
                        "./deployment/production/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg",
                        "./ssl:/etc/ssl/certs"
                    ],
                    "depends_on": ["financial-app"],
                    "networks": ["financial_network"]
                },
                
                # 监控服务
                "prometheus": {
                    "image": "prom/prometheus:latest",
                    "ports": ["9090:9090"],
                    "volumes": ["./config/prometheus.yml:/etc/prometheus/prometheus.yml"],
                    "networks": ["financial_network"]
                },
                
                "grafana": {
                    "image": "grafana/grafana:latest",
                    "ports": ["3000:3000"],
                    "volumes": ["grafana_data:/var/lib/grafana"],
                    "environment": {
                        "GF_SECURITY_ADMIN_PASSWORD": "admin123"
                    },
                    "networks": ["financial_network"]
                }
            },
            
            "volumes": {
                "redis_cluster_data": {},
                "clickhouse_cluster_data": {},
                "grafana_data": {}
            },
            
            "networks": {
                "financial_network": {
                    "driver": "bridge"
                }
            }
        }
    
    def execute_action(self, env: Environment, action: Action) -> bool:
        """执行部署动作"""
        logger.info(f"在 {env.value} 环境执行 {action.value} 操作")
        
        if not self.check_prerequisites(env):
            return False
        
        config = self.env_configs[env]
        compose_file = self.project_root / config["compose_file"]
        
        # 确保Compose文件存在
        if not compose_file.exists():
            compose_file = self.generate_compose_file(env)
        
        try:
            if action == Action.START:
                return self._start_services(compose_file, config["services"])
            elif action == Action.STOP:
                return self._stop_services(compose_file)
            elif action == Action.RESTART:
                return self._restart_services(compose_file)
            elif action == Action.DEPLOY:
                return self._deploy_services(compose_file, env)
            elif action == Action.STATUS:
                return self._check_status(compose_file)
            elif action == Action.LOGS:
                return self._show_logs(compose_file)
            elif action == Action.CLEAN:
                return self._clean_environment(compose_file)
            else:
                logger.error(f"不支持的操作: {action.value}")
                return False
                
        except Exception as e:
            logger.error(f"执行操作失败: {e}")
            return False
    
    def _start_services(self, compose_file: str, services: List[str]) -> bool:
        """启动服务"""
        logger.info("启动服务...")
        cmd = ["docker-compose", "-f", compose_file, "up", "-d"] + services
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("服务启动成功")
            return True
        else:
            logger.error(f"服务启动失败: {result.stderr}")
            return False
    
    def _stop_services(self, compose_file: str) -> bool:
        """停止服务"""
        logger.info("停止服务...")
        cmd = ["docker-compose", "-f", compose_file, "down"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("服务停止成功")
            return True
        else:
            logger.error(f"服务停止失败: {result.stderr}")
            return False
    
    def _restart_services(self, compose_file: str) -> bool:
        """重启服务"""
        logger.info("重启服务...")
        return self._stop_services(compose_file) and self._start_services(compose_file, [])
    
    def _deploy_services(self, compose_file: str, env: Environment) -> bool:
        """部署服务"""
        logger.info("部署服务...")
        
        # 拉取最新镜像
        cmd = ["docker-compose", "-f", compose_file, "pull"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.warning(f"拉取镜像警告: {result.stderr}")
        
        # 构建镜像（如果需要）
        cmd = ["docker-compose", "-f", compose_file, "build"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.warning(f"构建镜像警告: {result.stderr}")
        
        # 启动服务
        return self._start_services(compose_file, [])
    
    def _check_status(self, compose_file: str) -> bool:
        """检查服务状态"""
        logger.info("检查服务状态...")
        cmd = ["docker-compose", "-f", compose_file, "ps"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(result.stdout)
        return result.returncode == 0
    
    def _show_logs(self, compose_file: str) -> bool:
        """显示服务日志"""
        logger.info("显示服务日志...")
        cmd = ["docker-compose", "-f", compose_file, "logs", "-f", "--tail=100"]
        
        # 直接显示日志，不捕获输出
        result = subprocess.run(cmd)
        return result.returncode == 0
    
    def _clean_environment(self, compose_file: str) -> bool:
        """清理环境"""
        logger.info("清理环境...")
        
        # 停止并删除容器
        cmd = ["docker-compose", "-f", compose_file, "down", "-v", "--remove-orphans"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("环境清理成功")
            return True
        else:
            logger.error(f"环境清理失败: {result.stderr}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="金融数据服务系统统一部署管理器")
    
    parser.add_argument("--env", choices=["dev", "test", "prod"], required=True,
                       help="部署环境")
    parser.add_argument("--action", choices=["start", "stop", "restart", "deploy", "status", "logs", "clean"], 
                       required=True, help="部署动作")
    
    args = parser.parse_args()
    
    # 转换为枚举
    env = Environment(args.env)
    action = Action(args.action)
    
    # 创建部署管理器并执行操作
    manager = UnifiedDeploymentManager()
    success = manager.execute_action(env, action)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
