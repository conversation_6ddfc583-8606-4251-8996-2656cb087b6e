#!/bin/bash

# Financial Data Service - Integration Test Runner
# Comprehensive integration testing script for system deployment validation

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_RESULTS_DIR="$PROJECT_ROOT/test_results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create test results directory
mkdir -p "$TEST_RESULTS_DIR"

# Test configuration
TEST_CONFIG="$PROJECT_ROOT/config/test_config.json"
if [[ ! -f "$TEST_CONFIG" ]]; then
    log "Creating test configuration..."
    cat > "$TEST_CONFIG" << EOF
{
    "test_environment": {
        "redis_host": "localhost",
        "redis_port": 6379,
        "clickhouse_host": "localhost",
        "clickhouse_port": 9000,
        "kafka_brokers": ["localhost:9092"],
        "websocket_port": 8080,
        "grpc_port": 50051,
        "api_port": 8080
    },
    "test_parameters": {
        "max_latency_us": 50,
        "min_throughput_per_sec": 1000000,
        "max_concurrent_clients": 1000,
        "test_duration_seconds": 60,
        "data_integrity_samples": 10000
    },
    "test_data": {
        "symbols": ["CU2409", "AL2409", "ZN2409", "AU2409", "AG2409"],
        "exchanges": ["SHFE", "DCE", "CZCE", "CFFEX"],
        "data_types": ["tick", "level2", "kline"]
    }
}
EOF
fi

# Pre-test system checks
run_pre_test_checks() {
    log "Running pre-test system checks..."
    
    # Check if services are running
    local services=("redis" "clickhouse" "kafka" "financial-app")
    for service in "${services[@]}"; do
        if ! docker ps | grep -q "$service"; then
            error "Service $service is not running. Please start the system first."
        fi
    done
    
    # Check system resources
    local available_memory=$(free -m | awk 'NR==2{print $7}')
    if [[ $available_memory -lt 4096 ]]; then
        warning "Low available memory: ${available_memory}MB. Tests may be affected."
    fi
    
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then
        warning "High CPU usage: ${cpu_usage}%. Tests may be affected."
    fi
    
    success "Pre-test checks completed"
}

# Build integration tests
build_integration_tests() {
    log "Building integration tests..."
    
    cd "$PROJECT_ROOT"
    
    # Create build directory
    mkdir -p build/tests
    cd build/tests
    
    # Configure CMake for tests
    cmake -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_TESTING=ON \
          -DBUILD_INTEGRATION_TESTS=ON \
          "$PROJECT_ROOT"
    
    # Build integration tests
    make -j$(nproc) integration_test_suite
    
    if [[ ! -f "./tests/integration/integration_test_suite" ]]; then
        error "Failed to build integration test suite"
    fi
    
    success "Integration tests built successfully"
}

# Run C++ integration tests
run_cpp_integration_tests() {
    log "Running C++ integration tests..."
    
    cd "$PROJECT_ROOT/build/tests"
    
    # Set test environment variables
    export GTEST_OUTPUT="xml:$TEST_RESULTS_DIR/cpp_integration_results_$TIMESTAMP.xml"
    export GTEST_COLOR=1
    
    # Run integration tests with timeout
    timeout 600 ./tests/integration/integration_test_suite \
        --gtest_filter="-*Stress*" \
        2>&1 | tee "$TEST_RESULTS_DIR/cpp_integration_log_$TIMESTAMP.txt"
    
    local exit_code=$?
    if [[ $exit_code -eq 0 ]]; then
        success "C++ integration tests passed"
    else
        error "C++ integration tests failed with exit code $exit_code"
    fi
}

# Run Python integration tests
run_python_integration_tests() {
    log "Running Python integration tests..."
    
    cd "$PROJECT_ROOT"
    
    # Create Python test script
    cat > "$TEST_RESULTS_DIR/python_integration_test.py" << 'EOF'
#!/usr/bin/env python3

import asyncio
import json
import time
import sys
import websockets
import grpc
import redis
import requests
from concurrent.futures import ThreadPoolExecutor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PythonIntegrationTest:
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
    
    def run_test(self, test_name, test_func):
        """Run a single test and record results"""
        self.test_results['total_tests'] += 1
        logger.info(f"Running test: {test_name}")
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                self.test_results['passed_tests'] += 1
                status = 'PASSED'
                logger.info(f"Test {test_name} PASSED ({end_time - start_time:.2f}s)")
            else:
                self.test_results['failed_tests'] += 1
                status = 'FAILED'
                logger.error(f"Test {test_name} FAILED ({end_time - start_time:.2f}s)")
            
            self.test_results['test_details'].append({
                'name': test_name,
                'status': status,
                'duration': end_time - start_time
            })
            
            return result
            
        except Exception as e:
            self.test_results['failed_tests'] += 1
            logger.error(f"Test {test_name} FAILED with exception: {e}")
            
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'FAILED',
                'error': str(e),
                'duration': 0
            })
            
            return False
    
    def test_redis_connection(self):
        """Test Redis connection and basic operations"""
        try:
            r = redis.Redis(
                host=self.config['test_environment']['redis_host'],
                port=self.config['test_environment']['redis_port'],
                decode_responses=True
            )
            
            # Test basic operations
            r.set('test_key', 'test_value')
            value = r.get('test_key')
            r.delete('test_key')
            
            return value == 'test_value'
        except Exception as e:
            logger.error(f"Redis test failed: {e}")
            return False
    
    def test_rest_api(self):
        """Test REST API endpoints"""
        try:
            base_url = f"http://localhost:{self.config['test_environment']['api_port']}"
            
            # Test health endpoint
            response = requests.get(f"{base_url}/health", timeout=10)
            if response.status_code != 200:
                return False
            
            # Test API documentation endpoint
            response = requests.get(f"{base_url}/api/docs", timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"REST API test failed: {e}")
            return False
    
    async def test_websocket_connection(self):
        """Test WebSocket connection and data subscription"""
        try:
            uri = f"ws://localhost:{self.config['test_environment']['websocket_port']}/ws"
            
            async with websockets.connect(uri) as websocket:
                # Send subscription message
                subscribe_msg = {
                    'type': 'subscribe',
                    'symbols': ['TEST_SYMBOL'],
                    'data_types': ['tick']
                }
                
                await websocket.send(json.dumps(subscribe_msg))
                
                # Wait for response
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                
                return 'type' in data
                
        except Exception as e:
            logger.error(f"WebSocket test failed: {e}")
            return False
    
    def test_grpc_connection(self):
        """Test gRPC connection"""
        try:
            channel = grpc.insecure_channel(
                f"localhost:{self.config['test_environment']['grpc_port']}"
            )
            
            # Test channel connectivity
            grpc.channel_ready_future(channel).result(timeout=10)
            channel.close()
            
            return True
            
        except Exception as e:
            logger.error(f"gRPC test failed: {e}")
            return False
    
    def test_concurrent_connections(self):
        """Test concurrent client connections"""
        try:
            def make_request():
                try:
                    response = requests.get(
                        f"http://localhost:{self.config['test_environment']['api_port']}/health",
                        timeout=5
                    )
                    return response.status_code == 200
                except:
                    return False
            
            # Test with 100 concurrent connections
            with ThreadPoolExecutor(max_workers=100) as executor:
                futures = [executor.submit(make_request) for _ in range(100)]
                results = [future.result() for future in futures]
            
            success_rate = sum(results) / len(results)
            return success_rate >= 0.95  # 95% success rate
            
        except Exception as e:
            logger.error(f"Concurrent connections test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all integration tests"""
        logger.info("Starting Python integration tests...")
        
        # Run synchronous tests
        self.run_test("Redis Connection", self.test_redis_connection)
        self.run_test("REST API", self.test_rest_api)
        self.run_test("gRPC Connection", self.test_grpc_connection)
        self.run_test("Concurrent Connections", self.test_concurrent_connections)
        
        # Run async tests
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        self.run_test("WebSocket Connection", 
                     lambda: loop.run_until_complete(self.test_websocket_connection()))
        
        loop.close()
        
        # Print results
        logger.info("Python integration test results:")
        logger.info(f"Total tests: {self.test_results['total_tests']}")
        logger.info(f"Passed: {self.test_results['passed_tests']}")
        logger.info(f"Failed: {self.test_results['failed_tests']}")
        
        # Save results to file
        with open('python_integration_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        return self.test_results['failed_tests'] == 0

if __name__ == '__main__':
    config_file = sys.argv[1] if len(sys.argv) > 1 else 'config/test_config.json'
    
    test_runner = PythonIntegrationTest(config_file)
    success = test_runner.run_all_tests()
    
    sys.exit(0 if success else 1)
EOF
    
    # Run Python tests
    cd "$TEST_RESULTS_DIR"
    python3 python_integration_test.py "$TEST_CONFIG" 2>&1 | \
        tee "python_integration_log_$TIMESTAMP.txt"
    
    local exit_code=$?
    if [[ $exit_code -eq 0 ]]; then
        success "Python integration tests passed"
    else
        error "Python integration tests failed with exit code $exit_code"
    fi
}

# Run performance tests
run_performance_tests() {
    log "Running performance tests..."
    
    cd "$PROJECT_ROOT/tests/performance"
    
    # Run performance test suite
    python3 run_python_tests.py --config "$TEST_CONFIG" \
        --output "$TEST_RESULTS_DIR/performance_results_$TIMESTAMP.json" \
        2>&1 | tee "$TEST_RESULTS_DIR/performance_log_$TIMESTAMP.txt"
    
    local exit_code=$?
    if [[ $exit_code -eq 0 ]]; then
        success "Performance tests completed"
    else
        warning "Performance tests completed with issues (exit code $exit_code)"
    fi
}

# Run stress tests
run_stress_tests() {
    log "Running stress tests..."
    
    cd "$PROJECT_ROOT/build/tests"
    
    # Run stress tests with extended timeout
    timeout 1800 ./tests/integration/integration_test_suite \
        --gtest_filter="*Stress*" \
        2>&1 | tee "$TEST_RESULTS_DIR/stress_test_log_$TIMESTAMP.txt"
    
    local exit_code=$?
    if [[ $exit_code -eq 0 ]]; then
        success "Stress tests passed"
    elif [[ $exit_code -eq 124 ]]; then
        warning "Stress tests timed out (30 minutes)"
    else
        warning "Stress tests completed with issues (exit code $exit_code)"
    fi
}

# Generate test report
generate_test_report() {
    log "Generating test report..."
    
    local report_file="$TEST_RESULTS_DIR/integration_test_report_$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Financial Data Service - Integration Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .passed { color: green; font-weight: bold; }
        .failed { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .log-section { background-color: #f9f9f9; padding: 10px; border-radius: 5px; }
        pre { white-space: pre-wrap; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Financial Data Service - Integration Test Report</h1>
        <p><strong>Generated:</strong> $(date)</p>
        <p><strong>Test Environment:</strong> $(hostname)</p>
        <p><strong>System Info:</strong> $(uname -a)</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <table>
            <tr><th>Test Suite</th><th>Status</th><th>Duration</th><th>Details</th></tr>
EOF
    
    # Add test results to report
    local cpp_status="UNKNOWN"
    local python_status="UNKNOWN"
    local performance_status="UNKNOWN"
    local stress_status="UNKNOWN"
    
    # Check test results
    if [[ -f "$TEST_RESULTS_DIR/cpp_integration_results_$TIMESTAMP.xml" ]]; then
        if grep -q 'failures="0"' "$TEST_RESULTS_DIR/cpp_integration_results_$TIMESTAMP.xml"; then
            cpp_status="PASSED"
        else
            cpp_status="FAILED"
        fi
    fi
    
    if [[ -f "$TEST_RESULTS_DIR/python_integration_results.json" ]]; then
        local failed_count=$(jq '.failed_tests' "$TEST_RESULTS_DIR/python_integration_results.json" 2>/dev/null || echo "0")
        if [[ "$failed_count" == "0" ]]; then
            python_status="PASSED"
        else
            python_status="FAILED"
        fi
    fi
    
    # Add results to HTML report
    cat >> "$report_file" << EOF
            <tr>
                <td>C++ Integration Tests</td>
                <td class="$(echo $cpp_status | tr '[:upper:]' '[:lower:]')">$cpp_status</td>
                <td>-</td>
                <td><a href="cpp_integration_log_$TIMESTAMP.txt">View Log</a></td>
            </tr>
            <tr>
                <td>Python Integration Tests</td>
                <td class="$(echo $python_status | tr '[:upper:]' '[:lower:]')">$python_status</td>
                <td>-</td>
                <td><a href="python_integration_log_$TIMESTAMP.txt">View Log</a></td>
            </tr>
            <tr>
                <td>Performance Tests</td>
                <td class="$(echo $performance_status | tr '[:upper:]' '[:lower:]')">$performance_status</td>
                <td>-</td>
                <td><a href="performance_log_$TIMESTAMP.txt">View Log</a></td>
            </tr>
            <tr>
                <td>Stress Tests</td>
                <td class="$(echo $stress_status | tr '[:upper:]' '[:lower:]')">$stress_status</td>
                <td>-</td>
                <td><a href="stress_test_log_$TIMESTAMP.txt">View Log</a></td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>System Information</h2>
        <div class="log-section">
            <h3>Docker Containers</h3>
            <pre>$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}")</pre>
            
            <h3>System Resources</h3>
            <pre>$(free -h)</pre>
            <pre>$(df -h)</pre>
        </div>
    </div>
    
    <div class="section">
        <h2>Test Artifacts</h2>
        <ul>
EOF
    
    # List all test artifacts
    for file in "$TEST_RESULTS_DIR"/*_$TIMESTAMP.*; do
        if [[ -f "$file" ]]; then
            local filename=$(basename "$file")
            echo "            <li><a href=\"$filename\">$filename</a></li>" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
        </ul>
    </div>
</body>
</html>
EOF
    
    success "Test report generated: $report_file"
}

# Cleanup function
cleanup() {
    log "Cleaning up test environment..."
    
    # Kill any remaining test processes
    pkill -f "integration_test_suite" || true
    pkill -f "python_integration_test.py" || true
    
    # Clean up temporary files
    rm -f "$TEST_RESULTS_DIR/python_integration_test.py"
    
    log "Cleanup completed"
}

# Main execution
main() {
    log "Starting Financial Data Service Integration Tests"
    log "================================================"
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    # Parse command line arguments
    local run_cpp=true
    local run_python=true
    local run_performance=true
    local run_stress=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --cpp-only)
                run_python=false
                run_performance=false
                run_stress=false
                shift
                ;;
            --python-only)
                run_cpp=false
                run_performance=false
                run_stress=false
                shift
                ;;
            --performance-only)
                run_cpp=false
                run_python=false
                run_stress=false
                shift
                ;;
            --include-stress)
                run_stress=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --cpp-only        Run only C++ integration tests"
                echo "  --python-only     Run only Python integration tests"
                echo "  --performance-only Run only performance tests"
                echo "  --include-stress  Include stress tests (long running)"
                echo "  --help           Show this help message"
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                ;;
        esac
    done
    
    # Run tests
    run_pre_test_checks
    
    if [[ "$run_cpp" == true ]]; then
        build_integration_tests
        run_cpp_integration_tests
    fi
    
    if [[ "$run_python" == true ]]; then
        run_python_integration_tests
    fi
    
    if [[ "$run_performance" == true ]]; then
        run_performance_tests
    fi
    
    if [[ "$run_stress" == true ]]; then
        run_stress_tests
    fi
    
    generate_test_report
    
    success "Integration tests completed successfully!"
    log "Test results available in: $TEST_RESULTS_DIR"
}

# Run main function
main "$@"