@echo off
REM Redis Cluster setup script for Windows

echo Setting up Redis Cluster for Financial Data Service...

REM Start Redis cluster nodes using Docker Compose
echo Starting Redis cluster nodes...
docker-compose --profile cluster up -d redis-cluster-1 redis-cluster-2 redis-cluster-3 redis-cluster-4 redis-cluster-5 redis-cluster-6

REM Wait for nodes to start
echo Waiting for Redis nodes to start...
timeout /t 10 /nobreak > nul

REM Create the cluster
echo Creating Redis cluster...
docker exec -it financial-redis-cluster-1 redis-cli --cluster create ^
  127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 ^
  127.0.0.1:7004 127.0.0.1:7005 127.0.0.1:7006 ^
  --cluster-replicas 1 --cluster-yes

REM Verify cluster status
echo Verifying cluster status...
docker exec -it financial-redis-cluster-1 redis-cli --cluster info 127.0.0.1:7001

echo Redis cluster setup completed!
echo Cluster nodes:
echo   Master nodes: 127.0.0.1:7001, 127.0.0.1:7002, 127.0.0.1:7003
echo   Replica nodes: 127.0.0.1:7004, 127.0.0.1:7005, 127.0.0.1:7006

pause