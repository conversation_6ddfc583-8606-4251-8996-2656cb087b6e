@echo off
REM Financial Data Service - Integration Test Runner (Windows)
REM Comprehensive integration testing script for system deployment validation

setlocal enabledelayedexpansion

set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set TEST_RESULTS_DIR=%PROJECT_ROOT%\test_results
set TIMESTAMP=%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

echo [%time%] Starting Financial Data Service Integration Tests
echo ================================================

REM Create test results directory
if not exist "%TEST_RESULTS_DIR%" mkdir "%TEST_RESULTS_DIR%"

REM Check if Docker is running
docker ps >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running or not accessible
    exit /b 1
)

REM Check if services are running
echo [%time%] Checking if services are running...
docker ps | findstr "redis" >nul
if errorlevel 1 (
    echo [ERROR] Redis service is not running
    exit /b 1
)

docker ps | findstr "clickhouse" >nul
if errorlevel 1 (
    echo [ERROR] ClickHouse service is not running
    exit /b 1
)

docker ps | findstr "kafka" >nul
if errorlevel 1 (
    echo [ERROR] Kafka service is not running
    exit /b 1
)

echo [SUCCESS] All required services are running

REM Build integration tests
echo [%time%] Building integration tests...
cd /d "%PROJECT_ROOT%"

if not exist "build\tests" mkdir "build\tests"
cd build\tests

cmake -DCMAKE_BUILD_TYPE=Release -DBUILD_TESTING=ON -DBUILD_INTEGRATION_TESTS=ON "%PROJECT_ROOT%"
if errorlevel 1 (
    echo [ERROR] CMake configuration failed
    exit /b 1
)

cmake --build . --target integration_test_suite --config Release
if errorlevel 1 (
    echo [ERROR] Build failed
    exit /b 1
)

echo [SUCCESS] Integration tests built successfully

REM Run C++ integration tests
echo [%time%] Running C++ integration tests...
if exist "tests\integration\integration_test_suite.exe" (
    tests\integration\integration_test_suite.exe --gtest_output=xml:"%TEST_RESULTS_DIR%\cpp_integration_results_%TIMESTAMP%.xml" > "%TEST_RESULTS_DIR%\cpp_integration_log_%TIMESTAMP%.txt" 2>&1
    if errorlevel 1 (
        echo [ERROR] C++ integration tests failed
        set CPP_TEST_STATUS=FAILED
    ) else (
        echo [SUCCESS] C++ integration tests passed
        set CPP_TEST_STATUS=PASSED
    )
) else (
    echo [ERROR] Integration test executable not found
    set CPP_TEST_STATUS=FAILED
)

REM Run Python integration tests
echo [%time%] Running Python integration tests...
cd /d "%PROJECT_ROOT%"

REM Create Python test script
echo import sys > "%TEST_RESULTS_DIR%\simple_python_test.py"
echo import requests >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo import json >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo. >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo def test_health_endpoint(): >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo     try: >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo         response = requests.get('http://localhost:8080/health', timeout=10) >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo         return response.status_code == 200 >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo     except: >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo         return False >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo. >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo if __name__ == '__main__': >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo     result = test_health_endpoint() >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo     print(f'Health endpoint test: {"PASSED" if result else "FAILED"}') >> "%TEST_RESULTS_DIR%\simple_python_test.py"
echo     sys.exit(0 if result else 1) >> "%TEST_RESULTS_DIR%\simple_python_test.py"

python "%TEST_RESULTS_DIR%\simple_python_test.py" > "%TEST_RESULTS_DIR%\python_integration_log_%TIMESTAMP%.txt" 2>&1
if errorlevel 1 (
    echo [ERROR] Python integration tests failed
    set PYTHON_TEST_STATUS=FAILED
) else (
    echo [SUCCESS] Python integration tests passed
    set PYTHON_TEST_STATUS=PASSED
)

REM Run basic performance tests
echo [%time%] Running basic performance tests...
cd /d "%PROJECT_ROOT%\tests\performance"

if exist "run_python_tests.py" (
    python run_python_tests.py --quick > "%TEST_RESULTS_DIR%\performance_log_%TIMESTAMP%.txt" 2>&1
    if errorlevel 1 (
        echo [WARNING] Performance tests completed with issues
        set PERFORMANCE_TEST_STATUS=WARNING
    ) else (
        echo [SUCCESS] Performance tests completed
        set PERFORMANCE_TEST_STATUS=PASSED
    )
) else (
    echo [WARNING] Performance test script not found
    set PERFORMANCE_TEST_STATUS=SKIPPED
)

REM Generate test report
echo [%time%] Generating test report...
set REPORT_FILE=%TEST_RESULTS_DIR%\integration_test_report_%TIMESTAMP%.html

echo ^<!DOCTYPE html^> > "%REPORT_FILE%"
echo ^<html^> >> "%REPORT_FILE%"
echo ^<head^> >> "%REPORT_FILE%"
echo     ^<title^>Financial Data Service - Integration Test Report^</title^> >> "%REPORT_FILE%"
echo     ^<style^> >> "%REPORT_FILE%"
echo         body { font-family: Arial, sans-serif; margin: 20px; } >> "%REPORT_FILE%"
echo         .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; } >> "%REPORT_FILE%"
echo         .section { margin: 20px 0; } >> "%REPORT_FILE%"
echo         .passed { color: green; font-weight: bold; } >> "%REPORT_FILE%"
echo         .failed { color: red; font-weight: bold; } >> "%REPORT_FILE%"
echo         .warning { color: orange; font-weight: bold; } >> "%REPORT_FILE%"
echo         table { border-collapse: collapse; width: 100%%; } >> "%REPORT_FILE%"
echo         th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } >> "%REPORT_FILE%"
echo         th { background-color: #f2f2f2; } >> "%REPORT_FILE%"
echo     ^</style^> >> "%REPORT_FILE%"
echo ^</head^> >> "%REPORT_FILE%"
echo ^<body^> >> "%REPORT_FILE%"
echo     ^<div class="header"^> >> "%REPORT_FILE%"
echo         ^<h1^>Financial Data Service - Integration Test Report^</h1^> >> "%REPORT_FILE%"
echo         ^<p^>^<strong^>Generated:^</strong^> %date% %time%^</p^> >> "%REPORT_FILE%"
echo         ^<p^>^<strong^>Test Environment:^</strong^> %COMPUTERNAME%^</p^> >> "%REPORT_FILE%"
echo     ^</div^> >> "%REPORT_FILE%"
echo     ^<div class="section"^> >> "%REPORT_FILE%"
echo         ^<h2^>Test Summary^</h2^> >> "%REPORT_FILE%"
echo         ^<table^> >> "%REPORT_FILE%"
echo             ^<tr^>^<th^>Test Suite^</th^>^<th^>Status^</th^>^<th^>Details^</th^>^</tr^> >> "%REPORT_FILE%"
echo             ^<tr^>^<td^>C++ Integration Tests^</td^>^<td class="%CPP_TEST_STATUS%"^>%CPP_TEST_STATUS%^</td^>^<td^>^<a href="cpp_integration_log_%TIMESTAMP%.txt"^>View Log^</a^>^</td^>^</tr^> >> "%REPORT_FILE%"
echo             ^<tr^>^<td^>Python Integration Tests^</td^>^<td class="%PYTHON_TEST_STATUS%"^>%PYTHON_TEST_STATUS%^</td^>^<td^>^<a href="python_integration_log_%TIMESTAMP%.txt"^>View Log^</a^>^</td^>^</tr^> >> "%REPORT_FILE%"
echo             ^<tr^>^<td^>Performance Tests^</td^>^<td class="%PERFORMANCE_TEST_STATUS%"^>%PERFORMANCE_TEST_STATUS%^</td^>^<td^>^<a href="performance_log_%TIMESTAMP%.txt"^>View Log^</a^>^</td^>^</tr^> >> "%REPORT_FILE%"
echo         ^</table^> >> "%REPORT_FILE%"
echo     ^</div^> >> "%REPORT_FILE%"
echo ^</body^> >> "%REPORT_FILE%"
echo ^</html^> >> "%REPORT_FILE%"

REM Cleanup
del "%TEST_RESULTS_DIR%\simple_python_test.py" 2>nul

REM Final status
echo.
echo ================================================
echo Integration Test Summary
echo ================================================
echo C++ Integration Tests: %CPP_TEST_STATUS%
echo Python Integration Tests: %PYTHON_TEST_STATUS%
echo Performance Tests: %PERFORMANCE_TEST_STATUS%
echo.
echo Test report generated: %REPORT_FILE%
echo Test results directory: %TEST_RESULTS_DIR%
echo ================================================

REM Determine overall exit code
if "%CPP_TEST_STATUS%"=="FAILED" (
    echo [ERROR] Integration tests failed
    exit /b 1
)

if "%PYTHON_TEST_STATUS%"=="FAILED" (
    echo [ERROR] Integration tests failed
    exit /b 1
)

echo [SUCCESS] Integration tests completed successfully!
exit /b 0