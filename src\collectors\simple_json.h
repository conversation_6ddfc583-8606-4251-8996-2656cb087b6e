#pragma once

#include <string>
#include <unordered_map>
#include <variant>
#include <vector>
#include <fstream>
#include <sstream>

namespace simple_json {

class json {
public:
    using value_type = std::variant<std::string, int, double, bool>;
    
private:
    std::unordered_map<std::string, value_type> data_;
    
public:
    json() = default;
    
    // 简化的JSON解析（仅支持基本的键值对）
    static json parse_simple_config(const std::string& content) {
        json result;
        std::istringstream iss(content);
        std::string line;
        
        while (std::getline(iss, line)) {
            // 简单解析 "key": "value" 格式
            size_t colon_pos = line.find(':');
            if (colon_pos != std::string::npos) {
                std::string key = line.substr(0, colon_pos);
                std::string value = line.substr(colon_pos + 1);
                
                // 清理空格和引号
                key.erase(0, key.find_first_not_of(" \t\""));
                key.erase(key.find_last_not_of(" \t\",") + 1);
                
                value.erase(0, value.find_first_not_of(" \t\""));
                value.erase(value.find_last_not_of(" \t\",") + 1);
                
                if (!key.empty() && !value.empty()) {
                    // 尝试解析为数字
                    try {
                        if (value.find('.') != std::string::npos) {
                            result.data_[key] = std::stod(value);
                        } else {
                            result.data_[key] = std::stoi(value);
                        }
                    } catch (...) {
                        // 如果不是数字，存储为字符串
                        result.data_[key] = value;
                    }
                }
            }
        }
        
        return result;
    }
    
    bool contains(const std::string& key) const {
        return data_.find(key) != data_.end();
    }
    
    template<typename T>
    T value(const std::string& key, const T& default_value) const {
        auto it = data_.find(key);
        if (it != data_.end()) {
            try {
                return std::get<T>(it->second);
            } catch (...) {
                return default_value;
            }
        }
        return default_value;
    }
    
    json operator[](const std::string& key) const {
        json result;
        // 简化实现，返回空对象
        return result;
    }
};

} // namespace simple_json

// Compatibility for nlohmann::json
namespace nlohmann = simple_json;