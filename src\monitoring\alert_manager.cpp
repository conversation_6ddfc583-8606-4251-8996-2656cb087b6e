#include "alert_manager.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <random>
#include <algorithm>
#include <ctime>

namespace monitoring {

// ConsoleAlertChannel implementation
bool ConsoleAlertChannel::sendAlert(const Alert& alert) {
    std::string severity_str;
    switch (alert.severity) {
        case AlertSeverity::INFO: severity_str = "INFO"; break;
        case AlertSeverity::WARNING: severity_str = "WARNING"; break;
        case AlertSeverity::CRITICAL: severity_str = "CRITICAL"; break;
    }
    
    auto time_t = std::chrono::system_clock::to_time_t(
        std::chrono::system_clock::now());
    
    std::cout << "\n=== ALERT [" << severity_str << "] ===" << std::endl;
    std::cout << "Time: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << std::endl;
    std::cout << "Type: " << alert.type << std::endl;
    std::cout << "ID: " << alert.id << std::endl;
    std::cout << "Message: " << alert.message << std::endl;
    
    if (!alert.metadata.empty()) {
        std::cout << "Metadata:" << std::endl;
        for (const auto& [key, value] : alert.metadata) {
            std::cout << "  " << key << ": " << value << std::endl;
        }
    }
    std::cout << "=========================" << std::endl;
    
    return true;
}

// EmailAlertChannel implementation
EmailAlertChannel::EmailAlertChannel(const std::string& smtp_server, int port,
                                   const std::string& username, const std::string& password,
                                   const std::vector<std::string>& recipients)
    : smtp_server_(smtp_server), port_(port), username_(username), 
      password_(password), recipients_(recipients) {
}

bool EmailAlertChannel::sendAlert(const Alert& alert) {
    // Simplified email implementation - in production, use a proper SMTP library
    std::cout << "EMAIL ALERT: Would send email to " << recipients_.size() 
              << " recipients about " << alert.type << std::endl;
    
    // Simulate email sending delay
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    return true; // Assume success for demo
}

// WebhookAlertChannel implementation
WebhookAlertChannel::WebhookAlertChannel(const std::string& webhook_url)
    : webhook_url_(webhook_url) {
}

bool WebhookAlertChannel::sendAlert(const Alert& alert) {
    // Simplified webhook implementation - in production, use HTTP client library
    std::cout << "WEBHOOK ALERT: Would POST to " << webhook_url_ 
              << " about " << alert.type << std::endl;
    
    // Simulate HTTP request delay
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    return true; // Assume success for demo
}

// SlackAlertChannel implementation
SlackAlertChannel::SlackAlertChannel(const std::string& webhook_url, const std::string& channel)
    : webhook_url_(webhook_url), channel_(channel) {
}

bool SlackAlertChannel::sendAlert(const Alert& alert) {
    std::string severity_emoji;
    switch (alert.severity) {
        case AlertSeverity::INFO: severity_emoji = ":information_source:"; break;
        case AlertSeverity::WARNING: severity_emoji = ":warning:"; break;
        case AlertSeverity::CRITICAL: severity_emoji = ":rotating_light:"; break;
    }
    
    std::cout << "SLACK ALERT: Would send to " << channel_ << " - " 
              << severity_emoji << " " << alert.type << std::endl;
    
    // Simulate Slack API delay
    std::this_thread::sleep_for(std::chrono::milliseconds(75));
    
    return true; // Assume success for demo
}

// AlertManager implementation
AlertManager::AlertManager() {
    // Add default console channel
    addChannel(std::make_unique<ConsoleAlertChannel>());
}

AlertManager::~AlertManager() {
    stop();
}

bool AlertManager::start() {
    if (running_.load()) {
        return true;
    }
    
    running_.store(true);
    processing_thread_ = std::thread(&AlertManager::processingLoop, this);
    
    std::cout << "Alert manager started with " << channels_.size() << " channels" << std::endl;
    return true;
}

void AlertManager::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    queue_cv_.notify_all();
    
    if (processing_thread_.joinable()) {
        processing_thread_.join();
    }
    
    std::cout << "Alert manager stopped" << std::endl;
}

void AlertManager::addChannel(std::unique_ptr<AlertChannel> channel) {
    std::lock_guard<std::mutex> lock(channels_mutex_);
    std::cout << "Added alert channel: " << channel->getName() << std::endl;
    channels_.push_back(std::move(channel));
}

void AlertManager::removeChannel(const std::string& channel_name) {
    std::lock_guard<std::mutex> lock(channels_mutex_);
    
    auto it = std::remove_if(channels_.begin(), channels_.end(),
        [&channel_name](const std::unique_ptr<AlertChannel>& channel) {
            return channel->getName() == channel_name;
        });
    
    if (it != channels_.end()) {
        channels_.erase(it, channels_.end());
        std::cout << "Removed alert channel: " << channel_name << std::endl;
    }
}

void AlertManager::sendAlert(const std::string& type, const std::string& severity,
                           const std::string& message,
                           const std::unordered_map<std::string, std::string>& metadata) {
    
    // Check rate limiting
    if (isRateLimited(type)) {
        updateStatistics(Alert{}, false); // Record rate limited alert
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.alerts_rate_limited++;
        return;
    }
    
    Alert alert;
    alert.type = type;
    alert.severity = parseSeverity(severity);
    alert.message = message;
    alert.metadata = metadata;
    alert.timestamp = std::chrono::high_resolution_clock::now();
    alert.id = generateAlertId();
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        alert_queue_.push(alert);
    }
    queue_cv_.notify_one();
    
    updateRateLimit(type);
}

void AlertManager::processingLoop() {
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        queue_cv_.wait(lock, [this] {
            return !alert_queue_.empty() || !running_.load();
        });
        
        while (!alert_queue_.empty() && running_.load()) {
            Alert alert = alert_queue_.front();
            alert_queue_.pop();
            lock.unlock();
            
            processAlert(alert);
            
            lock.lock();
        }
        
        // Cleanup rate limiting periodically
        if (running_.load()) {
            lock.unlock();
            cleanupRateLimit();
            lock.lock();
        }
    }
}

void AlertManager::processAlert(const Alert& alert) {
    addToHistory(alert);
    
    std::cout << "Processing alert: " << formatAlertForLogging(alert) << std::endl;
    
    bool success = sendAlertToChannels(alert);
    updateStatistics(alert, success);
}

bool AlertManager::sendAlertToChannels(const Alert& alert) {
    std::lock_guard<std::mutex> lock(channels_mutex_);
    
    if (channels_.empty()) {
        std::cerr << "No alert channels configured!" << std::endl;
        return false;
    }
    
    bool overall_success = true;
    
    for (auto& channel : channels_) {
        bool channel_success = sendAlertWithRetry(channel.get(), alert);
        updateStatistics(alert, channel_success, channel->getName());
        
        if (!channel_success) {
            overall_success = false;
        }
    }
    
    return overall_success;
}

bool AlertManager::sendAlertWithRetry(AlertChannel* channel, const Alert& alert) {
    for (int attempt = 0; attempt <= max_retries_; ++attempt) {
        try {
            if (channel->sendAlert(alert)) {
                if (attempt > 0) {
                    std::cout << "Alert sent successfully to " << channel->getName() 
                              << " after " << attempt << " retries" << std::endl;
                }
                return true;
            }
        } catch (const std::exception& e) {
            std::cerr << "Exception sending alert to " << channel->getName() 
                      << ": " << e.what() << std::endl;
        }
        
        if (attempt < max_retries_) {
            std::cout << "Retrying alert to " << channel->getName() 
                      << " in " << retry_delay_.count() << " seconds..." << std::endl;
            std::this_thread::sleep_for(retry_delay_);
        }
    }
    
    std::cerr << "Failed to send alert to " << channel->getName() 
              << " after " << max_retries_ << " retries" << std::endl;
    return false;
}

bool AlertManager::isRateLimited(const std::string& alert_type) {
    std::lock_guard<std::mutex> lock(rate_limit_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    int count = 0;
    
    // Count alerts of this type in the current window
    std::queue<RateLimitEntry> temp_queue = rate_limit_queue_;
    while (!temp_queue.empty()) {
        const auto& entry = temp_queue.front();
        if (entry.alert_type == alert_type && 
            (now - entry.timestamp) <= rate_limit_window_) {
            count++;
        }
        temp_queue.pop();
    }
    
    return count >= max_alerts_per_window_;
}

void AlertManager::updateRateLimit(const std::string& alert_type) {
    std::lock_guard<std::mutex> lock(rate_limit_mutex_);
    
    RateLimitEntry entry;
    entry.timestamp = std::chrono::steady_clock::now();
    entry.alert_type = alert_type;
    
    rate_limit_queue_.push(entry);
}

void AlertManager::cleanupRateLimit() {
    std::lock_guard<std::mutex> lock(rate_limit_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    
    while (!rate_limit_queue_.empty()) {
        const auto& entry = rate_limit_queue_.front();
        if ((now - entry.timestamp) > rate_limit_window_) {
            rate_limit_queue_.pop();
        } else {
            break; // Queue is ordered by time, so we can stop here
        }
    }
}

AlertSeverity AlertManager::parseSeverity(const std::string& severity_str) {
    std::string lower_severity = severity_str;
    std::transform(lower_severity.begin(), lower_severity.end(), 
                  lower_severity.begin(), ::tolower);
    
    if (lower_severity == "critical") return AlertSeverity::CRITICAL;
    if (lower_severity == "warning") return AlertSeverity::WARNING;
    return AlertSeverity::INFO;
}

std::string AlertManager::generateAlertId() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);
    
    std::ostringstream oss;
    for (int i = 0; i < 8; ++i) {
        oss << std::hex << dis(gen);
    }
    
    return oss.str();
}

std::string AlertManager::formatAlertForLogging(const Alert& alert) {
    std::ostringstream oss;
    oss << "[" << alert.id << "] " << alert.type;
    
    switch (alert.severity) {
        case AlertSeverity::INFO: oss << " (INFO)"; break;
        case AlertSeverity::WARNING: oss << " (WARNING)"; break;
        case AlertSeverity::CRITICAL: oss << " (CRITICAL)"; break;
    }
    
    return oss.str();
}

void AlertManager::addToHistory(const Alert& alert) {
    std::lock_guard<std::mutex> lock(history_mutex_);
    
    alert_history_.push_back(alert);
    
    // Keep history size under limit
    if (alert_history_.size() > MAX_HISTORY_SIZE) {
        alert_history_.erase(alert_history_.begin(), 
                           alert_history_.begin() + (alert_history_.size() - MAX_HISTORY_SIZE));
    }
}

void AlertManager::updateStatistics(const Alert& alert, bool success, const std::string& channel_name) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (success) {
        stats_.total_alerts_sent++;
        if (!channel_name.empty()) {
            stats_.channel_success_count[channel_name]++;
        }
    } else {
        stats_.total_alerts_failed++;
        if (!channel_name.empty()) {
            stats_.channel_failure_count[channel_name]++;
        }
    }
    
    if (!alert.type.empty()) {
        stats_.alerts_by_type[alert.type]++;
        
        std::string severity_str;
        switch (alert.severity) {
            case AlertSeverity::INFO: severity_str = "info"; break;
            case AlertSeverity::WARNING: severity_str = "warning"; break;
            case AlertSeverity::CRITICAL: severity_str = "critical"; break;
        }
        stats_.alerts_by_severity[severity_str]++;
    }
}

AlertManager::AlertStats AlertManager::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void AlertManager::resetStatistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = {};
}

std::vector<Alert> AlertManager::getRecentAlerts(int count) const {
    std::lock_guard<std::mutex> lock(history_mutex_);
    
    if (count >= static_cast<int>(alert_history_.size())) {
        return alert_history_;
    }
    
    return std::vector<Alert>(alert_history_.end() - count, alert_history_.end());
}

void AlertManager::clearAlertHistory() {
    std::lock_guard<std::mutex> lock(history_mutex_);
    alert_history_.clear();
}

} // namespace monitoring