#include "lifecycle_manager.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <random>
#include <filesystem>
#include <regex>
#include <json/json.h>

namespace financial_data {
namespace storage {

// Cron调度器简单实现
class LifecycleManager::CronScheduler {
public:
    CronScheduler(const std::string& cron_expression) : cron_expression_(cron_expression) {}
    
    bool ShouldRun() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        // 简化的cron解析，只支持 "0 2 * * *" 格式（每天凌晨2点）
        if (cron_expression_ == "0 2 * * *") {
            return tm.tm_hour == 2 && tm.tm_min == 0 && 
                   (now - last_run_) > std::chrono::hours(23);
        }
        
        return false;
    }
    
    void MarkRun() {
        last_run_ = std::chrono::system_clock::now();
    }
    
private:
    std::string cron_expression_;
    std::chrono::system_clock::time_point last_run_;
};

LifecycleManager::LifecycleManager(std::shared_ptr<ColdDataStorage> cold_storage)
    : cold_storage_(cold_storage) {
}

LifecycleManager::~LifecycleManager() {
    StopAutomaticMigration();
}

bool LifecycleManager::Initialize(const MigrationPolicy& policy) {
    policy_ = policy;
    scheduler_ = std::make_unique<CronScheduler>(policy.cron_schedule);
    
    // 加载之前的任务状态
    LoadTaskState();
    
    std::cout << "Lifecycle manager initialized with policy:" << std::endl;
    std::cout << "  Warm to cold migration: " << policy_.warm_to_cold_days << " days" << std::endl;
    std::cout << "  Data retention: " << policy_.retention_years << " years" << std::endl;
    std::cout << "  Schedule: " << policy_.cron_schedule << std::endl;
    std::cout << "  S3 backup: " << (policy_.enable_s3_backup ? "enabled" : "disabled") << std::endl;
    
    return true;
}

void LifecycleManager::StartAutomaticMigration() {
    if (running_) {
        return;
    }
    
    running_ = true;
    migration_thread_ = std::thread(&LifecycleManager::MigrationWorker, this);
    
    std::cout << "Automatic migration started" << std::endl;
}

void LifecycleManager::StopAutomaticMigration() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    if (migration_thread_.joinable()) {
        migration_thread_.join();
    }
    
    // 保存任务状态
    SaveTaskState();
    
    std::cout << "Automatic migration stopped" << std::endl;
}

void LifecycleManager::MigrationWorker() {
    while (running_) {
        try {
            // 检查是否需要按计划执行迁移
            if (scheduler_->ShouldRun()) {
                std::cout << "Scheduled migration triggered" << std::endl;
                auto future = TriggerMigration();
                future.wait();
                scheduler_->MarkRun();
            }
            
            // 处理待处理的任务
            ProcessPendingTasks();
            
            // 每分钟检查一次
            std::this_thread::sleep_for(std::chrono::minutes(1));
            
        } catch (const std::exception& e) {
            std::cerr << "Migration worker error: " << e.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::minutes(5));
        }
    }
}

std::future<bool> LifecycleManager::TriggerMigration() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            std::cout << "Starting automatic data migration..." << std::endl;
            
            // 查找需要迁移的数据
            auto data_ranges = FindDataToMigrate();
            
            std::cout << "Found " << data_ranges.size() << " data ranges to migrate" << std::endl;
            
            size_t total_tasks = 0;
            for (const auto& range : data_ranges) {
                std::string task_id = ScheduleMigrationTask(
                    range.symbol, range.exchange, range.start_time, range.end_time);
                
                if (!task_id.empty()) {
                    total_tasks++;
                }
            }
            
            std::cout << "Scheduled " << total_tasks << " migration tasks" << std::endl;
            return total_tasks > 0;
            
        } catch (const std::exception& e) {
            std::cerr << "Error triggering migration: " << e.what() << std::endl;
            return false;
        }
    });
}

std::string LifecycleManager::ScheduleMigrationTask(const std::string& symbol,
                                                   const std::string& exchange,
                                                   const std::chrono::system_clock::time_point& start_date,
                                                   const std::chrono::system_clock::time_point& end_date) {
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    MigrationTask task;
    task.task_id = "task_" + std::to_string(++task_counter_);
    task.symbol = symbol;
    task.exchange = exchange;
    task.start_date = start_date;
    task.end_date = end_date;
    task.source_storage = "warm";
    task.target_storage = "cold";
    task.created_at = std::chrono::system_clock::now();
    task.scheduled_at = std::chrono::system_clock::now();
    task.status = MigrationTask::PENDING;
    
    // 估算记录数（简化实现）
    auto duration = end_date - start_date;
    auto days = std::chrono::duration_cast<std::chrono::hours>(duration).count() / 24;
    task.estimated_records = days * 100000; // 假设每天10万条记录
    
    migration_tasks_.push_back(task);
    
    std::cout << "Scheduled migration task " << task.task_id 
              << " for " << symbol << " (" << exchange << ")" << std::endl;
    
    return task.task_id;
}

void LifecycleManager::ProcessPendingTasks() {
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    for (auto& task : migration_tasks_) {
        if (task.status == MigrationTask::PENDING) {
            // 检查是否到了执行时间
            auto now = std::chrono::system_clock::now();
            if (now >= task.scheduled_at) {
                task.status = MigrationTask::RUNNING;
                
                // 异步执行任务
                std::thread([this, task_id = task.task_id]() mutable {
                    std::lock_guard<std::mutex> task_lock(tasks_mutex_);
                    auto it = std::find_if(migration_tasks_.begin(), migration_tasks_.end(),
                        [&task_id](const MigrationTask& t) { return t.task_id == task_id; });
                    
                    if (it != migration_tasks_.end()) {
                        bool success = ExecuteMigrationTask(*it);
                        it->status = success ? MigrationTask::COMPLETED : MigrationTask::FAILED;
                        
                        if (success) {
                            NotifyTaskCompleted(*it);
                        } else {
                            NotifyTaskFailed(*it);
                        }
                        
                        UpdateStats(*it);
                    }
                }).detach();
                
                break; // 一次只处理一个任务
            }
        }
    }
}

bool LifecycleManager::ExecuteMigrationTask(MigrationTask& task) {
    try {
        std::cout << "Executing migration task " << task.task_id << std::endl;
        
        auto start_time = std::chrono::steady_clock::now();
        
        // 这里应该从ClickHouse查询数据
        // 简化实现：创建模拟数据
        TickDataBatch batch;
        batch.reserve(task.estimated_records);
        
        // 生成模拟数据
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<> price_dist(50000.0, 80000.0);
        std::uniform_int_distribution<> volume_dist(1, 1000);
        
        auto current_time = std::chrono::duration_cast<std::chrono::nanoseconds>(
            task.start_date.time_since_epoch()).count();
        
        for (size_t i = 0; i < std::min(task.estimated_records, size_t(10000)); ++i) {
            batch.timestamps.push_back(current_time + i * 1000000); // 每毫秒一条
            batch.symbols.push_back(task.symbol);
            batch.exchanges.push_back(task.exchange);
            batch.last_prices.push_back(price_dist(gen));
            batch.volumes.push_back(volume_dist(gen));
            batch.turnovers.push_back(batch.last_prices.back() * batch.volumes.back());
            batch.open_interests.push_back(volume_dist(gen) * 100);
            batch.sequences.push_back(static_cast<uint32_t>(i));
            
            // 简化的买卖盘数据
            batch.bid_prices.push_back({price_dist(gen), price_dist(gen), price_dist(gen), price_dist(gen), price_dist(gen)});
            batch.bid_volumes.push_back({volume_dist(gen), volume_dist(gen), volume_dist(gen), volume_dist(gen), volume_dist(gen)});
            batch.ask_prices.push_back({price_dist(gen), price_dist(gen), price_dist(gen), price_dist(gen), price_dist(gen)});
            batch.ask_volumes.push_back({volume_dist(gen), volume_dist(gen), volume_dist(gen), volume_dist(gen), volume_dist(gen)});
            
            // 更新进度
            task.progress = static_cast<double>(i + 1) / batch.size() * 100.0;
            task.processed_records = i + 1;
        }
        
        // 归档数据到冷存储
        auto archive_future = cold_storage_->ArchiveData(batch, task.symbol, task.exchange, task.start_date);
        bool success = archive_future.get();
        
        if (success) {
            task.progress = 100.0;
            task.processed_records = batch.size();
            
            auto end_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            LogMigrationMetrics(task, duration);
            
            std::cout << "Migration task " << task.task_id << " completed successfully" << std::endl;
            std::cout << "  Processed " << task.processed_records << " records" << std::endl;
            std::cout << "  Duration: " << duration.count() << " ms" << std::endl;
        } else {
            task.error_message = "Failed to archive data to cold storage";
            std::cerr << "Migration task " << task.task_id << " failed: " << task.error_message << std::endl;
        }
        
        return success;
        
    } catch (const std::exception& e) {
        task.error_message = e.what();
        std::cerr << "Migration task " << task.task_id << " failed with exception: " << e.what() << std::endl;
        return false;
    }
}

std::vector<LifecycleManager::DataRange> LifecycleManager::FindDataToMigrate() {
    std::vector<DataRange> ranges;
    
    try {
        // 这里应该查询ClickHouse数据库找到超过阈值的数据
        // 简化实现：返回一些模拟数据范围
        
        auto threshold_time = std::chrono::system_clock::now() - 
                             std::chrono::hours(24 * policy_.warm_to_cold_days);
        
        // 模拟一些需要迁移的数据
        std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409", "AU2412"};
        std::vector<std::string> exchanges = {"SHFE", "SHFE", "SHFE", "SHFE"};
        
        for (size_t i = 0; i < symbols.size(); ++i) {
            DataRange range;
            range.symbol = symbols[i];
            range.exchange = exchanges[i];
            range.start_time = threshold_time - std::chrono::hours(24);
            range.end_time = threshold_time;
            range.record_count = 100000;
            ranges.push_back(range);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error finding data to migrate: " << e.what() << std::endl;
    }
    
    return ranges;
}

MigrationTask LifecycleManager::GetMigrationTask(const std::string& task_id) {
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    auto it = std::find_if(migration_tasks_.begin(), migration_tasks_.end(),
        [&task_id](const MigrationTask& task) { return task.task_id == task_id; });
    
    if (it != migration_tasks_.end()) {
        return *it;
    }
    
    return MigrationTask{}; // 返回空任务
}

std::vector<MigrationTask> LifecycleManager::GetMigrationTasks(MigrationTask::Status status) {
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    std::vector<MigrationTask> result;
    for (const auto& task : migration_tasks_) {
        if (status == MigrationTask::PENDING || task.status == status) {
            result.push_back(task);
        }
    }
    
    return result;
}

MigrationStats LifecycleManager::GetMigrationStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void LifecycleManager::UpdateStats(const MigrationTask& task) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    stats_.total_tasks++;
    
    switch (task.status) {
        case MigrationTask::COMPLETED:
            stats_.completed_tasks++;
            stats_.total_migrated_records += task.processed_records;
            stats_.last_migration_time = std::chrono::system_clock::now();
            break;
        case MigrationTask::FAILED:
            stats_.failed_tasks++;
            break;
        case MigrationTask::PENDING:
            stats_.pending_tasks++;
            break;
        case MigrationTask::RUNNING:
            stats_.running_tasks++;
            break;
        default:
            break;
    }
}

void LifecycleManager::LogMigrationMetrics(const MigrationTask& task, 
                                          std::chrono::milliseconds duration) {
    // 记录迁移指标到日志文件
    std::ofstream log_file("migration_metrics.log", std::ios::app);
    if (log_file.is_open()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        log_file << std::put_time(std::gmtime(&time_t), "%Y-%m-%d %H:%M:%S")
                 << " [MIGRATION] "
                 << "task_id=" << task.task_id
                 << " symbol=" << task.symbol
                 << " exchange=" << task.exchange
                 << " records=" << task.processed_records
                 << " duration_ms=" << duration.count()
                 << " throughput=" << (task.processed_records * 1000 / duration.count()) << "rec/s"
                 << std::endl;
    }
}

bool LifecycleManager::SaveTaskState() {
    try {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        
        Json::Value root;
        Json::Value tasks(Json::arrayValue);
        
        for (const auto& task : migration_tasks_) {
            Json::Value task_json;
            task_json["task_id"] = task.task_id;
            task_json["symbol"] = task.symbol;
            task_json["exchange"] = task.exchange;
            task_json["start_date"] = std::chrono::duration_cast<std::chrono::seconds>(
                task.start_date.time_since_epoch()).count();
            task_json["end_date"] = std::chrono::duration_cast<std::chrono::seconds>(
                task.end_date.time_since_epoch()).count();
            task_json["source_storage"] = task.source_storage;
            task_json["target_storage"] = task.target_storage;
            task_json["estimated_records"] = static_cast<Json::UInt64>(task.estimated_records);
            task_json["status"] = static_cast<int>(task.status);
            task_json["progress"] = task.progress;
            task_json["processed_records"] = static_cast<Json::UInt64>(task.processed_records);
            task_json["error_message"] = task.error_message;
            
            tasks.append(task_json);
        }
        
        root["tasks"] = tasks;
        
        std::ofstream file("migration_tasks.json");
        Json::StreamWriterBuilder builder;
        std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
        writer->write(root, &file);
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error saving task state: " << e.what() << std::endl;
        return false;
    }
}

bool LifecycleManager::LoadTaskState() {
    try {
        if (!std::filesystem::exists("migration_tasks.json")) {
            return true; // 文件不存在是正常的
        }
        
        std::ifstream file("migration_tasks.json");
        Json::Value root;
        Json::CharReaderBuilder builder;
        std::string errors;
        
        if (!Json::parseFromStream(builder, file, &root, &errors)) {
            std::cerr << "Error parsing task state file: " << errors << std::endl;
            return false;
        }
        
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        migration_tasks_.clear();
        
        const Json::Value& tasks = root["tasks"];
        for (const auto& task_json : tasks) {
            MigrationTask task;
            task.task_id = task_json["task_id"].asString();
            task.symbol = task_json["symbol"].asString();
            task.exchange = task_json["exchange"].asString();
            
            task.start_date = std::chrono::system_clock::from_time_t(
                task_json["start_date"].asInt64());
            task.end_date = std::chrono::system_clock::from_time_t(
                task_json["end_date"].asInt64());
            
            task.source_storage = task_json["source_storage"].asString();
            task.target_storage = task_json["target_storage"].asString();
            task.estimated_records = task_json["estimated_records"].asUInt64();
            task.status = static_cast<MigrationTask::Status>(task_json["status"].asInt());
            task.progress = task_json["progress"].asDouble();
            task.processed_records = task_json["processed_records"].asUInt64();
            task.error_message = task_json["error_message"].asString();
            
            migration_tasks_.push_back(task);
        }
        
        std::cout << "Loaded " << migration_tasks_.size() << " migration tasks from state file" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading task state: " << e.what() << std::endl;
        return false;
    }
}

void LifecycleManager::NotifyTaskCompleted(const MigrationTask& task) {
    std::cout << "Migration task completed: " << task.task_id 
              << " (" << task.processed_records << " records)" << std::endl;
}

void LifecycleManager::NotifyTaskFailed(const MigrationTask& task) {
    std::cerr << "Migration task failed: " << task.task_id 
              << " - " << task.error_message << std::endl;
}

LifecycleManager::HealthStatus LifecycleManager::CheckHealth() {
    HealthStatus status;
    status.last_check_time = std::chrono::system_clock::now();
    
    try {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        
        status.pending_tasks = std::count_if(migration_tasks_.begin(), migration_tasks_.end(),
            [](const MigrationTask& task) { return task.status == MigrationTask::PENDING; });
        
        status.failed_tasks = std::count_if(migration_tasks_.begin(), migration_tasks_.end(),
            [](const MigrationTask& task) { return task.status == MigrationTask::FAILED; });
        
        // 检查是否有太多失败的任务
        if (status.failed_tasks > 10) {
            status.is_healthy = false;
            status.status_message = "Too many failed migration tasks";
        }
        
        // 检查是否有长时间未完成的任务
        auto now = std::chrono::system_clock::now();
        for (const auto& task : migration_tasks_) {
            if (task.status == MigrationTask::RUNNING) {
                auto duration = now - task.scheduled_at;
                if (duration > std::chrono::hours(24)) {
                    status.is_healthy = false;
                    status.status_message = "Long running migration task detected";
                    break;
                }
            }
        }
        
        if (status.is_healthy) {
            status.status_message = "All systems operational";
        }
        
    } catch (const std::exception& e) {
        status.is_healthy = false;
        status.status_message = "Health check failed: " + std::string(e.what());
    }
    
    return status;
}

// StorageAnalyzer实现
StorageAnalyzer::StorageAnalyzer(std::shared_ptr<ColdDataStorage> cold_storage)
    : cold_storage_(cold_storage) {
}

std::future<StorageAnalyzer::StorageReport> StorageAnalyzer::GenerateReport() {
    return std::async(std::launch::async, [this]() -> StorageReport {
        StorageReport report;
        report.generated_at = std::chrono::system_clock::now();
        
        try {
            // 分析各存储层
            report.hot_storage = AnalyzeLayer("hot");
            report.warm_storage = AnalyzeLayer("warm");
            report.cold_storage = AnalyzeLayer("cold");
            
            // 计算总体统计
            report.total_size = report.hot_storage.total_size + 
                               report.warm_storage.total_size + 
                               report.cold_storage.total_size;
            
            // 获取存储统计信息
            auto stats = cold_storage_->GetStorageStats();
            report.compression_ratio = stats.average_compression_ratio;
            
            // 生成建议
            report.recommendations = GenerateRecommendations(report);
            
        } catch (const std::exception& e) {
            std::cerr << "Error generating storage report: " << e.what() << std::endl;
        }
        
        return report;
    });
}

StorageAnalyzer::StorageReport::LayerInfo StorageAnalyzer::AnalyzeLayer(const std::string& layer_name) {
    StorageReport::LayerInfo info;
    info.layer_name = layer_name;
    
    // 简化实现：返回模拟数据
    if (layer_name == "hot") {
        info.total_size = 100 * 1024 * 1024 * 1024ULL; // 100GB
        info.file_count = 1000;
        info.record_count = 10000000;
        info.utilization = 0.8;
    } else if (layer_name == "warm") {
        info.total_size = 1024 * 1024 * 1024 * 1024ULL; // 1TB
        info.file_count = 10000;
        info.record_count = 100000000;
        info.utilization = 0.6;
    } else if (layer_name == "cold") {
        info.total_size = 10 * 1024 * 1024 * 1024 * 1024ULL; // 10TB
        info.file_count = 100000;
        info.record_count = 1000000000;
        info.utilization = 0.4;
    }
    
    return info;
}

std::vector<std::string> StorageAnalyzer::GenerateRecommendations(const StorageReport& report) {
    std::vector<std::string> recommendations;
    
    if (report.hot_storage.utilization > 0.9) {
        recommendations.push_back("Hot storage utilization is high, consider migrating data to warm storage");
    }
    
    if (report.warm_storage.utilization > 0.8) {
        recommendations.push_back("Warm storage utilization is high, consider migrating data to cold storage");
    }
    
    if (report.compression_ratio < 5.0) {
        recommendations.push_back("Compression ratio is low, consider optimizing compression settings");
    }
    
    if (report.duplicate_data_size > 0) {
        recommendations.push_back("Duplicate data detected, consider deduplication");
    }
    
    return recommendations;
}

// DataIntegrityValidator实现
DataIntegrityValidator::DataIntegrityValidator(std::shared_ptr<ColdDataStorage> cold_storage)
    : cold_storage_(cold_storage) {
}

std::future<DataIntegrityValidator::ValidationResult> DataIntegrityValidator::ValidateFile(const std::string& file_path) {
    return std::async(std::launch::async, [this, file_path]() -> ValidationResult {
        ValidationResult result;
        result.file_path = file_path;
        result.validated_at = std::chrono::system_clock::now();
        
        try {
            // 验证文件完整性
            auto verify_future = cold_storage_->VerifyDataIntegrity(file_path);
            result.is_valid = verify_future.get();
            
            if (!result.is_valid) {
                result.error_message = "Data integrity check failed";
            }
            
        } catch (const std::exception& e) {
            result.is_valid = false;
            result.error_message = e.what();
        }
        
        return result;
    });
}

} // namespace storage
} // namespace financial_data