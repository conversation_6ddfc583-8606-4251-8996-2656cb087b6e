version: '3.8'

services:
  # MinIO集群节点1
  minio1:
    image: minio/minio:RELEASE.2024-07-16T23-46-41Z
    hostname: minio1
    volumes:
      - minio1-data:/data1
      - minio1-data2:/data2
    ports:
      - "9001:9000"
      - "9011:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-admin123456}
      MINIO_PROMETHEUS_AUTH_TYPE: public
    command: server --console-address ":9001" http://minio{1...4}/data{1...2}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - minio-cluster

  # MinIO集群节点2
  minio2:
    image: minio/minio:RELEASE.2024-07-16T23-46-41Z
    hostname: minio2
    volumes:
      - minio2-data:/data1
      - minio2-data2:/data2
    ports:
      - "9002:9000"
      - "9012:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-admin123456}
      MINIO_PROMETHEUS_AUTH_TYPE: public
    command: server --console-address ":9001" http://minio{1...4}/data{1...2}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - minio-cluster

  # MinIO集群节点3
  minio3:
    image: minio/minio:RELEASE.2024-07-16T23-46-41Z
    hostname: minio3
    volumes:
      - minio3-data:/data1
      - minio3-data2:/data2
    ports:
      - "9003:9000"
      - "9013:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-admin123456}
      MINIO_PROMETHEUS_AUTH_TYPE: public
    command: server --console-address ":9001" http://minio{1...4}/data{1...2}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - minio-cluster

  # MinIO集群节点4
  minio4:
    image: minio/minio:RELEASE.2024-07-16T23-46-41Z
    hostname: minio4
    volumes:
      - minio4-data:/data1
      - minio4-data2:/data2
    ports:
      - "9004:9000"
      - "9014:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-admin123456}
      MINIO_PROMETHEUS_AUTH_TYPE: public
    command: server --console-address ":9001" http://minio{1...4}/data{1...2}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - minio-cluster

  # MinIO负载均衡器
  nginx:
    image: nginx:1.25-alpine
    hostname: nginx
    volumes:
      - ./nginx-minio.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "9000:9000"
      - "9001:9001"
    depends_on:
      - minio1
      - minio2
      - minio3
      - minio4
    networks:
      - minio-cluster

  # MinIO客户端工具
  mc:
    image: minio/mc:RELEASE.2024-07-13T01-46-15Z
    depends_on:
      - nginx
    entrypoint: >
      /bin/sh -c "
      sleep 10;
      /usr/bin/mc alias set myminio http://nginx:9000 admin admin123456;
      /usr/bin/mc mb myminio/market-data --ignore-existing;
      /usr/bin/mc mb myminio/backup --ignore-existing;
      /usr/bin/mc policy set public myminio/market-data;
      /usr/bin/mc admin info myminio;
      exit 0;
      "
    networks:
      - minio-cluster

volumes:
  minio1-data:
    driver: local
  minio1-data2:
    driver: local
  minio2-data:
    driver: local
  minio2-data2:
    driver: local
  minio3-data:
    driver: local
  minio3-data2:
    driver: local
  minio4-data:
    driver: local
  minio4-data2:
    driver: local

networks:
  minio-cluster:
    driver: bridge