/**
 * @file throughput_test.cpp
 * @brief Throughput testing implementation for financial data service
 */

#include "throughput_test.h"
#include "test_utils.h"
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <future>
#include <iostream>
#include <algorithm>

namespace performance_tests {

ThroughputTest::ThroughputTest() : test_utils_(std::make_unique<TestUtils>()) {}

ThroughputTest::~ThroughputTest() = default;

ThroughputResult ThroughputTest::TestDataIngestionThroughput() {
    std::cout << "    Testing data ingestion throughput..." << std::endl;
    
    const uint32_t test_duration_seconds = 30;
    const uint32_t num_threads = 8;
    
    // Setup test environment
    auto data_bus = test_utils_->CreateMockDataBus();
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    std::atomic<uint64_t> total_messages{0};
    std::atomic<uint64_t> total_bytes{0};
    std::atomic<bool> stop_test{false};
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Launch producer threads
    std::vector<std::future<void>> producer_futures;
    for (uint32_t i = 0; i < num_threads; ++i) {
        producer_futures.push_back(std::async(std::launch::async, [&, i]() {
            uint64_t thread_messages = 0;
            uint64_t thread_bytes = 0;
            
            while (!stop_test.load()) {
                // Generate batch of tick data
                auto tick_batch = tick_generator->GenerateBatch(100);
                
                for (const auto& tick : tick_batch) {
                    if (stop_test.load()) break;
                    
                    // Publish to data bus
                    if (data_bus->Publish(tick)) {
                        thread_messages++;
                        thread_bytes += tick.GetSerializedSize();
                    }
                }
                
                // Small yield to prevent CPU spinning
                if (thread_messages % 10000 == 0) {
                    std::this_thread::yield();
                }
            }
            
            total_messages.fetch_add(thread_messages);
            total_bytes.fetch_add(thread_bytes);
        }));
    }
    
    // Run test for specified duration
    std::this_thread::sleep_for(std::chrono::seconds(test_duration_seconds));
    stop_test.store(true);
    
    // Wait for all threads to complete
    for (auto& future : producer_futures) {
        future.wait();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto actual_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time).count() / 1000.0;
    
    // Calculate system resource usage
    auto resource_usage = test_utils_->GetSystemResourceUsage();
    
    ThroughputResult result;
    result.messages_per_second = static_cast<uint64_t>(total_messages.load() / actual_duration);
    result.bytes_per_second = static_cast<uint64_t>(total_bytes.load() / actual_duration);
    result.total_messages = total_messages.load();
    result.test_duration_seconds = actual_duration;
    result.cpu_usage_percent = resource_usage.cpu_usage_percent;
    result.memory_usage_mb = resource_usage.memory_usage_mb;
    
    std::cout << "      Achieved: " << result.messages_per_second << " msg/s" << std::endl;
    std::cout << "      CPU usage: " << result.cpu_usage_percent << "%" << std::endl;
    std::cout << "      Memory usage: " << result.memory_usage_mb << " MB" << std::endl;
    
    return result;
}

ThroughputResult ThroughputTest::TestWebSocketBroadcastThroughput() {
    std::cout << "    Testing WebSocket broadcast throughput..." << std::endl;
    
    const uint32_t test_duration_seconds = 20;
    const uint32_t num_clients = 100;
    
    // Setup WebSocket test environment
    auto ws_server = test_utils_->CreateMockWebSocketServer();
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    // Create multiple WebSocket clients
    std::vector<std::unique_ptr<MockWebSocketClient>> clients;
    for (uint32_t i = 0; i < num_clients; ++i) {
        auto client = test_utils_->CreateWebSocketTestClient();
        client->Connect("ws://localhost:8080/market-data");
        client->Subscribe({"CU2409", "AL2409", "ZN2409", "AU2409", "AG2409"});
        clients.push_back(std::move(client));
    }
    
    std::atomic<uint64_t> total_broadcasts{0};
    std::atomic<uint64_t> total_bytes{0};
    std::atomic<bool> stop_test{false};
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Producer thread
    auto producer_future = std::async(std::launch::async, [&]() {
        uint64_t messages = 0;
        uint64_t bytes = 0;
        
        while (!stop_test.load()) {
            auto tick = tick_generator->GenerateRandomTick();
            
            // Broadcast to all connected clients
            if (ws_server->BroadcastTick(tick)) {
                messages++;
                bytes += tick.GetSerializedSize() * num_clients; // Multiply by client count
            }
            
            // Control broadcast rate
            if (messages % 1000 == 0) {
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        }
        
        total_broadcasts.store(messages);
        total_bytes.store(bytes);
    });
    
    // Run test
    std::this_thread::sleep_for(std::chrono::seconds(test_duration_seconds));
    stop_test.store(true);
    producer_future.wait();
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto actual_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time).count() / 1000.0;
    
    // Verify client reception
    uint64_t total_received = 0;
    for (const auto& client : clients) {
        total_received += client->GetReceivedMessageCount();
    }
    
    auto resource_usage = test_utils_->GetSystemResourceUsage();
    
    ThroughputResult result;
    result.messages_per_second = static_cast<uint64_t>(total_broadcasts.load() / actual_duration);
    result.bytes_per_second = static_cast<uint64_t>(total_bytes.load() / actual_duration);
    result.total_messages = total_broadcasts.load();
    result.test_duration_seconds = actual_duration;
    result.cpu_usage_percent = resource_usage.cpu_usage_percent;
    result.memory_usage_mb = resource_usage.memory_usage_mb;
    
    std::cout << "      Broadcast rate: " << result.messages_per_second << " msg/s" << std::endl;
    std::cout << "      Client reception rate: " << (total_received / actual_duration / num_clients) 
              << " msg/s per client" << std::endl;
    
    return result;
}

ThroughputResult ThroughputTest::TestStorageWriteThroughput() {
    std::cout << "    Testing storage write throughput..." << std::endl;
    
    const uint32_t test_duration_seconds = 25;
    const uint32_t num_writer_threads = 4;
    
    // Setup storage test environment
    auto redis_client = test_utils_->CreateRedisTestClient();
    auto clickhouse_client = test_utils_->CreateClickHouseTestClient();
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    std::atomic<uint64_t> redis_writes{0};
    std::atomic<uint64_t> clickhouse_writes{0};
    std::atomic<uint64_t> total_bytes{0};
    std::atomic<bool> stop_test{false};
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Redis writer threads
    std::vector<std::future<void>> redis_futures;
    for (uint32_t i = 0; i < num_writer_threads / 2; ++i) {
        redis_futures.push_back(std::async(std::launch::async, [&]() {
            uint64_t writes = 0;
            uint64_t bytes = 0;
            
            while (!stop_test.load()) {
                auto tick_batch = tick_generator->GenerateBatch(50);
                
                for (const auto& tick : tick_batch) {
                    if (stop_test.load()) break;
                    
                    if (redis_client->StoreTick(tick)) {
                        writes++;
                        bytes += tick.GetSerializedSize();
                    }
                }
                
                // Batch commit for better performance
                redis_client->FlushPipeline();
            }
            
            redis_writes.fetch_add(writes);
            total_bytes.fetch_add(bytes);
        }));
    }
    
    // ClickHouse writer threads
    std::vector<std::future<void>> clickhouse_futures;
    for (uint32_t i = 0; i < num_writer_threads / 2; ++i) {
        clickhouse_futures.push_back(std::async(std::launch::async, [&]() {
            uint64_t writes = 0;
            uint64_t bytes = 0;
            
            while (!stop_test.load()) {
                auto tick_batch = tick_generator->GenerateBatch(100);
                
                if (!tick_batch.empty() && !stop_test.load()) {
                    if (clickhouse_client->BatchInsertTicks(tick_batch)) {
                        writes += tick_batch.size();
                        for (const auto& tick : tick_batch) {
                            bytes += tick.GetSerializedSize();
                        }
                    }
                }
                
                // ClickHouse works better with larger batches
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            
            clickhouse_writes.fetch_add(writes);
            total_bytes.fetch_add(bytes);
        }));
    }
    
    // Run test
    std::this_thread::sleep_for(std::chrono::seconds(test_duration_seconds));
    stop_test.store(true);
    
    // Wait for all threads
    for (auto& future : redis_futures) {
        future.wait();
    }
    for (auto& future : clickhouse_futures) {
        future.wait();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto actual_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time).count() / 1000.0;
    
    auto resource_usage = test_utils_->GetSystemResourceUsage();
    
    uint64_t total_writes = redis_writes.load() + clickhouse_writes.load();
    
    ThroughputResult result;
    result.messages_per_second = static_cast<uint64_t>(total_writes / actual_duration);
    result.bytes_per_second = static_cast<uint64_t>(total_bytes.load() / actual_duration);
    result.total_messages = total_writes;
    result.test_duration_seconds = actual_duration;
    result.cpu_usage_percent = resource_usage.cpu_usage_percent;
    result.memory_usage_mb = resource_usage.memory_usage_mb;
    
    std::cout << "      Storage write rate: " << result.messages_per_second << " msg/s" << std::endl;
    std::cout << "      Redis writes: " << redis_writes.load() << std::endl;
    std::cout << "      ClickHouse writes: " << clickhouse_writes.load() << std::endl;
    
    return result;
}

ThroughputResult ThroughputTest::TestQueryThroughput() {
    std::cout << "    Testing query throughput..." << std::endl;
    
    const uint32_t test_duration_seconds = 15;
    const uint32_t num_query_threads = 6;
    
    // Setup query test environment
    auto redis_client = test_utils_->CreateRedisTestClient();
    auto clickhouse_client = test_utils_->CreateClickHouseTestClient();
    
    // Pre-populate with test data
    auto tick_generator = test_utils_->CreateTickGenerator();
    auto test_data = tick_generator->GenerateBatch(10000);
    
    for (const auto& tick : test_data) {
        redis_client->StoreTick(tick);
    }
    clickhouse_client->BatchInsertTicks(test_data);
    
    std::atomic<uint64_t> total_queries{0};
    std::atomic<uint64_t> total_results{0};
    std::atomic<bool> stop_test{false};
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Query threads
    std::vector<std::future<void>> query_futures;
    for (uint32_t i = 0; i < num_query_threads; ++i) {
        query_futures.push_back(std::async(std::launch::async, [&, i]() {
            uint64_t queries = 0;
            uint64_t results = 0;
            
            std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409", "AU2409", "AG2409"};
            
            while (!stop_test.load()) {
                const auto& symbol = symbols[queries % symbols.size()];
                
                if (i % 2 == 0) {
                    // Redis queries (hot data)
                    auto latest_tick = redis_client->GetLatestTick(symbol);
                    if (latest_tick.has_value()) {
                        results++;
                    }
                } else {
                    // ClickHouse queries (historical data)
                    auto historical_data = clickhouse_client->QueryHistoricalTicks(
                        symbol,
                        std::chrono::system_clock::now() - std::chrono::hours(1),
                        std::chrono::system_clock::now(),
                        100
                    );
                    results += historical_data.size();
                }
                
                queries++;
                
                // Small delay to prevent overwhelming the database
                if (queries % 100 == 0) {
                    std::this_thread::sleep_for(std::chrono::microseconds(100));
                }
            }
            
            total_queries.fetch_add(queries);
            total_results.fetch_add(results);
        }));
    }
    
    // Run test
    std::this_thread::sleep_for(std::chrono::seconds(test_duration_seconds));
    stop_test.store(true);
    
    // Wait for all threads
    for (auto& future : query_futures) {
        future.wait();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto actual_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time).count() / 1000.0;
    
    auto resource_usage = test_utils_->GetSystemResourceUsage();
    
    ThroughputResult result;
    result.messages_per_second = static_cast<uint64_t>(total_queries.load() / actual_duration);
    result.bytes_per_second = static_cast<uint64_t>(total_results.load() * 200 / actual_duration); // Estimate
    result.total_messages = total_queries.load();
    result.test_duration_seconds = actual_duration;
    result.cpu_usage_percent = resource_usage.cpu_usage_percent;
    result.memory_usage_mb = resource_usage.memory_usage_mb;
    
    std::cout << "      Query rate: " << result.messages_per_second << " queries/s" << std::endl;
    std::cout << "      Results returned: " << total_results.load() << std::endl;
    
    return result;
}

} // namespace performance_tests