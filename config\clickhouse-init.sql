-- ClickHouse Warm Storage Database Initialization
-- This script creates the database schema and tables for financial market data

-- Create main database
CREATE DATABASE IF NOT EXISTS market_data;

-- Create metadata database
CREATE DATABASE IF NOT EXISTS metadata;

-- Use market_data database
USE market_data;

-- =====================================================
-- FUTURES TICK DATA TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS futures_tick_local ON CLUSTER financial_cluster (
    timestamp DateTime64(9) CODEC(Delta, ZSTD),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    last_price Float64 CODEC(Gorilla, ZSTD),
    volume UInt64 CODEC(Delta, ZSTD),
    turnover Float64 CODEC(Gorilla, ZSTD),
    open_interest UInt64 CODEC(Delta, ZSTD),
    bid_prices Array(Float64) CODEC(Gorilla, ZSTD),
    bid_volumes Array(UInt32) CODEC(Delta, ZSTD),
    ask_prices Array(Float64) CODEC(Gorilla, ZSTD),
    ask_volumes Array(UInt32) CODEC(Delta, ZSTD),
    sequence UInt32 CODEC(Delta, ZSTD),
    trade_flag LowCardinality(String),
    settlement_price Float64 CODEC(Gorilla, ZSTD),
    pre_settlement Float64 CODEC(Gorilla, ZSTD),
    pre_close_price Float64 CODEC(Gorilla, ZSTD),
    pre_open_interest UInt64 CODEC(Delta, ZSTD),
    INDEX idx_symbol (symbol) TYPE bloom_filter(0.01),
    INDEX idx_timestamp (timestamp) TYPE minmax GRANULARITY 3,
    INDEX idx_exchange (exchange) TYPE set(10) GRANULARITY 1
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/futures_tick', '{replica}')
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'
SETTINGS 
    index_granularity = 8192,
    merge_with_ttl_timeout = 86400,
    ttl_only_drop_parts = 1;

-- Create distributed table for futures tick data
CREATE TABLE IF NOT EXISTS futures_tick ON CLUSTER financial_cluster AS futures_tick_local
ENGINE = Distributed(financial_cluster, market_data, futures_tick_local, rand());

-- =====================================================
-- STOCK TICK DATA TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS stock_tick_local ON CLUSTER financial_cluster (
    timestamp DateTime64(9) CODEC(Delta, ZSTD),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    last_price Float64 CODEC(Gorilla, ZSTD),
    volume UInt64 CODEC(Delta, ZSTD),
    turnover Float64 CODEC(Gorilla, ZSTD),
    bid_prices Array(Float64) CODEC(Gorilla, ZSTD),
    bid_volumes Array(UInt32) CODEC(Delta, ZSTD),
    ask_prices Array(Float64) CODEC(Gorilla, ZSTD),
    ask_volumes Array(UInt32) CODEC(Delta, ZSTD),
    sequence UInt32 CODEC(Delta, ZSTD),
    trade_flag LowCardinality(String),
    high_price Float64 CODEC(Gorilla, ZSTD),
    low_price Float64 CODEC(Gorilla, ZSTD),
    open_price Float64 CODEC(Gorilla, ZSTD),
    pre_close_price Float64 CODEC(Gorilla, ZSTD),
    total_bid_volume UInt64 CODEC(Delta, ZSTD),
    total_ask_volume UInt64 CODEC(Delta, ZSTD),
    INDEX idx_symbol (symbol) TYPE bloom_filter(0.01),
    INDEX idx_timestamp (timestamp) TYPE minmax GRANULARITY 3,
    INDEX idx_exchange (exchange) TYPE set(10) GRANULARITY 1
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/stock_tick', '{replica}')
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'
SETTINGS 
    index_granularity = 8192,
    merge_with_ttl_timeout = 86400,
    ttl_only_drop_parts = 1;

-- Create distributed table for stock tick data
CREATE TABLE IF NOT EXISTS stock_tick ON CLUSTER financial_cluster AS stock_tick_local
ENGINE = Distributed(financial_cluster, market_data, stock_tick_local, rand());

-- =====================================================
-- OPTIONS TICK DATA TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS options_tick_local ON CLUSTER financial_cluster (
    timestamp DateTime64(9) CODEC(Delta, ZSTD),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    underlying_symbol LowCardinality(String),
    option_type LowCardinality(String), -- 'call' or 'put'
    strike_price Float64 CODEC(Gorilla, ZSTD),
    expiry_date Date,
    last_price Float64 CODEC(Gorilla, ZSTD),
    volume UInt64 CODEC(Delta, ZSTD),
    turnover Float64 CODEC(Gorilla, ZSTD),
    open_interest UInt64 CODEC(Delta, ZSTD),
    bid_prices Array(Float64) CODEC(Gorilla, ZSTD),
    bid_volumes Array(UInt32) CODEC(Delta, ZSTD),
    ask_prices Array(Float64) CODEC(Gorilla, ZSTD),
    ask_volumes Array(UInt32) CODEC(Delta, ZSTD),
    sequence UInt32 CODEC(Delta, ZSTD),
    trade_flag LowCardinality(String),
    implied_volatility Float64 CODEC(Gorilla, ZSTD),
    delta Float64 CODEC(Gorilla, ZSTD),
    gamma Float64 CODEC(Gorilla, ZSTD),
    theta Float64 CODEC(Gorilla, ZSTD),
    vega Float64 CODEC(Gorilla, ZSTD),
    INDEX idx_symbol (symbol) TYPE bloom_filter(0.01),
    INDEX idx_underlying (underlying_symbol) TYPE bloom_filter(0.01),
    INDEX idx_timestamp (timestamp) TYPE minmax GRANULARITY 3,
    INDEX idx_expiry (expiry_date) TYPE minmax GRANULARITY 1
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/options_tick', '{replica}')
PARTITION BY (toYYYYMM(timestamp), underlying_symbol)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'
SETTINGS 
    index_granularity = 8192,
    merge_with_ttl_timeout = 86400,
    ttl_only_drop_parts = 1;

-- Create distributed table for options tick data
CREATE TABLE IF NOT EXISTS options_tick ON CLUSTER financial_cluster AS options_tick_local
ENGINE = Distributed(financial_cluster, market_data, options_tick_local, rand());

-- =====================================================
-- FOREX TICK DATA TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS forex_tick_local ON CLUSTER financial_cluster (
    timestamp DateTime64(9) CODEC(Delta, ZSTD),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    base_currency LowCardinality(String),
    quote_currency LowCardinality(String),
    last_price Float64 CODEC(Gorilla, ZSTD),
    volume UInt64 CODEC(Delta, ZSTD),
    turnover Float64 CODEC(Gorilla, ZSTD),
    bid_prices Array(Float64) CODEC(Gorilla, ZSTD),
    bid_volumes Array(UInt32) CODEC(Delta, ZSTD),
    ask_prices Array(Float64) CODEC(Gorilla, ZSTD),
    ask_volumes Array(UInt32) CODEC(Delta, ZSTD),
    sequence UInt32 CODEC(Delta, ZSTD),
    spread Float64 CODEC(Gorilla, ZSTD),
    INDEX idx_symbol (symbol) TYPE bloom_filter(0.01),
    INDEX idx_timestamp (timestamp) TYPE minmax GRANULARITY 3,
    INDEX idx_base_currency (base_currency) TYPE set(50) GRANULARITY 1
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/forex_tick', '{replica}')
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'
SETTINGS 
    index_granularity = 8192,
    merge_with_ttl_timeout = 86400,
    ttl_only_drop_parts = 1;

-- Create distributed table for forex tick data
CREATE TABLE IF NOT EXISTS forex_tick ON CLUSTER financial_cluster AS forex_tick_local
ENGINE = Distributed(financial_cluster, market_data, forex_tick_local, rand());

-- =====================================================
-- KLINE DATA TABLES (1min, 5min, 15min, 1hour, 1day)
-- =====================================================
CREATE TABLE IF NOT EXISTS kline_1m_local ON CLUSTER financial_cluster (
    timestamp DateTime64(3) CODEC(Delta, ZSTD),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    product_type LowCardinality(String),
    open Float64 CODEC(Gorilla, ZSTD),
    high Float64 CODEC(Gorilla, ZSTD),
    low Float64 CODEC(Gorilla, ZSTD),
    close Float64 CODEC(Gorilla, ZSTD),
    volume UInt64 CODEC(Delta, ZSTD),
    turnover Float64 CODEC(Gorilla, ZSTD),
    open_interest UInt64 CODEC(Delta, ZSTD),
    trade_count UInt32 CODEC(Delta, ZSTD),
    INDEX idx_symbol (symbol) TYPE bloom_filter(0.01),
    INDEX idx_timestamp (timestamp) TYPE minmax GRANULARITY 3
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/kline_1m', '{replica}')
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'
SETTINGS index_granularity = 8192;

-- Create distributed table for 1min kline data
CREATE TABLE IF NOT EXISTS kline_1m ON CLUSTER financial_cluster AS kline_1m_local
ENGINE = Distributed(financial_cluster, market_data, kline_1m_local, rand());

-- Similar tables for other timeframes
CREATE TABLE IF NOT EXISTS kline_5m_local ON CLUSTER financial_cluster AS kline_1m_local
ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/kline_5m', '{replica}')
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'
SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS kline_5m ON CLUSTER financial_cluster AS kline_5m_local
ENGINE = Distributed(financial_cluster, market_data, kline_5m_local, rand());

CREATE TABLE IF NOT EXISTS kline_15m_local ON CLUSTER financial_cluster AS kline_1m_local
ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/kline_15m', '{replica}')
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'
SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS kline_15m ON CLUSTER financial_cluster AS kline_15m_local
ENGINE = Distributed(financial_cluster, market_data, kline_15m_local, rand());

CREATE TABLE IF NOT EXISTS kline_1h_local ON CLUSTER financial_cluster AS kline_1m_local
ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/kline_1h', '{replica}')
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 5 YEAR TO DISK 'cold_storage'
SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS kline_1h ON CLUSTER financial_cluster AS kline_1h_local
ENGINE = Distributed(financial_cluster, market_data, kline_1h_local, rand());

CREATE TABLE IF NOT EXISTS kline_1d_local ON CLUSTER financial_cluster AS kline_1m_local
ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/kline_1d', '{replica}')
PARTITION BY toYYYY(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 10 YEAR TO DISK 'cold_storage'
SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS kline_1d ON CLUSTER financial_cluster AS kline_1d_local
ENGINE = Distributed(financial_cluster, market_data, kline_1d_local, rand());

-- =====================================================
-- METADATA TABLES
-- =====================================================
USE metadata;

-- Instruments metadata table
CREATE TABLE IF NOT EXISTS instruments_local ON CLUSTER financial_cluster (
    symbol String,
    exchange LowCardinality(String),
    product_type Enum8('futures'=1, 'stock'=2, 'option'=3, 'forex'=4, 'bond'=5),
    underlying String,
    expiry_date Date,
    contract_size Float64,
    tick_size Float64,
    min_move Float64,
    currency LowCardinality(String),
    trading_hours String,
    created_at DateTime DEFAULT now(),
    updated_at DateTime DEFAULT now(),
    is_active UInt8 DEFAULT 1
) ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/instruments', '{replica}', updated_at)
ORDER BY (exchange, symbol)
SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS instruments ON CLUSTER financial_cluster AS instruments_local
ENGINE = Distributed(financial_cluster, metadata, instruments_local, rand());

-- Trading calendar table
CREATE TABLE IF NOT EXISTS trading_calendar_local ON CLUSTER financial_cluster (
    exchange LowCardinality(String),
    trading_date Date,
    is_trading_day UInt8,
    session_start DateTime,
    session_end DateTime,
    break_start DateTime,
    break_end DateTime,
    night_start DateTime,
    night_end DateTime
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/trading_calendar', '{replica}')
ORDER BY (exchange, trading_date)
SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS trading_calendar ON CLUSTER financial_cluster AS trading_calendar_local
ENGINE = Distributed(financial_cluster, metadata, trading_calendar_local, rand());

-- Data quality metrics table
CREATE TABLE IF NOT EXISTS data_quality_metrics_local ON CLUSTER financial_cluster (
    timestamp DateTime DEFAULT now(),
    table_name LowCardinality(String),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    metric_type LowCardinality(String), -- 'latency', 'completeness', 'accuracy'
    metric_value Float64,
    threshold_value Float64,
    is_anomaly UInt8,
    details String
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/data_quality_metrics', '{replica}')
PARTITION BY toYYYYMM(timestamp)
ORDER BY (timestamp, table_name, symbol)
TTL timestamp + INTERVAL 6 MONTH
SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS data_quality_metrics ON CLUSTER financial_cluster AS data_quality_metrics_local
ENGINE = Distributed(financial_cluster, metadata, data_quality_metrics_local, rand());

-- =====================================================
-- MATERIALIZED VIEWS FOR REAL-TIME AGGREGATIONS
-- =====================================================
USE market_data;

-- Real-time volume weighted average price (VWAP)
CREATE MATERIALIZED VIEW IF NOT EXISTS futures_vwap_1m_mv ON CLUSTER financial_cluster
TO kline_1m_local AS
SELECT
    toStartOfMinute(timestamp) as timestamp,
    symbol,
    exchange,
    'futures' as product_type,
    argMin(last_price, timestamp) as open,
    max(last_price) as high,
    min(last_price) as low,
    argMax(last_price, timestamp) as close,
    sum(volume) as volume,
    sum(turnover) as turnover,
    argMax(open_interest, timestamp) as open_interest,
    count() as trade_count
FROM futures_tick_local
WHERE timestamp >= now() - INTERVAL 1 HOUR
GROUP BY symbol, exchange, toStartOfMinute(timestamp);

-- Create indexes for better query performance
-- These will be created after tables are populated with data