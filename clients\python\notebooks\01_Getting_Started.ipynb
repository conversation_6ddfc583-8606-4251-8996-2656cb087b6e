{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Financial Data SDK - Getting Started\n", "\n", "This notebook demonstrates the basic usage of the Financial Data SDK for accessing real-time and historical market data.\n", "\n", "## Features Covered\n", "- Client initialization\n", "- Historical data retrieval\n", "- Data format conversion (pandas/numpy)\n", "- Basic technical indicators\n", "- Real-time data streaming"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import asyncio\n", "\n", "# Import Financial Data SDK\n", "from financial_data_sdk import FinancialDataClient, AsyncFinancialDataClient\n", "from financial_data_sdk.indicators import TechnicalIndicators, calculate_all_indicators\n", "from financial_data_sdk.utils import DataConverter\n", "\n", "# Set up plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✓ Libraries imported successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Client Initialization\n", "\n", "Initialize the Financial Data Client with server configuration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure servers (replace with actual server addresses)\n", "servers = [\n", "    \"localhost:50051\",\n", "    \"localhost:50052\",\n", "    \"localhost:50053\"\n", "]\n", "\n", "# Initialize client with caching enabled\n", "client = FinancialDataClient(\n", "    servers=servers,\n", "    max_retries=3,\n", "    cache_size=10000,\n", "    enable_cache=True\n", ")\n", "\n", "# Check service health\n", "if client.health_check():\n", "    print(\"✓ Service is healthy and ready\")\n", "    print(f\"✓ Connected to {len(servers)} servers\")\n", "else:\n", "    print(\"✗ Service health check failed\")\n", "    print(\"Note: This is expected if servers are not running\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Historical Data Retrieval\n", "\n", "Retrieve historical tick and K-line data for analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define time range for historical data\n", "end_time = datetime.now()\n", "start_time = end_time - <PERSON><PERSON><PERSON>(days=7)\n", "\n", "symbol = \"AAPL\"\n", "exchange = \"NASDAQ\"\n", "\n", "print(f\"Retrieving data for {symbol} from {start_time.date()} to {end_time.date()}\")\n", "\n", "# Get historical tick data\n", "try:\n", "    tick_df = client.get_tick_data(\n", "        symbol=symbol,\n", "        exchange=exchange,\n", "        start_time=start_time,\n", "        end_time=end_time,\n", "        limit=1000,\n", "        as_dataframe=True\n", "    )\n", "    \n", "    print(f\"✓ Retrieved {len(tick_df)} tick records\")\n", "    if not tick_df.empty:\n", "        print(\"\\nTick Data Sample:\")\n", "        display(tick_df.head())\n", "    \n", "except Exception as e:\n", "    print(f\"Note: Tick data retrieval failed (expected if server not running): {e}\")\n", "    # Create sample data for demonstration\n", "    dates = pd.date_range(start_time, end_time, freq='1min')[:1000]\n", "    np.random.seed(42)\n", "    prices = 150 + np.random.randn(len(dates)).cumsum() * 0.5\n", "    \n", "    tick_df = pd.DataFrame({\n", "        'timestamp': [int(d.timestamp() * 1e9) for d in dates],\n", "        'symbol': symbol,\n", "        'exchange': exchange,\n", "        'last_price': prices,\n", "        'volume': np.random.randint(100, 1000, len(dates)),\n", "        'turnover': prices * np.random.randint(100, 1000, len(dates))\n", "    }, index=dates)\n", "    \n", "    print(f\"✓ Created sample tick data with {len(tick_df)} records\")\n", "    print(\"\\nSample Tick Data:\")\n", "    display(tick_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get historical K-line data\n", "try:\n", "    kline_df = client.get_kline_data(\n", "        symbol=symbol,\n", "        period=\"1h\",\n", "        exchange=exchange,\n", "        start_time=start_time,\n", "        end_time=end_time,\n", "        limit=500,\n", "        as_dataframe=True\n", "    )\n", "    \n", "    print(f\"✓ Retrieved {len(kline_df)} K-line records\")\n", "    if not kline_df.empty:\n", "        print(\"\\nK-line Data Sample:\")\n", "        display(kline_df.head())\n", "    \n", "except Exception as e:\n", "    print(f\"Note: K-line data retrieval failed (expected if server not running): {e}\")\n", "    # Create sample K-line data from tick data\n", "    kline_df = tick_df.resample('1H').agg({\n", "        'last_price': ['first', 'max', 'min', 'last'],\n", "        'volume': 'sum',\n", "        'turnover': 'sum'\n", "    }).dropna()\n", "    \n", "    # Flatten column names\n", "    kline_df.columns = ['open', 'high', 'low', 'close', 'volume', 'turnover']\n", "    kline_df['symbol'] = symbol\n", "    kline_df['exchange'] = exchange\n", "    kline_df['period'] = '1H'\n", "    \n", "    print(f\"✓ Created sample K-line data with {len(kline_df)} records\")\n", "    print(\"\\nSample K-line Data:\")\n", "    display(kline_df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Visualization\n", "\n", "Visualize the retrieved market data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot tick data\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))\n", "\n", "# Price chart\n", "ax1.plot(tick_df.index, tick_df['last_price'], linewidth=0.8, alpha=0.8)\n", "ax1.set_title(f'{symbol} Tick Prices')\n", "ax1.set_ylabel('Price ($)')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Volume chart\n", "ax2.bar(tick_df.index, tick_df['volume'], alpha=0.6, width=0.0001)\n", "ax2.set_title(f'{symbol} Volume')\n", "ax2.set_ylabel('Volume')\n", "ax2.set_xlabel('Time')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot K-line data as candlestick chart\n", "from matplotlib.patches import Rectangle\n", "\n", "fig, ax = plt.subplots(figsize=(12, 6))\n", "\n", "# Simple candlestick representation\n", "for i, (idx, row) in enumerate(kline_df.iterrows()):\n", "    color = 'green' if row['close'] >= row['open'] else 'red'\n", "    \n", "    # High-low line\n", "    ax.plot([i, i], [row['low'], row['high']], color='black', linewidth=1)\n", "    \n", "    # Open-close rectangle\n", "    height = abs(row['close'] - row['open'])\n", "    bottom = min(row['open'], row['close'])\n", "    rect = Rectangle((i-0.3, bottom), 0.6, height, \n", "                    facecolor=color, alpha=0.7, edgecolor='black')\n", "    ax.add_patch(rect)\n", "\n", "ax.set_title(f'{symbol} Candlestick Chart (1H)')\n", "ax.set_ylabel('Price ($)')\n", "ax.set_xlabel('Time Period')\n", "ax.grid(True, alpha=0.3)\n", "\n", "# Set x-axis labels\n", "step = max(1, len(kline_df) // 10)\n", "ax.set_xticks(range(0, len(kline_df), step))\n", "ax.set_xticklabels([kline_df.index[i].strftime('%m-%d %H:%M') \n", "                   for i in range(0, len(kline_df), step)], rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Technical Indicators\n", "\n", "Calculate and visualize technical indicators."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate individual indicators\n", "close_prices = kline_df['close']\n", "\n", "# Moving averages\n", "sma_20 = TechnicalIndicators.sma(close_prices, 20)\n", "ema_12 = TechnicalIndicators.ema(close_prices, 12)\n", "\n", "# RSI\n", "rsi = TechnicalIndicators.rsi(close_prices)\n", "\n", "# MACD\n", "macd_line, signal_line, histogram = TechnicalIndicators.macd(close_prices)\n", "\n", "# Bollinger Bands\n", "bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(close_prices)\n", "\n", "print(\"✓ Technical indicators calculated\")\n", "print(f\"Latest values:\")\n", "print(f\"  Price: ${close_prices.iloc[-1]:.2f}\")\n", "print(f\"  SMA(20): ${sma_20.iloc[-1]:.2f}\")\n", "print(f\"  RSI: {rsi.iloc[-1]:.1f}\")\n", "print(f\"  MACD: {macd_line.iloc[-1]:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot price with indicators\n", "fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10), \n", "                                   gridspec_kw={'height_ratios': [3, 1, 1]})\n", "\n", "# Price and moving averages\n", "ax1.plot(kline_df.index, close_prices, label='Close Price', linewidth=1.5)\n", "ax1.plot(kline_df.index, sma_20, label='SMA(20)', alpha=0.8)\n", "ax1.plot(kline_df.index, ema_12, label='EMA(12)', alpha=0.8)\n", "\n", "# Bollinger Bands\n", "ax1.fill_between(kline_df.index, bb_upper, bb_lower, alpha=0.2, label='Bollinger Bands')\n", "ax1.plot(kline_df.index, bb_upper, '--', alpha=0.5, color='gray')\n", "ax1.plot(kline_df.index, bb_lower, '--', alpha=0.5, color='gray')\n", "\n", "ax1.set_title(f'{symbol} Price with Technical Indicators')\n", "ax1.set_ylabel('Price ($)')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# RSI\n", "ax2.plot(kline_df.index, rsi, label='RSI', color='orange', linewidth=1.5)\n", "ax2.axhline(y=70, color='r', linestyle='--', alpha=0.7, label='Overbought')\n", "ax2.axhline(y=30, color='g', linestyle='--', alpha=0.7, label='Oversold')\n", "ax2.set_ylabel('RSI')\n", "ax2.set_ylim(0, 100)\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# MACD\n", "ax3.plot(kline_df.index, macd_line, label='MACD', color='blue', linewidth=1.5)\n", "ax3.plot(kline_df.index, signal_line, label='Signal', color='red', linewidth=1.5)\n", "ax3.bar(kline_df.index, histogram, alpha=0.3, label='Histogram', width=0.02)\n", "ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "ax3.set_ylabel('MACD')\n", "ax3.set_xlabel('Time')\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON> All Indicators at Once\n", "\n", "Use the convenience function to calculate all common indicators."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate all indicators\n", "df_with_indicators = calculate_all_indicators(kline_df)\n", "\n", "print(f\"Original columns: {list(kline_df.columns)}\")\n", "print(f\"\\nWith indicators: {list(df_with_indicators.columns)}\")\n", "print(f\"\\nAdded {len(df_with_indicators.columns) - len(kline_df.columns)} indicators\")\n", "\n", "# Display latest values\n", "print(\"\\nLatest indicator values:\")\n", "latest_row = df_with_indicators.iloc[-1]\n", "indicator_cols = [col for col in df_with_indicators.columns \n", "                 if col not in ['open', 'high', 'low', 'close', 'volume', 'turnover', 'symbol', 'exchange', 'period']]\n", "\n", "for col in indicator_cols[:10]:  # Show first 10 indicators\n", "    if pd.notna(latest_row[col]):\n", "        print(f\"  {col}: {latest_row[col]:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Data Format Conversions\n", "\n", "Demonstrate various data format conversions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert to numpy arrays\n", "from financial_data_sdk.data_models import NumpyArrayBuilder\n", "\n", "# Create sample tick data objects for demonstration\n", "from financial_data_sdk.data_models import TickData\n", "\n", "sample_ticks = []\n", "for i, (idx, row) in enumerate(tick_df.head(10).iterrows()):\n", "    tick = TickData(\n", "        timestamp=int(idx.timestamp() * 1e9),\n", "        symbol=row['symbol'],\n", "        exchange=row['exchange'],\n", "        last_price=row['last_price'],\n", "        volume=int(row['volume']),\n", "        turnover=row['turnover']\n", "    )\n", "    sample_ticks.append(tick)\n", "\n", "# Convert to numpy array\n", "tick_array = NumpyArrayBuilder.from_tick_list(sample_ticks)\n", "print(f\"Tick data as numpy array shape: {tick_array.shape}\")\n", "print(f\"Array:\\n{tick_array[:5]}\")\n", "\n", "# Convert K-line data to numpy\n", "from financial_data_sdk.data_models import KlineData\n", "\n", "sample_klines = []\n", "for i, (idx, row) in enumerate(kline_df.head(5).iterrows()):\n", "    kline = KlineData(\n", "        timestamp=int(idx.timestamp() * 1e9),\n", "        symbol=row['symbol'],\n", "        exchange=row['exchange'],\n", "        period=row['period'],\n", "        open=row['open'],\n", "        high=row['high'],\n", "        low=row['low'],\n", "        close=row['close'],\n", "        volume=int(row['volume']),\n", "        turnover=row['turnover']\n", "    )\n", "    sample_klines.append(kline)\n", "\n", "kline_array = NumpyArrayBuilder.from_kline_list(sample_klines)\n", "print(f\"\\nK-line data as numpy array shape: {kline_array.shape}\")\n", "print(f\"Array:\\n{kline_array}\")\n", "\n", "# OHLCV array optimized for technical analysis\n", "ohlcv_array = NumpyArrayBuilder.ohlcv_array(sample_klines)\n", "print(f\"\\nOHLCV array shape: {ohlcv_array.shape}\")\n", "print(f\"OHLCV Array:\\n{ohlcv_array}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON>\n", "\n", "Demonstrate batch data retrieval for multiple symbols."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define batch requests\n", "batch_requests = [\n", "    {\n", "        'id': 'aapl_tick',\n", "        'type': 'tick',\n", "        'symbol': 'AAPL',\n", "        'exchange': 'NASDAQ',\n", "        'limit': 100\n", "    },\n", "    {\n", "        'id': 'googl_tick',\n", "        'type': 'tick',\n", "        'symbol': 'GOOGL',\n", "        'exchange': 'NASDAQ',\n", "        'limit': 100\n", "    },\n", "    {\n", "        'id': 'msft_kline',\n", "        'type': 'kline',\n", "        'symbol': 'MSFT',\n", "        'period': '1h',\n", "        'exchange': 'NASDAQ',\n", "        'limit': 50\n", "    }\n", "]\n", "\n", "print(\"Executing batch requests...\")\n", "\n", "try:\n", "    # Execute batch requests in parallel\n", "    batch_results = client.get_batch_data(\n", "        requests=batch_requests,\n", "        as_dataframe=True,\n", "        parallel=True\n", "    )\n", "    \n", "    print(\"\\nBatch Results:\")\n", "    for req_id, result in batch_results.items():\n", "        if result is not None:\n", "            print(f\"✓ {req_id}: {len(result)} records\")\n", "        else:\n", "            print(f\"✗ {req_id}: Failed\")\n", "            \n", "except Exception as e:\n", "    print(f\"Note: Batch operation failed (expected if server not running): {e}\")\n", "    print(\"In a real scenario, this would retrieve data for multiple symbols simultaneously\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Performance and Cache Statistics\n", "\n", "Monitor SDK performance and cache effectiveness."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get cache statistics\n", "if client.cache:\n", "    cache_stats = client.cache.stats()\n", "    print(\"Cache Statistics:\")\n", "    print(f\"  Size: {cache_stats['size']}/{cache_stats['max_size']}\")\n", "    print(f\"  Hit Rate: {cache_stats['hit_rate']:.2%}\")\n", "    print(f\"  Hits: {cache_stats['hit_count']}\")\n", "    print(f\"  Misses: {cache_stats['miss_count']}\")\n", "    print(f\"  TTL: {cache_stats['ttl_seconds']} seconds\")\nelse:\n", "    print(\"<PERSON><PERSON> is disabled\")\n", "\n", "# Get server statistics\n", "server_stats = client.get_server_stats()\n", "print(\"\\nServer Statistics:\")\n", "for server, stats in server_stats.items():\n", "    print(f\"  {server}:\")\n", "    print(f\"    Latency: {stats['latency']:.2f}ms\")\n", "    print(f\"    Errors: {stats['errors']}\")\n", "    print(f\"    Healthy: {stats['healthy']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Data Validation\n", "\n", "Demonstrate data validation capabilities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from financial_data_sdk.utils import DataValidator\n", "\n", "# Test data validation\n", "print(\"Data Validation Examples:\")\n", "\n", "# Valid tick data\n", "valid_tick = {\n", "    'timestamp': int(datetime.now().timestamp() * 1e9),\n", "    'symbol': 'AAPL',\n", "    'last_price': 150.25,\n", "    'volume': 1000,\n", "    'bid_prices': [150.24, 150.23],\n", "    'ask_prices': [150.26, 150.27]\n", "}\n", "\n", "errors = DataValidator.validate_tick_data(valid_tick)\n", "print(f\"\\nValid tick data errors: {errors}\")\n", "\n", "# Invalid tick data\n", "invalid_tick = {\n", "    'timestamp': 'invalid_timestamp',\n", "    'symbol': 'AAPL',\n", "    'last_price': -10,  # Invalid negative price\n", "    'volume': -100,     # Invalid negative volume\n", "    'bid_prices': [150.30],  # Bid higher than ask\n", "    'ask_prices': [150.25]\n", "}\n", "\n", "errors = DataValidator.validate_tick_data(invalid_tick)\n", "print(f\"\\nInvalid tick data errors: {errors}\")\n", "\n", "# Outlier detection\n", "sample_prices = close_prices.copy()\n", "# Add some outliers\n", "sample_prices.iloc[10] = sample_prices.iloc[10] * 2  # Price spike\n", "sample_prices.iloc[20] = sample_prices.iloc[20] * 0.5  # Price drop\n", "\n", "outliers = DataValidator.detect_outliers(sample_prices, method='iqr', threshold=1.5)\n", "print(f\"\\nDetected {outliers.sum()} outliers in price data\")\n", "if outliers.sum() > 0:\n", "    print(f\"Outlier indices: {outliers[outliers].index.tolist()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Cleanup\n", "\n", "Clean up resources and close connections."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Clear cache\n", "client.clear_cache()\n", "print(\"✓ Cache cleared\")\n", "\n", "# Close client connections\n", "client.close()\n", "print(\"✓ Client connections closed\")\n", "\n", "print(\"\\n🎉 Tutorial completed successfully!\")\n", "print(\"\\nNext steps:\")\n", "print(\"- Try the Advanced Usage notebook for more complex scenarios\")\n", "print(\"- Explore async operations with AsyncFinancialDataClient\")\n", "print(\"- Build custom trading strategies using the indicators module\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}