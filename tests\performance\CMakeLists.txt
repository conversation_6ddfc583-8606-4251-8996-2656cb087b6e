# Performance Testing Suite CMakeLists.txt

cmake_minimum_required(VERSION 3.16)

# Find required packages
find_package(Threads REQUIRED)

# Try to find <PERSON><PERSON> (optional for this mock implementation)
find_package(Boost QUIET COMPONENTS system thread chrono)

# Performance test executable
add_executable(performance_tests
    latency_test.cpp
    throughput_test.cpp
    concurrent_test.cpp
    data_integrity_test.cpp
    failover_test.cpp
    benchmark_runner.cpp
    test_utils.cpp
)

target_link_libraries(performance_tests
    PRIVATE
    Threads::Threads
)

# Link Boost if available
if(Boost_FOUND)
    target_link_libraries(performance_tests PRIVATE
        Boost::system
        Boost::thread
        Boost::chrono
    )
    target_compile_definitions(performance_tests PRIVATE BOOST_AVAILABLE)
endif()

target_include_directories(performance_tests
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src
)

# Compiler flags for performance testing
if(MSVC)
    target_compile_options(performance_tests PRIVATE /O2 /DNDEBUG)
else()
    target_compile_options(performance_tests PRIVATE -O3 -DNDEBUG)
    # Only use -march=native on non-cross-compilation builds
    if(NOT CMAKE_CROSSCOMPILING)
        target_compile_options(performance_tests PRIVATE -march=native)
    endif()
endif()

# Set C++ standard
target_compile_features(performance_tests PRIVATE cxx_std_17)

# Copy Python test files to build directory
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/run_python_tests.py
    ${CMAKE_CURRENT_BINARY_DIR}/run_python_tests.py
    COPYONLY
)

configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/run_all_tests.bat
    ${CMAKE_CURRENT_BINARY_DIR}/run_all_tests.bat
    COPYONLY
)

configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/run_all_tests.sh
    ${CMAKE_CURRENT_BINARY_DIR}/run_all_tests.sh
    COPYONLY
)

configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/README.md
    ${CMAKE_CURRENT_BINARY_DIR}/README.md
    COPYONLY
)