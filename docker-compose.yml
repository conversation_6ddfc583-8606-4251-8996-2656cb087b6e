version: '3.8'

services:
  # Redis - 单实例开发配置
  redis:
    image: redis:7-alpine
    container_name: financial-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - financial-network
    restart: unless-stopped

  # ClickHouse - 单实例开发配置
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: financial-clickhouse
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - ./config/clickhouse-init.sql:/docker-entrypoint-initdb.d/init.sql
      - clickhouse_data:/var/lib/clickhouse
    environment:
      CLICKHOUSE_DB: market_data
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: password123
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    networks:
      - financial-network
    restart: unless-stopped
    # 开发环境资源限制
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Kafka - 简化开发配置
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: financial-kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 1
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
    depends_on:
      - zookeeper
    networks:
      - financial-network
    restart: unless-stopped
    # 开发环境资源限制
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: financial-zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_INIT_LIMIT: 5
      ZOOKEEPER_SYNC_LIMIT: 2
    networks:
      - financial-network
    restart: unless-stopped
    # 开发环境资源限制
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # MinIO - 对象存储服务
  minio:
    image: minio/minio:latest
    container_name: financial-minio
    ports:
      - "9001:9001"
      - "9002:9002"
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password123
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
    command: server /data --console-address ":9001" --address ":9002"
    networks:
      - financial-network
    restart: unless-stopped
    # 开发环境资源限制
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # 监控服务 - 可选启动
  prometheus:
    image: prom/prometheus:latest
    container_name: financial-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
    networks:
      - financial-network
    restart: unless-stopped
    profiles:
      - monitoring
    # 开发环境资源限制
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  grafana:
    image: grafana/grafana:latest
    container_name: financial-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
      GF_SECURITY_ALLOW_EMBEDDING: 'true'
    networks:
      - financial-network
    restart: unless-stopped
    profiles:
      - monitoring
    # 开发环境资源限制
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  redis_data:
    driver: local
  clickhouse_data:
    driver: local
  minio_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  financial-network:
    driver: bridge