"""
任务失败处理机制测试模块

测试覆盖：
- 错误分类和严重程度判断
- 重试策略和延迟计算
- 任务状态持久化
- 错误统计和分析
- 失败恢复机制
"""

import asyncio
import pytest
import os
import json
import tempfile
import shutil
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import sys

# 添加src路径以便导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'collectors'))

from task_failure_handler import (
    TaskFailureHandler,
    ErrorClassifier,
    RetryCalculator,
    TaskStatePersistence,
    ErrorInfo,
    ErrorType,
    ErrorSeverity,
    RetryStrategy,
    RetryConfig,
    TaskState
)


class TestErrorInfo:
    """测试ErrorInfo类"""
    
    def test_error_info_creation(self):
        """测试错误信息创建"""
        error_info = ErrorInfo(
            error_type=ErrorType.NETWORK_ERROR,
            severity=ErrorSeverity.MEDIUM,
            message="Connection timeout",
            timestamp=datetime.now(),
            task_id="test_task",
            execution_id="exec_123",
            retry_count=1,
            context={"url": "http://example.com"}
        )
        
        assert error_info.error_type == ErrorType.NETWORK_ERROR
        assert error_info.severity == ErrorSeverity.MEDIUM
        assert error_info.message == "Connection timeout"
        assert error_info.task_id == "test_task"
        assert error_info.execution_id == "exec_123"
        assert error_info.retry_count == 1
        assert error_info.context["url"] == "http://example.com"
    
    def test_error_info_serialization(self):
        """测试错误信息序列化"""
        error_info = ErrorInfo(
            error_type=ErrorType.STORAGE_ERROR,
            severity=ErrorSeverity.HIGH,
            message="Database connection failed",
            timestamp=datetime(2024, 1, 1, 12, 0, 0),
            task_id="storage_task",
            execution_id="exec_456"
        )
        
        # 转换为字典
        error_dict = error_info.to_dict()
        assert error_dict['error_type'] == ErrorType.STORAGE_ERROR.value
        assert error_dict['severity'] == ErrorSeverity.HIGH.value
        assert error_dict['message'] == "Database connection failed"
        assert error_dict['timestamp'] == "2024-01-01T12:00:00"
        
        # 从字典恢复
        restored_error = ErrorInfo.from_dict(error_dict)
        assert restored_error.error_type == error_info.error_type
        assert restored_error.severity == error_info.severity
        assert restored_error.message == error_info.message
        assert restored_error.timestamp == error_info.timestamp


class TestRetryConfig:
    """测试RetryConfig类"""
    
    def test_default_retry_config(self):
        """测试默认重试配置"""
        config = RetryConfig()
        
        assert config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF
        assert config.max_retries == 3
        assert config.base_delay_seconds == 1.0
        assert config.max_delay_seconds == 300.0
        assert config.backoff_multiplier == 2.0
        assert config.jitter is True
        assert ErrorType.NETWORK_ERROR in config.retry_on_errors
        assert ErrorType.AUTHENTICATION_ERROR in config.no_retry_on_errors
    
    def test_custom_retry_config(self):
        """测试自定义重试配置"""
        config = RetryConfig(
            strategy=RetryStrategy.LINEAR_BACKOFF,
            max_retries=5,
            base_delay_seconds=2.0,
            max_delay_seconds=600.0,
            backoff_multiplier=1.5,
            jitter=False
        )
        
        assert config.strategy == RetryStrategy.LINEAR_BACKOFF
        assert config.max_retries == 5
        assert config.base_delay_seconds == 2.0
        assert config.max_delay_seconds == 600.0
        assert config.backoff_multiplier == 1.5
        assert config.jitter is False


class TestTaskState:
    """测试TaskState类"""
    
    def test_task_state_creation(self):
        """测试任务状态创建"""
        start_time = datetime.now()
        task_state = TaskState(
            task_id="test_task",
            execution_id="exec_123",
            status="running",
            start_time=start_time,
            last_update_time=start_time,
            retry_count=0,
            context={"symbol": "AAPL"}
        )
        
        assert task_state.task_id == "test_task"
        assert task_state.execution_id == "exec_123"
        assert task_state.status == "running"
        assert task_state.start_time == start_time
        assert task_state.retry_count == 0
        assert task_state.context["symbol"] == "AAPL"
    
    def test_task_state_serialization(self):
        """测试任务状态序列化"""
        start_time = datetime(2024, 1, 1, 10, 0, 0)
        update_time = datetime(2024, 1, 1, 10, 5, 0)
        
        error_info = ErrorInfo(
            error_type=ErrorType.TIMEOUT_ERROR,
            severity=ErrorSeverity.MEDIUM,
            message="Request timeout",
            timestamp=update_time,
            task_id="test_task",
            execution_id="exec_123"
        )
        
        task_state = TaskState(
            task_id="test_task",
            execution_id="exec_123",
            status="retrying",
            start_time=start_time,
            last_update_time=update_time,
            retry_count=1,
            error_history=[error_info],
            context={"symbol": "GOOGL"}
        )
        
        # 转换为字典
        state_dict = task_state.to_dict()
        assert state_dict['task_id'] == "test_task"
        assert state_dict['status'] == "retrying"
        assert state_dict['retry_count'] == 1
        assert len(state_dict['error_history']) == 1
        
        # 从字典恢复
        restored_state = TaskState.from_dict(state_dict)
        assert restored_state.task_id == task_state.task_id
        assert restored_state.status == task_state.status
        assert restored_state.retry_count == task_state.retry_count
        assert len(restored_state.error_history) == 1
        assert restored_state.error_history[0].error_type == ErrorType.TIMEOUT_ERROR


class TestErrorClassifier:
    """测试ErrorClassifier类"""
    
    def setup_method(self):
        """测试前设置"""
        self.classifier = ErrorClassifier()
    
    def test_classify_network_error(self):
        """测试网络错误分类"""
        exception = ConnectionError("Connection refused")
        context = {"task_id": "test_task", "execution_id": "exec_123"}
        
        error_info = self.classifier.classify_error(exception, context)
        
        assert error_info.error_type == ErrorType.NETWORK_ERROR
        assert error_info.severity == ErrorSeverity.MEDIUM
        assert error_info.message == "Connection refused"
        assert error_info.task_id == "test_task"
        assert error_info.execution_id == "exec_123"
    
    def test_classify_timeout_error(self):
        """测试超时错误分类"""
        exception = TimeoutError("Request timeout")
        context = {"task_id": "timeout_task", "execution_id": "exec_456"}
        
        error_info = self.classifier.classify_error(exception, context)
        
        assert error_info.error_type == ErrorType.TIMEOUT_ERROR
        assert error_info.severity == ErrorSeverity.MEDIUM
        assert error_info.message == "Request timeout"
    
    def test_classify_validation_error(self):
        """测试验证错误分类"""
        exception = ValueError("Invalid data format")
        context = {"task_id": "validation_task", "execution_id": "exec_789"}
        
        error_info = self.classifier.classify_error(exception, context)
        
        assert error_info.error_type == ErrorType.VALIDATION_ERROR
        assert error_info.severity == ErrorSeverity.LOW
        assert error_info.message == "Invalid data format"
    
    def test_classify_authentication_error(self):
        """测试认证错误分类"""
        exception = PermissionError("Access denied")
        context = {"task_id": "auth_task", "execution_id": "exec_101"}
        
        error_info = self.classifier.classify_error(exception, context)
        
        assert error_info.error_type == ErrorType.AUTHENTICATION_ERROR
        assert error_info.severity == ErrorSeverity.HIGH
        assert error_info.message == "Access denied"
    
    def test_classify_by_message_pattern(self):
        """测试基于消息模式的分类"""
        # 测试数据源错误
        exception = Exception("API service unavailable")
        error_info = self.classifier.classify_error(exception)
        assert error_info.error_type == ErrorType.DATA_SOURCE_ERROR
        
        # 测试存储错误
        exception = Exception("Database write error")
        error_info = self.classifier.classify_error(exception)
        assert error_info.error_type == ErrorType.STORAGE_ERROR
        
        # 测试限流错误
        exception = Exception("Rate limit exceeded")
        error_info = self.classifier.classify_error(exception)
        assert error_info.error_type == ErrorType.RATE_LIMIT_ERROR
    
    def test_classify_unknown_error(self):
        """测试未知错误分类"""
        exception = Exception("Some unknown error")
        error_info = self.classifier.classify_error(exception)
        
        assert error_info.error_type == ErrorType.UNKNOWN_ERROR
        assert error_info.severity == ErrorSeverity.MEDIUM


class TestRetryCalculator:
    """测试RetryCalculator类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = RetryConfig(
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            max_retries=3,
            base_delay_seconds=1.0,
            backoff_multiplier=2.0,
            jitter=False  # 关闭抖动以便测试
        )
        self.calculator = RetryCalculator(self.config)
    
    def test_should_retry_within_limit(self):
        """测试在重试限制内应该重试"""
        error_info = ErrorInfo(
            error_type=ErrorType.NETWORK_ERROR,
            severity=ErrorSeverity.MEDIUM,
            message="Connection failed",
            timestamp=datetime.now(),
            task_id="test_task",
            execution_id="exec_123",
            retry_count=1  # 小于max_retries
        )
        
        should_retry = self.calculator.should_retry(error_info)
        assert should_retry is True
    
    def test_should_not_retry_exceed_limit(self):
        """测试超过重试限制不应该重试"""
        error_info = ErrorInfo(
            error_type=ErrorType.NETWORK_ERROR,
            severity=ErrorSeverity.MEDIUM,
            message="Connection failed",
            timestamp=datetime.now(),
            task_id="test_task",
            execution_id="exec_123",
            retry_count=3  # 等于max_retries
        )
        
        should_retry = self.calculator.should_retry(error_info)
        assert should_retry is False
    
    def test_should_not_retry_critical_error(self):
        """测试严重错误不应该重试"""
        error_info = ErrorInfo(
            error_type=ErrorType.CONFIGURATION_ERROR,
            severity=ErrorSeverity.CRITICAL,
            message="Configuration error",
            timestamp=datetime.now(),
            task_id="test_task",
            execution_id="exec_123",
            retry_count=0
        )
        
        should_retry = self.calculator.should_retry(error_info)
        assert should_retry is False
    
    def test_should_not_retry_excluded_error(self):
        """测试排除的错误类型不应该重试"""
        error_info = ErrorInfo(
            error_type=ErrorType.AUTHENTICATION_ERROR,
            severity=ErrorSeverity.HIGH,
            message="Authentication failed",
            timestamp=datetime.now(),
            task_id="test_task",
            execution_id="exec_123",
            retry_count=0
        )
        
        should_retry = self.calculator.should_retry(error_info)
        assert should_retry is False
    
    def test_exponential_backoff_delay(self):
        """测试指数退避延迟计算"""
        # 第1次重试
        delay1 = self.calculator.calculate_delay(0)
        assert delay1 == 1.0  # base_delay_seconds * (2^0)
        
        # 第2次重试
        delay2 = self.calculator.calculate_delay(1)
        assert delay2 == 2.0  # base_delay_seconds * (2^1)
        
        # 第3次重试
        delay3 = self.calculator.calculate_delay(2)
        assert delay3 == 4.0  # base_delay_seconds * (2^2)
    
    def test_linear_backoff_delay(self):
        """测试线性退避延迟计算"""
        config = RetryConfig(
            strategy=RetryStrategy.LINEAR_BACKOFF,
            base_delay_seconds=2.0,
            jitter=False
        )
        calculator = RetryCalculator(config)
        
        # 第1次重试
        delay1 = calculator.calculate_delay(0)
        assert delay1 == 2.0  # base_delay_seconds * (0+1)
        
        # 第2次重试
        delay2 = calculator.calculate_delay(1)
        assert delay2 == 4.0  # base_delay_seconds * (1+1)
        
        # 第3次重试
        delay3 = calculator.calculate_delay(2)
        assert delay3 == 6.0  # base_delay_seconds * (2+1)
    
    def test_fixed_interval_delay(self):
        """测试固定间隔延迟计算"""
        config = RetryConfig(
            strategy=RetryStrategy.FIXED_INTERVAL,
            base_delay_seconds=5.0,
            jitter=False
        )
        calculator = RetryCalculator(config)
        
        # 所有重试都应该是相同的延迟
        for retry_count in range(5):
            delay = calculator.calculate_delay(retry_count)
            assert delay == 5.0
    
    def test_immediate_retry_delay(self):
        """测试立即重试延迟计算"""
        config = RetryConfig(strategy=RetryStrategy.IMMEDIATE)
        calculator = RetryCalculator(config)
        
        delay = calculator.calculate_delay(0)
        assert delay == 0
    
    def test_max_delay_limit(self):
        """测试最大延迟限制"""
        config = RetryConfig(
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay_seconds=100.0,
            max_delay_seconds=200.0,
            backoff_multiplier=3.0,
            jitter=False
        )
        calculator = RetryCalculator(config)
        
        # 第3次重试应该超过最大延迟限制
        delay = calculator.calculate_delay(2)  # 100 * (3^2) = 900, 但限制为200
        assert delay == 200.0


class TestTaskStatePersistence:
    """测试TaskStatePersistence类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.persistence = TaskStatePersistence(self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_save_and_load_task_state(self):
        """测试保存和加载任务状态"""
        task_state = TaskState(
            task_id="test_task",
            execution_id="exec_123",
            status="running",
            start_time=datetime.now(),
            last_update_time=datetime.now(),
            retry_count=1,
            context={"symbol": "AAPL"}
        )
        
        # 保存任务状态
        success = self.persistence.save_task_state(task_state)
        assert success is True
        
        # 加载任务状态
        loaded_state = self.persistence.load_task_state("test_task", "exec_123")
        assert loaded_state is not None
        assert loaded_state.task_id == task_state.task_id
        assert loaded_state.execution_id == task_state.execution_id
        assert loaded_state.status == task_state.status
        assert loaded_state.retry_count == task_state.retry_count
        assert loaded_state.context["symbol"] == "AAPL"
    
    def test_load_nonexistent_task_state(self):
        """测试加载不存在的任务状态"""
        loaded_state = self.persistence.load_task_state("nonexistent", "exec_999")
        assert loaded_state is None
    
    def test_delete_task_state(self):
        """测试删除任务状态"""
        task_state = TaskState(
            task_id="delete_task",
            execution_id="exec_456",
            status="completed",
            start_time=datetime.now(),
            last_update_time=datetime.now()
        )
        
        # 保存任务状态
        self.persistence.save_task_state(task_state)
        
        # 确认存在
        loaded_state = self.persistence.load_task_state("delete_task", "exec_456")
        assert loaded_state is not None
        
        # 删除任务状态
        success = self.persistence.delete_task_state("delete_task", "exec_456")
        assert success is True
        
        # 确认已删除
        loaded_state = self.persistence.load_task_state("delete_task", "exec_456")
        assert loaded_state is None
    
    def test_list_task_states(self):
        """测试列出所有任务状态"""
        # 创建多个任务状态
        states = []
        for i in range(3):
            state = TaskState(
                task_id=f"task_{i}",
                execution_id=f"exec_{i}",
                status="completed",
                start_time=datetime.now(),
                last_update_time=datetime.now()
            )
            states.append(state)
            self.persistence.save_task_state(state)
        
        # 列出所有任务状态
        all_states = self.persistence.list_task_states()
        assert len(all_states) == 3
        
        # 验证任务ID
        task_ids = {state.task_id for state in all_states}
        expected_ids = {f"task_{i}" for i in range(3)}
        assert task_ids == expected_ids
    
    def test_cleanup_old_states(self):
        """测试清理旧的任务状态"""
        # 创建一个旧的任务状态文件
        old_file = os.path.join(self.temp_dir, "old_task_exec_old.json")
        with open(old_file, 'w') as f:
            json.dump({"test": "data"}, f)
        
        # 修改文件时间为8天前
        old_time = time.time() - (8 * 24 * 3600)
        os.utime(old_file, (old_time, old_time))
        
        # 创建一个新的任务状态
        new_state = TaskState(
            task_id="new_task",
            execution_id="exec_new",
            status="completed",
            start_time=datetime.now(),
            last_update_time=datetime.now()
        )
        self.persistence.save_task_state(new_state)
        
        # 清理旧状态（保留7天内的）
        cleaned_count = self.persistence.cleanup_old_states(max_age_days=7)
        assert cleaned_count == 1
        
        # 验证旧文件被删除，新文件保留
        assert not os.path.exists(old_file)
        remaining_states = self.persistence.list_task_states()
        assert len(remaining_states) == 1
        assert remaining_states[0].task_id == "new_task"


class TestTaskFailureHandler:
    """测试TaskFailureHandler类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.retry_config = RetryConfig(
            max_retries=2,
            base_delay_seconds=0.1,  # 快速测试
            jitter=False
        )
        self.handler = TaskFailureHandler(self.retry_config, self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir)
    
    @pytest.mark.asyncio
    async def test_handle_task_failure_with_retry(self):
        """测试处理任务失败并重试"""
        exception = ConnectionError("Network connection failed")
        context = {"url": "http://example.com"}
        
        # 第一次失败，应该重试
        should_retry = await self.handler.handle_task_failure(
            "test_task", "exec_123", exception, context
        )
        
        assert should_retry is True
        
        # 检查任务状态
        task_state = self.handler.get_task_state("test_task", "exec_123")
        assert task_state is not None
        assert task_state.status == "retrying"
        assert task_state.retry_count == 1
        assert len(task_state.error_history) == 1
        assert task_state.error_history[0].error_type == ErrorType.NETWORK_ERROR
    
    @pytest.mark.asyncio
    async def test_handle_task_failure_no_retry(self):
        """测试处理任务失败不重试"""
        exception = PermissionError("Authentication failed")
        context = {"user": "test_user"}
        
        # 认证错误不应该重试
        should_retry = await self.handler.handle_task_failure(
            "auth_task", "exec_456", exception, context
        )
        
        assert should_retry is False
        
        # 检查任务状态
        task_state = self.handler.get_task_state("auth_task", "exec_456")
        assert task_state is not None
        assert task_state.status == "failed"
        assert task_state.retry_count == 0
        assert len(task_state.error_history) == 1
        assert task_state.error_history[0].error_type == ErrorType.AUTHENTICATION_ERROR
    
    @pytest.mark.asyncio
    async def test_handle_task_failure_exceed_retry_limit(self):
        """测试超过重试限制的任务失败"""
        exception = TimeoutError("Request timeout")
        
        # 模拟多次失败
        for i in range(3):  # max_retries = 2, 所以第3次应该不重试
            should_retry = await self.handler.handle_task_failure(
                "timeout_task", "exec_789", exception
            )
            
            if i < 2:
                assert should_retry is True
            else:
                assert should_retry is False
        
        # 检查最终状态
        task_state = self.handler.get_task_state("timeout_task", "exec_789")
        assert task_state is not None
        assert task_state.status == "failed"
        assert task_state.retry_count == 2  # 最后一次不增加重试计数
        assert len(task_state.error_history) == 3
    
    def test_mark_task_success(self):
        """测试标记任务成功"""
        # 先创建一个失败的任务状态
        task_state = TaskState(
            task_id="success_task",
            execution_id="exec_success",
            status="retrying",
            start_time=datetime.now(),
            last_update_time=datetime.now(),
            retry_count=1
        )
        self.handler.persistence.save_task_state(task_state)
        
        # 标记为成功
        self.handler.mark_task_success("success_task", "exec_success")
        
        # 检查状态更新
        updated_state = self.handler.get_task_state("success_task", "exec_success")
        assert updated_state is not None
        assert updated_state.status == "completed"
        assert updated_state.retry_count == 1  # 保持原有重试计数
    
    def test_get_failed_tasks(self):
        """测试获取失败的任务"""
        # 创建多个不同状态的任务
        states = [
            TaskState("task1", "exec1", "failed", datetime.now(), datetime.now()),
            TaskState("task2", "exec2", "completed", datetime.now(), datetime.now()),
            TaskState("task3", "exec3", "failed", datetime.now(), datetime.now()),
            TaskState("task4", "exec4", "retrying", datetime.now(), datetime.now())
        ]
        
        for state in states:
            self.handler.persistence.save_task_state(state)
        
        # 获取失败的任务
        failed_tasks = self.handler.get_failed_tasks()
        assert len(failed_tasks) == 2
        
        failed_task_ids = {task.task_id for task in failed_tasks}
        assert failed_task_ids == {"task1", "task3"}
    
    def test_get_retrying_tasks(self):
        """测试获取正在重试的任务"""
        # 创建多个不同状态的任务
        states = [
            TaskState("task1", "exec1", "retrying", datetime.now(), datetime.now()),
            TaskState("task2", "exec2", "completed", datetime.now(), datetime.now()),
            TaskState("task3", "exec3", "retrying", datetime.now(), datetime.now()),
            TaskState("task4", "exec4", "failed", datetime.now(), datetime.now())
        ]
        
        for state in states:
            self.handler.persistence.save_task_state(state)
        
        # 获取正在重试的任务
        retrying_tasks = self.handler.get_retrying_tasks()
        assert len(retrying_tasks) == 2
        
        retrying_task_ids = {task.task_id for task in retrying_tasks}
        assert retrying_task_ids == {"task1", "task3"}
    
    def test_error_statistics(self):
        """测试错误统计功能"""
        # 重置统计信息
        self.handler.reset_statistics()
        
        # 模拟一些错误
        errors = [
            (ConnectionError("Network error 1"), ErrorType.NETWORK_ERROR),
            (TimeoutError("Timeout error 1"), ErrorType.TIMEOUT_ERROR),
            (ConnectionError("Network error 2"), ErrorType.NETWORK_ERROR),
            (ValueError("Validation error 1"), ErrorType.VALIDATION_ERROR)
        ]
        
        for i, (exception, expected_type) in enumerate(errors):
            error_info = self.handler.error_classifier.classify_error(
                exception, {"task_id": f"task_{i}", "execution_id": f"exec_{i}"}
            )
            self.handler._update_error_stats(error_info)
        
        # 检查统计信息
        stats = self.handler.get_error_statistics()
        assert stats['total_errors'] == 4
        assert stats['errors_by_type'][ErrorType.NETWORK_ERROR.value] == 2
        assert stats['errors_by_type'][ErrorType.TIMEOUT_ERROR.value] == 1
        assert stats['errors_by_type'][ErrorType.VALIDATION_ERROR.value] == 1
    
    def test_error_callbacks(self):
        """测试错误回调功能"""
        # 设置回调函数
        error_callback_called = []
        retry_callback_called = []
        recovery_callback_called = []
        
        def error_callback(error_info):
            error_callback_called.append(error_info)
        
        def retry_callback(task_id, retry_count, delay):
            retry_callback_called.append((task_id, retry_count, delay))
        
        def recovery_callback(task_id, retry_count):
            recovery_callback_called.append((task_id, retry_count))
        
        self.handler.set_callbacks(
            error_callback=error_callback,
            retry_callback=retry_callback,
            recovery_callback=recovery_callback
        )
        
        # 测试错误回调
        exception = ConnectionError("Test error")
        asyncio.run(self.handler.handle_task_failure(
            "callback_task", "exec_callback", exception
        ))
        
        assert len(error_callback_called) == 1
        assert len(retry_callback_called) == 1
        assert error_callback_called[0].error_type == ErrorType.NETWORK_ERROR
        
        # 测试恢复回调
        self.handler.mark_task_success("callback_task", "exec_callback")
        assert len(recovery_callback_called) == 1
        assert recovery_callback_called[0][0] == "callback_task"
    
    def test_error_history(self):
        """测试错误历史功能"""
        # 创建一些错误历史
        error_infos = [
            ErrorInfo(
                error_type=ErrorType.NETWORK_ERROR,
                severity=ErrorSeverity.MEDIUM,
                message="Network error 1",
                timestamp=datetime.now(),
                task_id="task1",
                execution_id="exec1"
            ),
            ErrorInfo(
                error_type=ErrorType.TIMEOUT_ERROR,
                severity=ErrorSeverity.MEDIUM,
                message="Timeout error 1",
                timestamp=datetime.now(),
                task_id="task2",
                execution_id="exec2"
            )
        ]
        
        # 创建任务状态并添加错误历史
        for i, error_info in enumerate(error_infos):
            task_state = TaskState(
                task_id=error_info.task_id,
                execution_id=error_info.execution_id,
                status="failed",
                start_time=datetime.now(),
                last_update_time=datetime.now(),
                error_history=[error_info]
            )
            self.handler.persistence.save_task_state(task_state)
        
        # 获取错误历史
        history = self.handler.get_error_history()
        assert len(history) == 2
        
        # 按任务ID过滤
        task1_history = self.handler.get_error_history(task_id="task1")
        assert len(task1_history) == 1
        assert task1_history[0]['task_id'] == "task1"
        
        # 按错误类型过滤
        network_history = self.handler.get_error_history(error_type=ErrorType.NETWORK_ERROR)
        assert len(network_history) == 1
        assert network_history[0]['error_type'] == ErrorType.NETWORK_ERROR.value
    
    def test_export_error_report(self):
        """测试导出错误报告"""
        # 创建一些测试数据
        exception = ConnectionError("Test error for report")
        asyncio.run(self.handler.handle_task_failure(
            "report_task", "exec_report", exception
        ))
        
        # 导出报告
        report_path = os.path.join(self.temp_dir, "error_report.json")
        success = self.handler.export_error_report(report_path)
        assert success is True
        
        # 验证报告文件
        assert os.path.exists(report_path)
        
        with open(report_path, 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        assert 'generated_at' in report
        assert 'statistics' in report
        assert 'failed_tasks' in report
        assert 'retrying_tasks' in report
        assert 'recent_errors' in report
    
    def test_analyze_error_patterns(self):
        """测试错误模式分析"""
        # 创建一些测试错误数据
        errors = [
            (ConnectionError("Network error 1"), "task1"),
            (ConnectionError("Network error 2"), "task1"),  # 同一任务多个错误
            (TimeoutError("Timeout error 1"), "task2"),
            (ValueError("Validation error 1"), "task3")
        ]
        
        for i, (exception, task_id) in enumerate(errors):
            error_info = self.handler.error_classifier.classify_error(
                exception, {"task_id": task_id, "execution_id": f"exec_{i}"}
            )
            
            task_state = TaskState(
                task_id=task_id,
                execution_id=f"exec_{i}",
                status="failed",
                start_time=datetime.now(),
                last_update_time=datetime.now(),
                error_history=[error_info]
            )
            self.handler.persistence.save_task_state(task_state)
        
        # 分析错误模式
        analysis = self.handler.analyze_error_patterns()
        
        assert analysis['total_errors'] == 4
        assert analysis['most_common_error_type'] == ErrorType.NETWORK_ERROR.value
        assert 'task1' in analysis['tasks_with_most_errors']
        assert analysis['tasks_with_most_errors']['task1'] == 2  # task1有2个错误
        assert 'error_frequency_by_hour' in analysis
        assert 'average_retries_per_error_type' in analysis


class TestIntegration:
    """集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.retry_config = RetryConfig(
            max_retries=2,
            base_delay_seconds=0.01,  # 非常快的测试
            jitter=False
        )
        self.handler = TaskFailureHandler(self.retry_config, self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir)
    
    @pytest.mark.asyncio
    async def test_complete_failure_recovery_cycle(self):
        """测试完整的失败恢复周期"""
        task_id = "integration_task"
        execution_id = "exec_integration"
        
        # 第一次失败 - 网络错误，应该重试
        exception1 = ConnectionError("Network connection failed")
        should_retry1 = await self.handler.handle_task_failure(
            task_id, execution_id, exception1
        )
        assert should_retry1 is True
        
        # 检查状态
        state1 = self.handler.get_task_state(task_id, execution_id)
        assert state1.status == "retrying"
        assert state1.retry_count == 1
        
        # 第二次失败 - 仍然是网络错误，继续重试
        exception2 = ConnectionError("Network still failed")
        should_retry2 = await self.handler.handle_task_failure(
            task_id, execution_id, exception2
        )
        assert should_retry2 is True
        
        # 检查状态
        state2 = self.handler.get_task_state(task_id, execution_id)
        assert state2.status == "retrying"
        assert state2.retry_count == 2
        assert len(state2.error_history) == 2
        
        # 第三次失败 - 超过重试限制，不再重试
        exception3 = ConnectionError("Network failed again")
        should_retry3 = await self.handler.handle_task_failure(
            task_id, execution_id, exception3
        )
        assert should_retry3 is False
        
        # 检查最终状态
        state3 = self.handler.get_task_state(task_id, execution_id)
        assert state3.status == "failed"
        assert state3.retry_count == 2  # 不增加，因为不再重试
        assert len(state3.error_history) == 3
        
        # 验证错误统计
        stats = self.handler.get_error_statistics()
        assert stats['total_errors'] == 3
        assert stats['retry_attempts'] == 2
        assert stats['failed_retries'] == 1
    
    @pytest.mark.asyncio
    async def test_different_error_types_handling(self):
        """测试不同错误类型的处理"""
        test_cases = [
            (ConnectionError("Network error"), True, ErrorType.NETWORK_ERROR),
            (TimeoutError("Request timeout"), True, ErrorType.TIMEOUT_ERROR),
            (PermissionError("Access denied"), False, ErrorType.AUTHENTICATION_ERROR),
            (ValueError("Invalid data"), False, ErrorType.VALIDATION_ERROR),
            (Exception("Rate limit exceeded"), True, ErrorType.RATE_LIMIT_ERROR)
        ]
        
        for i, (exception, should_retry_expected, error_type_expected) in enumerate(test_cases):
            task_id = f"error_type_task_{i}"
            execution_id = f"exec_{i}"
            
            should_retry = await self.handler.handle_task_failure(
                task_id, execution_id, exception
            )
            
            assert should_retry == should_retry_expected
            
            state = self.handler.get_task_state(task_id, execution_id)
            assert len(state.error_history) == 1
            assert state.error_history[0].error_type == error_type_expected
            
            if should_retry_expected:
                assert state.status == "retrying"
                assert state.retry_count == 1
            else:
                assert state.status == "failed"
                assert state.retry_count == 0
    
    def test_persistence_across_handler_instances(self):
        """测试跨处理器实例的持久化"""
        task_id = "persistence_task"
        execution_id = "exec_persistence"
        
        # 使用第一个处理器实例创建任务状态
        exception = ConnectionError("Network error")
        asyncio.run(self.handler.handle_task_failure(task_id, execution_id, exception))
        
        # 创建新的处理器实例
        new_handler = TaskFailureHandler(self.retry_config, self.temp_dir)
        
        # 验证状态被正确加载
        loaded_state = new_handler.get_task_state(task_id, execution_id)
        assert loaded_state is not None
        assert loaded_state.task_id == task_id
        assert loaded_state.execution_id == execution_id
        assert loaded_state.status == "retrying"
        assert loaded_state.retry_count == 1
        assert len(loaded_state.error_history) == 1
    
    def test_cleanup_and_maintenance(self):
        """测试清理和维护功能"""
        # 创建一些任务状态
        for i in range(5):
            state = TaskState(
                task_id=f"cleanup_task_{i}",
                execution_id=f"exec_{i}",
                status="completed",
                start_time=datetime.now(),
                last_update_time=datetime.now()
            )
            self.handler.persistence.save_task_state(state)
        
        # 验证创建了5个状态
        all_states = self.handler.get_all_task_states()
        assert len(all_states) == 5
        
        # 清理旧状态（这里不会清理任何东西，因为都是新创建的）
        cleaned_count = self.handler.cleanup_old_states(max_age_days=1)
        assert cleaned_count == 0
        
        # 验证状态仍然存在
        all_states_after = self.handler.get_all_task_states()
        assert len(all_states_after) == 5
        
        # 重置统计信息
        self.handler.reset_statistics()
        stats = self.handler.get_error_statistics()
        assert stats['total_errors'] == 0
        assert stats['retry_attempts'] == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])