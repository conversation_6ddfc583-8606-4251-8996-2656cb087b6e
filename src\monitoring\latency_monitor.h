#pragma once

#include <chrono>
#include <atomic>
#include <memory>
#include <thread>
#include <functional>
#include <queue>
#include <mutex>
#include <condition_variable>

namespace monitoring {

class AlertManager;

struct LatencyMeasurement {
    std::string operation;
    std::chrono::high_resolution_clock::time_point start_time;
    std::chrono::high_resolution_clock::time_point end_time;
    double latency_microseconds;
    std::string symbol;
    uint64_t sequence_id;
};

class LatencyMonitor {
public:
    explicit LatencyMonitor(std::shared_ptr<AlertManager> alert_manager);
    ~LatencyMonitor();
    
    // Start/stop monitoring
    bool start();
    void stop();
    
    // Record latency measurements
    void recordLatency(const LatencyMeasurement& measurement);
    
    // Convenience methods for common operations
    uint64_t startMeasurement(const std::string& operation, const std::string& symbol = "");
    void endMeasurement(uint64_t measurement_id);
    
    // Configuration
    void setLatencyThreshold(double threshold_microseconds) { 
        latency_threshold_microseconds_ = threshold_microseconds; 
    }
    void setAlertCooldown(std::chrono::seconds cooldown) { 
        alert_cooldown_ = cooldown; 
    }
    
    // Statistics
    double getAverageLatency() const { return average_latency_.load(); }
    double getMaxLatency() const { return max_latency_.load(); }
    uint64_t getViolationCount() const { return violation_count_.load(); }
    
private:
    std::shared_ptr<AlertManager> alert_manager_;
    
    // Configuration
    std::atomic<double> latency_threshold_microseconds_{50.0}; // 50 microseconds
    std::chrono::seconds alert_cooldown_{30}; // 30 seconds between alerts
    
    // Statistics
    std::atomic<double> average_latency_{0.0};
    std::atomic<double> max_latency_{0.0};
    std::atomic<uint64_t> violation_count_{0};
    std::atomic<uint64_t> total_measurements_{0};
    
    // Threading
    std::atomic<bool> running_{false};
    std::thread processing_thread_;
    
    // Measurement queue
    std::queue<LatencyMeasurement> measurement_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // Active measurements (for start/end pattern)
    struct ActiveMeasurement {
        std::string operation;
        std::string symbol;
        std::chrono::high_resolution_clock::time_point start_time;
    };
    std::unordered_map<uint64_t, ActiveMeasurement> active_measurements_;
    std::mutex active_measurements_mutex_;
    std::atomic<uint64_t> next_measurement_id_{1};
    
    // Alert state
    std::chrono::steady_clock::time_point last_alert_time_;
    std::mutex alert_mutex_;
    
    // Processing methods
    void processingLoop();
    void processLatencyMeasurement(const LatencyMeasurement& measurement);
    void updateStatistics(double latency_microseconds);
    void checkThresholdViolation(const LatencyMeasurement& measurement);
    void sendLatencyAlert(const LatencyMeasurement& measurement);
    
    // Utility methods
    double calculateMicroseconds(
        const std::chrono::high_resolution_clock::time_point& start,
        const std::chrono::high_resolution_clock::time_point& end
    );
};

} // namespace monitoring