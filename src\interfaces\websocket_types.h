#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <atomic>
#include <chrono>
#include <functional>
#include <nlohmann/json.hpp>
#include "data_types.h"

namespace financial_data {
namespace interfaces {

/**
 * @brief WebSocket连接状态
 */
enum class ConnectionState {
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    DISCONNECTED,
    ERROR
};

/**
 * @brief WebSocket消息类型
 */
enum class MessageType {
    SUBSCRIBE,
    UNSUBSCRIBE,
    HEARTBEAT,
    MARKET_DATA,
    ERROR_MESSAGE,
    STATUS_UPDATE
};

/**
 * @brief 订阅请求结构
 */
struct SubscriptionRequest {
    std::string client_id;
    std::vector<std::string> symbols;
    std::vector<std::string> exchanges;
    std::vector<std::string> data_types;  // tick, level2, kline
    std::unordered_map<std::string, std::string> filters;  // 自定义过滤器
    bool include_history = false;
    int64_t start_time_ns = 0;
    
    // JSON序列化
    nlohmann::json ToJson() const {
        nlohmann::json j;
        j["client_id"] = client_id;
        j["symbols"] = symbols;
        j["exchanges"] = exchanges;
        j["data_types"] = data_types;
        j["filters"] = filters;
        j["include_history"] = include_history;
        j["start_time_ns"] = start_time_ns;
        return j;
    }
    
    // JSON反序列化
    static SubscriptionRequest FromJson(const nlohmann::json& j) {
        SubscriptionRequest req;
        req.client_id = j.value("client_id", "");
        req.symbols = j.value("symbols", std::vector<std::string>{});
        req.exchanges = j.value("exchanges", std::vector<std::string>{});
        req.data_types = j.value("data_types", std::vector<std::string>{"tick"});
        req.filters = j.value("filters", std::unordered_map<std::string, std::string>{});
        req.include_history = j.value("include_history", false);
        req.start_time_ns = j.value("start_time_ns", 0L);
        return req;
    }
};

/**
 * @brief 订阅响应结构
 */
struct SubscriptionResponse {
    bool success = false;
    std::string message;
    std::string subscription_id;
    std::vector<std::string> subscribed_symbols;
    
    nlohmann::json ToJson() const {
        nlohmann::json j;
        j["success"] = success;
        j["message"] = message;
        j["subscription_id"] = subscription_id;
        j["subscribed_symbols"] = subscribed_symbols;
        return j;
    }
};

/**
 * @brief WebSocket消息结构
 */
struct WebSocketMessage {
    MessageType type;
    std::string client_id;
    nlohmann::json payload;
    int64_t timestamp_ns;
    std::string message_id;
    
    WebSocketMessage() : timestamp_ns(StandardTick::GetCurrentTimestampNs()) {}
    
    std::string ToString() const {
        nlohmann::json j;
        j["type"] = static_cast<int>(type);
        j["client_id"] = client_id;
        j["payload"] = payload;
        j["timestamp_ns"] = timestamp_ns;
        j["message_id"] = message_id;
        return j.dump();
    }
    
    static WebSocketMessage FromString(const std::string& str) {
        WebSocketMessage msg;
        try {
            auto j = nlohmann::json::parse(str);
            msg.type = static_cast<MessageType>(j.value("type", 0));
            msg.client_id = j.value("client_id", "");
            msg.payload = j.value("payload", nlohmann::json{});
            msg.timestamp_ns = j.value("timestamp_ns", 0L);
            msg.message_id = j.value("message_id", "");
        } catch (const std::exception& e) {
            // 解析失败，返回错误消息
            msg.type = MessageType::ERROR_MESSAGE;
            msg.payload["error"] = "Failed to parse message: " + std::string(e.what());
        }
        return msg;
    }
};

/**
 * @brief 客户端连接信息
 */
struct ClientConnection {
    std::string client_id;
    std::string remote_address;
    std::string user_agent;
    ConnectionState state = ConnectionState::CONNECTING;
    
    // 订阅信息
    std::unordered_set<std::string> subscribed_symbols;
    std::unordered_set<std::string> subscribed_exchanges;
    std::unordered_set<std::string> subscribed_data_types;
    std::unordered_map<std::string, std::string> subscription_filters;
    
    // 连接统计
    std::atomic<uint64_t> messages_sent{0};
    std::atomic<uint64_t> messages_received{0};
    std::atomic<uint64_t> bytes_sent{0};
    std::atomic<uint64_t> bytes_received{0};
    
    // 时间戳
    std::chrono::steady_clock::time_point connect_time;
    std::chrono::steady_clock::time_point last_heartbeat;
    std::chrono::steady_clock::time_point last_activity;
    
    // 性能统计
    std::atomic<uint64_t> avg_latency_ns{0};
    std::atomic<uint64_t> max_latency_ns{0};
    
    ClientConnection() {
        auto now = std::chrono::steady_clock::now();
        connect_time = now;
        last_heartbeat = now;
        last_activity = now;
    }
    
    void UpdateActivity() {
        last_activity = std::chrono::steady_clock::now();
    }
    
    void UpdateHeartbeat() {
        last_heartbeat = std::chrono::steady_clock::now();
        UpdateActivity();
    }
    
    bool IsSubscribedTo(const std::string& symbol, const std::string& exchange = "") const {
        if (!subscribed_symbols.empty() && subscribed_symbols.find(symbol) == subscribed_symbols.end()) {
            return false;
        }
        if (!exchange.empty() && !subscribed_exchanges.empty() && 
            subscribed_exchanges.find(exchange) == subscribed_exchanges.end()) {
            return false;
        }
        return true;
    }
    
    std::chrono::milliseconds GetConnectionDuration() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - connect_time);
    }
    
    std::chrono::milliseconds GetTimeSinceLastHeartbeat() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - last_heartbeat);
    }
};

/**
 * @brief WebSocket服务器配置
 */
struct WebSocketConfig {
    // 服务器配置
    std::string host = "0.0.0.0";
    uint16_t port = 8080;
    size_t max_connections = 1000;  // 支持1000个并发连接
    size_t thread_pool_size = 4;
    
    // 消息配置
    size_t max_message_size = 1024 * 1024;  // 1MB
    bool enable_compression = true;
    std::string compression_algorithm = "deflate";  // deflate, gzip
    size_t compression_threshold = 1024;  // 压缩阈值
    
    // 批量推送配置
    bool enable_batching = true;
    size_t batch_size = 100;
    uint32_t batch_timeout_ms = 10;
    size_t max_batch_size = 1000;
    
    // 心跳配置
    bool enable_heartbeat = true;
    uint32_t heartbeat_interval_ms = 30000;  // 30秒
    uint32_t heartbeat_timeout_ms = 60000;   // 60秒
    
    // 性能配置
    size_t send_buffer_size = 64 * 1024;     // 64KB
    size_t receive_buffer_size = 64 * 1024;  // 64KB
    bool enable_tcp_nodelay = true;
    bool enable_keep_alive = true;
    
    // 安全配置
    bool enable_ssl = false;
    std::string ssl_cert_file;
    std::string ssl_key_file;
    std::string ssl_ca_file;
    
    // 监控配置
    bool enable_monitoring = true;
    uint32_t statistics_interval_ms = 1000;
    
    // 日志配置
    std::string log_level = "info";
    bool log_messages = false;
    bool log_connections = true;
};

/**
 * @brief WebSocket服务器统计信息
 */
struct WebSocketStatistics {
    // 连接统计
    std::atomic<uint64_t> total_connections{0};
    std::atomic<uint64_t> active_connections{0};
    std::atomic<uint64_t> max_concurrent_connections{0};
    std::atomic<uint64_t> failed_connections{0};
    
    // 消息统计
    std::atomic<uint64_t> total_messages_sent{0};
    std::atomic<uint64_t> total_messages_received{0};
    std::atomic<uint64_t> total_bytes_sent{0};
    std::atomic<uint64_t> total_bytes_received{0};
    
    // 订阅统计
    std::atomic<uint64_t> total_subscriptions{0};
    std::atomic<uint64_t> active_subscriptions{0};
    std::atomic<uint64_t> subscription_errors{0};
    
    // 性能统计
    std::atomic<uint64_t> avg_message_latency_ns{0};
    std::atomic<uint64_t> max_message_latency_ns{0};
    std::atomic<uint64_t> messages_per_second{0};
    
    // 压缩统计
    std::atomic<uint64_t> compressed_messages{0};
    std::atomic<uint64_t> compression_ratio_percent{0};  // 压缩率百分比
    
    // 错误统计
    std::atomic<uint64_t> protocol_errors{0};
    std::atomic<uint64_t> timeout_errors{0};
    std::atomic<uint64_t> send_errors{0};
    std::atomic<uint64_t> receive_errors{0};
    
    void Reset() {
        total_connections = 0;
        active_connections = 0;
        max_concurrent_connections = 0;
        failed_connections = 0;
        total_messages_sent = 0;
        total_messages_received = 0;
        total_bytes_sent = 0;
        total_bytes_received = 0;
        total_subscriptions = 0;
        active_subscriptions = 0;
        subscription_errors = 0;
        avg_message_latency_ns = 0;
        max_message_latency_ns = 0;
        messages_per_second = 0;
        compressed_messages = 0;
        compression_ratio_percent = 0;
        protocol_errors = 0;
        timeout_errors = 0;
        send_errors = 0;
        receive_errors = 0;
    }
    
    double GetSuccessRate() const {
        uint64_t total = total_connections.load();
        return total > 0 ? static_cast<double>(total - failed_connections.load()) / total : 0.0;
    }
    
    double GetAverageCompressionRatio() const {
        return compression_ratio_percent.load() / 100.0;
    }
};

/**
 * @brief 消息批次结构
 */
struct MessageBatch {
    std::vector<nlohmann::json> messages;
    int64_t batch_timestamp_ns;
    uint32_t batch_sequence;
    std::string target_client_id;
    
    MessageBatch() : batch_timestamp_ns(StandardTick::GetCurrentTimestampNs()), batch_sequence(0) {
        messages.reserve(100);  // 预分配空间
    }
    
    void AddMessage(const nlohmann::json& message) {
        messages.push_back(message);
    }
    
    void AddMarketData(const StandardTick& tick) {
        nlohmann::json j;
        j["type"] = "tick";
        j["timestamp_ns"] = tick.timestamp_ns;
        j["symbol"] = tick.symbol;
        j["exchange"] = tick.exchange;
        j["last_price"] = tick.last_price;
        j["volume"] = tick.volume;
        j["turnover"] = tick.turnover;
        j["open_interest"] = tick.open_interest;
        j["sequence"] = tick.sequence;
        j["trade_flag"] = tick.trade_flag;
        
        // 添加买卖盘数据
        nlohmann::json bids = nlohmann::json::array();
        nlohmann::json asks = nlohmann::json::array();
        
        for (const auto& bid : tick.bids) {
            if (bid.price > 0) {
                bids.push_back({
                    {"price", bid.price},
                    {"volume", bid.volume},
                    {"order_count", bid.order_count}
                });
            }
        }
        
        for (const auto& ask : tick.asks) {
            if (ask.price > 0) {
                asks.push_back({
                    {"price", ask.price},
                    {"volume", ask.volume},
                    {"order_count", ask.order_count}
                });
            }
        }
        
        j["bids"] = bids;
        j["asks"] = asks;
        
        messages.push_back(j);
    }
    
    void AddLevel2Data(const Level2Data& level2) {
        nlohmann::json j;
        j["type"] = "level2";
        j["timestamp_ns"] = level2.timestamp_ns;
        j["symbol"] = level2.symbol;
        j["exchange"] = level2.exchange;
        j["sequence"] = level2.sequence;
        
        nlohmann::json bids = nlohmann::json::array();
        nlohmann::json asks = nlohmann::json::array();
        
        for (const auto& bid : level2.bids) {
            bids.push_back({
                {"price", bid.price},
                {"volume", bid.volume},
                {"order_count", bid.order_count}
            });
        }
        
        for (const auto& ask : level2.asks) {
            asks.push_back({
                {"price", ask.price},
                {"volume", ask.volume},
                {"order_count", ask.order_count}
            });
        }
        
        j["bids"] = bids;
        j["asks"] = asks;
        
        messages.push_back(j);
    }
    
    size_t Size() const {
        return messages.size();
    }
    
    bool IsEmpty() const {
        return messages.empty();
    }
    
    void Clear() {
        messages.clear();
        batch_timestamp_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    std::string ToJsonString() const {
        nlohmann::json batch_json;
        batch_json["batch_timestamp_ns"] = batch_timestamp_ns;
        batch_json["batch_sequence"] = batch_sequence;
        batch_json["messages"] = messages;
        return batch_json.dump();
    }
};

} // namespace interfaces
} // namespace financial_data