#include "rbac_manager.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <json/json.h>

namespace financial_data {
namespace security {

RBACManager::RBACManager(const RBACConfig& config)
    : config_(config), initialized_(false) {
}

RBACManager::~RBACManager() {
    if (initialized_) {
        SaveRoles();
        SaveUserRoles();
    }
}

bool RBACManager::Initialize() {
    if (initialized_) {
        return true;
    }
    
    // 加载角色和用户角色分配
    LoadRoles();
    LoadUserRoles();
    
    // 创建默认角色（如果不存在）
    if (roles_.empty()) {
        CreateDefaultRoles();
    }
    
    initialized_ = true;
    return true;
}

bool RBACManager::CreateRole(const Role& role) {
    if (roles_.find(role.role_id) != roles_.end()) {
        return false; // 角色已存在
    }
    
    roles_[role.role_id] = role;
    SaveRoles();
    return true;
}

bool RBACManager::UpdateRole(const Role& role) {
    auto it = roles_.find(role.role_id);
    if (it == roles_.end()) {
        return false; // 角色不存在
    }
    
    it->second = role;
    it->second.updated_at = std::chrono::system_clock::now();
    
    // 清除相关用户的权限缓存
    for (const auto& user_role_pair : user_roles_) {
        for (const auto& assignment : user_role_pair.second) {
            if (assignment.role_id == role.role_id && assignment.is_active) {
                InvalidateUserCache(user_role_pair.first);
                break;
            }
        }
    }
    
    SaveRoles();
    return true;
}

bool RBACManager::DeleteRole(const std::string& role_id) {
    auto it = roles_.find(role_id);
    if (it == roles_.end()) {
        return false;
    }
    
    // 检查是否有用户使用此角色
    for (const auto& user_role_pair : user_roles_) {
        for (const auto& assignment : user_role_pair.second) {
            if (assignment.role_id == role_id && assignment.is_active) {
                return false; // 角色正在使用中，不能删除
            }
        }
    }
    
    roles_.erase(it);
    SaveRoles();
    return true;
}

Role RBACManager::GetRole(const std::string& role_id) {
    auto it = roles_.find(role_id);
    if (it != roles_.end()) {
        return it->second;
    }
    return Role{}; // 返回空角色
}

std::vector<Role> RBACManager::GetAllRoles() {
    std::vector<Role> result;
    for (const auto& pair : roles_) {
        result.push_back(pair.second);
    }
    return result;
}

bool RBACManager::AssignRoleToUser(const std::string& user_id, const std::string& role_id,
                                  const std::string& assigned_by,
                                  std::chrono::system_clock::time_point expires_at) {
    // 检查角色是否存在
    if (roles_.find(role_id) == roles_.end()) {
        return false;
    }
    
    // 检查用户是否已有此角色
    auto& user_assignments = user_roles_[user_id];
    for (auto& assignment : user_assignments) {
        if (assignment.role_id == role_id && assignment.is_active) {
            return false; // 用户已有此角色
        }
    }
    
    // 创建新的角色分配
    UserRoleAssignment assignment;
    assignment.user_id = user_id;
    assignment.role_id = role_id;
    assignment.assigned_at = std::chrono::system_clock::now();
    assignment.expires_at = expires_at;
    assignment.is_active = true;
    assignment.assigned_by = assigned_by;
    
    user_assignments.push_back(assignment);
    
    // 清除用户权限缓存
    InvalidateUserCache(user_id);
    
    SaveUserRoles();
    return true;
}

bool RBACManager::RevokeRoleFromUser(const std::string& user_id, const std::string& role_id) {
    auto it = user_roles_.find(user_id);
    if (it == user_roles_.end()) {
        return false;
    }
    
    bool found = false;
    for (auto& assignment : it->second) {
        if (assignment.role_id == role_id && assignment.is_active) {
            assignment.is_active = false;
            found = true;
            break;
        }
    }
    
    if (found) {
        InvalidateUserCache(user_id);
        SaveUserRoles();
    }
    
    return found;
}

std::vector<std::string> RBACManager::GetUserRoles(const std::string& user_id) {
    std::vector<std::string> result;
    
    auto it = user_roles_.find(user_id);
    if (it != user_roles_.end()) {
        auto now = std::chrono::system_clock::now();
        for (const auto& assignment : it->second) {
            if (assignment.is_active && 
                (assignment.expires_at == std::chrono::system_clock::time_point{} || 
                 assignment.expires_at > now)) {
                result.push_back(assignment.role_id);
            }
        }
    }
    
    return result;
}

std::vector<std::string> RBACManager::GetRoleUsers(const std::string& role_id) {
    std::vector<std::string> result;
    auto now = std::chrono::system_clock::now();
    
    for (const auto& user_role_pair : user_roles_) {
        for (const auto& assignment : user_role_pair.second) {
            if (assignment.role_id == role_id && assignment.is_active &&
                (assignment.expires_at == std::chrono::system_clock::time_point{} || 
                 assignment.expires_at > now)) {
                result.push_back(user_role_pair.first);
                break;
            }
        }
    }
    
    return result;
}

bool RBACManager::CheckPermission(const std::string& user_id, Permission permission,
                                 ResourceType resource, Action action,
                                 const std::unordered_map<std::string, std::string>& context) {
    // 获取用户权限
    std::vector<PermissionRule> user_permissions = GetUserPermissions(user_id);
    
    // 检查权限
    bool granted = false;
    for (const auto& rule : user_permissions) {
        if (rule.permission == permission && rule.resource == resource && rule.action == action) {
            if (CheckConditions(rule.conditions, context)) {
                granted = true;
                break;
            }
        }
    }
    
    // 记录权限检查日志
    PermissionCheck check;
    check.user_id = user_id;
    check.permission = permission;
    check.resource = resource;
    check.action = action;
    check.granted = granted;
    check.timestamp = std::chrono::system_clock::now();
    
    // 构建上下文字符串
    Json::Value context_json;
    for (const auto& pair : context) {
        context_json[pair.first] = pair.second;
    }
    Json::StreamWriterBuilder builder;
    check.context = Json::writeString(builder, context_json);
    
    permission_audit_log_.push_back(check);
    
    // 限制审计日志大小
    if (permission_audit_log_.size() > 10000) {
        permission_audit_log_.erase(permission_audit_log_.begin(), 
                                   permission_audit_log_.begin() + 1000);
    }
    
    return granted;
}

std::vector<PermissionRule> RBACManager::GetUserPermissions(const std::string& user_id) {
    // 检查缓存
    if (IsCacheValid(user_id)) {
        return user_permissions_cache_[user_id];
    }
    
    // 解析用户权限
    std::vector<PermissionRule> permissions = ResolveUserPermissions(user_id);
    
    // 添加动态权限
    auto dynamic_it = dynamic_permissions_.find(user_id);
    if (dynamic_it != dynamic_permissions_.end()) {
        permissions.insert(permissions.end(), 
                          dynamic_it->second.begin(), 
                          dynamic_it->second.end());
    }
    
    // 更新缓存
    UpdateCache(user_id, permissions);
    
    return permissions;
}

std::vector<PermissionRule> RBACManager::ResolveUserPermissions(const std::string& user_id) {
    std::vector<PermissionRule> permissions;
    std::unordered_set<std::string> processed_roles;
    
    // 获取用户角色
    std::vector<std::string> user_roles = GetUserRoles(user_id);
    
    // 递归解析角色权限（处理角色继承）
    std::function<void(const std::string&)> resolve_role = [&](const std::string& role_id) {
        if (processed_roles.find(role_id) != processed_roles.end()) {
            return; // 避免循环引用
        }
        processed_roles.insert(role_id);
        
        auto role_it = roles_.find(role_id);
        if (role_it != roles_.end()) {
            const Role& role = role_it->second;
            
            // 添加角色权限
            permissions.insert(permissions.end(), 
                              role.permissions.begin(), 
                              role.permissions.end());
            
            // 递归处理父角色
            for (const std::string& parent_role : role.parent_roles) {
                resolve_role(parent_role);
            }
        }
    };
    
    // 解析所有用户角色
    for (const std::string& role_id : user_roles) {
        resolve_role(role_id);
    }
    
    return permissions;
}

bool RBACManager::CheckConditions(const std::vector<std::string>& conditions,
                                 const std::unordered_map<std::string, std::string>& context) {
    for (const std::string& condition : conditions) {
        // 解析条件格式: "key=value" 或 "key!=value" 等
        size_t eq_pos = condition.find('=');
        if (eq_pos != std::string::npos) {
            std::string key = condition.substr(0, eq_pos);
            std::string expected_value = condition.substr(eq_pos + 1);
            
            auto context_it = context.find(key);
            if (context_it == context.end() || context_it->second != expected_value) {
                return false;
            }
        }
        // 可以添加更多条件类型的处理
    }
    
    return true;
}

void RBACManager::InvalidateUserCache(const std::string& user_id) {
    user_permissions_cache_.erase(user_id);
}

void RBACManager::ClearAllCache() {
    user_permissions_cache_.clear();
}

bool RBACManager::IsCacheValid(const std::string& user_id) {
    return user_permissions_cache_.find(user_id) != user_permissions_cache_.end();
}

void RBACManager::UpdateCache(const std::string& user_id, const std::vector<PermissionRule>& permissions) {
    user_permissions_cache_[user_id] = permissions;
}

void RBACManager::CreateDefaultRoles() {
    // 创建管理员角色
    Role admin_role;
    admin_role.role_id = "admin";
    admin_role.name = "系统管理员";
    admin_role.description = "拥有系统所有权限的管理员角色";
    admin_role.is_active = true;
    admin_role.created_at = std::chrono::system_clock::now();
    
    // 添加所有权限
    std::vector<Permission> all_permissions = {
        Permission::READ_MARKET_DATA, Permission::WRITE_MARKET_DATA,
        Permission::READ_HISTORICAL_DATA, Permission::EXPORT_DATA,
        Permission::MANAGE_USERS, Permission::MANAGE_ROLES,
        Permission::SYSTEM_CONFIG, Permission::VIEW_AUDIT_LOGS,
        Permission::MANAGE_SUBSCRIPTIONS, Permission::API_ACCESS,
        Permission::WEBSOCKET_ACCESS, Permission::GRPC_ACCESS
    };
    
    for (Permission perm : all_permissions) {
        PermissionRule rule;
        rule.permission = perm;
        rule.resource = ResourceType::MARKET_DATA; // 简化处理
        rule.action = Action::MANAGE;
        admin_role.permissions.push_back(rule);
    }
    
    CreateRole(admin_role);
    
    // 创建普通用户角色
    Role user_role;
    user_role.role_id = "user";
    user_role.name = "普通用户";
    user_role.description = "具有基本数据访问权限的用户角色";
    user_role.is_active = true;
    user_role.created_at = std::chrono::system_clock::now();
    
    // 添加基本权限
    std::vector<Permission> user_permissions = {
        Permission::READ_MARKET_DATA, Permission::READ_HISTORICAL_DATA,
        Permission::API_ACCESS, Permission::WEBSOCKET_ACCESS, Permission::GRPC_ACCESS
    };
    
    for (Permission perm : user_permissions) {
        PermissionRule rule;
        rule.permission = perm;
        rule.resource = ResourceType::MARKET_DATA;
        rule.action = Action::READ;
        user_role.permissions.push_back(rule);
    }
    
    CreateRole(user_role);
    
    // 创建只读用户角色
    Role readonly_role;
    readonly_role.role_id = "readonly";
    readonly_role.name = "只读用户";
    readonly_role.description = "只能查看数据的用户角色";
    readonly_role.is_active = true;
    readonly_role.created_at = std::chrono::system_clock::now();
    
    PermissionRule readonly_rule;
    readonly_rule.permission = Permission::READ_MARKET_DATA;
    readonly_rule.resource = ResourceType::MARKET_DATA;
    readonly_rule.action = Action::READ;
    readonly_role.permissions.push_back(readonly_rule);
    
    CreateRole(readonly_role);
}

bool RBACManager::LoadRoles() {
    if (config_.roles_config_file.empty()) {
        return true; // 没有配置文件，使用默认角色
    }
    
    std::ifstream file(config_.roles_config_file);
    if (!file.is_open()) {
        return true; // 文件不存在，使用默认角色
    }
    
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(file, root)) {
        std::cerr << "Failed to parse roles config file" << std::endl;
        return false;
    }
    
    // 解析角色配置
    // 这里简化处理，实际应该完整解析JSON配置
    
    return true;
}

bool RBACManager::SaveRoles() {
    if (config_.roles_config_file.empty()) {
        return true;
    }
    
    Json::Value root;
    
    // 序列化角色数据
    for (const auto& pair : roles_) {
        Json::Value role_json;
        role_json["role_id"] = pair.second.role_id;
        role_json["name"] = pair.second.name;
        role_json["description"] = pair.second.description;
        role_json["is_active"] = pair.second.is_active;
        
        root["roles"].append(role_json);
    }
    
    std::ofstream file(config_.roles_config_file);
    if (!file.is_open()) {
        return false;
    }
    
    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    writer->write(root, &file);
    
    return true;
}

bool RBACManager::LoadUserRoles() {
    // 简化实现，实际应该从持久化存储加载
    return true;
}

bool RBACManager::SaveUserRoles() {
    // 简化实现，实际应该保存到持久化存储
    return true;
}

std::string RBACManager::PermissionToString(Permission permission) {
    switch (permission) {
        case Permission::READ_MARKET_DATA: return "READ_MARKET_DATA";
        case Permission::WRITE_MARKET_DATA: return "WRITE_MARKET_DATA";
        case Permission::READ_HISTORICAL_DATA: return "READ_HISTORICAL_DATA";
        case Permission::EXPORT_DATA: return "EXPORT_DATA";
        case Permission::MANAGE_USERS: return "MANAGE_USERS";
        case Permission::MANAGE_ROLES: return "MANAGE_ROLES";
        case Permission::SYSTEM_CONFIG: return "SYSTEM_CONFIG";
        case Permission::VIEW_AUDIT_LOGS: return "VIEW_AUDIT_LOGS";
        case Permission::MANAGE_SUBSCRIPTIONS: return "MANAGE_SUBSCRIPTIONS";
        case Permission::API_ACCESS: return "API_ACCESS";
        case Permission::WEBSOCKET_ACCESS: return "WEBSOCKET_ACCESS";
        case Permission::GRPC_ACCESS: return "GRPC_ACCESS";
        default: return "UNKNOWN";
    }
}

} // namespace security
} // namespace financial_data