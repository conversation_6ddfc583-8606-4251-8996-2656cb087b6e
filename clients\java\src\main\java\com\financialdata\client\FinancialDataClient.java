package com.financialdata.client;

import io.grpc.*;
import io.grpc.stub.StreamObserver;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.logging.Logger;
import java.util.logging.Level;

// 导入生成的gRPC类
import com.financialdata.proto.*;

/**
 * 金融数据gRPC客户端SDK
 * 支持流式数据订阅、负载均衡和故障转移
 */
public class FinancialDataClient {
    private static final Logger logger = Logger.getLogger(FinancialDataClient.class.getName());
    
    private final LoadBalancer loadBalancer;
    private final Map<String, ManagedChannel> channels;
    private final Map<String, MarketDataServiceGrpc.MarketDataServiceStub> stubs;
    private final Map<String, MarketDataServiceGrpc.MarketDataServiceBlockingStub> blockingStubs;
    private final int maxRetries;
    private final ExecutorService executorService;
    
    /**
     * 负载均衡器
     */
    public static class LoadBalancer {
        private final List<String> servers;
        private final Map<String, ServerStats> serverStats;
        private final Object lock = new Object();
        
        public static class ServerStats {
            public final AtomicLong latency = new AtomicLong(0);
            public final AtomicInteger errors = new AtomicInteger(0);
            public final AtomicBoolean healthy = new AtomicBoolean(true);
            public volatile long lastSeen = System.currentTimeMillis();
        }
        
        public LoadBalancer(List<String> servers) {
            this.servers = new ArrayList<>(servers);
            this.serverStats = new HashMap<>();
            
            for (String server : servers) {
                serverStats.put(server, new ServerStats());
            }
        }
        
        public String getBestServer() {
            synchronized (lock) {
                List<String> healthyServers = new ArrayList<>();
                for (Map.Entry<String, ServerStats> entry : serverStats.entrySet()) {
                    if (entry.getValue().healthy.get()) {
                        healthyServers.add(entry.getKey());
                    }
                }
                
                if (healthyServers.isEmpty()) {
                    // 重置所有服务器状态
                    for (ServerStats stats : serverStats.values()) {
                        stats.healthy.set(true);
                        stats.errors.set(0);
                    }
                    healthyServers = new ArrayList<>(servers);
                }
                
                // 选择延迟最低的服务器
                String bestServer = healthyServers.get(0);
                long bestLatency = serverStats.get(bestServer).latency.get();
                
                for (String server : healthyServers) {
                    long latency = serverStats.get(server).latency.get();
                    if (latency < bestLatency) {
                        bestServer = server;
                        bestLatency = latency;
                    }
                }
                
                return bestServer;
            }
        }
        
        public void reportError(String server) {
            ServerStats stats = serverStats.get(server);
            if (stats != null) {
                int errors = stats.errors.incrementAndGet();
                if (errors > 3) {
                    stats.healthy.set(false);
                    logger.warning("Server " + server + " marked as unhealthy");
                }
            }
        }
        
        public void updateLatency(String server, long latencyMs) {
            ServerStats stats = serverStats.get(server);
            if (stats != null) {
                stats.latency.set(latencyMs);
                stats.lastSeen = System.currentTimeMillis();
            }
        }
    }
    
    /**
     * 流量控制器
     */
    public static class StreamFlowController {
        private final int bufferSize;
        private final AtomicInteger pendingMessages = new AtomicInteger(0);
        private final AtomicBoolean backpressureActive = new AtomicBoolean(false);
        
        public StreamFlowController(int bufferSize) {
            this.bufferSize = bufferSize;
        }
        
        public boolean canSend() {
            return !backpressureActive.get() && pendingMessages.get() < bufferSize;
        }
        
        public void onMessageSent() {
            int pending = pendingMessages.incrementAndGet();
            if (pending >= bufferSize * 0.8) {
                if (backpressureActive.compareAndSet(false, true)) {
                    logger.warning("Backpressure activated");
                }
            }
        }
        
        public void onMessageProcessed() {
            int pending = pendingMessages.get();
            if (pending > 0) {
                pendingMessages.decrementAndGet();
                if (pending <= bufferSize * 0.5) {
                    if (backpressureActive.compareAndSet(true, false)) {
                        logger.info("Backpressure released");
                    }
                }
            }
        }
    }
    
    /**
     * 构造函数
     */
    public FinancialDataClient(List<String> servers, int maxRetries) {
        this.loadBalancer = new LoadBalancer(servers);
        this.channels = new ConcurrentHashMap<>();
        this.stubs = new ConcurrentHashMap<>();
        this.blockingStubs = new ConcurrentHashMap<>();
        this.maxRetries = maxRetries;
        this.executorService = Executors.newCachedThreadPool();
        
        initializeConnections();
    }
    
    /**
     * 初始化连接
     */
    private void initializeConnections() {
        for (String server : loadBalancer.servers) {
            try {
                ManagedChannel channel = ManagedChannelBuilder.forTarget(server)
                    .usePlaintext()
                    .keepAliveTime(30, TimeUnit.SECONDS)
                    .keepAliveTimeout(5, TimeUnit.SECONDS)
                    .keepAliveWithoutCalls(true)
                    .maxInboundMessageSize(4 * 1024 * 1024)
                    .maxInboundMetadataSize(8192)
                    .build();
                
                MarketDataServiceGrpc.MarketDataServiceStub stub = 
                    MarketDataServiceGrpc.newStub(channel);
                MarketDataServiceGrpc.MarketDataServiceBlockingStub blockingStub = 
                    MarketDataServiceGrpc.newBlockingStub(channel);
                
                channels.put(server, channel);
                stubs.put(server, stub);
                blockingStubs.put(server, blockingStub);
                
                logger.info("Connected to server: " + server);
                
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Failed to connect to server " + server, e);
                loadBalancer.reportError(server);
            }
        }
    }
    
    /**
     * 获取可用的stub，支持重试和故障转移
     */
    private MarketDataServiceGrpc.MarketDataServiceStub getStubWithRetry() throws Exception {
        for (int attempt = 0; attempt < maxRetries; attempt++) {
            String server = loadBalancer.getBestServer();
            MarketDataServiceGrpc.MarketDataServiceStub stub = stubs.get(server);
            
            if (stub != null) {
                return stub;
            }
            
            // 尝试重新连接
            initializeConnections();
            stub = stubs.get(server);
            if (stub != null) {
                return stub;
            }
            
            loadBalancer.reportError(server);
            Thread.sleep(100 * (attempt + 1)); // 指数退避
        }
        
        throw new Exception("No healthy servers available");
    }
    
    /**
     * 获取可用的阻塞stub
     */
    private MarketDataServiceGrpc.MarketDataServiceBlockingStub getBlockingStubWithRetry() throws Exception {
        for (int attempt = 0; attempt < maxRetries; attempt++) {
            String server = loadBalancer.getBestServer();
            MarketDataServiceGrpc.MarketDataServiceBlockingStub stub = blockingStubs.get(server);
            
            if (stub != null) {
                return stub;
            }
            
            initializeConnections();
            stub = blockingStubs.get(server);
            if (stub != null) {
                return stub;
            }
            
            loadBalancer.reportError(server);
            Thread.sleep(100 * (attempt + 1));
        }
        
        throw new Exception("No healthy servers available");
    }
    
    /**
     * 订阅实时tick数据流
     */
    public CompletableFuture<Void> streamTickData(List<String> symbols, String exchange, 
            Consumer<TickDataResponse> callback, int bufferSize) {
        
        return CompletableFuture.runAsync(() -> {
            try {
                MarketDataServiceGrpc.MarketDataServiceStub stub = getStubWithRetry();
                String server = loadBalancer.getBestServer();
                
                TickDataRequest request = TickDataRequest.newBuilder()
                    .addAllSymbols(symbols)
                    .setExchange(exchange)
                    .setBufferSize(bufferSize)
                    .build();
                
                StreamFlowController flowController = new StreamFlowController(bufferSize);
                
                stub.streamTickData(request, new StreamObserver<TickDataResponse>() {
                    private long startTime = System.currentTimeMillis();
                    
                    @Override
                    public void onNext(TickDataResponse response) {
                        // 更新延迟统计
                        long latency = System.currentTimeMillis() - startTime;
                        loadBalancer.updateLatency(server, latency);
                        
                        // 流量控制
                        if (!flowController.canSend()) {
                            logger.warning("Flow control: dropping tick message");
                            return;
                        }
                        
                        flowController.onMessageSent();
                        
                        // 异步处理数据
                        executorService.submit(() -> {
                            try {
                                callback.accept(response);
                            } catch (Exception e) {
                                logger.log(Level.SEVERE, "Callback error", e);
                            } finally {
                                flowController.onMessageProcessed();
                            }
                        });
                        
                        startTime = System.currentTimeMillis();
                    }
                    
                    @Override
                    public void onError(Throwable t) {
                        logger.log(Level.SEVERE, "Stream error", t);
                        loadBalancer.reportError(server);
                    }
                    
                    @Override
                    public void onCompleted() {
                        logger.info("Tick data stream completed");
                    }
                });
                
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Failed to start tick data stream", e);
                throw new RuntimeException(e);
            }
        }, executorService);
    }
    
    /**
     * 订阅K线数据流
     */
    public CompletableFuture<Void> streamKlineData(List<String> symbols, String exchange, String period,
            Consumer<KlineDataResponse> callback, int bufferSize) {
        
        return CompletableFuture.runAsync(() -> {
            try {
                MarketDataServiceGrpc.MarketDataServiceStub stub = getStubWithRetry();
                String server = loadBalancer.getBestServer();
                
                KlineDataRequest request = KlineDataRequest.newBuilder()
                    .addAllSymbols(symbols)
                    .setExchange(exchange)
                    .setPeriod(period)
                    .setBufferSize(bufferSize)
                    .build();
                
                StreamFlowController flowController = new StreamFlowController(bufferSize);
                
                stub.streamKlineData(request, new StreamObserver<KlineDataResponse>() {
                    private long startTime = System.currentTimeMillis();
                    
                    @Override
                    public void onNext(KlineDataResponse response) {
                        long latency = System.currentTimeMillis() - startTime;
                        loadBalancer.updateLatency(server, latency);
                        
                        if (!flowController.canSend()) {
                            logger.warning("Flow control: dropping kline message");
                            return;
                        }
                        
                        flowController.onMessageSent();
                        
                        executorService.submit(() -> {
                            try {
                                callback.accept(response);
                            } catch (Exception e) {
                                logger.log(Level.SEVERE, "Kline callback error", e);
                            } finally {
                                flowController.onMessageProcessed();
                            }
                        });
                        
                        startTime = System.currentTimeMillis();
                    }
                    
                    @Override
                    public void onError(Throwable t) {
                        logger.log(Level.SEVERE, "Kline stream error", t);
                        loadBalancer.reportError(server);
                    }
                    
                    @Override
                    public void onCompleted() {
                        logger.info("Kline data stream completed");
                    }
                });
                
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Failed to start kline data stream", e);
                throw new RuntimeException(e);
            }
        }, executorService);
    }
    
    /**
     * 订阅Level2深度数据流
     */
    public CompletableFuture<Void> streamLevel2Data(List<String> symbols, String exchange, int depth,
            Consumer<Level2DataResponse> callback, int bufferSize) {
        
        return CompletableFuture.runAsync(() -> {
            try {
                MarketDataServiceGrpc.MarketDataServiceStub stub = getStubWithRetry();
                String server = loadBalancer.getBestServer();
                
                Level2DataRequest request = Level2DataRequest.newBuilder()
                    .addAllSymbols(symbols)
                    .setExchange(exchange)
                    .setDepth(depth)
                    .setBufferSize(bufferSize)
                    .build();
                
                StreamFlowController flowController = new StreamFlowController(bufferSize);
                
                stub.streamLevel2Data(request, new StreamObserver<Level2DataResponse>() {
                    private long startTime = System.currentTimeMillis();
                    
                    @Override
                    public void onNext(Level2DataResponse response) {
                        long latency = System.currentTimeMillis() - startTime;
                        loadBalancer.updateLatency(server, latency);
                        
                        if (!flowController.canSend()) {
                            logger.warning("Flow control: dropping level2 message");
                            return;
                        }
                        
                        flowController.onMessageSent();
                        
                        executorService.submit(() -> {
                            try {
                                callback.accept(response);
                            } catch (Exception e) {
                                logger.log(Level.SEVERE, "Level2 callback error", e);
                            } finally {
                                flowController.onMessageProcessed();
                            }
                        });
                        
                        startTime = System.currentTimeMillis();
                    }
                    
                    @Override
                    public void onError(Throwable t) {
                        logger.log(Level.SEVERE, "Level2 stream error", t);
                        loadBalancer.reportError(server);
                    }
                    
                    @Override
                    public void onCompleted() {
                        logger.info("Level2 data stream completed");
                    }
                });
                
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Failed to start level2 data stream", e);
                throw new RuntimeException(e);
            }
        }, executorService);
    }
    
    /**
     * 获取历史tick数据
     */
    public CompletableFuture<Void> getHistoricalTickData(String symbol, String exchange,
            long startTimestamp, long endTimestamp, Consumer<TickDataResponse> callback, int limit) {
        
        return CompletableFuture.runAsync(() -> {
            try {
                MarketDataServiceGrpc.MarketDataServiceStub stub = getStubWithRetry();
                String server = loadBalancer.getBestServer();
                
                HistoricalTickDataRequest request = HistoricalTickDataRequest.newBuilder()
                    .setSymbol(symbol)
                    .setExchange(exchange)
                    .setStartTimestamp(startTimestamp)
                    .setEndTimestamp(endTimestamp)
                    .setLimit(limit)
                    .build();
                
                stub.getHistoricalTickData(request, new StreamObserver<TickDataResponse>() {
                    @Override
                    public void onNext(TickDataResponse response) {
                        try {
                            callback.accept(response);
                        } catch (Exception e) {
                            logger.log(Level.SEVERE, "Historical data callback error", e);
                        }
                    }
                    
                    @Override
                    public void onError(Throwable t) {
                        logger.log(Level.SEVERE, "Historical data error", t);
                        loadBalancer.reportError(server);
                    }
                    
                    @Override
                    public void onCompleted() {
                        logger.info("Historical data stream completed");
                    }
                });
                
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Failed to get historical data", e);
                throw new RuntimeException(e);
            }
        }, executorService);
    }
    
    /**
     * 健康检查
     */
    public boolean healthCheck() {
        try {
            MarketDataServiceGrpc.MarketDataServiceBlockingStub stub = getBlockingStubWithRetry();
            
            HealthCheckRequest request = HealthCheckRequest.newBuilder()
                .setService("MarketDataService")
                .build();
            
            HealthCheckResponse response = stub.healthCheck(request);
            return response.getStatus() == HealthCheckResponse.ServingStatus.SERVING;
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Health check failed", e);
            return false;
        }
    }
    
    /**
     * 关闭客户端
     */
    public void close() {
        executorService.shutdown();
        
        for (Map.Entry<String, ManagedChannel> entry : channels.entrySet()) {
            try {
                entry.getValue().shutdown().awaitTermination(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                logger.log(Level.WARNING, "Error closing channel for " + entry.getKey(), e);
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 使用示例
     */
    public static void main(String[] args) throws InterruptedException {
        // 服务器列表
        List<String> servers = Arrays.asList(
            "localhost:50051",
            "localhost:50052",
            "localhost:50053"
        );
        
        // 创建客户端
        FinancialDataClient client = new FinancialDataClient(servers, 3);
        
        try {
            // 健康检查
            if (client.healthCheck()) {
                logger.info("Service is healthy");
            }
            
            // 定义回调函数
            Consumer<TickDataResponse> tickCallback = response -> {
                for (TickData tick : response.getTicksList()) {
                    System.out.printf("Tick: %s @ %.2f vol:%d%n", 
                        tick.getSymbol(), tick.getLastPrice(), tick.getVolume());
                }
            };
            
            Consumer<KlineDataResponse> klineCallback = response -> {
                for (KlineData kline : response.getKlinesList()) {
                    System.out.printf("Kline: %s %s O:%.2f H:%.2f L:%.2f C:%.2f%n",
                        kline.getSymbol(), kline.getPeriod(), 
                        kline.getOpen(), kline.getHigh(), kline.getLow(), kline.getClose());
                }
            };
            
            Consumer<Level2DataResponse> level2Callback = response -> {
                for (Level2Data level2 : response.getLevel2DataList()) {
                    System.out.printf("Level2: %s bids:%d asks:%d%n",
                        level2.getSymbol(), level2.getBidsCount(), level2.getAsksCount());
                }
            };
            
            // 启动数据流
            CompletableFuture<Void> tickFuture = client.streamTickData(
                Arrays.asList("AAPL", "GOOGL", "MSFT"), "NASDAQ", tickCallback, 1000);
            
            CompletableFuture<Void> klineFuture = client.streamKlineData(
                Arrays.asList("AAPL"), "NASDAQ", "1m", klineCallback, 500);
            
            CompletableFuture<Void> level2Future = client.streamLevel2Data(
                Arrays.asList("AAPL"), "NASDAQ", 10, level2Callback, 200);
            
            CompletableFuture<Void> historicalFuture = client.getHistoricalTickData(
                "AAPL", "NASDAQ", 
                System.currentTimeMillis() - 3600000, // 1小时前
                System.currentTimeMillis(), // 现在
                tickCallback, 1000);
            
            // 等待30秒
            logger.info("Streaming data for 30 seconds...");
            Thread.sleep(30000);
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Client error", e);
        } finally {
            client.close();
            logger.info("Client closed");
        }
    }
}