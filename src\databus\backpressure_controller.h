#pragma once

#include <atomic>
#include <chrono>
#include <memory>
#include <thread>
#include <vector>
#include <functional>
#include <mutex>
#include <condition_variable>
#include <unordered_map>
#include "data_types.h"

namespace financial_data {
namespace databus {

/**
 * @brief 背压控制策略枚举
 */
enum class BackpressureStrategy {
    DROP_OLDEST,        // 丢弃最旧的数据
    DROP_NEWEST,        // 丢弃最新的数据
    BLOCK_PRODUCER,     // 阻塞生产者
    THROTTLE_PRODUCER,  // 限流生产者
    ADAPTIVE            // 自适应策略
};

/**
 * @brief 背压控制配置
 */
struct BackpressureConfig {
    // 队列阈值配置
    size_t queue_high_watermark = 80;      // 高水位线（百分比）
    size_t queue_low_watermark = 60;       // 低水位线（百分比）
    size_t queue_critical_watermark = 95;  // 临界水位线（百分比）
    
    // 内存阈值配置
    size_t memory_high_watermark_mb = 1024;    // 高内存水位线（MB）
    size_t memory_critical_watermark_mb = 2048; // 临界内存水位线（MB）
    
    // 延迟阈值配置
    uint64_t latency_threshold_us = 1000;      // 延迟阈值（微秒）
    uint64_t latency_critical_us = 5000;       // 临界延迟阈值（微秒）
    
    // 吞吐量配置
    uint64_t throughput_threshold_per_sec = 500000;  // 吞吐量阈值（条/秒）
    
    // 控制策略
    BackpressureStrategy strategy = BackpressureStrategy::ADAPTIVE;
    
    // 监控间隔
    uint32_t monitor_interval_ms = 100;        // 监控间隔（毫秒）
    
    // 恢复参数
    uint32_t recovery_time_ms = 5000;          // 恢复时间（毫秒）
    double recovery_factor = 0.8;              // 恢复因子
    
    // 限流参数
    uint32_t throttle_sleep_us = 100;          // 限流休眠时间（微秒）
    double throttle_factor = 0.5;              // 限流因子
};

/**
 * @brief 背压状态枚举
 */
enum class BackpressureState {
    NORMAL,         // 正常状态
    WARNING,        // 警告状态
    CRITICAL,       // 临界状态
    EMERGENCY       // 紧急状态
};

/**
 * @brief 背压统计信息
 */
struct BackpressureStatistics {
    std::atomic<uint64_t> total_messages{0};
    std::atomic<uint64_t> dropped_messages{0};
    std::atomic<uint64_t> throttled_messages{0};
    std::atomic<uint64_t> blocked_operations{0};
    
    std::atomic<uint64_t> queue_overflow_count{0};
    std::atomic<uint64_t> memory_overflow_count{0};
    std::atomic<uint64_t> latency_overflow_count{0};
    
    std::atomic<uint64_t> avg_queue_size{0};
    std::atomic<uint64_t> max_queue_size{0};
    std::atomic<uint64_t> avg_latency_us{0};
    std::atomic<uint64_t> max_latency_us{0};
    
    std::atomic<uint64_t> memory_usage_mb{0};
    std::atomic<uint64_t> peak_memory_usage_mb{0};
    
    BackpressureState current_state{BackpressureState::NORMAL};
    std::chrono::steady_clock::time_point last_state_change;
    
    void Reset() {
        total_messages = 0;
        dropped_messages = 0;
        throttled_messages = 0;
        blocked_operations = 0;
        queue_overflow_count = 0;
        memory_overflow_count = 0;
        latency_overflow_count = 0;
        avg_queue_size = 0;
        max_queue_size = 0;
        avg_latency_us = 0;
        max_latency_us = 0;
        memory_usage_mb = 0;
        peak_memory_usage_mb = 0;
        current_state = BackpressureState::NORMAL;
        last_state_change = std::chrono::steady_clock::now();
    }
    
    double GetDropRate() const {
        uint64_t total = total_messages.load();
        return total > 0 ? static_cast<double>(dropped_messages.load()) / total : 0.0;
    }
    
    double GetThrottleRate() const {
        uint64_t total = total_messages.load();
        return total > 0 ? static_cast<double>(throttled_messages.load()) / total : 0.0;
    }
};

/**
 * @brief 队列监控接口
 */
class QueueMonitor {
public:
    virtual ~QueueMonitor() = default;
    virtual size_t GetQueueSize() const = 0;
    virtual size_t GetQueueCapacity() const = 0;
    virtual bool IsEmpty() const = 0;
    virtual bool IsFull() const = 0;
    virtual void Clear() = 0;
};

/**
 * @brief 背压控制器
 * 
 * 监控系统资源使用情况，在资源紧张时采取相应的背压控制策略，
 * 防止数据积压和内存溢出
 */
class BackpressureController {
private:
    BackpressureConfig config_;
    BackpressureStatistics statistics_;
    
    // 监控的队列
    std::vector<std::shared_ptr<QueueMonitor>> monitored_queues_;
    std::mutex queues_mutex_;
    
    // 控制状态
    std::atomic<BackpressureState> current_state_{BackpressureState::NORMAL};
    std::atomic<bool> throttling_enabled_{false};
    std::atomic<bool> blocking_enabled_{false};
    
    // 监控线程
    std::thread monitor_thread_;
    std::atomic<bool> running_{false};
    
    // 回调函数
    std::function<void(BackpressureState, BackpressureState)> state_change_callback_;
    std::function<void(const BackpressureStatistics&)> statistics_callback_;
    
    // 自适应参数
    std::atomic<double> adaptive_threshold_factor_{1.0};
    std::chrono::steady_clock::time_point last_adjustment_time_;
    
    // 性能计数器
    std::atomic<uint64_t> last_message_count_{0};
    std::chrono::steady_clock::time_point last_throughput_check_;

public:
    explicit BackpressureController(const BackpressureConfig& config = BackpressureConfig{});
    ~BackpressureController();

    /**
     * @brief 启动背压控制器
     */
    bool Start();

    /**
     * @brief 停止背压控制器
     */
    void Stop();

    /**
     * @brief 添加监控队列
     */
    void AddMonitoredQueue(std::shared_ptr<QueueMonitor> queue);

    /**
     * @brief 移除监控队列
     */
    void RemoveMonitoredQueue(std::shared_ptr<QueueMonitor> queue);

    /**
     * @brief 检查是否应该丢弃消息
     */
    bool ShouldDropMessage();

    /**
     * @brief 检查是否应该限流
     */
    bool ShouldThrottle();

    /**
     * @brief 检查是否应该阻塞
     */
    bool ShouldBlock();

    /**
     * @brief 等待背压缓解
     */
    bool WaitForBackpressureRelief(uint32_t timeout_ms = 1000);

    /**
     * @brief 记录消息处理
     */
    void RecordMessage(uint64_t processing_latency_us = 0);

    /**
     * @brief 记录消息丢弃
     */
    void RecordDroppedMessage();

    /**
     * @brief 记录限流操作
     */
    void RecordThrottledMessage();

    /**
     * @brief 记录阻塞操作
     */
    void RecordBlockedOperation();

    /**
     * @brief 获取当前背压状态
     */
    BackpressureState GetCurrentState() const {
        return current_state_.load();
    }

    /**
     * @brief 获取统计信息
     */
    BackpressureStatistics GetStatistics() const;

    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

    /**
     * @brief 设置状态变化回调
     */
    void SetStateChangeCallback(std::function<void(BackpressureState, BackpressureState)> callback) {
        state_change_callback_ = std::move(callback);
    }

    /**
     * @brief 设置统计信息回调
     */
    void SetStatisticsCallback(std::function<void(const BackpressureStatistics&)> callback) {
        statistics_callback_ = std::move(callback);
    }

    /**
     * @brief 更新配置
     */
    void UpdateConfig(const BackpressureConfig& config);

    /**
     * @brief 获取配置
     */
    BackpressureConfig GetConfig() const {
        return config_;
    }

    /**
     * @brief 手动触发背压控制
     */
    void TriggerBackpressure(BackpressureState state);

    /**
     * @brief 手动恢复正常状态
     */
    void RecoverFromBackpressure();

private:
    /**
     * @brief 监控循环
     */
    void MonitorLoop();

    /**
     * @brief 评估系统状态
     */
    BackpressureState EvaluateSystemState();

    /**
     * @brief 检查队列状态
     */
    bool CheckQueuePressure(size_t& avg_queue_usage, size_t& max_queue_size);

    /**
     * @brief 检查内存压力
     */
    bool CheckMemoryPressure();

    /**
     * @brief 检查延迟压力
     */
    bool CheckLatencyPressure();

    /**
     * @brief 检查吞吐量压力
     */
    bool CheckThroughputPressure();

    /**
     * @brief 更新状态
     */
    void UpdateState(BackpressureState new_state);

    /**
     * @brief 应用背压策略
     */
    void ApplyBackpressureStrategy(BackpressureState state);

    /**
     * @brief 自适应调整
     */
    void AdaptiveAdjustment();

    /**
     * @brief 获取系统内存使用量
     */
    size_t GetSystemMemoryUsage() const;

    /**
     * @brief 计算队列使用率
     */
    double CalculateQueueUsage(size_t queue_size, size_t queue_capacity) const;

    /**
     * @brief 应用限流
     */
    void ApplyThrottling();

    /**
     * @brief 清理过期数据
     */
    void CleanupExpiredData();
};

/**
 * @brief 队列监控器实现
 */
template<typename QueueType>
class QueueMonitorImpl : public QueueMonitor {
private:
    std::shared_ptr<QueueType> queue_;

public:
    explicit QueueMonitorImpl(std::shared_ptr<QueueType> queue) 
        : queue_(std::move(queue)) {}

    size_t GetQueueSize() const override {
        return queue_ ? queue_->Size() : 0;
    }

    size_t GetQueueCapacity() const override {
        return queue_ ? QueueType::Capacity() : 0;
    }

    bool IsEmpty() const override {
        return queue_ ? queue_->IsEmpty() : true;
    }

    bool IsFull() const override {
        return queue_ ? queue_->IsFull() : false;
    }

    void Clear() override {
        if (queue_) {
            queue_->Clear();
        }
    }
};

/**
 * @brief 背压控制器工厂
 */
class BackpressureControllerFactory {
public:
    /**
     * @brief 创建默认背压控制器
     */
    static std::unique_ptr<BackpressureController> CreateDefault();

    /**
     * @brief 创建高性能背压控制器
     */
    static std::unique_ptr<BackpressureController> CreateHighPerformance();

    /**
     * @brief 创建低延迟背压控制器
     */
    static std::unique_ptr<BackpressureController> CreateLowLatency();

    /**
     * @brief 创建内存敏感背压控制器
     */
    static std::unique_ptr<BackpressureController> CreateMemorySensitive();
};

} // namespace databus
} // namespace financial_data