# 金融数据服务系统设计文档

## 概述

本设计文档详细描述了金融数据服务系统的技术架构、组件设计和实现方案。系统采用微服务架构，支持微秒级延迟的实时行情数据采集、存储和分发，满足量化投资和高频交易的严格性能要求。

## 架构设计

### 总体架构

系统采用分层架构设计，从下到上分为：数据源层、数据采集层、数据处理层、存储层、接口层和应用层。

```mermaid
graph TB
    subgraph "应用层"
        A1[策略客户端]
        A2[回测系统]
        A3[风控系统]
        A4[Web管理界面]
    end
    
    subgraph "接口层"
        I1[WebSocket网关]
        I2[REST API网关]
        I3[gRPC服务]
        I4[TCP/UDP服务]
    end
    
    subgraph "数据处理层"
        P1[实时数据总线]
        P2[数据标准化引擎]
        P3[数据校验引擎]
        P4[订阅管理器]
    end
    
    subgraph "存储层"
        S1[Redis热数据]
        S2[ClickHouse温数据]
        S3[MinIO冷数据]
        S4[元数据存储]
    end
    
    subgraph "数据采集层"
        C1[CTP采集器]
        C2[股票采集器]
        C3[期权采集器]
        C4[外汇采集器]
    end
    
    subgraph "数据源层"
        D1[上期所SHFE]
        D2[大商所DCE]
        D3[郑商所CZCE]
        D4[中金所CFFEX]
        D5[上交所SSE]
        D6[深交所SZSE]
    end
    
    A1 --> I1
    A2 --> I2
    A3 --> I3
    A4 --> I2
    
    I1 --> P1
    I2 --> P1
    I3 --> P1
    I4 --> P1
    
    P1 --> S1
    P2 --> S2
    P3 --> S3
    P4 --> S4
    
    C1 --> P1
    C2 --> P2
    C3 --> P3
    C4 --> P4
    
    D1 --> C1
    D2 --> C1
    D3 --> C1
    D4 --> C1
    D5 --> C2
    D6 --> C2
```

### 核心组件架构

#### 1. 数据采集层架构

采用多适配器模式，支持不同交易所的数据接入：

```mermaid
graph LR
    subgraph "采集适配器"
        CA1[CTP适配器]
        CA2[恒生O32适配器]
        CA3[金仕达适配器]
        CA4[WebSocket适配器]
    end
    
    subgraph "数据处理"
        DP1[协议解析器]
        DP2[数据标准化]
        DP3[完整性校验]
        DP4[时间戳同步]
    end
    
    subgraph "数据总线"
        DB1[共享内存队列]
        DB2[Kafka消息队列]
        DB3[Redis发布订阅]
    end
    
    CA1 --> DP1
    CA2 --> DP1
    CA3 --> DP1
    CA4 --> DP1
    
    DP1 --> DP2
    DP2 --> DP3
    DP3 --> DP4
    
    DP4 --> DB1
    DP4 --> DB2
    DP4 --> DB3
```

#### 2. 存储层架构

采用分层存储策略，根据数据访问频率和成本优化：

```mermaid
graph TB
    subgraph "热数据层 (7天)"
        H1[Redis Cluster]
        H2[TimescaleDB]
        H3[内存映射文件]
    end
    
    subgraph "温数据层 (2年)"
        W1[ClickHouse集群]
        W2[Parquet文件]
        W3[SSD存储]
    end
    
    subgraph "冷数据层 (永久)"
        C1[MinIO对象存储]
        C2[AWS S3]
        C3[磁带备份]
    end
    
    H1 --> W1
    H2 --> W1
    H3 --> W2
    
    W1 --> C1
    W2 --> C2
    W3 --> C3
```

## 组件和接口

### 1. 数据采集组件

#### CTP行情采集器
```cpp
class CTPMarketDataCollector {
private:
    CThostFtdcMdApi* md_api_;
    std::shared_ptr<DataBus> data_bus_;
    std::atomic<bool> connected_;
    
public:
    bool Initialize(const CTPConfig& config);
    bool Subscribe(const std::vector<std::string>& symbols);
    void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* data);
    void OnFrontDisconnected(int reason);
    void OnRspError(CThostFtdcRspInfoField* error_info);
};
```

#### 数据标准化引擎
```cpp
class DataNormalizer {
public:
    struct StandardTick {
        int64_t timestamp_ns;
        std::string symbol;
        std::string exchange;
        double last_price;
        int64_t volume;
        double turnover;
        PriceLevel bids[5];
        PriceLevel asks[5];
        uint32_t sequence;
    };
    
    StandardTick Normalize(const RawMarketData& raw_data);
    bool ValidateData(const StandardTick& tick);
};
```

### 2. 存储组件

#### Redis热数据存储
```python
class HotDataStorage:
    def __init__(self, redis_cluster_config):
        self.redis_cluster = RedisCluster(**redis_cluster_config)
        
    async def store_tick(self, tick_data):
        # 存储最新tick
        key = f"tick:{tick_data.symbol}:latest"
        await self.redis_cluster.hset(key, mapping=tick_data.to_dict())
        
        # 维护时间序列索引
        ts_key = f"ts:{tick_data.symbol}"
        await self.redis_cluster.zadd(ts_key, {tick_data.timestamp: tick_data.sequence})
        
    async def get_latest_tick(self, symbol):
        key = f"tick:{symbol}:latest"
        return await self.redis_cluster.hgetall(key)
```

#### ClickHouse温数据存储
```sql
-- 期货tick数据表设计
CREATE TABLE market_data.futures_tick (
    timestamp DateTime64(9) CODEC(Delta, ZSTD),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    last_price Float64 CODEC(Gorilla, ZSTD),
    volume UInt64 CODEC(Delta, ZSTD),
    turnover Float64 CODEC(Gorilla, ZSTD),
    open_interest UInt64 CODEC(Delta, ZSTD),
    bid_prices Array(Float64) CODEC(Gorilla, ZSTD),
    bid_volumes Array(UInt32) CODEC(Delta, ZSTD),
    ask_prices Array(Float64) CODEC(Gorilla, ZSTD),
    ask_volumes Array(UInt32) CODEC(Delta, ZSTD),
    sequence UInt32 CODEC(Delta, ZSTD),
    INDEX idx_symbol (symbol) TYPE bloom_filter(0.01),
    INDEX idx_timestamp (timestamp) TYPE minmax GRANULARITY 3
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR TO DISK 'cold_storage'
SETTINGS index_granularity = 8192;
```

### 3. 接口组件

#### WebSocket实时接口
```python
class WebSocketHandler:
    def __init__(self, data_bus):
        self.data_bus = data_bus
        self.subscriptions = {}
        
    async def handle_connection(self, websocket, path):
        client_id = str(uuid.uuid4())
        try:
            async for message in websocket:
                await self.handle_message(client_id, message, websocket)
        finally:
            await self.cleanup_client(client_id)
            
    async def handle_subscribe(self, client_id, symbols, websocket):
        for symbol in symbols:
            if symbol not in self.subscriptions:
                self.subscriptions[symbol] = set()
            self.subscriptions[symbol].add((client_id, websocket))
            
    async def broadcast_tick(self, tick_data):
        symbol = tick_data.symbol
        if symbol in self.subscriptions:
            message = json.dumps(tick_data.to_dict())
            for client_id, websocket in self.subscriptions[symbol]:
                try:
                    await websocket.send(message)
                except websockets.exceptions.ConnectionClosed:
                    await self.cleanup_client(client_id)
```

#### gRPC历史数据接口
```protobuf
syntax = "proto3";

service MarketDataService {
    rpc GetTickData(TickDataRequest) returns (stream TickDataResponse);
    rpc GetKlineData(KlineDataRequest) returns (stream KlineDataResponse);
    rpc GetLevel2Data(Level2DataRequest) returns (stream Level2DataResponse);
}

message TickDataRequest {
    string symbol = 1;
    int64 start_timestamp = 2;
    int64 end_timestamp = 3;
    int32 limit = 4;
}

message TickDataResponse {
    repeated TickData ticks = 1;
    bool has_more = 2;
    string next_cursor = 3;
}

message TickData {
    int64 timestamp = 1;
    string symbol = 2;
    string exchange = 3;
    double last_price = 4;
    int64 volume = 5;
    double turnover = 6;
    repeated PriceLevel bids = 7;
    repeated PriceLevel asks = 8;
}
```

## 数据模型

### 标准化数据格式

#### Tick数据模型
```json
{
  "timestamp": 1721446200123456789,
  "symbol": "CU2409",
  "exchange": "SHFE",
  "last_price": 78560.0,
  "volume": 12580,
  "turnover": 9876543210.0,
  "open_interest": 156789,
  "bids": [
    {"price": 78550.0, "volume": 10, "order_count": 5},
    {"price": 78540.0, "volume": 25, "order_count": 8}
  ],
  "asks": [
    {"price": 78570.0, "volume": 8, "order_count": 3},
    {"price": 78580.0, "volume": 15, "order_count": 7}
  ],
  "sequence": 123456,
  "trade_flag": "buy_open"
}
```

#### K线数据模型
```json
{
  "symbol": "CU2409",
  "exchange": "SHFE",
  "period": "1m",
  "timestamp": 1721446200000000000,
  "open": 78550.0,
  "high": 78580.0,
  "low": 78540.0,
  "close": 78560.0,
  "volume": 1258,
  "turnover": 98765432.0,
  "open_interest": 156789
}
```

### 数据库Schema设计

#### 元数据表
```sql
-- 合约信息表
CREATE TABLE metadata.instruments (
    symbol String,
    exchange String,
    product_type Enum8('futures'=1, 'stock'=2, 'option'=3, 'forex'=4),
    underlying String,
    expiry_date Date,
    contract_size Float64,
    tick_size Float64,
    created_at DateTime,
    updated_at DateTime
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY (exchange, symbol);

-- 交易日历表
CREATE TABLE metadata.trading_calendar (
    exchange String,
    trading_date Date,
    is_trading_day UInt8,
    session_start DateTime,
    session_end DateTime,
    break_start DateTime,
    break_end DateTime
) ENGINE = MergeTree()
ORDER BY (exchange, trading_date);
```

## 错误处理

### 错误分类和处理策略

#### 1. 网络连接错误
```cpp
class ConnectionManager {
private:
    std::atomic<int> retry_count_{0};
    std::chrono::seconds retry_interval_{5};
    
public:
    void HandleDisconnection() {
        retry_count_++;
        if (retry_count_ <= MAX_RETRY_COUNT) {
            std::this_thread::sleep_for(retry_interval_);
            Reconnect();
        } else {
            NotifyFailover();
        }
    }
    
    void NotifyFailover() {
        // 切换到备用数据源
        failover_manager_->SwitchToBackup();
        // 发送告警
        alert_manager_->SendAlert(AlertLevel::CRITICAL, "Primary connection failed");
    }
};
```

#### 2. 数据质量错误
```python
class DataQualityChecker:
    def __init__(self):
        self.price_deviation_threshold = 0.1  # 10%
        self.volume_spike_threshold = 10.0    # 10倍
        
    def validate_tick(self, current_tick, previous_tick):
        errors = []
        
        # 价格异常检测
        if previous_tick:
            price_change = abs(current_tick.last_price - previous_tick.last_price) / previous_tick.last_price
            if price_change > self.price_deviation_threshold:
                errors.append(f"Price deviation too large: {price_change:.2%}")
        
        # 成交量异常检测
        if previous_tick:
            volume_ratio = current_tick.volume / max(previous_tick.volume, 1)
            if volume_ratio > self.volume_spike_threshold:
                errors.append(f"Volume spike detected: {volume_ratio:.1f}x")
        
        # 时间戳检查
        if current_tick.timestamp <= previous_tick.timestamp:
            errors.append("Timestamp not increasing")
            
        return errors
```

#### 3. 存储错误处理
```python
class StorageErrorHandler:
    async def handle_write_error(self, data, error):
        if isinstance(error, RedisConnectionError):
            # Redis连接错误，写入本地缓存
            await self.write_to_local_cache(data)
            await self.schedule_retry(data)
        elif isinstance(error, ClickHouseError):
            # ClickHouse错误，写入备用存储
            await self.write_to_backup_storage(data)
        else:
            # 其他错误，记录日志并告警
            logger.error(f"Storage error: {error}")
            await self.send_alert(error)
```

## 测试策略

### 1. 性能测试

#### 延迟测试
```python
class LatencyTest:
    def __init__(self):
        self.latency_samples = []
        
    async def test_end_to_end_latency(self):
        """测试从数据源到客户端的端到端延迟"""
        start_time = time.time_ns()
        
        # 模拟发送数据
        test_data = self.generate_test_tick()
        await self.data_bus.publish(test_data)
        
        # 等待客户端接收
        received_data = await self.client.receive()
        end_time = time.time_ns()
        
        latency = (end_time - start_time) / 1000  # 转换为微秒
        self.latency_samples.append(latency)
        
        assert latency < 50, f"Latency {latency}μs exceeds 50μs threshold"
        
    def test_throughput(self):
        """测试系统吞吐量"""
        start_time = time.time()
        message_count = 1000000
        
        for i in range(message_count):
            test_data = self.generate_test_tick()
            self.data_bus.publish_sync(test_data)
            
        end_time = time.time()
        throughput = message_count / (end_time - start_time)
        
        assert throughput >= 1000000, f"Throughput {throughput} msg/s below requirement"
```

#### 压力测试
```python
class StressTest:
    async def test_concurrent_clients(self):
        """测试1000个并发客户端"""
        clients = []
        for i in range(1000):
            client = WebSocketClient(f"client_{i}")
            clients.append(client)
            
        # 并发连接
        await asyncio.gather(*[client.connect() for client in clients])
        
        # 并发订阅
        await asyncio.gather(*[client.subscribe(["CU2409", "AL2409"]) for client in clients])
        
        # 发送测试数据
        test_data = self.generate_test_data(10000)
        start_time = time.time()
        
        for data in test_data:
            await self.data_bus.publish(data)
            
        # 验证所有客户端都收到数据
        for client in clients:
            received_count = await client.get_received_count()
            assert received_count == len(test_data)
```

### 2. 数据完整性测试

```python
class DataIntegrityTest:
    def test_no_data_loss(self):
        """测试数据零丢失"""
        # 生成连续序列号的测试数据
        test_data = [self.generate_tick_with_sequence(i) for i in range(10000)]
        
        # 发送数据
        for data in test_data:
            self.data_bus.publish(data)
            
        # 等待处理完成
        time.sleep(5)
        
        # 验证存储中的数据完整性
        stored_data = self.storage.query_by_sequence_range(0, 9999)
        assert len(stored_data) == 10000
        
        # 验证序列号连续性
        sequences = [data.sequence for data in stored_data]
        sequences.sort()
        for i, seq in enumerate(sequences):
            assert seq == i, f"Missing sequence number: {i}"
```

### 3. 故障恢复测试

```python
class FailoverTest:
    async def test_primary_failure_recovery(self):
        """测试主服务器故障恢复"""
        # 启动主备服务器
        primary = MarketDataServer("primary")
        backup = MarketDataServer("backup")
        
        await primary.start()
        await backup.start()
        
        # 客户端连接到主服务器
        client = WebSocketClient()
        await client.connect(primary.address)
        
        # 模拟主服务器故障
        await primary.simulate_failure()
        
        # 验证故障转移时间
        start_time = time.time()
        await client.wait_for_reconnection()
        failover_time = time.time() - start_time
        
        assert failover_time < 5.0, f"Failover time {failover_time}s exceeds 5s threshold"
        
        # 验证数据连续性
        data_gap = await self.check_data_gap()
        assert data_gap == 0, f"Data gap detected: {data_gap} ticks"
```

通过以上设计，系统能够满足所有需求中的性能指标和功能要求，提供高可用、低延迟、高吞吐的金融数据服务。