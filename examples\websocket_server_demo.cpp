/**
 * @file websocket_server_demo.cpp
 * @brief WebSocket服务器演示程序
 * 
 * 演示如何使用WebSocket服务器提供实时市场数据服务
 */

#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <random>

#include "../src/interfaces/websocket_server.h"
#include "../src/databus/data_bus.h"
#include "../src/proto/data_types.h"

using namespace financial_data;
using namespace financial_data::interfaces;
using namespace financial_data::databus;

// 全局变量用于优雅关闭
std::atomic<bool> running{true};
std::unique_ptr<WebSocketServer> g_server;
std::shared_ptr<DataBus> g_data_bus;

void SignalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在关闭服务器..." << std::endl;
    running = false;
    
    if (g_server) {
        g_server->Stop();
    }
    if (g_data_bus) {
        g_data_bus->Stop();
    }
}

/**
 * @brief 创建测试Tick数据
 */
StandardTick CreateTestTick(const std::string& symbol, double base_price) {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_real_distribution<> price_dis(-0.05, 0.05);  // ±5%价格波动
    static std::uniform_int_distribution<> volume_dis(100, 10000);   // 成交量范围
    static uint32_t sequence = 0;
    
    StandardTick tick;
    tick.SetCurrentTimestamp();
    tick.symbol = symbol;
    tick.exchange = "SHFE";
    tick.last_price = base_price * (1.0 + price_dis(gen));
    tick.volume = volume_dis(gen);
    tick.turnover = tick.last_price * tick.volume;
    tick.open_interest = 150000 + (sequence % 10000);
    tick.sequence = ++sequence;
    tick.trade_flag = (sequence % 2 == 0) ? "buy_open" : "sell_close";
    
    // 生成五档买卖盘数据
    for (int i = 0; i < 5; ++i) {
        double bid_price = tick.last_price - (i + 1) * 10;
        double ask_price = tick.last_price + (i + 1) * 10;
        
        tick.bids[i] = PriceLevel(bid_price, 100 + i * 20, i + 1);
        tick.asks[i] = PriceLevel(ask_price, 100 + i * 20, i + 1);
    }
    
    return tick;
}

/**
 * @brief 数据生成线程
 */
void DataGeneratorThread() {
    std::cout << "数据生成线程启动" << std::endl;
    
    // 定义测试合约和基础价格
    std::vector<std::pair<std::string, double>> contracts = {
        {"CU2409", 78560.0},   // 铜
        {"AL2409", 19850.0},   // 铝
        {"ZN2409", 25420.0},   // 锌
        {"PB2409", 16780.0},   // 铅
        {"NI2409", 128900.0},  // 镍
        {"SN2409", 218500.0},  // 锡
        {"AU2412", 568.50},    // 黄金
        {"AG2412", 7.85}       // 白银
    };
    
    while (running) {
        try {
            // 为每个合约生成数据
            for (const auto& [symbol, base_price] : contracts) {
                if (!running) break;
                
                auto tick = CreateTestTick(symbol, base_price);
                
                if (g_data_bus && !g_data_bus->PushTick(tick)) {
                    std::cerr << "推送数据失败: " << symbol << std::endl;
                }
            }
            
            // 控制数据生成频率（每秒约100条数据）
            std::this_thread::sleep_for(std::chrono::milliseconds(80));
            
        } catch (const std::exception& e) {
            std::cerr << "数据生成异常: " << e.what() << std::endl;
        }
    }
    
    std::cout << "数据生成线程停止" << std::endl;
}

/**
 * @brief 统计监控线程
 */
void StatisticsThread() {
    std::cout << "统计监控线程启动" << std::endl;
    
    while (running) {
        try {
            std::this_thread::sleep_for(std::chrono::seconds(10));
            
            if (!running) break;
            
            if (g_server) {
                auto ws_stats = g_server->GetStatistics();
                auto health = g_server->GetHealthStatus();
                
                std::cout << "\n=== WebSocket服务器统计 ===" << std::endl;
                std::cout << "活跃连接数: " << ws_stats.active_connections.load() << std::endl;
                std::cout << "总连接数: " << ws_stats.total_connections.load() << std::endl;
                std::cout << "发送消息数: " << ws_stats.total_messages_sent.load() << std::endl;
                std::cout << "接收消息数: " << ws_stats.total_messages_received.load() << std::endl;
                std::cout << "平均延迟: " << ws_stats.avg_message_latency_ns.load() / 1000 << " μs" << std::endl;
                std::cout << "最大延迟: " << ws_stats.max_message_latency_ns.load() / 1000 << " μs" << std::endl;
                std::cout << "每秒消息数: " << ws_stats.messages_per_second.load() << std::endl;
                
                if (ws_stats.compressed_messages.load() > 0) {
                    std::cout << "压缩消息数: " << ws_stats.compressed_messages.load() << std::endl;
                    std::cout << "压缩率: " << ws_stats.compression_ratio_percent.load() << "%" << std::endl;
                }
                
                std::cout << "健康状态: " << health.status_message << std::endl;
                std::cout << "连接使用率: " << (health.connection_usage * 100) << "%" << std::endl;
            }
            
            if (g_data_bus) {
                auto bus_stats = g_data_bus->GetStatistics();
                
                std::cout << "\n=== 数据总线统计 ===" << std::endl;
                std::cout << "接收消息数: " << bus_stats.total_messages_received.load() << std::endl;
                std::cout << "处理消息数: " << bus_stats.total_messages_processed.load() << std::endl;
                std::cout << "发送消息数: " << bus_stats.total_messages_sent.load() << std::endl;
                std::cout << "丢弃消息数: " << bus_stats.total_messages_dropped.load() << std::endl;
                std::cout << "活跃客户端数: " << bus_stats.active_clients.load() << std::endl;
                std::cout << "处理延迟: " << bus_stats.avg_processing_latency_ns.load() / 1000 << " μs" << std::endl;
                std::cout << "吞吐量: " << bus_stats.throughput_per_second.load() << " msg/s" << std::endl;
            }
            
            std::cout << "=========================" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "统计监控异常: " << e.what() << std::endl;
        }
    }
    
    std::cout << "统计监控线程停止" << std::endl;
}

int main(int argc, char* argv[]) {
    // 设置信号处理器
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    std::cout << "=== 金融数据WebSocket服务器演示 ===" << std::endl;
    
    try {
        // 解析命令行参数
        uint16_t port = 8080;
        if (argc > 1) {
            port = static_cast<uint16_t>(std::stoi(argv[1]));
        }
        
        std::cout << "启动端口: " << port << std::endl;
        
        // 创建数据总线
        std::cout << "初始化数据总线..." << std::endl;
        DataBusConfig bus_config;
        bus_config.enable_kafka = false;  // 演示时禁用Kafka
        bus_config.enable_monitoring = true;
        bus_config.enable_batching = true;
        bus_config.batch_size = 50;
        bus_config.batch_timeout_ms = 100;
        
        g_data_bus = std::make_shared<DataBus>(bus_config);
        if (!g_data_bus->Start()) {
            std::cerr << "数据总线启动失败" << std::endl;
            return 1;
        }
        std::cout << "数据总线启动成功" << std::endl;
        
        // 创建WebSocket服务器
        std::cout << "初始化WebSocket服务器..." << std::endl;
        WebSocketConfig ws_config;
        ws_config.port = port;
        ws_config.max_connections = 1000;
        ws_config.thread_pool_size = 4;
        
        // 启用压缩
        ws_config.enable_compression = true;
        ws_config.compression_algorithm = "deflate";
        ws_config.compression_threshold = 1024;
        
        // 启用批处理
        ws_config.enable_batching = true;
        ws_config.batch_size = 20;
        ws_config.batch_timeout_ms = 50;
        
        // 启用心跳
        ws_config.enable_heartbeat = true;
        ws_config.heartbeat_interval_ms = 30000;  // 30秒
        ws_config.heartbeat_timeout_ms = 60000;   // 60秒
        
        // 性能优化
        ws_config.enable_tcp_nodelay = true;
        ws_config.send_buffer_size = 128 * 1024;
        ws_config.receive_buffer_size = 64 * 1024;
        
        g_server = std::make_unique<WebSocketServer>(ws_config);
        g_server->SetDataBus(g_data_bus);
        
        if (!g_server->Initialize()) {
            std::cerr << "WebSocket服务器初始化失败" << std::endl;
            return 1;
        }
        std::cout << "WebSocket服务器初始化成功" << std::endl;
        
        // 启动数据生成线程
        std::thread data_thread(DataGeneratorThread);
        
        // 启动统计监控线程
        std::thread stats_thread(StatisticsThread);
        
        std::cout << "启动WebSocket服务器..." << std::endl;
        std::cout << "服务器地址: ws://localhost:" << port << std::endl;
        std::cout << "按 Ctrl+C 停止服务器" << std::endl;
        std::cout << "\n客户端订阅示例:" << std::endl;
        std::cout << R"({
  "type": 0,
  "payload": {
    "symbols": ["CU2409", "AL2409"],
    "exchanges": ["SHFE"],
    "data_types": ["tick"]
  }
})" << std::endl;
        
        // 在后台线程启动服务器
        std::thread server_thread([&]() {
            g_server->Start();
        });
        
        // 等待停止信号
        while (running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        std::cout << "正在停止服务..." << std::endl;
        
        // 等待线程结束
        if (data_thread.joinable()) {
            data_thread.join();
        }
        
        if (stats_thread.joinable()) {
            stats_thread.join();
        }
        
        if (server_thread.joinable()) {
            server_thread.join();
        }
        
        std::cout << "服务器已停止" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}