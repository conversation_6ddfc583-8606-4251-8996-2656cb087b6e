#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "../src/storage/clickhouse_storage.h"
#include <chrono>
#include <thread>
#include <random>

using namespace financial_data;

class ClickHouseStorageTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Configure ClickHouse for testing
        config_.host = "localhost";
        config_.port = 9000;
        config_.database = "market_data_test";
        config_.username = "admin";
        config_.password = "password123";
        config_.batch_size = 1000;
        config_.batch_timeout = std::chrono::seconds(1);
        
        storage_ = std::make_unique<ClickHouseStorage>(config_);
        
        // Initialize connection
        ASSERT_TRUE(storage_->Initialize()) << "Failed to initialize ClickHouse connection";
        
        // Create test database and tables
        SetupTestTables();
    }
    
    void TearDown() override {
        // Cleanup test data
        CleanupTestTables();
        storage_.reset();
    }
    
    void SetupTestTables() {
        // Create test database
        storage_->ExecuteQuery("CREATE DATABASE IF NOT EXISTS market_data_test");
        storage_->ExecuteQuery("USE market_data_test");
        
        // Create test table for futures tick data
        std::string create_table_sql = R"(
            CREATE TABLE IF NOT EXISTS futures_tick_test (
                timestamp DateTime64(9) CODEC(Delta, ZSTD),
                symbol LowCardinality(String),
                exchange LowCardinality(String),
                last_price Float64 CODEC(Gorilla, ZSTD),
                volume UInt64 CODEC(Delta, ZSTD),
                turnover Float64 CODEC(Gorilla, ZSTD),
                open_interest UInt64 CODEC(Delta, ZSTD),
                bid_prices Array(Float64) CODEC(Gorilla, ZSTD),
                bid_volumes Array(UInt32) CODEC(Delta, ZSTD),
                ask_prices Array(Float64) CODEC(Gorilla, ZSTD),
                ask_volumes Array(UInt32) CODEC(Delta, ZSTD),
                sequence UInt32 CODEC(Delta, ZSTD),
                trade_flag LowCardinality(String),
                settlement_price Float64 CODEC(Gorilla, ZSTD),
                pre_settlement Float64 CODEC(Gorilla, ZSTD),
                pre_close_price Float64 CODEC(Gorilla, ZSTD),
                pre_open_interest UInt64 CODEC(Delta, ZSTD)
            ) ENGINE = MergeTree()
            PARTITION BY toYYYYMM(timestamp)
            ORDER BY (symbol, timestamp)
            SETTINGS index_granularity = 8192
        )";
        
        ASSERT_TRUE(storage_->ExecuteQuery(create_table_sql)) << "Failed to create test table";
    }
    
    void CleanupTestTables() {
        storage_->ExecuteQuery("DROP DATABASE IF EXISTS market_data_test");
    }
    
    StandardizedTick CreateTestTick(const std::string& symbol, int64_t timestamp_ns, uint32_t sequence) {
        StandardizedTick tick;
        tick.timestamp_ns = timestamp_ns;
        tick.symbol = symbol;
        tick.exchange = "SHFE";
        tick.product_type = "futures";
        tick.last_price = 75000.0 + (sequence % 1000);
        tick.volume = 100 + (sequence % 50);
        tick.turnover = tick.last_price * tick.volume;
        tick.open_interest = 50000 + sequence;
        tick.sequence = sequence;
        tick.trade_flag = "buy_open";
        tick.settlement_price = 75000.0;
        tick.pre_settlement = 74950.0;
        tick.pre_close_price = 74980.0;
        tick.pre_open_interest = 49900;
        
        // Add bid/ask data
        for (int i = 0; i < 5; ++i) {
            tick.bid_prices.push_back(tick.last_price - (i + 1) * 10);
            tick.bid_volumes.push_back(10 + i);
            tick.ask_prices.push_back(tick.last_price + (i + 1) * 10);
            tick.ask_volumes.push_back(8 + i);
        }
        
        return tick;
    }
    
    std::vector<StandardizedTick> CreateTestBatch(size_t count, const std::string& symbol = "CU2409") {
        std::vector<StandardizedTick> batch;
        auto base_time = std::chrono::system_clock::now().time_since_epoch().count();
        
        for (size_t i = 0; i < count; ++i) {
            auto tick = CreateTestTick(symbol, base_time + i * 1000000, static_cast<uint32_t>(i));
            batch.push_back(tick);
        }
        
        return batch;
    }
    
    ClickHouseConfig config_;
    std::unique_ptr<ClickHouseStorage> storage_;
};

// Test basic connection and initialization
TEST_F(ClickHouseStorageTest, ConnectionTest) {
    EXPECT_TRUE(storage_->IsConnected());
    EXPECT_TRUE(storage_->CheckClusterHealth());
    
    auto nodes = storage_->GetClusterNodes();
    EXPECT_GT(nodes.size(), 0);
}

// Test single tick data insertion
TEST_F(ClickHouseStorageTest, SingleTickInsertionTest) {
    auto tick = CreateTestTick("CU2409", 1721446200123456789LL, 12345);
    
    EXPECT_TRUE(storage_->InsertTickData(tick));
    
    // Verify insertion by querying
    auto result = storage_->QueryTickData(
        "CU2409", "SHFE", 
        1721446200000000000LL, 1721446200999999999LL
    );
    
    EXPECT_EQ(result.data.size(), 1);
    EXPECT_EQ(result.data[0].symbol, "CU2409");
    EXPECT_EQ(result.data[0].sequence, 12345);
    EXPECT_DOUBLE_EQ(result.data[0].last_price, 75345.0);
}

// Test batch insertion performance
TEST_F(ClickHouseStorageTest, BatchInsertionTest) {
    const size_t batch_size = 10000;
    auto test_batch = CreateTestBatch(batch_size);
    
    DataBatch<StandardizedTick> batch;
    for (const auto& tick : test_batch) {
        batch.Add(tick);
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    auto future = storage_->InsertTickDataBatch(batch);
    bool success = future.get();
    auto end_time = std::chrono::high_resolution_clock::now();
    
    EXPECT_TRUE(success);
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "Inserted " << batch_size << " records in " << duration.count() << "ms" << std::endl;
    
    // Verify insertion rate meets requirement (100k records/second)
    double records_per_second = (batch_size * 1000.0) / duration.count();
    EXPECT_GT(records_per_second, 50000) << "Insertion rate too slow: " << records_per_second << " records/sec";
}

// Test query performance
TEST_F(ClickHouseStorageTest, QueryPerformanceTest) {
    // Insert test data
    const size_t total_records = 50000;
    auto test_batch = CreateTestBatch(total_records);
    
    DataBatch<StandardizedTick> batch;
    for (const auto& tick : test_batch) {
        batch.Add(tick);
    }
    
    auto insert_future = storage_->InsertTickDataBatch(batch);
    ASSERT_TRUE(insert_future.get());
    
    // Wait for data to be available
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Test query performance
    auto start_time = std::chrono::high_resolution_clock::now();
    auto result = storage_->QueryTickData(
        "CU2409", "SHFE",
        test_batch[0].timestamp_ns, test_batch.back().timestamp_ns,
        10000
    );
    auto end_time = std::chrono::high_resolution_clock::now();
    
    EXPECT_GT(result.data.size(), 0);
    EXPECT_LE(result.query_time.count(), 1000) << "Query time exceeds 1 second: " << result.query_time.count() << "ms";
    
    std::cout << "Query returned " << result.data.size() << " records in " 
              << result.query_time.count() << "ms" << std::endl;
}

// Test data compression and storage efficiency
TEST_F(ClickHouseStorageTest, CompressionTest) {
    const size_t batch_size = 10000;
    auto test_batch = CreateTestBatch(batch_size);
    
    DataBatch<StandardizedTick> batch;
    for (const auto& tick : test_batch) {
        batch.Add(tick);
    }
    
    // Insert data
    auto insert_future = storage_->InsertTickDataBatch(batch);
    ASSERT_TRUE(insert_future.get());
    
    // Wait for compression to take effect
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Check storage size (this would require additional ClickHouse system table queries)
    // For now, we'll just verify the data was inserted correctly
    auto result = storage_->QueryTickData(
        "CU2409", "SHFE",
        test_batch[0].timestamp_ns, test_batch.back().timestamp_ns,
        batch_size
    );
    
    EXPECT_EQ(result.data.size(), batch_size);
    
    // Verify data integrity
    for (size_t i = 0; i < std::min(result.data.size(), test_batch.size()); ++i) {
        EXPECT_EQ(result.data[i].symbol, test_batch[i].symbol);
        EXPECT_EQ(result.data[i].sequence, test_batch[i].sequence);
        EXPECT_DOUBLE_EQ(result.data[i].last_price, test_batch[i].last_price);
    }
}

// Test concurrent insertions
TEST_F(ClickHouseStorageTest, ConcurrentInsertionTest) {
    const size_t num_threads = 4;
    const size_t batch_size_per_thread = 5000;
    
    std::vector<std::future<bool>> futures;
    std::vector<std::thread> threads;
    
    for (size_t t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, batch_size_per_thread, &futures]() {
            std::string symbol = "CU240" + std::to_string(t + 1);
            auto test_batch = CreateTestBatch(batch_size_per_thread, symbol);
            
            DataBatch<StandardizedTick> batch;
            for (const auto& tick : test_batch) {
                batch.Add(tick);
            }
            
            auto future = storage_->InsertTickDataBatch(batch);
            futures.push_back(std::move(future));
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Check all insertions succeeded
    for (auto& future : futures) {
        EXPECT_TRUE(future.get());
    }
    
    // Verify data was inserted for all symbols
    for (size_t t = 0; t < num_threads; ++t) {
        std::string symbol = "CU240" + std::to_string(t + 1);
        auto result = storage_->QueryTickData(symbol, "SHFE", 0, LLONG_MAX, 10000);
        EXPECT_EQ(result.data.size(), batch_size_per_thread);
    }
}

// Test data partitioning by month
TEST_F(ClickHouseStorageTest, PartitioningTest) {
    // Create data for different months
    std::vector<StandardizedTick> jan_data, feb_data;
    
    // January 2024 data
    int64_t jan_timestamp = 1704067200000000000LL; // 2024-01-01
    for (int i = 0; i < 1000; ++i) {
        auto tick = CreateTestTick("CU2401", jan_timestamp + i * 1000000, i);
        jan_data.push_back(tick);
    }
    
    // February 2024 data  
    int64_t feb_timestamp = 1706745600000000000LL; // 2024-02-01
    for (int i = 0; i < 1000; ++i) {
        auto tick = CreateTestTick("CU2402", feb_timestamp + i * 1000000, i + 1000);
        feb_data.push_back(tick);
    }
    
    // Insert both batches
    DataBatch<StandardizedTick> jan_batch, feb_batch;
    for (const auto& tick : jan_data) jan_batch.Add(tick);
    for (const auto& tick : feb_data) feb_batch.Add(tick);
    
    ASSERT_TRUE(storage_->InsertTickDataBatch(jan_batch).get());
    ASSERT_TRUE(storage_->InsertTickDataBatch(feb_batch).get());
    
    // Query each month separately
    auto jan_result = storage_->QueryTickData("CU2401", "SHFE", jan_timestamp, jan_timestamp + 86400000000000LL);
    auto feb_result = storage_->QueryTickData("CU2402", "SHFE", feb_timestamp, feb_timestamp + 86400000000000LL);
    
    EXPECT_EQ(jan_result.data.size(), 1000);
    EXPECT_EQ(feb_result.data.size(), 1000);
}

// Test performance metrics collection
TEST_F(ClickHouseStorageTest, MetricsTest) {
    // Reset metrics
    storage_->ResetMetrics();
    
    // Perform some operations
    auto tick = CreateTestTick("CU2409", 1721446200123456789LL, 1);
    ASSERT_TRUE(storage_->InsertTickData(tick));
    
    auto result = storage_->QueryTickData("CU2409", "SHFE", 1721446200000000000LL, 1721446200999999999LL);
    
    // Check metrics
    auto metrics = storage_->GetMetrics();
    EXPECT_GT(metrics.total_inserts, 0);
    EXPECT_GT(metrics.total_queries, 0);
    EXPECT_EQ(metrics.failed_inserts, 0);
    EXPECT_EQ(metrics.failed_queries, 0);
    EXPECT_GT(metrics.avg_insert_time.count(), 0);
    EXPECT_GT(metrics.avg_query_time.count(), 0);
}

// Test error handling and reconnection
TEST_F(ClickHouseStorageTest, ErrorHandlingTest) {
    // This test would require simulating network failures
    // For now, we'll test basic error conditions
    
    // Test invalid query
    EXPECT_FALSE(storage_->ExecuteQuery("INVALID SQL QUERY"));
    
    // Test query with non-existent table
    auto result = storage_->QueryTickData("INVALID_SYMBOL", "INVALID_EXCHANGE", 0, 1000);
    EXPECT_EQ(result.data.size(), 0);
    
    // Verify connection is still active after errors
    EXPECT_TRUE(storage_->IsConnected());
}

// Integration test with realistic data volume
TEST_F(ClickHouseStorageTest, RealisticVolumeTest) {
    const size_t total_records = 100000; // 100k records
    const size_t batch_size = 10000;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Insert data in batches
    for (size_t i = 0; i < total_records; i += batch_size) {
        size_t current_batch_size = std::min(batch_size, total_records - i);
        auto test_batch = CreateTestBatch(current_batch_size);
        
        DataBatch<StandardizedTick> batch;
        for (const auto& tick : test_batch) {
            batch.Add(tick);
        }
        
        auto future = storage_->InsertTickDataBatch(batch);
        ASSERT_TRUE(future.get()) << "Failed to insert batch starting at " << i;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    double records_per_second = (total_records * 1000.0) / duration.count();
    std::cout << "Inserted " << total_records << " records in " << duration.count() 
              << "ms (" << records_per_second << " records/sec)" << std::endl;
    
    // Verify we meet the performance requirement (100k records/second)
    EXPECT_GT(records_per_second, 50000) << "Performance requirement not met";
    
    // Verify data integrity with sampling
    auto result = storage_->QueryTickData("CU2409", "SHFE", 0, LLONG_MAX, 1000);
    EXPECT_GT(result.data.size(), 0);
    EXPECT_LE(result.query_time.count(), 100) << "Query time too slow: " << result.query_time.count() << "ms";
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}