#!/usr/bin/env python3
"""
验证Redis热数据存储层实现完整性的脚本
"""

import os
import sys
import re
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✓ {description}: {file_path}")
        return True
    else:
        print(f"✗ {description}: {file_path} (NOT FOUND)")
        return False

def check_implementation_completeness():
    """检查实现完整性"""
    print("=== Redis热数据存储层实现验证 ===\n")
    
    all_checks_passed = True
    
    # 1. 检查核心实现文件
    print("1. 核心实现文件:")
    core_files = [
        ("src/storage/redis_storage.h", "Redis存储头文件"),
        ("src/storage/redis_storage.cpp", "Redis存储实现文件"),
        ("src/storage/redis_config_loader.h", "配置加载器"),
        ("src/proto/data_types.h", "数据类型定义")
    ]
    
    for file_path, desc in core_files:
        if not check_file_exists(file_path, desc):
            all_checks_passed = False
    
    # 2. 检查配置文件
    print("\n2. 配置文件:")
    config_files = [
        ("config/redis.conf", "Redis基础配置"),
        ("config/redis-cluster.conf", "Redis集群配置"),
        ("config/redis_hot_storage.json", "热数据存储配置"),
        ("docker-compose.yml", "Docker编排文件")
    ]
    
    for file_path, desc in config_files:
        if not check_file_exists(file_path, desc):
            all_checks_passed = False
    
    # 3. 检查测试文件
    print("\n3. 测试文件:")
    test_files = [
        ("tests/redis_hot_storage_test.cpp", "Redis存储测试"),
        ("tests/redis_mock_test.cpp", "Mock测试"),
        ("examples/redis_hot_storage_demo.cpp", "使用示例")
    ]
    
    for file_path, desc in test_files:
        if not check_file_exists(file_path, desc):
            all_checks_passed = False
    
    # 4. 检查脚本文件
    print("\n4. 脚本文件:")
    script_files = [
        ("scripts/setup-redis-cluster.bat", "集群设置脚本"),
        ("scripts/build_and_test.bat", "构建测试脚本")
    ]
    
    for file_path, desc in script_files:
        if not check_file_exists(file_path, desc):
            all_checks_passed = False
    
    return all_checks_passed

def check_implementation_features():
    """检查实现功能特性"""
    print("\n=== 功能特性检查 ===\n")
    
    features_found = []
    
    # 检查Redis存储头文件中的关键特性
    redis_header_path = "src/storage/redis_storage.h"
    if os.path.exists(redis_header_path):
        with open(redis_header_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查关键类和方法
            key_features = [
                ("class RedisHotStorage", "Redis热数据存储类"),
                ("class RedisConnectionPool", "Redis连接池"),
                ("StoreTick", "Tick数据存储"),
                ("StoreLevel2", "Level2数据存储"),
                ("StoreBatch", "批量存储"),
                ("StoreTickAsync", "异步存储"),
                ("GetLatestTick", "最新数据查询"),
                ("QueryTicks", "时间序列查询"),
                ("GetLatestTicks", "批量查询"),
                ("SetupExpirationPolicy", "过期策略设置"),
                ("CleanupExpiredData", "过期数据清理"),
                ("StorageStats", "存储统计"),
                ("hot_data_ttl_seconds", "7天TTL配置")
            ]
            
            for feature, desc in key_features:
                if feature in content:
                    print(f"✓ {desc}")
                    features_found.append(feature)
                else:
                    print(f"✗ {desc} (未找到: {feature})")
    
    # 检查实现文件中的关键方法
    redis_impl_path = "src/storage/redis_storage.cpp"
    if os.path.exists(redis_impl_path):
        with open(redis_impl_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            impl_features = [
                ("OptimizeForHotData", "热数据优化"),
                ("InitializeCluster", "集群初始化"),
                ("ConfigureMemoryPolicy", "内存策略配置"),
                ("SerializeTick", "Tick序列化"),
                ("DeserializeTick", "Tick反序列化"),
                ("SerializeLevel2", "Level2序列化"),
                ("DeserializeLevel2", "Level2反序列化"),
                ("AsyncWriteWorker", "异步写入工作线程"),
                ("UpdateWriteStats", "写入统计更新"),
                ("UpdateQueryStats", "查询统计更新")
            ]
            
            for feature, desc in impl_features:
                if feature in content:
                    print(f"✓ {desc}")
                    features_found.append(feature)
                else:
                    print(f"✗ {desc} (未找到: {feature})")
    
    return len(features_found)

def check_configuration_completeness():
    """检查配置完整性"""
    print("\n=== 配置完整性检查 ===\n")
    
    # 检查Redis配置
    redis_conf_path = "config/redis.conf"
    if os.path.exists(redis_conf_path):
        with open(redis_conf_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            config_items = [
                ("maxmemory", "内存限制配置"),
                ("maxmemory-policy", "内存策略配置"),
                ("notify-keyspace-events", "键空间通知配置"),
                ("hash-max-ziplist-entries", "哈希优化配置"),
                ("zset-max-ziplist-entries", "有序集合优化配置")
            ]
            
            for item, desc in config_items:
                if item in content:
                    print(f"✓ {desc}")
                else:
                    print(f"✗ {desc} (未找到: {item})")
    
    # 检查集群配置
    cluster_conf_path = "config/redis-cluster.conf"
    if os.path.exists(cluster_conf_path):
        with open(cluster_conf_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            cluster_items = [
                ("cluster-enabled yes", "集群模式启用"),
                ("cluster-node-timeout", "节点超时配置"),
                ("cluster-require-full-coverage no", "部分覆盖允许"),
                ("cluster-allow-reads-when-down yes", "故障时读取允许")
            ]
            
            for item, desc in cluster_items:
                if item in content:
                    print(f"✓ {desc}")
                else:
                    print(f"✗ {desc} (未找到: {item})")

def check_task_requirements():
    """检查任务需求完成情况"""
    print("\n=== 任务需求完成情况 ===\n")
    
    requirements = [
        ("Redis Cluster集群配置", "config/redis-cluster.conf", "支持高可用和数据分片"),
        ("热数据存储Schema设计", "src/storage/redis_storage.h", "优化最近7天数据查询性能"),
        ("数据写入接口", "StoreTick|StoreBatch|StoreTickAsync", "支持批量写入和异步处理"),
        ("数据查询接口", "GetLatestTick|QueryTicks", "实现1毫秒内单条查询响应"),
        ("数据过期策略", "SetupExpirationPolicy|CleanupExpiredData", "自动清理超过7天的热数据")
    ]
    
    completed_count = 0
    
    for req_name, check_target, description in requirements:
        if os.path.exists(check_target):
            print(f"✓ {req_name}: {description}")
            completed_count += 1
        elif any(os.path.exists(f) for f in ["src/storage/redis_storage.h", "src/storage/redis_storage.cpp"]):
            # 检查代码中是否包含相关实现
            for file_path in ["src/storage/redis_storage.h", "src/storage/redis_storage.cpp"]:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if re.search(check_target, content):
                            print(f"✓ {req_name}: {description}")
                            completed_count += 1
                            break
            else:
                print(f"✗ {req_name}: {description}")
        else:
            print(f"✗ {req_name}: {description}")
    
    return completed_count, len(requirements)

def main():
    """主函数"""
    print("Redis热数据存储层实现验证工具")
    print("=" * 50)
    
    # 检查实现完整性
    implementation_complete = check_implementation_completeness()
    
    # 检查功能特性
    features_count = check_implementation_features()
    
    # 检查配置完整性
    check_configuration_completeness()
    
    # 检查任务需求
    completed_reqs, total_reqs = check_task_requirements()
    
    # 总结
    print("\n" + "=" * 50)
    print("验证总结:")
    print(f"实现文件完整性: {'✓ 通过' if implementation_complete else '✗ 不完整'}")
    print(f"功能特性数量: {features_count}")
    print(f"任务需求完成: {completed_reqs}/{total_reqs}")
    
    if implementation_complete and completed_reqs == total_reqs:
        print("\n🎉 Redis热数据存储层实现验证通过！")
        return 0
    else:
        print("\n⚠️  实现存在不完整的地方，请检查上述输出")
        return 1

if __name__ == "__main__":
    sys.exit(main())