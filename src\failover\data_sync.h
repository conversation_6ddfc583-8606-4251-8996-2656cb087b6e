#pragma once

#include <memory>
#include <string>
#include <vector>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <functional>
#include <queue>
#include <unordered_map>
#include <spdlog/spdlog.h>

namespace financial_data {

enum class SyncStatus {
    IDLE,
    SY<PERSON><PERSON>,
    SUCCESS,
    FAILED,
    TIMEOUT
};

struct SyncOperation {
    std::string operation_id;
    std::string source_node;
    std::string target_node;
    std::string data_type;
    std::vector<uint8_t> data;
    std::chrono::steady_clock::time_point timestamp;
    int retry_count;
    SyncStatus status;
    std::string error_message;
    
    SyncOperation() : retry_count(0), status(SyncStatus::IDLE) {}
};

struct SyncStats {
    int total_operations;
    int successful_operations;
    int failed_operations;
    int pending_operations;
    std::chrono::steady_clock::time_point last_sync_time;
    double average_sync_time_ms;
    int64_t total_bytes_synced;
    
    SyncStats() : total_operations(0), successful_operations(0), 
                 failed_operations(0), pending_operations(0), 
                 average_sync_time_ms(0.0), total_bytes_synced(0) {}
};

struct DataSyncConfig {
    int sync_interval_ms = 1000;           // 同步间隔
    int sync_timeout_ms = 10000;           // 同步超时
    int max_retry_attempts = 3;            // 最大重试次数
    int retry_delay_ms = 1000;             // 重试延迟
    int max_queue_size = 1000;             // 最大队列大小
    bool enable_compression = true;         // 启用压缩
    bool enable_encryption = false;         // 启用加密
    std::string sync_mode = "async";        // 同步模式: sync/async
    std::vector<std::string> peer_nodes;   // 对等节点列表
};

class DataSyncManager {
public:
    using DataProvider = std::function<std::vector<uint8_t>(const std::string& data_type)>;
    using DataConsumer = std::function<bool(const std::string& data_type, const std::vector<uint8_t>& data)>;
    using SyncCallback = std::function<void(const SyncOperation& operation)>;

    explicit DataSyncManager(const DataSyncConfig& config);
    ~DataSyncManager();

    // 启动数据同步管理器
    bool Start();
    
    // 停止数据同步管理器
    void Stop();
    
    // 注册数据提供者
    void RegisterDataProvider(const std::string& data_type, DataProvider provider);
    
    // 注册数据消费者
    void RegisterDataConsumer(const std::string& data_type, DataConsumer consumer);
    
    // 设置同步回调
    void SetSyncCallback(SyncCallback callback) { sync_callback_ = callback; }
    
    // 请求同步数据到指定节点
    bool RequestSync(const std::string& target_node, const std::string& data_type);
    
    // 请求同步数据到所有节点
    bool RequestSyncToAll(const std::string& data_type);
    
    // 手动触发全量同步
    bool TriggerFullSync();
    
    // 获取同步统计信息
    SyncStats GetSyncStats() const;
    
    // 获取待同步操作数量
    int GetPendingOperations() const;
    
    // 检查与指定节点的同步状态
    SyncStatus GetNodeSyncStatus(const std::string& node_id) const;
    
    // 清理失败的同步操作
    void CleanupFailedOperations();

private:
    // 同步工作线程
    void SyncWorkerThread();
    
    // 处理同步操作
    void ProcessSyncOperation(SyncOperation& operation);
    
    // 发送数据到目标节点
    bool SendDataToNode(const std::string& target_node, const SyncOperation& operation);
    
    // 接收来自其他节点的数据
    bool ReceiveDataFromNode(const std::string& source_node, const SyncOperation& operation);
    
    // 压缩数据
    std::vector<uint8_t> CompressData(const std::vector<uint8_t>& data);
    
    // 解压数据
    std::vector<uint8_t> DecompressData(const std::vector<uint8_t>& compressed_data);
    
    // 加密数据
    std::vector<uint8_t> EncryptData(const std::vector<uint8_t>& data);
    
    // 解密数据
    std::vector<uint8_t> DecryptData(const std::vector<uint8_t>& encrypted_data);
    
    // 生成操作ID
    std::string GenerateOperationId();
    
    // 更新统计信息
    void UpdateStats(const SyncOperation& operation, bool success, double duration_ms);

private:
    DataSyncConfig config_;
    std::shared_ptr<spdlog::logger> logger_;
    
    std::atomic<bool> running_;
    std::thread sync_worker_thread_;
    
    // 同步队列
    mutable std::mutex queue_mutex_;
    std::queue<SyncOperation> sync_queue_;
    
    // 数据提供者和消费者
    mutable std::mutex providers_mutex_;
    std::unordered_map<std::string, DataProvider> data_providers_;
    std::unordered_map<std::string, DataConsumer> data_consumers_;
    
    // 节点状态
    mutable std::mutex node_status_mutex_;
    std::unordered_map<std::string, SyncStatus> node_sync_status_;
    
    // 回调函数
    SyncCallback sync_callback_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    SyncStats stats_;
    
    // 操作计数器
    std::atomic<int> operation_counter_;
};

// 内置的数据同步实现
class MemoryDataSync {
public:
    static std::vector<uint8_t> SerializeTickData(const std::vector<uint8_t>& tick_data);
    static std::vector<uint8_t> DeserializeTickData(const std::vector<uint8_t>& serialized_data);
    
    static std::vector<uint8_t> SerializeLevel2Data(const std::vector<uint8_t>& level2_data);
    static std::vector<uint8_t> DeserializeLevel2Data(const std::vector<uint8_t>& serialized_data);
    
    static std::vector<uint8_t> SerializeConfiguration(const std::string& config_json);
    static std::string DeserializeConfiguration(const std::vector<uint8_t>& serialized_data);
};

} // namespace financial_data