@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 增强版PyTDX数据采集调度器启动脚本

echo ===============================================
echo        增强版PyTDX数据采集调度器
echo ===============================================
echo.

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)

:: 检查依赖
echo 正在检查依赖...
python -c "import pytdx, pandas, asyncio" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要依赖，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

:: 创建必要目录
if not exist "logs" mkdir logs
if not exist "config" mkdir config

:: 检查配置文件
if not exist "config\scheduler_config.json" (
    echo ⚠️  配置文件不存在，将使用默认配置
)

echo.
echo 🚀 启动增强版调度器...
echo.
echo 功能特性:
echo   ✅ 自动更新所有类型代码表
echo   ✅ 全量数据更新 (股票、指数、期货、基金)
echo   ✅ 实时数据更新
echo   ✅ 智能调度和故障恢复
echo.
echo 按 Ctrl+C 停止服务
echo ===============================================
echo.

:: 启动服务
python start_enhanced_scheduler.py

echo.
echo 服务已停止
pause