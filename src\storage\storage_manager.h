#pragma once

#include <memory>
#include <string>
#include <atomic>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <future>
#include <chrono>
#include <spdlog/spdlog.h>

#include "redis_storage.h"
#include "clickhouse_storage.h"
#include "../data_models.h"

namespace financial_data {

/**
 * @brief 存储管理器配置
 */
struct StorageManagerConfig {
    // Redis配置
    RedisConfig redis_config;
    bool enable_redis = true;
    
    // ClickHouse配置
    ClickHouseConfig clickhouse_config;
    bool enable_clickhouse = true;
    
    // 异步写入配置
    size_t async_queue_size = 50000;
    size_t worker_thread_count = 4;
    size_t batch_size = 1000;
    uint32_t batch_timeout_ms = 100;
    
    // 数据迁移配置
    bool enable_auto_migration = true;
    uint32_t migration_interval_hours = 1;
    uint32_t hot_data_retention_hours = 168; // 7天
    
    // 性能配置
    bool enable_write_batching = true;
    bool enable_compression = true;
    uint32_t max_retry_attempts = 3;
    uint32_t retry_delay_ms = 1000;
};

/**
 * @brief 存储任务类型
 */
enum class StorageTaskType {
    STORE_TICK,
    STORE_LEVEL2,
    STORE_BATCH,
    MIGRATE_DATA,
    CLEANUP_EXPIRED
};

/**
 * @brief 存储任务
 */
struct StorageTask {
    StorageTaskType type;
    std::string task_id;
    std::chrono::steady_clock::time_point submit_time;
    std::promise<bool> promise;
    
    // 数据载荷
    std::vector<StandardTick> ticks;
    std::vector<Level2Data> level2_data;
    MarketDataBatch batch;
    
    // 任务参数
    std::string symbol;
    int64_t start_time = 0;
    int64_t end_time = 0;
    
    StorageTask(StorageTaskType t) : type(t) {
        submit_time = std::chrono::steady_clock::now();
        task_id = GenerateTaskId();
    }
    
private:
    static std::string GenerateTaskId() {
        static std::atomic<uint64_t> counter{0};
        auto now = std::chrono::steady_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
            now.time_since_epoch()).count();
        return "task_" + std::to_string(timestamp) + "_" + std::to_string(counter++);
    }
};

/**
 * @brief 存储统计信息
 */
struct StorageStatistics {
    // Redis统计
    std::atomic<uint64_t> redis_writes_success{0};
    std::atomic<uint64_t> redis_writes_failed{0};
    std::atomic<double> redis_avg_latency_ms{0.0};
    
    // ClickHouse统计
    std::atomic<uint64_t> clickhouse_writes_success{0};
    std::atomic<uint64_t> clickhouse_writes_failed{0};
    std::atomic<double> clickhouse_avg_latency_ms{0.0};
    
    // 队列统计
    std::atomic<uint64_t> pending_tasks{0};
    std::atomic<uint64_t> completed_tasks{0};
    std::atomic<uint64_t> failed_tasks{0};
    
    // 数据迁移统计
    std::atomic<uint64_t> migrated_records{0};
    std::atomic<uint64_t> migration_errors{0};
    std::atomic<uint64_t> cleanup_operations{0};
    
    void Reset() {
        redis_writes_success = 0;
        redis_writes_failed = 0;
        redis_avg_latency_ms = 0.0;
        clickhouse_writes_success = 0;
        clickhouse_writes_failed = 0;
        clickhouse_avg_latency_ms = 0.0;
        pending_tasks = 0;
        completed_tasks = 0;
        failed_tasks = 0;
        migrated_records = 0;
        migration_errors = 0;
        cleanup_operations = 0;
    }
};

/**
 * @brief 统一存储管理器
 * 
 * 负责协调Redis热存储和ClickHouse温存储，
 * 提供异步写入、批处理、数据迁移等功能
 */
class StorageManager {
public:
    explicit StorageManager(const StorageManagerConfig& config = StorageManagerConfig{});
    ~StorageManager();
    
    // 初始化和生命周期管理
    bool Initialize();
    void Shutdown();
    bool IsRunning() const { return running_.load(); }
    
    // 同步写入接口
    bool StoreTick(const StandardTick& tick);
    bool StoreLevel2(const Level2Data& level2);
    bool StoreBatch(const std::vector<StandardTick>& ticks);
    bool StoreBatch(const std::vector<Level2Data>& level2_data);
    
    // 异步写入接口
    std::future<bool> StoreTickAsync(const StandardTick& tick);
    std::future<bool> StoreLevel2Async(const Level2Data& level2);
    std::future<bool> StoreBatchAsync(const std::vector<StandardTick>& ticks);
    std::future<bool> StoreBatchAsync(const std::vector<Level2Data>& level2_data);
    
    // 查询接口（优先从Redis查询，回退到ClickHouse）
    bool GetLatestTick(const std::string& symbol, StandardTick& tick);
    bool GetLatestLevel2(const std::string& symbol, Level2Data& level2);
    std::vector<StandardTick> QueryTicks(const std::string& symbol, 
                                        int64_t start_time, int64_t end_time, 
                                        size_t limit = 1000);
    
    // 数据迁移管理
    std::future<bool> TriggerDataMigration(const std::string& symbol = "");
    bool StartAutoMigration();
    void StopAutoMigration();
    
    // 数据清理
    std::future<bool> CleanupExpiredData();
    
    // 统计和监控
    StorageStatistics GetStatistics() const;
    void ResetStatistics();
    
    // 健康检查
    struct HealthStatus {
        bool overall_healthy;
        bool redis_healthy;
        bool clickhouse_healthy;
        bool queue_healthy;
        std::string error_message;
    };
    
    HealthStatus GetHealthStatus() const;
    
    // 配置管理
    bool UpdateConfig(const StorageManagerConfig& config);
    StorageManagerConfig GetConfig() const { return config_; }

private:
    StorageManagerConfig config_;
    std::atomic<bool> running_{false};
    std::atomic<bool> shutdown_requested_{false};
    
    // 存储组件
    std::unique_ptr<RedisHotStorage> redis_storage_;
    std::unique_ptr<ClickHouseStorage> clickhouse_storage_;
    
    // 异步任务队列
    std::queue<StorageTask> task_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // 工作线程
    std::vector<std::thread> worker_threads_;
    std::thread migration_thread_;
    std::thread cleanup_thread_;
    
    // 批处理
    std::vector<StandardTick> tick_batch_;
    std::vector<Level2Data> level2_batch_;
    std::mutex batch_mutex_;
    std::chrono::steady_clock::time_point last_batch_time_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    StorageStatistics statistics_;
    
    // 日志记录器
    std::shared_ptr<spdlog::logger> logger_;

private:
    // 初始化方法
    void InitializeLogger();
    bool InitializeRedisStorage();
    bool InitializeClickHouseStorage();
    
    // 工作线程
    void WorkerLoop(size_t worker_id);
    void MigrationLoop();
    void CleanupLoop();
    
    // 任务处理
    void ProcessTask(StorageTask& task);
    bool ProcessStoreTick(const StorageTask& task);
    bool ProcessStoreLevel2(const StorageTask& task);
    bool ProcessStoreBatch(const StorageTask& task);
    bool ProcessMigrateData(const StorageTask& task);
    bool ProcessCleanupExpired(const StorageTask& task);
    
    // 批处理管理
    void FlushBatches();
    bool ShouldFlushBatch() const;
    
    // 数据迁移
    bool MigrateSymbolData(const std::string& symbol, int64_t cutoff_time);
    std::vector<std::string> GetSymbolsForMigration();
    
    // 统计更新
    void UpdateRedisStats(bool success, double latency_ms);
    void UpdateClickHouseStats(bool success, double latency_ms);
    void UpdateTaskStats(bool success);
    
    // 错误处理和重试
    bool RetryTask(StorageTask& task);
    void HandleTaskError(const StorageTask& task, const std::string& error);
    
    // 辅助方法
    std::string GenerateTaskId() const;
    int64_t GetCurrentTimestamp() const;
    bool IsExpiredData(int64_t timestamp) const;
};

/**
 * @brief 存储管理器工厂
 */
class StorageManagerFactory {
public:
    static std::unique_ptr<StorageManager> CreateDefault();
    static std::unique_ptr<StorageManager> CreateHighPerformance();
    static std::unique_ptr<StorageManager> CreateLowLatency();
    static std::unique_ptr<StorageManager> CreateFromConfig(const std::string& config_file);
};

} // namespace financial_data