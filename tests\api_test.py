"""
Tests for the RESTful API
"""

import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, MagicMock
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from api.main import app, get_data_service, get_cache_manager
from api.services import HistoricalDataService
from api.cache import CacheManager
from api.models import TickData, KlineData, Level2Data, PaginationInfo


class TestAPIEndpoints:
    """Test API endpoints"""
    
    def setup_method(self):
        """Setup test client and mocks"""
        self.client = TestClient(app)
        
        # Mock data service
        self.mock_data_service = AsyncMock(spec=HistoricalDataService)
        self.mock_cache_manager = AsyncMock(spec=CacheManager)
        
        # Override dependencies
        app.dependency_overrides[get_data_service] = lambda: self.mock_data_service
        app.dependency_overrides[get_cache_manager] = lambda: self.mock_cache_manager
    
    def teardown_method(self):
        """Clean up after tests"""
        app.dependency_overrides.clear()
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = self.client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
    
    def test_get_tick_data_success(self):
        """Test successful tick data query"""
        # Mock cache miss
        self.mock_cache_manager.get.return_value = None
        
        # Mock data service response
        mock_tick_data = [
            TickData(
                timestamp=1721446200123456789,
                symbol="CU2409",
                exchange="SHFE",
                last_price=78560.0,
                volume=12580,
                turnover=**********.0,
                sequence=123456
            )
        ]
        
        from api.models import TickDataResponse
        mock_response = TickDataResponse(
            data=mock_tick_data,
            pagination=PaginationInfo(
                has_next=False,
                next_cursor=None,
                page_size=1
            )
        )
        
        self.mock_data_service.get_tick_data.return_value = mock_response
        
        # Make request
        response = self.client.get("/api/v1/tick-data?symbol=CU2409&exchange=SHFE&limit=1000")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        assert len(data["data"]) == 1
        assert data["data"][0]["symbol"] == "CU2409"
        assert data["data"][0]["exchange"] == "SHFE"
        assert data["data"][0]["last_price"] == 78560.0
    
    def test_get_tick_data_invalid_symbol(self):
        """Test tick data query with invalid symbol"""
        response = self.client.get("/api/v1/tick-data?symbol=&exchange=SHFE")
        assert response.status_code == 400  # Validation error
        data = response.json()
        assert "Symbol cannot be empty" in data["error"]
    
    def test_get_kline_data_success(self):
        """Test successful K-line data query"""
        # Mock cache miss
        self.mock_cache_manager.get.return_value = None
        
        # Mock data service response
        mock_kline_data = [
            KlineData(
                timestamp=1721446200000000000,
                symbol="CU2409",
                exchange="SHFE",
                period="1m",
                open=78550.0,
                high=78580.0,
                low=78540.0,
                close=78560.0,
                volume=1258,
                turnover=98765432.0
            )
        ]
        
        from api.models import KlineDataResponse
        mock_response = KlineDataResponse(
            data=mock_kline_data,
            pagination=PaginationInfo(
                has_next=False,
                next_cursor=None,
                page_size=1
            )
        )
        
        self.mock_data_service.get_kline_data.return_value = mock_response
        
        # Make request
        response = self.client.get("/api/v1/kline-data?symbol=CU2409&period=1m")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        assert len(data["data"]) == 1
        assert data["data"][0]["symbol"] == "CU2409"
        assert data["data"][0]["period"] == "1m"
    
    def test_get_kline_data_invalid_period(self):
        """Test K-line data query with invalid period"""
        response = self.client.get("/api/v1/kline-data?symbol=CU2409&period=invalid")
        assert response.status_code == 400
        data = response.json()
        assert "Invalid period" in data["error"]
    
    def test_get_level2_data_success(self):
        """Test successful Level-2 data query"""
        # Mock cache miss
        self.mock_cache_manager.get.return_value = None
        
        # Mock data service response
        mock_level2_data = [
            Level2Data(
                timestamp=1721446200123456789,
                symbol="CU2409",
                exchange="SHFE",
                bids=[{"price": 78550.0, "volume": 10, "order_count": None}],
                asks=[{"price": 78570.0, "volume": 8, "order_count": None}],
                sequence=123456
            )
        ]
        
        from api.models import Level2DataResponse
        mock_response = Level2DataResponse(
            data=mock_level2_data,
            pagination=PaginationInfo(
                has_next=False,
                next_cursor=None,
                page_size=1
            )
        )
        
        self.mock_data_service.get_level2_data.return_value = mock_response
        
        # Make request
        response = self.client.get("/api/v1/level2-data?symbol=CU2409")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        assert len(data["data"]) == 1
        assert data["data"][0]["symbol"] == "CU2409"
        assert len(data["data"][0]["bids"]) == 1
        assert len(data["data"][0]["asks"]) == 1
    
    def test_get_symbols_success(self):
        """Test get available symbols"""
        mock_symbols = [
            {
                "symbol": "CU2409",
                "exchange": "SHFE",
                "product_type": "futures",
                "underlying": "Copper",
                "contract_size": 5.0,
                "tick_size": 10.0
            }
        ]
        
        self.mock_data_service.get_available_symbols.return_value = mock_symbols
        
        response = self.client.get("/api/v1/symbols")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["symbol"] == "CU2409"
        assert data[0]["exchange"] == "SHFE"
    
    def test_get_exchanges_success(self):
        """Test get available exchanges"""
        mock_exchanges = ["SHFE", "DCE", "CZCE", "CFFEX"]
        
        self.mock_data_service.get_available_exchanges.return_value = mock_exchanges
        
        response = self.client.get("/api/v1/exchanges")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 4
        assert "SHFE" in data
        assert "DCE" in data
    
    def test_cache_hit_scenario(self):
        """Test cache hit scenario"""
        # Mock cache hit
        cached_response = {
            "data": [
                {
                    "timestamp": 1721446200123456789,
                    "symbol": "CU2409",
                    "exchange": "SHFE",
                    "last_price": 78560.0,
                    "volume": 12580,
                    "turnover": **********.0,
                    "sequence": 123456,
                    "bids": [],
                    "asks": [],
                    "open_interest": None,
                    "trade_flag": None
                }
            ],
            "success": True,
            "pagination": {
                "has_next": False,
                "next_cursor": None,
                "page_size": 1
            }
        }
        
        self.mock_cache_manager.get.return_value = cached_response
        
        response = self.client.get("/api/v1/tick-data?symbol=CU2409")
        
        assert response.status_code == 200
        # Data service should not be called when cache hits
        self.mock_data_service.get_tick_data.assert_not_called()
    
    def test_pagination_with_cursor(self):
        """Test pagination with cursor"""
        # Mock cache miss
        self.mock_cache_manager.get.return_value = None
        
        # Mock data service response with pagination
        mock_tick_data = [
            TickData(
                timestamp=1721446200123456789,
                symbol="CU2409",
                exchange="SHFE",
                last_price=78560.0,
                volume=12580,
                turnover=**********.0,
                sequence=123456
            )
        ]
        
        from api.models import TickDataResponse
        mock_response = TickDataResponse(
            data=mock_tick_data,
            pagination=PaginationInfo(
                has_next=True,
                next_cursor="bmV4dF9jdXJzb3I=",  # base64 encoded cursor
                page_size=1
            )
        )
        
        self.mock_data_service.get_tick_data.return_value = mock_response
        
        response = self.client.get("/api/v1/tick-data?symbol=CU2409&limit=1")
        
        assert response.status_code == 200
        data = response.json()
        assert data["pagination"]["has_next"] == True
        assert data["pagination"]["next_cursor"] is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])