"""
数据质量管理器测试

测试数据质量控制功能，包括：
- 数据验证器测试
- 数据去重器测试
- 数据完整性校验测试
- 质量管理器集成测试
"""

import asyncio
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from collectors.data_quality_manager import (
    DataQualityManager,
    EnhancedDataValidator,
    EnhancedDataDeduplicator,
    DataIntegrityChecker,
    QualityMetrics,
    PriceValidationRule,
    OHLCValidationRule,
    VolumeValidationRule,
    TimeSeriesValidationRule
)


class TestValidationRules:
    """测试验证规则"""
    
    @pytest.fixture
    def sample_k_data(self):
        """创建样本K线数据"""
        dates = pd.date_range(start='2024-01-01', periods=10, freq='D')
        data = {
            'open': [10.0, 10.5, 11.0, 10.8, 11.2, 10.9, 11.5, 11.1, 10.7, 11.3],
            'high': [10.8, 11.2, 11.5, 11.3, 11.8, 11.4, 12.0, 11.6, 11.2, 11.9],
            'low': [9.8, 10.2, 10.7, 10.5, 10.9, 10.6, 11.2, 10.8, 10.4, 11.0],
            'close': [10.5, 11.0, 10.8, 11.2, 10.9, 11.5, 11.1, 10.7, 11.3, 11.6],
            'volume': [1000, 1200, 800, 1500, 900, 1100, 1300, 950, 1050, 1250]
        }
        df = pd.DataFrame(data, index=dates)
        
        # 确保OHLC关系正确
        for i in range(len(df)):
            row = df.iloc[i]
            high_val = max(row['open'], row['close'], row['high'])
            low_val = min(row['open'], row['close'], row['low'])
            df.iloc[i, df.columns.get_loc('high')] = high_val
            df.iloc[i, df.columns.get_loc('low')] = low_val
        
        return df
    
    @pytest.fixture
    def invalid_k_data(self):
        """创建包含无效数据的K线数据"""
        dates = pd.date_range(start='2024-01-01', periods=10, freq='D')
        data = {
            'open': [10.0, -5.0, 11.0, 0.0, 11.2, 10.9, 11.5, 11.1, 10.7, 11.3],  # 负价格和零价格
            'high': [10.8, 11.2, 11.5, 11.3, 11.8, 11.4, 12.0, 11.6, 11.2, 11.9],
            'low': [12.0, 10.2, 10.7, 10.5, 10.9, 10.6, 11.2, 10.8, 10.4, 11.0],  # low > high
            'close': [10.5, 11.0, 10.8, 11.2, 10.9, 11.5, 11.1, 10.7, 11.3, 11.6],
            'volume': [1000, -500, 800, 1500, 900, 1100, 1300, 950, 1050, 1250]  # 负成交量
        }
        return pd.DataFrame(data, index=dates)
    
    def test_price_validation_rule(self, sample_k_data, invalid_k_data):
        """测试价格验证规则"""
        rule = PriceValidationRule(min_price=0.01, max_price=1000.0, max_change_ratio=0.5)
        
        # 测试有效数据
        valid_mask, issues = rule.validate(sample_k_data, 'TEST001')
        assert valid_mask.all(), "Valid data should pass price validation"
        assert len(issues) == 0, "Valid data should not have issues"
        
        # 测试无效数据
        invalid_mask, issues = rule.validate(invalid_k_data, 'TEST002')
        assert not invalid_mask.all(), "Invalid data should fail price validation"
        assert len(issues) > 0, "Invalid data should have issues"
        assert any("negative prices" in issue for issue in issues)
        assert any("zero prices" in issue for issue in issues)
    
    def test_ohlc_validation_rule(self, sample_k_data, invalid_k_data):
        """测试OHLC验证规则"""
        rule = OHLCValidationRule()
        
        # 测试有效数据
        valid_mask, issues = rule.validate(sample_k_data, 'TEST001')
        assert valid_mask.all(), "Valid OHLC data should pass validation"
        assert len(issues) == 0, "Valid OHLC data should not have issues"
        
        # 测试无效数据
        invalid_mask, issues = rule.validate(invalid_k_data, 'TEST002')
        assert not invalid_mask.all(), "Invalid OHLC data should fail validation"
        assert len(issues) > 0, "Invalid OHLC data should have issues"
        assert any("invalid OHLC relationships" in issue for issue in issues)
    
    def test_volume_validation_rule(self, sample_k_data, invalid_k_data):
        """测试成交量验证规则"""
        rule = VolumeValidationRule(min_volume=0, max_volume_multiplier=10.0)
        
        # 测试有效数据
        valid_mask, issues = rule.validate(sample_k_data, 'TEST001')
        assert valid_mask.all(), "Valid volume data should pass validation"
        assert len(issues) == 0, "Valid volume data should not have issues"
        
        # 测试无效数据
        invalid_mask, issues = rule.validate(invalid_k_data, 'TEST002')
        assert not invalid_mask.all(), "Invalid volume data should fail validation"
        assert len(issues) > 0, "Invalid volume data should have issues"
        assert any("negative volumes" in issue for issue in issues)
    
    def test_timeseries_validation_rule(self):
        """测试时间序列验证规则"""
        rule = TimeSeriesValidationRule(allow_duplicates=False, max_time_gap_minutes=1440)
        
        # 创建有序时间序列数据
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        ordered_data = pd.DataFrame({'price': [10, 11, 12, 13, 14]}, index=dates)
        
        valid_mask, issues = rule.validate(ordered_data, 'TEST001')
        assert valid_mask.all(), "Ordered time series should pass validation"
        assert len(issues) == 0, "Ordered time series should not have issues"
        
        # 创建包含重复时间戳的数据
        duplicate_dates = [dates[0], dates[0], dates[1], dates[2], dates[3]]
        duplicate_data = pd.DataFrame({'price': [10, 10.5, 11, 12, 13]}, index=duplicate_dates)
        
        valid_mask, issues = rule.validate(duplicate_data, 'TEST002')
        assert len(issues) > 0, "Duplicate timestamps should be detected"
        assert any("duplicate timestamps" in issue for issue in issues)


class TestEnhancedDataValidator:
    """测试增强数据验证器"""
    
    @pytest.fixture
    def validator(self):
        """创建数据验证器"""
        config = {
            'min_price': 0.01,
            'max_price': 1000.0,
            'max_price_change_ratio': 0.5,
            'min_volume': 0,
            'max_volume_multiplier': 100.0,
            'allow_duplicate_timestamps': False,
            'max_time_gap_minutes': 1440
        }
        return EnhancedDataValidator(config)
    
    @pytest.fixture
    def sample_k_data(self):
        """创建样本K线数据"""
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        data = {
            'open': [10.0, 10.5, 11.0, 10.8, 11.2],
            'high': [10.8, 11.2, 11.5, 11.3, 11.8],
            'low': [9.8, 10.2, 10.7, 10.5, 10.9],
            'close': [10.5, 11.0, 10.8, 11.2, 10.9],
            'volume': [1000, 1200, 800, 1500, 900]
        }
        return pd.DataFrame(data, index=dates)
    
    @pytest.fixture
    def sample_tick_data(self):
        """创建样本tick数据"""
        times = pd.date_range(start='2024-01-01 09:30:00', periods=10, freq='1S')
        data = {
            'price': [10.0, 10.1, 10.05, 10.15, 10.2, 10.18, 10.25, 10.22, 10.3, 10.28],
            'volume': [100, 200, 150, 300, 250, 180, 220, 160, 280, 190]
        }
        return pd.DataFrame(data, index=times)
    
    @pytest.mark.asyncio
    async def test_validate_k_data_success(self, validator, sample_k_data):
        """测试K线数据验证成功"""
        validated_data, metrics = await validator.validate_k_data('TEST001', sample_k_data)
        
        assert not validated_data.empty, "Validated data should not be empty"
        assert len(validated_data) == len(sample_k_data), "All valid data should be retained"
        assert metrics.symbol == 'TEST001'
        assert metrics.total_records == len(sample_k_data)
        assert metrics.valid_records == len(validated_data)
        assert metrics.accuracy_ratio == 1.0, "All data should be valid"
        assert len(metrics.quality_issues) == 0, "No quality issues should be found"
    
    @pytest.mark.asyncio
    async def test_validate_k_data_with_invalid_data(self, validator):
        """测试包含无效数据的K线验证"""
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        invalid_data = pd.DataFrame({
            'open': [10.0, -5.0, 11.0, 0.0, 11.2],  # 包含负价格和零价格
            'high': [10.8, 11.2, 11.5, 11.3, 11.8],
            'low': [9.8, 10.2, 10.7, 10.5, 10.9],
            'close': [10.5, 11.0, 10.8, 11.2, 10.9],
            'volume': [1000, 1200, 800, 1500, 900]
        }, index=dates)
        
        validated_data, metrics = await validator.validate_k_data('TEST002', invalid_data)
        
        assert len(validated_data) < len(invalid_data), "Invalid data should be filtered out"
        assert metrics.invalid_records > 0, "Invalid records should be counted"
        assert metrics.accuracy_ratio < 1.0, "Accuracy ratio should be less than 1.0"
        assert len(metrics.quality_issues) > 0, "Quality issues should be detected"
    
    @pytest.mark.asyncio
    async def test_validate_tick_data_success(self, validator, sample_tick_data):
        """测试tick数据验证成功"""
        validated_data, metrics = await validator.validate_tick_data('TEST001', sample_tick_data)
        
        assert not validated_data.empty, "Validated data should not be empty"
        assert len(validated_data) == len(sample_tick_data), "All valid data should be retained"
        assert metrics.symbol == 'TEST001'
        assert metrics.total_records == len(sample_tick_data)
        assert metrics.valid_records == len(validated_data)
        assert metrics.accuracy_ratio == 1.0, "All data should be valid"
    
    @pytest.mark.asyncio
    async def test_validate_empty_data(self, validator):
        """测试空数据验证"""
        empty_data = pd.DataFrame()
        
        validated_data, metrics = await validator.validate_k_data('TEST_EMPTY', empty_data)
        
        assert validated_data.empty, "Empty data should remain empty"
        assert metrics.total_records == 0
        assert metrics.valid_records == 0
        assert 'Empty dataset' in metrics.quality_issues
    
    def test_add_remove_rules(self, validator):
        """测试添加和移除验证规则"""
        initial_rule_count = len(validator.rules)
        
        # 添加自定义规则
        custom_rule = PriceValidationRule(min_price=1.0, max_price=100.0)
        custom_rule.name = "custom_price_validation"  # 给自定义规则一个不同的名字
        validator.add_rule(custom_rule)
        
        assert len(validator.rules) == initial_rule_count + 1
        
        # 移除自定义规则
        validator.remove_rule('custom_price_validation')
        assert len(validator.rules) == initial_rule_count
        
        # 测试移除默认规则
        validator.remove_rule('price_validation')
        assert len(validator.rules) == initial_rule_count - 1
    
    def test_statistics(self, validator):
        """测试统计信息"""
        stats = validator.get_validation_statistics()
        assert isinstance(stats, dict)
        
        validator.reset_statistics()
        stats_after_reset = validator.get_validation_statistics()
        assert len(stats_after_reset) == 0


class TestEnhancedDataDeduplicator:
    """测试增强数据去重器"""
    
    @pytest.fixture
    def deduplicator(self):
        """创建数据去重器"""
        config = {
            'enable_cross_batch_dedup': True,
            'cache_max_size': 1000
        }
        return EnhancedDataDeduplicator(config)
    
    @pytest.fixture
    def duplicate_k_data(self):
        """创建包含重复数据的K线数据"""
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        # 创建重复的时间戳
        duplicate_dates = list(dates) + [dates[0], dates[1]]
        
        data = {
            'open': [10.0, 10.5, 11.0, 10.8, 11.2, 10.0, 10.5],  # 前两个重复
            'high': [10.8, 11.2, 11.5, 11.3, 11.8, 10.8, 11.2],
            'low': [9.8, 10.2, 10.7, 10.5, 10.9, 9.8, 10.2],
            'close': [10.5, 11.0, 10.8, 11.2, 10.9, 10.5, 11.0],
            'volume': [1000, 1200, 800, 1500, 900, 1000, 1200]
        }
        return pd.DataFrame(data, index=duplicate_dates)
    
    @pytest.fixture
    def duplicate_tick_data(self):
        """创建包含重复数据的tick数据"""
        times = pd.date_range(start='2024-01-01 09:30:00', periods=5, freq='1S')
        # 创建重复的时间戳
        duplicate_times = list(times) + [times[0], times[1]]
        
        data = {
            'price': [10.0, 10.1, 10.05, 10.15, 10.2, 10.0, 10.1],  # 前两个重复
            'volume': [100, 200, 150, 300, 250, 100, 200]
        }
        return pd.DataFrame(data, index=duplicate_times)
    
    @pytest.mark.asyncio
    async def test_deduplicate_k_data(self, deduplicator, duplicate_k_data):
        """测试K线数据去重"""
        original_count = len(duplicate_k_data)
        deduplicated_data, removed_count = await deduplicator.deduplicate_k_data('TEST001', duplicate_k_data)
        
        assert len(deduplicated_data) < original_count, "Duplicates should be removed"
        assert removed_count > 0, "Removed count should be greater than 0"
        assert len(deduplicated_data) + removed_count == original_count, "Counts should match"
        
        # 检查时间戳唯一性
        assert not deduplicated_data.index.duplicated().any(), "No duplicate timestamps should remain"
    
    @pytest.mark.asyncio
    async def test_deduplicate_tick_data(self, deduplicator, duplicate_tick_data):
        """测试tick数据去重"""
        original_count = len(duplicate_tick_data)
        deduplicated_data, removed_count = await deduplicator.deduplicate_tick_data('TEST001', duplicate_tick_data)
        
        assert len(deduplicated_data) < original_count, "Duplicates should be removed"
        assert removed_count > 0, "Removed count should be greater than 0"
        assert len(deduplicated_data) + removed_count == original_count, "Counts should match"
        
        # 检查时间戳唯一性
        assert not deduplicated_data.index.duplicated().any(), "No duplicate timestamps should remain"
    
    @pytest.mark.asyncio
    async def test_cross_batch_deduplication(self, deduplicator):
        """测试跨批次去重"""
        # 第一批数据
        dates1 = pd.date_range(start='2024-01-01', periods=3, freq='D')
        data1 = pd.DataFrame({
            'open': [10.0, 10.5, 11.0],
            'high': [10.8, 11.2, 11.5],
            'low': [9.8, 10.2, 10.7],
            'close': [10.5, 11.0, 10.8],
            'volume': [1000, 1200, 800]
        }, index=dates1)
        
        # 第二批数据（包含第一批的重复数据）
        dates2 = pd.date_range(start='2024-01-01', periods=5, freq='D')
        data2 = pd.DataFrame({
            'open': [10.0, 10.5, 11.0, 10.8, 11.2],  # 前三个与第一批重复
            'high': [10.8, 11.2, 11.5, 11.3, 11.8],
            'low': [9.8, 10.2, 10.7, 10.5, 10.9],
            'close': [10.5, 11.0, 10.8, 11.2, 10.9],
            'volume': [1000, 1200, 800, 1500, 900]
        }, index=dates2)
        
        # 处理第一批
        result1, removed1 = await deduplicator.deduplicate_k_data('TEST001', data1)
        assert len(result1) == 3, "First batch should have all records"
        
        # 处理第二批（应该检测到跨批次重复）
        result2, removed2 = await deduplicator.deduplicate_k_data('TEST001', data2)
        assert removed2 > 0, "Cross-batch duplicates should be detected"
        assert len(result2) < len(data2), "Some records should be removed"
    
    @pytest.mark.asyncio
    async def test_empty_data_deduplication(self, deduplicator):
        """测试空数据去重"""
        empty_data = pd.DataFrame()
        
        result, removed_count = await deduplicator.deduplicate_k_data('TEST_EMPTY', empty_data)
        
        assert result.empty, "Empty data should remain empty"
        assert removed_count == 0, "No records should be removed from empty data"
    
    def test_cache_management(self, deduplicator):
        """测试缓存管理"""
        # 测试缓存清空
        deduplicator.clear_cache('TEST001')
        assert 'TEST001_k_data' not in deduplicator.dedup_cache
        
        deduplicator.clear_cache()  # 清空所有缓存
        assert len(deduplicator.dedup_cache) == 0
    
    def test_statistics(self, deduplicator):
        """测试统计信息"""
        stats = deduplicator.get_deduplication_statistics()
        assert isinstance(stats, dict)
        
        deduplicator.reset_statistics()
        stats_after_reset = deduplicator.get_deduplication_statistics()
        assert len(stats_after_reset) == 0


class TestDataIntegrityChecker:
    """测试数据完整性校验器"""
    
    @pytest.fixture
    def integrity_checker(self):
        """创建完整性校验器"""
        return DataIntegrityChecker()
    
    @pytest.fixture
    def complete_daily_data(self):
        """创建完整的日线数据"""
        # 创建5个工作日的数据
        dates = pd.bdate_range(start='2024-01-01', periods=5)
        data = pd.DataFrame({
            'close': [10.0, 10.5, 11.0, 10.8, 11.2]
        }, index=dates)
        return data
    
    @pytest.fixture
    def incomplete_daily_data(self):
        """创建不完整的日线数据"""
        # 缺少中间的一天
        dates = pd.to_datetime(['2024-01-01', '2024-01-02', '2024-01-04', '2024-01-05'])
        data = pd.DataFrame({
            'close': [10.0, 10.5, 11.0, 10.8]
        }, index=dates)
        return data
    
    @pytest.mark.asyncio
    async def test_check_complete_data(self, integrity_checker, complete_daily_data):
        """测试完整数据的完整性检查"""
        result = await integrity_checker.check_data_completeness('TEST001', complete_daily_data, 'D')
        
        assert result['is_complete'], "Complete data should be marked as complete"
        assert result['missing_periods'] == 0, "No periods should be missing"
        assert result['completeness_ratio'] == 1.0, "Completeness ratio should be 1.0"
        assert len(result['missing_dates']) == 0, "No missing dates should be found"
        assert len(result['issues']) == 0, "No issues should be found"
    
    @pytest.mark.asyncio
    async def test_check_incomplete_data(self, integrity_checker, incomplete_daily_data):
        """测试不完整数据的完整性检查"""
        result = await integrity_checker.check_data_completeness('TEST002', incomplete_daily_data, 'D')
        
        assert not result['is_complete'], "Incomplete data should be marked as incomplete"
        assert result['missing_periods'] > 0, "Missing periods should be detected"
        assert result['completeness_ratio'] < 1.0, "Completeness ratio should be less than 1.0"
        assert len(result['missing_dates']) > 0, "Missing dates should be found"
        assert len(result['issues']) > 0, "Issues should be found"
    
    @pytest.mark.asyncio
    async def test_check_data_consistency(self, integrity_checker, complete_daily_data):
        """测试数据一致性检查"""
        result = await integrity_checker.check_data_consistency('TEST001', complete_daily_data)
        
        assert result['is_consistent'], "Consistent data should be marked as consistent"
        assert result['consistency_score'] >= 0.8, "Consistency score should be high"
        assert len(result['issues']) == 0, "No consistency issues should be found"
    
    @pytest.mark.asyncio
    async def test_check_inconsistent_data(self, integrity_checker):
        """测试不一致数据的一致性检查"""
        # 创建包含极端价格变化的数据
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        inconsistent_data = pd.DataFrame({
            'open': [10.0, 10.5, 500.0, 10.8, 11.2],  # 极端价格跳跃 (5000%变化)
            'high': [10.8, 11.2, 550.0, 11.3, 11.8],
            'low': [9.8, 10.2, 450.0, 10.5, 10.9],
            'close': [10.5, 11.0, 520.0, 11.2, 10.9],
            'volume': [1000, 0, 0, 0, 0]  # 包含80%零成交量
        }, index=dates)
        
        result = await integrity_checker.check_data_consistency('TEST003', inconsistent_data)
        
        assert not result['is_consistent'], "Inconsistent data should be marked as inconsistent"
        assert result['consistency_score'] < 0.8, "Consistency score should be low"
        assert len(result['issues']) > 0, "Consistency issues should be found"
    
    @pytest.mark.asyncio
    async def test_empty_data_integrity(self, integrity_checker):
        """测试空数据的完整性检查"""
        empty_data = pd.DataFrame()
        
        completeness_result = await integrity_checker.check_data_completeness('TEST_EMPTY', empty_data)
        consistency_result = await integrity_checker.check_data_consistency('TEST_EMPTY', empty_data)
        
        assert not completeness_result['is_complete']
        assert 'Empty dataset' in completeness_result['issues']
        assert not consistency_result['is_consistent']
        assert 'Empty dataset' in consistency_result['issues']
    
    def test_statistics(self, integrity_checker):
        """测试统计信息"""
        stats = integrity_checker.get_integrity_statistics()
        assert isinstance(stats, dict)
        
        integrity_checker.reset_statistics()
        stats_after_reset = integrity_checker.get_integrity_statistics()
        assert len(stats_after_reset) == 0


class TestDataQualityManager:
    """测试数据质量管理器"""
    
    @pytest.fixture
    def quality_manager(self):
        """创建数据质量管理器"""
        config = {
            'validation': {
                'min_price': 0.01,
                'max_price': 1000.0,
                'max_price_change_ratio': 0.5,
                'min_volume': 0,
                'max_volume_multiplier': 100.0
            },
            'deduplication': {
                'enable_cross_batch_dedup': True,
                'cache_max_size': 1000
            },
            'integrity': {
                'check_completeness': True,
                'check_consistency': True
            }
        }
        return DataQualityManager(config)
    
    @pytest.fixture
    def sample_k_data(self):
        """创建样本K线数据"""
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        data = {
            'open': [10.0, 10.5, 11.0, 10.8, 11.2],
            'high': [10.8, 11.2, 11.5, 11.3, 11.8],
            'low': [9.8, 10.2, 10.7, 10.5, 10.9],
            'close': [10.5, 11.0, 10.8, 11.2, 10.9],
            'volume': [1000, 1200, 800, 1500, 900]
        }
        return pd.DataFrame(data, index=dates)
    
    @pytest.fixture
    def sample_tick_data(self):
        """创建样本tick数据"""
        times = pd.date_range(start='2024-01-01 09:30:00', periods=10, freq='1S')
        data = {
            'price': [10.0, 10.1, 10.05, 10.15, 10.2, 10.18, 10.25, 10.22, 10.3, 10.28],
            'volume': [100, 200, 150, 300, 250, 180, 220, 160, 280, 190]
        }
        return pd.DataFrame(data, index=times)
    
    @pytest.mark.asyncio
    async def test_process_k_data_success(self, quality_manager, sample_k_data):
        """测试K线数据处理成功"""
        processed_data, quality_report = await quality_manager.process_k_data('TEST001', sample_k_data)
        
        assert not processed_data.empty, "Processed data should not be empty"
        assert quality_report['status'] == 'success', "Processing should be successful"
        assert quality_report['symbol'] == 'TEST001'
        assert quality_report['data_type'] == 'k_data'
        assert quality_report['original_count'] == len(sample_k_data)
        assert quality_report['final_count'] == len(processed_data)
        assert 'quality_score' in quality_report
        assert 'validation_metrics' in quality_report
        assert 'completeness' in quality_report
        assert 'consistency' in quality_report
    
    @pytest.mark.asyncio
    async def test_process_tick_data_success(self, quality_manager, sample_tick_data):
        """测试tick数据处理成功"""
        processed_data, quality_report = await quality_manager.process_tick_data('TEST001', sample_tick_data)
        
        assert not processed_data.empty, "Processed data should not be empty"
        assert quality_report['status'] == 'success', "Processing should be successful"
        assert quality_report['symbol'] == 'TEST001'
        assert quality_report['data_type'] == 'tick_data'
        assert quality_report['original_count'] == len(sample_tick_data)
        assert quality_report['final_count'] == len(processed_data)
        assert 'quality_score' in quality_report
        assert 'validation_metrics' in quality_report
        assert 'consistency' in quality_report
    
    @pytest.mark.asyncio
    async def test_process_invalid_data(self, quality_manager):
        """测试处理无效数据"""
        # 创建全部无效的数据
        dates = pd.date_range(start='2024-01-01', periods=3, freq='D')
        invalid_data = pd.DataFrame({
            'open': [-10.0, 0.0, -5.0],  # 全部无效价格
            'high': [-8.0, 0.0, -3.0],
            'low': [-12.0, 0.0, -7.0],
            'close': [-9.0, 0.0, -4.0],
            'volume': [-100, -200, -150]  # 全部无效成交量
        }, index=dates)
        
        processed_data, quality_report = await quality_manager.process_k_data('TEST_INVALID', invalid_data)
        
        assert processed_data.empty, "Invalid data should result in empty processed data"
        assert quality_report['status'] == 'validation_failed', "Processing should fail validation"
        assert len(quality_report['validation_metrics']['quality_issues']) > 0, "Quality issues should be detected"
    
    @pytest.mark.asyncio
    async def test_process_empty_data(self, quality_manager):
        """测试处理空数据"""
        empty_data = pd.DataFrame()
        
        processed_data, quality_report = await quality_manager.process_k_data('TEST_EMPTY', empty_data)
        
        assert processed_data.empty, "Empty data should remain empty"
        assert quality_report['status'] == 'empty', "Status should be empty"
        assert 'Empty dataset' in quality_report['issues']
    
    def test_quality_statistics(self, quality_manager):
        """测试质量统计信息"""
        stats = quality_manager.get_quality_statistics()
        
        assert isinstance(stats, dict)
        assert 'total_processed' in stats
        assert 'total_validated' in stats
        assert 'total_deduplicated' in stats
        assert 'total_integrity_checked' in stats
        assert 'validation_stats' in stats
        assert 'deduplication_stats' in stats
        assert 'integrity_stats' in stats
    
    def test_quality_reports(self, quality_manager):
        """测试质量报告"""
        # 初始状态应该没有报告
        reports = quality_manager.get_quality_reports()
        assert len(reports) == 0
        
        # 测试报告限制
        reports_limited = quality_manager.get_quality_reports(limit=5)
        assert len(reports_limited) <= 5
    
    def test_quality_summary(self, quality_manager):
        """测试质量摘要"""
        summary = quality_manager.generate_quality_summary()
        
        assert isinstance(summary, dict)
        assert 'symbol' in summary
        assert 'total_reports' in summary
        assert 'generated_at' in summary
    
    def test_reset_statistics(self, quality_manager):
        """测试重置统计信息"""
        # 重置前获取统计信息
        stats_before = quality_manager.get_quality_statistics()
        
        # 重置统计信息
        quality_manager.reset_statistics()
        
        # 重置后获取统计信息
        stats_after = quality_manager.get_quality_statistics()
        
        assert stats_after['total_processed'] == 0
        assert stats_after['total_validated'] == 0
        assert stats_after['total_deduplicated'] == 0
        assert stats_after['total_integrity_checked'] == 0


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])