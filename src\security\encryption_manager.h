#pragma once

#include "security_config.h"
#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/rand.h>
#include <string>
#include <vector>
#include <memory>

namespace financial_data {
namespace security {

class EncryptionManager {
public:
    explicit EncryptionManager(const EncryptionConfig& config);
    ~EncryptionManager();

    // 初始化加密管理器
    bool Initialize();
    
    // 加密数据
    bool EncryptData(const std::vector<uint8_t>& plaintext, 
                     std::vector<uint8_t>& ciphertext,
                     std::vector<uint8_t>& iv);
    
    // 解密数据
    bool DecryptData(const std::vector<uint8_t>& ciphertext,
                     const std::vector<uint8_t>& iv,
                     std::vector<uint8_t>& plaintext);
    
    // 加密文件
    bool EncryptFile(const std::string& input_file, const std::string& output_file);
    
    // 解密文件
    bool DecryptFile(const std::string& input_file, const std::string& output_file);
    
    // 生成新的加密密钥
    bool GenerateKey();
    
    // 密钥轮换
    bool RotateKey();
    
    // 获取密钥指纹
    std::string GetKeyFingerprint() const;

private:
    EncryptionConfig config_;
    std::vector<uint8_t> encryption_key_;
    bool initialized_;
    
    // 加载密钥
    bool LoadKey();
    
    // 保存密钥
    bool SaveKey();
    
    // 生成随机IV
    bool GenerateIV(std::vector<uint8_t>& iv);
    
    // 内部加密函数
    bool EncryptInternal(const uint8_t* plaintext, size_t plaintext_len,
                        const uint8_t* key, const uint8_t* iv,
                        uint8_t* ciphertext, size_t& ciphertext_len);
    
    // 内部解密函数
    bool DecryptInternal(const uint8_t* ciphertext, size_t ciphertext_len,
                        const uint8_t* key, const uint8_t* iv,
                        uint8_t* plaintext, size_t& plaintext_len);
};

} // namespace security
} // namespace financial_data