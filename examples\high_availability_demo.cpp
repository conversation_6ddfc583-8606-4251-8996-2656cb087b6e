#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <signal.h>
#include "failover/failover_manager.h"
#include "failover/health_checker.h"
#include "failover/data_sync.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>

using namespace financial_data;

class HighAvailabilityDemo {
public:
    HighAvailabilityDemo(const std::string& node_id, const std::vector<std::string>& peers)
        : node_id_(node_id), running_(false) {
        
        // 配置日志
        auto console = spdlog::stdout_color_mt("ha_demo");
        spdlog::set_default_logger(console);
        spdlog::set_level(spdlog::level::info);
        
        // 配置故障转移管理器
        FailoverConfig failover_config;
        failover_config.local_node_id = node_id;
        failover_config.peer_nodes = peers;
        failover_config.heartbeat_interval_ms = 2000;
        failover_config.heartbeat_timeout_ms = 10000;
        failover_config.failover_timeout_ms = 5000;
        failover_config.enable_auto_failback = true;
        
        failover_manager_ = std::make_unique<FailoverManager>(failover_config);
        
        // 配置健康检查器
        HealthCheckConfig health_config;
        health_config.check_interval_ms = 3000;
        health_config.enable_auto_recovery = true;
        
        health_checker_ = std::make_unique<HealthChecker>(health_config);
        
        // 配置数据同步管理器
        DataSyncConfig sync_config;
        sync_config.peer_nodes = peers;
        sync_config.sync_interval_ms = 1000;
        sync_config.enable_compression = true;
        
        data_sync_manager_ = std::make_unique<DataSyncManager>(sync_config);
        
        SetupCallbacks();
        RegisterComponents();
    }
    
    ~HighAvailabilityDemo() {
        Stop();
    }
    
    bool Start() {
        spdlog::info("=== Starting High Availability Demo ===");
        spdlog::info("Node ID: {}", node_id_);
        
        running_ = true;
        
        // 启动健康检查器
        if (!health_checker_->Start()) {
            spdlog::error("Failed to start health checker");
            return false;
        }
        spdlog::info("✓ Health checker started");
        
        // 启动数据同步管理器
        if (!data_sync_manager_->Start()) {
            spdlog::error("Failed to start data sync manager");
            return false;
        }
        spdlog::info("✓ Data sync manager started");
        
        // 启动故障转移管理器
        if (!failover_manager_->Start()) {
            spdlog::error("Failed to start failover manager");
            return false;
        }
        spdlog::info("✓ Failover manager started");
        
        // 启动状态监控线程
        monitor_thread_ = std::thread(&HighAvailabilityDemo::MonitorThread, this);
        
        spdlog::info("=== High Availability Demo Started Successfully ===");
        return true;
    }
    
    void Stop() {
        if (!running_) {
            return;
        }
        
        spdlog::info("=== Stopping High Availability Demo ===");
        
        running_ = false;
        
        // 停止监控线程
        if (monitor_thread_.joinable()) {
            monitor_thread_.join();
        }
        
        // 停止所有组件
        if (failover_manager_) {
            failover_manager_->Stop();
            spdlog::info("✓ Failover manager stopped");
        }
        
        if (data_sync_manager_) {
            data_sync_manager_->Stop();
            spdlog::info("✓ Data sync manager stopped");
        }
        
        if (health_checker_) {
            health_checker_->Stop();
            spdlog::info("✓ Health checker stopped");
        }
        
        spdlog::info("=== High Availability Demo Stopped ===");
    }
    
    void TriggerFailover() {
        spdlog::info("=== Manual Failover Triggered ===");
        failover_manager_->TriggerFailover("Manual failover from demo");
    }
    
    void SimulateHealthIssue() {
        spdlog::info("=== Simulating Health Issue ===");
        simulate_unhealthy_ = true;
        
        // 5秒后恢复
        std::thread([this]() {
            std::this_thread::sleep_for(std::chrono::seconds(5));
            simulate_unhealthy_ = false;
            spdlog::info("=== Health Issue Resolved ===");
        }).detach();
    }
    
    void PrintStatus() {
        spdlog::info("=== System Status ===");
        
        // 故障转移状态
        auto role = failover_manager_->GetCurrentRole();
        auto status = failover_manager_->GetCurrentStatus();
        
        std::string role_str = (role == NodeRole::PRIMARY) ? "PRIMARY" :
                              (role == NodeRole::SECONDARY) ? "SECONDARY" : "UNKNOWN";
        std::string status_str = (status == NodeStatus::HEALTHY) ? "HEALTHY" :
                                (status == NodeStatus::UNHEALTHY) ? "UNHEALTHY" :
                                (status == NodeStatus::DISCONNECTED) ? "DISCONNECTED" : "UNKNOWN";
        
        spdlog::info("Node Role: {}, Status: {}", role_str, status_str);
        
        // 集群状态
        auto cluster_status = failover_manager_->GetClusterStatus();
        spdlog::info("Cluster Nodes: {}", cluster_status.size());
        for (const auto& node : cluster_status) {
            std::string node_role = (node.role == NodeRole::PRIMARY) ? "PRIMARY" :
                                   (node.role == NodeRole::SECONDARY) ? "SECONDARY" : "UNKNOWN";
            std::string node_status = (node.status == NodeStatus::HEALTHY) ? "HEALTHY" :
                                     (node.status == NodeStatus::UNHEALTHY) ? "UNHEALTHY" :
                                     (node.status == NodeStatus::DISCONNECTED) ? "DISCONNECTED" : "UNKNOWN";
            spdlog::info("  - {}: {} ({})", node.node_id, node_role, node_status);
        }
        
        // 健康状态
        auto system_health = health_checker_->GetSystemHealth();
        std::string health_str = (system_health.overall_status == HealthStatus::HEALTHY) ? "HEALTHY" :
                                (system_health.overall_status == HealthStatus::WARNING) ? "WARNING" :
                                (system_health.overall_status == HealthStatus::CRITICAL) ? "CRITICAL" : "UNKNOWN";
        
        spdlog::info("System Health: {} ({} healthy, {} warning, {} critical)",
                    health_str, system_health.healthy_components,
                    system_health.warning_components, system_health.critical_components);
        
        // 同步状态
        auto sync_stats = data_sync_manager_->GetSyncStats();
        spdlog::info("Sync Stats: {} total, {} success, {} failed, {} pending",
                    sync_stats.total_operations, sync_stats.successful_operations,
                    sync_stats.failed_operations, sync_stats.pending_operations);
    }

private:
    void SetupCallbacks() {
        // 故障转移回调
        failover_manager_->SetFailoverCallback([this](NodeRole old_role, NodeRole new_role) {
            std::string old_str = (old_role == NodeRole::PRIMARY) ? "PRIMARY" :
                                 (old_role == NodeRole::SECONDARY) ? "SECONDARY" : "UNKNOWN";
            std::string new_str = (new_role == NodeRole::PRIMARY) ? "PRIMARY" :
                                 (new_role == NodeRole::SECONDARY) ? "SECONDARY" : "UNKNOWN";
            
            spdlog::warn("🔄 ROLE CHANGE: {} -> {}", old_str, new_str);
            
            if (new_role == NodeRole::PRIMARY) {
                spdlog::info("🎯 This node is now the PRIMARY node!");
                // 触发全量数据同步
                data_sync_manager_->TriggerFullSync();
            }
        });
        
        // 健康检查回调
        failover_manager_->SetHealthCheckCallback([this]() {
            return health_checker_->IsSystemHealthy() && !simulate_unhealthy_;
        });
        
        // 数据同步回调
        failover_manager_->SetDataSyncCallback([this](const std::string& peer_node) {
            spdlog::info("Syncing data to peer node: {}", peer_node);
            return data_sync_manager_->RequestSyncToAll("system_config");
        });
        
        // 健康告警回调
        health_checker_->SetAlertCallback([this](const ComponentHealth& component) {
            std::string status_str = (component.overall_status == HealthStatus::WARNING) ? "WARNING" :
                                    (component.overall_status == HealthStatus::CRITICAL) ? "CRITICAL" : "UNKNOWN";
            spdlog::warn("🚨 HEALTH ALERT: {} - {} ({})", 
                        component.component_name, status_str, component.error_message);
        });
        
        // 同步回调
        data_sync_manager_->SetSyncCallback([this](const SyncOperation& operation) {
            if (operation.status == SyncStatus::SUCCESS) {
                spdlog::debug("✓ Sync completed: {} -> {} ({})", 
                             operation.source_node, operation.target_node, operation.data_type);
            } else if (operation.status == SyncStatus::FAILED) {
                spdlog::warn("✗ Sync failed: {} -> {} ({}) - {}", 
                            operation.source_node, operation.target_node, 
                            operation.data_type, operation.error_message);
            }
        });
    }
    
    void RegisterComponents() {
        // 注册自定义健康检查组件
        health_checker_->RegisterComponent("demo_service", [this]() {
            ComponentHealth health;
            health.component_name = "demo_service";
            
            if (simulate_unhealthy_) {
                health.overall_status = HealthStatus::CRITICAL;
                health.error_message = "Simulated health issue";
            } else {
                health.overall_status = HealthStatus::HEALTHY;
            }
            
            return health;
        });
        
        // 注册数据提供者
        data_sync_manager_->RegisterDataProvider("system_config", [this](const std::string& data_type) {
            std::string config = R"({"node_id": ")" + node_id_ + R"(", "timestamp": ")" + 
                               std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                                   std::chrono::system_clock::now().time_since_epoch()).count()) + R"("})";
            
            return std::vector<uint8_t>(config.begin(), config.end());
        });
        
        data_sync_manager_->RegisterDataProvider("market_data", [this](const std::string& data_type) {
            // 模拟市场数据
            std::vector<uint8_t> data(1024);  // 1KB的模拟数据
            std::fill(data.begin(), data.end(), static_cast<uint8_t>(node_id_.back()));
            return data;
        });
        
        // 注册数据消费者
        data_sync_manager_->RegisterDataConsumer("system_config", [this](const std::string& data_type, const std::vector<uint8_t>& data) {
            std::string config(data.begin(), data.end());
            spdlog::debug("Received config data: {}", config);
            return true;
        });
        
        data_sync_manager_->RegisterDataConsumer("market_data", [this](const std::string& data_type, const std::vector<uint8_t>& data) {
            spdlog::debug("Received market data: {} bytes", data.size());
            return true;
        });
    }
    
    void MonitorThread() {
        spdlog::info("Monitor thread started");
        
        while (running_) {
            try {
                // 定期打印状态
                PrintStatus();
                
                // 定期触发数据同步
                if (failover_manager_->IsPrimary()) {
                    data_sync_manager_->RequestSyncToAll("market_data");
                }
                
                std::this_thread::sleep_for(std::chrono::seconds(10));
                
            } catch (const std::exception& e) {
                spdlog::error("Error in monitor thread: {}", e.what());
            }
        }
        
        spdlog::info("Monitor thread stopped");
    }

private:
    std::string node_id_;
    std::atomic<bool> running_;
    std::atomic<bool> simulate_unhealthy_{false};
    
    std::unique_ptr<FailoverManager> failover_manager_;
    std::unique_ptr<HealthChecker> health_checker_;
    std::unique_ptr<DataSyncManager> data_sync_manager_;
    
    std::thread monitor_thread_;
};

// 全局变量用于信号处理
std::unique_ptr<HighAvailabilityDemo> g_demo;

void SignalHandler(int signal) {
    spdlog::info("Received signal {}, shutting down...", signal);
    if (g_demo) {
        g_demo->Stop();
    }
    exit(0);
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    std::string node_id = "node1";
    std::vector<std::string> peers = {"node2", "node3"};
    
    // 解析命令行参数
    if (argc >= 2) {
        node_id = argv[1];
    }
    
    if (argc >= 3) {
        peers.clear();
        for (int i = 2; i < argc; ++i) {
            peers.push_back(argv[i]);
        }
    }
    
    try {
        g_demo = std::make_unique<HighAvailabilityDemo>(node_id, peers);
        
        if (!g_demo->Start()) {
            spdlog::error("Failed to start high availability demo");
            return 1;
        }
        
        spdlog::info("=== High Availability Demo Running ===");
        spdlog::info("Commands:");
        spdlog::info("  f - Trigger manual failover");
        spdlog::info("  h - Simulate health issue");
        spdlog::info("  s - Print status");
        spdlog::info("  q - Quit");
        spdlog::info("=====================================");
        
        // 交互式命令循环
        char command;
        while (std::cin >> command) {
            switch (command) {
                case 'f':
                case 'F':
                    g_demo->TriggerFailover();
                    break;
                    
                case 'h':
                case 'H':
                    g_demo->SimulateHealthIssue();
                    break;
                    
                case 's':
                case 'S':
                    g_demo->PrintStatus();
                    break;
                    
                case 'q':
                case 'Q':
                    spdlog::info("Quitting...");
                    g_demo->Stop();
                    return 0;
                    
                default:
                    spdlog::info("Unknown command: {}", command);
                    break;
            }
        }
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in main: {}", e.what());
        return 1;
    }
    
    return 0;
}