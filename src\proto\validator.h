#pragma once

#include "data_types.h"
#include <unordered_map>
#include <unordered_set>
#include <chrono>
#include <limits>
#include <cmath>

namespace financial_data {

// 验证结果枚举
enum class ValidationResult {
    VALID,
    INVALID_TIMESTAMP,
    INVALID_PRICE,
    INVALID_SEQUENCE,
    PRICE_ANOMALY,
    TIMESTAMP_ANOMALY,
    SEQUENCE_GAP,
    DUPLICATE_SEQUENCE,
    INVALID_SYMBOL,
    INVALID_VOLUME
};

// 价格异常检测配置
struct PriceAnomalyConfig {
    double max_price_change_ratio = 0.1;    // 最大价格变动比例 (10%)
    double min_price = 0.0001;              // 最小有效价格
    double max_price = 1000000.0;           // 最大有效价格
    double max_spread_ratio = 0.05;         // 最大买卖价差比例 (5%)
    bool enable_price_jump_detection = true; // 启用价格跳跃检测
};

// 时间戳验证配置
struct TimestampConfig {
    int64_t max_future_offset_ns = 1000000000LL;  // 最大未来时间偏移 (1秒)
    int64_t max_past_offset_ns = 86400000000000LL; // 最大过去时间偏移 (1天)
    bool enable_monotonic_check = true;           // 启用单调性检查
};

// 序列号验证配置
struct SequenceConfig {
    uint32_t max_sequence_gap = 100;        // 最大序列号间隔
    bool allow_sequence_reset = true;       // 允许序列号重置
    uint32_t sequence_reset_threshold = 1000000; // 序列号重置阈值
};

// 数据验证器
class DataValidator {
private:
    PriceAnomalyConfig price_config_;
    TimestampConfig timestamp_config_;
    SequenceConfig sequence_config_;
    
    // 历史数据缓存，用于异常检测
    std::unordered_map<std::string, StandardTick> last_tick_cache_;
    std::unordered_map<std::string, Level2Data> last_level2_cache_;
    std::unordered_map<std::string, uint32_t> last_sequence_cache_;
    std::unordered_map<std::string, int64_t> last_timestamp_cache_;
    
    // 价格历史统计
    struct PriceStats {
        double sum = 0.0;
        double sum_squares = 0.0;
        uint32_t count = 0;
        double min_price = std::numeric_limits<double>::max();
        double max_price = std::numeric_limits<double>::min();
        
        void Update(double price) {
            sum += price;
            sum_squares += price * price;
            count++;
            min_price = std::min(min_price, price);
            max_price = std::max(max_price, price);
        }
        
        double GetMean() const {
            return count > 0 ? sum / count : 0.0;
        }
        
        double GetStdDev() const {
            if (count < 2) return 0.0;
            double mean = GetMean();
            return std::sqrt((sum_squares - 2 * mean * sum + count * mean * mean) / (count - 1));
        }
    };
    
    std::unordered_map<std::string, PriceStats> price_stats_cache_;
    
public:
    DataValidator() = default;
    
    // 设置配置
    void SetPriceConfig(const PriceAnomalyConfig& config) { price_config_ = config; }
    void SetTimestampConfig(const TimestampConfig& config) { timestamp_config_ = config; }
    void SetSequenceConfig(const SequenceConfig& config) { sequence_config_ = config; }
    
    // 验证StandardTick数据
    ValidationResult ValidateTick(const StandardTick& tick);
    
    // 验证Level2Data数据
    ValidationResult ValidateLevel2(const Level2Data& level2);
    
    // 验证MarketDataWrapper数据
    ValidationResult ValidateMarketData(const MarketDataWrapper& wrapper);
    
    // 验证批量数据
    std::vector<ValidationResult> ValidateBatch(const MarketDataBatch& batch);
    
    // 价格异常检测
    bool DetectPriceAnomaly(const std::string& symbol, double current_price);
    
    // 时间戳验证
    bool ValidateTimestamp(int64_t timestamp_ns, const std::string& symbol = "");
    
    // 序列号验证
    bool ValidateSequence(const std::string& symbol, uint32_t sequence);
    
    // 买卖价差检查
    bool ValidateSpread(const std::vector<PriceLevel>& bids, const std::vector<PriceLevel>& asks);
    
    // 清理缓存
    void ClearCache();
    void ClearSymbolCache(const std::string& symbol);
    
    // 获取统计信息
    size_t GetCacheSize() const;
    PriceStats GetPriceStats(const std::string& symbol) const;
    
    // 验证结果转换为字符串
    static std::string ValidationResultToString(ValidationResult result);
};

// 实时数据质量监控器
class DataQualityMonitor {
private:
    struct QualityMetrics {
        uint64_t total_records = 0;
        uint64_t valid_records = 0;
        uint64_t invalid_timestamp = 0;
        uint64_t invalid_price = 0;
        uint64_t invalid_sequence = 0;
        uint64_t price_anomalies = 0;
        uint64_t timestamp_anomalies = 0;
        uint64_t sequence_gaps = 0;
        uint64_t duplicate_sequences = 0;
        
        double GetValidityRate() const {
            return total_records > 0 ? static_cast<double>(valid_records) / total_records : 0.0;
        }
    };
    
    std::unordered_map<std::string, QualityMetrics> symbol_metrics_;
    QualityMetrics global_metrics_;
    DataValidator validator_;
    
    // 时间窗口统计
    int64_t window_start_time_ = 0;
    int64_t window_duration_ns_ = 60000000000LL; // 1分钟窗口
    
public:
    DataQualityMonitor() {
        window_start_time_ = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();
    }
    
    // 监控单个数据
    ValidationResult MonitorData(const MarketDataWrapper& data);
    
    // 监控批量数据
    std::vector<ValidationResult> MonitorBatch(const MarketDataBatch& batch);
    
    // 获取质量指标
    QualityMetrics GetGlobalMetrics() const { return global_metrics_; }
    QualityMetrics GetSymbolMetrics(const std::string& symbol) const;
    
    // 获取数据质量报告
    std::string GenerateQualityReport() const;
    
    // 重置统计
    void ResetMetrics();
    void ResetSymbolMetrics(const std::string& symbol);
    
    // 设置时间窗口
    void SetTimeWindow(int64_t duration_ns) { window_duration_ns_ = duration_ns; }
    
    // 检查是否需要重置时间窗口
    bool ShouldResetWindow() const;
    
    // 获取配置访问
    DataValidator& GetValidator() { return validator_; }
    const DataValidator& GetValidator() const { return validator_; }
};

// 数据完整性检查器
class DataIntegrityChecker {
private:
    std::unordered_set<std::string> required_fields_;
    std::unordered_map<std::string, std::pair<double, double>> price_ranges_;
    std::unordered_map<std::string, std::pair<uint64_t, uint64_t>> volume_ranges_;
    
public:
    DataIntegrityChecker();
    
    // 设置必需字段
    void SetRequiredFields(const std::vector<std::string>& fields);
    
    // 设置价格范围
    void SetPriceRange(const std::string& symbol, double min_price, double max_price);
    
    // 设置成交量范围
    void SetVolumeRange(const std::string& symbol, uint64_t min_volume, uint64_t max_volume);
    
    // 检查数据完整性
    bool CheckIntegrity(const StandardTick& tick) const;
    bool CheckIntegrity(const Level2Data& level2) const;
    bool CheckIntegrity(const MarketDataWrapper& wrapper) const;
    
    // 检查字段完整性
    bool CheckFieldCompleteness(const StandardTick& tick) const;
    bool CheckFieldCompleteness(const Level2Data& level2) const;
    
    // 检查数值范围
    bool CheckValueRanges(const StandardTick& tick) const;
    bool CheckValueRanges(const Level2Data& level2) const;
    
    // 检查逻辑一致性
    bool CheckLogicalConsistency(const StandardTick& tick) const;
    bool CheckLogicalConsistency(const Level2Data& level2) const;
};

} // namespace financial_data