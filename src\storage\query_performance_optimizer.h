#pragma once

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <mutex>
#include <thread>
#include <queue>
#include <functional>
#include <future>

#include "../data_models.h"
#include "unified_data_access.h"

namespace financial_data {

/**
 * @brief 查询缓存策略
 */
enum class CacheStrategy {
    LRU = 0,        // 最近最少使用
    LFU = 1,        // 最少使用频率
    TTL = 2,        // 基于时间过期
    ADAPTIVE = 3    // 自适应策略
};

/**
 * @brief 查询优化配置
 */
struct QueryOptimizationConfig {
    // 缓存配置
    CacheStrategy cache_strategy = CacheStrategy::LRU;
    size_t max_cache_size = 10000;
    std::chrono::minutes cache_ttl{30};
    double cache_hit_ratio_threshold = 0.8;
    
    // 批量查询配置
    bool enable_batch_optimization = true;
    size_t batch_size = 1000;
    std::chrono::milliseconds batch_timeout{100};
    size_t max_concurrent_batches = 10;
    
    // 分页查询配置
    bool enable_pagination = true;
    size_t default_page_size = 1000;
    size_t max_page_size = 10000;
    std::chrono::minutes cursor_ttl{60};
    
    // 预取配置
    bool enable_prefetching = true;
    size_t prefetch_size = 500;
    double prefetch_threshold = 0.8; // 当缓存命中率低于此值时启用预取
    
    // 查询合并配置
    bool enable_query_coalescing = true;
    std::chrono::milliseconds coalescing_window{50};
    size_t max_coalesced_queries = 20;
    
    // 性能监控配置
    bool enable_performance_monitoring = true;
    std::chrono::seconds monitoring_interval{60};
    size_t slow_query_threshold_ms = 1000;
};

/**
 * @brief 查询性能指标
 */
struct QueryPerformanceMetrics {
    std::atomic<uint64_t> total_queries{0};
    std::atomic<uint64_t> cache_hits{0};
    std::atomic<uint64_t> cache_misses{0};
    std::atomic<uint64_t> batch_queries{0};
    std::atomic<uint64_t> paginated_queries{0};
    std::atomic<uint64_t> prefetch_queries{0};
    std::atomic<uint64_t> coalesced_queries{0};
    std::atomic<uint64_t> slow_queries{0};
    
    std::atomic<double> avg_query_time_ms{0.0};
    std::atomic<double> avg_cache_lookup_time_ms{0.0};
    std::atomic<double> cache_hit_ratio{0.0};
    std::atomic<double> batch_efficiency{0.0};
    
    void Reset() {
        total_queries = 0;
        cache_hits = 0;
        cache_misses = 0;
        batch_queries = 0;
        paginated_queries = 0;
        prefetch_queries = 0;
        coalesced_queries = 0;
        slow_queries = 0;
        avg_query_time_ms = 0.0;
        avg_cache_lookup_time_ms = 0.0;
        cache_hit_ratio = 0.0;
        batch_efficiency = 0.0;
    }
    
    void UpdateQueryMetrics(double query_time_ms, bool is_slow = false) {
        total_queries++;
        if (is_slow) slow_queries++;
        
        // 更新平均查询时间
        uint64_t total = total_queries.load();
        double current_avg = avg_query_time_ms.load();
        double new_avg = (current_avg * (total - 1) + query_time_ms) / total;
        avg_query_time_ms.store(new_avg);
    }
    
    void UpdateCacheMetrics(bool hit, double lookup_time_ms) {
        if (hit) {
            cache_hits++;
        } else {
            cache_misses++;
        }
        
        // 更新缓存命中率
        uint64_t total_cache_ops = cache_hits.load() + cache_misses.load();
        if (total_cache_ops > 0) {
            double hit_ratio = double(cache_hits.load()) / total_cache_ops;
            cache_hit_ratio.store(hit_ratio);
        }
        
        // 更新平均缓存查找时间
        double current_avg = avg_cache_lookup_time_ms.load();
        double new_avg = (current_avg * (total_cache_ops - 1) + lookup_time_ms) / total_cache_ops;
        avg_cache_lookup_time_ms.store(new_avg);
    }
};

/**
 * @brief 高级缓存项
 */
struct AdvancedCacheItem {
    QueryResponse response;
    std::chrono::steady_clock::time_point timestamp;
    std::chrono::steady_clock::time_point last_access;
    std::string query_hash;
    std::atomic<uint32_t> access_count{0};
    double priority_score = 0.0;
    
    bool IsExpired(std::chrono::minutes ttl) const {
        auto now = std::chrono::steady_clock::now();
        return (now - timestamp) > ttl;
    }
    
    void UpdateAccess() {
        last_access = std::chrono::steady_clock::now();
        access_count++;
    }
    
    double CalculatePriorityScore() const {
        auto now = std::chrono::steady_clock::now();
        auto age = std::chrono::duration_cast<std::chrono::minutes>(now - timestamp);
        auto last_access_age = std::chrono::duration_cast<std::chrono::minutes>(now - last_access);
        
        // 综合考虑访问频率、最近访问时间和数据新鲜度
        double frequency_score = std::log(1.0 + access_count.load());
        double recency_score = 1.0 / (1.0 + last_access_age.count());
        double freshness_score = 1.0 / (1.0 + age.count());
        
        return frequency_score * 0.4 + recency_score * 0.4 + freshness_score * 0.2;
    }
};

/**
 * @brief 批量查询请求
 */
struct BatchQueryRequest {
    std::vector<QueryRequest> requests;
    std::promise<std::vector<QueryResponse>> promise;
    std::chrono::steady_clock::time_point submit_time;
    std::string batch_id;
    
    BatchQueryRequest() {
        submit_time = std::chrono::steady_clock::now();
        batch_id = GenerateBatchId();
    }
    
private:
    static std::string GenerateBatchId() {
        static std::atomic<uint64_t> counter{0};
        auto now = std::chrono::steady_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
            now.time_since_epoch()).count();
        return "batch_" + std::to_string(timestamp) + "_" + std::to_string(counter++);
    }
};

/**
 * @brief 分页游标信息
 */
struct PaginationCursor {
    QueryRequest original_request;
    size_t current_offset = 0;
    size_t total_estimated = 0;
    std::chrono::steady_clock::time_point created_time;
    std::chrono::steady_clock::time_point last_access;
    std::string cursor_id;
    
    PaginationCursor() {
        created_time = std::chrono::steady_clock::now();
        last_access = created_time;
        cursor_id = GenerateCursorId();
    }
    
    bool IsExpired(std::chrono::minutes ttl) const {
        auto now = std::chrono::steady_clock::now();
        return (now - created_time) > ttl;
    }
    
    void UpdateAccess() {
        last_access = std::chrono::steady_clock::now();
    }
    
private:
    static std::string GenerateCursorId() {
        static std::atomic<uint64_t> counter{0};
        auto now = std::chrono::steady_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
            now.time_since_epoch()).count();
        return "cursor_" + std::to_string(timestamp) + "_" + std::to_string(counter++);
    }
};

/**
 * @brief 查询性能优化器
 * 
 * 提供高级缓存、批量查询、分页查询、预取和查询合并等性能优化功能
 */
class QueryPerformanceOptimizer {
public:
    explicit QueryPerformanceOptimizer(const QueryOptimizationConfig& config = QueryOptimizationConfig{});
    ~QueryPerformanceOptimizer();
    
    // 初始化和生命周期管理
    bool Initialize(std::shared_ptr<UnifiedDataAccessInterface> data_access);
    void Shutdown();
    bool IsRunning() const { return running_.load(); }
    
    // 优化查询接口
    std::future<QueryResponse> OptimizedQuery(const QueryRequest& request);
    std::future<std::vector<QueryResponse>> BatchQuery(const std::vector<QueryRequest>& requests);
    
    // 分页查询接口
    std::future<QueryResponse> PaginatedQuery(const QueryRequest& request, 
                                             const std::string& cursor = "");
    std::string CreatePaginationCursor(const QueryRequest& request);
    bool ValidateCursor(const std::string& cursor);
    
    // 预取接口
    void EnablePrefetching(const std::string& symbol, const std::string& pattern = "");
    void DisablePrefetching(const std::string& symbol);
    std::future<void> TriggerPrefetch(const QueryRequest& base_request);
    
    // 缓存管理
    void InvalidateCache(const std::string& pattern = "");
    void WarmupCache(const std::vector<QueryRequest>& requests);
    size_t GetCacheSize() const;
    double GetCacheHitRatio() const;
    void OptimizeCache();
    
    // 性能监控
    QueryPerformanceMetrics GetMetrics() const;
    void ResetMetrics();
    std::vector<QueryRequest> GetSlowQueries() const;
    
    // 配置管理
    bool UpdateConfig(const QueryOptimizationConfig& config);
    QueryOptimizationConfig GetConfig() const { return config_; }
    
    // 查询分析
    struct QueryAnalysis {
        std::string query_pattern;
        uint64_t frequency;
        double avg_response_time_ms;
        double cache_hit_ratio;
        std::vector<std::string> optimization_suggestions;
    };
    
    std::vector<QueryAnalysis> AnalyzeQueryPatterns() const;
    std::vector<std::string> GetOptimizationRecommendations() const;

private:
    // 缓存管理
    std::string GenerateQueryHash(const QueryRequest& request) const;
    bool GetFromCache(const std::string& query_hash, QueryResponse& response);
    void PutToCache(const std::string& query_hash, const QueryResponse& response);
    void EvictFromCache(const std::string& query_hash);
    void CleanupExpiredCache();
    void ApplyCacheStrategy();
    
    // 批量查询处理
    void ProcessBatchQueries();
    void ExecuteBatchQuery(const BatchQueryRequest& batch_request);
    std::vector<QueryResponse> MergeBatchResponses(const std::vector<QueryResponse>& responses);
    
    // 分页查询处理
    QueryResponse ProcessPaginatedQuery(const QueryRequest& request, const std::string& cursor);
    void CleanupExpiredCursors();
    
    // 预取处理
    void ProcessPrefetching();
    std::vector<QueryRequest> GeneratePrefetchRequests(const QueryRequest& base_request);
    bool ShouldPrefetch(const std::string& symbol) const;
    
    // 查询合并处理
    void ProcessQueryCoalescing();
    std::vector<QueryRequest> FindSimilarQueries(const QueryRequest& request) const;
    QueryRequest MergeQueryRequests(const std::vector<QueryRequest>& requests) const;
    
    // 性能监控
    void MonitorPerformance();
    void RecordSlowQuery(const QueryRequest& request, double response_time_ms);
    void UpdatePerformanceMetrics();
    
    // 查询模式分析
    void AnalyzeQueryPattern(const QueryRequest& request);
    std::string ExtractQueryPattern(const QueryRequest& request) const;
    
    // 配置和状态
    QueryOptimizationConfig config_;
    std::atomic<bool> running_{false};
    std::atomic<bool> shutdown_requested_{false};
    
    // 数据访问接口
    std::shared_ptr<UnifiedDataAccessInterface> data_access_;
    
    // 高级缓存
    mutable std::mutex cache_mutex_;
    std::unordered_map<std::string, AdvancedCacheItem> advanced_cache_;
    
    // 批量查询队列
    std::mutex batch_mutex_;
    std::condition_variable batch_cv_;
    std::queue<BatchQueryRequest> batch_queue_;
    std::vector<std::thread> batch_workers_;
    
    // 分页游标管理
    mutable std::mutex cursor_mutex_;
    std::unordered_map<std::string, PaginationCursor> pagination_cursors_;
    
    // 预取管理
    std::mutex prefetch_mutex_;
    std::unordered_map<std::string, bool> prefetch_enabled_;
    std::queue<QueryRequest> prefetch_queue_;
    std::thread prefetch_worker_;
    
    // 查询合并
    std::mutex coalescing_mutex_;
    std::unordered_map<std::string, std::vector<QueryRequest>> pending_queries_;
    std::thread coalescing_worker_;
    
    // 性能监控
    mutable QueryPerformanceMetrics metrics_;
    std::mutex slow_queries_mutex_;
    std::vector<std::pair<QueryRequest, double>> slow_queries_;
    std::thread monitoring_worker_;
    
    // 查询模式分析
    mutable std::mutex pattern_mutex_;
    std::unordered_map<std::string, QueryAnalysis> query_patterns_;
    
    // 日志记录器
    std::shared_ptr<spdlog::logger> logger_;
    
    void InitializeLogger();
    void StartWorkerThreads();
    void StopWorkerThreads();
};

/**
 * @brief 查询性能优化器工厂
 */
class QueryPerformanceOptimizerFactory {
public:
    static std::unique_ptr<QueryPerformanceOptimizer> CreateDefault();
    static std::unique_ptr<QueryPerformanceOptimizer> CreateHighThroughput();
    static std::unique_ptr<QueryPerformanceOptimizer> CreateLowLatency();
    static std::unique_ptr<QueryPerformanceOptimizer> CreateFromConfig(const std::string& config_file);
};

} // namespace financial_data