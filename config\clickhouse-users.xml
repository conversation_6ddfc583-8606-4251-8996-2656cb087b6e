<?xml version="1.0"?>
<yandex>
    <users>
        <!-- Admin user with full privileges -->
        <admin>
            <password>password123</password>
            <networks>
                <ip>::/0</ip>
            </networks>
            <profile>default</profile>
            <quota>default</quota>
            <access_management>1</access_management>
        </admin>
        
        <!-- Read-only user for queries -->
        <readonly_user>
            <password>readonly123</password>
            <networks>
                <ip>::/0</ip>
            </networks>
            <profile>readonly</profile>
            <quota>default</quota>
        </readonly_user>
        
        <!-- High performance user for analytics -->
        <analytics_user>
            <password>analytics123</password>
            <networks>
                <ip>::/0</ip>
            </networks>
            <profile>high_performance</profile>
            <quota>default</quota>
        </analytics_user>
        
        <!-- Default user (disabled for security) -->
        <default>
            <password></password>
            <networks>
                <ip>127.0.0.1</ip>
            </networks>
            <profile>default</profile>
            <quota>default</quota>
        </default>
    </users>
</yandex>