# 核心数据模型和协议定义

本模块实现了金融数据服务的核心数据模型、Protocol Buffers协议定义、序列化工具和数据验证功能。

## 文件结构

```
src/proto/
├── market_data.proto      # Protocol Buffers协议定义
├── data_types.h          # 标准化C++数据结构
├── serializer.h/.cpp     # 序列化和反序列化工具
├── validator.h/.cpp      # 数据验证工具
└── README.md            # 本文档
```

## 核心功能

### 1. 数据结构定义

#### StandardTick
标准化的Tick数据结构，支持：
- 纳秒级时间戳 (`timestamp_ns`)
- 基础市场数据（价格、成交量、成交额等）
- 五档买卖盘数据
- 序列号和交易标志

#### Level2Data
Level2深度数据结构，支持：
- 纳秒级时间戳
- 最多10档买卖盘数据
- 每档包含价格、数量、订单数

#### MarketDataWrapper
市场数据包装器，统一处理不同类型的数据：
- 支持Tick和Level2数据类型
- 包含数据源标识和接收时间戳

#### MarketDataBatch
批量数据处理结构：
- 支持混合数据类型批处理
- 预分配内存优化性能
- 批次序列号和时间戳

### 2. Protocol Buffers协议

定义了以下消息类型：
- `PriceLevel`: 价格档位信息
- `TickData`: Tick数据
- `Level2Data`: Level2深度数据
- `MarketData`: 统一市场数据消息
- `MarketDataBatch`: 批量数据消息
- `SubscriptionRequest/Response`: 订阅请求和响应

协议特性：
- 启用Arena分配器优化内存使用
- 优化速度而非大小
- 支持oneof联合类型

### 3. 序列化工具

#### Serializer
静态序列化工具类，提供：
- StandardTick ↔ TickData 转换
- Level2Data ↔ Level2Data 转换
- MarketDataWrapper ↔ MarketData 转换
- 批量数据序列化支持

#### FastSerializer
高性能序列化器，特性：
- 零拷贝缓冲区管理
- 自动容量扩展
- 字符串视图和字节跨度支持

#### ZeroCopyBuffer
零拷贝缓冲区管理器：
- 自动内存管理
- 容量按需扩展
- 零拷贝数据访问

#### MemoryPool
内存池分配器：
- 减少内存分配开销
- 自动池扩展
- 对象重用机制

### 4. 数据验证

#### DataValidator
核心数据验证器，支持：
- **价格异常检测**: 基于历史数据的统计异常检测
- **时间戳验证**: 未来/过去时间检查，单调性验证
- **序列号检查**: 重复、间隔、重置检测
- **买卖价差验证**: 价差比例检查
- **数据完整性**: 必需字段和逻辑一致性检查

配置选项：
```cpp
PriceAnomalyConfig price_config;
price_config.max_price_change_ratio = 0.1;    // 最大价格变动10%
price_config.min_price = 0.01;                // 最小有效价格
price_config.max_price = 10000.0;             // 最大有效价格
price_config.max_spread_ratio = 0.05;         // 最大买卖价差5%

TimestampConfig timestamp_config;
timestamp_config.max_future_offset_ns = 1000000000LL;  // 1秒未来偏移
timestamp_config.max_past_offset_ns = 86400000000000LL; // 1天过去偏移

SequenceConfig sequence_config;
sequence_config.max_sequence_gap = 100;        // 最大序列号间隔
sequence_config.allow_sequence_reset = true;   // 允许序列号重置
```

#### DataQualityMonitor
实时数据质量监控器：
- 实时统计数据质量指标
- 按符号分类统计
- 生成质量报告
- 时间窗口统计

#### DataIntegrityChecker
数据完整性检查器：
- 必需字段检查
- 数值范围验证
- 逻辑一致性检查
- 可配置验证规则

## 使用示例

### 基本数据创建和验证

```cpp
#include "proto/data_types.h"
#include "proto/validator.h"

// 创建Tick数据
StandardTick tick;
tick.symbol = "AAPL";
tick.exchange = "NASDAQ";
tick.SetCurrentTimestamp();
tick.last_price = 150.25;
tick.volume = 1000;
tick.sequence = 1;

// 验证数据
DataValidator validator;
ValidationResult result = validator.ValidateTick(tick);
if (result == ValidationResult::VALID) {
    std::cout << "Data is valid" << std::endl;
}
```

### 序列化和反序列化

```cpp
#include "proto/serializer.h"

// 创建数据
StandardTick tick = CreateTick();
MarketDataWrapper wrapper(tick);

// 序列化
FastSerializer serializer;
bool success = serializer.SerializeToBuffer(wrapper);

// 反序列化
MarketDataWrapper deserialized;
success = serializer.DeserializeFromBuffer(
    serializer.GetSerializedData(), &deserialized);
```

### 批量处理

```cpp
#include "proto/data_types.h"

MarketDataBatch batch;

// 添加数据
StandardTick tick1 = CreateTick("AAPL");
Level2Data level2 = CreateLevel2("MSFT");

batch.AddTick(tick1);
batch.AddLevel2(level2);

// 批量验证
DataValidator validator;
std::vector<ValidationResult> results = validator.ValidateBatch(batch);
```

### 质量监控

```cpp
#include "proto/validator.h"

DataQualityMonitor monitor;

// 监控数据
for (const auto& data : market_data_stream) {
    ValidationResult result = monitor.MonitorData(data);
    // 处理验证结果...
}

// 生成报告
std::string report = monitor.GenerateQualityReport();
std::cout << report << std::endl;
```

## 性能特性

1. **零拷贝操作**: 使用字符串视图和字节跨度避免不必要的内存拷贝
2. **内存池**: 减少频繁的内存分配和释放开销
3. **批量处理**: 支持批量序列化和验证，提高吞吐量
4. **预分配**: 容器预分配内存，减少动态扩展开销
5. **Protocol Buffers优化**: 启用Arena分配器和速度优化

## 验证规则

### 价格异常检测
- 基于历史价格的3-sigma统计检测
- 可配置的最大价格变动比例
- 价格范围检查

### 时间戳验证
- 未来时间偏移检查
- 历史时间范围验证
- 单调性检查（可选）

### 序列号验证
- 重复序列号检测
- 序列号间隔检查
- 序列号重置支持

### 数据完整性
- 必需字段完整性
- 数值范围验证
- 逻辑一致性检查（如成交额与价格、成交量的一致性）

## 扩展性

该模块设计为可扩展的：
1. 可以轻松添加新的数据类型
2. 验证规则可以通过配置调整
3. 支持自定义序列化格式
4. 可以添加新的质量监控指标

## 依赖项

- Protocol Buffers (libprotobuf)
- C++17标准库
- Google Test (测试)

## 编译

```bash
# 生成Protocol Buffers文件
protoc --cpp_out=. market_data.proto

# 使用CMake编译
mkdir build && cd build
cmake ..
make proto
```