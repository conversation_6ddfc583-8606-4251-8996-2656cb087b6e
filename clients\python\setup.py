#!/usr/bin/env python3
"""
Setup script for Financial Data SDK Python Client
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="financial-data-sdk",
    version="1.0.0",
    author="Financial Data Service Team",
    author_email="<EMAIL>",
    description="A comprehensive Python SDK for accessing real-time and historical financial market data",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/financial-data-service/python-sdk",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Financial and Insurance Industry",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Information Analysis",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=1.0.0",
            "isort>=5.0.0",
        ],
        "notebooks": [
            "jupyter>=1.0.0",
            "ipywidgets>=8.0.0",
            "plotly>=5.0.0",
        ],
        "visualization": [
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
            "plotly>=5.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "financial-data-cli=financial_data_sdk.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "financial_data_sdk": [
            "*.md",
            "examples/*.py",
            "notebooks/*.ipynb",
        ],
    },
    keywords=[
        "financial-data",
        "market-data",
        "trading",
        "quantitative-finance",
        "technical-analysis",
        "pandas",
        "numpy",
        "asyncio",
        "grpc",
        "real-time",
        "historical-data",
        "tick-data",
        "kline-data",
        "technical-indicators",
    ],
    project_urls={
        "Bug Reports": "https://github.com/financial-data-service/python-sdk/issues",
        "Source": "https://github.com/financial-data-service/python-sdk",
        "Documentation": "https://financial-data-sdk.readthedocs.io/",
        "Examples": "https://github.com/financial-data-service/python-sdk/tree/main/examples",
    },
)