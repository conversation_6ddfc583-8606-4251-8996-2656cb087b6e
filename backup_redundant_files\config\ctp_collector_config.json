{"collector": {"name": "CTP数据采集器", "version": "2.0.0", "mode": "production", "timezone": "Asia/Shanghai", "max_workers": 8, "task_timeout": 300, "retry_attempts": 3, "retry_delay": 60, "heartbeat_interval": 30}, "data_sources": {"pytdx": {"enabled": true, "servers": [{"host": "**************", "port": 7709, "name": "通达信1", "weight": 1}, {"host": "*************", "port": 7709, "name": "通达信2", "weight": 1}, {"host": "*************", "port": 7709, "name": "通达信3", "weight": 1}, {"host": "**************", "port": 7709, "name": "通达信4", "weight": 1}, {"host": "*************", "port": 7709, "name": "通达信5", "weight": 1}], "timeout": 30, "retry_count": 3, "connection_pool_size": 10, "max_concurrent_requests": 50, "rate_limit_per_second": 20}, "ctp": {"enabled": false, "broker_id": "9999", "user_id": "", "password": "", "app_id": "", "auth_code": "", "front_addr": "tcp://***************:10130", "name_server": "tcp://***************:12001"}}, "storage": {"redis": {"host": "redis", "port": 6379, "db": 0, "password": null, "max_connections": 20, "socket_timeout": 30, "socket_connect_timeout": 30, "retry_on_timeout": true, "health_check_interval": 60}, "clickhouse": {"host": "clickhouse", "port": 9000, "database": "ctp_data", "user": "ctp_user", "password": "ctp_password123", "timeout": 60, "max_connections": 10, "compression": true, "secure": false}, "kafka": {"enabled": false, "bootstrap_servers": ["kafka:29092"], "topics": {"realtime_quotes": "ctp.quotes.realtime", "kline_data": "ctp.kline", "tick_data": "ctp.tick", "futures_data": "ctp.futures"}, "producer_config": {"acks": "all", "retries": 3, "batch_size": 16384, "linger_ms": 10, "buffer_memory": 33554432}}}, "data_collection": {"stock_data": {"enabled": true, "markets": ["SZ", "SH"], "data_types": ["kline", "realtime", "tick"], "kline_periods": ["1min", "5min", "15min", "30min", "60min", "daily"], "collection_hours": {"start": "09:00", "end": "15:30", "break_start": "11:30", "break_end": "13:00"}, "batch_size": 100, "concurrent_limit": 10}, "futures_data": {"enabled": true, "exchanges": ["SHFE", "DCE", "CZCE", "CFFEX", "INE"], "data_types": ["kline", "realtime", "tick"], "kline_periods": ["1min", "5min", "15min", "30min", "60min", "daily"], "collection_hours": {"day_start": "09:00", "day_end": "15:15", "night_start": "21:00", "night_end": "02:30"}, "batch_size": 50, "concurrent_limit": 5}, "index_data": {"enabled": true, "indices": ["000001", "399001", "399006"], "data_types": ["kline", "realtime"], "kline_periods": ["1min", "5min", "15min", "30min", "60min", "daily"], "batch_size": 20, "concurrent_limit": 3}}, "tasks": {"symbol_list_update": {"enabled": true, "schedule": "0 8 * * *", "description": "更新代码表", "timeout": 600, "data_types": ["stock", "futures", "index", "fund", "bond"]}, "daily_data_update": {"enabled": true, "schedule": "0 18 * * 1-5", "description": "日线数据更新", "timeout": 3600, "data_types": ["stock", "futures", "index"], "lookback_days": 5}, "realtime_data_update": {"enabled": true, "schedule": "*/30 9-15 * * 1-5", "description": "实时数据更新", "timeout": 300, "data_types": ["stock", "futures", "index"], "max_symbols": 500}, "minute_data_update": {"enabled": true, "schedule": "*/1 9-15 * * 1-5", "description": "分钟数据更新", "timeout": 120, "data_types": ["stock", "futures"], "max_symbols": 200}, "futures_night_session": {"enabled": true, "schedule": "*/5 21-23,0-2 * * 1-5", "description": "期货夜盘数据", "timeout": 180, "data_types": ["futures"], "max_symbols": 100}}, "monitoring": {"enabled": true, "metrics_port": 8080, "health_check_interval": 60, "performance_tracking": true, "prometheus": {"enabled": true, "port": 8080, "path": "/metrics"}, "alerts": {"enabled": true, "channels": ["log", "webhook"], "webhook_url": "", "thresholds": {"error_rate": 0.05, "response_time": 5.0, "memory_usage": 0.8, "disk_usage": 0.9}}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "/app/logs/ctp_collector.log", "max_size": "100MB", "backup_count": 10, "console": true, "structured": true, "fields": {"service": "ctp-collector", "version": "2.0.0", "environment": "production"}}, "cache": {"enabled": true, "type": "redis", "ttl": {"symbol_list": 86400, "realtime_quotes": 60, "kline_data": 3600, "company_info": 604800}, "max_memory": "512MB", "eviction_policy": "allkeys-lru"}, "features": {"auto_symbol_discovery": true, "data_validation": true, "error_recovery": true, "performance_optimization": true, "concurrent_processing": true, "smart_retry": true, "circuit_breaker": true, "rate_limiting": true}, "limits": {"max_concurrent_tasks": 20, "max_memory_usage": "4GB", "max_disk_usage": "50GB", "rate_limit_per_second": 100, "max_connections_per_server": 10, "request_timeout": 30, "batch_timeout": 300}, "security": {"enable_ssl": false, "ssl_cert_path": "", "ssl_key_path": "", "api_key_required": false, "allowed_ips": [], "rate_limit_by_ip": true}}