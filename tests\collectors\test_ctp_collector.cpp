#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <thread>
#include <chrono>
#include <fstream>
#include <filesystem>

#include "collectors/ctp_collector.h"

using namespace financial_data;
using namespace testing;

class CTPCollectorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试配置文件
        CreateTestConfig();
        
        // 初始化采集器
        collector_ = std::make_unique<CTPMarketDataCollector>();
        
        // 设置回调函数
        data_received_count_ = 0;
        status_changes_.clear();
        
        collector_->SetDataCallback([this](const MarketDataWrapper& data) {
            OnDataReceived(data);
        });
        
        collector_->SetStatusCallback([this](ConnectionStatus status, const std::string& message) {
            OnStatusChanged(status, message);
        });
    }
    
    void TearDown() override {
        if (collector_) {
            collector_->Shutdown();
        }
        
        // 清理测试文件
        std::filesystem::remove(test_config_path_);
    }
    
    void CreateTestConfig() {
        test_config_path_ = "test_ctp_config.json";
        
        nlohmann::json config = {
            {"ctp", {
                {"front_address", "tcp://127.0.0.1:10131"},
                {"broker_id", "test_broker"},
                {"user_id", "test_user"},
                {"password", "test_password"},
                {"flow_path", "./test_flow/"},
                {"heartbeat_interval", 5},
                {"reconnect_interval", 2},
                {"max_reconnect_attempts", 3},
                {"enable_level2", true}
            }}
        };
        
        std::ofstream file(test_config_path_);
        file << config.dump(4);
        file.close();
    }
    
    void OnDataReceived(const MarketDataWrapper& data) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        received_data_.push_back(data);
        data_received_count_++;
    }
    
    void OnStatusChanged(ConnectionStatus status, const std::string& message) {
        std::lock_guard<std::mutex> lock(status_mutex_);
        status_changes_.emplace_back(status, message);
    }
    
    void WaitForDataCount(int expected_count, int timeout_ms = 5000) {
        auto start = std::chrono::steady_clock::now();
        while (data_received_count_ < expected_count) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            auto elapsed = std::chrono::steady_clock::now() - start;
            if (std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count() > timeout_ms) {
                break;
            }
        }
    }
    
    void WaitForStatusChange(ConnectionStatus expected_status, int timeout_ms = 5000) {
        auto start = std::chrono::steady_clock::now();
        while (true) {
            {
                std::lock_guard<std::mutex> lock(status_mutex_);
                for (const auto& change : status_changes_) {
                    if (change.first == expected_status) {
                        return;
                    }
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            auto elapsed = std::chrono::steady_clock::now() - start;
            if (std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count() > timeout_ms) {
                break;
            }
        }
    }

protected:
    std::unique_ptr<CTPMarketDataCollector> collector_;
    std::string test_config_path_;
    
    // 数据接收统计
    std::atomic<int> data_received_count_;
    std::vector<MarketDataWrapper> received_data_;
    std::mutex data_mutex_;
    
    // 状态变化记录
    std::vector<std::pair<ConnectionStatus, std::string>> status_changes_;
    std::mutex status_mutex_;
};

// 测试配置加载
TEST_F(CTPCollectorTest, ConfigurationLoading) {
    // 测试从文件加载配置
    EXPECT_TRUE(collector_->Initialize(test_config_path_));
    
    // 测试无效配置文件
    EXPECT_FALSE(collector_->Initialize("nonexistent_config.json"));
    
    // 测试直接配置对象
    CTPConfig config;
    config.front_address = "tcp://127.0.0.1:10131";
    config.broker_id = "test_broker";
    config.user_id = "test_user";
    config.password = "test_password";
    config.flow_path = "./test_flow/";
    
    EXPECT_TRUE(collector_->Initialize(config));
    
    // 测试无效配置
    CTPConfig invalid_config;
    EXPECT_FALSE(collector_->Initialize(invalid_config));
}

// 测试连接管理
TEST_F(CTPCollectorTest, ConnectionManagement) {
    ASSERT_TRUE(collector_->Initialize(test_config_path_));
    
    // 初始状态应该是未连接
    EXPECT_EQ(collector_->GetConnectionStatus(), ConnectionStatus::DISCONNECTED);
    EXPECT_FALSE(collector_->IsConnected());
    
    // 测试连接
    collector_->Start();
    EXPECT_TRUE(collector_->Connect());
    
    // 等待连接状态变化
    WaitForStatusChange(ConnectionStatus::CONNECTED, 3000);
    
    EXPECT_EQ(collector_->GetConnectionStatus(), ConnectionStatus::CONNECTED);
    EXPECT_TRUE(collector_->IsConnected());
    
    // 测试断开连接
    collector_->Disconnect();
    EXPECT_EQ(collector_->GetConnectionStatus(), ConnectionStatus::DISCONNECTED);
    EXPECT_FALSE(collector_->IsConnected());
}

// 测试订阅管理
TEST_F(CTPCollectorTest, SubscriptionManagement) {
    ASSERT_TRUE(collector_->Initialize(test_config_path_));
    collector_->Start();
    ASSERT_TRUE(collector_->Connect());
    
    WaitForStatusChange(ConnectionStatus::CONNECTED, 3000);
    
    // 测试单个合约订阅
    EXPECT_TRUE(collector_->Subscribe("CU2409"));
    
    auto subscribed = collector_->GetSubscribedSymbols();
    EXPECT_EQ(subscribed.size(), 1);
    EXPECT_EQ(subscribed[0], "CU2409");
    
    // 测试批量订阅
    std::vector<std::string> symbols = {"AL2409", "ZN2409", "AU2409"};
    EXPECT_TRUE(collector_->Subscribe(symbols));
    
    subscribed = collector_->GetSubscribedSymbols();
    EXPECT_EQ(subscribed.size(), 4);
    
    // 测试重复订阅
    EXPECT_TRUE(collector_->Subscribe("CU2409"));
    subscribed = collector_->GetSubscribedSymbols();
    EXPECT_EQ(subscribed.size(), 4); // 不应该增加
    
    // 测试取消订阅
    EXPECT_TRUE(collector_->Unsubscribe("CU2409"));
    subscribed = collector_->GetSubscribedSymbols();
    EXPECT_EQ(subscribed.size(), 3);
    
    // 测试清空订阅
    collector_->ClearSubscriptions();
    subscribed = collector_->GetSubscribedSymbols();
    EXPECT_TRUE(subscribed.empty());
}

// 测试数据接收和处理
TEST_F(CTPCollectorTest, DataReceptionAndProcessing) {
    ASSERT_TRUE(collector_->Initialize(test_config_path_));
    collector_->Start();
    ASSERT_TRUE(collector_->Connect());
    
    WaitForStatusChange(ConnectionStatus::CONNECTED, 3000);
    
    // 订阅合约
    EXPECT_TRUE(collector_->Subscribe("CU2409"));
    
    // 模拟接收数据（在实际实现中，这会通过CTP API回调触发）
    // 这里我们直接调用OnRtnDepthMarketData来模拟
    for (int i = 0; i < 10; ++i) {
        collector_->OnRtnDepthMarketData(nullptr); // 传入模拟数据
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 等待数据处理
    WaitForDataCount(10, 5000);
    
    // 验证接收到的数据
    EXPECT_GE(data_received_count_.load(), 10);
    
    {
        std::lock_guard<std::mutex> lock(data_mutex_);
        EXPECT_FALSE(received_data_.empty());
        
        // 验证数据格式
        for (const auto& data : received_data_) {
            EXPECT_TRUE(data.IsValid());
            EXPECT_EQ(data.source, "CTP");
            
            if (data.type == MarketDataWrapper::DataType::TICK) {
                EXPECT_FALSE(data.tick_data.symbol.empty());
                EXPECT_FALSE(data.tick_data.exchange.empty());
                EXPECT_GT(data.tick_data.timestamp_ns, 0);
            }
        }
    }
}

// 测试数据验证
TEST_F(CTPCollectorTest, DataValidation) {
    ASSERT_TRUE(collector_->Initialize(test_config_path_));
    
    // 测试有效的Tick数据
    StandardTick valid_tick;
    valid_tick.SetCurrentTimestamp();
    valid_tick.symbol = "CU2409";
    valid_tick.exchange = "SHFE";
    valid_tick.last_price = 78560.0;
    valid_tick.volume = 1000;
    valid_tick.sequence = 1;
    
    MarketDataWrapper valid_wrapper(valid_tick);
    EXPECT_TRUE(valid_wrapper.IsValid());
    
    // 测试无效的Tick数据
    StandardTick invalid_tick;
    // 缺少必要字段
    MarketDataWrapper invalid_wrapper(invalid_tick);
    EXPECT_FALSE(invalid_wrapper.IsValid());
    
    // 测试价格验证
    EXPECT_TRUE(ctp_utils::ValidatePrice(78560.0));
    EXPECT_FALSE(ctp_utils::ValidatePrice(-100.0));
    EXPECT_FALSE(ctp_utils::ValidatePrice(std::numeric_limits<double>::infinity()));
    
    // 测试成交量验证
    EXPECT_TRUE(ctp_utils::ValidateVolume(1000));
    EXPECT_FALSE(ctp_utils::ValidateVolume(0));
    
    // 测试成交额验证
    EXPECT_TRUE(ctp_utils::ValidateTurnover(1000000.0));
    EXPECT_TRUE(ctp_utils::ValidateTurnover(0.0));
    EXPECT_FALSE(ctp_utils::ValidateTurnover(std::numeric_limits<double>::quiet_NaN()));
}

// 测试统计信息
TEST_F(CTPCollectorTest, Statistics) {
    ASSERT_TRUE(collector_->Initialize(test_config_path_));
    collector_->Start();
    
    // 获取初始统计信息
    auto stats = collector_->GetStatistics();
    EXPECT_EQ(stats.total_received, 0);
    EXPECT_EQ(stats.total_processed, 0);
    EXPECT_EQ(stats.total_errors, 0);
    EXPECT_GE(stats.uptime_seconds, 0.0);
    
    // 模拟处理一些数据
    ASSERT_TRUE(collector_->Connect());
    WaitForStatusChange(ConnectionStatus::CONNECTED, 3000);
    
    EXPECT_TRUE(collector_->Subscribe("CU2409"));
    
    // 模拟接收数据
    for (int i = 0; i < 5; ++i) {
        collector_->OnRtnDepthMarketData(nullptr);
    }
    
    WaitForDataCount(5, 3000);
    
    // 验证统计信息更新
    stats = collector_->GetStatistics();
    EXPECT_GE(stats.total_received, 5);
    EXPECT_GE(stats.total_processed, 5);
    EXPECT_GT(stats.uptime_seconds, 0.0);
    
    if (stats.uptime_seconds > 0) {
        EXPECT_GE(stats.messages_per_second, 0.0);
    }
    
    // 测试统计重置
    collector_->ResetStatistics();
    stats = collector_->GetStatistics();
    EXPECT_EQ(stats.total_received, 0);
    EXPECT_EQ(stats.total_processed, 0);
    EXPECT_EQ(stats.total_errors, 0);
}

// 测试错误处理
TEST_F(CTPCollectorTest, ErrorHandling) {
    ASSERT_TRUE(collector_->Initialize(test_config_path_));
    collector_->Start();
    
    // 测试CTP API错误
    collector_->OnRspError(nullptr, 12345, true);
    
    auto stats = collector_->GetStatistics();
    EXPECT_GT(stats.total_errors, 0);
    
    // 测试错误码转换
    EXPECT_EQ(ctp_utils::GetErrorMessage(0), "Success");
    EXPECT_EQ(ctp_utils::GetErrorMessage(-1), "Network error");
    EXPECT_NE(ctp_utils::GetErrorMessage(999), "Success");
    
    // 测试可恢复错误判断
    EXPECT_TRUE(ctp_utils::IsRecoverableError(-1));  // 网络错误
    EXPECT_TRUE(ctp_utils::IsRecoverableError(-2));  // 连接错误
    EXPECT_FALSE(ctp_utils::IsRecoverableError(-3)); // 登录错误
}

// 测试工厂类
TEST_F(CTPCollectorTest, Factory) {
    // 测试从配置创建
    CTPConfig config;
    config.front_address = "tcp://127.0.0.1:10131";
    config.broker_id = "test_broker";
    config.user_id = "test_user";
    config.password = "test_password";
    config.flow_path = "./test_flow/";
    
    auto collector = CTPCollectorFactory::Create(config);
    EXPECT_NE(collector, nullptr);
    
    // 测试从文件创建
    auto collector2 = CTPCollectorFactory::CreateFromFile(test_config_path_);
    EXPECT_NE(collector2, nullptr);
    
    // 测试无效配置
    CTPConfig invalid_config;
    auto invalid_collector = CTPCollectorFactory::Create(invalid_config);
    EXPECT_EQ(invalid_collector, nullptr);
    
    // 测试环境验证
    EXPECT_TRUE(CTPCollectorFactory::ValidateCTPEnvironment());
    
    // 测试版本获取
    std::string version = CTPCollectorFactory::GetCTPVersion();
    EXPECT_FALSE(version.empty());
}

// 测试辅助工具函数
TEST_F(CTPCollectorTest, UtilityFunctions) {
    // 测试交易所识别
    EXPECT_EQ(ctp_utils::GetExchangeFromSymbol("CU2409"), "SHFE");
    EXPECT_EQ(ctp_utils::GetExchangeFromSymbol("A2409"), "DCE");
    EXPECT_EQ(ctp_utils::GetExchangeFromSymbol("CF2409"), "CZCE");
    EXPECT_EQ(ctp_utils::GetExchangeFromSymbol("IF2409"), "CFFEX");
    EXPECT_EQ(ctp_utils::GetExchangeFromSymbol("INVALID"), "UNKNOWN");
    
    // 测试合约代码验证
    EXPECT_TRUE(ctp_utils::IsValidCTPSymbol("CU2409"));
    EXPECT_TRUE(ctp_utils::IsValidCTPSymbol("AL2409"));
    EXPECT_TRUE(ctp_utils::IsValidCTPSymbol("IF2409"));
    EXPECT_FALSE(ctp_utils::IsValidCTPSymbol(""));
    EXPECT_FALSE(ctp_utils::IsValidCTPSymbol("INVALID"));
    EXPECT_FALSE(ctp_utils::IsValidCTPSymbol("CU"));
    
    // 测试时间转换
    int64_t timestamp = StandardTick::GetCurrentTimestampNs();
    std::string ctp_time = ctp_utils::NanosecondsToCTPTime(timestamp);
    EXPECT_FALSE(ctp_time.empty());
    EXPECT_EQ(ctp_time.length(), 8); // HH:MM:SS格式
    
    int64_t converted = ctp_utils::CTPTimeToNanoseconds("09:30:00", 123);
    EXPECT_GT(converted, 0);
}

// 性能测试
TEST_F(CTPCollectorTest, PerformanceTest) {
    ASSERT_TRUE(collector_->Initialize(test_config_path_));
    collector_->Start();
    ASSERT_TRUE(collector_->Connect());
    
    WaitForStatusChange(ConnectionStatus::CONNECTED, 3000);
    EXPECT_TRUE(collector_->Subscribe("CU2409"));
    
    // 测试高频数据处理
    const int message_count = 1000;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < message_count; ++i) {
        collector_->OnRtnDepthMarketData(nullptr);
    }
    
    WaitForDataCount(message_count, 10000);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double messages_per_second = static_cast<double>(message_count) / 
                                (static_cast<double>(duration.count()) / 1000000.0);
    
    // 验证处理性能（应该能处理至少10000条/秒）
    EXPECT_GT(messages_per_second, 10000.0);
    
    // 验证延迟（平均处理时间应该小于100微秒）
    double avg_latency_us = static_cast<double>(duration.count()) / message_count;
    EXPECT_LT(avg_latency_us, 100.0);
    
    std::cout << "Performance: " << messages_per_second << " msg/s, "
              << "Avg latency: " << avg_latency_us << " μs" << std::endl;
}