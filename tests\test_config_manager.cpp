#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <fstream>
#include <filesystem>
#include <thread>
#include <chrono>
#include "../src/config/config_manager.h"
#include "../src/config/config_validators.h"

using namespace config;
using namespace testing;

class ConfigManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_config_file_ = "test_config.json";
        test_config_content_ = R"({
            "server": {
                "host": "localhost",
                "port": 8080,
                "threads": 4
            },
            "redis": {
                "host": "127.0.0.1",
                "port": 6379,
                "database": 0
            },
            "logging": {
                "level": "info",
                "file": "test.log"
            }
        })";
        
        // 创建测试配置文件
        std::ofstream file(test_config_file_);
        file << test_config_content_;
        file.close();
        
        // 重置ConfigManager实例
        ConfigManager::Instance().Shutdown();
    }
    
    void TearDown() override {
        ConfigManager::Instance().Shutdown();
        
        // 清理测试文件
        if (std::filesystem::exists(test_config_file_)) {
            std::filesystem::remove(test_config_file_);
        }
        
        // 清理其他测试文件
        for (const auto& file : test_files_to_cleanup_) {
            if (std::filesystem::exists(file)) {
                std::filesystem::remove(file);
            }
        }
    }
    
    void AddFileToCleanup(const std::string& file) {
        test_files_to_cleanup_.push_back(file);
    }
    
    std::string test_config_file_;
    std::string test_config_content_;
    std::vector<std::string> test_files_to_cleanup_;
};

// Mock配置变更监听器
class MockConfigChangeListener : public ConfigChangeListener {
public:
    MOCK_METHOD(void, OnConfigChanged, (const ConfigChangeEvent& event), (override));
};

// Mock配置验证器
class MockConfigValidator : public ConfigValidator {
public:
    MOCK_METHOD(ValidationResult, Validate, (const nlohmann::json& config), (const, override));
    MOCK_METHOD(std::string, GetValidatorName, (), (const, override));
};

// 测试配置管理器初始化
TEST_F(ConfigManagerTest, InitializeAndLoadConfig) {
    auto& config_manager = ConfigManager::Instance();
    
    EXPECT_TRUE(config_manager.Initialize(test_config_file_));
    
    // 验证配置加载
    EXPECT_EQ(config_manager.GetValue<std::string>("server.host"), "localhost");
    EXPECT_EQ(config_manager.GetValue<int>("server.port"), 8080);
    EXPECT_EQ(config_manager.GetValue<int>("server.threads"), 4);
    EXPECT_EQ(config_manager.GetValue<std::string>("redis.host"), "127.0.0.1");
    EXPECT_EQ(config_manager.GetValue<int>("redis.port"), 6379);
}

// 测试配置值设置和获取
TEST_F(ConfigManagerTest, SetAndGetValues) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    // 测试设置新值
    EXPECT_TRUE(config_manager.SetValue("new_key", "new_value"));
    EXPECT_EQ(config_manager.GetValue<std::string>("new_key"), "new_value");
    
    // 测试修改现有值
    EXPECT_TRUE(config_manager.SetValue("server.port", 9090));
    EXPECT_EQ(config_manager.GetValue<int>("server.port"), 9090);
    
    // 测试嵌套键设置
    EXPECT_TRUE(config_manager.SetValue("database.mysql.host", "mysql.example.com"));
    EXPECT_EQ(config_manager.GetValue<std::string>("database.mysql.host"), "mysql.example.com");
    
    // 测试默认值
    EXPECT_EQ(config_manager.GetValue<std::string>("non_existent_key", "default"), "default");
    EXPECT_EQ(config_manager.GetValue<int>("non_existent_int", 42), 42);
}

// 测试配置键存在性检查
TEST_F(ConfigManagerTest, HasKeyAndRemoveKey) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    // 测试键存在性
    EXPECT_TRUE(config_manager.HasKey("server.host"));
    EXPECT_TRUE(config_manager.HasKey("redis.port"));
    EXPECT_FALSE(config_manager.HasKey("non_existent_key"));
    
    // 测试删除键
    EXPECT_TRUE(config_manager.RemoveKey("server.threads"));
    EXPECT_FALSE(config_manager.HasKey("server.threads"));
    EXPECT_FALSE(config_manager.RemoveKey("non_existent_key"));
}

// 测试配置节操作
TEST_F(ConfigManagerTest, SectionOperations) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    // 获取配置节
    auto server_config = config_manager.GetSection("server");
    EXPECT_TRUE(server_config.contains("host"));
    EXPECT_TRUE(server_config.contains("port"));
    
    // 设置新配置节
    nlohmann::json new_section = {
        {"username", "admin"},
        {"password", "secret"},
        {"timeout", 30}
    };
    
    EXPECT_TRUE(config_manager.SetSection("database", new_section));
    
    auto database_config = config_manager.GetSection("database");
    EXPECT_EQ(database_config["username"], "admin");
    EXPECT_EQ(database_config["password"], "secret");
    EXPECT_EQ(database_config["timeout"], 30);
    
    // 删除配置节
    EXPECT_TRUE(config_manager.RemoveSection("database"));
    EXPECT_TRUE(config_manager.GetSection("database").empty());
    
    // 获取配置节名称
    auto section_names = config_manager.GetSectionNames();
    EXPECT_THAT(section_names, Contains("server"));
    EXPECT_THAT(section_names, Contains("redis"));
    EXPECT_THAT(section_names, Contains("logging"));
}

// 测试配置验证
TEST_F(ConfigManagerTest, ConfigValidation) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    // 注册验证器
    auto server_validator = std::make_shared<ServerConfigValidator>();
    auto redis_validator = std::make_shared<RedisConfigValidator>();
    
    EXPECT_TRUE(config_manager.RegisterValidator("server", server_validator));
    EXPECT_TRUE(config_manager.RegisterValidator("redis", redis_validator));
    
    // 验证配置
    auto result = config_manager.ValidateConfig();
    EXPECT_TRUE(result.is_valid);
    
    // 验证特定节
    auto server_result = config_manager.ValidateSection("server");
    EXPECT_TRUE(server_result.is_valid);
    
    // 设置无效配置并验证
    config_manager.SetValue("server.port", -1);
    auto invalid_result = config_manager.ValidateSection("server");
    EXPECT_FALSE(invalid_result.is_valid);
    EXPECT_FALSE(invalid_result.errors.empty());
    
    // 注销验证器
    config_manager.UnregisterValidator("server");
    config_manager.UnregisterValidator("redis");
}

// 测试配置变更监听
TEST_F(ConfigManagerTest, ConfigChangeListener) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    auto mock_listener = std::make_shared<MockConfigChangeListener>();
    
    // 期望收到配置变更通知
    EXPECT_CALL(*mock_listener, OnConfigChanged(_))
        .Times(AtLeast(1))
        .WillRepeatedly([](const ConfigChangeEvent& event) {
            EXPECT_EQ(event.type, ConfigChangeType::MODIFIED);
            EXPECT_EQ(event.key, "server.port");
        });
    
    config_manager.RegisterChangeListener(mock_listener);
    
    // 修改配置触发通知
    config_manager.SetValue("server.port", 9090);
    
    config_manager.UnregisterChangeListener(mock_listener);
}

// 测试版本管理
TEST_F(ConfigManagerTest, VersionManagement) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    // 创建快照
    std::string version1 = config_manager.CreateSnapshot("Initial version");
    EXPECT_FALSE(version1.empty());
    
    // 修改配置
    config_manager.SetValue("server.port", 9090);
    config_manager.SetValue("new_key", "new_value");
    
    // 创建另一个快照
    std::string version2 = config_manager.CreateSnapshot("Modified version");
    EXPECT_FALSE(version2.empty());
    EXPECT_NE(version1, version2);
    
    // 获取版本历史
    auto history = config_manager.GetVersionHistory();
    EXPECT_EQ(history.size(), 2);
    
    // 恢复到第一个版本
    EXPECT_TRUE(config_manager.RestoreFromSnapshot(version1));
    EXPECT_EQ(config_manager.GetValue<int>("server.port"), 8080);
    EXPECT_FALSE(config_manager.HasKey("new_key"));
    
    // 删除快照
    EXPECT_TRUE(config_manager.DeleteSnapshot(version2));
    history = config_manager.GetVersionHistory();
    EXPECT_EQ(history.size(), 1);
    
    // 设置最大版本历史
    config_manager.SetMaxVersionHistory(1);
    config_manager.CreateSnapshot("New version");
    history = config_manager.GetVersionHistory();
    EXPECT_EQ(history.size(), 1);
}

// 测试配置合并
TEST_F(ConfigManagerTest, ConfigMerging) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    // 准备要合并的配置
    nlohmann::json merge_config = {
        {"server", {
            {"port", 9090},
            {"ssl", true}
        }},
        {"database", {
            {"host", "db.example.com"},
            {"port", 3306}
        }}
    };
    
    // 合并配置（覆盖模式）
    EXPECT_TRUE(config_manager.MergeConfig(merge_config, true));
    
    EXPECT_EQ(config_manager.GetValue<int>("server.port"), 9090);
    EXPECT_EQ(config_manager.GetValue<bool>("server.ssl"), true);
    EXPECT_EQ(config_manager.GetValue<std::string>("server.host"), "localhost"); // 保持原值
    EXPECT_EQ(config_manager.GetValue<std::string>("database.host"), "db.example.com");
    
    // 从文件合并
    std::string merge_file = "merge_config.json";
    AddFileToCleanup(merge_file);
    
    std::ofstream file(merge_file);
    file << merge_config.dump(4);
    file.close();
    
    EXPECT_TRUE(config_manager.MergeFromFile(merge_file, false));
}

// 测试环境变量支持
TEST_F(ConfigManagerTest, EnvironmentVariables) {
    auto& config_manager = ConfigManager::Instance();
    
    // 设置环境变量
    setenv("TEST_HOST", "env.example.com", 1);
    setenv("MARKET_DATA_PORT", "8888", 1);
    
    // 创建包含环境变量的配置
    std::string env_config_file = "env_config.json";
    AddFileToCleanup(env_config_file);
    
    nlohmann::json env_config = {
        {"server", {
            {"host", "${TEST_HOST}"},
            {"port", "${PORT}"},
            {"backup_port", "${MARKET_DATA_PORT}"}
        }}
    };
    
    std::ofstream file(env_config_file);
    file << env_config.dump(4);
    file.close();
    
    // 启用环境变量支持
    config_manager.EnableEnvironmentVariables(true);
    config_manager.SetEnvironmentPrefix("MARKET_DATA_");
    
    EXPECT_TRUE(config_manager.Initialize(env_config_file));
    
    // 验证环境变量解析
    EXPECT_EQ(config_manager.GetValue<std::string>("server.host"), "env.example.com");
    EXPECT_EQ(config_manager.GetValue<std::string>("server.backup_port"), "8888");
    
    // 清理环境变量
    unsetenv("TEST_HOST");
    unsetenv("MARKET_DATA_PORT");
}

// 测试配置导出
TEST_F(ConfigManagerTest, ConfigExport) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    // 导出为字符串
    std::string exported = config_manager.ExportToString(true);
    EXPECT_FALSE(exported.empty());
    EXPECT_THAT(exported, HasSubstr("\"server\""));
    EXPECT_THAT(exported, HasSubstr("\"redis\""));
    
    // 导出到文件
    std::string export_file = "exported_config.json";
    AddFileToCleanup(export_file);
    
    EXPECT_TRUE(config_manager.ExportToFile(export_file, true));
    EXPECT_TRUE(std::filesystem::exists(export_file));
    
    // 验证导出的文件可以重新加载
    ConfigManager test_manager;
    EXPECT_TRUE(test_manager.LoadFromFile(export_file));
}

// 测试统计信息
TEST_F(ConfigManagerTest, Statistics) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    auto stats = config_manager.GetStatistics();
    EXPECT_GT(stats.total_keys, 0);
    EXPECT_GT(stats.total_sections, 0);
    
    // 修改配置增加变更计数
    config_manager.SetValue("test_key", "test_value");
    
    auto updated_stats = config_manager.GetStatistics();
    EXPECT_GT(updated_stats.change_count, stats.change_count);
}

// 测试热更新功能
TEST_F(ConfigManagerTest, HotReload) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    auto mock_listener = std::make_shared<MockConfigChangeListener>();
    config_manager.RegisterChangeListener(mock_listener);
    
    // 期望收到重新加载通知
    EXPECT_CALL(*mock_listener, OnConfigChanged(_))
        .WillOnce([](const ConfigChangeEvent& event) {
            EXPECT_EQ(event.type, ConfigChangeType::RELOADED);
        });
    
    // 启用热更新
    config_manager.EnableHotReload(true);
    config_manager.SetFileWatchInterval(std::chrono::milliseconds(100));
    
    EXPECT_TRUE(config_manager.IsHotReloadEnabled());
    
    // 修改配置文件
    std::this_thread::sleep_for(std::chrono::milliseconds(150));
    
    nlohmann::json modified_config = {
        {"server", {
            {"host", "modified.example.com"},
            {"port", 9999},
            {"threads", 8
        }},
        {"redis", {
            {"host", "127.0.0.1"},
            {"port", 6379},
            {"database", 0
        }},
        {"logging", {
            {"level", "debug"},
            {"file", "test.log"
        }}
    };
    
    std::ofstream file(test_config_file_);
    file << modified_config.dump(4);
    file.close();
    
    // 等待文件监控检测到变更
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 验证配置已更新
    EXPECT_EQ(config_manager.GetValue<std::string>("server.host"), "modified.example.com");
    EXPECT_EQ(config_manager.GetValue<int>("server.port"), 9999);
    
    // 禁用热更新
    config_manager.EnableHotReload(false);
    EXPECT_FALSE(config_manager.IsHotReloadEnabled());
    
    config_manager.UnregisterChangeListener(mock_listener);
}

// 测试配置验证器
TEST_F(ConfigManagerTest, ConfigValidators) {
    // 测试服务器配置验证器
    ServerConfigValidator server_validator;
    
    nlohmann::json valid_server_config = {
        {"host", "localhost"},
        {"port", 8080},
        {"threads", 4}
    };
    
    auto result = server_validator.Validate(valid_server_config);
    EXPECT_TRUE(result.is_valid);
    EXPECT_TRUE(result.errors.empty());
    
    nlohmann::json invalid_server_config = {
        {"host", "localhost"},
        {"port", -1},  // 无效端口
        {"threads", 0}  // 无效线程数
    };
    
    result = server_validator.Validate(invalid_server_config);
    EXPECT_FALSE(result.is_valid);
    EXPECT_FALSE(result.errors.empty());
    
    // 测试Redis配置验证器
    RedisConfigValidator redis_validator;
    
    nlohmann::json valid_redis_config = {
        {"host", "127.0.0.1"},
        {"port", 6379},
        {"database", 0},
        {"pool_size", 10}
    };
    
    result = redis_validator.Validate(valid_redis_config);
    EXPECT_TRUE(result.is_valid);
    
    nlohmann::json invalid_redis_config = {
        {"host", "127.0.0.1"},
        {"port", 70000},  // 端口超出范围
        {"database", 20}  // 数据库编号超出范围
    };
    
    result = redis_validator.Validate(invalid_redis_config);
    EXPECT_FALSE(result.is_valid);
}

// 性能测试
TEST_F(ConfigManagerTest, PerformanceTest) {
    auto& config_manager = ConfigManager::Instance();
    config_manager.Initialize(test_config_file_);
    
    const int num_operations = 10000;
    
    // 测试读取性能
    auto start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_operations; ++i) {
        config_manager.GetValue<std::string>("server.host");
        config_manager.GetValue<int>("server.port");
    }
    auto end = std::chrono::high_resolution_clock::now();
    
    auto read_duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    std::cout << "Read performance: " << num_operations << " operations in " 
              << read_duration.count() << " microseconds" << std::endl;
    
    // 测试写入性能
    start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_operations; ++i) {
        config_manager.SetValue("test_key_" + std::to_string(i), i);
    }
    end = std::chrono::high_resolution_clock::now();
    
    auto write_duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    std::cout << "Write performance: " << num_operations << " operations in " 
              << write_duration.count() << " microseconds" << std::endl;
    
    // 性能应该在合理范围内
    EXPECT_LT(read_duration.count(), 1000000);  // 1秒内完成
    EXPECT_LT(write_duration.count(), 5000000); // 5秒内完成
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}