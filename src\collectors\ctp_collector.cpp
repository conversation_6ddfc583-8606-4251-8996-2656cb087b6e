#include "ctp_collector.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <regex>
#include <iomanip>
#include <filesystem>
#include <spdlog/sinks/stdout_color_sinks.h>

namespace financial_data {

// CTPConfig实现
CTPConfig CTPConfig::LoadFromFile(const std::string& config_path) {
    CTPConfig config;
    
    try {
        std::ifstream file(config_path);
        if (!file.is_open()) {
            throw std::runtime_error("Cannot open config file: " + config_path);
        }
        
        nlohmann::json json_config;
        file >> json_config;
        
        // 解析配置项
        if (json_config.contains("ctp")) {
            auto ctp_config = json_config["ctp"];
            
            config.front_address = ctp_config.value("front_address", "");
            config.broker_id = ctp_config.value("broker_id", "");
            config.user_id = ctp_config.value("user_id", "");
            config.password = ctp_config.value("password", "");
            config.flow_path = ctp_config.value("flow_path", "./flow/");
            config.heartbeat_interval = ctp_config.value("heartbeat_interval", 30);
            config.reconnect_interval = ctp_config.value("reconnect_interval", 5);
            config.max_reconnect_attempts = ctp_config.value("max_reconnect_attempts", 10);
            config.enable_level2 = ctp_config.value("enable_level2", true);
        }
    } catch (const std::exception& e) {
        spdlog::error("Failed to load CTP config from {}: {}", config_path, e.what());
    }
    
    return config;
}

bool CTPConfig::IsValid() const {
    return !front_address.empty() && 
           !broker_id.empty() && 
           !user_id.empty() && 
           !password.empty() &&
           heartbeat_interval > 0 &&
           reconnect_interval > 0 &&
           max_reconnect_attempts > 0;
}

// CTPMarketDataCollector实现
CTPMarketDataCollector::CTPMarketDataCollector()
    : connection_status_(ConnectionStatus::DISCONNECTED)
    , running_(false)
    , shutdown_requested_(false)
    , reconnect_attempts_(0)
    , sequence_counter_(0)
    , total_received_(0)
    , total_processed_(0)
    , total_errors_(0)
    , start_time_(std::chrono::steady_clock::now()) {
    
    InitializeLogger();
    ctp_api_ = std::make_unique<CTPApiWrapper>();
    logger_->info("CTP Market Data Collector initialized");
}

CTPMarketDataCollector::~CTPMarketDataCollector() {
    Shutdown();
    logger_->info("CTP Market Data Collector destroyed");
}

void CTPMarketDataCollector::InitializeLogger() {
    logger_ = spdlog::get("ctp_collector");
    if (!logger_) {
        logger_ = spdlog::stdout_color_mt("ctp_collector");
        logger_->set_level(spdlog::level::info);
        logger_->set_pattern("[%Y-%m-%d %H:%M:%S.%f] [%n] [%l] %v");
    }
}

bool CTPMarketDataCollector::Initialize(const CTPConfig& config) {
    if (!config.IsValid()) {
        logger_->error("Invalid CTP configuration");
        return false;
    }
    
    config_ = config;
    
    // 重置状态
    connection_status_ = ConnectionStatus::DISCONNECTED;
    running_ = false;
    shutdown_requested_ = false;
    reconnect_attempts_ = 0;
    
    logger_->info("CTP Collector initialized with broker: {}, user: {}", 
                  config_.broker_id, config_.user_id);
    
    return true;
}

bool CTPMarketDataCollector::Initialize(const std::string& config_path) {
    auto config = CTPConfig::LoadFromFile(config_path);
    return Initialize(config);
}

bool CTPMarketDataCollector::Connect() {
    if (connection_status_ == ConnectionStatus::CONNECTED) {
        logger_->warn("Already connected to CTP");
        return true;
    }
    
    if (connection_status_ == ConnectionStatus::CONNECTING) {
        logger_->warn("Connection already in progress");
        return false;
    }
    
    logger_->info("Connecting to CTP front server: {}", config_.front_address);
    connection_status_ = ConnectionStatus::CONNECTING;
    
    // 模拟连接过程（实际实现需要调用CTP API）
    bool success = EstablishConnection();
    
    if (success) {
        connection_status_ = ConnectionStatus::CONNECTED;
        reconnect_attempts_ = 0;
        UpdateHeartbeat();
        NotifyStatusChange(ConnectionStatus::CONNECTED, "Connected successfully");
        logger_->info("Successfully connected to CTP");
    } else {
        connection_status_ = ConnectionStatus::ERROR;
        NotifyStatusChange(ConnectionStatus::ERROR, "Connection failed");
        logger_->error("Failed to connect to CTP");
    }
    
    return success;
}

void CTPMarketDataCollector::Disconnect() {
    if (connection_status_ == ConnectionStatus::DISCONNECTED) {
        return;
    }
    
    logger_->info("Disconnecting from CTP");
    connection_status_ = ConnectionStatus::DISCONNECTED;
    
    // 登出并释放CTP API
    if (ctp_api_) {
        if (ctp_api_->IsLoggedIn()) {
            ctp_api_->Logout();
        }
        ctp_api_->Release();
    }
    
    // 清理订阅
    {
        std::lock_guard<std::mutex> lock(subscription_mutex_);
        subscribed_symbols_.clear();
    }
    
    NotifyStatusChange(ConnectionStatus::DISCONNECTED, "Disconnected");
    logger_->info("Disconnected from CTP");
}

bool CTPMarketDataCollector::IsConnected() const {
    return connection_status_ == ConnectionStatus::CONNECTED;
}

ConnectionStatus CTPMarketDataCollector::GetConnectionStatus() const {
    return connection_status_;
}

bool CTPMarketDataCollector::Subscribe(const std::vector<std::string>& symbols) {
    if (!IsConnected()) {
        logger_->error("Cannot subscribe: not connected to CTP");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    
    std::vector<std::string> new_symbols;
    for (const auto& symbol : symbols) {
        if (!IsValidSymbol(symbol)) {
            logger_->warn("Invalid symbol: {}", symbol);
            continue;
        }
        
        if (subscribed_symbols_.find(symbol) == subscribed_symbols_.end()) {
            new_symbols.push_back(symbol);
            subscribed_symbols_.insert(symbol);
        }
    }
    
    if (new_symbols.empty()) {
        logger_->info("No new symbols to subscribe");
        return true;
    }
    
    // 调用CTP API进行订阅
    if (!ctp_api_ || !ctp_api_->IsLoggedIn()) {
        logger_->error("CTP API not ready for subscription");
        return false;
    }
    
    bool success = ctp_api_->SubscribeMarketData(new_symbols);
    if (success) {
        logger_->info("Successfully subscribed to {} symbols", new_symbols.size());
        for (const auto& symbol : new_symbols) {
            logger_->debug("Subscribed to symbol: {}", symbol);
        }
    } else {
        logger_->error("Failed to subscribe to symbols");
        // 回滚订阅状态
        for (const auto& symbol : new_symbols) {
            subscribed_symbols_.erase(symbol);
        }
    }
    
    return success;
}

bool CTPMarketDataCollector::Subscribe(const std::string& symbol) {
    return Subscribe(std::vector<std::string>{symbol});
}

bool CTPMarketDataCollector::Unsubscribe(const std::vector<std::string>& symbols) {
    if (!IsConnected()) {
        logger_->error("Cannot unsubscribe: not connected to CTP");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    
    std::vector<std::string> symbols_to_unsubscribe;
    for (const auto& symbol : symbols) {
        auto it = subscribed_symbols_.find(symbol);
        if (it != subscribed_symbols_.end()) {
            symbols_to_unsubscribe.push_back(symbol);
        }
    }
    
    if (symbols_to_unsubscribe.empty()) {
        logger_->info("No symbols to unsubscribe");
        return true;
    }
    
    // 调用CTP API进行退订
    if (!ctp_api_ || !ctp_api_->IsLoggedIn()) {
        logger_->error("CTP API not ready for unsubscription");
        return false;
    }
    
    bool success = ctp_api_->UnsubscribeMarketData(symbols_to_unsubscribe);
    if (success) {
        for (const auto& symbol : symbols_to_unsubscribe) {
            subscribed_symbols_.erase(symbol);
            logger_->debug("Unsubscribed from symbol: {}", symbol);
        }
    } else {
        logger_->error("Failed to unsubscribe from symbols");
    }
    
    return success;
}

bool CTPMarketDataCollector::Unsubscribe(const std::string& symbol) {
    return Unsubscribe(std::vector<std::string>{symbol});
}

std::vector<std::string> CTPMarketDataCollector::GetSubscribedSymbols() const {
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    return std::vector<std::string>(subscribed_symbols_.begin(), subscribed_symbols_.end());
}

void CTPMarketDataCollector::ClearSubscriptions() {
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    subscribed_symbols_.clear();
    logger_->info("Cleared all subscriptions");
}

void CTPMarketDataCollector::SetDataCallback(const MarketDataCallback& callback) {
    data_callback_ = callback;
    logger_->debug("Data callback set");
}

void CTPMarketDataCollector::SetStatusCallback(const ConnectionStatusCallback& callback) {
    status_callback_ = callback;
    logger_->debug("Status callback set");
}

void CTPMarketDataCollector::Start() {
    if (running_) {
        logger_->warn("Collector already running");
        return;
    }
    
    running_ = true;
    shutdown_requested_ = false;
    start_time_ = std::chrono::steady_clock::now();
    
    // 启动各个工作线程
    StartHeartbeat();
    StartDataProcessor();
    
    logger_->info("CTP Collector started");
}

void CTPMarketDataCollector::Stop() {
    if (!running_) {
        return;
    }
    
    logger_->info("Stopping CTP Collector");
    running_ = false;
    
    // 通知所有线程停止
    queue_cv_.notify_all();
    reconnect_cv_.notify_all();
    
    // 等待线程结束
    if (heartbeat_thread_ && heartbeat_thread_->joinable()) {
        heartbeat_thread_->join();
    }
    
    if (reconnect_thread_ && reconnect_thread_->joinable()) {
        reconnect_thread_->join();
    }
    
    if (data_processor_thread_ && data_processor_thread_->joinable()) {
        data_processor_thread_->join();
    }
    
    logger_->info("CTP Collector stopped");
}

void CTPMarketDataCollector::Shutdown() {
    if (shutdown_requested_) {
        return;
    }
    
    logger_->info("Shutting down CTP Collector");
    shutdown_requested_ = true;
    
    Stop();
    Disconnect();
    
    logger_->info("CTP Collector shutdown complete");
}

CTPMarketDataCollector::Statistics CTPMarketDataCollector::GetStatistics() const {
    Statistics stats;
    stats.total_received = total_received_.load();
    stats.total_processed = total_processed_.load();
    stats.total_errors = total_errors_.load();
    stats.status = connection_status_.load();
    stats.reconnect_attempts = reconnect_attempts_.load();
    stats.last_heartbeat = last_heartbeat_.load();
    
    auto now = std::chrono::steady_clock::now();
    stats.uptime_seconds = std::chrono::duration<double>(now - start_time_).count();
    
    if (stats.uptime_seconds > 0) {
        stats.messages_per_second = static_cast<double>(stats.total_processed) / stats.uptime_seconds;
    }
    
    return stats;
}

void CTPMarketDataCollector::ResetStatistics() {
    total_received_ = 0;
    total_processed_ = 0;
    total_errors_ = 0;
    start_time_ = std::chrono::steady_clock::now();
    logger_->info("Statistics reset");
}

bool CTPMarketDataCollector::IsHealthy() const {
    if (!IsConnected()) {
        return false;
    }
    
    if (IsHeartbeatTimeout()) {
        return false;
    }
    
    // 检查数据处理队列是否过载
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        if (data_queue_.size() > 10000) {  // 队列过载阈值
            return false;
        }
    }
    
    return true;
}

std::string CTPMarketDataCollector::GetHealthStatus() const {
    std::ostringstream oss;
    oss << "Status: " << static_cast<int>(connection_status_.load()) << ", ";
    oss << "Healthy: " << (IsHealthy() ? "Yes" : "No") << ", ";
    oss << "Subscriptions: " << subscribed_symbols_.size() << ", ";
    oss << "Queue Size: ";
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        oss << data_queue_.size();
    }
    return oss.str();
}

// 私有方法实现
bool CTPMarketDataCollector::EstablishConnection() {
    if (!ctp_api_) {
        logger_->error("CTP API wrapper not initialized");
        return false;
    }
    
    try {
        // 设置回调函数
        ctp_api_->SetConnectedCallback([this]() {
            HandleFrontConnected();
        });
        
        ctp_api_->SetDisconnectedCallback([this](int reason) {
            HandleFrontDisconnected(reason);
        });
        
        ctp_api_->SetLoginCallback([this](bool success, const std::string& error_msg) {
            HandleUserLogin(success, error_msg);
        });
        
        ctp_api_->SetMarketDataCallback([this](CThostFtdcDepthMarketDataField* data) {
            HandleMarketData(data);
        });
        
        ctp_api_->SetErrorCallback([this](int error_id, const std::string& error_msg) {
            HandleError(error_id, error_msg);
        });
        
        // 初始化CTP API
        if (!ctp_api_->Initialize(config_.flow_path)) {
            logger_->error("Failed to initialize CTP API");
            return false;
        }
        
        // 注册前置机地址
        if (!ctp_api_->RegisterFront(config_.front_address)) {
            logger_->error("Failed to register front server: {}", config_.front_address);
            return false;
        }
        
        // 启动连接
        ctp_api_->Init();
        
        // 等待连接建立（最多等待10秒）
        auto start_time = std::chrono::steady_clock::now();
        while (!ctp_api_->IsConnected() && 
               std::chrono::duration_cast<std::chrono::seconds>(
                   std::chrono::steady_clock::now() - start_time).count() < 10) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (!ctp_api_->IsConnected()) {
            logger_->error("Connection timeout");
            return false;
        }
        
        // 发送登录请求
        if (!ctp_api_->Login(config_.broker_id, config_.user_id, config_.password)) {
            logger_->error("Failed to send login request");
            return false;
        }
        
        // 等待登录完成（最多等待5秒）
        start_time = std::chrono::steady_clock::now();
        while (!ctp_api_->IsLoggedIn() && 
               std::chrono::duration_cast<std::chrono::seconds>(
                   std::chrono::steady_clock::now() - start_time).count() < 5) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        return ctp_api_->IsLoggedIn();
        
    } catch (const std::exception& e) {
        logger_->error("Exception during connection establishment: {}", e.what());
        return false;
    }
}

void CTPMarketDataCollector::HandleDisconnection(const std::string& reason) {
    logger_->warn("Connection lost: {}", reason);
    connection_status_ = ConnectionStatus::DISCONNECTED;
    NotifyStatusChange(ConnectionStatus::DISCONNECTED, reason);
    
    if (!shutdown_requested_ && running_) {
        StartReconnectProcess();
    }
}

void CTPMarketDataCollector::StartReconnectProcess() {
    if (connection_status_ == ConnectionStatus::RECONNECTING) {
        return;
    }
    
    connection_status_ = ConnectionStatus::RECONNECTING;
    NotifyStatusChange(ConnectionStatus::RECONNECTING, "Starting reconnection");
    
    if (!reconnect_thread_ || !reconnect_thread_->joinable()) {
        reconnect_thread_ = std::make_unique<std::thread>(&CTPMarketDataCollector::ReconnectLoop, this);
    }
}

void CTPMarketDataCollector::ReconnectLoop() {
    logger_->info("Starting reconnection loop");
    
    while (running_ && !shutdown_requested_ && reconnect_attempts_ < static_cast<uint32_t>(config_.max_reconnect_attempts)) {
        std::unique_lock<std::mutex> lock(reconnect_mutex_);
        
        // 等待重连间隔
        if (reconnect_cv_.wait_for(lock, std::chrono::seconds(config_.reconnect_interval),
                                   [this] { return shutdown_requested_ || !running_; })) {
            break; // 收到停止信号
        }
        
        reconnect_attempts_++;
        logger_->info("Reconnection attempt {} of {}", reconnect_attempts_.load(), config_.max_reconnect_attempts);
        
        if (Connect()) {
            logger_->info("Reconnection successful");
            
            // 重新订阅之前的合约
            auto symbols = GetSubscribedSymbols();
            if (!symbols.empty()) {
                Subscribe(symbols);
            }
            
            break;
        } else {
            logger_->warn("Reconnection attempt {} failed", reconnect_attempts_.load());
        }
    }
    
    if (reconnect_attempts_ >= static_cast<uint32_t>(config_.max_reconnect_attempts)) {
        logger_->error("Max reconnection attempts reached, giving up");
        connection_status_ = ConnectionStatus::ERROR;
        NotifyStatusChange(ConnectionStatus::ERROR, "Max reconnection attempts reached");
    }
}

void CTPMarketDataCollector::StartHeartbeat() {
    if (heartbeat_thread_ && heartbeat_thread_->joinable()) {
        return;
    }
    
    heartbeat_thread_ = std::make_unique<std::thread>(&CTPMarketDataCollector::HeartbeatLoop, this);
}

void CTPMarketDataCollector::HeartbeatLoop() {
    logger_->debug("Heartbeat thread started");
    
    while (running_ && !shutdown_requested_) {
        std::this_thread::sleep_for(std::chrono::seconds(config_.heartbeat_interval));
        
        if (IsConnected()) {
            if (IsHeartbeatTimeout()) {
                logger_->warn("Heartbeat timeout detected");
                HandleDisconnection("Heartbeat timeout");
            } else {
                UpdateHeartbeat();
                logger_->debug("Heartbeat updated");
            }
        }
    }
    
    logger_->debug("Heartbeat thread stopped");
}

void CTPMarketDataCollector::UpdateHeartbeat() {
    last_heartbeat_ = std::chrono::steady_clock::now();
}

bool CTPMarketDataCollector::IsHeartbeatTimeout() const {
    if (!IsConnected()) {
        return false;
    }
    
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_heartbeat_.load()).count();
    return elapsed > (config_.heartbeat_interval * 2); // 超时阈值为心跳间隔的2倍
}

void CTPMarketDataCollector::StartDataProcessor() {
    if (data_processor_thread_ && data_processor_thread_->joinable()) {
        return;
    }
    
    data_processor_thread_ = std::make_unique<std::thread>(&CTPMarketDataCollector::DataProcessorLoop, this);
}

void CTPMarketDataCollector::DataProcessorLoop() {
    logger_->debug("Data processor thread started");
    
    while (running_ && !shutdown_requested_) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        // 等待数据或停止信号
        queue_cv_.wait(lock, [this] {
            return !data_queue_.empty() || !running_ || shutdown_requested_;
        });
        
        // 处理队列中的所有数据
        while (!data_queue_.empty() && running_ && !shutdown_requested_) {
            auto data = data_queue_.front();
            data_queue_.pop();
            lock.unlock();
            
            ProcessMarketData(data);
            
            lock.lock();
        }
    }
    
    logger_->debug("Data processor thread stopped");
}

void CTPMarketDataCollector::ProcessMarketData(const MarketDataWrapper& data) {
    try {
        if (!ValidateMarketData(data)) {
            total_errors_++;
            logger_->warn("Invalid market data received");
            return;
        }
        
        // 调用数据回调
        if (data_callback_) {
            data_callback_(data);
        }
        
        total_processed_++;
        
        if (total_processed_ % 10000 == 0) {
            logger_->debug("Processed {} market data messages", total_processed_.load());
        }
        
    } catch (const std::exception& e) {
        total_errors_++;
        logger_->error("Error processing market data: {}", e.what());
    }
}

uint32_t CTPMarketDataCollector::GetNextSequence() {
    return ++sequence_counter_;
}

uint32_t CTPMarketDataCollector::GetSymbolSequence(const std::string& symbol) {
    std::lock_guard<std::mutex> lock(sequence_mutex_);
    return symbol_sequences_[symbol];
}

void CTPMarketDataCollector::UpdateSymbolSequence(const std::string& symbol, uint32_t sequence) {
    std::lock_guard<std::mutex> lock(sequence_mutex_);
    symbol_sequences_[symbol] = sequence;
}

StandardTick CTPMarketDataCollector::ConvertToStandardTick(CThostFtdcDepthMarketDataField* ctp_data) {
    if (!ctp_data) {
        throw std::invalid_argument("CTP data is null");
    }
    
    StandardTick tick;
    
    // 基本信息
    tick.symbol = std::string(ctp_data->InstrumentID);
    tick.exchange = GetExchangeCode(tick.symbol);
    tick.sequence = GetNextSequence();
    
    // 时间戳处理
    tick.update_time = std::string(ctp_data->UpdateTime);
    tick.update_millisec = ctp_data->UpdateMillisec;
    tick.timestamp_ns = ctp_utils::CTPTimeToNanoseconds(tick.update_time, tick.update_millisec);
    
    // 价格信息
    tick.last_price = ctp_data->LastPrice;
    tick.pre_close_price = ctp_data->PreClosePrice;
    tick.open_price = ctp_data->OpenPrice;
    tick.high_price = ctp_data->HighestPrice;
    tick.low_price = ctp_data->LowestPrice;
    tick.close_price = ctp_data->ClosePrice;
    tick.settlement_price = ctp_data->SettlementPrice;
    tick.upper_limit = ctp_data->UpperLimitPrice;
    tick.lower_limit = ctp_data->LowerLimitPrice;
    
    // 成交信息
    tick.volume = static_cast<uint64_t>(ctp_data->Volume);
    tick.turnover = ctp_data->Turnover;
    tick.open_interest = static_cast<uint64_t>(ctp_data->OpenInterest);
    
    // 五档买盘数据
    tick.bids[0] = PriceLevel(ctp_data->BidPrice1, static_cast<uint64_t>(ctp_data->BidVolume1));
    tick.bids[1] = PriceLevel(ctp_data->BidPrice2, static_cast<uint64_t>(ctp_data->BidVolume2));
    tick.bids[2] = PriceLevel(ctp_data->BidPrice3, static_cast<uint64_t>(ctp_data->BidVolume3));
    tick.bids[3] = PriceLevel(ctp_data->BidPrice4, static_cast<uint64_t>(ctp_data->BidVolume4));
    tick.bids[4] = PriceLevel(ctp_data->BidPrice5, static_cast<uint64_t>(ctp_data->BidVolume5));
    
    // 五档卖盘数据
    tick.asks[0] = PriceLevel(ctp_data->AskPrice1, static_cast<uint64_t>(ctp_data->AskVolume1));
    tick.asks[1] = PriceLevel(ctp_data->AskPrice2, static_cast<uint64_t>(ctp_data->AskVolume2));
    tick.asks[2] = PriceLevel(ctp_data->AskPrice3, static_cast<uint64_t>(ctp_data->AskVolume3));
    tick.asks[3] = PriceLevel(ctp_data->AskPrice4, static_cast<uint64_t>(ctp_data->AskVolume4));
    tick.asks[4] = PriceLevel(ctp_data->AskPrice5, static_cast<uint64_t>(ctp_data->AskVolume5));
    
    // 数据验证
    if (!ctp_utils::ValidatePrice(tick.last_price)) {
        logger_->warn("Invalid last price for {}: {}", tick.symbol, tick.last_price);
    }
    
    if (!ctp_utils::ValidateVolume(tick.volume)) {
        logger_->warn("Invalid volume for {}: {}", tick.symbol, tick.volume);
    }
    
    return tick;
}

Level2Data CTPMarketDataCollector::ConvertToLevel2Data(CThostFtdcDepthMarketDataField* ctp_data) {
    // 模拟Level2数据转换
    Level2Data level2;
    level2.SetCurrentTimestamp();
    level2.symbol = "CU2409";
    level2.exchange = "SHFE";
    level2.sequence = GetNextSequence();
    
    // 模拟十档数据
    for (int i = 0; i < 10; ++i) {
        level2.bids.emplace_back(78550.0 - i * 10, 10 + i * 5, 2 + i);
        level2.asks.emplace_back(78570.0 + i * 10, 8 + i * 3, 1 + i);
    }
    
    return level2;
}

bool CTPMarketDataCollector::ValidateMarketData(const MarketDataWrapper& data) {
    return data.IsValid();
}

void CTPMarketDataCollector::HandleError(const std::string& error_msg) {
    total_errors_++;
    LogError(error_msg);
}

void CTPMarketDataCollector::LogError(const std::string& error_msg) {
    logger_->error("CTP Error: {}", error_msg);
}

void CTPMarketDataCollector::NotifyStatusChange(ConnectionStatus status, const std::string& message) {
    if (status_callback_) {
        status_callback_(status, message);
    }
}

std::string CTPMarketDataCollector::GetExchangeCode(const std::string& symbol) const {
    return ctp_utils::GetExchangeFromSymbol(symbol);
}

bool CTPMarketDataCollector::IsValidSymbol(const std::string& symbol) const {
    return ctp_utils::IsValidCTPSymbol(symbol);
}

int64_t CTPMarketDataCollector::GetCurrentTimestampNs() const {
    return StandardTick::GetCurrentTimestampNs();
}

// CTP API回调处理方法
void CTPMarketDataCollector::HandleFrontConnected() {
    logger_->info("CTP front connected");
    UpdateHeartbeat();
    connection_status_ = ConnectionStatus::CONNECTING;
    NotifyStatusChange(ConnectionStatus::CONNECTING, "Front connected, waiting for login");
}

void CTPMarketDataCollector::HandleFrontDisconnected(int reason) {
    logger_->warn("CTP front disconnected, reason: {}", reason);
    HandleDisconnection("Front disconnected: " + std::to_string(reason));
}

void CTPMarketDataCollector::HandleUserLogin(bool success, const std::string& error_msg) {
    if (success) {
        logger_->info("User login successful");
        connection_status_ = ConnectionStatus::CONNECTED;
        NotifyStatusChange(ConnectionStatus::CONNECTED, "Login successful");
        UpdateHeartbeat();
    } else {
        logger_->error("User login failed: {}", error_msg);
        connection_status_ = ConnectionStatus::ERROR;
        NotifyStatusChange(ConnectionStatus::ERROR, "Login failed: " + error_msg);
    }
}

void CTPMarketDataCollector::HandleMarketData(CThostFtdcDepthMarketDataField* depth_market_data) {
    if (!depth_market_data) {
        return;
    }
    
    total_received_++;
    
    try {
        // 转换为标准格式
        auto tick = ConvertToStandardTick(depth_market_data);
        MarketDataWrapper wrapper(tick);
        wrapper.source = "CTP";
        
        // 加入处理队列
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            data_queue_.push(wrapper);
        }
        queue_cv_.notify_one();
        
        // 更新心跳时间
        UpdateHeartbeat();
        
    } catch (const std::exception& e) {
        HandleError("Failed to process market data: " + std::string(e.what()));
    }
}

void CTPMarketDataCollector::HandleError(int error_id, const std::string& error_msg) {
    total_errors_++;
    logger_->error("CTP API error {}: {}", error_id, error_msg);
    
    // 根据错误类型决定是否需要重连
    if (ctp_utils::IsRecoverableError(error_id)) {
        HandleDisconnection("Recoverable error: " + error_msg);
    }
}

// 数据回补相关方法
void CTPMarketDataCollector::RequestMissedData(const std::string& symbol, int64_t from_time, int64_t to_time) {
    logger_->info("Requesting missed data for {} from {} to {}", symbol, from_time, to_time);
    // 实际实现需要调用历史数据接口
}

bool CTPMarketDataCollector::HasDataGap(const std::string& symbol, uint32_t current_seq, uint32_t expected_seq) {
    return current_seq != expected_seq;
}

void CTPMarketDataCollector::HandleDataGap(const std::string& symbol, uint32_t missing_start, uint32_t missing_end) {
    logger_->warn("Data gap detected for {}: missing sequences {} to {}", 
                  symbol, missing_start, missing_end);
    
    // 实际实现中需要请求缺失的数据
    // RequestMissedData(symbol, missing_start, missing_end);
}

// CTPCollectorFactory实现
std::unique_ptr<CTPMarketDataCollector> CTPCollectorFactory::Create(const CTPConfig& config) {
    auto collector = std::make_unique<CTPMarketDataCollector>();
    if (collector->Initialize(config)) {
        return collector;
    }
    return nullptr;
}

std::unique_ptr<CTPMarketDataCollector> CTPCollectorFactory::CreateFromFile(const std::string& config_path) {
    auto config = CTPConfig::LoadFromFile(config_path);
    return Create(config);
}

bool CTPCollectorFactory::ValidateCTPEnvironment() {
    // 检查CTP库是否可用
    // 实际实现需要检查CTP动态库
    return true;
}

std::string CTPCollectorFactory::GetCTPVersion() {
    // 返回CTP API版本
    return "6.6.9";
}

// 辅助函数实现
namespace ctp_utils {

std::string GetExchangeFromSymbol(const std::string& symbol) {
    if (symbol.empty()) {
        return "";
    }
    
    // 简单的交易所识别逻辑
    if (symbol.find("CU") == 0 || symbol.find("AL") == 0 || symbol.find("ZN") == 0) {
        return "SHFE";  // 上期所
    } else if (symbol.find("A") == 0 || symbol.find("M") == 0 || symbol.find("Y") == 0) {
        return "DCE";   // 大商所
    } else if (symbol.find("CF") == 0 || symbol.find("SR") == 0 || symbol.find("TA") == 0) {
        return "CZCE";  // 郑商所
    } else if (symbol.find("IF") == 0 || symbol.find("IC") == 0 || symbol.find("IH") == 0) {
        return "CFFEX"; // 中金所
    }
    
    return "UNKNOWN";
}

bool IsValidCTPSymbol(const std::string& symbol) {
    if (symbol.empty() || symbol.length() < 3) {
        return false;
    }
    
    // 基本的合约代码格式检查
    std::regex pattern(R"([A-Z]{1,2}\d{4})");
    return std::regex_match(symbol, pattern);
}

int64_t CTPTimeToNanoseconds(const std::string& update_time, int update_millisec) {
    // 简化的时间转换实现
    // 实际实现需要解析CTP的时间格式
    auto now = std::chrono::high_resolution_clock::now();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
}

std::string NanosecondsToCTPTime(int64_t timestamp_ns) {
    auto time_point = std::chrono::time_point<std::chrono::high_resolution_clock>(
        std::chrono::nanoseconds(timestamp_ns));
    auto time_t = std::chrono::system_clock::to_time_t(
        std::chrono::system_clock::now());
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
    return oss.str();
}

bool ValidatePrice(double price) {
    return price > 0.0 && std::isfinite(price);
}

bool ValidateVolume(uint64_t volume) {
    return volume > 0;
}

bool ValidateTurnover(double turnover) {
    return turnover >= 0.0 && std::isfinite(turnover);
}

std::string GetErrorMessage(int error_code) {
    // CTP错误码映射
    switch (error_code) {
        case 0: return "Success";
        case -1: return "Network error";
        case -2: return "Not connected";
        case -3: return "Login failed";
        default: return "Unknown error: " + std::to_string(error_code);
    }
}

bool IsRecoverableError(int error_code) {
    // 判断错误是否可恢复
    return error_code == -1 || error_code == -2; // 网络错误和连接错误可恢复
}

} // namespace ctp_utils

} // namespace financial_data