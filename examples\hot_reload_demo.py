#!/usr/bin/env python3
"""
配置热更新功能演示

这个演示展示了增强的配置热更新功能，包括：
1. 文件监控和自动重载
2. 配置验证和安全重载
3. 配置备份和恢复
4. 变更通知和回调
"""

import os
import sys
import json
import time
import tempfile
import shutil
import threading
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from config.config_manager_python import (
    PythonConfigManager, ConfigChangeListener, ConfigChangeEvent,
    ConfigValidator, ValidationResult
)


class DemoConfigValidator(ConfigValidator):
    """演示用配置验证器"""
    
    def validate(self, config):
        result = ValidationResult()
        
        # 验证服务器配置
        if 'host' in config:
            if not isinstance(config['host'], str) or not config['host']:
                result.add_error("Host must be a non-empty string")
        
        if 'port' in config:
            if not isinstance(config['port'], int) or config['port'] <= 0 or config['port'] > 65535:
                result.add_error("Port must be an integer between 1 and 65535")
        
        # 添加警告示例
        if 'port' in config and config['port'] < 1024:
            result.add_warning("Using privileged port (< 1024)")
        
        return result
    
    def get_validator_name(self):
        return "DemoConfigValidator"


class DemoConfigListener(ConfigChangeListener):
    """演示用配置变更监听器"""
    
    def __init__(self, name):
        self.name = name
        self.events = []
    
    def on_config_changed(self, event):
        self.events.append(event)
        print(f"[{self.name}] Config changed: {event.type.value}")
        if event.key:
            print(f"  Key: {event.key}")
            print(f"  Old value: {event.old_value}")
            print(f"  New value: {event.new_value}")
        print()


def create_demo_config():
    """创建演示配置"""
    return {
        "server": {
            "host": "localhost",
            "port": 8080,
            "threads": 4,
            "debug": False
        },
        "database": {
            "host": "127.0.0.1",
            "port": 5432,
            "name": "demo_db",
            "pool_size": 10
        },
        "logging": {
            "level": "info",
            "file": "demo.log",
            "console": True
        },
        "features": {
            "hot_reload": True,
            "backup_enabled": True,
            "validation": True
        }
    }


def demo_basic_hot_reload():
    """演示基本热更新功能"""
    print("=== 基本热更新功能演示 ===\n")
    
    # 创建临时目录和配置文件
    temp_dir = tempfile.mkdtemp()
    config_file = os.path.join(temp_dir, "demo_config.json")
    backup_dir = os.path.join(temp_dir, "backups")
    
    try:
        # 写入初始配置
        initial_config = create_demo_config()
        with open(config_file, 'w') as f:
            json.dump(initial_config, f, indent=4)
        
        # 初始化配置管理器
        config_manager = PythonConfigManager()
        config_manager.set_backup_directory(backup_dir)
        config_manager.set_backup_enabled(True)
        config_manager.set_max_backups(3)
        
        # 初始化配置
        print(f"初始化配置文件: {config_file}")
        config_manager.initialize(config_file)
        
        # 注册监听器
        listener = DemoConfigListener("BasicDemo")
        config_manager.register_change_listener(listener)
        
        # 注册重载回调
        def reload_callback(success, message):
            status = "成功" if success else "失败"
            print(f"配置重载{status}: {message}\n")
        
        config_manager.register_reload_callback(reload_callback)
        
        # 启用热更新
        print("启用热更新功能...")
        config_manager.enable_hot_reload(True)
        
        # 显示初始配置
        print(f"初始服务器端口: {config_manager.get_value('server.port')}")
        print(f"初始数据库主机: {config_manager.get_value('database.host')}")
        print()
        
        # 等待用户输入以修改配置
        input("按回车键开始修改配置文件...")
        
        # 修改配置文件
        print("修改配置文件...")
        modified_config = initial_config.copy()
        modified_config['server']['port'] = 9090
        modified_config['server']['debug'] = True
        modified_config['database']['host'] = 'db.example.com'
        modified_config['new_feature'] = {
            'enabled': True,
            'timeout': 30
        }
        
        with open(config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 等待热更新生效
        print("等待热更新生效...")
        time.sleep(1.5)
        
        # 显示更新后的配置
        print(f"更新后服务器端口: {config_manager.get_value('server.port')}")
        print(f"更新后数据库主机: {config_manager.get_value('database.host')}")
        print(f"新功能启用状态: {config_manager.get_value('new_feature.enabled')}")
        print()
        
        # 检查备份文件
        if os.path.exists(backup_dir):
            backup_files = [f for f in os.listdir(backup_dir) 
                           if f.startswith("config_backup_")]
            print(f"创建了 {len(backup_files)} 个备份文件")
            for backup_file in backup_files:
                print(f"  - {backup_file}")
        print()
        
        # 显示监听器收到的事件
        print(f"监听器收到 {len(listener.events)} 个配置变更事件")
        
        # 清理
        config_manager.shutdown()
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


def demo_validation_and_safety():
    """演示配置验证和安全重载"""
    print("=== 配置验证和安全重载演示 ===\n")
    
    # 创建临时目录和配置文件
    temp_dir = tempfile.mkdtemp()
    config_file = os.path.join(temp_dir, "validation_config.json")
    
    try:
        # 写入初始配置
        initial_config = create_demo_config()
        with open(config_file, 'w') as f:
            json.dump(initial_config, f, indent=4)
        
        # 初始化配置管理器
        config_manager = PythonConfigManager()
        config_manager.set_safe_reload_enabled(True)  # 启用安全重载
        
        # 注册验证器
        server_validator = DemoConfigValidator()
        config_manager.register_validator("server", server_validator)
        
        # 初始化配置
        print(f"初始化配置文件: {config_file}")
        config_manager.initialize(config_file)
        
        # 注册重载回调
        reload_results = []
        def reload_callback(success, message):
            reload_results.append((success, message))
            status = "成功" if success else "失败"
            print(f"配置重载{status}: {message}")
        
        config_manager.register_reload_callback(reload_callback)
        
        # 启用热更新
        print("启用热更新功能（安全模式）...")
        config_manager.enable_hot_reload(True)
        
        print(f"初始服务器端口: {config_manager.get_value('server.port')}")
        print()
        
        # 测试1: 有效配置修改
        print("测试1: 修改为有效配置...")
        input("按回车键继续...")
        
        valid_config = initial_config.copy()
        valid_config['server']['port'] = 9090  # 有效端口
        
        with open(config_file, 'w') as f:
            json.dump(valid_config, f, indent=4)
        
        time.sleep(1.5)
        print(f"更新后服务器端口: {config_manager.get_value('server.port')}")
        print()
        
        # 测试2: 无效配置修改
        print("测试2: 修改为无效配置...")
        input("按回车键继续...")
        
        invalid_config = initial_config.copy()
        invalid_config['server']['port'] = 70000  # 无效端口
        
        with open(config_file, 'w') as f:
            json.dump(invalid_config, f, indent=4)
        
        time.sleep(1.5)
        print(f"端口应该保持不变: {config_manager.get_value('server.port')}")
        print()
        
        # 测试3: JSON格式错误
        print("测试3: 写入无效JSON...")
        input("按回车键继续...")
        
        with open(config_file, 'w') as f:
            f.write('{ "invalid": json, }')
        
        time.sleep(1.5)
        print(f"端口应该保持不变: {config_manager.get_value('server.port')}")
        print()
        
        # 显示重载结果
        print("重载结果汇总:")
        for i, (success, message) in enumerate(reload_results, 1):
            status = "成功" if success else "失败"
            print(f"  {i}. {status}: {message}")
        
        # 清理
        config_manager.shutdown()
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


def demo_concurrent_access():
    """演示并发访问时的热更新"""
    print("=== 并发访问热更新演示 ===\n")
    
    # 创建临时目录和配置文件
    temp_dir = tempfile.mkdtemp()
    config_file = os.path.join(temp_dir, "concurrent_config.json")
    
    try:
        # 写入初始配置
        initial_config = create_demo_config()
        with open(config_file, 'w') as f:
            json.dump(initial_config, f, indent=4)
        
        # 初始化配置管理器
        config_manager = PythonConfigManager()
        config_manager.initialize(config_file)
        config_manager.enable_hot_reload(True)
        
        # 并发读取配置的线程
        read_results = []
        stop_reading = threading.Event()
        
        def config_reader(thread_id):
            """配置读取线程"""
            while not stop_reading.is_set():
                try:
                    port = config_manager.get_value('server.port')
                    host = config_manager.get_value('server.host')
                    read_results.append((thread_id, port, host, time.time()))
                    time.sleep(0.1)
                except Exception as e:
                    print(f"Thread {thread_id} error: {e}")
        
        # 启动多个读取线程
        print("启动多个并发读取线程...")
        threads = []
        for i in range(3):
            thread = threading.Thread(target=config_reader, args=(i,))
            thread.daemon = True
            thread.start()
            threads.append(thread)
        
        time.sleep(0.5)
        
        # 在并发读取过程中修改配置
        print("在并发读取过程中修改配置...")
        
        for iteration in range(3):
            modified_config = initial_config.copy()
            modified_config['server']['port'] = 8080 + iteration * 10
            modified_config['server']['host'] = f'host{iteration}.example.com'
            
            with open(config_file, 'w') as f:
                json.dump(modified_config, f, indent=4)
            
            print(f"修改 {iteration + 1}: 端口={modified_config['server']['port']}, "
                  f"主机={modified_config['server']['host']}")
            
            time.sleep(1.0)
        
        # 停止读取线程
        print("停止读取线程...")
        stop_reading.set()
        
        for thread in threads:
            thread.join(timeout=1.0)
        
        # 分析读取结果
        print(f"\n总共进行了 {len(read_results)} 次配置读取")
        
        # 按线程分组显示结果
        for thread_id in range(3):
            thread_results = [r for r in read_results if r[0] == thread_id]
            if thread_results:
                print(f"线程 {thread_id}: {len(thread_results)} 次读取")
                # 显示端口变化
                ports = list(set(r[1] for r in thread_results))
                print(f"  观察到的端口值: {sorted(ports)}")
        
        # 清理
        config_manager.shutdown()
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


def main():
    """主函数"""
    print("配置热更新功能演示\n")
    print("这个演示将展示以下功能:")
    print("1. 基本热更新功能")
    print("2. 配置验证和安全重载")
    print("3. 并发访问时的热更新")
    print()
    
    try:
        # 演示1: 基本热更新
        demo_basic_hot_reload()
        
        input("\n按回车键继续下一个演示...")
        print()
        
        # 演示2: 配置验证和安全重载
        demo_validation_and_safety()
        
        input("\n按回车键继续下一个演示...")
        print()
        
        # 演示3: 并发访问
        demo_concurrent_access()
        
        print("\n=== 演示完成 ===")
        print("热更新功能演示已完成！")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()