"""
Simple Storage Integration Test

Basic test to verify the storage manager integration works correctly.
"""

import asyncio
import pytest
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from collectors.pytdx_collector import (
    PyTDXCollector, 
    PyTDXConfig, 
    ArchiverConfig,
    MockStorageManager
)


@pytest.mark.asyncio
async def test_basic_storage_integration():
    """Test basic storage integration functionality"""
    
    # Create a mock storage manager
    storage_manager = MockStorageManager()
    
    # Create configuration
    archiver_config = ArchiverConfig(
        enable_data_validation=True,
        enable_deduplication=True,
        archive_batch_size=100
    )
    
    config = PyTDXConfig(
        batch_size=50,
        archive_enabled=False,  # Disable archiver to test direct storage
        archiver_config=archiver_config
    )
    
    # Create collector with mock storage
    collector = PyTDXCollector(config, storage_manager)
    
    # Create sample K-line data
    dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
    k_data = pd.DataFrame({
        'open': [10.0, 11.0, 12.0, 13.0, 14.0],
        'high': [15.0, 16.0, 17.0, 18.0, 19.0],
        'low': [9.0, 10.0, 11.0, 12.0, 13.0],
        'close': [12.0, 13.0, 14.0, 15.0, 16.0],
        'volume': [1000, 1100, 1200, 1300, 1400],
        'amount': [15000, 16500, 18000, 19500, 21000]
    }, index=dates)
    
    # Test batch storage
    success = await collector.batch_store_data_async([k_data], ['000001'], 'kline_D')
    
    assert success is True
    
    # Verify data was stored
    stored_data = storage_manager.get_stored_data()
    assert len(stored_data) == 5  # Should have 5 records
    
    # Verify data format
    for tick in stored_data:
        assert 'timestamp_ns' in tick
        assert 'symbol' in tick
        assert tick['symbol'] == '000001'
        assert 'exchange' in tick
        assert tick['exchange'] == 'PYTDX'
        assert 'last_price' in tick
        assert tick['last_price'] > 0
        assert 'data_type' in tick
        assert tick['data_type'] == 'kline_D'
    
    print(f"Successfully stored {len(stored_data)} records")
    print(f"Sample record: {stored_data[0]}")


@pytest.mark.asyncio
async def test_realtime_quotes_storage():
    """Test realtime quotes storage"""
    
    storage_manager = MockStorageManager()
    config = PyTDXConfig(archive_enabled=False)
    collector = PyTDXCollector(config, storage_manager)
    
    # Create sample realtime quotes
    quotes = [
        {
            'code': '000001',
            'name': 'Test Stock 1',
            'price': 15.0,
            'last_close': 14.0,
            'open': 14.5,
            'high': 16.0,
            'low': 13.5,
            'volume': 10000,
            'amount': 150000,
            'bid1': 14.9,
            'ask1': 15.1,
            'bid1_vol': 1000,
            'ask1_vol': 1000,
            'timestamp': datetime.now().isoformat()
        }
    ]
    
    success = await collector.store_realtime_data_async(quotes)
    
    assert success is True
    
    stored_data = storage_manager.get_stored_data()
    assert len(stored_data) == 1
    
    tick = stored_data[0]
    assert tick['symbol'] == '000001'
    assert tick['last_price'] == 15.0
    assert tick['data_type'] == 'realtime'
    assert 'bid_price' in tick
    assert 'ask_price' in tick
    
    print(f"Successfully stored realtime quote: {tick}")


def test_storage_manager_basic():
    """Test basic storage manager functionality"""
    
    storage_manager = MockStorageManager()
    
    # Test health check
    assert storage_manager.is_healthy() is True
    
    # Test statistics
    stats = storage_manager.get_statistics()
    assert 'total_stored' in stats
    assert 'successful_stores' in stats
    assert 'failed_stores' in stats
    
    print(f"Storage manager stats: {stats}")


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "-s"])