#include "storage_manager.h"
#include <algorithm>
#include <sstream>
#include <fstream>
#include <nlohmann/json.hpp>

namespace financial_data {

StorageManager::StorageManager(const StorageManagerConfig& config)
    : config_(config) {
    InitializeLogger();
    statistics_.Reset();
    last_batch_time_ = std::chrono::steady_clock::now();
}

StorageManager::~StorageManager() {
    Shutdown();
}

void StorageManager::InitializeLogger() {
    logger_ = spdlog::get("storage_manager");
    if (!logger_) {
        logger_ = spdlog::default_logger()->clone("storage_manager");
    }
}

bool StorageManager::Initialize() {
    if (running_.load()) {
        logger_->warn("StorageManager already running");
        return true;
    }
    
    logger_->info("Initializing StorageManager");
    
    // 初始化Redis存储
    if (config_.enable_redis && !InitializeRedisStorage()) {
        logger_->error("Failed to initialize Redis storage");
        return false;
    }
    
    // 初始化ClickHouse存储
    if (config_.enable_clickhouse && !InitializeClickHouseStorage()) {
        logger_->error("Failed to initialize ClickHouse storage");
        return false;
    }
    
    running_ = true;
    shutdown_requested_ = false;
    
    // 启动工作线程
    worker_threads_.reserve(config_.worker_thread_count);
    for (size_t i = 0; i < config_.worker_thread_count; ++i) {
        worker_threads_.emplace_back(&StorageManager::WorkerLoop, this, i);
    }
    
    // 启动数据迁移线程
    if (config_.enable_auto_migration && config_.enable_clickhouse) {
        migration_thread_ = std::thread(&StorageManager::MigrationLoop, this);
    }
    
    // 启动清理线程
    cleanup_thread_ = std::thread(&StorageManager::CleanupLoop, this);
    
    logger_->info("StorageManager initialized successfully with {} worker threads", 
                  config_.worker_thread_count);
    
    return true;
}

void StorageManager::Shutdown() {
    if (!running_.load()) {
        return;
    }
    
    logger_->info("Shutting down StorageManager");
    
    shutdown_requested_ = true;
    running_ = false;
    
    // 通知所有线程停止
    queue_cv_.notify_all();
    
    // 等待工作线程结束
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    worker_threads_.clear();
    
    // 等待迁移线程结束
    if (migration_thread_.joinable()) {
        migration_thread_.join();
    }
    
    // 等待清理线程结束
    if (cleanup_thread_.joinable()) {
        cleanup_thread_.join();
    }
    
    // 刷新剩余批次
    FlushBatches();
    
    // 关闭存储组件
    if (redis_storage_) {
        redis_storage_->Shutdown();
    }
    
    if (clickhouse_storage_) {
        clickhouse_storage_->Disconnect();
    }
    
    logger_->info("StorageManager shutdown completed");
}

bool StorageManager::InitializeRedisStorage() {
    try {
        redis_storage_ = std::make_unique<RedisHotStorage>(config_.redis_config);
        
        if (!redis_storage_->Initialize()) {
            logger_->error("Failed to initialize Redis hot storage");
            return false;
        }
        
        logger_->info("Redis hot storage initialized successfully");
        return true;
    } catch (const std::exception& e) {
        logger_->error("Exception initializing Redis storage: {}", e.what());
        return false;
    }
}

bool StorageManager::InitializeClickHouseStorage() {
    try {
        clickhouse_storage_ = std::make_unique<ClickHouseStorage>(config_.clickhouse_config);
        
        if (!clickhouse_storage_->Initialize()) {
            logger_->error("Failed to initialize ClickHouse storage");
            return false;
        }
        
        logger_->info("ClickHouse storage initialized successfully");
        return true;
    } catch (const std::exception& e) {
        logger_->error("Exception initializing ClickHouse storage: {}", e.what());
        return false;
    }
}

// 同步写入接口实现
bool StorageManager::StoreTick(const StandardTick& tick) {
    if (!running_.load()) {
        return false;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    bool success = true;
    
    // 写入Redis热存储
    if (redis_storage_) {
        if (!redis_storage_->StoreTick(tick)) {
            logger_->warn("Failed to store tick to Redis: {}", tick.symbol);
            success = false;
        }
    }
    
    // 如果启用批处理，添加到批次中
    if (config_.enable_write_batching && clickhouse_storage_) {
        std::lock_guard<std::mutex> lock(batch_mutex_);
        tick_batch_.push_back(tick);
        
        if (ShouldFlushBatch()) {
            FlushBatches();
        }
    } else if (clickhouse_storage_) {
        // 直接写入ClickHouse
        if (!clickhouse_storage_->InsertTickData(ConvertToStandardizedTick(tick))) {
            logger_->warn("Failed to store tick to ClickHouse: {}", tick.symbol);
            success = false;
        }
    }
    
    // 更新统计
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    if (redis_storage_) {
        UpdateRedisStats(success, latency_ms);
    }
    if (clickhouse_storage_) {
        UpdateClickHouseStats(success, latency_ms);
    }
    
    return success;
}

bool StorageManager::StoreLevel2(const Level2Data& level2) {
    if (!running_.load()) {
        return false;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    bool success = true;
    
    // 写入Redis热存储
    if (redis_storage_) {
        if (!redis_storage_->StoreLevel2(level2)) {
            logger_->warn("Failed to store level2 to Redis: {}", level2.symbol);
            success = false;
        }
    }
    
    // 批处理或直接写入ClickHouse
    if (config_.enable_write_batching && clickhouse_storage_) {
        std::lock_guard<std::mutex> lock(batch_mutex_);
        level2_batch_.push_back(level2);
        
        if (ShouldFlushBatch()) {
            FlushBatches();
        }
    }
    
    // 更新统计
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    if (redis_storage_) {
        UpdateRedisStats(success, latency_ms);
    }
    
    return success;
}

bool StorageManager::StoreBatch(const std::vector<StandardTick>& ticks) {
    if (!running_.load() || ticks.empty()) {
        return false;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    bool success = true;
    
    // 批量写入Redis
    if (redis_storage_) {
        if (!redis_storage_->StoreBatch(ticks)) {
            logger_->warn("Failed to store tick batch to Redis, size: {}", ticks.size());
            success = false;
        }
    }
    
    // 批量写入ClickHouse
    if (clickhouse_storage_) {
        DataBatch<StandardizedTick> batch;
        for (const auto& tick : ticks) {
            batch.Add(ConvertToStandardizedTick(tick));
        }
        
        auto future = clickhouse_storage_->InsertTickDataBatch(batch);
        if (!future.get()) {
            logger_->warn("Failed to store tick batch to ClickHouse, size: {}", ticks.size());
            success = false;
        }
    }
    
    // 更新统计
    auto end_time = std::chrono::high_resolution_clock::now();
    auto latency_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    UpdateRedisStats(success, latency_ms);
    UpdateClickHouseStats(success, latency_ms);
    
    return success;
}

// 异步写入接口实现
std::future<bool> StorageManager::StoreTickAsync(const StandardTick& tick) {
    StorageTask task(StorageTaskType::STORE_TICK);
    task.ticks.push_back(tick);
    
    auto future = task.promise.get_future();
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        if (task_queue_.size() >= config_.async_queue_size) {
            task.promise.set_value(false);
            return future;
        }
        
        task_queue_.push(std::move(task));
        statistics_.pending_tasks++;
    }
    
    queue_cv_.notify_one();
    return future;
}

std::future<bool> StorageManager::StoreLevel2Async(const Level2Data& level2) {
    StorageTask task(StorageTaskType::STORE_LEVEL2);
    task.level2_data.push_back(level2);
    
    auto future = task.promise.get_future();
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        if (task_queue_.size() >= config_.async_queue_size) {
            task.promise.set_value(false);
            return future;
        }
        
        task_queue_.push(std::move(task));
        statistics_.pending_tasks++;
    }
    
    queue_cv_.notify_one();
    return future;
}

std::future<bool> StorageManager::StoreBatchAsync(const std::vector<StandardTick>& ticks) {
    StorageTask task(StorageTaskType::STORE_BATCH);
    task.ticks = ticks;
    
    auto future = task.promise.get_future();
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        if (task_queue_.size() >= config_.async_queue_size) {
            task.promise.set_value(false);
            return future;
        }
        
        task_queue_.push(std::move(task));
        statistics_.pending_tasks++;
    }
    
    queue_cv_.notify_one();
    return future;
}

// 查询接口实现
bool StorageManager::GetLatestTick(const std::string& symbol, StandardTick& tick) {
    // 优先从Redis查询
    if (redis_storage_ && redis_storage_->GetLatestTick(symbol, tick)) {
        return true;
    }
    
    // 回退到ClickHouse查询
    if (clickhouse_storage_) {
        auto now = std::chrono::system_clock::now();
        auto one_hour_ago = now - std::chrono::hours(1);
        
        auto result = clickhouse_storage_->QueryTickData(
            symbol, "", 
            std::chrono::duration_cast<std::chrono::nanoseconds>(one_hour_ago.time_since_epoch()).count(),
            std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count(),
            1
        );
        
        if (!result.data.empty()) {
            tick = ConvertFromStandardizedTick(result.data[0]);
            return true;
        }
    }
    
    return false;
}

std::vector<StandardTick> StorageManager::QueryTicks(const std::string& symbol, 
                                                    int64_t start_time, int64_t end_time, 
                                                    size_t limit) {
    std::vector<StandardTick> results;
    
    // 先从Redis查询热数据
    if (redis_storage_) {
        QueryOptions options;
        options.start_time_ns = start_time;
        options.end_time_ns = end_time;
        options.limit = static_cast<int>(limit);
        
        auto redis_result = redis_storage_->QueryTicks(symbol, options);
        for (const auto& tick : redis_result.ticks) {
            results.push_back(tick);
        }
    }
    
    // 如果需要更多数据，从ClickHouse查询
    if (results.size() < limit && clickhouse_storage_) {
        auto clickhouse_result = clickhouse_storage_->QueryTickData(
            symbol, "", start_time, end_time, limit - results.size()
        );
        
        for (const auto& standardized_tick : clickhouse_result.data) {
            results.push_back(ConvertFromStandardizedTick(standardized_tick));
        }
    }
    
    // 按时间戳排序
    std::sort(results.begin(), results.end(), 
              [](const StandardTick& a, const StandardTick& b) {
                  return a.timestamp_ns < b.timestamp_ns;
              });
    
    return results;
}

// 工作线程实现
void StorageManager::WorkerLoop(size_t worker_id) {
    logger_->debug("Storage worker thread {} started", worker_id);
    
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        // 等待任务或停止信号
        queue_cv_.wait(lock, [this] {
            return !task_queue_.empty() || !running_.load();
        });
        
        if (!running_.load()) {
            break;
        }
        
        // 获取任务
        if (task_queue_.empty()) {
            continue;
        }
        
        StorageTask task = std::move(task_queue_.front());
        task_queue_.pop();
        statistics_.pending_tasks--;
        
        lock.unlock();
        
        // 处理任务
        try {
            ProcessTask(task);
        } catch (const std::exception& e) {
            logger_->error("Worker {} task processing error: {}", worker_id, e.what());
            HandleTaskError(task, e.what());
        }
    }
    
    logger_->debug("Storage worker thread {} stopped", worker_id);
}

void StorageManager::ProcessTask(StorageTask& task) {
    bool success = false;
    
    switch (task.type) {
        case StorageTaskType::STORE_TICK:
            success = ProcessStoreTick(task);
            break;
        case StorageTaskType::STORE_LEVEL2:
            success = ProcessStoreLevel2(task);
            break;
        case StorageTaskType::STORE_BATCH:
            success = ProcessStoreBatch(task);
            break;
        case StorageTaskType::MIGRATE_DATA:
            success = ProcessMigrateData(task);
            break;
        case StorageTaskType::CLEANUP_EXPIRED:
            success = ProcessCleanupExpired(task);
            break;
        default:
            logger_->error("Unknown task type: {}", static_cast<int>(task.type));
            success = false;
    }
    
    // 设置任务结果
    task.promise.set_value(success);
    UpdateTaskStats(success);
}

bool StorageManager::ProcessStoreTick(const StorageTask& task) {
    if (task.ticks.empty()) {
        return false;
    }
    
    return StoreTick(task.ticks[0]);
}

bool StorageManager::ProcessStoreLevel2(const StorageTask& task) {
    if (task.level2_data.empty()) {
        return false;
    }
    
    return StoreLevel2(task.level2_data[0]);
}

bool StorageManager::ProcessStoreBatch(const StorageTask& task) {
    if (!task.ticks.empty()) {
        return StoreBatch(task.ticks);
    } else if (!task.level2_data.empty()) {
        return StoreBatch(task.level2_data);
    }
    
    return false;
}

// 批处理管理
void StorageManager::FlushBatches() {
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    // 刷新tick批次
    if (!tick_batch_.empty() && clickhouse_storage_) {
        DataBatch<StandardizedTick> batch;
        for (const auto& tick : tick_batch_) {
            batch.Add(ConvertToStandardizedTick(tick));
        }
        
        auto future = clickhouse_storage_->InsertTickDataBatch(batch);
        // 异步处理，不等待结果
        
        logger_->debug("Flushed {} ticks to ClickHouse", tick_batch_.size());
        tick_batch_.clear();
    }
    
    // 刷新level2批次
    if (!level2_batch_.empty()) {
        // TODO: 实现Level2批量写入到ClickHouse
        logger_->debug("Flushed {} level2 records", level2_batch_.size());
        level2_batch_.clear();
    }
    
    last_batch_time_ = std::chrono::steady_clock::now();
}

bool StorageManager::ShouldFlushBatch() const {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_batch_time_);
    
    return tick_batch_.size() >= config_.batch_size || 
           level2_batch_.size() >= config_.batch_size ||
           elapsed.count() >= config_.batch_timeout_ms;
}

// 数据迁移实现
void StorageManager::MigrationLoop() {
    logger_->info("Data migration thread started");
    
    while (running_.load()) {
        try {
            // 等待迁移间隔
            std::this_thread::sleep_for(std::chrono::hours(config_.migration_interval_hours));
            
            if (!running_.load()) {
                break;
            }
            
            // 执行数据迁移
            auto symbols = GetSymbolsForMigration();
            int64_t cutoff_time = GetCurrentTimestamp() - 
                                 (config_.hot_data_retention_hours * 3600 * 1000000000LL);
            
            for (const auto& symbol : symbols) {
                if (!running_.load()) {
                    break;
                }
                
                if (MigrateSymbolData(symbol, cutoff_time)) {
                    statistics_.migrated_records++;
                    logger_->debug("Migrated data for symbol: {}", symbol);
                } else {
                    statistics_.migration_errors++;
                    logger_->warn("Failed to migrate data for symbol: {}", symbol);
                }
            }
            
        } catch (const std::exception& e) {
            logger_->error("Migration thread error: {}", e.what());
            statistics_.migration_errors++;
        }
    }
    
    logger_->info("Data migration thread stopped");
}

// 清理线程实现
void StorageManager::CleanupLoop() {
    logger_->info("Data cleanup thread started");
    
    while (running_.load()) {
        try {
            // 每小时执行一次清理
            std::this_thread::sleep_for(std::chrono::hours(1));
            
            if (!running_.load()) {
                break;
            }
            
            // 清理Redis过期数据
            if (redis_storage_) {
                auto cleaned = redis_storage_->CleanupExpiredData();
                if (cleaned > 0) {
                    logger_->info("Cleaned {} expired keys from Redis", cleaned);
                    statistics_.cleanup_operations++;
                }
            }
            
        } catch (const std::exception& e) {
            logger_->error("Cleanup thread error: {}", e.what());
        }
    }
    
    logger_->info("Data cleanup thread stopped");
}

// 统计更新方法
void StorageManager::UpdateRedisStats(bool success, double latency_ms) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (success) {
        statistics_.redis_writes_success++;
    } else {
        statistics_.redis_writes_failed++;
    }
    
    // 更新平均延迟
    uint64_t total_ops = statistics_.redis_writes_success.load() + statistics_.redis_writes_failed.load();
    if (total_ops > 0) {
        double current_avg = statistics_.redis_avg_latency_ms.load();
        statistics_.redis_avg_latency_ms = (current_avg * (total_ops - 1) + latency_ms) / total_ops;
    }
}

void StorageManager::UpdateClickHouseStats(bool success, double latency_ms) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (success) {
        statistics_.clickhouse_writes_success++;
    } else {
        statistics_.clickhouse_writes_failed++;
    }
    
    // 更新平均延迟
    uint64_t total_ops = statistics_.clickhouse_writes_success.load() + statistics_.clickhouse_writes_failed.load();
    if (total_ops > 0) {
        double current_avg = statistics_.clickhouse_avg_latency_ms.load();
        statistics_.clickhouse_avg_latency_ms = (current_avg * (total_ops - 1) + latency_ms) / total_ops;
    }
}

void StorageManager::UpdateTaskStats(bool success) {
    if (success) {
        statistics_.completed_tasks++;
    } else {
        statistics_.failed_tasks++;
    }
}

// 获取统计信息
StorageStatistics StorageManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return statistics_;
}

void StorageManager::ResetStatistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    statistics_.Reset();
}

// 健康检查
StorageManager::HealthStatus StorageManager::GetHealthStatus() const {
    HealthStatus status;
    status.overall_healthy = true;
    status.error_message = "";
    
    // 检查Redis健康状态
    if (config_.enable_redis && redis_storage_) {
        auto redis_stats = redis_storage_->GetStats();
        status.redis_healthy = redis_stats.active_connections > 0;
        
        if (!status.redis_healthy) {
            status.overall_healthy = false;
            status.error_message += "Redis not connected; ";
        }
    } else {
        status.redis_healthy = true;
    }
    
    // 检查ClickHouse健康状态
    if (config_.enable_clickhouse && clickhouse_storage_) {
        status.clickhouse_healthy = clickhouse_storage_->IsConnected();
        
        if (!status.clickhouse_healthy) {
            status.overall_healthy = false;
            status.error_message += "ClickHouse not connected; ";
        }
    } else {
        status.clickhouse_healthy = true;
    }
    
    // 检查队列健康状态
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        status.queue_healthy = task_queue_.size() < config_.async_queue_size * 0.9;
        
        if (!status.queue_healthy) {
            status.overall_healthy = false;
            status.error_message += "Task queue overloaded; ";
        }
    }
    
    return status;
}

// 辅助方法实现
StandardizedTick StorageManager::ConvertToStandardizedTick(const StandardTick& tick) {
    StandardizedTick std_tick;
    std_tick.timestamp_ns = tick.timestamp_ns;
    std_tick.symbol = tick.symbol;
    std_tick.exchange = tick.exchange;
    std_tick.product_type = "futures"; // 默认为期货，实际应该根据symbol判断
    std_tick.last_price = tick.last_price;
    std_tick.volume = tick.volume;
    std_tick.turnover = tick.turnover;
    std_tick.open_interest = tick.open_interest;
    std_tick.sequence = tick.sequence;
    std_tick.trade_flag = tick.trade_flag;
    std_tick.settlement_price = tick.settlement_price;
    std_tick.pre_close_price = tick.pre_close_price;
    
    // 转换买卖盘数据
    for (size_t i = 0; i < std::min(tick.bids.size(), size_t(5)); ++i) {
        std_tick.bid_prices.push_back(tick.bids[i].price);
        std_tick.bid_volumes.push_back(static_cast<uint32_t>(tick.bids[i].volume));
    }
    
    for (size_t i = 0; i < std::min(tick.asks.size(), size_t(5)); ++i) {
        std_tick.ask_prices.push_back(tick.asks[i].price);
        std_tick.ask_volumes.push_back(static_cast<uint32_t>(tick.asks[i].volume));
    }
    
    return std_tick;
}

StandardTick StorageManager::ConvertFromStandardizedTick(const StandardizedTick& std_tick) {
    StandardTick tick;
    tick.timestamp_ns = std_tick.timestamp_ns;
    tick.symbol = std_tick.symbol;
    tick.exchange = std_tick.exchange;
    tick.last_price = std_tick.last_price;
    tick.volume = std_tick.volume;
    tick.turnover = std_tick.turnover;
    tick.open_interest = std_tick.open_interest;
    tick.sequence = std_tick.sequence;
    tick.trade_flag = std_tick.trade_flag;
    tick.settlement_price = std_tick.settlement_price;
    tick.pre_close_price = std_tick.pre_close_price;
    
    // 转换买卖盘数据
    for (size_t i = 0; i < std::min(std_tick.bid_prices.size(), size_t(5)); ++i) {
        if (i < tick.bids.size()) {
            tick.bids[i].price = std_tick.bid_prices[i];
            tick.bids[i].volume = std_tick.bid_volumes[i];
        }
    }
    
    for (size_t i = 0; i < std::min(std_tick.ask_prices.size(), size_t(5)); ++i) {
        if (i < tick.asks.size()) {
            tick.asks[i].price = std_tick.ask_prices[i];
            tick.asks[i].volume = std_tick.ask_volumes[i];
        }
    }
    
    return tick;
}

std::vector<std::string> StorageManager::GetSymbolsForMigration() {
    // 简化实现，实际应该从Redis获取所有活跃的symbol
    return {"CU2409", "AL2409", "ZN2409", "AU2412", "AG2412"};
}

bool StorageManager::MigrateSymbolData(const std::string& symbol, int64_t cutoff_time) {
    if (!redis_storage_ || !clickhouse_storage_) {
        return false;
    }
    
    try {
        // 从Redis查询需要迁移的数据
        QueryOptions options;
        options.start_time_ns = 0;
        options.end_time_ns = cutoff_time;
        options.limit = 10000; // 批量迁移
        
        auto result = redis_storage_->QueryTicks(symbol, options);
        
        if (!result.ticks.empty()) {
            // 转换并写入ClickHouse
            DataBatch<StandardizedTick> batch;
            for (const auto& tick : result.ticks) {
                batch.Add(ConvertToStandardizedTick(tick));
            }
            
            auto future = clickhouse_storage_->InsertTickDataBatch(batch);
            return future.get();
        }
        
        return true;
    } catch (const std::exception& e) {
        logger_->error("Migration error for symbol {}: {}", symbol, e.what());
        return false;
    }
}

int64_t StorageManager::GetCurrentTimestamp() const {
    return StandardTick::GetCurrentTimestampNs();
}

// 工厂方法实现
std::unique_ptr<StorageManager> StorageManagerFactory::CreateDefault() {
    StorageManagerConfig config;
    config.enable_redis = true;
    config.enable_clickhouse = true;
    config.worker_thread_count = 4;
    config.enable_write_batching = true;
    config.enable_auto_migration = true;
    
    return std::make_unique<StorageManager>(config);
}

std::unique_ptr<StorageManager> StorageManagerFactory::CreateHighPerformance() {
    StorageManagerConfig config;
    config.enable_redis = true;
    config.enable_clickhouse = true;
    config.worker_thread_count = 8;
    config.async_queue_size = 100000;
    config.batch_size = 2000;
    config.batch_timeout_ms = 50;
    config.enable_write_batching = true;
    config.enable_auto_migration = true;
    
    return std::make_unique<StorageManager>(config);
}

} // namespace financial_data