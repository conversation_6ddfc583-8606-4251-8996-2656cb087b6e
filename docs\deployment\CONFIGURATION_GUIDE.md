# Market Data Collection Enhancement - Configuration Guide

## Overview

This guide provides detailed information about configuring the Market Data Collection Enhancement system. The system uses a unified configuration approach with support for hot reloading and environment-specific settings.

## Configuration File Structure

The main configuration file is `config/unified_config.json`. Here's the complete structure with detailed explanations:

```json
{
  "collection": {
    "pytdx": {
      "enabled": true,
      "servers": [
        {"host": "**************", "port": 7709},
        {"host": "************", "port": 7709},
        {"host": "*************", "port": 7709}
      ],
      "batch_size": 1000,
      "concurrent_requests": 5,
      "archive_enabled": true,
      "archive_batch_size": 5000,
      "connection_timeout_seconds": 30,
      "read_timeout_seconds": 60,
      "retry_attempts": 3,
      "retry_delay_seconds": 5
    },
    "ctp": {
      "enabled": true,
      "config_path": "/app/config/ctp_config.json",
      "failover_timeout": 30,
      "heartbeat_interval": 10,
      "reconnect_attempts": 5,
      "reconnect_delay_seconds": 10
    },
    "coordination": {
      "priority_source": "ctp",
      "overlap_tolerance_seconds": 300,
      "enable_data_merge": true,
      "conflict_resolution_strategy": "timestamp_priority",
      "data_validation_enabled": true
    }
  },
  "storage": {
    "hot_storage": {
      "type": "redis",
      "retention_days": 7,
      "config": {
        "host": "redis-service",
        "port": 6379,
        "db": 0,
        "password": "",
        "max_connections": 100,
        "connection_timeout_seconds": 5,
        "socket_timeout_seconds": 10,
        "retry_on_timeout": true,
        "health_check_interval": 30
      }
    },
    "warm_storage": {
      "type": "clickhouse",
      "retention_days": 730,
      "config": {
        "host": "clickhouse-service",
        "port": 9000,
        "database": "market_data",
        "username": "market_user",
        "password": "market_password",
        "connection_timeout_seconds": 30,
        "query_timeout_seconds": 300,
        "max_connections": 50,
        "compression": "lz4"
      }
    },
    "cold_storage": {
      "type": "s3",
      "config": {
        "endpoint": "http://minio-service:9000",
        "access_key": "minioadmin",
        "secret_key": "minioadmin123",
        "bucket": "market-data-archive",
        "region": "us-east-1",
        "secure": false,
        "compression": "gzip",
        "part_size_mb": 64,
        "max_concurrent_uploads": 10
      }
    },
    "migration": {
      "hot_to_warm_threshold_days": 7,
      "warm_to_cold_threshold_days": 730,
      "migration_batch_size": 10000,
      "migration_schedule": "0 3 * * *",
      "enable_automatic_migration": true,
      "verify_after_migration": true
    }
  },
  "scheduling": {
    "historical_update": {
      "cron": "0 2 * * *",
      "symbols": ["all"],
      "lookback_days": 1,
      "batch_size": 1000,
      "max_concurrent_tasks": 5,
      "timeout_minutes": 120
    },
    "data_migration": {
      "cron": "0 3 * * *",
      "batch_size": 10000,
      "max_concurrent_migrations": 3,
      "timeout_minutes": 240
    },
    "health_check": {
      "cron": "*/5 * * * *",
      "timeout_seconds": 30,
      "alert_on_failure": true
    },
    "cleanup": {
      "cron": "0 4 * * 0",
      "retention_policy": {
        "logs_days": 30,
        "temp_files_hours": 24,
        "failed_tasks_days": 7
      }
    }
  },
  "monitoring": {
    "enable_metrics": true,
    "metrics_port": 9090,
    "alert_thresholds": {
      "data_delay_seconds": 60,
      "error_rate_percent": 5.0,
      "memory_usage_percent": 80,
      "disk_usage_percent": 85,
      "connection_failure_count": 10
    },
    "logging": {
      "level": "INFO",
      "format": "json",
      "output": "stdout",
      "file_rotation": {
        "max_size_mb": 100,
        "max_files": 10,
        "max_age_days": 30
      }
    }
  },
  "api": {
    "http": {
      "enabled": true,
      "port": 8080,
      "host": "0.0.0.0",
      "cors_enabled": true,
      "cors_origins": ["*"],
      "rate_limiting": {
        "enabled": true,
        "requests_per_minute": 1000,
        "burst_size": 100
      }
    },
    "grpc": {
      "enabled": true,
      "port": 8081,
      "host": "0.0.0.0",
      "max_message_size_mb": 64,
      "keepalive_time_seconds": 30,
      "keepalive_timeout_seconds": 5
    },
    "websocket": {
      "enabled": true,
      "path": "/ws",
      "max_connections": 1000,
      "ping_interval_seconds": 30,
      "pong_timeout_seconds": 10
    }
  },
  "security": {
    "authentication": {
      "enabled": true,
      "type": "bearer_token",
      "token_expiry_hours": 24
    },
    "encryption": {
      "enabled": false,
      "tls_cert_path": "/app/certs/server.crt",
      "tls_key_path": "/app/certs/server.key"
    }
  }
}
```

## Configuration Parameters Reference

### Collection Configuration

#### pytdx Section
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `enabled` | boolean | true | Enable/disable pytdx collector |
| `servers` | array | - | List of pytdx servers with host and port |
| `batch_size` | integer | 1000 | Number of records to fetch per request |
| `concurrent_requests` | integer | 5 | Maximum concurrent requests to pytdx servers |
| `archive_enabled` | boolean | true | Enable automatic data archiving |
| `archive_batch_size` | integer | 5000 | Batch size for archiving operations |
| `connection_timeout_seconds` | integer | 30 | Connection timeout |
| `read_timeout_seconds` | integer | 60 | Read timeout |
| `retry_attempts` | integer | 3 | Number of retry attempts on failure |
| `retry_delay_seconds` | integer | 5 | Delay between retry attempts |

#### ctp Section
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `enabled` | boolean | true | Enable/disable CTP collector |
| `config_path` | string | - | Path to CTP configuration file |
| `failover_timeout` | integer | 30 | Timeout for failover detection |
| `heartbeat_interval` | integer | 10 | Heartbeat interval in seconds |
| `reconnect_attempts` | integer | 5 | Number of reconnection attempts |
| `reconnect_delay_seconds` | integer | 10 | Delay between reconnection attempts |

#### coordination Section
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `priority_source` | string | "ctp" | Priority data source (ctp/pytdx) |
| `overlap_tolerance_seconds` | integer | 300 | Time overlap tolerance |
| `enable_data_merge` | boolean | true | Enable data merging from multiple sources |
| `conflict_resolution_strategy` | string | "timestamp_priority" | Strategy for resolving data conflicts |
| `data_validation_enabled` | boolean | true | Enable data validation |

### Storage Configuration

#### hot_storage Section (Redis)
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `retention_days` | integer | 7 | Data retention period in hot storage |
| `host` | string | - | Redis server hostname |
| `port` | integer | 6379 | Redis server port |
| `db` | integer | 0 | Redis database number |
| `password` | string | "" | Redis password |
| `max_connections` | integer | 100 | Maximum connection pool size |
| `connection_timeout_seconds` | integer | 5 | Connection timeout |
| `socket_timeout_seconds` | integer | 10 | Socket timeout |
| `retry_on_timeout` | boolean | true | Retry on timeout |
| `health_check_interval` | integer | 30 | Health check interval |

#### warm_storage Section (ClickHouse)
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `retention_days` | integer | 730 | Data retention period in warm storage |
| `host` | string | - | ClickHouse server hostname |
| `port` | integer | 9000 | ClickHouse server port |
| `database` | string | - | Database name |
| `username` | string | - | Database username |
| `password` | string | - | Database password |
| `connection_timeout_seconds` | integer | 30 | Connection timeout |
| `query_timeout_seconds` | integer | 300 | Query timeout |
| `max_connections` | integer | 50 | Maximum connection pool size |
| `compression` | string | "lz4" | Compression algorithm |

#### cold_storage Section (S3/MinIO)
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `endpoint` | string | - | S3 endpoint URL |
| `access_key` | string | - | S3 access key |
| `secret_key` | string | - | S3 secret key |
| `bucket` | string | - | S3 bucket name |
| `region` | string | "us-east-1" | S3 region |
| `secure` | boolean | false | Use HTTPS |
| `compression` | string | "gzip" | Compression algorithm |
| `part_size_mb` | integer | 64 | Multipart upload part size |
| `max_concurrent_uploads` | integer | 10 | Maximum concurrent uploads |

### Scheduling Configuration

#### Cron Expression Format
The system uses standard cron expressions with 5 fields:
```
* * * * *
│ │ │ │ │
│ │ │ │ └─── day of week (0-7, Sunday = 0 or 7)
│ │ │ └───── month (1-12)
│ │ └─────── day of month (1-31)
│ └───────── hour (0-23)
└─────────── minute (0-59)
```

Examples:
- `0 2 * * *` - Daily at 2:00 AM
- `*/5 * * * *` - Every 5 minutes
- `0 0 * * 0` - Weekly on Sunday at midnight
- `0 3 1 * *` - Monthly on the 1st at 3:00 AM

### Monitoring Configuration

#### Alert Thresholds
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `data_delay_seconds` | integer | 60 | Alert if data is delayed more than this |
| `error_rate_percent` | float | 5.0 | Alert if error rate exceeds this percentage |
| `memory_usage_percent` | integer | 80 | Alert if memory usage exceeds this |
| `disk_usage_percent` | integer | 85 | Alert if disk usage exceeds this |
| `connection_failure_count` | integer | 10 | Alert if connection failures exceed this |

#### Logging Levels
- `DEBUG`: Detailed debugging information
- `INFO`: General information messages
- `WARN`: Warning messages
- `ERROR`: Error messages
- `FATAL`: Fatal error messages

## Environment-Specific Configuration

### Development Environment
```json
{
  "collection": {
    "pytdx": {
      "batch_size": 100,
      "concurrent_requests": 2
    }
  },
  "monitoring": {
    "logging": {
      "level": "DEBUG"
    }
  }
}
```

### Production Environment
```json
{
  "collection": {
    "pytdx": {
      "batch_size": 5000,
      "concurrent_requests": 10
    }
  },
  "monitoring": {
    "logging": {
      "level": "INFO"
    }
  },
  "security": {
    "authentication": {
      "enabled": true
    },
    "encryption": {
      "enabled": true
    }
  }
}
```

## Configuration Validation

The system validates configuration on startup and during hot reloads. Common validation rules:

1. **Required Fields**: All required fields must be present
2. **Type Validation**: Values must match expected types
3. **Range Validation**: Numeric values must be within valid ranges
4. **Format Validation**: Strings must match expected formats (e.g., cron expressions)
5. **Dependency Validation**: Related configurations must be consistent

### Validation Errors
```json
{
  "error": "Configuration validation failed",
  "details": [
    {
      "field": "collection.pytdx.batch_size",
      "message": "Value must be between 1 and 10000",
      "value": 15000
    },
    {
      "field": "scheduling.historical_update.cron",
      "message": "Invalid cron expression",
      "value": "invalid cron"
    }
  ]
}
```

## Hot Configuration Reload

The system supports hot reloading of configuration without restart:

### Supported Changes
- Collection parameters (batch sizes, timeouts)
- Storage connection settings
- Monitoring thresholds
- Scheduling parameters
- API settings

### Non-Reloadable Changes
- Storage types (Redis → ClickHouse)
- Core system architecture
- Security certificates

### Reload Methods

#### Via API
```bash
curl -X PUT http://localhost:8080/api/v1/config \
  -H "Content-Type: application/json" \
  -d @new_config.json
```

#### Via File Watch
The system automatically detects changes to the configuration file and reloads.

#### Via Signal
```bash
# Send SIGHUP to reload configuration
kill -HUP <process_id>
```

## Configuration Best Practices

### 1. Environment Variables
Use environment variables for sensitive information:

```json
{
  "storage": {
    "warm_storage": {
      "config": {
        "password": "${CLICKHOUSE_PASSWORD}"
      }
    }
  }
}
```

### 2. Configuration Layering
Use multiple configuration files for different environments:

```
config/
├── base_config.json          # Base configuration
├── development_config.json   # Development overrides
├── staging_config.json       # Staging overrides
└── production_config.json    # Production overrides
```

### 3. Backup Configuration
Always backup configuration before changes:

```bash
cp config/unified_config.json config/unified_config.json.backup
```

### 4. Validation Testing
Test configuration changes in a development environment first:

```bash
# Validate configuration
./bin/financial_data_service --validate-config --config=config/unified_config.json
```

### 5. Monitoring Configuration Changes
Monitor configuration changes and their effects:

```bash
# Check configuration reload status
curl http://localhost:8080/api/v1/config/reload-status
```

## Troubleshooting Configuration Issues

### Common Issues

#### 1. Connection Timeouts
```json
{
  "storage": {
    "warm_storage": {
      "config": {
        "connection_timeout_seconds": 60,  // Increase timeout
        "query_timeout_seconds": 600       // Increase query timeout
      }
    }
  }
}
```

#### 2. Memory Issues
```json
{
  "collection": {
    "pytdx": {
      "batch_size": 500,           // Reduce batch size
      "concurrent_requests": 3     // Reduce concurrency
    }
  }
}
```

#### 3. Performance Issues
```json
{
  "storage": {
    "hot_storage": {
      "config": {
        "max_connections": 200     // Increase connection pool
      }
    }
  }
}
```

### Configuration Debugging

Enable debug logging to troubleshoot configuration issues:

```json
{
  "monitoring": {
    "logging": {
      "level": "DEBUG"
    }
  }
}
```

Check configuration validation logs:
```bash
# View configuration validation logs
kubectl logs -n market-data deployment/market-data-collector | grep "config"
```

## Security Considerations

### 1. Sensitive Data
Never store sensitive data in plain text:
- Use environment variables
- Use secret management systems
- Encrypt sensitive configuration sections

### 2. Access Control
Restrict access to configuration files:
```bash
chmod 600 config/unified_config.json
chown app:app config/unified_config.json
```

### 3. Configuration Validation
Always validate configuration from untrusted sources:
- Sanitize input values
- Validate against schema
- Check for malicious content

### 4. Audit Trail
Log all configuration changes:
```json
{
  "monitoring": {
    "audit": {
      "enabled": true,
      "log_config_changes": true,
      "log_access_attempts": true
    }
  }
}
```