cmake_minimum_required(VERSION 3.15)
project(FinancialDataService VERSION 1.0.0 LANGUAGES CXX)

# Set C++17 standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler flags for MSVC
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /DDEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O3")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -DDEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
endif()

# Find required packages using vcpkg
find_package(Threads REQUIRED)
find_package(spdlog CONFIG REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)
find_package(GTest CONFIG REQUIRED)
find_package(protobuf CONFIG REQUIRED)
find_package(clickhouse-cpp CONFIG REQUIRED)
find_package(hiredis CONFIG REQUIRED)
find_package(websocketpp CONFIG REQUIRED)
find_package(asio CONFIG REQUIRED)
find_package(ZLIB REQUIRED)

# CTP API configuration
if(WIN32)
    set(CTP_API_ROOT "${CMAKE_SOURCE_DIR}/third_party/ctp/traderapi_se_windows64")
else()
    set(CTP_API_ROOT "${CMAKE_SOURCE_DIR}/third_party/ctp/traderapi_se_linux64")
endif()
set(CTP_INCLUDE_DIR "${CTP_API_ROOT}")
set(CTP_LIB_DIR "${CTP_API_ROOT}")

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/src)
include_directories(${CTP_INCLUDE_DIR})

# Add source subdirectories
add_subdirectory(src)

# Find zlib for data compression
find_package(ZLIB REQUIRED)

# CTP API libraries
find_library(CTP_MD_LIB thostmduserapi_se PATHS ${CTP_LIB_DIR} NO_DEFAULT_PATH)
find_library(CTP_TRADER_LIB thosttraderapi_se PATHS ${CTP_LIB_DIR} NO_DEFAULT_PATH)

if(NOT CTP_MD_LIB)
    message(FATAL_ERROR "CTP MD API library not found in ${CTP_LIB_DIR}")
endif()

if(NOT CTP_TRADER_LIB)
    message(FATAL_ERROR "CTP Trader API library not found in ${CTP_LIB_DIR}")
endif()

message(STATUS "Found CTP MD API: ${CTP_MD_LIB}")
message(STATUS "Found CTP Trader API: ${CTP_TRADER_LIB}")

# Create library
add_library(financial_data_lib)
target_link_libraries(financial_data_lib 
    proto
    collectors
    databus
    storage
    interfaces
    failover
    Threads::Threads
    spdlog::spdlog
    nlohmann_json::nlohmann_json
    clickhouse-cpp-lib
    hiredis::hiredis
    websocketpp::websocketpp
    asio::asio
    ZLIB::ZLIB
    ${CTP_MD_LIB}
    ${CTP_TRADER_LIB}
)

# Main executable
add_executable(financial_data_service src/main.cpp)
target_link_libraries(financial_data_service financial_data_lib)

# Test executable with Google Test
enable_testing()

# Main test executable
file(GLOB TEST_SOURCES 
    "tests/collectors/*.cpp"
    "tests/ctp_collector_test.cpp"
)
add_executable(financial_data_tests ${TEST_SOURCES})
target_link_libraries(financial_data_tests 
    financial_data_lib
    databus
    GTest::gtest 
    GTest::gtest_main
    GTest::gmock
    GTest::gmock_main
)

# DataBus performance test
add_executable(databus_performance_test tests/databus_performance_test.cpp)
target_link_libraries(databus_performance_test 
    financial_data_lib
    databus
    GTest::gtest 
    GTest::gtest_main
)

# DataBus integration test
add_executable(databus_integration_test tests/databus_integration_test.cpp)
target_link_libraries(databus_integration_test 
    financial_data_lib
    databus
    GTest::gtest 
    GTest::gtest_main
)

# Failover test
add_executable(failover_test tests/failover_test.cpp)
target_link_libraries(failover_test 
    financial_data_lib
    failover
    GTest::gtest 
    GTest::gtest_main
    GTest::gmock
    GTest::gmock_main
)

# Add tests
include(GoogleTest)
gtest_discover_tests(financial_data_tests)
gtest_discover_tests(databus_performance_test)
gtest_discover_tests(databus_integration_test)
gtest_discover_tests(clickhouse_warm_storage_test)
gtest_discover_tests(failover_test)

# Simple test executable (for quick testing)
add_executable(simple_ctp_test tests/simple_test.cpp)
target_link_libraries(simple_ctp_test financial_data_lib)

# ClickHouse warm storage test
add_executable(clickhouse_warm_storage_test tests/clickhouse_warm_storage_test.cpp)
target_link_libraries(clickhouse_warm_storage_test 
    financial_data_lib
    GTest::gtest 
    GTest::gtest_main
    GTest::gmock
    GTest::gmock_main
    clickhouse-cpp-lib
)

# ClickHouse warm storage demo
add_executable(clickhouse_warm_storage_demo examples/clickhouse_warm_storage_demo.cpp)
target_link_libraries(clickhouse_warm_storage_demo 
    financial_data_lib
    clickhouse-cpp-lib
)

# CTP collector demo
add_executable(ctp_collector_demo examples/ctp_collector_demo.cpp)
target_link_libraries(ctp_collector_demo 
    financial_data_lib
)

# High availability demo
add_executable(high_availability_demo examples/high_availability_demo.cpp)
target_link_libraries(high_availability_demo 
    financial_data_lib
    failover
)

# Connection test utility
add_executable(connection_test src/connection_test.cpp)
target_link_libraries(connection_test 
    nlohmann_json::nlohmann_json
)
if(WIN32)
    target_link_libraries(connection_test ws2_32)
endif()

# Performance tests
add_subdirectory(tests/performance)

# Install targets
install(TARGETS financial_data_service DESTINATION bin)
install(DIRECTORY config/ DESTINATION etc/financial-data-service)
install(FILES docs/README.md DESTINATION share/doc/financial-data-service)