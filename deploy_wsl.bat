@echo off
REM WSL环境部署批处理脚本
REM 用于在Windows下通过WSL部署金融数据服务

setlocal enabledelayedexpansion

echo ========================================
echo     金融数据服务 - WSL部署助手
echo ========================================
echo.

REM 检查WSL是否可用
wsl --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] WSL未安装或不可用
    echo 请先安装WSL2: https://docs.microsoft.com/en-us/windows/wsl/install
    pause
    exit /b 1
)

echo [INFO] 检测到WSL环境
wsl --list --verbose

echo.
echo 选择部署选项:
echo 1. 快速测试 (验证环境)
echo 2. 完整部署 (安装所有服务)
echo 3. 查看服务状态
echo 4. 停止服务
echo 5. 重启服务
echo 6. 进入WSL终端
echo.

set /p choice="请选择 (1-6): "

if "%choice%"=="1" goto quick_test
if "%choice%"=="2" goto full_deploy
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto stop_services
if "%choice%"=="5" goto restart_services
if "%choice%"=="6" goto enter_wsl
goto invalid_choice

:quick_test
echo.
echo [INFO] 运行快速测试...
wsl bash -c "cd '%cd%' && python3 quick_test_wsl.py"
goto end

:full_deploy
echo.
echo [INFO] 开始完整部署...
echo [INFO] 这可能需要几分钟时间，请耐心等待...
echo.

REM 给脚本添加执行权限
wsl bash -c "cd '%cd%' && chmod +x deploy_wsl_test.sh"

REM 运行部署脚本
wsl bash -c "cd '%cd%' && ./deploy_wsl_test.sh deploy"
goto end

:check_status
echo.
echo [INFO] 检查服务状态...
wsl bash -c "cd '%cd%' && ./deploy_wsl_test.sh --status"
goto end

:stop_services
echo.
echo [INFO] 停止服务...
wsl bash -c "cd '%cd%' && ./deploy_wsl_test.sh --stop"
goto end

:restart_services
echo.
echo [INFO] 重启服务...
wsl bash -c "cd '%cd%' && ./deploy_wsl_test.sh --restart"
goto end

:enter_wsl
echo.
echo [INFO] 进入WSL终端...
echo [INFO] 在WSL中，你可以运行以下命令:
echo   ./deploy_wsl_test.sh --help    查看帮助
echo   python3 quick_test_wsl.py      快速测试
echo   python3 test_enhanced_features.py  功能测试
echo.
wsl bash -c "cd '%cd%' && bash"
goto end

:invalid_choice
echo.
echo [ERROR] 无效选择，请重新运行脚本
goto end

:end
echo.
echo ========================================
echo 部署完成！
echo.
echo 有用的命令:
echo   查看日志: wsl bash -c "cd '%cd%' && tail -f logs/scheduler_service.log"
echo   进入WSL: wsl bash -c "cd '%cd%' && bash"
echo   重新部署: %~nx0
echo.
echo 服务访问地址:
echo   Redis: localhost:6379
echo   ClickHouse: http://localhost:8123
echo   监控面板: http://localhost:9090
echo ========================================
echo.
pause