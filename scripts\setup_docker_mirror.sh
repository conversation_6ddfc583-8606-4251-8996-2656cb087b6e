#!/bin/bash

echo "配置 Docker 镜像加速器..."

# 创建 Docker daemon 配置目录
sudo mkdir -p /etc/docker

# 配置镜像加速器
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}
EOF

echo "重启 Docker 服务..."
sudo systemctl daemon-reload
sudo systemctl restart docker

echo "检查 Docker 状态..."
sudo systemctl status docker --no-pager -l

echo "验证镜像加速器配置..."
docker info | grep -A 10 "Registry Mirrors"

echo "Docker 镜像加速器配置完成！"