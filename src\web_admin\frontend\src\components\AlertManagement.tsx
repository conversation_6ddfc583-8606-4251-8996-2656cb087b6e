import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Popconfirm,
  Tag,
  Space,
  Card,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  BellOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';

interface AlertConfig {
  id: string;
  name: string;
  metric: string;
  threshold: number;
  operator: string;
  enabled: boolean;
  notification_channels: string[];
}

const AlertManagement: React.FC = () => {
  const [alerts, setAlerts] = useState<AlertConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAlert, setEditingAlert] = useState<AlertConfig | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchAlerts();
  }, []);

  const fetchAlerts = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/alerts');
      setAlerts(response.data);
    } catch (error) {
      message.error('获取告警配置失败');
      console.error('Error fetching alerts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAlert = () => {
    setEditingAlert(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditAlert = (alert: AlertConfig) => {
    setEditingAlert(alert);
    form.setFieldsValue({
      name: alert.name,
      metric: alert.metric,
      threshold: alert.threshold,
      operator: alert.operator,
      enabled: alert.enabled,
      notification_channels: alert.notification_channels
    });
    setModalVisible(true);
  };

  const handleDeleteAlert = async (alertId: string) => {
    try {
      await axios.delete(`/api/alerts/${alertId}`);
      message.success('告警配置删除成功');
      fetchAlerts();
    } catch (error) {
      message.error('删除告警配置失败');
      console.error('Error deleting alert:', error);
    }
  };

  const handleToggleAlert = async (alertId: string, enabled: boolean) => {
    try {
      const alert = alerts.find(a => a.id === alertId);
      if (alert) {
        await axios.put(`/api/alerts/${alertId}`, {
          ...alert,
          enabled
        });
        message.success(`告警已${enabled ? '启用' : '禁用'}`);
        fetchAlerts();
      }
    } catch (error) {
      message.error('更新告警状态失败');
      console.error('Error toggling alert:', error);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingAlert) {
        // 更新告警配置
        await axios.put(`/api/alerts/${editingAlert.id}`, values);
        message.success('告警配置更新成功');
      } else {
        // 创建告警配置
        await axios.post('/api/alerts', values);
        message.success('告警配置创建成功');
      }
      
      setModalVisible(false);
      fetchAlerts();
    } catch (error) {
      message.error('操作失败');
      console.error('Error saving alert:', error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    form.resetFields();
  };

  const metricOptions = [
    { value: 'cpu_usage', label: 'CPU使用率 (%)' },
    { value: 'memory_usage', label: '内存使用率 (%)' },
    { value: 'disk_usage', label: '磁盘使用率 (%)' },
    { value: 'latency_p99', label: 'P99延迟 (μs)' },
    { value: 'error_rate', label: '错误率 (%)' },
    { value: 'active_connections', label: '活跃连接数' },
    { value: 'data_throughput', label: '数据吞吐量 (条/秒)' }
  ];

  const operatorOptions = [
    { value: '>', label: '大于 (>)' },
    { value: '>=', label: '大于等于 (>=)' },
    { value: '<', label: '小于 (<)' },
    { value: '<=', label: '小于等于 (<=)' },
    { value: '==', label: '等于 (==)' }
  ];

  const channelOptions = [
    { value: 'email', label: '邮件' },
    { value: 'sms', label: '短信' },
    { value: 'webhook', label: 'Webhook' },
    { value: 'dingtalk', label: '钉钉' },
    { value: 'wechat', label: '微信' }
  ];

  const columns = [
    {
      title: '告警名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <BellOutlined />
          {text}
        </Space>
      )
    },
    {
      title: '监控指标',
      dataIndex: 'metric',
      key: 'metric',
      render: (metric: string) => {
        const option = metricOptions.find(opt => opt.value === metric);
        return option ? option.label : metric;
      }
    },
    {
      title: '阈值条件',
      key: 'condition',
      render: (_, record: AlertConfig) => (
        <span>{record.operator} {record.threshold}</span>
      )
    },
    {
      title: '通知渠道',
      dataIndex: 'notification_channels',
      key: 'notification_channels',
      render: (channels: string[]) => (
        <Space>
          {channels.map(channel => {
            const option = channelOptions.find(opt => opt.value === channel);
            return (
              <Tag key={channel} color="blue">
                {option ? option.label : channel}
              </Tag>
            );
          })}
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean, record: AlertConfig) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleToggleAlert(record.id, checked)}
          checkedChildren={<CheckCircleOutlined />}
          unCheckedChildren={<ExclamationCircleOutlined />}
        />
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: AlertConfig) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditAlert(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个告警配置吗？"
            onConfirm={() => handleDeleteAlert(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  const enabledCount = alerts.filter(a => a.enabled).length;
  const disabledCount = alerts.filter(a => !a.enabled).length;

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总告警规则"
              value={alerts.length}
              prefix={<BellOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="已启用"
              value={enabledCount}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="已禁用"
              value={disabledCount}
              valueStyle={{ color: '#f5222d' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <h2>告警管理</h2>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateAlert}
          >
            新建告警
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={alerts}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个告警规则`
          }}
        />
      </Card>

      <Modal
        title={editingAlert ? '编辑告警配置' : '新建告警配置'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ enabled: true, operator: '>', notification_channels: ['email'] }}
        >
          <Form.Item
            name="name"
            label="告警名称"
            rules={[{ required: true, message: '请输入告警名称' }]}
          >
            <Input placeholder="请输入告警名称" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="metric"
                label="监控指标"
                rules={[{ required: true, message: '请选择监控指标' }]}
              >
                <Select placeholder="请选择监控指标" options={metricOptions} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="operator"
                label="比较操作符"
                rules={[{ required: true, message: '请选择操作符' }]}
              >
                <Select placeholder="操作符" options={operatorOptions} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="threshold"
                label="阈值"
                rules={[{ required: true, message: '请输入阈值' }]}
              >
                <InputNumber
                  placeholder="阈值"
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notification_channels"
            label="通知渠道"
            rules={[{ required: true, message: '请选择至少一个通知渠道' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择通知渠道"
              options={channelOptions}
            />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AlertManagement;