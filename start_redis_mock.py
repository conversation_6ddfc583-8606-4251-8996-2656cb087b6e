#!/usr/bin/env python3
"""
Redis模拟服务器
用于在没有Redis的环境下进行测试
"""

import asyncio
import socket
import threading
import time
from datetime import datetime

class MockRedisServer:
    def __init__(self, host='localhost', port=6379):
        self.host = host
        self.port = port
        self.data = {}
        self.running = False
        self.server = None
        
    def start(self):
        """启动模拟Redis服务器"""
        self.running = True
        
        def run_server():
            try:
                self.server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.server.bind((self.host, self.port))
                self.server.listen(5)
                print(f"[INFO] Mock Redis服务器启动在 {self.host}:{self.port}")
                
                while self.running:
                    try:
                        client, addr = self.server.accept()
                        threading.Thread(target=self.handle_client, args=(client,)).start()
                    except Exception as e:
                        if self.running:
                            print(f"[ERROR] 接受连接失败: {e}")
                        break
                        
            except Exception as e:
                print(f"[ERROR] 启动服务器失败: {e}")
        
        self.server_thread = threading.Thread(target=run_server)
        self.server_thread.daemon = True
        self.server_thread.start()
        
        # 等待服务器启动
        time.sleep(0.5)
        return True
    
    def handle_client(self, client):
        """处理客户端连接"""
        try:
            while self.running:
                data = client.recv(1024)
                if not data:
                    break
                
                # 简单的PING响应
                command = data.decode('utf-8').strip().upper()
                if 'PING' in command:
                    response = "+PONG\r\n"
                    client.send(response.encode('utf-8'))
                elif 'SET' in command:
                    response = "+OK\r\n"
                    client.send(response.encode('utf-8'))
                elif 'GET' in command:
                    response = "$-1\r\n"  # NULL response
                    client.send(response.encode('utf-8'))
                else:
                    response = "+OK\r\n"
                    client.send(response.encode('utf-8'))
                    
        except Exception as e:
            print(f"[ERROR] 处理客户端失败: {e}")
        finally:
            client.close()
    
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.server:
            self.server.close()
        print("[INFO] Mock Redis服务器已停止")

def main():
    """主函数"""
    print("=" * 50)
    print("Mock Redis服务器")
    print("=" * 50)
    
    server = MockRedisServer()
    
    try:
        if server.start():
            print("✅ 服务器启动成功")
            print("按 Ctrl+C 停止服务器")
            
            # 保持运行
            while True:
                time.sleep(1)
                
    except KeyboardInterrupt:
        print("\n收到中断信号...")
    except Exception as e:
        print(f"❌ 服务器异常: {e}")
    finally:
        server.stop()

if __name__ == "__main__":
    main()