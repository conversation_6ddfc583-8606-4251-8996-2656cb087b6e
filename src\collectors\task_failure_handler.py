"""
任务失败处理机制 - 错误处理、重试策略和状态持久化

功能特性：
- 指数退避重试策略
- 错误分类和处理逻辑
- 任务状态持久化
- 失败恢复机制
- 错误统计和分析
"""

import asyncio
import logging
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Set, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
import random
import hashlib
from abc import ABC, abstractmethod
import pickle

logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"                    # 网络连接错误
    DATA_SOURCE_ERROR = "data_source_error"            # 数据源错误
    STORAGE_ERROR = "storage_error"                    # 存储错误
    VALIDATION_ERROR = "validation_error"              # 数据验证错误
    TIMEOUT_ERROR = "timeout_error"                    # 超时错误
    AUTHENTICATION_ERROR = "authentication_error"      # 认证错误
    RATE_LIMIT_ERROR = "rate_limit_error"              # 限流错误
    RESOURCE_ERROR = "resource_error"                  # 资源不足错误
    CONFIGURATION_ERROR = "configuration_error"        # 配置错误
    UNKNOWN_ERROR = "unknown_error"                    # 未知错误


class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    LOW = 1      # 低级错误，可以忽略
    MEDIUM = 2   # 中级错误，需要重试
    HIGH = 3     # 高级错误，需要立即处理
    CRITICAL = 4 # 严重错误，需要停止任务


class RetryStrategy(Enum):
    """重试策略枚举"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"    # 指数退避
    LINEAR_BACKOFF = "linear_backoff"              # 线性退避
    FIXED_INTERVAL = "fixed_interval"              # 固定间隔
    IMMEDIATE = "immediate"                        # 立即重试
    NO_RETRY = "no_retry"                         # 不重试


@dataclass
class ErrorInfo:
    """错误信息类"""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    timestamp: datetime
    task_id: str
    execution_id: str
    retry_count: int = 0
    stack_trace: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_type': self.error_type.value,
            'severity': self.severity.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'task_id': self.task_id,
            'execution_id': self.execution_id,
            'retry_count': self.retry_count,
            'stack_trace': self.stack_trace,
            'context': self.context
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ErrorInfo':
        """从字典创建"""
        return cls(
            error_type=ErrorType(data['error_type']),
            severity=ErrorSeverity(data['severity']),
            message=data['message'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            task_id=data['task_id'],
            execution_id=data['execution_id'],
            retry_count=data.get('retry_count', 0),
            stack_trace=data.get('stack_trace'),
            context=data.get('context', {})
        )


@dataclass
class RetryConfig:
    """重试配置类"""
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    max_retries: int = 3
    base_delay_seconds: float = 1.0
    max_delay_seconds: float = 300.0  # 5分钟
    backoff_multiplier: float = 2.0
    jitter: bool = True  # 添加随机抖动
    retry_on_errors: Set[ErrorType] = field(default_factory=lambda: {
        ErrorType.NETWORK_ERROR,
        ErrorType.DATA_SOURCE_ERROR,
        ErrorType.TIMEOUT_ERROR,
        ErrorType.RATE_LIMIT_ERROR,
        ErrorType.RESOURCE_ERROR
    })
    no_retry_on_errors: Set[ErrorType] = field(default_factory=lambda: {
        ErrorType.AUTHENTICATION_ERROR,
        ErrorType.CONFIGURATION_ERROR,
        ErrorType.VALIDATION_ERROR
    })


@dataclass
class TaskState:
    """任务状态类"""
    task_id: str
    execution_id: str
    status: str  # scheduled, running, completed, failed, retrying
    start_time: datetime
    last_update_time: datetime
    retry_count: int = 0
    error_history: List[ErrorInfo] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'execution_id': self.execution_id,
            'status': self.status,
            'start_time': self.start_time.isoformat(),
            'last_update_time': self.last_update_time.isoformat(),
            'retry_count': self.retry_count,
            'error_history': [error.to_dict() for error in self.error_history],
            'context': self.context
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskState':
        """从字典创建"""
        return cls(
            task_id=data['task_id'],
            execution_id=data['execution_id'],
            status=data['status'],
            start_time=datetime.fromisoformat(data['start_time']),
            last_update_time=datetime.fromisoformat(data['last_update_time']),
            retry_count=data.get('retry_count', 0),
            error_history=[ErrorInfo.from_dict(e) for e in data.get('error_history', [])],
            context=data.get('context', {})
        )


class ErrorClassifier:
    """错误分类器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.ErrorClassifier")
        
        # 错误模式映射
        self.error_patterns = {
            ErrorType.NETWORK_ERROR: [
                'connection', 'network', 'timeout', 'unreachable', 'dns',
                'socket', 'connection refused', 'connection reset'
            ],
            ErrorType.DATA_SOURCE_ERROR: [
                'data source', 'api error', 'service unavailable', 'bad gateway',
                'internal server error', 'service error'
            ],
            ErrorType.STORAGE_ERROR: [
                'storage', 'database', 'disk', 'write error', 'read error',
                'permission denied', 'no space left'
            ],
            ErrorType.VALIDATION_ERROR: [
                'validation', 'invalid data', 'format error', 'schema error',
                'data integrity', 'constraint violation'
            ],
            ErrorType.TIMEOUT_ERROR: [
                'timeout', 'time out', 'deadline exceeded', 'request timeout'
            ],
            ErrorType.AUTHENTICATION_ERROR: [
                'authentication', 'unauthorized', 'access denied', 'forbidden',
                'invalid credentials', 'token expired'
            ],
            ErrorType.RATE_LIMIT_ERROR: [
                'rate limit', 'too many requests', 'quota exceeded', 'throttled'
            ],
            ErrorType.RESOURCE_ERROR: [
                'memory', 'cpu', 'resource', 'out of memory', 'resource exhausted'
            ],
            ErrorType.CONFIGURATION_ERROR: [
                'configuration', 'config', 'setting', 'parameter', 'missing required'
            ]
        }
        
        # 严重程度映射
        self.severity_mapping = {
            ErrorType.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.DATA_SOURCE_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.STORAGE_ERROR: ErrorSeverity.HIGH,
            ErrorType.VALIDATION_ERROR: ErrorSeverity.LOW,
            ErrorType.TIMEOUT_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.AUTHENTICATION_ERROR: ErrorSeverity.HIGH,
            ErrorType.RATE_LIMIT_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.RESOURCE_ERROR: ErrorSeverity.HIGH,
            ErrorType.CONFIGURATION_ERROR: ErrorSeverity.CRITICAL,
            ErrorType.UNKNOWN_ERROR: ErrorSeverity.MEDIUM
        }
    
    def classify_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorInfo:
        """分类错误"""
        error_message = str(exception).lower()
        error_type = ErrorType.UNKNOWN_ERROR
        
        # 首先根据异常类型进行分类（优先级更高）
        if isinstance(exception, TimeoutError):
            error_type = ErrorType.TIMEOUT_ERROR
        elif isinstance(exception, PermissionError):
            error_type = ErrorType.AUTHENTICATION_ERROR
        elif isinstance(exception, ValueError):
            error_type = ErrorType.VALIDATION_ERROR
        elif isinstance(exception, MemoryError):
            error_type = ErrorType.RESOURCE_ERROR
        elif isinstance(exception, (ConnectionError, OSError)):
            error_type = ErrorType.NETWORK_ERROR
        else:
            # 如果异常类型无法确定，则根据错误消息匹配错误类型
            for err_type, patterns in self.error_patterns.items():
                if any(pattern in error_message for pattern in patterns):
                    error_type = err_type
                    break
        
        severity = self.severity_mapping.get(error_type, ErrorSeverity.MEDIUM)
        
        # 获取堆栈跟踪
        import traceback
        stack_trace = traceback.format_exc()
        
        return ErrorInfo(
            error_type=error_type,
            severity=severity,
            message=str(exception),
            timestamp=datetime.now(),
            task_id=context.get('task_id', 'unknown') if context else 'unknown',
            execution_id=context.get('execution_id', 'unknown') if context else 'unknown',
            stack_trace=stack_trace,
            context=context or {}
        )


class RetryCalculator:
    """重试计算器"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.RetryCalculator")
    
    def should_retry(self, error_info: ErrorInfo) -> bool:
        """判断是否应该重试"""
        # 检查重试次数
        if error_info.retry_count >= self.config.max_retries:
            return False
        
        # 检查错误类型是否在不重试列表中
        if error_info.error_type in self.config.no_retry_on_errors:
            return False
        
        # 检查错误类型是否在重试列表中
        if error_info.error_type not in self.config.retry_on_errors:
            return False
        
        # 检查错误严重程度
        if error_info.severity == ErrorSeverity.CRITICAL:
            return False
        
        return True
    
    def calculate_delay(self, retry_count: int) -> float:
        """计算重试延迟"""
        if self.config.strategy == RetryStrategy.NO_RETRY:
            return 0
        
        if self.config.strategy == RetryStrategy.IMMEDIATE:
            return 0
        
        if self.config.strategy == RetryStrategy.FIXED_INTERVAL:
            delay = self.config.base_delay_seconds
        
        elif self.config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.config.base_delay_seconds * (retry_count + 1)
        
        elif self.config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.config.base_delay_seconds * (self.config.backoff_multiplier ** retry_count)
        
        else:
            delay = self.config.base_delay_seconds
        
        # 限制最大延迟
        delay = min(delay, self.config.max_delay_seconds)
        
        # 添加随机抖动
        if self.config.jitter:
            jitter_range = delay * 0.1  # 10%的抖动
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)


class TaskStatePersistence:
    """任务状态持久化"""
    
    def __init__(self, storage_path: str = "task_states"):
        self.storage_path = storage_path
        self.logger = logging.getLogger(f"{__name__}.TaskStatePersistence")
        
        # 确保存储目录存在
        os.makedirs(storage_path, exist_ok=True)
    
    def save_task_state(self, task_state: TaskState) -> bool:
        """保存任务状态"""
        try:
            file_path = os.path.join(self.storage_path, f"{task_state.task_id}_{task_state.execution_id}.json")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(task_state.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"Saved task state for {task_state.task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save task state for {task_state.task_id}: {e}")
            return False
    
    def load_task_state(self, task_id: str, execution_id: str) -> Optional[TaskState]:
        """加载任务状态"""
        try:
            file_path = os.path.join(self.storage_path, f"{task_id}_{execution_id}.json")
            
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return TaskState.from_dict(data)
            
        except Exception as e:
            self.logger.error(f"Failed to load task state for {task_id}: {e}")
            return None
    
    def delete_task_state(self, task_id: str, execution_id: str) -> bool:
        """删除任务状态"""
        try:
            file_path = os.path.join(self.storage_path, f"{task_id}_{execution_id}.json")
            
            if os.path.exists(file_path):
                os.remove(file_path)
                self.logger.debug(f"Deleted task state for {task_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete task state for {task_id}: {e}")
            return False
    
    def list_task_states(self) -> List[TaskState]:
        """列出所有任务状态"""
        task_states = []
        
        try:
            for filename in os.listdir(self.storage_path):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.storage_path, filename)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        task_state = TaskState.from_dict(data)
                        task_states.append(task_state)
                        
                    except Exception as e:
                        self.logger.warning(f"Failed to load task state from {filename}: {e}")
            
        except Exception as e:
            self.logger.error(f"Failed to list task states: {e}")
        
        return task_states
    
    def cleanup_old_states(self, max_age_days: int = 7) -> int:
        """清理旧的任务状态"""
        cleaned_count = 0
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        
        try:
            for filename in os.listdir(self.storage_path):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.storage_path, filename)
                    
                    try:
                        # 检查文件修改时间
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                        
                        if file_mtime < cutoff_time:
                            os.remove(file_path)
                            cleaned_count += 1
                            self.logger.debug(f"Cleaned up old task state: {filename}")
                    
                    except Exception as e:
                        self.logger.warning(f"Failed to cleanup {filename}: {e}")
            
            self.logger.info(f"Cleaned up {cleaned_count} old task states")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old states: {e}")
        
        return cleaned_count


class TaskFailureHandler:
    """任务失败处理器"""
    
    def __init__(self, retry_config: RetryConfig = None, storage_path: str = "task_states"):
        self.retry_config = retry_config or RetryConfig()
        self.error_classifier = ErrorClassifier()
        self.retry_calculator = RetryCalculator(self.retry_config)
        self.persistence = TaskStatePersistence(storage_path)
        
        self.logger = logging.getLogger(f"{__name__}.TaskFailureHandler")
        
        # 错误统计
        self.error_stats = {
            'total_errors': 0,
            'errors_by_type': {},
            'errors_by_severity': {},
            'retry_attempts': 0,
            'successful_retries': 0,
            'failed_retries': 0,
            'last_error_time': None
        }
        
        # 回调函数
        self.error_callback: Optional[Callable] = None
        self.retry_callback: Optional[Callable] = None
        self.recovery_callback: Optional[Callable] = None
    
    def set_callbacks(self, 
                     error_callback: Optional[Callable] = None,
                     retry_callback: Optional[Callable] = None,
                     recovery_callback: Optional[Callable] = None):
        """设置回调函数"""
        self.error_callback = error_callback
        self.retry_callback = retry_callback
        self.recovery_callback = recovery_callback
    
    async def handle_task_failure(self, task_id: str, execution_id: str, 
                                exception: Exception, context: Dict[str, Any] = None) -> bool:
        """处理任务失败"""
        try:
            # 分类错误
            error_info = self.error_classifier.classify_error(exception, context)
            error_info.task_id = task_id
            error_info.execution_id = execution_id
            
            # 更新统计信息
            self._update_error_stats(error_info)
            
            # 加载或创建任务状态
            task_state = self.persistence.load_task_state(task_id, execution_id)
            if task_state is None:
                task_state = TaskState(
                    task_id=task_id,
                    execution_id=execution_id,
                    status='failed',
                    start_time=datetime.now(),
                    last_update_time=datetime.now(),
                    context=context or {}
                )
            
            # 更新错误信息
            error_info.retry_count = task_state.retry_count
            task_state.error_history.append(error_info)
            task_state.last_update_time = datetime.now()
            
            # 调用错误回调
            if self.error_callback:
                try:
                    self.error_callback(error_info)
                except Exception as callback_error:
                    self.logger.error(f"Error in error callback: {callback_error}")
            
            # 判断是否应该重试
            should_retry = self.retry_calculator.should_retry(error_info)
            
            if should_retry:
                # 计算重试延迟
                delay = self.retry_calculator.calculate_delay(task_state.retry_count)
                
                # 更新任务状态
                task_state.status = 'retrying'
                task_state.retry_count += 1
                
                # 保存任务状态
                self.persistence.save_task_state(task_state)
                
                # 更新重试统计
                self.error_stats['retry_attempts'] += 1
                
                self.logger.info(f"Task {task_id} will retry in {delay:.2f}s (attempt {task_state.retry_count})")
                
                # 调用重试回调
                if self.retry_callback:
                    try:
                        self.retry_callback(task_id, task_state.retry_count, delay)
                    except Exception as callback_error:
                        self.logger.error(f"Error in retry callback: {callback_error}")
                
                # 等待重试延迟
                if delay > 0:
                    await asyncio.sleep(delay)
                
                return True  # 表示应该重试
            
            else:
                # 不重试，标记为最终失败
                task_state.status = 'failed'
                self.persistence.save_task_state(task_state)
                
                self.error_stats['failed_retries'] += 1
                
                self.logger.error(f"Task {task_id} failed permanently: {error_info.message}")
                
                return False  # 表示不应该重试
        
        except Exception as e:
            self.logger.error(f"Error in failure handler: {e}")
            return False
    
    def mark_task_success(self, task_id: str, execution_id: str):
        """标记任务成功"""
        try:
            task_state = self.persistence.load_task_state(task_id, execution_id)
            if task_state:
                task_state.status = 'completed'
                task_state.last_update_time = datetime.now()
                self.persistence.save_task_state(task_state)
                
                # 如果之前有重试，更新成功重试统计
                if task_state.retry_count > 0:
                    self.error_stats['successful_retries'] += 1
                
                # 调用恢复回调
                if self.recovery_callback:
                    try:
                        self.recovery_callback(task_id, task_state.retry_count)
                    except Exception as callback_error:
                        self.logger.error(f"Error in recovery callback: {callback_error}")
                
                self.logger.info(f"Task {task_id} completed successfully after {task_state.retry_count} retries")
        
        except Exception as e:
            self.logger.error(f"Error marking task success: {e}")
    
    def get_task_state(self, task_id: str, execution_id: str) -> Optional[TaskState]:
        """获取任务状态"""
        return self.persistence.load_task_state(task_id, execution_id)
    
    def get_all_task_states(self) -> List[TaskState]:
        """获取所有任务状态"""
        return self.persistence.list_task_states()
    
    def get_failed_tasks(self) -> List[TaskState]:
        """获取失败的任务"""
        all_states = self.persistence.list_task_states()
        return [state for state in all_states if state.status == 'failed']
    
    def get_retrying_tasks(self) -> List[TaskState]:
        """获取正在重试的任务"""
        all_states = self.persistence.list_task_states()
        return [state for state in all_states if state.status == 'retrying']
    
    def _update_error_stats(self, error_info: ErrorInfo):
        """更新错误统计信息"""
        self.error_stats['total_errors'] += 1
        self.error_stats['last_error_time'] = error_info.timestamp.isoformat()
        
        # 按类型统计
        error_type = error_info.error_type.value
        if error_type not in self.error_stats['errors_by_type']:
            self.error_stats['errors_by_type'][error_type] = 0
        self.error_stats['errors_by_type'][error_type] += 1
        
        # 按严重程度统计
        severity = error_info.severity.value
        if severity not in self.error_stats['errors_by_severity']:
            self.error_stats['errors_by_severity'][severity] = 0
        self.error_stats['errors_by_severity'][severity] += 1
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        stats = self.error_stats.copy()
        
        # 计算重试成功率
        total_retries = stats['retry_attempts']
        if total_retries > 0:
            stats['retry_success_rate'] = stats['successful_retries'] / total_retries
        else:
            stats['retry_success_rate'] = 0.0
        
        return stats
    
    def get_error_history(self, task_id: Optional[str] = None, 
                         error_type: Optional[ErrorType] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """获取错误历史"""
        all_states = self.persistence.list_task_states()
        error_history = []
        
        for state in all_states:
            for error in state.error_history:
                if task_id and error.task_id != task_id:
                    continue
                
                if error_type and error.error_type != error_type:
                    continue
                
                error_history.append(error.to_dict())
        
        # 按时间倒序排列
        error_history.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return error_history[:limit]
    
    def cleanup_old_states(self, max_age_days: int = 7) -> int:
        """清理旧的任务状态"""
        return self.persistence.cleanup_old_states(max_age_days)
    
    def reset_statistics(self):
        """重置统计信息"""
        self.error_stats = {
            'total_errors': 0,
            'errors_by_type': {},
            'errors_by_severity': {},
            'retry_attempts': 0,
            'successful_retries': 0,
            'failed_retries': 0,
            'last_error_time': None
        }
        self.logger.info("Error statistics reset completed")
    
    def export_error_report(self, output_path: str) -> bool:
        """导出错误报告"""
        try:
            report = {
                'generated_at': datetime.now().isoformat(),
                'statistics': self.get_error_statistics(),
                'failed_tasks': [state.to_dict() for state in self.get_failed_tasks()],
                'retrying_tasks': [state.to_dict() for state in self.get_retrying_tasks()],
                'recent_errors': self.get_error_history(limit=50)
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Error report exported to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export error report: {e}")
            return False
    
    def analyze_error_patterns(self) -> Dict[str, Any]:
        """分析错误模式"""
        all_states = self.persistence.list_task_states()
        
        # 收集错误数据
        error_data = []
        for state in all_states:
            for error in state.error_history:
                error_data.append({
                    'type': error.error_type.value,
                    'severity': error.severity.value,
                    'timestamp': error.timestamp,
                    'task_id': error.task_id,
                    'retry_count': error.retry_count
                })
        
        if not error_data:
            return {'message': 'No error data available for analysis'}
        
        # 分析错误模式
        analysis = {
            'total_errors': len(error_data),
            'most_common_error_type': None,
            'most_severe_errors': 0,
            'error_frequency_by_hour': {},
            'tasks_with_most_errors': {},
            'average_retries_per_error_type': {}
        }
        
        # 最常见的错误类型
        type_counts = {}
        severity_counts = {}
        hour_counts = {}
        task_error_counts = {}
        type_retry_counts = {}
        type_retry_totals = {}
        
        for error in error_data:
            # 错误类型统计
            error_type = error['type']
            type_counts[error_type] = type_counts.get(error_type, 0) + 1
            
            # 严重程度统计
            severity = error['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # 按小时统计
            hour = error['timestamp'].hour
            hour_counts[hour] = hour_counts.get(hour, 0) + 1
            
            # 按任务统计
            task_id = error['task_id']
            task_error_counts[task_id] = task_error_counts.get(task_id, 0) + 1
            
            # 重试次数统计
            if error_type not in type_retry_counts:
                type_retry_counts[error_type] = 0
                type_retry_totals[error_type] = 0
            type_retry_counts[error_type] += error['retry_count']
            type_retry_totals[error_type] += 1
        
        # 填充分析结果
        if type_counts:
            analysis['most_common_error_type'] = max(type_counts, key=type_counts.get)
        
        analysis['most_severe_errors'] = severity_counts.get(ErrorSeverity.CRITICAL.value, 0)
        analysis['error_frequency_by_hour'] = hour_counts
        
        # 错误最多的任务（前5个）
        sorted_tasks = sorted(task_error_counts.items(), key=lambda x: x[1], reverse=True)
        analysis['tasks_with_most_errors'] = dict(sorted_tasks[:5])
        
        # 每种错误类型的平均重试次数
        for error_type in type_retry_totals:
            if type_retry_totals[error_type] > 0:
                avg_retries = type_retry_counts[error_type] / type_retry_totals[error_type]
                analysis['average_retries_per_error_type'][error_type] = round(avg_retries, 2)
        
        return analysis