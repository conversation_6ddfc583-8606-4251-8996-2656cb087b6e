syntax = "proto3";

package financial_data;

option cc_generic_services = false;

// Market data streaming service
service MarketDataService {
    // Stream real-time tick data
    rpc StreamTickData(TickDataRequest) returns (stream TickDataResponse);
    
    // Stream historical tick data
    rpc GetHistoricalTickData(HistoricalTickDataRequest) returns (stream TickDataResponse);
    
    // Stream K-line data
    rpc StreamKlineData(KlineDataRequest) returns (stream KlineDataResponse);
    
    // Stream Level-2 market depth data
    rpc StreamLevel2Data(Level2DataRequest) returns (stream Level2DataResponse);
    
    // Health check
    rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}

// Request messages
message TickDataRequest {
    repeated string symbols = 1;
    string exchange = 2;
    bool include_level2 = 3;
    int32 buffer_size = 4;  // Client buffer size for flow control
}

message HistoricalTickDataRequest {
    string symbol = 1;
    string exchange = 2;
    int64 start_timestamp = 3;
    int64 end_timestamp = 4;
    int32 limit = 5;
    string cursor = 6;  // For pagination
    int32 buffer_size = 7;
}

message KlineDataRequest {
    repeated string symbols = 1;
    string exchange = 2;
    string period = 3;  // 1m, 5m, 15m, 1h, 1d
    int64 start_timestamp = 4;
    int64 end_timestamp = 5;
    int32 buffer_size = 6;
}

message Level2DataRequest {
    repeated string symbols = 1;
    string exchange = 2;
    int32 depth = 3;  // Number of price levels
    int32 buffer_size = 4;
}

message HealthCheckRequest {
    string service = 1;
}

// Response messages
message TickDataResponse {
    repeated TickData ticks = 1;
    bool has_more = 2;
    string next_cursor = 3;
    ResponseMetadata metadata = 4;
}

message KlineDataResponse {
    repeated KlineData klines = 1;
    bool has_more = 2;
    ResponseMetadata metadata = 3;
}

message Level2DataResponse {
    repeated Level2Data level2_data = 1;
    ResponseMetadata metadata = 2;
}

message HealthCheckResponse {
    enum ServingStatus {
        UNKNOWN = 0;
        SERVING = 1;
        NOT_SERVING = 2;
        SERVICE_UNKNOWN = 3;
    }
    ServingStatus status = 1;
    string message = 2;
}

// Data structures
message TickData {
    int64 timestamp = 1;
    string symbol = 2;
    string exchange = 3;
    double last_price = 4;
    int64 volume = 5;
    double turnover = 6;
    int64 open_interest = 7;
    repeated PriceLevel bids = 8;
    repeated PriceLevel asks = 9;
    uint32 sequence = 10;
    string trade_flag = 11;
}

message KlineData {
    string symbol = 1;
    string exchange = 2;
    string period = 3;
    int64 timestamp = 4;
    double open = 5;
    double high = 6;
    double low = 7;
    double close = 8;
    int64 volume = 9;
    double turnover = 10;
    int64 open_interest = 11;
}

message Level2Data {
    int64 timestamp = 1;
    string symbol = 2;
    string exchange = 3;
    repeated PriceLevel bids = 4;
    repeated PriceLevel asks = 5;
    uint32 sequence = 6;
}

message PriceLevel {
    double price = 1;
    int32 volume = 2;
    int32 order_count = 3;
}

message ResponseMetadata {
    int64 server_timestamp = 1;
    string server_id = 2;
    int32 sequence_number = 3;
    double processing_latency_us = 4;
}