#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <functional>

namespace monitoring {

enum class AlertSeverity {
    INFO,
    WARNING,
    CRITICAL
};

struct Alert {
    std::string type;
    AlertSeverity severity;
    std::string message;
    std::unordered_map<std::string, std::string> metadata;
    std::chrono::high_resolution_clock::time_point timestamp;
    std::string id;
};

class AlertChannel {
public:
    virtual ~AlertChannel() = default;
    virtual bool sendAlert(const Alert& alert) = 0;
    virtual std::string getName() const = 0;
};

class ConsoleAlertChannel : public AlertChannel {
public:
    bool sendAlert(const Alert& alert) override;
    std::string getName() const override { return "console"; }
};

class EmailAlertChannel : public AlertChannel {
public:
    EmailAlertChannel(const std::string& smtp_server, int port, 
                     const std::string& username, const std::string& password,
                     const std::vector<std::string>& recipients);
    
    bool sendAlert(const Alert& alert) override;
    std::string getName() const override { return "email"; }
    
private:
    std::string smtp_server_;
    int port_;
    std::string username_;
    std::string password_;
    std::vector<std::string> recipients_;
};

class WebhookAlertChannel : public AlertChannel {
public:
    explicit WebhookAlertChannel(const std::string& webhook_url);
    
    bool sendAlert(const Alert& alert) override;
    std::string getName() const override { return "webhook"; }
    
private:
    std::string webhook_url_;
};

class SlackAlertChannel : public AlertChannel {
public:
    SlackAlertChannel(const std::string& webhook_url, const std::string& channel);
    
    bool sendAlert(const Alert& alert) override;
    std::string getName() const override { return "slack"; }
    
private:
    std::string webhook_url_;
    std::string channel_;
};

class AlertManager {
public:
    AlertManager();
    ~AlertManager();
    
    // Start/stop alert processing
    bool start();
    void stop();
    
    // Channel management
    void addChannel(std::unique_ptr<AlertChannel> channel);
    void removeChannel(const std::string& channel_name);
    
    // Alert sending
    void sendAlert(const std::string& type, const std::string& severity, 
                  const std::string& message, 
                  const std::unordered_map<std::string, std::string>& metadata = {});
    
    // Configuration
    void setMaxRetries(int max_retries) { max_retries_ = max_retries; }
    void setRetryDelay(std::chrono::seconds delay) { retry_delay_ = delay; }
    void setRateLimitWindow(std::chrono::seconds window) { rate_limit_window_ = window; }
    void setMaxAlertsPerWindow(int max_alerts) { max_alerts_per_window_ = max_alerts; }
    
    // Statistics
    struct AlertStats {
        uint64_t total_alerts_sent;
        uint64_t total_alerts_failed;
        uint64_t alerts_rate_limited;
        std::unordered_map<std::string, uint64_t> alerts_by_type;
        std::unordered_map<std::string, uint64_t> alerts_by_severity;
        std::unordered_map<std::string, uint64_t> channel_success_count;
        std::unordered_map<std::string, uint64_t> channel_failure_count;
    };
    
    AlertStats getStatistics() const;
    void resetStatistics();
    
    // Alert history
    std::vector<Alert> getRecentAlerts(int count = 100) const;
    void clearAlertHistory();
    
private:
    // Threading
    std::atomic<bool> running_{false};
    std::thread processing_thread_;
    
    // Alert queue
    std::queue<Alert> alert_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // Channels
    std::vector<std::unique_ptr<AlertChannel>> channels_;
    std::mutex channels_mutex_;
    
    // Configuration
    int max_retries_{3};
    std::chrono::seconds retry_delay_{5};
    std::chrono::seconds rate_limit_window_{60}; // 1 minute
    int max_alerts_per_window_{10};
    
    // Rate limiting
    struct RateLimitEntry {
        std::chrono::steady_clock::time_point timestamp;
        std::string alert_type;
    };
    std::queue<RateLimitEntry> rate_limit_queue_;
    std::mutex rate_limit_mutex_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    AlertStats stats_;
    
    // Alert history
    static const size_t MAX_HISTORY_SIZE = 1000;
    std::vector<Alert> alert_history_;
    mutable std::mutex history_mutex_;
    
    // Processing methods
    void processingLoop();
    void processAlert(const Alert& alert);
    bool sendAlertToChannels(const Alert& alert);
    bool sendAlertWithRetry(AlertChannel* channel, const Alert& alert);
    
    // Rate limiting
    bool isRateLimited(const std::string& alert_type);
    void updateRateLimit(const std::string& alert_type);
    void cleanupRateLimit();
    
    // Utility methods
    AlertSeverity parseSeverity(const std::string& severity_str);
    std::string generateAlertId();
    std::string formatAlertForLogging(const Alert& alert);
    void addToHistory(const Alert& alert);
    void updateStatistics(const Alert& alert, bool success, const std::string& channel_name = "");
};

} // namespace monitoring