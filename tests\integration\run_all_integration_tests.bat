@echo off
REM 综合集成测试执行脚本 - Windows版本
REM 运行所有端到端集成测试、数据一致性验证和性能压力测试

setlocal enabledelayedexpansion

REM 获取脚本目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%..\.."
set "RESULTS_DIR=%PROJECT_ROOT%\tests\integration\results"

REM 创建结果目录
if not exist "%RESULTS_DIR%" mkdir "%RESULTS_DIR%"

REM 时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,8%_%dt:~8,6%"
set "REPORT_FILE=%RESULTS_DIR%\integration_test_report_%TIMESTAMP%.json"

echo [INFO] Starting comprehensive integration test suite
echo [INFO] Project root: %PROJECT_ROOT%
echo [INFO] Results directory: %RESULTS_DIR%

REM 初始化测试结果变量
set "PYTHON_E2E_SUCCESS=false"
set "PYTHON_CONSISTENCY_SUCCESS=false"
set "PYTHON_PERFORMANCE_SUCCESS=false"
set "CPP_E2E_SUCCESS=false"
set "CPP_CONSISTENCY_SUCCESS=false"
set "CPP_PERFORMANCE_SUCCESS=false"

REM 检查测试环境
echo [INFO] Checking test environment...

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    goto :error
) else (
    echo [SUCCESS] Python is available
)

REM 检查Redis
redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Redis service is not responding
) else (
    echo [SUCCESS] Redis service is available
)

REM 设置Python环境
cd /d "%PROJECT_ROOT%"
set "PYTHONPATH=%PROJECT_ROOT%"
set "TESTING=1"

REM 运行Python集成测试
echo [INFO] Running Python integration tests...

REM 1. 端到端集成测试
echo [INFO] Running end-to-end integration tests...
python tests\integration\end_to_end_integration_test.py > "%RESULTS_DIR%\python_e2e_%TIMESTAMP%.log" 2>&1
if errorlevel 1 (
    echo [ERROR] End-to-end integration tests failed
) else (
    echo [SUCCESS] End-to-end integration tests passed
    set "PYTHON_E2E_SUCCESS=true"
)

REM 2. 数据一致性验证
echo [INFO] Running data consistency validation...
python tests\integration\data_consistency_validator.py > "%RESULTS_DIR%\python_consistency_%TIMESTAMP%.log" 2>&1
if errorlevel 1 (
    echo [ERROR] Data consistency validation failed
) else (
    echo [SUCCESS] Data consistency validation passed
    set "PYTHON_CONSISTENCY_SUCCESS=true"
)

REM 3. 性能压力测试
echo [INFO] Running performance stress tests...
python tests\integration\performance_stress_test.py > "%RESULTS_DIR%\python_performance_%TIMESTAMP%.log" 2>&1
if errorlevel 1 (
    echo [ERROR] Performance stress tests failed
) else (
    echo [SUCCESS] Performance stress tests passed
    set "PYTHON_PERFORMANCE_SUCCESS=true"
)

REM 构建C++测试
echo [INFO] Building C++ tests...
set "BUILD_DIR=%PROJECT_ROOT%\build"
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

cd /d "%BUILD_DIR%"

REM CMake配置
cmake .. > "%RESULTS_DIR%\cmake_config_%TIMESTAMP%.log" 2>&1
if errorlevel 1 (
    echo [ERROR] CMake configuration failed
    goto :skip_cpp
) else (
    echo [SUCCESS] CMake configuration successful
)

REM 构建集成测试
cmake --build . --target end_to_end_integration_test > "%RESULTS_DIR%\cpp_build_%TIMESTAMP%.log" 2>&1
if errorlevel 1 (
    echo [WARNING] C++ end-to-end test build failed
) else (
    echo [SUCCESS] C++ end-to-end test build successful
)

cmake --build . --target data_consistency_validator > "%RESULTS_DIR%\cpp_build_consistency_%TIMESTAMP%.log" 2>&1
if errorlevel 1 (
    echo [WARNING] C++ consistency validator build failed
) else (
    echo [SUCCESS] C++ consistency validator build successful
)

cmake --build . --target performance_stress_test > "%RESULTS_DIR%\cpp_build_performance_%TIMESTAMP%.log" 2>&1
if errorlevel 1 (
    echo [WARNING] C++ performance test build failed
) else (
    echo [SUCCESS] C++ performance test build successful
)

REM 运行C++集成测试
echo [INFO] Running C++ integration tests...

REM 1. 端到端集成测试
if exist "%BUILD_DIR%\tests\integration\end_to_end_integration_test.exe" (
    echo [INFO] Running C++ end-to-end integration tests...
    "%BUILD_DIR%\tests\integration\end_to_end_integration_test.exe" > "%RESULTS_DIR%\cpp_e2e_%TIMESTAMP%.log" 2>&1
    if errorlevel 1 (
        echo [ERROR] C++ end-to-end integration tests failed
    ) else (
        echo [SUCCESS] C++ end-to-end integration tests passed
        set "CPP_E2E_SUCCESS=true"
    )
) else (
    echo [WARNING] C++ end-to-end test executable not found, skipping
)

REM 2. 数据一致性验证
if exist "%BUILD_DIR%\tests\integration\data_consistency_validator.exe" (
    echo [INFO] Running C++ data consistency validation...
    "%BUILD_DIR%\tests\integration\data_consistency_validator.exe" > "%RESULTS_DIR%\cpp_consistency_%TIMESTAMP%.log" 2>&1
    if errorlevel 1 (
        echo [ERROR] C++ data consistency validation failed
    ) else (
        echo [SUCCESS] C++ data consistency validation passed
        set "CPP_CONSISTENCY_SUCCESS=true"
    )
) else (
    echo [WARNING] C++ consistency validator executable not found, skipping
)

REM 3. 性能压力测试
if exist "%BUILD_DIR%\tests\integration\performance_stress_test.exe" (
    echo [INFO] Running C++ performance stress tests...
    "%BUILD_DIR%\tests\integration\performance_stress_test.exe" > "%RESULTS_DIR%\cpp_performance_%TIMESTAMP%.log" 2>&1
    if errorlevel 1 (
        echo [ERROR] C++ performance stress tests failed
    ) else (
        echo [SUCCESS] C++ performance stress tests passed
        set "CPP_PERFORMANCE_SUCCESS=true"
    )
) else (
    echo [WARNING] C++ performance test executable not found, skipping
)

goto :generate_report

:skip_cpp
echo [WARNING] C++ tests skipped due to build failure

:generate_report
REM 生成综合报告
echo [INFO] Generating comprehensive test report...

REM 计算成功的测试数量
set /a "TOTAL_TESTS=6"
set /a "PASSED_TESTS=0"

if "%PYTHON_E2E_SUCCESS%"=="true" set /a "PASSED_TESTS+=1"
if "%PYTHON_CONSISTENCY_SUCCESS%"=="true" set /a "PASSED_TESTS+=1"
if "%PYTHON_PERFORMANCE_SUCCESS%"=="true" set /a "PASSED_TESTS+=1"
if "%CPP_E2E_SUCCESS%"=="true" set /a "PASSED_TESTS+=1"
if "%CPP_CONSISTENCY_SUCCESS%"=="true" set /a "PASSED_TESTS+=1"
if "%CPP_PERFORMANCE_SUCCESS%"=="true" set /a "PASSED_TESTS+=1"

set /a "FAILED_TESTS=%TOTAL_TESTS%-%PASSED_TESTS%"

REM 创建简单的JSON报告
echo { > "%REPORT_FILE%"
echo   "timestamp": "%date% %time%", >> "%REPORT_FILE%"
echo   "total_tests": %TOTAL_TESTS%, >> "%REPORT_FILE%"
echo   "passed_tests": %PASSED_TESTS%, >> "%REPORT_FILE%"
echo   "failed_tests": %FAILED_TESTS%, >> "%REPORT_FILE%"
echo   "tests": { >> "%REPORT_FILE%"
echo     "python_e2e": {"success": %PYTHON_E2E_SUCCESS%}, >> "%REPORT_FILE%"
echo     "python_consistency": {"success": %PYTHON_CONSISTENCY_SUCCESS%}, >> "%REPORT_FILE%"
echo     "python_performance": {"success": %PYTHON_PERFORMANCE_SUCCESS%}, >> "%REPORT_FILE%"
echo     "cpp_e2e": {"success": %CPP_E2E_SUCCESS%}, >> "%REPORT_FILE%"
echo     "cpp_consistency": {"success": %CPP_CONSISTENCY_SUCCESS%}, >> "%REPORT_FILE%"
echo     "cpp_performance": {"success": %CPP_PERFORMANCE_SUCCESS%} >> "%REPORT_FILE%"
echo   } >> "%REPORT_FILE%"
echo } >> "%REPORT_FILE%"

REM 打印控制台报告
echo.
echo ================================================================================
echo                     COMPREHENSIVE INTEGRATION TEST REPORT
echo ================================================================================
echo Timestamp: %date% %time%
echo Report File: %REPORT_FILE%
echo.
echo Test Summary:
echo   Total Tests: %TOTAL_TESTS%
echo   Passed: %PASSED_TESTS%
echo   Failed: %FAILED_TESTS%
echo.
echo Individual Test Results:
echo   Python E2E: %PYTHON_E2E_SUCCESS%
echo   Python Consistency: %PYTHON_CONSISTENCY_SUCCESS%
echo   Python Performance: %PYTHON_PERFORMANCE_SUCCESS%
echo   C++ E2E: %CPP_E2E_SUCCESS%
echo   C++ Consistency: %CPP_CONSISTENCY_SUCCESS%
echo   C++ Performance: %CPP_PERFORMANCE_SUCCESS%
echo.
echo Log Files Location: %RESULTS_DIR%
echo ================================================================================

echo [SUCCESS] Comprehensive test report generated: %REPORT_FILE%

REM 清理
echo [INFO] Cleaning up test environment...
del /q "%PROJECT_ROOT%\*.tmp" 2>nul
echo [SUCCESS] Cleanup completed

REM 返回结果
if %FAILED_TESTS% gtr 0 (
    echo [ERROR] Some integration tests failed
    exit /b 1
) else (
    echo [SUCCESS] All integration tests completed successfully
    exit /b 0
)

:error
echo [ERROR] Environment check failed
exit /b 1