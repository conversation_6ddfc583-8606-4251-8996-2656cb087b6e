#include "archive_interface.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <thread>
#include <queue>
#include <unordered_map>
#include <json/json.h>
#include <zstd.h>

namespace financial_data {
namespace storage {

// 查询执行器实现
class ArchiveInterface::QueryExecutor {
public:
    QueryExecutor(std::shared_ptr<ColdDataStorage> cold_storage) 
        : cold_storage_(cold_storage) {}
    
    std::future<QueryResult> ExecuteQuery(const QueryCondition& condition) {
        return std::async(std::launch::async, [this, condition]() -> QueryResult {
            QueryResult result;
            result.query_id = GenerateQueryId();
            
            auto start_time = std::chrono::steady_clock::now();
            
            try {
                // 查找相关文件
                auto file_list = FindRelevantFiles(condition);
                result.stats.files_scanned = file_list.size();
                
                // 处理每个文件
                for (const auto& file_path : file_list) {
                    auto batch = ProcessFile(file_path, condition);
                    
                    // 合并数据
                    MergeBatch(result.data, batch);
                    result.stats.records_scanned += batch.size();
                }
                
                // 应用排序和分页
                ApplySortingAndPaging(result, condition);
                
                auto end_time = std::chrono::steady_clock::now();
                result.query_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                    end_time - start_time);
                
                std::cout << "Query " << result.query_id << " completed: "
                          << result.data.size() << " records in " 
                          << result.query_time.count() << " ms" << std::endl;
                
            } catch (const std::exception& e) {
                std::cerr << "Query execution error: " << e.what() << std::endl;
                result.data.clear();
            }
            
            return result;
        });
    }
    
private:
    std::shared_ptr<ColdDataStorage> cold_storage_;
    
    std::string GenerateQueryId() {
        static std::atomic<size_t> counter{0};
        return "query_" + std::to_string(++counter);
    }
    
    std::vector<std::string> FindRelevantFiles(const QueryCondition& condition) {
        std::vector<std::string> files;
        
        // 简化实现：基于时间范围和符号查找文件
        for (const auto& symbol : condition.symbols) {
            for (const auto& exchange : condition.exchanges) {
                auto current_time = condition.start_time;
                while (current_time <= condition.end_time) {
                    // 生成可能的文件路径
                    auto time_t = std::chrono::system_clock::to_time_t(current_time);
                    auto tm = *std::gmtime(&time_t);
                    
                    std::ostringstream oss;
                    oss << exchange << "/" << symbol << "/" 
                        << (tm.tm_year + 1900) << "/" 
                        << std::setfill('0') << std::setw(2) << (tm.tm_mon + 1) << "/"
                        << std::setfill('0') << std::setw(2) << tm.tm_mday << "/"
                        << symbol << "_" << exchange << "_"
                        << (tm.tm_year + 1900) 
                        << std::setfill('0') << std::setw(2) << (tm.tm_mon + 1)
                        << std::setfill('0') << std::setw(2) << tm.tm_mday
                        << ".parquet";
                    
                    files.push_back(oss.str());
                    current_time += std::chrono::hours(24);
                }
            }
        }
        
        return files;
    }
    
    TickDataBatch ProcessFile(const std::string& file_path, const QueryCondition& condition) {
        TickDataBatch result;
        
        try {
            // 从冷存储检索数据
            auto future = cold_storage_->RetrieveData(
                condition.symbols.empty() ? "" : condition.symbols[0],
                condition.exchanges.empty() ? "" : condition.exchanges[0],
                condition.start_time,
                condition.end_time
            );
            
            auto batch = future.get();
            
            // 应用过滤条件
            for (size_t i = 0; i < batch.size(); ++i) {
                if (MatchesCondition(batch, i, condition)) {
                    result.timestamps.push_back(batch.timestamps[i]);
                    result.symbols.push_back(batch.symbols[i]);
                    result.exchanges.push_back(batch.exchanges[i]);
                    result.last_prices.push_back(batch.last_prices[i]);
                    result.volumes.push_back(batch.volumes[i]);
                    result.turnovers.push_back(batch.turnovers[i]);
                    result.open_interests.push_back(batch.open_interests[i]);
                    result.bid_prices.push_back(batch.bid_prices[i]);
                    result.bid_volumes.push_back(batch.bid_volumes[i]);
                    result.ask_prices.push_back(batch.ask_prices[i]);
                    result.ask_volumes.push_back(batch.ask_volumes[i]);
                    result.sequences.push_back(batch.sequences[i]);
                }
            }
            
        } catch (const std::exception& e) {
            std::cerr << "Error processing file " << file_path << ": " << e.what() << std::endl;
        }
        
        return result;
    }
    
    bool MatchesCondition(const TickDataBatch& batch, size_t index, const QueryCondition& condition) {
        // 检查价格范围
        if (condition.min_price && batch.last_prices[index] < *condition.min_price) {
            return false;
        }
        if (condition.max_price && batch.last_prices[index] > *condition.max_price) {
            return false;
        }
        
        // 检查成交量范围
        if (condition.min_volume && batch.volumes[index] < *condition.min_volume) {
            return false;
        }
        if (condition.max_volume && batch.volumes[index] > *condition.max_volume) {
            return false;
        }
        
        return true;
    }
    
    void MergeBatch(TickDataBatch& target, const TickDataBatch& source) {
        target.timestamps.insert(target.timestamps.end(), source.timestamps.begin(), source.timestamps.end());
        target.symbols.insert(target.symbols.end(), source.symbols.begin(), source.symbols.end());
        target.exchanges.insert(target.exchanges.end(), source.exchanges.begin(), source.exchanges.end());
        target.last_prices.insert(target.last_prices.end(), source.last_prices.begin(), source.last_prices.end());
        target.volumes.insert(target.volumes.end(), source.volumes.begin(), source.volumes.end());
        target.turnovers.insert(target.turnovers.end(), source.turnovers.begin(), source.turnovers.end());
        target.open_interests.insert(target.open_interests.end(), source.open_interests.begin(), source.open_interests.end());
        target.bid_prices.insert(target.bid_prices.end(), source.bid_prices.begin(), source.bid_prices.end());
        target.bid_volumes.insert(target.bid_volumes.end(), source.bid_volumes.begin(), source.bid_volumes.end());
        target.ask_prices.insert(target.ask_prices.end(), source.ask_prices.begin(), source.ask_prices.end());
        target.ask_volumes.insert(target.ask_volumes.end(), source.ask_volumes.begin(), source.ask_volumes.end());
        target.sequences.insert(target.sequences.end(), source.sequences.begin(), source.sequences.end());
    }
    
    void ApplySortingAndPaging(QueryResult& result, const QueryCondition& condition) {
        // 创建索引数组用于排序
        std::vector<size_t> indices(result.data.size());
        std::iota(indices.begin(), indices.end(), 0);
        
        // 根据排序字段排序
        switch (condition.sort_field) {
            case QueryCondition::TIMESTAMP:
                std::sort(indices.begin(), indices.end(), [&](size_t a, size_t b) {
                    return condition.sort_order == QueryCondition::ASC ? 
                           result.data.timestamps[a] < result.data.timestamps[b] :
                           result.data.timestamps[a] > result.data.timestamps[b];
                });
                break;
            case QueryCondition::PRICE:
                std::sort(indices.begin(), indices.end(), [&](size_t a, size_t b) {
                    return condition.sort_order == QueryCondition::ASC ? 
                           result.data.last_prices[a] < result.data.last_prices[b] :
                           result.data.last_prices[a] > result.data.last_prices[b];
                });
                break;
            case QueryCondition::VOLUME:
                std::sort(indices.begin(), indices.end(), [&](size_t a, size_t b) {
                    return condition.sort_order == QueryCondition::ASC ? 
                           result.data.volumes[a] < result.data.volumes[b] :
                           result.data.volumes[a] > result.data.volumes[b];
                });
                break;
            default:
                break;
        }
        
        // 应用分页
        if (condition.limit > 0) {
            size_t start = condition.offset;
            size_t end = std::min(start + condition.limit, indices.size());
            
            if (start < indices.size()) {
                indices.erase(indices.begin() + end, indices.end());
                indices.erase(indices.begin(), indices.begin() + start);
                result.has_more = (end < result.data.size());
            } else {
                indices.clear();
            }
        }
        
        // 重新组织数据
        ReorganizeData(result.data, indices);
    }
    
    void ReorganizeData(TickDataBatch& batch, const std::vector<size_t>& indices) {
        if (indices.empty()) {
            batch.clear();
            return;
        }
        
        TickDataBatch temp;
        temp.reserve(indices.size());
        
        for (size_t idx : indices) {
            temp.timestamps.push_back(batch.timestamps[idx]);
            temp.symbols.push_back(batch.symbols[idx]);
            temp.exchanges.push_back(batch.exchanges[idx]);
            temp.last_prices.push_back(batch.last_prices[idx]);
            temp.volumes.push_back(batch.volumes[idx]);
            temp.turnovers.push_back(batch.turnovers[idx]);
            temp.open_interests.push_back(batch.open_interests[idx]);
            temp.bid_prices.push_back(batch.bid_prices[idx]);
            temp.bid_volumes.push_back(batch.bid_volumes[idx]);
            temp.ask_prices.push_back(batch.ask_prices[idx]);
            temp.ask_volumes.push_back(batch.ask_volumes[idx]);
            temp.sequences.push_back(batch.sequences[idx]);
        }
        
        batch = std::move(temp);
    }
};

// 批量查询管理器实现
class ArchiveInterface::BulkQueryManager {
public:
    BulkQueryManager(std::shared_ptr<ColdDataStorage> cold_storage)
        : cold_storage_(cold_storage), running_(true) {
        worker_thread_ = std::thread(&BulkQueryManager::WorkerThread, this);
    }
    
    ~BulkQueryManager() {
        running_ = false;
        condition_.notify_all();
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
    }
    
    std::string SubmitBulkQuery(const BulkQueryRequest& request) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        BulkQueryResult result;
        result.request_id = request.request_id;
        result.status = "pending";
        result.created_at = std::chrono::system_clock::now();
        
        bulk_queries_[request.request_id] = result;
        pending_requests_.push(request);
        
        condition_.notify_one();
        
        std::cout << "Submitted bulk query: " << request.request_id << std::endl;
        return request.request_id;
    }
    
    BulkQueryResult GetBulkQueryStatus(const std::string& request_id) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = bulk_queries_.find(request_id);
        if (it != bulk_queries_.end()) {
            return it->second;
        }
        
        return BulkQueryResult{}; // 返回空结果
    }
    
    bool CancelBulkQuery(const std::string& request_id) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = bulk_queries_.find(request_id);
        if (it != bulk_queries_.end() && it->second.status == "pending") {
            it->second.status = "cancelled";
            return true;
        }
        
        return false;
    }
    
    std::vector<BulkQueryResult> ListBulkQueries() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::vector<BulkQueryResult> results;
        for (const auto& pair : bulk_queries_) {
            results.push_back(pair.second);
        }
        
        return results;
    }
    
private:
    std::shared_ptr<ColdDataStorage> cold_storage_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    
    std::mutex mutex_;
    std::condition_variable condition_;
    std::queue<BulkQueryRequest> pending_requests_;
    std::unordered_map<std::string, BulkQueryResult> bulk_queries_;
    
    void WorkerThread() {
        while (running_) {
            std::unique_lock<std::mutex> lock(mutex_);
            condition_.wait(lock, [this] { return !pending_requests_.empty() || !running_; });
            
            if (!running_) break;
            
            if (!pending_requests_.empty()) {
                auto request = pending_requests_.front();
                pending_requests_.pop();
                
                auto& result = bulk_queries_[request.request_id];
                result.status = "running";
                
                lock.unlock();
                
                // 执行批量查询
                ExecuteBulkQuery(request, result);
                
                lock.lock();
                result.completed_at = std::chrono::system_clock::now();
            }
        }
    }
    
    void ExecuteBulkQuery(const BulkQueryRequest& request, BulkQueryResult& result) {
        try {
            std::cout << "Executing bulk query: " << request.request_id << std::endl;
            
            size_t total_conditions = request.conditions.size();
            size_t completed_conditions = 0;
            
            for (const auto& condition : request.conditions) {
                // 执行单个查询条件
                auto query_result = ExecuteCondition(condition);
                
                // 导出结果到文件
                std::string output_file = GenerateOutputFile(request, condition, query_result);
                result.output_files.push_back(output_file);
                
                result.summary.total_records += query_result.data.size();
                result.summary.total_files++;
                
                completed_conditions++;
                result.progress = static_cast<double>(completed_conditions) / total_conditions * 100.0;
                
                std::cout << "Bulk query progress: " << result.progress << "%" << std::endl;
            }
            
            result.status = "completed";
            std::cout << "Bulk query completed: " << request.request_id << std::endl;
            
        } catch (const std::exception& e) {
            result.status = "failed";
            result.error_message = e.what();
            std::cerr << "Bulk query failed: " << request.request_id << " - " << e.what() << std::endl;
        }
    }
    
    QueryResult ExecuteCondition(const QueryCondition& condition) {
        // 简化实现：直接调用冷存储检索
        QueryResult result;
        
        if (!condition.symbols.empty() && !condition.exchanges.empty()) {
            auto future = cold_storage_->RetrieveData(
                condition.symbols[0], condition.exchanges[0],
                condition.start_time, condition.end_time);
            
            result.data = future.get();
        }
        
        return result;
    }
    
    std::string GenerateOutputFile(const BulkQueryRequest& request,
                                  const QueryCondition& condition,
                                  const QueryResult& query_result) {
        // 生成输出文件路径
        std::string filename = request.request_id + "_" + 
                              (condition.symbols.empty() ? "all" : condition.symbols[0]) + "_" +
                              (condition.exchanges.empty() ? "all" : condition.exchanges[0]);
        
        if (request.output_format == "parquet") {
            filename += ".parquet";
        } else if (request.output_format == "csv") {
            filename += ".csv";
        } else if (request.output_format == "json") {
            filename += ".json";
        }
        
        std::string output_path = "bulk_query_results/" + filename;
        
        // 确保输出目录存在
        std::filesystem::create_directories("bulk_query_results");
        
        // 导出数据
        if (request.output_format == "csv") {
            DataConverter::ConvertToCSV(query_result.data, output_path);
        } else if (request.output_format == "json") {
            DataConverter::ConvertToJSON(query_result.data, output_path);
        } else {
            DataConverter::ConvertToParquet(query_result.data, output_path);
        }
        
        // 如果需要压缩
        if (request.compression != "none") {
            std::string compressed_path = output_path + "." + request.compression;
            DataConverter::CompressFile(output_path, compressed_path, request.compression);
            std::filesystem::remove(output_path);
            return compressed_path;
        }
        
        return output_path;
    }
};

// ArchiveInterface主类实现
ArchiveInterface::ArchiveInterface(std::shared_ptr<ColdDataStorage> cold_storage)
    : cold_storage_(cold_storage) {
    query_executor_ = std::make_unique<QueryExecutor>(cold_storage);
    bulk_query_manager_ = std::make_unique<BulkQueryManager>(cold_storage);
}

ArchiveInterface::~ArchiveInterface() = default;

std::future<QueryResult> ArchiveInterface::Query(const QueryCondition& condition) {
    return query_executor_->ExecuteQuery(condition);
}

std::unique_ptr<ArchiveInterface::QueryStream> ArchiveInterface::CreateQueryStream(const QueryCondition& condition) {
    return std::make_unique<FileBasedQueryStream>(cold_storage_, condition);
}

std::future<std::string> ArchiveInterface::SubmitBulkQuery(const BulkQueryRequest& request) {
    return std::async(std::launch::async, [this, request]() -> std::string {
        return bulk_query_manager_->SubmitBulkQuery(request);
    });
}

BulkQueryResult ArchiveInterface::GetBulkQueryStatus(const std::string& request_id) {
    return bulk_query_manager_->GetBulkQueryStatus(request_id);
}

std::future<std::string> ArchiveInterface::ExportToFile(const QueryCondition& condition,
                                                        const std::string& file_path,
                                                        const std::string& format) {
    return std::async(std::launch::async, [this, condition, file_path, format]() -> std::string {
        try {
            auto query_future = Query(condition);
            auto result = query_future.get();
            
            if (format == "csv") {
                DataConverter::ConvertToCSV(result.data, file_path);
            } else if (format == "json") {
                DataConverter::ConvertToJSON(result.data, file_path);
            } else {
                DataConverter::ConvertToParquet(result.data, file_path);
            }
            
            std::cout << "Exported " << result.data.size() << " records to " << file_path << std::endl;
            return file_path;
            
        } catch (const std::exception& e) {
            std::cerr << "Export failed: " << e.what() << std::endl;
            return "";
        }
    });
}

// FileBasedQueryStream实现
FileBasedQueryStream::FileBasedQueryStream(std::shared_ptr<ColdDataStorage> cold_storage,
                                          const QueryCondition& condition)
    : cold_storage_(cold_storage), condition_(condition) {
    InitializeFileList();
}

FileBasedQueryStream::~FileBasedQueryStream() {
    Close();
}

void FileBasedQueryStream::InitializeFileList() {
    // 简化实现：基于条件生成文件列表
    for (const auto& symbol : condition_.symbols) {
        for (const auto& exchange : condition_.exchanges) {
            auto current_time = condition_.start_time;
            while (current_time <= condition_.end_time) {
                // 生成文件路径（与之前的逻辑相同）
                auto time_t = std::chrono::system_clock::to_time_t(current_time);
                auto tm = *std::gmtime(&time_t);
                
                std::ostringstream oss;
                oss << exchange << "/" << symbol << "/" 
                    << (tm.tm_year + 1900) << "/" 
                    << std::setfill('0') << std::setw(2) << (tm.tm_mon + 1) << "/"
                    << std::setfill('0') << std::setw(2) << tm.tm_mday << "/"
                    << symbol << "_" << exchange << "_"
                    << (tm.tm_year + 1900) 
                    << std::setfill('0') << std::setw(2) << (tm.tm_mon + 1)
                    << std::setfill('0') << std::setw(2) << tm.tm_mday
                    << ".parquet";
                
                file_list_.push_back(oss.str());
                current_time += std::chrono::hours(24);
            }
        }
    }
}

std::future<TickDataBatch> FileBasedQueryStream::GetNextBatch() {
    return std::async(std::launch::async, [this]() -> TickDataBatch {
        if (closed_ || current_file_index_ >= file_list_.size()) {
            return TickDataBatch{};
        }
        
        std::string file_path = file_list_[current_file_index_++];
        return ProcessFile(file_path);
    });
}

bool FileBasedQueryStream::HasMore() const {
    return !closed_ && current_file_index_ < file_list_.size();
}

void FileBasedQueryStream::Close() {
    closed_ = true;
}

QueryResult::Stats FileBasedQueryStream::GetStats() const {
    return stats_;
}

TickDataBatch FileBasedQueryStream::ProcessFile(const std::string& file_path) {
    TickDataBatch result;
    
    try {
        // 从冷存储检索文件数据
        auto future = cold_storage_->RetrieveData(
            condition_.symbols.empty() ? "" : condition_.symbols[0],
            condition_.exchanges.empty() ? "" : condition_.exchanges[0],
            condition_.start_time,
            condition_.end_time
        );
        
        auto batch = future.get();
        stats_.files_scanned++;
        stats_.records_scanned += batch.size();
        
        // 应用过滤条件
        for (size_t i = 0; i < batch.size(); ++i) {
            if (MatchesCondition(batch, i)) {
                result.timestamps.push_back(batch.timestamps[i]);
                result.symbols.push_back(batch.symbols[i]);
                result.exchanges.push_back(batch.exchanges[i]);
                result.last_prices.push_back(batch.last_prices[i]);
                result.volumes.push_back(batch.volumes[i]);
                result.turnovers.push_back(batch.turnovers[i]);
                result.open_interests.push_back(batch.open_interests[i]);
                result.bid_prices.push_back(batch.bid_prices[i]);
                result.bid_volumes.push_back(batch.bid_volumes[i]);
                result.ask_prices.push_back(batch.ask_prices[i]);
                result.ask_volumes.push_back(batch.ask_volumes[i]);
                result.sequences.push_back(batch.sequences[i]);
            }
        }
        
        stats_.records_filtered += result.size();
        
    } catch (const std::exception& e) {
        std::cerr << "Error processing file " << file_path << ": " << e.what() << std::endl;
    }
    
    return result;
}

bool FileBasedQueryStream::MatchesCondition(const TickDataBatch& batch, size_t index) {
    // 检查价格范围
    if (condition_.min_price && batch.last_prices[index] < *condition_.min_price) {
        return false;
    }
    if (condition_.max_price && batch.last_prices[index] > *condition_.max_price) {
        return false;
    }
    
    // 检查成交量范围
    if (condition_.min_volume && batch.volumes[index] < *condition_.min_volume) {
        return false;
    }
    if (condition_.max_volume && batch.volumes[index] > *condition_.max_volume) {
        return false;
    }
    
    return true;
}

// DataConverter实现
bool DataConverter::ConvertToCSV(const TickDataBatch& batch, const std::string& file_path) {
    try {
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        // 写入CSV头部
        WriteCSVHeader(file);
        
        // 写入数据行
        for (size_t i = 0; i < batch.size(); ++i) {
            WriteCSVRecord(file, batch, i);
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error converting to CSV: " << e.what() << std::endl;
        return false;
    }
}

bool DataConverter::WriteCSVHeader(std::ofstream& file) {
    file << "timestamp,symbol,exchange,last_price,volume,turnover,open_interest,sequence\n";
    return true;
}

bool DataConverter::WriteCSVRecord(std::ofstream& file, const TickDataBatch& batch, size_t index) {
    file << batch.timestamps[index] << ","
         << batch.symbols[index] << ","
         << batch.exchanges[index] << ","
         << batch.last_prices[index] << ","
         << batch.volumes[index] << ","
         << batch.turnovers[index] << ","
         << batch.open_interests[index] << ","
         << batch.sequences[index] << "\n";
    return true;
}

bool DataConverter::ConvertToJSON(const TickDataBatch& batch, const std::string& file_path) {
    try {
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        file << "[\n";
        for (size_t i = 0; i < batch.size(); ++i) {
            if (i > 0) file << ",\n";
            WriteJSONRecord(file, batch, i);
        }
        file << "\n]\n";
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error converting to JSON: " << e.what() << std::endl;
        return false;
    }
}

bool DataConverter::WriteJSONRecord(std::ofstream& file, const TickDataBatch& batch, size_t index) {
    file << "  {\n"
         << "    \"timestamp\": " << batch.timestamps[index] << ",\n"
         << "    \"symbol\": \"" << batch.symbols[index] << "\",\n"
         << "    \"exchange\": \"" << batch.exchanges[index] << "\",\n"
         << "    \"last_price\": " << batch.last_prices[index] << ",\n"
         << "    \"volume\": " << batch.volumes[index] << ",\n"
         << "    \"turnover\": " << batch.turnovers[index] << ",\n"
         << "    \"open_interest\": " << batch.open_interests[index] << ",\n"
         << "    \"sequence\": " << batch.sequences[index] << "\n"
         << "  }";
    return true;
}

bool DataConverter::CompressFile(const std::string& input_path, 
                                const std::string& output_path,
                                const std::string& compression) {
    if (compression == "zstd") {
        try {
            std::ifstream input(input_path, std::ios::binary);
            std::ofstream output(output_path, std::ios::binary);
            
            if (!input.is_open() || !output.is_open()) {
                return false;
            }
            
            // 简化的ZSTD压缩实现
            // 实际应该使用ZSTD库进行压缩
            std::string buffer((std::istreambuf_iterator<char>(input)),
                              std::istreambuf_iterator<char>());
            
            // 这里应该调用ZSTD_compress
            output.write(buffer.c_str(), buffer.size());
            
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "Error compressing file: " << e.what() << std::endl;
            return false;
        }
    }
    
    return false;
}

} // namespace storage
} // namespace financial_data