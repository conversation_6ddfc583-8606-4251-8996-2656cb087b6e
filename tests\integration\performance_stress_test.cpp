/**
 * 性能压力测试工具 - C++版本
 * 测试高并发数据采集性能，验证存储系统吞吐量，测试查询响应时间，生成性能测试报告
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <algorithm>
#include <atomic>
#include <chrono>
#include <fstream>
#include <future>
#include <iomanip>
#include <memory>
#include <numeric>
#include <random>
#include <thread>
#include <vector>
#include <json/json.h>

#ifdef HAVE_BENCHMARK
#include <benchmark/benchmark.h>
#endif

#include "../../src/storage/unified_data_access.h"
#include "../../src/storage/storage_layer_selector.h"
#include "../../src/storage/query_performance_optimizer.h"
#include "../../src/config/config_manager.h"
#include "../../src/data_models.h"

using namespace std::chrono_literals;
using testing::_;
using testing::Return;

class PerformanceMetrics {
public:
    struct Measurement {
        std::chrono::steady_clock::time_point timestamp;
        double value;
    };
    
    void recordThroughput(double ops_per_second) {
        std::lock_guard<std::mutex> lock(mutex_);
        throughput_measurements_.push_back({std::chrono::steady_clock::now(), ops_per_second});
    }
    
    void recordLatency(double latency_ms) {
        std::lock_guard<std::mutex> lock(mutex_);
        latency_measurements_.push_back({std::chrono::steady_clock::now(), latency_ms});
    }
    
    void recordCpuUsage(double cpu_percent) {
        std::lock_guard<std::mutex> lock(mutex_);
        cpu_measurements_.push_back({std::chrono::steady_clock::now(), cpu_percent});
    }
    
    void recordMemoryUsage(double memory_percent) {
        std::lock_guard<std::mutex> lock(mutex_);
        memory_measurements_.push_back({std::chrono::steady_clock::now(), memory_percent});
    }
    
    Json::Value getSummary() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        Json::Value summary;
        summary["throughput"] = calculateStats(throughput_measurements_);
        summary["latency"] = calculateStats(latency_measurements_);
        summary["cpu_usage"] = calculateStats(cpu_measurements_);
        summary["memory_usage"] = calculateStats(memory_measurements_);
        summary["total_measurements"] = static_cast<int>(throughput_measurements_.size());
        
        return summary;
    }

private:
    mutable std::mutex mutex_;
    std::vector<Measurement> throughput_measurements_;
    std::vector<Measurement> latency_measurements_;
    std::vector<Measurement> cpu_measurements_;
    std::vector<Measurement> memory_measurements_;
    
    Json::Value calculateStats(const std::vector<Measurement>& measurements) const {
        Json::Value stats;
        
        if (measurements.empty()) {
            stats["min"] = 0.0;
            stats["max"] = 0.0;
            stats["avg"] = 0.0;
            stats["p50"] = 0.0;
            stats["p95"] = 0.0;
            stats["p99"] = 0.0;
            return stats;
        }
        
        std::vector<double> values;
        values.reserve(measurements.size());
        for (const auto& m : measurements) {
            values.push_back(m.value);
        }
        
        std::sort(values.begin(), values.end());
        
        stats["min"] = values.front();
        stats["max"] = values.back();
        stats["avg"] = std::accumulate(values.begin(), values.end(), 0.0) / values.size();
        stats["p50"] = values[values.size() * 50 / 100];
        stats["p95"] = values[values.size() * 95 / 100];
        stats["p99"] = values[values.size() * 99 / 100];
        
        return stats;
    }
};

class PerformanceStressTester : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化测试配置
        setupTestConfig();
        
        // 初始化组件
        unified_access_ = std::make_unique<UnifiedDataAccessInterface>(test_config_);
        storage_selector_ = std::make_unique<StorageLayerSelector>(test_config_);
        query_optimizer_ = std::make_unique<QueryPerformanceOptimizer>(test_config_);
        
        // 初始化性能指标收集器
        metrics_ = std::make_unique<PerformanceMetrics>();
        
        // 测试参数
        test_symbols_ = {"000001", "000002", "600000", "600036", "000858"};
        concurrent_levels_ = {1, 5, 10, 20, 50};
        data_volumes_ = {100, 1000, 10000, 50000};
        
        std::cout << "Performance stress tester initialized" << std::endl;
    }
    
    void TearDown() override {
        // 生成性能报告
        generatePerformanceReport();
        
        std::cout << "Performance stress test completed" << std::endl;
    }

private:
    void setupTestConfig() {
        test_config_["redis"]["host"] = "localhost";
        test_config_["redis"]["port"] = 6379;
        test_config_["redis"]["db"] = 1;
        
        test_config_["clickhouse"]["host"] = "localhost";
        test_config_["clickhouse"]["port"] = 8123;
        test_config_["clickhouse"]["database"] = "test_market_data";
        
        test_config_["hot_storage_days"] = 7;
        test_config_["warm_storage_days"] = 730;
        
        test_config_["performance"]["batch_size"] = 1000;
        test_config_["performance"]["max_concurrent"] = 50;
    }
    
    void generatePerformanceReport() {
        Json::Value report;
        report["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        report["performance_summary"] = metrics_->getSummary();
        report["test_results"] = test_results_;
        
        // 保存报告
        std::ofstream report_file("tests/integration/cpp_performance_report.json");
        report_file << report;
        
        // 生成控制台报告
        printPerformanceReport(report);
        
        std::cout << "Performance report generated" << std::endl;
    }
    
    void printPerformanceReport(const Json::Value& report) {
        std::cout << "\n" << std::string(80, '=') << std::endl;
        std::cout << "C++ PERFORMANCE STRESS TEST REPORT" << std::endl;
        std::cout << std::string(80, '=') << std::endl;
        
        auto summary = report["performance_summary"];
        std::cout << "Performance Summary:" << std::endl;
        std::cout << "  Average Throughput: " << std::fixed << std::setprecision(2) 
                  << summary["throughput"]["avg"].asDouble() << " ops/sec" << std::endl;
        std::cout << "  Average Latency: " << std::fixed << std::setprecision(2) 
                  << summary["latency"]["avg"].asDouble() << " ms" << std::endl;
        std::cout << "  Peak CPU Usage: " << std::fixed << std::setprecision(1) 
                  << summary["cpu_usage"]["max"].asDouble() << "%" << std::endl;
        std::cout << "  Peak Memory Usage: " << std::fixed << std::setprecision(1) 
                  << summary["memory_usage"]["max"].asDouble() << "%" << std::endl;
        
        std::cout << std::string(80, '=') << std::endl;
    }

protected:
    std::unique_ptr<UnifiedDataAccessInterface> unified_access_;
    std::unique_ptr<StorageLayerSelector> storage_selector_;
    std::unique_ptr<QueryPerformanceOptimizer> query_optimizer_;
    std::unique_ptr<PerformanceMetrics> metrics_;
    
    std::map<std::string, Json::Value> test_config_;
    Json::Value test_results_;
    
    std::vector<std::string> test_symbols_;
    std::vector<int> concurrent_levels_;
    std::vector<int> data_volumes_;
};

TEST_F(PerformanceStressTester, TestStorageThroughput) {
    std::cout << "Testing storage system throughput" << std::endl;
    
    Json::Value throughput_results;
    
    // 测试不同数据量的写入性能
    std::vector<int> write_volumes = {1000, 5000, 10000};
    
    for (int volume : write_volumes) {
        std::cout << "Testing write throughput with " << volume << " records" << std::endl;
        
        // 创建测试数据
        std::vector<StandardTick> test_data;
        auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        for (int i = 0; i < volume; ++i) {
            StandardTick tick;
            tick.symbol = "THROUGHPUT_TEST";
            tick.timestamp_ns = base_timestamp + i * 1000000000LL;
            tick.price = 10.0 + i * 0.01;
            tick.volume = 1000 + i;
            tick.bid_price = tick.price - 0.01;
            tick.ask_price = tick.price + 0.01;
            
            test_data.push_back(tick);
        }
        
        // 测试写入性能
        auto start_time = std::chrono::steady_clock::now();
        
        auto store_result = unified_access_->StoreBatchAsync(test_data).get();
        
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        EXPECT_TRUE(store_result.success) << "Storage failed for volume " << volume;
        
        if (store_result.success) {
            double throughput = static_cast<double>(volume) / (duration.count() / 1000.0);
            metrics_->recordThroughput(throughput);
            metrics_->recordLatency(duration.count());
            
            Json::Value volume_result;
            volume_result["volume"] = volume;
            volume_result["success"] = true;
            volume_result["time_ms"] = duration.count();
            volume_result["throughput_records_per_second"] = throughput;
            
            throughput_results[std::to_string(volume)] = volume_result;
            
            std::cout << "  Volume " << volume << ": " << std::fixed << std::setprecision(2) 
                      << throughput << " records/sec" << std::endl;
        }
    }
    
    test_results_["storage_throughput"] = throughput_results;
}

TEST_F(PerformanceStressTester, TestQueryPerformance) {
    std::cout << "Testing query performance" << std::endl;
    
    Json::Value query_results;
    
    // 首先存储一些测试数据
    std::vector<StandardTick> test_data;
    auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    for (int i = 0; i < 10000; ++i) {
        StandardTick tick;
        tick.symbol = "QUERY_TEST";
        tick.timestamp_ns = base_timestamp + i * 1000000000LL;
        tick.price = 10.0 + i * 0.01;
        tick.volume = 1000 + i;
        
        test_data.push_back(tick);
    }
    
    auto store_result = unified_access_->StoreBatchAsync(test_data).get();
    ASSERT_TRUE(store_result.success) << "Failed to store test data for query performance test";
    
    // 测试不同查询大小的性能
    std::vector<int> query_limits = {10, 100, 1000, 5000};
    
    for (int limit : query_limits) {
        std::cout << "Testing query performance with limit " << limit << std::endl;
        
        QueryRequest query_request;
        query_request.symbol = "QUERY_TEST";
        query_request.data_type = "tick";
        query_request.start_timestamp_ns = base_timestamp;
        query_request.end_timestamp_ns = base_timestamp + limit * 1000000000LL;
        query_request.limit = limit;
        
        // 执行多次查询取平均值
        std::vector<double> query_times;
        const int num_iterations = 10;
        
        for (int i = 0; i < num_iterations; ++i) {
            auto start_time = std::chrono::steady_clock::now();
            
            auto query_result = unified_access_->QueryData(query_request).get();
            
            auto end_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
            
            EXPECT_TRUE(query_result.success) << "Query failed for limit " << limit;
            
            if (query_result.success) {
                query_times.push_back(duration.count() / 1000.0); // 转换为毫秒
            }
        }
        
        if (!query_times.empty()) {
            double avg_query_time = std::accumulate(query_times.begin(), query_times.end(), 0.0) / query_times.size();
            double throughput = limit / (avg_query_time / 1000.0); // records per second
            
            metrics_->recordLatency(avg_query_time);
            metrics_->recordThroughput(throughput);
            
            Json::Value limit_result;
            limit_result["limit"] = limit;
            limit_result["success"] = true;
            limit_result["avg_query_time_ms"] = avg_query_time;
            limit_result["throughput_records_per_second"] = throughput;
            limit_result["iterations"] = num_iterations;
            
            query_results[std::to_string(limit)] = limit_result;
            
            std::cout << "  Limit " << limit << ": " << std::fixed << std::setprecision(2) 
                      << avg_query_time << " ms avg, " << throughput << " records/sec" << std::endl;
        }
    }
    
    test_results_["query_performance"] = query_results;
}

TEST_F(PerformanceStressTester, TestConcurrentAccess) {
    std::cout << "Testing concurrent access performance" << std::endl;
    
    Json::Value concurrent_results;
    
    for (int concurrent_level : concurrent_levels_) {
        if (concurrent_level > 20) continue; // 限制并发级别以避免资源耗尽
        
        std::cout << "Testing with " << concurrent_level << " concurrent threads" << std::endl;
        
        std::atomic<int> successful_operations{0};
        std::atomic<int> failed_operations{0};
        std::vector<double> operation_times;
        std::mutex times_mutex;
        
        auto start_time = std::chrono::steady_clock::now();
        
        // 创建并发任务
        std::vector<std::future<void>> futures;
        
        for (int i = 0; i < concurrent_level; ++i) {
            auto future = std::async(std::launch::async, [&, i]() {
                // 每个线程执行10个操作
                for (int op = 0; op < 10; ++op) {
                    auto op_start = std::chrono::steady_clock::now();
                    
                    try {
                        // 创建线程特定的测试数据
                        StandardTick tick;
                        tick.symbol = "CONCURRENT_" + std::to_string(i);
                        tick.timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                            std::chrono::system_clock::now().time_since_epoch()).count() + op;
                        tick.price = 10.0 + i + op * 0.01;
                        tick.volume = 1000 + i * 100 + op;
                        
                        // 随机选择读或写操作
                        if (op % 2 == 0) {
                            // 写操作
                            auto store_result = unified_access_->StoreBatchAsync({tick}).get();
                            if (store_result.success) {
                                successful_operations++;
                            } else {
                                failed_operations++;
                            }
                        } else {
                            // 读操作
                            QueryRequest query_request;
                            query_request.symbol = tick.symbol;
                            query_request.data_type = "tick";
                            query_request.start_timestamp_ns = tick.timestamp_ns;
                            query_request.end_timestamp_ns = tick.timestamp_ns + 1;
                            query_request.limit = 1;
                            
                            auto query_result = unified_access_->QueryData(query_request).get();
                            if (query_result.success) {
                                successful_operations++;
                            } else {
                                failed_operations++;
                            }
                        }
                        
                        auto op_end = std::chrono::steady_clock::now();
                        auto op_duration = std::chrono::duration_cast<std::chrono::milliseconds>(op_end - op_start);
                        
                        {
                            std::lock_guard<std::mutex> lock(times_mutex);
                            operation_times.push_back(op_duration.count());
                        }
                        
                    } catch (const std::exception& e) {
                        failed_operations++;
                        std::cerr << "Concurrent operation failed: " << e.what() << std::endl;
                    }
                    
                    // 模拟操作间隔
                    std::this_thread::sleep_for(10ms);
                }
            });
            
            futures.push_back(std::move(future));
        }
        
        // 等待所有任务完成
        for (auto& future : futures) {
            future.get();
        }
        
        auto end_time = std::chrono::steady_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        // 计算统计数据
        int total_operations = successful_operations + failed_operations;
        double success_rate = total_operations > 0 ? 
            (static_cast<double>(successful_operations) / total_operations * 100) : 0;
        double throughput = total_operations > 0 ? 
            (static_cast<double>(total_operations) / (total_duration.count() / 1000.0)) : 0;
        
        double avg_operation_time = 0;
        if (!operation_times.empty()) {
            avg_operation_time = std::accumulate(operation_times.begin(), operation_times.end(), 0.0) / operation_times.size();
        }
        
        metrics_->recordThroughput(throughput);
        if (avg_operation_time > 0) {
            metrics_->recordLatency(avg_operation_time);
        }
        
        Json::Value level_result;
        level_result["concurrent_level"] = concurrent_level;
        level_result["success"] = success_rate > 80; // 80%成功率阈值
        level_result["total_operations"] = total_operations;
        level_result["successful_operations"] = successful_operations.load();
        level_result["failed_operations"] = failed_operations.load();
        level_result["success_rate_percent"] = success_rate;
        level_result["total_time_ms"] = total_duration.count();
        level_result["throughput_ops_per_second"] = throughput;
        level_result["avg_operation_time_ms"] = avg_operation_time;
        
        concurrent_results[std::to_string(concurrent_level)] = level_result;
        
        EXPECT_GT(success_rate, 80) << "Success rate too low for concurrent level " << concurrent_level;
        
        std::cout << "  Concurrent level " << concurrent_level << ": " 
                  << std::fixed << std::setprecision(1) << success_rate << "% success, "
                  << std::setprecision(2) << throughput << " ops/sec" << std::endl;
    }
    
    test_results_["concurrent_access"] = concurrent_results;
}

TEST_F(PerformanceStressTester, TestLargeVolumeProcessing) {
    std::cout << "Testing large volume data processing" << std::endl;
    
    Json::Value volume_results;
    
    // 测试较小的数据量以避免测试时间过长
    std::vector<int> test_volumes = {1000, 5000, 10000};
    
    for (int volume : test_volumes) {
        std::cout << "Testing with " << volume << " records" << std::endl;
        
        // 创建大量测试数据
        std::vector<StandardTick> large_dataset;
        auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        large_dataset.reserve(volume);
        for (int i = 0; i < volume; ++i) {
            StandardTick tick;
            tick.symbol = "VOLUME_TEST_" + std::to_string(volume);
            tick.timestamp_ns = base_timestamp + i * 1000000000LL;
            tick.price = 10.0 + (i % 1000) * 0.01;
            tick.volume = 1000 + i;
            tick.bid_price = tick.price - 0.01;
            tick.ask_price = tick.price + 0.01;
            
            large_dataset.push_back(tick);
        }
        
        // 测试存储性能
        auto storage_start = std::chrono::steady_clock::now();
        
        auto store_result = unified_access_->StoreBatchAsync(large_dataset).get();
        
        auto storage_end = std::chrono::steady_clock::now();
        auto storage_duration = std::chrono::duration_cast<std::chrono::milliseconds>(storage_end - storage_start);
        
        EXPECT_TRUE(store_result.success) << "Large volume storage failed for " << volume << " records";
        
        if (!store_result.success) {
            Json::Value volume_result;
            volume_result["volume"] = volume;
            volume_result["success"] = false;
            volume_result["error"] = "Storage failed";
            volume_results[std::to_string(volume)] = volume_result;
            continue;
        }
        
        // 测试查询性能
        auto query_start = std::chrono::steady_clock::now();
        
        QueryRequest query_request;
        query_request.symbol = "VOLUME_TEST_" + std::to_string(volume);
        query_request.data_type = "tick";
        query_request.start_timestamp_ns = base_timestamp;
        query_request.end_timestamp_ns = base_timestamp + volume * 1000000000LL;
        query_request.limit = volume;
        
        auto query_result = unified_access_->QueryData(query_request).get();
        
        auto query_end = std::chrono::steady_clock::now();
        auto query_duration = std::chrono::duration_cast<std::chrono::milliseconds>(query_end - query_start);
        
        EXPECT_TRUE(query_result.success) << "Large volume query failed for " << volume << " records";
        
        if (query_result.success) {
            int retrieved_records = static_cast<int>(query_result.ticks.size());
            
            double storage_throughput = volume / (storage_duration.count() / 1000.0);
            double query_throughput = retrieved_records / (query_duration.count() / 1000.0);
            
            metrics_->recordThroughput(storage_throughput);
            metrics_->recordLatency(storage_duration.count());
            
            Json::Value volume_result;
            volume_result["volume"] = volume;
            volume_result["success"] = true;
            volume_result["storage_time_ms"] = storage_duration.count();
            volume_result["query_time_ms"] = query_duration.count();
            volume_result["storage_throughput_records_per_second"] = storage_throughput;
            volume_result["query_throughput_records_per_second"] = query_throughput;
            volume_result["retrieved_records"] = retrieved_records;
            volume_result["data_integrity"] = (retrieved_records == volume);
            
            volume_results[std::to_string(volume)] = volume_result;
            
            std::cout << "  Volume " << volume << ": Storage " << std::fixed << std::setprecision(2) 
                      << storage_throughput << " records/sec, Query " << query_throughput << " records/sec" << std::endl;
        }
    }
    
    test_results_["large_volume_processing"] = volume_results;
}

TEST_F(PerformanceStressTester, TestMemoryPressure) {
    std::cout << "Testing memory pressure scenarios" << std::endl;
    
    Json::Value memory_results;
    
    // 测试不同内存压力级别
    std::vector<int> memory_loads = {10, 50, 100}; // MB
    
    for (int memory_load : memory_loads) {
        std::cout << "Testing with " << memory_load << "MB memory load" << std::endl;
        
        try {
            // 分配指定大小的内存
            std::vector<std::vector<double>> memory_data;
            
            for (int i = 0; i < memory_load; ++i) {
                // 每次分配约1MB的数据
                std::vector<double> chunk(125000); // 125000 * 8 bytes ≈ 1MB
                std::fill(chunk.begin(), chunk.end(), static_cast<double>(i));
                memory_data.push_back(std::move(chunk));
            }
            
            // 在内存压力下执行操作
            auto test_start = std::chrono::steady_clock::now();
            
            // 创建测试数据
            std::vector<StandardTick> test_data;
            auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            
            for (int i = 0; i < 1000; ++i) {
                StandardTick tick;
                tick.symbol = "MEMORY_TEST";
                tick.timestamp_ns = base_timestamp + i * 1000000000LL;
                tick.price = 10.0 + i * 0.01;
                tick.volume = 1000 + i;
                
                test_data.push_back(tick);
            }
            
            auto store_result = unified_access_->StoreBatchAsync(test_data).get();
            
            auto test_end = std::chrono::steady_clock::now();
            auto test_duration = std::chrono::duration_cast<std::chrono::milliseconds>(test_end - test_start);
            
            // 清理内存
            memory_data.clear();
            
            Json::Value memory_result;
            memory_result["memory_load_mb"] = memory_load;
            memory_result["success"] = store_result.success;
            memory_result["operation_time_ms"] = test_duration.count();
            
            if (store_result.success) {
                double throughput = 1000.0 / (test_duration.count() / 1000.0);
                metrics_->recordThroughput(throughput);
                memory_result["throughput_records_per_second"] = throughput;
            }
            
            memory_results[std::to_string(memory_load)] = memory_result;
            
            EXPECT_TRUE(store_result.success) << "Operation failed under " << memory_load << "MB memory pressure";
            
            std::cout << "  Memory load " << memory_load << "MB: " 
                      << (store_result.success ? "SUCCESS" : "FAILED") 
                      << " (" << test_duration.count() << "ms)" << std::endl;
            
        } catch (const std::exception& e) {
            Json::Value memory_result;
            memory_result["memory_load_mb"] = memory_load;
            memory_result["success"] = false;
            memory_result["error"] = e.what();
            
            memory_results[std::to_string(memory_load)] = memory_result;
            
            std::cout << "  Memory load " << memory_load << "MB: FAILED - " << e.what() << std::endl;
        }
    }
    
    test_results_["memory_pressure"] = memory_results;
}

TEST_F(PerformanceStressTester, TestLatencyUnderLoad) {
    std::cout << "Testing latency under various loads" << std::endl;
    
    Json::Value latency_results;
    
    // 测试不同负载下的延迟
    std::vector<int> load_levels = {1, 10, 50, 100};
    
    for (int load_level : load_levels) {
        std::cout << "Testing latency with load level " << load_level << std::endl;
        
        std::vector<double> latencies;
        
        // 执行多次操作测量延迟
        for (int i = 0; i < load_level; ++i) {
            auto start_time = std::chrono::steady_clock::now();
            
            // 创建测试数据
            StandardTick tick;
            tick.symbol = "LATENCY_TEST";
            tick.timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count() + i;
            tick.price = 10.0 + i * 0.01;
            tick.volume = 1000 + i;
            
            // 执行存储操作
            auto store_result = unified_access_->StoreBatchAsync({tick}).get();
            
            auto end_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
            
            if (store_result.success) {
                latencies.push_back(duration.count() / 1000.0); // 转换为毫秒
            }
        }
        
        if (!latencies.empty()) {
            std::sort(latencies.begin(), latencies.end());
            
            double avg_latency = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();
            double p50_latency = latencies[latencies.size() * 50 / 100];
            double p95_latency = latencies[latencies.size() * 95 / 100];
            double p99_latency = latencies[latencies.size() * 99 / 100];
            
            metrics_->recordLatency(avg_latency);
            
            Json::Value load_result;
            load_result["load_level"] = load_level;
            load_result["success"] = true;
            load_result["avg_latency_ms"] = avg_latency;
            load_result["p50_latency_ms"] = p50_latency;
            load_result["p95_latency_ms"] = p95_latency;
            load_result["p99_latency_ms"] = p99_latency;
            load_result["successful_operations"] = static_cast<int>(latencies.size());
            
            latency_results[std::to_string(load_level)] = load_result;
            
            std::cout << "  Load " << load_level << ": Avg " << std::fixed << std::setprecision(2) 
                      << avg_latency << "ms, P95 " << p95_latency << "ms, P99 " << p99_latency << "ms" << std::endl;
        }
    }
    
    test_results_["latency_under_load"] = latency_results;
}

#ifdef HAVE_BENCHMARK
// Google Benchmark集成测试
static void BM_StorageThroughput(benchmark::State& state) {
    // 初始化测试环境
    std::map<std::string, Json::Value> config;
    config["redis"]["host"] = "localhost";
    config["redis"]["port"] = 6379;
    config["redis"]["db"] = 1;
    
    UnifiedDataAccessInterface unified_access(config);
    
    for (auto _ : state) {
        // 创建测试数据
        std::vector<StandardTick> test_data;
        auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        for (int i = 0; i < state.range(0); ++i) {
            StandardTick tick;
            tick.symbol = "BENCHMARK_TEST";
            tick.timestamp_ns = base_timestamp + i * 1000000000LL;
            tick.price = 10.0 + i * 0.01;
            tick.volume = 1000 + i;
            
            test_data.push_back(tick);
        }
        
        // 执行存储操作
        auto store_result = unified_access.StoreBatchAsync(test_data).get();
        
        if (!store_result.success) {
            state.SkipWithError("Storage operation failed");
        }
    }
    
    state.SetItemsProcessed(state.iterations() * state.range(0));
}

BENCHMARK(BM_StorageThroughput)->Range(100, 10000)->Unit(benchmark::kMillisecond);

static void BM_QueryLatency(benchmark::State& state) {
    // 初始化测试环境
    std::map<std::string, Json::Value> config;
    config["redis"]["host"] = "localhost";
    config["redis"]["port"] = 6379;
    config["redis"]["db"] = 1;
    
    UnifiedDataAccessInterface unified_access(config);
    
    // 预先存储一些数据
    std::vector<StandardTick> test_data;
    auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    for (int i = 0; i < 10000; ++i) {
        StandardTick tick;
        tick.symbol = "BENCHMARK_QUERY_TEST";
        tick.timestamp_ns = base_timestamp + i * 1000000000LL;
        tick.price = 10.0 + i * 0.01;
        tick.volume = 1000 + i;
        
        test_data.push_back(tick);
    }
    
    unified_access.StoreBatchAsync(test_data).get();
    
    for (auto _ : state) {
        QueryRequest query_request;
        query_request.symbol = "BENCHMARK_QUERY_TEST";
        query_request.data_type = "tick";
        query_request.start_timestamp_ns = base_timestamp;
        query_request.end_timestamp_ns = base_timestamp + state.range(0) * 1000000000LL;
        query_request.limit = state.range(0);
        
        auto query_result = unified_access.QueryData(query_request).get();
        
        if (!query_result.success) {
            state.SkipWithError("Query operation failed");
        }
    }
    
    state.SetItemsProcessed(state.iterations() * state.range(0));
}

BENCHMARK(BM_QueryLatency)->Range(10, 1000)->Unit(benchmark::kMicrosecond);
#endif

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    // 设置测试环境
    setenv("TESTING", "1", 1);
    
    std::cout << "Starting C++ performance stress tests" << std::endl;
    
    int result = RUN_ALL_TESTS();
    
#ifdef HAVE_BENCHMARK
    // 如果有benchmark库，也运行benchmark测试
    if (result == 0) {
        std::cout << "\nRunning benchmark tests..." << std::endl;
        benchmark::Initialize(&argc, argv);
        benchmark::RunSpecifiedBenchmarks();
    }
#endif
    
    std::cout << "C++ performance stress tests completed" << std::endl;
    
    return result;
}