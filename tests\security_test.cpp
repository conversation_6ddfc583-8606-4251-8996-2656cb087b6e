#include <gtest/gtest.h>
#include "../src/security/security_manager.h"
#include "../src/security/tls_manager.h"
#include "../src/security/encryption_manager.h"
#include "../src/security/jwt_auth.h"
#include "../src/security/rbac_manager.h"
#include "../src/security/audit_logger.h"
#include <fstream>
#include <filesystem>

using namespace financial_data::security;

class SecurityTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试配置
        config_.tls.cert_file = "test_cert.pem";
        config_.tls.key_file = "test_key.pem";
        config_.tls.ca_file = "test_ca.pem";
        config_.tls.require_client_cert = false; // 测试时不要求客户端证书
        
        config_.encryption.key_file = "test_encryption.key";
        config_.encryption.algorithm = "AES-256-GCM";
        config_.encryption.enable_disk_encryption = true;
        
        config_.jwt.secret_key = "test-secret-key-for-jwt-testing";
        config_.jwt.issuer = "test-issuer";
        config_.jwt.token_expiry = std::chrono::seconds(3600);
        config_.jwt.refresh_expiry = std::chrono::seconds(86400);
        config_.jwt.require_mfa = false; // 测试时不要求MFA
        
        config_.rbac.roles_config_file = "test_roles.json";
        config_.rbac.permissions_config_file = "test_permissions.json";
        config_.rbac.enable_dynamic_permissions = true;
        config_.rbac.cache_ttl = std::chrono::seconds(300);
        
        config_.audit.log_file = "test_audit.log";
        config_.audit.log_level = "INFO";
        config_.audit.enable_real_time_alerts = true;
        config_.audit.max_log_size_mb = 10;
        config_.audit.max_log_files = 3;
        config_.audit.sensitive_operations = {"LOGIN", "LOGOUT", "DATA_ACCESS"};
        
        config_.enable_rate_limiting = true;
        config_.max_requests_per_minute = 100;
        config_.enable_ip_whitelist = false;
    }
    
    void TearDown() override {
        // 清理测试文件
        std::vector<std::string> test_files = {
            "test_encryption.key", "test_audit.log", "test_roles.json", "test_permissions.json"
        };
        
        for (const auto& file : test_files) {
            if (std::filesystem::exists(file)) {
                std::filesystem::remove(file);
            }
        }
    }
    
    SecurityConfig config_;
};

// 加密管理器测试
TEST_F(SecurityTest, EncryptionManagerTest) {
    EncryptionManager encryption_manager(config_.encryption);
    
    ASSERT_TRUE(encryption_manager.Initialize());
    
    // 测试数据加密和解密
    std::string original_data = "这是测试数据：价格=12345.67";
    std::vector<uint8_t> plaintext(original_data.begin(), original_data.end());
    std::vector<uint8_t> ciphertext;
    std::vector<uint8_t> iv;
    
    ASSERT_TRUE(encryption_manager.EncryptData(plaintext, ciphertext, iv));
    ASSERT_FALSE(ciphertext.empty());
    ASSERT_EQ(iv.size(), 16); // AES-GCM IV size
    
    // 解密数据
    std::vector<uint8_t> decrypted_data;
    ASSERT_TRUE(encryption_manager.DecryptData(ciphertext, iv, decrypted_data));
    
    std::string decrypted_string(decrypted_data.begin(), decrypted_data.end());
    ASSERT_EQ(original_data, decrypted_string);
    
    // 测试密钥指纹
    std::string fingerprint = encryption_manager.GetKeyFingerprint();
    ASSERT_FALSE(fingerprint.empty());
    ASSERT_EQ(fingerprint.length(), 64); // SHA256 hex string length
}

// JWT认证测试
TEST_F(SecurityTest, JWTAuthTest) {
    JWTAuth jwt_auth(config_.jwt);
    
    ASSERT_TRUE(jwt_auth.Initialize());
    
    // 创建测试用户
    UserInfo test_user;
    test_user.user_id = "test_user";
    test_user.username = "testuser";
    test_user.email = "<EMAIL>";
    test_user.roles = {"user"};
    test_user.created_at = std::chrono::system_clock::now();
    test_user.mfa_enabled = false;
    
    ASSERT_TRUE(jwt_auth.CreateUser(test_user, "test_password"));
    
    // 测试用户认证
    TokenInfo token_info = jwt_auth.Authenticate("testuser", "test_password");
    ASSERT_FALSE(token_info.token.empty());
    ASSERT_EQ(token_info.user_id, "test_user");
    
    // 测试令牌验证
    UserInfo verified_user;
    ASSERT_TRUE(jwt_auth.VerifyToken(token_info.token, verified_user));
    ASSERT_EQ(verified_user.user_id, "test_user");
    ASSERT_EQ(verified_user.username, "testuser");
    
    // 测试错误密码
    TokenInfo failed_token = jwt_auth.Authenticate("testuser", "wrong_password");
    ASSERT_TRUE(failed_token.token.empty());
    
    // 测试令牌撤销
    ASSERT_TRUE(jwt_auth.RevokeToken(token_info.token));
    ASSERT_FALSE(jwt_auth.VerifyToken(token_info.token, verified_user));
}

// RBAC管理器测试
TEST_F(SecurityTest, RBACManagerTest) {
    RBACManager rbac_manager(config_.rbac);
    
    ASSERT_TRUE(rbac_manager.Initialize());
    
    // 测试角色创建
    Role test_role;
    test_role.role_id = "test_role";
    test_role.name = "测试角色";
    test_role.description = "用于测试的角色";
    test_role.is_active = true;
    test_role.created_at = std::chrono::system_clock::now();
    
    PermissionRule rule;
    rule.permission = Permission::READ_MARKET_DATA;
    rule.resource = ResourceType::MARKET_DATA;
    rule.action = Action::READ;
    test_role.permissions.push_back(rule);
    
    ASSERT_TRUE(rbac_manager.CreateRole(test_role));
    
    // 测试角色查询
    Role retrieved_role = rbac_manager.GetRole("test_role");
    ASSERT_EQ(retrieved_role.role_id, "test_role");
    ASSERT_EQ(retrieved_role.name, "测试角色");
    
    // 测试用户角色分配
    ASSERT_TRUE(rbac_manager.AssignRoleToUser("test_user", "test_role", "admin"));
    
    std::vector<std::string> user_roles = rbac_manager.GetUserRoles("test_user");
    ASSERT_EQ(user_roles.size(), 1);
    ASSERT_EQ(user_roles[0], "test_role");
    
    // 测试权限检查
    ASSERT_TRUE(rbac_manager.CheckPermission("test_user", Permission::READ_MARKET_DATA, 
                                           ResourceType::MARKET_DATA, Action::READ));
    
    ASSERT_FALSE(rbac_manager.CheckPermission("test_user", Permission::WRITE_MARKET_DATA, 
                                            ResourceType::MARKET_DATA, Action::WRITE));
    
    // 测试角色撤销
    ASSERT_TRUE(rbac_manager.RevokeRoleFromUser("test_user", "test_role"));
    user_roles = rbac_manager.GetUserRoles("test_user");
    ASSERT_TRUE(user_roles.empty());
}

// 审计日志测试
TEST_F(SecurityTest, AuditLoggerTest) {
    AuditLogger audit_logger(config_.audit);
    
    ASSERT_TRUE(audit_logger.Initialize());
    
    // 测试登录日志
    audit_logger.LogLogin("test_user", "192.168.1.100", true);
    audit_logger.LogLogin("test_user", "192.168.1.100", false, "Invalid password");
    
    // 测试数据访问日志
    audit_logger.LogDataAccess("test_user", "market_data", "read", true);
    audit_logger.LogDataAccess("test_user", "historical_data", "export", false);
    
    // 测试配置变更日志
    audit_logger.LogConfigChange("admin", "max_connections", "100", "200");
    
    // 测试安全违规日志
    audit_logger.LogSecurityViolation("test_user", "UNAUTHORIZED_ACCESS", 
                                     "Attempted to access restricted resource", "192.168.1.100");
    
    // 查询审计事件
    AuditQuery query;
    query.user_id = "test_user";
    query.limit = 10;
    
    auto events = audit_logger.QueryEvents(query);
    ASSERT_GE(events.size(), 3); // 至少应该有3个事件
    
    // 验证事件内容
    bool found_login_success = false;
    bool found_login_failure = false;
    bool found_data_access = false;
    
    for (const auto& event : events) {
        if (event.event_type == AuditEventType::LOGIN && event.success) {
            found_login_success = true;
        }
        if (event.event_type == AuditEventType::LOGIN && !event.success) {
            found_login_failure = true;
        }
        if (event.event_type == AuditEventType::DATA_ACCESS) {
            found_data_access = true;
        }
    }
    
    ASSERT_TRUE(found_login_success);
    ASSERT_TRUE(found_login_failure);
    ASSERT_TRUE(found_data_access);
    
    // 测试统计信息
    auto stats = audit_logger.GetStatistics(
        std::chrono::system_clock::now() - std::chrono::hours(1),
        std::chrono::system_clock::now()
    );
    
    ASSERT_GE(stats.total_events, 4);
    ASSERT_GE(stats.login_attempts, 2);
    ASSERT_GE(stats.failed_logins, 1);
    ASSERT_GE(stats.security_violations, 1);
}

// 安全管理器集成测试
TEST_F(SecurityTest, SecurityManagerIntegrationTest) {
    SecurityManager security_manager(config_);
    
    ASSERT_TRUE(security_manager.Initialize());
    
    // 测试用户认证
    auto auth_result = security_manager.AuthenticateUser("admin", "admin123", 
                                                        "192.168.1.100", "TestClient/1.0");
    
    ASSERT_TRUE(auth_result.success);
    ASSERT_FALSE(auth_result.user_id.empty());
    ASSERT_FALSE(auth_result.session_id.empty());
    
    // 测试权限检查
    ASSERT_TRUE(security_manager.AuthorizeAction(auth_result.user_id, "market_data", "read"));
    ASSERT_TRUE(security_manager.AuthorizeAction(auth_result.user_id, "system_config", "manage"));
    
    // 测试会话验证
    std::string validated_user_id;
    // 注意：这里需要实际的JWT令牌，简化测试
    
    // 测试数据加密
    std::string original_data = "敏感数据测试";
    std::string encrypted_data;
    std::string decrypted_data;
    
    ASSERT_TRUE(security_manager.EncryptSensitiveData(original_data, encrypted_data));
    ASSERT_TRUE(security_manager.DecryptSensitiveData(encrypted_data, decrypted_data));
    ASSERT_EQ(original_data, decrypted_data);
    
    // 测试安全状态
    auto status = security_manager.GetSecurityStatus();
    ASSERT_TRUE(status.tls_enabled);
    ASSERT_TRUE(status.encryption_enabled);
    ASSERT_TRUE(status.jwt_enabled);
    ASSERT_TRUE(status.rbac_enabled);
    ASSERT_TRUE(status.audit_enabled);
    
    // 测试用户登出
    security_manager.LogoutUser(auth_result.user_id, auth_result.session_id);
    
    // 测试安全维护
    security_manager.PerformSecurityMaintenance();
}

// 性能测试
TEST_F(SecurityTest, PerformanceTest) {
    SecurityManager security_manager(config_);
    ASSERT_TRUE(security_manager.Initialize());
    
    const int num_operations = 1000;
    
    // 测试加密性能
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_operations; ++i) {
        std::string data = "测试数据" + std::to_string(i);
        std::string encrypted_data;
        std::string decrypted_data;
        
        ASSERT_TRUE(security_manager.EncryptSensitiveData(data, encrypted_data));
        ASSERT_TRUE(security_manager.DecryptSensitiveData(encrypted_data, decrypted_data));
        ASSERT_EQ(data, decrypted_data);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "加密/解密 " << num_operations << " 次操作耗时: " << duration.count() << " ms" << std::endl;
    std::cout << "平均每次操作耗时: " << (double)duration.count() / num_operations << " ms" << std::endl;
    
    // 性能要求：每次加密/解密操作应在10ms内完成
    ASSERT_LT((double)duration.count() / num_operations, 10.0);
}

// 并发测试
TEST_F(SecurityTest, ConcurrencyTest) {
    SecurityManager security_manager(config_);
    ASSERT_TRUE(security_manager.Initialize());
    
    const int num_threads = 10;
    const int operations_per_thread = 100;
    
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    std::atomic<int> failure_count{0};
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&, t]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                std::string data = "线程" + std::to_string(t) + "数据" + std::to_string(i);
                std::string encrypted_data;
                std::string decrypted_data;
                
                if (security_manager.EncryptSensitiveData(data, encrypted_data) &&
                    security_manager.DecryptSensitiveData(encrypted_data, decrypted_data) &&
                    data == decrypted_data) {
                    success_count++;
                } else {
                    failure_count++;
                }
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    int expected_operations = num_threads * operations_per_thread;
    ASSERT_EQ(success_count.load(), expected_operations);
    ASSERT_EQ(failure_count.load(), 0);
    
    std::cout << "并发测试完成: " << success_count.load() << " 次成功操作" << std::endl;
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}