#pragma once

#include "financial_data_sdk.h"
#include <functional>
#include <string>
#include <vector>
#include <thread>
#include <mutex>
#include <atomic>
#include <stdexcept>
#include <cmath>

namespace financial_data {
namespace sdk {
namespace test {

// Simple test framework
class AssertionFailure : public std::runtime_error {
public:
    explicit AssertionFailure(const std::string& message) : std::runtime_error(message) {}
};

class TestFramework {
public:
    static TestFramework& Instance();
    
    void RunTest(const std::string& test_name, std::function<void()> test_func);
    void PrintSummary();
    bool AllTestsPassed() const;
    void Reset();

private:
    int total_tests_ = 0;
    int passed_tests_ = 0;
    int failed_tests_ = 0;
};

// Test macros
#define RUN_TEST(test_func) \
    TestFramework::Instance().RunTest(#test_func, test_func)

// Assertion functions
void Assert(bool condition, const std::string& message = "Assertion failed");
void AssertEqual(const std::string& expected, const std::string& actual, const std::string& message = "Values not equal");
void AssertEqual(double expected, double actual, double tolerance = 1e-9, const std::string& message = "Values not equal");
void AssertEqual(int64_t expected, int64_t actual, const std::string& message = "Values not equal");
void AssertNotNull(const void* ptr, const std::string& message = "Pointer is null");
void AssertThrows(std::function<void()> func, const std::string& message = "Expected exception not thrown");

// Test data generators
StandardTick CreateTestTick(const std::string& symbol = "TEST", double price = 100.0, uint64_t volume = 1000);
Level2Data CreateTestLevel2(const std::string& symbol = "TEST", int num_levels = 5);

// Mock server for testing
class MockServer {
public:
    explicit MockServer(int port = 50051);
    ~MockServer();
    
    bool Start();
    void Stop();
    bool IsRunning() const;
    
    // Configure mock responses
    void SetTickResponse(const std::vector<StandardTick>& ticks);
    void SetErrorResponse(const std::string& error_message);
    
    // Get configured responses
    std::vector<StandardTick> GetTickResponses() const;
    std::string GetErrorMessage() const;

private:
    int port_;
    std::atomic<bool> running_;
    std::thread server_thread_;
    
    mutable std::mutex mutex_;
    std::vector<StandardTick> tick_responses_;
    std::string error_message_;
};

// Performance measurement utilities
class PerformanceTimer {
public:
    void Start() {
        start_time_ = std::chrono::high_resolution_clock::now();
    }
    
    std::chrono::microseconds Stop() {
        auto end_time = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time_);
    }

private:
    std::chrono::high_resolution_clock::time_point start_time_;
};

} // namespace test
} // namespace sdk
} // namespace financial_data