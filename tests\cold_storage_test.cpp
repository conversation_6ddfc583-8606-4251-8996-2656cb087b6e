#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "../src/storage/cold_storage.hpp"
#include "../src/storage/lifecycle_manager.hpp"
#include "../src/storage/archive_interface.hpp"
#include <chrono>
#include <thread>
#include <filesystem>

using namespace financial_data::storage;
using namespace testing;

class ColdStorageTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试配置
        config_.minio_endpoint = "localhost:9000";
        config_.minio_access_key = "admin";
        config_.minio_secret_key = "admin123456";
        config_.minio_bucket = "test-bucket";
        config_.minio_secure = false;
        
        config_.s3_region = "us-east-1";
        config_.s3_access_key = "test-access-key";
        config_.s3_secret_key = "test-secret-key";
        config_.s3_bucket = "test-s3-bucket";
        
        config_.archive_threshold_days = 730;
        config_.compression_level = 9;
        config_.batch_size = 10000;
        
        cold_storage_ = std::make_shared<ColdDataStorage>(config_);
        
        // 创建测试数据目录
        std::filesystem::create_directories("test_data");
        std::filesystem::create_directories("test_metadata");
    }
    
    void TearDown() override {
        // 清理测试数据
        std::filesystem::remove_all("test_data");
        std::filesystem::remove_all("test_metadata");
        std::filesystem::remove_all("metadata");
        std::filesystem::remove_all("bulk_query_results");
    }
    
    TickDataBatch CreateTestBatch(size_t size = 1000) {
        TickDataBatch batch;
        batch.reserve(size);
        
        auto base_time = std::chrono::system_clock::now();
        auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            base_time.time_since_epoch()).count();
        
        for (size_t i = 0; i < size; ++i) {
            batch.timestamps.push_back(base_timestamp + i * 1000000); // 每毫秒一条
            batch.symbols.push_back("CU2409");
            batch.exchanges.push_back("SHFE");
            batch.last_prices.push_back(75000.0 + (i % 1000));
            batch.volumes.push_back(100 + (i % 500));
            batch.turnovers.push_back(batch.last_prices.back() * batch.volumes.back());
            batch.open_interests.push_back(50000 + (i % 10000));
            batch.sequences.push_back(static_cast<uint32_t>(i));
            
            // 简化的买卖盘数据
            batch.bid_prices.push_back({75000.0, 74990.0, 74980.0, 74970.0, 74960.0});
            batch.bid_volumes.push_back({10, 20, 30, 40, 50});
            batch.ask_prices.push_back({75010.0, 75020.0, 75030.0, 75040.0, 75050.0});
            batch.ask_volumes.push_back({15, 25, 35, 45, 55});
        }
        
        return batch;
    }
    
    ColdStorageConfig config_;
    std::shared_ptr<ColdDataStorage> cold_storage_;
};

// 测试冷存储初始化
TEST_F(ColdStorageTest, InitializationTest) {
    // 注意：这个测试需要实际的MinIO服务运行
    // 在CI环境中可能需要跳过或使用mock
    GTEST_SKIP() << "Skipping test that requires MinIO service";
    
    bool result = cold_storage_->Initialize();
    EXPECT_TRUE(result);
}

// 测试数据归档功能
TEST_F(ColdStorageTest, ArchiveDataTest) {
    GTEST_SKIP() << "Skipping test that requires MinIO service";
    
    // 初始化存储
    ASSERT_TRUE(cold_storage_->Initialize());
    
    // 创建测试数据
    auto batch = CreateTestBatch(1000);
    auto date = std::chrono::system_clock::now();
    
    // 归档数据
    auto future = cold_storage_->ArchiveData(batch, "CU2409", "SHFE", date);
    bool result = future.get();
    
    EXPECT_TRUE(result);
}

// 测试数据检索功能
TEST_F(ColdStorageTest, RetrieveDataTest) {
    GTEST_SKIP() << "Skipping test that requires MinIO service";
    
    // 初始化存储
    ASSERT_TRUE(cold_storage_->Initialize());
    
    // 先归档一些数据
    auto batch = CreateTestBatch(1000);
    auto date = std::chrono::system_clock::now();
    
    auto archive_future = cold_storage_->ArchiveData(batch, "CU2409", "SHFE", date);
    ASSERT_TRUE(archive_future.get());
    
    // 检索数据
    auto start_time = date - std::chrono::hours(1);
    auto end_time = date + std::chrono::hours(1);
    
    auto retrieve_future = cold_storage_->RetrieveData("CU2409", "SHFE", start_time, end_time);
    auto retrieved_batch = retrieve_future.get();
    
    EXPECT_GT(retrieved_batch.size(), 0);
    EXPECT_EQ(retrieved_batch.symbols[0], "CU2409");
    EXPECT_EQ(retrieved_batch.exchanges[0], "SHFE");
}

// 测试压缩比
TEST_F(ColdStorageTest, CompressionRatioTest) {
    auto batch = CreateTestBatch(10000);
    
    // 估算原始数据大小
    size_t original_size = batch.size() * 200; // 假设每条记录200字节
    
    // 模拟压缩后大小（实际应该通过Parquet文件大小计算）
    size_t compressed_size = original_size / 8; // 假设8:1压缩比
    
    double compression_ratio = static_cast<double>(original_size) / compressed_size;
    
    EXPECT_GE(compression_ratio, 8.0); // 要求至少8:1的压缩比
}

// 测试生命周期管理器
class LifecycleManagerTest : public ColdStorageTest {
protected:
    void SetUp() override {
        ColdStorageTest::SetUp();
        
        MigrationPolicy policy;
        policy.warm_to_cold_days = 730;
        policy.retention_years = 10;
        policy.cron_schedule = "0 2 * * *";
        policy.enable_s3_backup = true;
        policy.enable_compression = true;
        policy.compression_level = 9;
        policy.batch_size = 100000;
        
        lifecycle_manager_ = std::make_unique<LifecycleManager>(cold_storage_);
        lifecycle_manager_->Initialize(policy);
    }
    
    std::unique_ptr<LifecycleManager> lifecycle_manager_;
};

TEST_F(LifecycleManagerTest, InitializationTest) {
    EXPECT_TRUE(lifecycle_manager_ != nullptr);
    EXPECT_FALSE(lifecycle_manager_->IsRunning());
}

TEST_F(LifecycleManagerTest, TaskSchedulingTest) {
    auto start_date = std::chrono::system_clock::now() - std::chrono::hours(24 * 800); // 800天前
    auto end_date = start_date + std::chrono::hours(24);
    
    std::string task_id = lifecycle_manager_->ScheduleMigrationTask(
        "CU2409", "SHFE", start_date, end_date);
    
    EXPECT_FALSE(task_id.empty());
    
    auto task = lifecycle_manager_->GetMigrationTask(task_id);
    EXPECT_EQ(task.task_id, task_id);
    EXPECT_EQ(task.symbol, "CU2409");
    EXPECT_EQ(task.exchange, "SHFE");
    EXPECT_EQ(task.status, MigrationTask::PENDING);
}

TEST_F(LifecycleManagerTest, AutomaticMigrationTest) {
    lifecycle_manager_->StartAutomaticMigration();
    EXPECT_TRUE(lifecycle_manager_->IsRunning());
    
    // 等待一小段时间让工作线程启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    lifecycle_manager_->StopAutomaticMigration();
    EXPECT_FALSE(lifecycle_manager_->IsRunning());
}

TEST_F(LifecycleManagerTest, HealthCheckTest) {
    auto health = lifecycle_manager_->CheckHealth();
    EXPECT_TRUE(health.is_healthy);
    EXPECT_FALSE(health.status_message.empty());
}

// 测试归档接口
class ArchiveInterfaceTest : public ColdStorageTest {
protected:
    void SetUp() override {
        ColdStorageTest::SetUp();
        archive_interface_ = std::make_unique<ArchiveInterface>(cold_storage_);
    }
    
    std::unique_ptr<ArchiveInterface> archive_interface_;
};

TEST_F(ArchiveInterfaceTest, QueryConditionTest) {
    QueryCondition condition;
    condition.symbols = {"CU2409", "AL2409"};
    condition.exchanges = {"SHFE"};
    condition.start_time = std::chrono::system_clock::now() - std::chrono::hours(24);
    condition.end_time = std::chrono::system_clock::now();
    condition.min_price = 70000.0;
    condition.max_price = 80000.0;
    condition.limit = 1000;
    condition.sort_field = QueryCondition::TIMESTAMP;
    condition.sort_order = QueryCondition::ASC;
    
    // 测试查询条件的有效性
    EXPECT_EQ(condition.symbols.size(), 2);
    EXPECT_EQ(condition.exchanges.size(), 1);
    EXPECT_TRUE(condition.min_price.has_value());
    EXPECT_TRUE(condition.max_price.has_value());
    EXPECT_EQ(condition.limit, 1000);
}

TEST_F(ArchiveInterfaceTest, BulkQueryRequestTest) {
    BulkQueryRequest request;
    request.request_id = "test_bulk_query_001";
    request.output_format = "parquet";
    request.compression = "zstd";
    request.async_mode = true;
    
    QueryCondition condition1;
    condition1.symbols = {"CU2409"};
    condition1.exchanges = {"SHFE"};
    condition1.start_time = std::chrono::system_clock::now() - std::chrono::hours(24);
    condition1.end_time = std::chrono::system_clock::now();
    
    QueryCondition condition2;
    condition2.symbols = {"AL2409"};
    condition2.exchanges = {"SHFE"};
    condition2.start_time = std::chrono::system_clock::now() - std::chrono::hours(24);
    condition2.end_time = std::chrono::system_clock::now();
    
    request.conditions = {condition1, condition2};
    
    EXPECT_EQ(request.conditions.size(), 2);
    EXPECT_EQ(request.output_format, "parquet");
    EXPECT_EQ(request.compression, "zstd");
}

// 测试数据转换器
TEST_F(ColdStorageTest, DataConverterTest) {
    auto batch = CreateTestBatch(100);
    
    // 测试CSV转换
    std::string csv_file = "test_data/test_output.csv";
    bool csv_result = DataConverter::ConvertToCSV(batch, csv_file);
    EXPECT_TRUE(csv_result);
    EXPECT_TRUE(std::filesystem::exists(csv_file));
    
    // 检查CSV文件内容
    std::ifstream csv_stream(csv_file);
    std::string first_line;
    std::getline(csv_stream, first_line);
    EXPECT_TRUE(first_line.find("timestamp") != std::string::npos);
    EXPECT_TRUE(first_line.find("symbol") != std::string::npos);
    
    // 测试JSON转换
    std::string json_file = "test_data/test_output.json";
    bool json_result = DataConverter::ConvertToJSON(batch, json_file);
    EXPECT_TRUE(json_result);
    EXPECT_TRUE(std::filesystem::exists(json_file));
    
    // 检查JSON文件内容
    std::ifstream json_stream(json_file);
    std::string json_content((std::istreambuf_iterator<char>(json_stream)),
                            std::istreambuf_iterator<char>());
    EXPECT_TRUE(json_content.find("timestamp") != std::string::npos);
    EXPECT_TRUE(json_content.find("CU2409") != std::string::npos);
}

// 性能测试
TEST_F(ColdStorageTest, PerformanceTest) {
    auto large_batch = CreateTestBatch(100000); // 10万条记录
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 测试CSV转换性能
    std::string csv_file = "test_data/performance_test.csv";
    bool result = DataConverter::ConvertToCSV(large_batch, csv_file);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_TRUE(result);
    EXPECT_LT(duration.count(), 5000); // 应该在5秒内完成
    
    // 检查文件大小
    auto file_size = std::filesystem::file_size(csv_file);
    EXPECT_GT(file_size, 0);
    
    std::cout << "Performance test results:" << std::endl;
    std::cout << "  Records: " << large_batch.size() << std::endl;
    std::cout << "  Duration: " << duration.count() << " ms" << std::endl;
    std::cout << "  File size: " << file_size << " bytes" << std::endl;
    std::cout << "  Throughput: " << (large_batch.size() * 1000 / duration.count()) << " records/sec" << std::endl;
}

// 并发测试
TEST_F(ColdStorageTest, ConcurrencyTest) {
    const int num_threads = 4;
    const int records_per_thread = 1000;
    
    std::vector<std::thread> threads;
    std::vector<bool> results(num_threads);
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, records_per_thread, &results]() {
            auto batch = CreateTestBatch(records_per_thread);
            std::string file_path = "test_data/concurrent_test_" + std::to_string(i) + ".csv";
            results[i] = DataConverter::ConvertToCSV(batch, file_path);
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // 检查所有线程都成功完成
    for (bool result : results) {
        EXPECT_TRUE(result);
    }
    
    // 检查所有文件都被创建
    for (int i = 0; i < num_threads; ++i) {
        std::string file_path = "test_data/concurrent_test_" + std::to_string(i) + ".csv";
        EXPECT_TRUE(std::filesystem::exists(file_path));
    }
    
    std::cout << "Concurrency test results:" << std::endl;
    std::cout << "  Threads: " << num_threads << std::endl;
    std::cout << "  Records per thread: " << records_per_thread << std::endl;
    std::cout << "  Total duration: " << duration.count() << " ms" << std::endl;
    std::cout << "  Total throughput: " << (num_threads * records_per_thread * 1000 / duration.count()) << " records/sec" << std::endl;
}

// 错误处理测试
TEST_F(ColdStorageTest, ErrorHandlingTest) {
    auto batch = CreateTestBatch(100);
    
    // 测试无效路径
    std::string invalid_path = "/invalid/path/test.csv";
    bool result = DataConverter::ConvertToCSV(batch, invalid_path);
    EXPECT_FALSE(result);
    
    // 测试空批次
    TickDataBatch empty_batch;
    std::string valid_path = "test_data/empty_test.csv";
    bool empty_result = DataConverter::ConvertToCSV(empty_batch, valid_path);
    EXPECT_TRUE(empty_result); // 空文件也应该成功创建
    
    if (std::filesystem::exists(valid_path)) {
        auto file_size = std::filesystem::file_size(valid_path);
        EXPECT_GT(file_size, 0); // 至少应该有CSV头部
    }
}

// 数据完整性测试
TEST_F(ColdStorageTest, DataIntegrityTest) {
    auto original_batch = CreateTestBatch(1000);
    
    // 转换为CSV
    std::string csv_file = "test_data/integrity_test.csv";
    bool result = DataConverter::ConvertToCSV(original_batch, csv_file);
    ASSERT_TRUE(result);
    
    // 读取CSV文件并验证数据
    std::ifstream file(csv_file);
    std::string line;
    
    // 跳过头部
    std::getline(file, line);
    
    size_t line_count = 0;
    while (std::getline(file, line)) {
        line_count++;
        
        // 简单验证：检查行是否包含预期的符号
        EXPECT_TRUE(line.find("CU2409") != std::string::npos);
        EXPECT_TRUE(line.find("SHFE") != std::string::npos);
    }
    
    EXPECT_EQ(line_count, original_batch.size());
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}