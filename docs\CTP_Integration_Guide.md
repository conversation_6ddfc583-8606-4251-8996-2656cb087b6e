# CTP期货行情采集集成指南

## 概述

本文档描述了如何将CTP (Comprehensive Transaction Platform) 期货行情API集成到金融数据服务系统中。

## 集成状态

✅ **已完成的功能**：
- CTP SDK集成 (v6.7.9_P1_20250319)
- 基础API包装器
- 连接测试验证
- 编译配置优化

🔄 **进行中的功能**：
- 完整的行情数据采集器
- 断线重连机制
- 数据标准化处理

## 目录结构

```
third_party/ctp/traderapi_se_windows64/
├── ThostFtdcMdApi.h              # 行情API头文件
├── ThostFtdcTraderApi.h          # 交易API头文件
├── ThostFtdcUserApiStruct.h      # 数据结构定义
├── ThostFtdcUserApiDataType.h    # 数据类型定义
├── thostmduserapi_se.lib         # 行情API静态库
├── thostmduserapi_se.dll         # 行情API动态库
├── thosttraderapi_se.lib         # 交易API静态库
└── thosttraderapi_se.dll         # 交易API动态库

src/collectors/
├── ctp_headers.h                 # CTP头文件包装器
├── ctp_api_wrapper.h             # CTP API包装类
├── ctp_api_wrapper.cpp           # CTP API包装实现
├── ctp_collector.h               # CTP数据采集器
└── ctp_collector.cpp             # CTP数据采集器实现
```

## 编译配置

### CMake配置要点

1. **字符集处理**：
```cmake
# 禁用C4828警告（字符集相关）
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4828")
# 禁用C4996警告（不安全函数）
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4996")
```

2. **库文件链接**：
```cmake
# 明确指定CTP库路径
set(CTP_MD_LIB "${CTP_LIB_DIR}/thostmduserapi_se.lib")
target_link_libraries(target_name ${CTP_MD_LIB})
```

3. **DLL复制**：
```cmake
# 自动复制DLL到输出目录
add_custom_command(TARGET target_name POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CTP_API_ROOT}/thostmduserapi_se.dll"
    $<TARGET_FILE_DIR:target_name>)
```

## 基础使用示例

### 1. 简单连接测试

```cpp
#include "collectors/ctp_headers.h"
#include <iostream>

int main() {
    // 创建flow目录
    system("mkdir flow 2>nul");
    
    // 创建CTP API实例
    CThostFtdcMdApi* api = CThostFtdcMdApi::CreateFtdcMdApi("./flow/");
    if (api) {
        std::cout << "CTP API Version: " << api->GetApiVersion() << std::endl;
        api->Release();
    }
    return 0;
}
```

### 2. 使用CTP包装器

```cpp
#include "collectors/ctp_api_wrapper.h"

int main() {
    financial_data::CTPApiWrapper wrapper;
    
    // 初始化
    if (wrapper.Initialize("./flow/")) {
        std::cout << "CTP wrapper initialized successfully" << std::endl;
    }
    
    // 设置回调
    wrapper.SetConnectedCallback([]() {
        std::cout << "Connected to CTP server" << std::endl;
    });
    
    return 0;
}
```

### 3. 完整的数据采集器

```cpp
#include "collectors/ctp_collector.h"

int main() {
    using namespace financial_data;
    
    // 配置CTP连接
    CTPConfig config;
    config.front_address = "tcp://***************:10131";
    config.broker_id = "9999";
    config.user_id = "your_user_id";
    config.password = "your_password";
    config.flow_path = "./flow/";
    
    // 创建采集器
    auto collector = std::make_unique<CTPMarketDataCollector>();
    
    // 设置数据回调
    collector->SetDataCallback([](const MarketDataWrapper& data) {
        std::cout << "Received data for: " << data.GetSymbol() << std::endl;
    });
    
    // 初始化并连接
    if (collector->Initialize(config)) {
        collector->Start();
        collector->Connect();
        
        // 订阅合约
        collector->Subscribe({"CU2409", "AL2409"});
        
        // 运行一段时间
        std::this_thread::sleep_for(std::chrono::seconds(10));
        
        collector->Stop();
    }
    
    return 0;
}
```

## 配置文件示例

### CTP配置文件 (config/ctp_config.json)

```json
{
  "ctp": {
    "front_address": "tcp://***************:10131",
    "broker_id": "9999",
    "user_id": "your_user_id",
    "password": "your_password",
    "flow_path": "./flow/",
    "heartbeat_interval": 30,
    "reconnect_interval": 5,
    "max_reconnect_attempts": 10,
    "enable_level2": true
  }
}
```

## 构建和测试

### 构建命令

```bash
# Windows
.\build_simple_connection_test.bat

# 或者手动构建
mkdir build && cd build
cmake -DCMAKE_TOOLCHAIN_FILE=%VCPKG_ROOT%/scripts/buildsystems/vcpkg.cmake ..
cmake --build . --config Release
```

### 测试验证

1. **连接测试**：验证CTP API基础功能
2. **数据采集测试**：验证行情数据接收
3. **断线重连测试**：验证网络异常处理
4. **性能测试**：验证延迟和吞吐量

## 常见问题

### 1. 编译错误

**问题**：C4828字符集警告
**解决**：在CMakeLists.txt中添加 `/wd4828` 编译选项

**问题**：找不到thosttraderapi_se.lib
**解决**：确保只包含行情API头文件，不包含交易API头文件

### 2. 运行时错误

**问题**：can not open CFlow file
**解决**：确保flow目录存在，或在代码中创建目录

**问题**：DLL加载失败
**解决**：确保thostmduserapi_se.dll在可执行文件目录中

### 3. 连接问题

**问题**：连接超时
**解决**：检查网络连接和前置机地址

**问题**：登录失败
**解决**：验证用户名、密码和经纪商ID

## 性能优化建议

1. **内存管理**：及时释放API实例
2. **线程安全**：使用互斥锁保护共享数据
3. **队列管理**：控制数据队列大小防止内存溢出
4. **日志级别**：生产环境降低日志级别

## 下一步计划

1. 完善数据采集器功能
2. 实现Level-2深度行情支持
3. 添加数据质量检查
4. 集成到主系统架构
5. 性能优化和压力测试

## 技术支持

- CTP API版本：v6.7.9_P1_20250319
- 支持的交易所：上期所、大商所、郑商所、中金所、能源中心
- 开发环境：Visual Studio 2022, CMake 3.15+, vcpkg