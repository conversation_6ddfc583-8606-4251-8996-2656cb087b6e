/**
 * 数据一致性验证工具 - C++版本
 * 测试数据去重和冲突解决，验证存储层数据一致性，检查数据完整性和准确性
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <algorithm>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <map>
#include <memory>
#include <set>
#include <sstream>
#include <thread>
#include <vector>
#include <json/json.h>

#include "../../src/storage/unified_data_access.h"
#include "../../src/storage/storage_layer_selector.h"
#include "../../src/data_models.h"

using namespace std::chrono_literals;
using testing::_;
using testing::Return;

class DataConsistencyValidator : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化测试配置
        setupTestConfig();
        
        // 初始化组件
        unified_access_ = std::make_unique<UnifiedDataAccessInterface>(test_config_);
        storage_selector_ = std::make_unique<StorageLayerSelector>(test_config_);
        
        // 清空一致性问题列表
        consistency_issues_.clear();
        
        std::cout << "Data consistency validator initialized" << std::endl;
    }
    
    void TearDown() override {
        // 生成测试报告
        generateConsistencyReport();
        
        std::cout << "Data consistency validation completed" << std::endl;
    }

private:
    void setupTestConfig() {
        test_config_["redis"]["host"] = "localhost";
        test_config_["redis"]["port"] = 6379;
        test_config_["redis"]["db"] = 1;
        
        test_config_["clickhouse"]["host"] = "localhost";
        test_config_["clickhouse"]["port"] = 8123;
        test_config_["clickhouse"]["database"] = "test_market_data";
        
        test_config_["hot_storage_days"] = 7;
        test_config_["warm_storage_days"] = 730;
    }
    
    void generateConsistencyReport() {
        Json::Value report;
        report["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        report["total_issues"] = static_cast<int>(consistency_issues_.size());
        
        Json::Value issues(Json::arrayValue);
        for (const auto& issue : consistency_issues_) {
            Json::Value issue_json;
            issue_json["type"] = issue.type;
            issue_json["symbol"] = issue.symbol;
            issue_json["timestamp"] = static_cast<Json::Int64>(issue.timestamp_ns);
            issue_json["description"] = issue.description;
            issue_json["severity"] = issue.severity;
            issues.append(issue_json);
        }
        report["issues"] = issues;
        
        // 保存报告
        std::ofstream report_file("tests/integration/cpp_consistency_report.json");
        report_file << report;
        
        std::cout << "Consistency report generated with " << consistency_issues_.size() << " issues" << std::endl;
    }

protected:
    struct ConsistencyIssue {
        std::string type;
        std::string symbol;
        int64_t timestamp_ns;
        std::string description;
        std::string severity;
    };
    
    std::unique_ptr<UnifiedDataAccessInterface> unified_access_;
    std::unique_ptr<StorageLayerSelector> storage_selector_;
    std::map<std::string, Json::Value> test_config_;
    std::vector<ConsistencyIssue> consistency_issues_;
};

TEST_F(DataConsistencyValidator, TestDataDeduplication) {
    std::cout << "Testing data deduplication" << std::endl;
    
    std::string test_symbol = "DEDUP_TEST_CPP";
    auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // 创建包含重复数据的测试集
    std::vector<StandardTick> test_data;
    std::set<int64_t> duplicate_timestamps;
    
    // 添加正常数据
    for (int i = 0; i < 100; ++i) {
        StandardTick tick;
        tick.symbol = test_symbol;
        tick.timestamp_ns = base_timestamp + i * 1000000000LL;
        tick.price = 10.0 + i * 0.01;
        tick.volume = 1000 + i;
        tick.bid_price = tick.price - 0.01;
        tick.ask_price = tick.price + 0.01;
        
        test_data.push_back(tick);
    }
    
    // 添加重复数据（相同时间戳，不同价格）
    for (int i = 0; i < 20; i += 2) {
        StandardTick duplicate_tick;
        duplicate_tick.symbol = test_symbol;
        duplicate_tick.timestamp_ns = base_timestamp + i * 1000000000LL; // 重复时间戳
        duplicate_tick.price = 10.0 + i * 0.01 + 0.001; // 略微不同的价格
        duplicate_tick.volume = 1000 + i + 1;
        duplicate_tick.bid_price = duplicate_tick.price - 0.01;
        duplicate_tick.ask_price = duplicate_tick.price + 0.01;
        
        duplicate_timestamps.insert(duplicate_tick.timestamp_ns);
        test_data.push_back(duplicate_tick);
    }
    
    // 存储数据（应该触发去重）
    auto store_result = unified_access_->StoreBatchAsync(test_data).get();
    EXPECT_TRUE(store_result.success) << "Failed to store deduplication test data";
    
    // 查询存储的数据
    QueryRequest query_request;
    query_request.symbol = test_symbol;
    query_request.data_type = "tick";
    query_request.start_timestamp_ns = base_timestamp;
    query_request.end_timestamp_ns = base_timestamp + 200 * 1000000000LL;
    query_request.limit = 1000;
    
    auto query_result = unified_access_->QueryData(query_request).get();
    EXPECT_TRUE(query_result.success) << "Failed to query stored data";
    
    // 验证去重效果
    std::set<int64_t> stored_timestamps;
    for (const auto& tick : query_result.ticks) {
        stored_timestamps.insert(tick.timestamp_ns);
    }
    
    int expected_unique_count = 100; // 原始数据数量
    int actual_unique_count = static_cast<int>(stored_timestamps.size());
    
    EXPECT_EQ(actual_unique_count, expected_unique_count) 
        << "Deduplication failed: expected " << expected_unique_count 
        << " unique records, got " << actual_unique_count;
    
    // 检查重复时间戳是否被正确处理
    for (int64_t dup_timestamp : duplicate_timestamps) {
        int matching_count = 0;
        for (const auto& tick : query_result.ticks) {
            if (tick.timestamp_ns == dup_timestamp) {
                matching_count++;
            }
        }
        
        if (matching_count != 1) {
            consistency_issues_.push_back({
                "deduplication_failure",
                test_symbol,
                dup_timestamp,
                "Expected 1 record, found " + std::to_string(matching_count),
                "high"
            });
        }
        
        EXPECT_EQ(matching_count, 1) 
            << "Duplicate timestamp " << dup_timestamp 
            << " should have exactly 1 record, found " << matching_count;
    }
    
    std::cout << "Deduplication test completed: " 
              << actual_unique_count << " unique records from " 
              << test_data.size() << " input records" << std::endl;
}

TEST_F(DataConsistencyValidator, TestConflictResolution) {
    std::cout << "Testing conflict resolution" << std::endl;
    
    std::string test_symbol = "CONFLICT_TEST_CPP";
    auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // 模拟来自不同数据源的冲突数据
    std::vector<StandardTick> pytdx_data;
    std::vector<StandardTick> ctp_data;
    
    // pytdx数据
    StandardTick pytdx_tick1;
    pytdx_tick1.symbol = test_symbol;
    pytdx_tick1.timestamp_ns = base_timestamp;
    pytdx_tick1.price = 10.00;
    pytdx_tick1.volume = 1000;
    pytdx_tick1.data_source = "pytdx";
    pytdx_data.push_back(pytdx_tick1);
    
    StandardTick pytdx_tick2;
    pytdx_tick2.symbol = test_symbol;
    pytdx_tick2.timestamp_ns = base_timestamp + 1000000000LL;
    pytdx_tick2.price = 10.01;
    pytdx_tick2.volume = 1100;
    pytdx_tick2.data_source = "pytdx";
    pytdx_data.push_back(pytdx_tick2);
    
    // CTP数据（包含冲突）
    StandardTick ctp_tick1;
    ctp_tick1.symbol = test_symbol;
    ctp_tick1.timestamp_ns = base_timestamp; // 相同时间戳
    ctp_tick1.price = 10.005; // 略微不同的价格
    ctp_tick1.volume = 1050;  // 略微不同的成交量
    ctp_tick1.data_source = "ctp";
    ctp_data.push_back(ctp_tick1);
    
    StandardTick ctp_tick2;
    ctp_tick2.symbol = test_symbol;
    ctp_tick2.timestamp_ns = base_timestamp + 2000000000LL;
    ctp_tick2.price = 10.02;
    ctp_tick2.volume = 1200;
    ctp_tick2.data_source = "ctp";
    ctp_data.push_back(ctp_tick2);
    
    // 先存储pytdx数据
    auto pytdx_result = unified_access_->StoreBatchAsync(pytdx_data).get();
    EXPECT_TRUE(pytdx_result.success) << "Failed to store pytdx data";
    
    // 再存储CTP数据（应该触发冲突解决）
    auto ctp_result = unified_access_->StoreBatchAsync(ctp_data).get();
    EXPECT_TRUE(ctp_result.success) << "Failed to store CTP data";
    
    // 查询最终存储的数据
    QueryRequest query_request;
    query_request.symbol = test_symbol;
    query_request.data_type = "tick";
    query_request.start_timestamp_ns = base_timestamp;
    query_request.end_timestamp_ns = base_timestamp + 3000000000LL;
    query_request.limit = 100;
    
    auto query_result = unified_access_->QueryData(query_request).get();
    EXPECT_TRUE(query_result.success) << "Failed to query conflict resolution result";
    
    // 验证冲突解决结果
    bool conflict_resolution_correct = true;
    
    // 检查重叠时间戳的数据
    auto overlapping_it = std::find_if(query_result.ticks.begin(), query_result.ticks.end(),
        [base_timestamp](const StandardTick& tick) {
            return tick.timestamp_ns == base_timestamp;
        });
    
    if (overlapping_it != query_result.ticks.end()) {
        // 根据配置的优先级策略验证（假设CTP优先）
        double expected_price = 10.005; // CTP的价格
        double actual_price = overlapping_it->price;
        
        if (std::abs(actual_price - expected_price) > 0.001) {
            conflict_resolution_correct = false;
            consistency_issues_.push_back({
                "conflict_resolution_failure",
                test_symbol,
                base_timestamp,
                "Expected price " + std::to_string(expected_price) + 
                ", got " + std::to_string(actual_price),
                "high"
            });
        }
    }
    
    EXPECT_TRUE(conflict_resolution_correct) << "Conflict resolution failed";
    
    std::cout << "Conflict resolution test completed: " 
              << (conflict_resolution_correct ? "PASSED" : "FAILED") << std::endl;
}

TEST_F(DataConsistencyValidator, TestStorageLayerConsistency) {
    std::cout << "Testing storage layer consistency" << std::endl;
    
    std::string test_symbol = "STORAGE_CONSISTENCY_TEST_CPP";
    
    auto now = std::chrono::system_clock::now();
    auto now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        now.time_since_epoch()).count();
    
    // 测试不同时间范围的数据路由到不同存储层
    struct TestCase {
        std::string name;
        int64_t timestamp_ns;
        std::string expected_layer;
    };
    
    std::vector<TestCase> test_cases = {
        {"hot_storage", now_ns - 24 * 3600 * 1000000000LL, "hot"},      // 1天前
        {"warm_storage", now_ns - 30 * 24 * 3600 * 1000000000LL, "warm"}, // 30天前
        {"cold_storage", now_ns - 800 * 24 * 3600 * 1000000000LL, "cold"}  // 800天前
    };
    
    std::vector<bool> case_results;
    
    for (const auto& test_case : test_cases) {
        // 创建测试数据
        StandardTick test_tick;
        test_tick.symbol = test_symbol;
        test_tick.timestamp_ns = test_case.timestamp_ns;
        test_tick.price = 10.0;
        test_tick.volume = 1000;
        
        // 存储数据
        auto store_result = unified_access_->StoreBatchAsync({test_tick}).get();
        EXPECT_TRUE(store_result.success) << "Failed to store " << test_case.name << " data";
        
        // 验证数据被存储到正确的层
        auto selected_layer = storage_selector_->SelectStorageLayer(test_case.timestamp_ns);
        
        bool case_success = (selected_layer == test_case.expected_layer);
        case_results.push_back(case_success);
        
        EXPECT_EQ(selected_layer, test_case.expected_layer)
            << "Storage layer routing error for " << test_case.name
            << ": expected " << test_case.expected_layer
            << ", got " << selected_layer;
        
        if (!case_success) {
            consistency_issues_.push_back({
                "storage_layer_inconsistency",
                test_symbol,
                test_case.timestamp_ns,
                "Expected " + test_case.expected_layer + ", got " + selected_layer,
                "medium"
            });
        }
        
        // 查询数据验证存储
        QueryRequest query_request;
        query_request.symbol = test_symbol;
        query_request.data_type = "tick";
        query_request.start_timestamp_ns = test_case.timestamp_ns;
        query_request.end_timestamp_ns = test_case.timestamp_ns + 1;
        query_request.limit = 1;
        
        auto query_result = unified_access_->QueryData(query_request).get();
        EXPECT_TRUE(query_result.success) << "Failed to query " << test_case.name << " data";
        EXPECT_GT(query_result.ticks.size(), 0) << "No data found for " << test_case.name;
    }
    
    bool overall_success = std::all_of(case_results.begin(), case_results.end(),
        [](bool result) { return result; });
    
    std::cout << "Storage layer consistency test completed: " 
              << std::count(case_results.begin(), case_results.end(), true) 
              << "/" << case_results.size() << " cases passed" << std::endl;
}

TEST_F(DataConsistencyValidator, TestDataIntegrity) {
    std::cout << "Testing data integrity" << std::endl;
    
    std::string test_symbol = "INTEGRITY_TEST_CPP";
    auto base_timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // 创建完整的测试数据集
    std::vector<StandardTick> original_data;
    for (int i = 0; i < 100; ++i) {
        StandardTick tick;
        tick.symbol = test_symbol;
        tick.timestamp_ns = base_timestamp + i * 1000000000LL;
        tick.price = 10.0 + i * 0.01;
        tick.volume = 1000 + i;
        tick.bid_price = tick.price - 0.01;
        tick.ask_price = tick.price + 0.01;
        
        original_data.push_back(tick);
    }
    
    // 存储数据
    auto store_result = unified_access_->StoreBatchAsync(original_data).get();
    EXPECT_TRUE(store_result.success) << "Failed to store integrity test data";
    
    // 查询所有数据
    QueryRequest query_request;
    query_request.symbol = test_symbol;
    query_request.data_type = "tick";
    query_request.start_timestamp_ns = base_timestamp;
    query_request.end_timestamp_ns = base_timestamp + 200 * 1000000000LL;
    query_request.limit = 1000;
    
    auto query_result = unified_access_->QueryData(query_request).get();
    EXPECT_TRUE(query_result.success) << "Failed to query integrity test data";
    
    // 完整性检查
    std::vector<std::string> integrity_issues;
    
    // 1. 数据数量检查
    size_t expected_count = original_data.size();
    size_t actual_count = query_result.ticks.size();
    
    EXPECT_EQ(actual_count, expected_count) 
        << "Data count mismatch: expected " << expected_count 
        << ", got " << actual_count;
    
    if (actual_count != expected_count) {
        integrity_issues.push_back("Data count mismatch");
    }
    
    // 2. 时间序列连续性检查
    std::vector<int64_t> stored_timestamps;
    for (const auto& tick : query_result.ticks) {
        stored_timestamps.push_back(tick.timestamp_ns);
    }
    std::sort(stored_timestamps.begin(), stored_timestamps.end());
    
    for (size_t i = 1; i < stored_timestamps.size(); ++i) {
        int64_t expected_gap = 1000000000LL; // 1秒
        int64_t actual_gap = stored_timestamps[i] - stored_timestamps[i-1];
        
        if (actual_gap != expected_gap) {
            integrity_issues.push_back("Time gap inconsistency at index " + std::to_string(i));
            consistency_issues_.push_back({
                "time_gap_inconsistency",
                test_symbol,
                stored_timestamps[i],
                "Expected gap " + std::to_string(expected_gap) + 
                ", got " + std::to_string(actual_gap),
                "medium"
            });
        }
    }
    
    // 3. 数据值合理性检查
    for (const auto& tick : query_result.ticks) {
        if (tick.price <= 0) {
            integrity_issues.push_back("Invalid price: " + std::to_string(tick.price));
        }
        
        if (tick.volume <= 0) {
            integrity_issues.push_back("Invalid volume: " + std::to_string(tick.volume));
        }
        
        // 检查买卖价格的合理性
        if (tick.bid_price >= tick.ask_price) {
            integrity_issues.push_back("Invalid bid/ask spread");
        }
    }
    
    // 4. 关键字段匹配检查
    bool key_fields_match = true;
    for (size_t i = 0; i < std::min(original_data.size(), query_result.ticks.size()); ++i) {
        const auto& original = original_data[i];
        const auto& stored = query_result.ticks[i];
        
        if (original.timestamp_ns != stored.timestamp_ns ||
            std::abs(original.price - stored.price) > 0.001) {
            key_fields_match = false;
            break;
        }
    }
    
    EXPECT_TRUE(key_fields_match) << "Key fields do not match between original and stored data";
    EXPECT_TRUE(integrity_issues.empty()) << "Data integrity issues found: " << integrity_issues.size();
    
    std::cout << "Data integrity test completed: " 
              << (integrity_issues.empty() && key_fields_match ? "PASSED" : "FAILED")
              << " (" << integrity_issues.size() << " issues found)" << std::endl;
}

TEST_F(DataConsistencyValidator, TestCrossStorageConsistency) {
    std::cout << "Testing cross-storage consistency" << std::endl;
    
    std::string test_symbol = "CROSS_STORAGE_TEST_CPP";
    
    auto now = std::chrono::system_clock::now();
    auto now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        now.time_since_epoch()).count();
    
    // 创建跨越多个存储层的数据
    std::vector<StandardTick> hot_data;
    std::vector<StandardTick> warm_data;
    
    // 热存储数据（最近1天）
    for (int i = 0; i < 10; ++i) {
        StandardTick tick;
        tick.symbol = test_symbol;
        tick.timestamp_ns = now_ns - i * 3600 * 1000000000LL; // 每小时一个数据点
        tick.price = 10.0 + i * 0.01;
        tick.volume = 1000 + i;
        
        hot_data.push_back(tick);
    }
    
    // 温存储数据（30天前）
    int64_t warm_base_timestamp = now_ns - 30 * 24 * 3600 * 1000000000LL;
    for (int i = 0; i < 10; ++i) {
        StandardTick tick;
        tick.symbol = test_symbol;
        tick.timestamp_ns = warm_base_timestamp - i * 3600 * 1000000000LL;
        tick.price = 9.0 + i * 0.01;
        tick.volume = 900 + i;
        
        warm_data.push_back(tick);
    }
    
    // 存储数据到不同层
    auto hot_result = unified_access_->StoreBatchAsync(hot_data).get();
    auto warm_result = unified_access_->StoreBatchAsync(warm_data).get();
    
    EXPECT_TRUE(hot_result.success) << "Failed to store hot data";
    EXPECT_TRUE(warm_result.success) << "Failed to store warm data";
    
    // 验证跨存储层查询的一致性
    std::vector<bool> consistency_checks;
    
    // 1. 单层查询一致性
    for (const auto& layer_data : {hot_data, warm_data}) {
        if (layer_data.empty()) continue;
        
        int64_t start_ts = layer_data.back().timestamp_ns; // 最早时间
        int64_t end_ts = layer_data.front().timestamp_ns;  // 最晚时间
        
        QueryRequest query_request;
        query_request.symbol = test_symbol;
        query_request.data_type = "tick";
        query_request.start_timestamp_ns = start_ts;
        query_request.end_timestamp_ns = end_ts + 1;
        query_request.limit = 100;
        
        auto query_result = unified_access_->QueryData(query_request).get();
        
        // 计算在时间范围内的实际数据数量
        int actual_count = 0;
        for (const auto& tick : query_result.ticks) {
            if (tick.timestamp_ns >= start_ts && tick.timestamp_ns <= end_ts) {
                actual_count++;
            }
        }
        
        bool layer_consistent = (actual_count == static_cast<int>(layer_data.size()));
        consistency_checks.push_back(layer_consistent);
        
        EXPECT_TRUE(layer_consistent) 
            << "Layer consistency failed: expected " << layer_data.size() 
            << " records, got " << actual_count;
    }
    
    // 2. 跨层查询一致性
    int64_t all_start_ts = std::min(
        hot_data.empty() ? LLONG_MAX : hot_data.back().timestamp_ns,
        warm_data.empty() ? LLONG_MAX : warm_data.back().timestamp_ns
    );
    int64_t all_end_ts = std::max(
        hot_data.empty() ? LLONG_MIN : hot_data.front().timestamp_ns,
        warm_data.empty() ? LLONG_MIN : warm_data.front().timestamp_ns
    );
    
    QueryRequest cross_query_request;
    cross_query_request.symbol = test_symbol;
    cross_query_request.data_type = "tick";
    cross_query_request.start_timestamp_ns = all_start_ts;
    cross_query_request.end_timestamp_ns = all_end_ts + 1;
    cross_query_request.limit = 1000;
    
    auto cross_query_result = unified_access_->QueryData(cross_query_request).get();
    EXPECT_TRUE(cross_query_result.success) << "Cross-storage query failed";
    
    // 验证跨层数据的时间顺序
    std::vector<int64_t> timestamps;
    for (const auto& tick : cross_query_result.ticks) {
        timestamps.push_back(tick.timestamp_ns);
    }
    
    std::vector<int64_t> sorted_timestamps = timestamps;
    std::sort(sorted_timestamps.begin(), sorted_timestamps.end());
    
    bool time_ordered = (timestamps == sorted_timestamps);
    consistency_checks.push_back(time_ordered);
    
    EXPECT_TRUE(time_ordered) << "Cross-storage data is not time-ordered";
    
    // 验证数据无重复
    std::set<int64_t> unique_timestamps(timestamps.begin(), timestamps.end());
    bool no_duplicates = (unique_timestamps.size() == timestamps.size());
    consistency_checks.push_back(no_duplicates);
    
    EXPECT_TRUE(no_duplicates) << "Duplicate timestamps found in cross-storage query";
    
    bool overall_success = std::all_of(consistency_checks.begin(), consistency_checks.end(),
        [](bool check) { return check; });
    
    std::cout << "Cross-storage consistency test completed: " 
              << (overall_success ? "PASSED" : "FAILED")
              << " (" << std::count(consistency_checks.begin(), consistency_checks.end(), true)
              << "/" << consistency_checks.size() << " checks passed)" << std::endl;
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    // 设置测试环境
    setenv("TESTING", "1", 1);
    
    std::cout << "Starting C++ data consistency validation tests" << std::endl;
    
    int result = RUN_ALL_TESTS();
    
    std::cout << "C++ data consistency validation completed" << std::endl;
    
    return result;
}