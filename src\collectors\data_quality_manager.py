"""
数据质量管理系统

提供全面的数据质量控制功能，包括：
- 数据验证器：检查价格、成交量等字段合理性
- 数据去重器：避免重复存储
- 数据完整性校验：确保数据完整性和一致性
- 质量监控和报告：生成数据质量报告
"""

import asyncio
import logging
import time
import hashlib
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Set, Any, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)


@dataclass
class QualityMetrics:
    """数据质量指标"""
    symbol: str
    start_time: datetime
    end_time: datetime
    total_records: int = 0
    valid_records: int = 0
    invalid_records: int = 0
    duplicate_records: int = 0
    missing_records: int = 0
    
    # 价格质量指标
    price_outliers: int = 0
    invalid_ohlc_records: int = 0
    zero_price_records: int = 0
    negative_price_records: int = 0
    
    # 成交量质量指标
    zero_volume_records: int = 0
    negative_volume_records: int = 0
    volume_outliers: int = 0
    
    # 时间序列质量指标
    out_of_order_records: int = 0
    duplicate_timestamps: int = 0
    time_gaps: int = 0
    
    # 完整性指标
    completeness_ratio: float = 0.0
    accuracy_ratio: float = 0.0
    consistency_ratio: float = 0.0
    
    # 质量问题列表
    quality_issues: List[str] = field(default_factory=list)
    
    def calculate_ratios(self):
        """计算质量比例"""
        if self.total_records > 0:
            self.completeness_ratio = (self.total_records - self.missing_records) / self.total_records
            self.accuracy_ratio = self.valid_records / self.total_records
            self.consistency_ratio = (self.total_records - self.duplicate_records - self.out_of_order_records) / self.total_records
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'total_records': self.total_records,
            'valid_records': self.valid_records,
            'invalid_records': self.invalid_records,
            'duplicate_records': self.duplicate_records,
            'missing_records': self.missing_records,
            'price_outliers': self.price_outliers,
            'invalid_ohlc_records': self.invalid_ohlc_records,
            'zero_price_records': self.zero_price_records,
            'negative_price_records': self.negative_price_records,
            'zero_volume_records': self.zero_volume_records,
            'negative_volume_records': self.negative_volume_records,
            'volume_outliers': self.volume_outliers,
            'out_of_order_records': self.out_of_order_records,
            'duplicate_timestamps': self.duplicate_timestamps,
            'time_gaps': self.time_gaps,
            'completeness_ratio': self.completeness_ratio,
            'accuracy_ratio': self.accuracy_ratio,
            'consistency_ratio': self.consistency_ratio,
            'quality_issues': self.quality_issues
        }


@dataclass
class ValidationRule:
    """数据验证规则"""
    name: str
    description: str
    rule_type: str  # 'price', 'volume', 'time', 'ohlc', 'custom'
    enabled: bool = True
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self, data: pd.DataFrame, symbol: str) -> Tuple[pd.Series, List[str]]:
        """执行验证规则，返回有效数据掩码和错误信息"""
        raise NotImplementedError("Subclasses must implement validate method")


class PriceValidationRule(ValidationRule):
    """价格验证规则"""
    
    def __init__(self, min_price: float = 0.01, max_price: float = 10000.0, 
                 max_change_ratio: float = 0.5):
        super().__init__(
            name="price_validation",
            description="验证价格数据的合理性",
            rule_type="price",
            parameters={
                'min_price': min_price,
                'max_price': max_price,
                'max_change_ratio': max_change_ratio
            }
        )
    
    def validate(self, data: pd.DataFrame, symbol: str) -> Tuple[pd.Series, List[str]]:
        """验证价格数据"""
        issues = []
        valid_mask = pd.Series(True, index=data.index)
        
        price_columns = ['open', 'high', 'low', 'close', 'price', 'last_price']
        available_price_cols = [col for col in price_columns if col in data.columns]
        
        if not available_price_cols:
            issues.append(f"No price columns found for {symbol}")
            return pd.Series(False, index=data.index), issues
        
        for col in available_price_cols:
            # 检查负价格和零价格
            negative_mask = data[col] < 0
            zero_mask = data[col] == 0
            
            if negative_mask.any():
                issues.append(f"Found {negative_mask.sum()} negative prices in {col} for {symbol}")
                valid_mask &= ~negative_mask
            
            if zero_mask.any():
                issues.append(f"Found {zero_mask.sum()} zero prices in {col} for {symbol}")
                valid_mask &= ~zero_mask
            
            # 检查价格范围
            min_price = self.parameters['min_price']
            max_price = self.parameters['max_price']
            
            out_of_range_mask = (data[col] < min_price) | (data[col] > max_price)
            if out_of_range_mask.any():
                issues.append(f"Found {out_of_range_mask.sum()} out-of-range prices in {col} for {symbol}")
                valid_mask &= ~out_of_range_mask
            
            # 检查价格变化幅度
            if len(data) > 1:
                price_change = data[col].pct_change().abs()
                max_change = self.parameters['max_change_ratio']
                outlier_mask = price_change > max_change
                
                if outlier_mask.any():
                    issues.append(f"Found {outlier_mask.sum()} price outliers in {col} for {symbol}")
                    valid_mask &= ~outlier_mask
        
        return valid_mask, issues


class OHLCValidationRule(ValidationRule):
    """OHLC数据验证规则"""
    
    def __init__(self):
        super().__init__(
            name="ohlc_validation",
            description="验证OHLC数据的逻辑关系",
            rule_type="ohlc"
        )
    
    def validate(self, data: pd.DataFrame, symbol: str) -> Tuple[pd.Series, List[str]]:
        """验证OHLC数据逻辑关系"""
        issues = []
        valid_mask = pd.Series(True, index=data.index)
        
        required_cols = ['open', 'high', 'low', 'close']
        if not all(col in data.columns for col in required_cols):
            missing_cols = [col for col in required_cols if col not in data.columns]
            issues.append(f"Missing OHLC columns for {symbol}: {missing_cols}")
            return pd.Series(False, index=data.index), issues
        
        # 检查 high >= max(open, close, low)
        high_invalid = (data['high'] < data['open']) | \
                      (data['high'] < data['close']) | \
                      (data['high'] < data['low'])
        
        # 检查 low <= min(open, close, high)
        low_invalid = (data['low'] > data['open']) | \
                     (data['low'] > data['close']) | \
                     (data['low'] > data['high'])
        
        invalid_ohlc = high_invalid | low_invalid
        
        if invalid_ohlc.any():
            issues.append(f"Found {invalid_ohlc.sum()} invalid OHLC relationships for {symbol}")
            valid_mask &= ~invalid_ohlc
        
        return valid_mask, issues


class VolumeValidationRule(ValidationRule):
    """成交量验证规则"""
    
    def __init__(self, min_volume: int = 0, max_volume_multiplier: float = 100.0):
        super().__init__(
            name="volume_validation",
            description="验证成交量数据的合理性",
            rule_type="volume",
            parameters={
                'min_volume': min_volume,
                'max_volume_multiplier': max_volume_multiplier
            }
        )
    
    def validate(self, data: pd.DataFrame, symbol: str) -> Tuple[pd.Series, List[str]]:
        """验证成交量数据"""
        issues = []
        valid_mask = pd.Series(True, index=data.index)
        
        volume_columns = ['volume', 'vol']
        available_vol_cols = [col for col in volume_columns if col in data.columns]
        
        if not available_vol_cols:
            issues.append(f"No volume columns found for {symbol}")
            return valid_mask, issues
        
        for col in available_vol_cols:
            # 检查负成交量
            negative_mask = data[col] < 0
            if negative_mask.any():
                issues.append(f"Found {negative_mask.sum()} negative volumes in {col} for {symbol}")
                valid_mask &= ~negative_mask
            
            # 检查异常大的成交量（基于历史平均值）
            if len(data) > 10:
                mean_volume = data[col].mean()
                max_volume = mean_volume * self.parameters['max_volume_multiplier']
                outlier_mask = data[col] > max_volume
                
                if outlier_mask.any():
                    issues.append(f"Found {outlier_mask.sum()} volume outliers in {col} for {symbol}")
                    valid_mask &= ~outlier_mask
        
        return valid_mask, issues


class TimeSeriesValidationRule(ValidationRule):
    """时间序列验证规则"""
    
    def __init__(self, allow_duplicates: bool = False, max_time_gap_minutes: int = 1440):
        super().__init__(
            name="timeseries_validation",
            description="验证时间序列数据的连续性和顺序",
            rule_type="time",
            parameters={
                'allow_duplicates': allow_duplicates,
                'max_time_gap_minutes': max_time_gap_minutes
            }
        )
    
    def validate(self, data: pd.DataFrame, symbol: str) -> Tuple[pd.Series, List[str]]:
        """验证时间序列数据"""
        issues = []
        valid_mask = pd.Series(True, index=data.index)
        
        if len(data) <= 1:
            return valid_mask, issues
        
        # 检查时间戳重复
        if not self.parameters['allow_duplicates']:
            duplicate_mask = data.index.duplicated(keep='first')
            if duplicate_mask.any():
                issues.append(f"Found {duplicate_mask.sum()} duplicate timestamps for {symbol}")
                valid_mask &= ~duplicate_mask
        
        # 检查时间顺序
        if not data.index.is_monotonic_increasing:
            issues.append(f"Time series is not in ascending order for {symbol}")
            # 不标记为无效，而是建议排序
        
        # 检查时间间隔
        time_diffs = pd.Series(data.index).diff().dt.total_seconds() / 60  # 转换为分钟
        max_gap = self.parameters['max_time_gap_minutes']
        large_gaps = time_diffs > max_gap
        
        if large_gaps.any():
            gap_count = large_gaps.sum()
            issues.append(f"Found {gap_count} large time gaps (>{max_gap} minutes) for {symbol}")
        
        return valid_mask, issues


class EnhancedDataValidator:
    """增强的数据验证器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.EnhancedDataValidator")
        
        # 初始化验证规则
        self.rules: List[ValidationRule] = []
        self._initialize_default_rules()
        
        # 验证统计
        self.validation_stats = defaultdict(int)
    
    def _initialize_default_rules(self):
        """初始化默认验证规则"""
        # 价格验证规则
        price_rule = PriceValidationRule(
            min_price=self.config.get('min_price', 0.01),
            max_price=self.config.get('max_price', 10000.0),
            max_change_ratio=self.config.get('max_price_change_ratio', 0.5)
        )
        self.rules.append(price_rule)
        
        # OHLC验证规则
        ohlc_rule = OHLCValidationRule()
        self.rules.append(ohlc_rule)
        
        # 成交量验证规则
        volume_rule = VolumeValidationRule(
            min_volume=self.config.get('min_volume', 0),
            max_volume_multiplier=self.config.get('max_volume_multiplier', 100.0)
        )
        self.rules.append(volume_rule)
        
        # 时间序列验证规则
        timeseries_rule = TimeSeriesValidationRule(
            allow_duplicates=self.config.get('allow_duplicate_timestamps', False),
            max_time_gap_minutes=self.config.get('max_time_gap_minutes', 1440)
        )
        self.rules.append(timeseries_rule)
    
    def add_rule(self, rule: ValidationRule):
        """添加自定义验证规则"""
        self.rules.append(rule)
        self.logger.info(f"Added validation rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除验证规则"""
        self.rules = [rule for rule in self.rules if rule.name != rule_name]
        self.logger.info(f"Removed validation rule: {rule_name}")
    
    async def validate_k_data(self, symbol: str, data: pd.DataFrame) -> Tuple[pd.DataFrame, QualityMetrics]:
        """验证K线数据"""
        if data.empty:
            return data, self._create_empty_metrics(symbol)
        
        start_time = datetime.now()
        original_count = len(data)
        
        # 创建质量指标对象
        metrics = QualityMetrics(
            symbol=symbol,
            start_time=start_time,
            end_time=start_time,
            total_records=original_count
        )
        
        try:
            validated_data = data.copy()
            overall_valid_mask = pd.Series(True, index=data.index)
            
            # 应用所有验证规则
            for rule in self.rules:
                if not rule.enabled:
                    continue
                
                try:
                    valid_mask, issues = rule.validate(validated_data, symbol)
                    overall_valid_mask &= valid_mask
                    metrics.quality_issues.extend(issues)
                    
                    # 更新统计信息
                    invalid_count = (~valid_mask).sum()
                    self.validation_stats[f"{rule.name}_invalid"] += invalid_count
                    
                    if rule.rule_type == 'price':
                        metrics.price_outliers += invalid_count
                    elif rule.rule_type == 'volume':
                        metrics.volume_outliers += invalid_count
                    elif rule.rule_type == 'ohlc':
                        metrics.invalid_ohlc_records += invalid_count
                    elif rule.rule_type == 'time':
                        metrics.out_of_order_records += invalid_count
                    
                except Exception as e:
                    self.logger.error(f"Error applying rule {rule.name} to {symbol}: {e}")
                    metrics.quality_issues.append(f"Rule {rule.name} failed: {str(e)}")
            
            # 应用验证结果
            validated_data = validated_data[overall_valid_mask]
            
            # 更新质量指标
            metrics.end_time = datetime.now()
            metrics.valid_records = len(validated_data)
            metrics.invalid_records = original_count - metrics.valid_records
            metrics.calculate_ratios()
            
            # 记录统计信息
            self.validation_stats['total_validated'] += 1
            self.validation_stats['total_records_processed'] += original_count
            self.validation_stats['total_valid_records'] += metrics.valid_records
            
            self.logger.info(f"Validated K data for {symbol}: {original_count} -> {metrics.valid_records} records")
            return validated_data, metrics
            
        except Exception as e:
            self.logger.error(f"Error validating K data for {symbol}: {e}")
            metrics.quality_issues.append(f"Validation failed: {str(e)}")
            return pd.DataFrame(), metrics
    
    async def validate_tick_data(self, symbol: str, data: pd.DataFrame) -> Tuple[pd.DataFrame, QualityMetrics]:
        """验证tick数据"""
        if data.empty:
            return data, self._create_empty_metrics(symbol)
        
        start_time = datetime.now()
        original_count = len(data)
        
        # 创建质量指标对象
        metrics = QualityMetrics(
            symbol=symbol,
            start_time=start_time,
            end_time=start_time,
            total_records=original_count
        )
        
        try:
            validated_data = data.copy()
            
            # 基本数据完整性检查
            required_columns = ['price', 'volume']
            missing_columns = [col for col in required_columns if col not in validated_data.columns]
            if missing_columns:
                metrics.quality_issues.append(f"Missing required columns: {missing_columns}")
                return pd.DataFrame(), metrics
            
            # 价格验证
            price_valid = (validated_data['price'] > 0)
            metrics.zero_price_records = (~price_valid).sum()
            
            # 成交量验证
            volume_valid = (validated_data['volume'] >= 0)
            metrics.negative_volume_records = (~volume_valid).sum()
            
            # 组合验证结果
            overall_valid = price_valid & volume_valid
            validated_data = validated_data[overall_valid]
            
            # 时间序列检查
            if not validated_data.index.is_monotonic_increasing:
                validated_data = validated_data.sort_index()
                metrics.quality_issues.append("Data was not in time order, sorted automatically")
            
            # 检查重复时间戳
            duplicate_mask = validated_data.index.duplicated(keep='last')
            if duplicate_mask.any():
                metrics.duplicate_timestamps = duplicate_mask.sum()
                validated_data = validated_data[~duplicate_mask]
                metrics.quality_issues.append(f"Removed {metrics.duplicate_timestamps} duplicate timestamps")
            
            # 更新质量指标
            metrics.end_time = datetime.now()
            metrics.valid_records = len(validated_data)
            metrics.invalid_records = original_count - metrics.valid_records
            metrics.calculate_ratios()
            
            self.logger.info(f"Validated tick data for {symbol}: {original_count} -> {metrics.valid_records} records")
            return validated_data, metrics
            
        except Exception as e:
            self.logger.error(f"Error validating tick data for {symbol}: {e}")
            metrics.quality_issues.append(f"Validation failed: {str(e)}")
            return pd.DataFrame(), metrics
    
    def _create_empty_metrics(self, symbol: str) -> QualityMetrics:
        """创建空的质量指标"""
        now = datetime.now()
        return QualityMetrics(
            symbol=symbol,
            start_time=now,
            end_time=now,
            quality_issues=["Empty dataset"]
        )
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        return dict(self.validation_stats)
    
    def reset_statistics(self):
        """重置统计信息"""
        self.validation_stats.clear()


class EnhancedDataDeduplicator:
    """增强的数据去重器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.EnhancedDataDeduplicator")
        
        # 去重缓存（用于跨批次去重）
        self.dedup_cache: Dict[str, Set[str]] = defaultdict(set)
        self.cache_max_size = self.config.get('cache_max_size', 10000)
        
        # 去重统计
        self.dedup_stats = defaultdict(int)
    
    async def deduplicate_k_data(self, symbol: str, data: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """K线数据去重"""
        if data.empty:
            return data, 0
        
        try:
            original_count = len(data)
            deduplicated_data = data.copy()
            
            # 1. 基于时间戳去重（保留最后一个）
            timestamp_duplicates = deduplicated_data.index.duplicated(keep='last')
            if timestamp_duplicates.any():
                deduplicated_data = deduplicated_data[~timestamp_duplicates]
                self.logger.info(f"Removed {timestamp_duplicates.sum()} timestamp duplicates for {symbol}")
            
            # 2. 基于内容去重
            content_columns = ['open', 'high', 'low', 'close', 'volume']
            available_columns = [col for col in content_columns if col in deduplicated_data.columns]
            
            if available_columns:
                content_duplicates = deduplicated_data.duplicated(subset=available_columns, keep='last')
                if content_duplicates.any():
                    deduplicated_data = deduplicated_data[~content_duplicates]
                    self.logger.info(f"Removed {content_duplicates.sum()} content duplicates for {symbol}")
            
            # 3. 基于哈希的跨批次去重
            if self.config.get('enable_cross_batch_dedup', True):
                deduplicated_data = await self._cross_batch_deduplicate(symbol, deduplicated_data, 'k_data')
            
            removed_count = original_count - len(deduplicated_data)
            
            # 更新统计信息
            self.dedup_stats[f"{symbol}_k_data_processed"] += original_count
            self.dedup_stats[f"{symbol}_k_data_removed"] += removed_count
            self.dedup_stats['total_k_data_removed'] += removed_count
            
            if removed_count > 0:
                self.logger.info(f"Removed {removed_count} duplicate K records for {symbol}")
            
            return deduplicated_data, removed_count
            
        except Exception as e:
            self.logger.error(f"Error deduplicating K data for {symbol}: {e}")
            return data, 0
    
    async def deduplicate_tick_data(self, symbol: str, data: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """tick数据去重"""
        if data.empty:
            return data, 0
        
        try:
            original_count = len(data)
            deduplicated_data = data.copy()
            
            # 1. 基于时间戳去重（保留最后一个）
            timestamp_duplicates = deduplicated_data.index.duplicated(keep='last')
            if timestamp_duplicates.any():
                deduplicated_data = deduplicated_data[~timestamp_duplicates]
                self.logger.info(f"Removed {timestamp_duplicates.sum()} timestamp duplicates for {symbol}")
            
            # 2. 基于价格和成交量去重
            content_columns = ['price', 'volume']
            available_columns = [col for col in content_columns if col in deduplicated_data.columns]
            
            if available_columns:
                # 对于tick数据，我们更严格一些，相同时间戳的相同价格和成交量认为是重复
                content_duplicates = deduplicated_data.duplicated(subset=available_columns, keep='last')
                if content_duplicates.any():
                    deduplicated_data = deduplicated_data[~content_duplicates]
                    self.logger.info(f"Removed {content_duplicates.sum()} content duplicates for {symbol}")
            
            # 3. 基于哈希的跨批次去重
            if self.config.get('enable_cross_batch_dedup', True):
                deduplicated_data = await self._cross_batch_deduplicate(symbol, deduplicated_data, 'tick_data')
            
            removed_count = original_count - len(deduplicated_data)
            
            # 更新统计信息
            self.dedup_stats[f"{symbol}_tick_data_processed"] += original_count
            self.dedup_stats[f"{symbol}_tick_data_removed"] += removed_count
            self.dedup_stats['total_tick_data_removed'] += removed_count
            
            if removed_count > 0:
                self.logger.info(f"Removed {removed_count} duplicate tick records for {symbol}")
            
            return deduplicated_data, removed_count
            
        except Exception as e:
            self.logger.error(f"Error deduplicating tick data for {symbol}: {e}")
            return data, 0
    
    async def _cross_batch_deduplicate(self, symbol: str, data: pd.DataFrame, data_type: str) -> pd.DataFrame:
        """跨批次去重"""
        try:
            cache_key = f"{symbol}_{data_type}"
            
            # 为每行数据生成哈希
            data_hashes = []
            valid_indices = []
            
            for idx, row in data.iterrows():
                # 创建行数据的哈希
                row_str = f"{idx}_{row.to_json()}"
                row_hash = hashlib.md5(row_str.encode()).hexdigest()
                
                if row_hash not in self.dedup_cache[cache_key]:
                    data_hashes.append(row_hash)
                    valid_indices.append(idx)
            
            # 更新缓存
            self.dedup_cache[cache_key].update(data_hashes)
            
            # 限制缓存大小
            if len(self.dedup_cache[cache_key]) > self.cache_max_size:
                # 保留最新的一半
                cache_list = list(self.dedup_cache[cache_key])
                self.dedup_cache[cache_key] = set(cache_list[-self.cache_max_size//2:])
            
            # 返回去重后的数据
            if len(valid_indices) < len(data):
                removed_count = len(data) - len(valid_indices)
                self.logger.info(f"Cross-batch dedup removed {removed_count} records for {symbol}")
                return data.loc[valid_indices]
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error in cross-batch deduplication for {symbol}: {e}")
            return data
    
    def get_deduplication_statistics(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        return dict(self.dedup_stats)
    
    def reset_statistics(self):
        """重置统计信息"""
        self.dedup_stats.clear()
    
    def clear_cache(self, symbol: Optional[str] = None):
        """清空去重缓存"""
        if symbol:
            # 清空特定symbol的缓存
            keys_to_remove = [key for key in self.dedup_cache.keys() if key.startswith(f"{symbol}_")]
            for key in keys_to_remove:
                del self.dedup_cache[key]
        else:
            # 清空所有缓存
            self.dedup_cache.clear()


class DataIntegrityChecker:
    """数据完整性校验器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.DataIntegrityChecker")
        
        # 完整性检查统计
        self.integrity_stats = defaultdict(int)
    
    async def check_data_completeness(self, symbol: str, data: pd.DataFrame, 
                                    expected_timeframe: str = 'D') -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            if data.empty:
                return {
                    'symbol': symbol,
                    'is_complete': False,
                    'missing_periods': 0,
                    'total_expected': 0,
                    'actual_count': 0,
                    'completeness_ratio': 0.0,
                    'missing_dates': [],
                    'issues': ['Empty dataset']
                }
            
            # 根据时间框架生成期望的时间序列
            start_date = data.index.min()
            end_date = data.index.max()
            
            if expected_timeframe == 'D':
                expected_dates = pd.date_range(start=start_date, end=end_date, freq='D')
                # 排除周末（股票市场不交易）
                expected_dates = expected_dates[expected_dates.weekday < 5]
            elif expected_timeframe == '1min':
                # 交易时间：9:30-11:30, 13:00-15:00
                expected_dates = self._generate_trading_minutes(start_date, end_date)
            else:
                # 其他时间框架，简单按频率生成
                expected_dates = pd.date_range(start=start_date, end=end_date, freq=expected_timeframe)
            
            # 找出缺失的时间点
            actual_dates = set(data.index)
            expected_dates_set = set(expected_dates)
            missing_dates = expected_dates_set - actual_dates
            
            # 计算完整性指标
            total_expected = len(expected_dates_set)
            actual_count = len(actual_dates)
            missing_count = len(missing_dates)
            completeness_ratio = (total_expected - missing_count) / total_expected if total_expected > 0 else 0.0
            
            # 生成报告
            result = {
                'symbol': symbol,
                'is_complete': missing_count == 0,
                'missing_periods': missing_count,
                'total_expected': total_expected,
                'actual_count': actual_count,
                'completeness_ratio': completeness_ratio,
                'missing_dates': sorted(list(missing_dates))[:100],  # 限制返回数量
                'issues': []
            }
            
            if missing_count > 0:
                result['issues'].append(f"Missing {missing_count} periods out of {total_expected}")
            
            if completeness_ratio < 0.95:
                result['issues'].append(f"Low completeness ratio: {completeness_ratio:.2%}")
            
            # 更新统计信息
            self.integrity_stats[f"{symbol}_completeness_checks"] += 1
            self.integrity_stats[f"{symbol}_missing_periods"] += missing_count
            
            self.logger.info(f"Completeness check for {symbol}: {completeness_ratio:.2%} complete")
            return result
            
        except Exception as e:
            self.logger.error(f"Error checking data completeness for {symbol}: {e}")
            return {
                'symbol': symbol,
                'is_complete': False,
                'error': str(e),
                'issues': [f"Completeness check failed: {str(e)}"]
            }
    
    def _generate_trading_minutes(self, start_date: datetime, end_date: datetime) -> pd.DatetimeIndex:
        """生成交易时间的分钟级时间序列"""
        trading_dates = pd.date_range(start=start_date.date(), end=end_date.date(), freq='D')
        trading_dates = trading_dates[trading_dates.weekday < 5]  # 排除周末
        
        all_minutes = []
        for date in trading_dates:
            # 上午交易时间：9:30-11:30
            morning_start = datetime.combine(date.date(), datetime.min.time().replace(hour=9, minute=30))
            morning_end = datetime.combine(date.date(), datetime.min.time().replace(hour=11, minute=30))
            morning_minutes = pd.date_range(start=morning_start, end=morning_end, freq='1min')
            
            # 下午交易时间：13:00-15:00
            afternoon_start = datetime.combine(date.date(), datetime.min.time().replace(hour=13, minute=0))
            afternoon_end = datetime.combine(date.date(), datetime.min.time().replace(hour=15, minute=0))
            afternoon_minutes = pd.date_range(start=afternoon_start, end=afternoon_end, freq='1min')
            
            all_minutes.extend(morning_minutes)
            all_minutes.extend(afternoon_minutes)
        
        return pd.DatetimeIndex(all_minutes)
    
    async def check_data_consistency(self, symbol: str, data: pd.DataFrame) -> Dict[str, Any]:
        """检查数据一致性"""
        try:
            issues = []
            consistency_score = 1.0
            
            if data.empty:
                return {
                    'symbol': symbol,
                    'is_consistent': False,
                    'consistency_score': 0.0,
                    'issues': ['Empty dataset']
                }
            
            # 检查时间序列一致性
            if not data.index.is_monotonic_increasing:
                issues.append("Time series is not in ascending order")
                consistency_score -= 0.2
            
            # 检查数据类型一致性
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if data[col].dtype == 'object':
                    issues.append(f"Column {col} has mixed data types")
                    consistency_score -= 0.1
            
            # 检查价格一致性（如果是OHLC数据）
            if all(col in data.columns for col in ['open', 'high', 'low', 'close']):
                # 检查价格跳跃
                close_prices = data['close'].dropna()
                if len(close_prices) > 1:
                    price_changes = close_prices.pct_change().abs()
                    extreme_changes = price_changes > 2.0  # 200%以上的价格变化才认为是极端变化
                    
                    if extreme_changes.any():
                        extreme_count = extreme_changes.sum()
                        issues.append(f"Found {extreme_count} extreme price changes (>200%)")
                        consistency_score -= min(0.5, extreme_count * 0.2)
            
            # 检查成交量一致性
            if 'volume' in data.columns:
                volumes = data['volume'].dropna()
                if len(volumes) > 0:
                    zero_volume_ratio = (volumes == 0).sum() / len(volumes)
                    if zero_volume_ratio > 0.1:  # 超过10%的零成交量
                        issues.append(f"High zero volume ratio: {zero_volume_ratio:.2%}")
                        consistency_score -= min(0.5, zero_volume_ratio)
            
            # 更新统计信息
            self.integrity_stats[f"{symbol}_consistency_checks"] += 1
            if consistency_score < 0.8:
                self.integrity_stats[f"{symbol}_consistency_issues"] += 1
            
            result = {
                'symbol': symbol,
                'is_consistent': consistency_score >= 0.8,
                'consistency_score': max(0.0, consistency_score),
                'issues': issues
            }
            
            self.logger.info(f"Consistency check for {symbol}: score {consistency_score:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error checking data consistency for {symbol}: {e}")
            return {
                'symbol': symbol,
                'is_consistent': False,
                'consistency_score': 0.0,
                'issues': [f"Consistency check failed: {str(e)}"]
            }
    
    def get_integrity_statistics(self) -> Dict[str, Any]:
        """获取完整性检查统计信息"""
        return dict(self.integrity_stats)
    
    def reset_statistics(self):
        """重置统计信息"""
        self.integrity_stats.clear()


class DataQualityManager:
    """数据质量管理器 - 统一管理所有数据质量控制功能"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.DataQualityManager")
        
        # 初始化各个组件
        self.validator = EnhancedDataValidator(self.config.get('validation', {}))
        self.deduplicator = EnhancedDataDeduplicator(self.config.get('deduplication', {}))
        self.integrity_checker = DataIntegrityChecker(self.config.get('integrity', {}))
        
        # 质量管理统计
        self.quality_stats = {
            'total_processed': 0,
            'total_validated': 0,
            'total_deduplicated': 0,
            'total_integrity_checked': 0,
            'start_time': datetime.now(),
            'last_update': None
        }
        
        # 质量报告历史
        self.quality_reports: deque = deque(maxlen=self.config.get('max_reports_history', 1000))
    
    async def process_k_data(self, symbol: str, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """处理K线数据的完整质量控制流程"""
        if data.empty:
            return data, {'symbol': symbol, 'status': 'empty', 'issues': ['Empty dataset']}
        
        try:
            self.logger.info(f"Starting quality control for K data: {symbol}, {len(data)} records")
            
            # 1. 数据验证
            validated_data, validation_metrics = await self.validator.validate_k_data(symbol, data)
            
            if validated_data.empty:
                return validated_data, {
                    'symbol': symbol,
                    'status': 'validation_failed',
                    'validation_metrics': validation_metrics.to_dict(),
                    'issues': validation_metrics.quality_issues
                }
            
            # 2. 数据去重
            deduplicated_data, removed_count = await self.deduplicator.deduplicate_k_data(symbol, validated_data)
            
            # 3. 完整性检查
            completeness_result = await self.integrity_checker.check_data_completeness(symbol, deduplicated_data, 'D')
            consistency_result = await self.integrity_checker.check_data_consistency(symbol, deduplicated_data)
            
            # 4. 生成质量报告
            quality_report = {
                'symbol': symbol,
                'data_type': 'k_data',
                'timestamp': datetime.now().isoformat(),
                'original_count': len(data),
                'final_count': len(deduplicated_data),
                'validation_metrics': validation_metrics.to_dict(),
                'removed_duplicates': removed_count,
                'completeness': completeness_result,
                'consistency': consistency_result,
                'status': 'success',
                'quality_score': self._calculate_quality_score(validation_metrics, completeness_result, consistency_result)
            }
            
            # 保存报告
            self.quality_reports.append(quality_report)
            
            # 更新统计信息
            self.quality_stats['total_processed'] += 1
            self.quality_stats['total_validated'] += 1
            self.quality_stats['total_deduplicated'] += 1
            self.quality_stats['total_integrity_checked'] += 1
            self.quality_stats['last_update'] = datetime.now()
            
            self.logger.info(f"Quality control completed for {symbol}: {len(data)} -> {len(deduplicated_data)} records")
            return deduplicated_data, quality_report
            
        except Exception as e:
            self.logger.error(f"Error in quality control for {symbol}: {e}")
            error_report = {
                'symbol': symbol,
                'data_type': 'k_data',
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e),
                'issues': [f"Quality control failed: {str(e)}"]
            }
            self.quality_reports.append(error_report)
            return pd.DataFrame(), error_report
    
    async def process_tick_data(self, symbol: str, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """处理tick数据的完整质量控制流程"""
        if data.empty:
            return data, {'symbol': symbol, 'status': 'empty', 'issues': ['Empty dataset']}
        
        try:
            self.logger.info(f"Starting quality control for tick data: {symbol}, {len(data)} records")
            
            # 1. 数据验证
            validated_data, validation_metrics = await self.validator.validate_tick_data(symbol, data)
            
            if validated_data.empty:
                return validated_data, {
                    'symbol': symbol,
                    'status': 'validation_failed',
                    'validation_metrics': validation_metrics.to_dict(),
                    'issues': validation_metrics.quality_issues
                }
            
            # 2. 数据去重
            deduplicated_data, removed_count = await self.deduplicator.deduplicate_tick_data(symbol, validated_data)
            
            # 3. 完整性检查（tick数据通常不检查完整性，因为是实时数据）
            consistency_result = await self.integrity_checker.check_data_consistency(symbol, deduplicated_data)
            
            # 4. 生成质量报告
            quality_report = {
                'symbol': symbol,
                'data_type': 'tick_data',
                'timestamp': datetime.now().isoformat(),
                'original_count': len(data),
                'final_count': len(deduplicated_data),
                'validation_metrics': validation_metrics.to_dict(),
                'removed_duplicates': removed_count,
                'consistency': consistency_result,
                'status': 'success',
                'quality_score': self._calculate_quality_score(validation_metrics, None, consistency_result)
            }
            
            # 保存报告
            self.quality_reports.append(quality_report)
            
            # 更新统计信息
            self.quality_stats['total_processed'] += 1
            self.quality_stats['total_validated'] += 1
            self.quality_stats['total_deduplicated'] += 1
            self.quality_stats['total_integrity_checked'] += 1
            self.quality_stats['last_update'] = datetime.now()
            
            self.logger.info(f"Quality control completed for {symbol}: {len(data)} -> {len(deduplicated_data)} records")
            return deduplicated_data, quality_report
            
        except Exception as e:
            self.logger.error(f"Error in quality control for {symbol}: {e}")
            error_report = {
                'symbol': symbol,
                'data_type': 'tick_data',
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e),
                'issues': [f"Quality control failed: {str(e)}"]
            }
            self.quality_reports.append(error_report)
            return pd.DataFrame(), error_report
    
    def _calculate_quality_score(self, validation_metrics: QualityMetrics, 
                               completeness_result: Optional[Dict], 
                               consistency_result: Dict) -> float:
        """计算综合质量分数"""
        try:
            score = 0.0
            weight_sum = 0.0
            
            # 验证分数 (权重: 0.4)
            if validation_metrics:
                validation_score = validation_metrics.accuracy_ratio
                score += validation_score * 0.4
                weight_sum += 0.4
            
            # 完整性分数 (权重: 0.3)
            if completeness_result and 'completeness_ratio' in completeness_result:
                completeness_score = completeness_result['completeness_ratio']
                score += completeness_score * 0.3
                weight_sum += 0.3
            
            # 一致性分数 (权重: 0.3)
            if consistency_result and 'consistency_score' in consistency_result:
                consistency_score = consistency_result['consistency_score']
                score += consistency_score * 0.3
                weight_sum += 0.3
            
            # 归一化分数
            if weight_sum > 0:
                return score / weight_sum
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"Error calculating quality score: {e}")
            return 0.0
    
    def get_quality_statistics(self) -> Dict[str, Any]:
        """获取质量管理统计信息"""
        stats = self.quality_stats.copy()
        
        # 添加各组件的统计信息
        stats['validation_stats'] = self.validator.get_validation_statistics()
        stats['deduplication_stats'] = self.deduplicator.get_deduplication_statistics()
        stats['integrity_stats'] = self.integrity_checker.get_integrity_statistics()
        
        # 计算运行时间
        if stats['start_time']:
            stats['uptime_seconds'] = (datetime.now() - stats['start_time']).total_seconds()
        
        return stats
    
    def get_quality_reports(self, symbol: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取质量报告"""
        reports = list(self.quality_reports)
        
        if symbol:
            reports = [r for r in reports if r.get('symbol') == symbol]
        
        # 按时间倒序排列，返回最新的报告
        reports.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        return reports[:limit]
    
    def generate_quality_summary(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """生成质量摘要报告"""
        try:
            reports = self.get_quality_reports(symbol)
            
            if not reports:
                return {
                    'symbol': symbol or 'all',
                    'total_reports': 0,
                    'message': 'No quality reports available',
                    'generated_at': datetime.now().isoformat()
                }
            
            # 统计各种指标
            total_reports = len(reports)
            successful_reports = len([r for r in reports if r.get('status') == 'success'])
            failed_reports = total_reports - successful_reports
            
            # 计算平均质量分数
            quality_scores = [r.get('quality_score', 0) for r in reports if r.get('quality_score') is not None]
            avg_quality_score = statistics.mean(quality_scores) if quality_scores else 0.0
            
            # 统计数据处理量
            total_original = sum(r.get('original_count', 0) for r in reports)
            total_final = sum(r.get('final_count', 0) for r in reports)
            total_removed = total_original - total_final
            
            # 收集常见问题
            all_issues = []
            for report in reports:
                if 'validation_metrics' in report and 'quality_issues' in report['validation_metrics']:
                    all_issues.extend(report['validation_metrics']['quality_issues'])
                if 'issues' in report:
                    all_issues.extend(report['issues'])
            
            # 统计问题频率
            issue_counts = defaultdict(int)
            for issue in all_issues:
                issue_counts[issue] += 1
            
            common_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
            summary = {
                'symbol': symbol or 'all',
                'report_period': {
                    'start': reports[-1].get('timestamp') if reports else None,
                    'end': reports[0].get('timestamp') if reports else None
                },
                'total_reports': total_reports,
                'successful_reports': successful_reports,
                'failed_reports': failed_reports,
                'success_rate': successful_reports / total_reports if total_reports > 0 else 0.0,
                'average_quality_score': avg_quality_score,
                'data_processing': {
                    'total_original_records': total_original,
                    'total_final_records': total_final,
                    'total_removed_records': total_removed,
                    'removal_rate': total_removed / total_original if total_original > 0 else 0.0
                },
                'common_issues': common_issues,
                'generated_at': datetime.now().isoformat()
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error generating quality summary: {e}")
            return {
                'symbol': symbol or 'all',
                'error': str(e),
                'generated_at': datetime.now().isoformat()
            }
    
    def reset_statistics(self):
        """重置所有统计信息"""
        self.validator.reset_statistics()
        self.deduplicator.reset_statistics()
        self.integrity_checker.reset_statistics()
        
        self.quality_stats = {
            'total_processed': 0,
            'total_validated': 0,
            'total_deduplicated': 0,
            'total_integrity_checked': 0,
            'start_time': datetime.now(),
            'last_update': None
        }
        
        self.quality_reports.clear()
        self.logger.info("All quality statistics have been reset")