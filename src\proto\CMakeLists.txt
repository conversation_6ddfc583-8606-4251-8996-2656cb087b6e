# Protocol Buffers and Data Models

# Find Protocol Buffers and gRPC using vcpkg
find_package(protobuf CONFIG REQUIRED)
find_package(gRPC CONFIG REQUIRED)

# Generate Protocol Buffer files
set(PROTO_FILES
    market_data.proto
    market_data_service.proto
)

# Generate C++ files from .proto files using new protobuf method
protobuf_generate(
    LANGUAGE cpp
    OUT_VAR PROTO_GENERATED_FILES
    PROTOS ${PROTO_FILES}
)

# Generate gRPC files for service proto
protobuf_generate(
    LANGUAGE grpc
    GENERATE_EXTENSIONS .grpc.pb.h .grpc.pb.cc
    PLUGIN "protoc-gen-grpc=\$<TARGET_FILE:gRPC::grpc_cpp_plugin>"
    OUT_VAR GRPC_GENERATED_FILES
    PROTOS market_data_service.proto
)

# Create proto library
add_library(proto
    ${PROTO_GENERATED_FILES}
    ${GRPC_GENERATED_FILES}
    data_types.h
    serializer.h
    serializer.cpp
    validator.h
    validator.cpp
)

# Include directories
target_include_directories(proto PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_BINARY_DIR}
)

# Link libraries using vcpkg targets
target_link_libraries(proto
    protobuf::libprotobuf
    gRPC::grpc++
)

# Set C++ standard
target_compile_features(proto PUBLIC cxx_std_17)

# Compiler-specific options
if(MSVC)
    target_compile_options(proto PRIVATE /W3)
    # Disable some warnings for generated protobuf code
    target_compile_definitions(proto PRIVATE _CRT_SECURE_NO_WARNINGS)
else()
    target_compile_options(proto PRIVATE -Wall -Wextra)
endif()

# Export include directories for other targets
target_include_directories(proto INTERFACE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_BINARY_DIR}
)