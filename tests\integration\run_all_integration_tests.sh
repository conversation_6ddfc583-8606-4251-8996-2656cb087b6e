#!/bin/bash

# 综合集成测试执行脚本
# 运行所有端到端集成测试、数据一致性验证和性能压力测试

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 测试结果目录
RESULTS_DIR="$PROJECT_ROOT/tests/integration/results"
mkdir -p "$RESULTS_DIR"

# 时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$RESULTS_DIR/integration_test_report_$TIMESTAMP.json"

log_info "Starting comprehensive integration test suite"
log_info "Project root: $PROJECT_ROOT"
log_info "Results directory: $RESULTS_DIR"

# 初始化测试结果
TEST_RESULTS="{\"timestamp\":\"$(date -Iseconds)\",\"tests\":{}}"

# 检查测试环境
check_environment() {
    log_info "Checking test environment..."
    
    # 检查Python环境
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 is not installed"
        return 1
    fi
    
    # 检查必要的Python包
    python3 -c "import pandas, redis, requests, asyncio" 2>/dev/null || {
        log_warning "Some Python dependencies are missing, tests may fail"
    }
    
    # 检查Redis服务
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            log_success "Redis service is available"
        else
            log_warning "Redis service is not responding"
        fi
    else
        log_warning "Redis CLI not found"
    fi
    
    # 检查ClickHouse服务
    if command -v clickhouse-client &> /dev/null; then
        if clickhouse-client --query "SELECT 1" &> /dev/null; then
            log_success "ClickHouse service is available"
        else
            log_warning "ClickHouse service is not responding"
        fi
    else
        log_warning "ClickHouse client not found"
    fi
    
    # 检查C++编译环境
    if command -v cmake &> /dev/null && command -v g++ &> /dev/null; then
        log_success "C++ build environment is available"
    else
        log_warning "C++ build environment not complete"
    fi
    
    log_success "Environment check completed"
}

# 运行Python集成测试
run_python_tests() {
    log_info "Running Python integration tests..."
    
    cd "$PROJECT_ROOT"
    
    # 设置环境变量
    export PYTHONPATH="$PROJECT_ROOT"
    export TESTING=1
    
    local python_success=true
    
    # 1. 端到端集成测试
    log_info "Running end-to-end integration tests..."
    if timeout 600 python3 tests/integration/end_to_end_integration_test.py > "$RESULTS_DIR/python_e2e_$TIMESTAMP.log" 2>&1; then
        log_success "End-to-end integration tests passed"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.python_e2e = {"success": true, "log_file": "python_e2e_'$TIMESTAMP'.log"}')
    else
        log_error "End-to-end integration tests failed"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.python_e2e = {"success": false, "log_file": "python_e2e_'$TIMESTAMP'.log"}')
        python_success=false
    fi
    
    # 2. 数据一致性验证
    log_info "Running data consistency validation..."
    if timeout 900 python3 tests/integration/data_consistency_validator.py > "$RESULTS_DIR/python_consistency_$TIMESTAMP.log" 2>&1; then
        log_success "Data consistency validation passed"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.python_consistency = {"success": true, "log_file": "python_consistency_'$TIMESTAMP'.log"}')
    else
        log_error "Data consistency validation failed"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.python_consistency = {"success": false, "log_file": "python_consistency_'$TIMESTAMP'.log"}')
        python_success=false
    fi
    
    # 3. 性能压力测试
    log_info "Running performance stress tests..."
    if timeout 1800 python3 tests/integration/performance_stress_test.py > "$RESULTS_DIR/python_performance_$TIMESTAMP.log" 2>&1; then
        log_success "Performance stress tests passed"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.python_performance = {"success": true, "log_file": "python_performance_'$TIMESTAMP'.log"}')
    else
        log_error "Performance stress tests failed"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.python_performance = {"success": false, "log_file": "python_performance_'$TIMESTAMP'.log"}')
        python_success=false
    fi
    
    if $python_success; then
        log_success "All Python tests passed"
        return 0
    else
        log_error "Some Python tests failed"
        return 1
    fi
}

# 构建C++测试
build_cpp_tests() {
    log_info "Building C++ tests..."
    
    local build_dir="$PROJECT_ROOT/build"
    mkdir -p "$build_dir"
    
    cd "$build_dir"
    
    # CMake配置
    if cmake .. > "$RESULTS_DIR/cmake_config_$TIMESTAMP.log" 2>&1; then
        log_success "CMake configuration successful"
    else
        log_error "CMake configuration failed"
        return 1
    fi
    
    # 构建集成测试
    if cmake --build . --target end_to_end_integration_test > "$RESULTS_DIR/cpp_build_$TIMESTAMP.log" 2>&1; then
        log_success "C++ end-to-end test build successful"
    else
        log_warning "C++ end-to-end test build failed"
    fi
    
    if cmake --build . --target data_consistency_validator > "$RESULTS_DIR/cpp_build_consistency_$TIMESTAMP.log" 2>&1; then
        log_success "C++ consistency validator build successful"
    else
        log_warning "C++ consistency validator build failed"
    fi
    
    if cmake --build . --target performance_stress_test > "$RESULTS_DIR/cpp_build_performance_$TIMESTAMP.log" 2>&1; then
        log_success "C++ performance test build successful"
    else
        log_warning "C++ performance test build failed"
    fi
    
    return 0
}

# 运行C++集成测试
run_cpp_tests() {
    log_info "Running C++ integration tests..."
    
    local build_dir="$PROJECT_ROOT/build"
    local cpp_success=true
    
    # 1. 端到端集成测试
    if [ -f "$build_dir/tests/integration/end_to_end_integration_test" ]; then
        log_info "Running C++ end-to-end integration tests..."
        if timeout 600 "$build_dir/tests/integration/end_to_end_integration_test" > "$RESULTS_DIR/cpp_e2e_$TIMESTAMP.log" 2>&1; then
            log_success "C++ end-to-end integration tests passed"
            TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_e2e = {"success": true, "log_file": "cpp_e2e_'$TIMESTAMP'.log"}')
        else
            log_error "C++ end-to-end integration tests failed"
            TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_e2e = {"success": false, "log_file": "cpp_e2e_'$TIMESTAMP'.log"}')
            cpp_success=false
        fi
    else
        log_warning "C++ end-to-end test executable not found, skipping"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_e2e = {"success": false, "error": "executable not found", "skipped": true}')
    fi
    
    # 2. 数据一致性验证
    if [ -f "$build_dir/tests/integration/data_consistency_validator" ]; then
        log_info "Running C++ data consistency validation..."
        if timeout 900 "$build_dir/tests/integration/data_consistency_validator" > "$RESULTS_DIR/cpp_consistency_$TIMESTAMP.log" 2>&1; then
            log_success "C++ data consistency validation passed"
            TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_consistency = {"success": true, "log_file": "cpp_consistency_'$TIMESTAMP'.log"}')
        else
            log_error "C++ data consistency validation failed"
            TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_consistency = {"success": false, "log_file": "cpp_consistency_'$TIMESTAMP'.log"}')
            cpp_success=false
        fi
    else
        log_warning "C++ consistency validator executable not found, skipping"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_consistency = {"success": false, "error": "executable not found", "skipped": true}')
    fi
    
    # 3. 性能压力测试
    if [ -f "$build_dir/tests/integration/performance_stress_test" ]; then
        log_info "Running C++ performance stress tests..."
        if timeout 1800 "$build_dir/tests/integration/performance_stress_test" > "$RESULTS_DIR/cpp_performance_$TIMESTAMP.log" 2>&1; then
            log_success "C++ performance stress tests passed"
            TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_performance = {"success": true, "log_file": "cpp_performance_'$TIMESTAMP'.log"}')
        else
            log_error "C++ performance stress tests failed"
            TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_performance = {"success": false, "log_file": "cpp_performance_'$TIMESTAMP'.log"}')
            cpp_success=false
        fi
    else
        log_warning "C++ performance test executable not found, skipping"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_performance = {"success": false, "error": "executable not found", "skipped": true}')
    fi
    
    if $cpp_success; then
        log_success "All available C++ tests passed"
        return 0
    else
        log_error "Some C++ tests failed"
        return 1
    fi
}

# 生成综合报告
generate_report() {
    log_info "Generating comprehensive test report..."
    
    # 添加总结信息
    local end_time=$(date -Iseconds)
    local total_tests=$(echo "$TEST_RESULTS" | jq '.tests | length')
    local passed_tests=$(echo "$TEST_RESULTS" | jq '[.tests[] | select(.success == true)] | length')
    local failed_tests=$(echo "$TEST_RESULTS" | jq '[.tests[] | select(.success == false and (.skipped // false) == false)] | length')
    local skipped_tests=$(echo "$TEST_RESULTS" | jq '[.tests[] | select(.skipped // false == true)] | length')
    
    TEST_RESULTS=$(echo "$TEST_RESULTS" | jq --arg end_time "$end_time" \
        --argjson total_tests "$total_tests" \
        --argjson passed_tests "$passed_tests" \
        --argjson failed_tests "$failed_tests" \
        --argjson skipped_tests "$skipped_tests" \
        '. + {
            "end_time": $end_time,
            "summary": {
                "total_tests": $total_tests,
                "passed_tests": $passed_tests,
                "failed_tests": $failed_tests,
                "skipped_tests": $skipped_tests,
                "success_rate": (($passed_tests / ($total_tests - $skipped_tests)) * 100)
            }
        }')
    
    # 保存报告
    echo "$TEST_RESULTS" | jq '.' > "$REPORT_FILE"
    
    # 打印控制台报告
    echo
    echo "================================================================================"
    echo "                    COMPREHENSIVE INTEGRATION TEST REPORT"
    echo "================================================================================"
    echo "Timestamp: $(date)"
    echo "Report File: $REPORT_FILE"
    echo
    echo "Test Summary:"
    echo "  Total Tests: $total_tests"
    echo "  Passed: $passed_tests"
    echo "  Failed: $failed_tests"
    echo "  Skipped: $skipped_tests"
    echo "  Success Rate: $(echo "$TEST_RESULTS" | jq -r '.summary.success_rate')%"
    echo
    echo "Individual Test Results:"
    echo "$TEST_RESULTS" | jq -r '.tests | to_entries[] | "  \(.key): \(if .value.success then "PASSED" elif (.value.skipped // false) then "SKIPPED" else "FAILED" end)"'
    echo
    
    if [ "$failed_tests" -gt 0 ]; then
        echo "Failed Test Details:"
        echo "$TEST_RESULTS" | jq -r '.tests | to_entries[] | select(.value.success == false and (.value.skipped // false) == false) | "  \(.key): \(.value.error // "Check log file: \(.value.log_file)")"'
        echo
    fi
    
    echo "Log Files Location: $RESULTS_DIR"
    echo "================================================================================"
    
    log_success "Comprehensive test report generated: $REPORT_FILE"
}

# 清理函数
cleanup() {
    log_info "Cleaning up test environment..."
    
    # 清理临时文件
    find "$PROJECT_ROOT" -name "*.tmp" -delete 2>/dev/null || true
    find "$PROJECT_ROOT" -name "core.*" -delete 2>/dev/null || true
    
    # 清理测试数据库
    if command -v redis-cli &> /dev/null; then
        redis-cli -n 1 FLUSHDB &> /dev/null || true
    fi
    
    log_success "Cleanup completed"
}

# 主执行流程
main() {
    local overall_success=true
    
    # 设置错误处理
    trap cleanup EXIT
    
    # 检查环境
    if ! check_environment; then
        log_error "Environment check failed"
        exit 1
    fi
    
    # 运行Python测试
    if ! run_python_tests; then
        overall_success=false
    fi
    
    # 构建并运行C++测试
    if build_cpp_tests; then
        if ! run_cpp_tests; then
            overall_success=false
        fi
    else
        log_warning "C++ tests skipped due to build failure"
        TEST_RESULTS=$(echo "$TEST_RESULTS" | jq '.tests.cpp_build = {"success": false, "error": "build failed", "skipped": true}')
    fi
    
    # 生成报告
    generate_report
    
    # 返回结果
    if $overall_success; then
        log_success "All integration tests completed successfully"
        exit 0
    else
        log_error "Some integration tests failed"
        exit 1
    fi
}

# 检查jq是否可用
if ! command -v jq &> /dev/null; then
    log_error "jq is required but not installed. Please install jq to run this script."
    exit 1
fi

# 运行主函数
main "$@"