version: '3.8'

services:
  # Market Data Collection Service
  market-data-collector:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.market-data-collector
    container_name: market-data-collector
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - CONFIG_PATH=/app/config/unified_config.json
      - REDIS_HOST=redis-cluster
      - CLICKHOUSE_HOST=clickhouse-cluster
      - S3_ENDPOINT=minio-cluster
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "8080:8080"  # HTTP API
      - "8081:8081"  # gRPC API
      - "9090:9090"  # Metrics
    depends_on:
      - redis-cluster
      - clickhouse-cluster
      - minio-cluster
    networks:
      - market-data-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Cluster for Hot Storage
  redis-cluster:
    image: redis:7-alpine
    container_name: redis-cluster
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ../../config/redis-cluster.conf:/usr/local/etc/redis/redis.conf:ro
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - market-data-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # ClickHouse for Warm Storage
  clickhouse-cluster:
    image: clickhouse/clickhouse-server:23.8
    container_name: clickhouse-cluster
    restart: unless-stopped
    environment:
      - CLICKHOUSE_DB=market_data
      - CLICKHOUSE_USER=market_user
      - CLICKHOUSE_PASSWORD=market_password
    volumes:
      - ../../config/clickhouse-cluster.xml:/etc/clickhouse-server/config.xml:ro
      - ../../config/clickhouse-users.xml:/etc/clickhouse-server/users.xml:ro
      - ../../config/clickhouse-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - clickhouse-data:/var/lib/clickhouse
    ports:
      - "8123:8123"  # HTTP
      - "9000:9000"  # Native
    networks:
      - market-data-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO for Cold Storage
  minio-cluster:
    image: minio/minio:latest
    container_name: minio-cluster
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio-data:/data
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    networks:
      - market-data-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ../../config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    ports:
      - "9091:9090"
    networks:
      - market-data-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ../../config/grafana_dashboard.json:/etc/grafana/provisioning/dashboards/dashboard.json:ro
    ports:
      - "3000:3000"
    networks:
      - market-data-network
    depends_on:
      - prometheus

volumes:
  redis-data:
  clickhouse-data:
  minio-data:
  prometheus-data:
  grafana-data:

networks:
  market-data-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16