#include "clickhouse_storage.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <thread>
#include <chrono>
#include <clickhouse/columns/date.h>
#include <clickhouse/columns/datetime.h>
#include <clickhouse/columns/datetime64.h>
#include <clickhouse/columns/numeric.h>
#include <clickhouse/columns/string.h>
#include <clickhouse/columns/array.h>
#include <clickhouse/columns/lowcardinality.h>

namespace financial_data {

ClickHouseStorage::ClickHouseStorage(const ClickHouseConfig& config)
    : config_(config), connected_(false) {
    // Initialize performance metrics
    metrics_ = {};
}

ClickHouseStorage::~ClickHouseStorage() {
    stop_batch_processing_ = true;
    batch_cv_.notify_all();
    
    if (batch_processor_.joinable()) {
        batch_processor_.join();
    }
    
    Disconnect();
}

bool ClickHouseStorage::Initialize() {
    try {
        // Create ClickHouse client options
        clickhouse::ClientOptions options;
        options.SetHost(config_.host);
        options.SetPort(config_.port);
        options.SetUser(config_.username);
        options.SetPassword(config_.password);
        options.SetDefaultDatabase(config_.database);
        options.SetPingBeforeQuery(true);
        options.SetCompressionMethod(clickhouse::CompressionMethod::LZ4);
        
        // Create client
        client_ = std::make_unique<clickhouse::Client>(options);
        
        // Test connection
        auto result = client_->Select("SELECT 1");
        if (result->GetRowCount() > 0) {
            connected_ = true;
            std::cout << "ClickHouse connection established successfully" << std::endl;
            
            // Start batch processor
            batch_processor_ = std::thread(&ClickHouseStorage::ProcessBatches, this);
            
            return true;
        }
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize ClickHouse connection: " << e.what() << std::endl;
        connected_ = false;
    }
    
    return false;
}

bool ClickHouseStorage::IsConnected() const {
    return connected_.load();
}

void ClickHouseStorage::Disconnect() {
    connected_ = false;
    client_.reset();
}

bool ClickHouseStorage::Reconnect() {
    Disconnect();
    return Initialize();
}

std::future<bool> ClickHouseStorage::InsertTickDataBatch(const DataBatch<StandardizedTick>& batch) {
    auto promise = std::make_shared<std::promise<bool>>();
    auto future = promise->get_future();
    
    if (batch.Empty()) {
        promise->set_value(true);
        return future;
    }
    
    std::lock_guard<std::mutex> lock(batch_mutex_);
    tick_batch_queue_.emplace(batch, std::move(*promise));
    batch_cv_.notify_one();
    
    return future;
}

std::future<bool> ClickHouseStorage::InsertKlineDataBatch(const DataBatch<KlineData>& batch) {
    auto promise = std::make_shared<std::promise<bool>>();
    auto future = promise->get_future();
    
    if (batch.Empty()) {
        promise->set_value(true);
        return future;
    }
    
    std::lock_guard<std::mutex> lock(batch_mutex_);
    kline_batch_queue_.emplace(batch, std::move(*promise));
    batch_cv_.notify_one();
    
    return future;
}

bool ClickHouseStorage::InsertTickData(const StandardizedTick& tick) {
    DataBatch<StandardizedTick> batch;
    batch.Add(tick);
    
    auto future = InsertTickDataBatch(batch);
    return future.get();
}

std::string ClickHouseStorage::GetTableName(const std::string& product_type, const std::string& data_type) {
    if (data_type == "tick") {
        if (product_type == "futures") return "futures_tick";
        else if (product_type == "stock") return "stock_tick";
        else if (product_type == "option") return "options_tick";
        else if (product_type == "forex") return "forex_tick";
    } else if (data_type.find("kline") == 0) {
        return data_type; // kline_1m, kline_5m, etc.
    }
    
    return "futures_tick"; // default
}

clickhouse::Block ClickHouseStorage::CreateTickDataBlock(
    const DataBatch<StandardizedTick>& batch, 
    const std::string& product_type) {
    
    const auto& data = batch.GetData();
    size_t size = data.size();
    
    clickhouse::Block block;
    
    // Common columns for all product types
    auto timestamp_col = std::make_shared<clickhouse::ColumnDateTime64>(9);
    auto symbol_col = std::make_shared<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>>();
    auto exchange_col = std::make_shared<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>>();
    auto last_price_col = std::make_shared<clickhouse::ColumnFloat64>();
    auto volume_col = std::make_shared<clickhouse::ColumnUInt64>();
    auto turnover_col = std::make_shared<clickhouse::ColumnFloat64>();
    auto sequence_col = std::make_shared<clickhouse::ColumnUInt32>();
    auto trade_flag_col = std::make_shared<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>>();
    
    // Bid/Ask arrays
    auto bid_prices_col = std::make_shared<clickhouse::ColumnArray>(
        std::make_shared<clickhouse::ColumnFloat64>());
    auto bid_volumes_col = std::make_shared<clickhouse::ColumnArray>(
        std::make_shared<clickhouse::ColumnUInt32>());
    auto ask_prices_col = std::make_shared<clickhouse::ColumnArray>(
        std::make_shared<clickhouse::ColumnFloat64>());
    auto ask_volumes_col = std::make_shared<clickhouse::ColumnArray>(
        std::make_shared<clickhouse::ColumnUInt32>());
    
    // Product-specific columns
    std::shared_ptr<clickhouse::ColumnUInt64> open_interest_col;
    std::shared_ptr<clickhouse::ColumnFloat64> settlement_price_col;
    std::shared_ptr<clickhouse::ColumnFloat64> pre_settlement_col;
    std::shared_ptr<clickhouse::ColumnFloat64> pre_close_price_col;
    std::shared_ptr<clickhouse::ColumnUInt64> pre_open_interest_col;
    
    if (product_type == "futures" || product_type == "option") {
        open_interest_col = std::make_shared<clickhouse::ColumnUInt64>();
        settlement_price_col = std::make_shared<clickhouse::ColumnFloat64>();
        pre_settlement_col = std::make_shared<clickhouse::ColumnFloat64>();
        pre_close_price_col = std::make_shared<clickhouse::ColumnFloat64>();
        pre_open_interest_col = std::make_shared<clickhouse::ColumnUInt64>();
    }
    
    // Options-specific columns
    std::shared_ptr<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>> underlying_col;
    std::shared_ptr<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>> option_type_col;
    std::shared_ptr<clickhouse::ColumnFloat64> strike_price_col;
    std::shared_ptr<clickhouse::ColumnDate> expiry_date_col;
    std::shared_ptr<clickhouse::ColumnFloat64> implied_vol_col;
    std::shared_ptr<clickhouse::ColumnFloat64> delta_col, gamma_col, theta_col, vega_col;
    
    if (product_type == "option") {
        underlying_col = std::make_shared<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>>();
        option_type_col = std::make_shared<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>>();
        strike_price_col = std::make_shared<clickhouse::ColumnFloat64>();
        expiry_date_col = std::make_shared<clickhouse::ColumnDate>();
        implied_vol_col = std::make_shared<clickhouse::ColumnFloat64>();
        delta_col = std::make_shared<clickhouse::ColumnFloat64>();
        gamma_col = std::make_shared<clickhouse::ColumnFloat64>();
        theta_col = std::make_shared<clickhouse::ColumnFloat64>();
        vega_col = std::make_shared<clickhouse::ColumnFloat64>();
    }
    
    // Forex-specific columns
    std::shared_ptr<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>> base_currency_col;
    std::shared_ptr<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>> quote_currency_col;
    std::shared_ptr<clickhouse::ColumnFloat64> spread_col;
    
    if (product_type == "forex") {
        base_currency_col = std::make_shared<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>>();
        quote_currency_col = std::make_shared<clickhouse::ColumnLowCardinalityT<clickhouse::ColumnString>>();
        spread_col = std::make_shared<clickhouse::ColumnFloat64>();
    }
    
    // Fill data
    for (const auto& tick : data) {
        // Convert nanosecond timestamp to DateTime64(9)
        timestamp_col->Append(tick.timestamp_ns / 1000000000ULL, (tick.timestamp_ns % 1000000000ULL));
        symbol_col->Append(tick.symbol);
        exchange_col->Append(tick.exchange);
        last_price_col->Append(tick.last_price);
        volume_col->Append(tick.volume);
        turnover_col->Append(tick.turnover);
        sequence_col->Append(tick.sequence);
        trade_flag_col->Append(tick.trade_flag);
        
        // Bid/Ask arrays
        auto bid_prices_array = std::make_shared<clickhouse::ColumnFloat64>();
        auto bid_volumes_array = std::make_shared<clickhouse::ColumnUInt32>();
        auto ask_prices_array = std::make_shared<clickhouse::ColumnFloat64>();
        auto ask_volumes_array = std::make_shared<clickhouse::ColumnUInt32>();
        
        for (double price : tick.bid_prices) bid_prices_array->Append(price);
        for (uint32_t vol : tick.bid_volumes) bid_volumes_array->Append(vol);
        for (double price : tick.ask_prices) ask_prices_array->Append(price);
        for (uint32_t vol : tick.ask_volumes) ask_volumes_array->Append(vol);
        
        bid_prices_col->AppendAsColumn(bid_prices_array);
        bid_volumes_col->AppendAsColumn(bid_volumes_array);
        ask_prices_col->AppendAsColumn(ask_prices_array);
        ask_volumes_col->AppendAsColumn(ask_volumes_array);
        
        // Product-specific data
        if (product_type == "futures" || product_type == "option") {
            open_interest_col->Append(tick.open_interest);
            settlement_price_col->Append(tick.settlement_price);
            pre_settlement_col->Append(tick.pre_settlement);
            pre_close_price_col->Append(tick.pre_close_price);
            pre_open_interest_col->Append(tick.pre_open_interest);
        }
        
        if (product_type == "option") {
            underlying_col->Append(tick.underlying_symbol);
            option_type_col->Append(tick.option_type);
            strike_price_col->Append(tick.strike_price);
            
            // Parse expiry date (assuming YYYY-MM-DD format)
            if (!tick.expiry_date.empty()) {
                std::tm tm = {};
                std::istringstream ss(tick.expiry_date);
                ss >> std::get_time(&tm, "%Y-%m-%d");
                auto time_point = std::chrono::system_clock::from_time_t(std::mktime(&tm));
                auto days_since_epoch = std::chrono::duration_cast<std::chrono::days>(
                    time_point.time_since_epoch()).count();
                expiry_date_col->Append(static_cast<uint16_t>(days_since_epoch));
            } else {
                expiry_date_col->Append(0);
            }
            
            implied_vol_col->Append(tick.implied_volatility);
            delta_col->Append(tick.delta);
            gamma_col->Append(tick.gamma);
            theta_col->Append(tick.theta);
            vega_col->Append(tick.vega);
        }
        
        if (product_type == "forex") {
            base_currency_col->Append(tick.base_currency);
            quote_currency_col->Append(tick.quote_currency);
            spread_col->Append(tick.spread);
        }
    }
    
    // Add columns to block
    block.AppendColumn("timestamp", timestamp_col);
    block.AppendColumn("symbol", symbol_col);
    block.AppendColumn("exchange", exchange_col);
    block.AppendColumn("last_price", last_price_col);
    block.AppendColumn("volume", volume_col);
    block.AppendColumn("turnover", turnover_col);
    block.AppendColumn("bid_prices", bid_prices_col);
    block.AppendColumn("bid_volumes", bid_volumes_col);
    block.AppendColumn("ask_prices", ask_prices_col);
    block.AppendColumn("ask_volumes", ask_volumes_col);
    block.AppendColumn("sequence", sequence_col);
    block.AppendColumn("trade_flag", trade_flag_col);
    
    if (product_type == "futures" || product_type == "option") {
        block.AppendColumn("open_interest", open_interest_col);
        block.AppendColumn("settlement_price", settlement_price_col);
        block.AppendColumn("pre_settlement", pre_settlement_col);
        block.AppendColumn("pre_close_price", pre_close_price_col);
        block.AppendColumn("pre_open_interest", pre_open_interest_col);
    }
    
    if (product_type == "option") {
        block.AppendColumn("underlying_symbol", underlying_col);
        block.AppendColumn("option_type", option_type_col);
        block.AppendColumn("strike_price", strike_price_col);
        block.AppendColumn("expiry_date", expiry_date_col);
        block.AppendColumn("implied_volatility", implied_vol_col);
        block.AppendColumn("delta", delta_col);
        block.AppendColumn("gamma", gamma_col);
        block.AppendColumn("theta", theta_col);
        block.AppendColumn("vega", vega_col);
    }
    
    if (product_type == "forex") {
        block.AppendColumn("base_currency", base_currency_col);
        block.AppendColumn("quote_currency", quote_currency_col);
        block.AppendColumn("spread", spread_col);
    }
    
    return block;
}

void ClickHouseStorage::ProcessBatches() {
    while (!stop_batch_processing_) {
        std::unique_lock<std::mutex> lock(batch_mutex_);
        
        // Wait for batches or stop signal
        batch_cv_.wait(lock, [this] {
            return !tick_batch_queue_.empty() || !kline_batch_queue_.empty() || stop_batch_processing_;
        });
        
        if (stop_batch_processing_) {
            break;
        }
        
        // Process tick data batches
        while (!tick_batch_queue_.empty()) {
            auto batch_item = std::move(tick_batch_queue_.front());
            tick_batch_queue_.pop();
            lock.unlock();
            
            auto start_time = std::chrono::steady_clock::now();
            bool success = false;
            
            try {
                if (!batch_item.first.Empty() && IsConnected()) {
                    // Group by product type for efficient insertion
                    std::map<std::string, DataBatch<StandardizedTick>> product_batches;
                    
                    for (const auto& tick : batch_item.first.GetData()) {
                        product_batches[tick.product_type].Add(tick);
                    }
                    
                    // Insert each product type batch
                    for (const auto& [product_type, batch] : product_batches) {
                        std::string table_name = GetTableName(product_type, "tick");
                        clickhouse::Block block = CreateTickDataBlock(batch, product_type);
                        
                        client_->Insert(table_name, block);
                    }
                    
                    success = true;
                }
            } catch (const std::exception& e) {
                std::cerr << "Failed to insert tick data batch: " << e.what() << std::endl;
                HandleConnectionError();
            }
            
            auto end_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            UpdateMetrics(true, success, duration);
            batch_item.second.set_value(success);
            
            lock.lock();
        }
        
        // Process kline data batches (similar implementation)
        // ... (implementation omitted for brevity)
    }
}

QueryResult<StandardizedTick> ClickHouseStorage::QueryTickData(
    const std::string& symbol,
    const std::string& exchange,
    int64_t start_timestamp,
    int64_t end_timestamp,
    size_t limit,
    const std::string& cursor) {
    
    QueryResult<StandardizedTick> result;
    
    if (!IsConnected()) {
        return result;
    }
    
    auto start_time = std::chrono::steady_clock::now();
    
    try {
        // Build query
        std::ostringstream query;
        query << "SELECT timestamp, symbol, exchange, last_price, volume, turnover, "
              << "bid_prices, bid_volumes, ask_prices, ask_volumes, sequence, trade_flag ";
        
        // Determine product type and table
        std::string table_name = "futures_tick"; // Default, should be determined by symbol lookup
        query << "FROM " << table_name << " ";
        
        query << "WHERE symbol = '" << symbol << "' ";
        query << "AND exchange = '" << exchange << "' ";
        query << "AND timestamp >= toDateTime64(" << start_timestamp / 1000000000.0 << ", 9) ";
        query << "AND timestamp <= toDateTime64(" << end_timestamp / 1000000000.0 << ", 9) ";
        
        if (!cursor.empty()) {
            query << "AND timestamp > toDateTime64(" << std::stoll(cursor) / 1000000000.0 << ", 9) ";
        }
        
        query << "ORDER BY timestamp ";
        query << "LIMIT " << (limit + 1); // +1 to check if there are more results
        
        auto block = ExecuteSelectQuery(query.str());
        
        // Parse results
        if (block.GetRowCount() > 0) {
            size_t rows_to_process = std::min(block.GetRowCount(), limit);
            result.has_more = (block.GetRowCount() > limit);
            
            for (size_t i = 0; i < rows_to_process; ++i) {
                StandardizedTick tick;
                
                // Extract data from block columns
                auto timestamp_col = block.GetColumn("timestamp");
                auto symbol_col = block.GetColumn("symbol");
                auto exchange_col = block.GetColumn("exchange");
                auto last_price_col = block.GetColumn("last_price");
                auto volume_col = block.GetColumn("volume");
                auto turnover_col = block.GetColumn("turnover");
                auto sequence_col = block.GetColumn("sequence");
                auto trade_flag_col = block.GetColumn("trade_flag");
                
                // Convert timestamp back to nanoseconds
                auto dt64_col = std::dynamic_pointer_cast<clickhouse::ColumnDateTime64>(timestamp_col);
                if (dt64_col) {
                    auto [seconds, subseconds] = dt64_col->At(i);
                    tick.timestamp_ns = seconds * 1000000000ULL + subseconds;
                }
                
                // Extract other fields
                tick.symbol = std::dynamic_pointer_cast<clickhouse::ColumnString>(symbol_col)->At(i);
                tick.exchange = std::dynamic_pointer_cast<clickhouse::ColumnString>(exchange_col)->At(i);
                tick.last_price = std::dynamic_pointer_cast<clickhouse::ColumnFloat64>(last_price_col)->At(i);
                tick.volume = std::dynamic_pointer_cast<clickhouse::ColumnUInt64>(volume_col)->At(i);
                tick.turnover = std::dynamic_pointer_cast<clickhouse::ColumnFloat64>(turnover_col)->At(i);
                tick.sequence = std::dynamic_pointer_cast<clickhouse::ColumnUInt32>(sequence_col)->At(i);
                tick.trade_flag = std::dynamic_pointer_cast<clickhouse::ColumnString>(trade_flag_col)->At(i);
                
                // Extract bid/ask arrays
                auto bid_prices_col = std::dynamic_pointer_cast<clickhouse::ColumnArray>(block.GetColumn("bid_prices"));
                auto bid_volumes_col = std::dynamic_pointer_cast<clickhouse::ColumnArray>(block.GetColumn("bid_volumes"));
                auto ask_prices_col = std::dynamic_pointer_cast<clickhouse::ColumnArray>(block.GetColumn("ask_prices"));
                auto ask_volumes_col = std::dynamic_pointer_cast<clickhouse::ColumnArray>(block.GetColumn("ask_volumes"));
                
                if (bid_prices_col && bid_volumes_col && ask_prices_col && ask_volumes_col) {
                    auto bid_prices_array = bid_prices_col->GetAsColumn(i);
                    auto bid_volumes_array = bid_volumes_col->GetAsColumn(i);
                    auto ask_prices_array = ask_prices_col->GetAsColumn(i);
                    auto ask_volumes_array = ask_volumes_col->GetAsColumn(i);
                    
                    // Convert arrays to vectors
                    for (size_t j = 0; j < bid_prices_array->Size(); ++j) {
                        tick.bid_prices.push_back(
                            std::dynamic_pointer_cast<clickhouse::ColumnFloat64>(bid_prices_array)->At(j));
                        tick.bid_volumes.push_back(
                            std::dynamic_pointer_cast<clickhouse::ColumnUInt32>(bid_volumes_array)->At(j));
                    }
                    
                    for (size_t j = 0; j < ask_prices_array->Size(); ++j) {
                        tick.ask_prices.push_back(
                            std::dynamic_pointer_cast<clickhouse::ColumnFloat64>(ask_prices_array)->At(j));
                        tick.ask_volumes.push_back(
                            std::dynamic_pointer_cast<clickhouse::ColumnUInt32>(ask_volumes_array)->At(j));
                    }
                }
                
                result.data.push_back(tick);
            }
            
            // Set next cursor if there are more results
            if (result.has_more && !result.data.empty()) {
                result.next_cursor = std::to_string(result.data.back().timestamp_ns);
            }
        }
        
        result.total_rows = result.data.size();
        
    } catch (const std::exception& e) {
        std::cerr << "Failed to query tick data: " << e.what() << std::endl;
        HandleConnectionError();
    }
    
    auto end_time = std::chrono::steady_clock::now();
    result.query_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    UpdateMetrics(false, !result.data.empty(), result.query_time);
    
    return result;
}

bool ClickHouseStorage::ExecuteQuery(const std::string& query) {
    if (!IsConnected()) {
        return false;
    }
    
    try {
        client_->Execute(query);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to execute query: " << e.what() << std::endl;
        HandleConnectionError();
        return false;
    }
}

clickhouse::Block ClickHouseStorage::ExecuteSelectQuery(const std::string& query) {
    if (!IsConnected()) {
        return clickhouse::Block();
    }
    
    try {
        return client_->Select(query);
    } catch (const std::exception& e) {
        std::cerr << "Failed to execute select query: " << e.what() << std::endl;
        HandleConnectionError();
        return clickhouse::Block();
    }
}

void ClickHouseStorage::HandleConnectionError() {
    connected_ = false;
    
    // Attempt to reconnect
    std::thread([this]() {
        std::this_thread::sleep_for(config_.retry_delay);
        Reconnect();
    }).detach();
}

void ClickHouseStorage::UpdateMetrics(bool is_insert, bool success, std::chrono::milliseconds duration) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    if (is_insert) {
        metrics_.total_inserts++;
        if (!success) metrics_.failed_inserts++;
        
        // Update average insert time
        auto total_time = metrics_.avg_insert_time * (metrics_.total_inserts - 1) + duration;
        metrics_.avg_insert_time = total_time / metrics_.total_inserts;
    } else {
        metrics_.total_queries++;
        if (!success) metrics_.failed_queries++;
        
        // Update average query time
        auto total_time = metrics_.avg_query_time * (metrics_.total_queries - 1) + duration;
        metrics_.avg_query_time = total_time / metrics_.total_queries;
    }
}

ClickHouseStorage::PerformanceMetrics ClickHouseStorage::GetMetrics() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    return metrics_;
}

void ClickHouseStorage::ResetMetrics() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    metrics_ = {};
}

std::vector<std::string> ClickHouseStorage::GetClusterNodes() {
    std::vector<std::string> nodes;
    
    if (!IsConnected()) {
        return nodes;
    }
    
    try {
        std::string query = "SELECT host_name, port FROM system.clusters WHERE cluster = '" + config_.cluster_name + "'";
        auto block = ExecuteSelectQuery(query);
        
        for (size_t i = 0; i < block.GetRowCount(); ++i) {
            auto host_col = std::dynamic_pointer_cast<clickhouse::ColumnString>(block.GetColumn("host_name"));
            auto port_col = std::dynamic_pointer_cast<clickhouse::ColumnUInt16>(block.GetColumn("port"));
            
            if (host_col && port_col) {
                std::string node = host_col->At(i) + ":" + std::to_string(port_col->At(i));
                nodes.push_back(node);
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Failed to get cluster nodes: " << e.what() << std::endl;
    }
    
    return nodes;
}

bool ClickHouseStorage::CheckClusterHealth() {
    if (!IsConnected()) {
        return false;
    }
    
    try {
        std::string query = "SELECT count() FROM system.clusters WHERE cluster = '" + config_.cluster_name + "'";
        auto block = ExecuteSelectQuery(query);
        
        if (block.GetRowCount() > 0) {
            auto count_col = std::dynamic_pointer_cast<clickhouse::ColumnUInt64>(block.GetColumn("count()"));
            return count_col && count_col->At(0) > 0;
        }
    } catch (const std::exception& e) {
        std::cerr << "Failed to check cluster health: " << e.what() << std::endl;
    }
    
    return false;
}

} // namespace financial_data