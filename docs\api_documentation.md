# Financial Data Service API Documentation

## Overview

The Financial Data Service API provides RESTful endpoints for querying historical market data including tick data, K-line data, and Level-2 market depth data. The API is built with FastAPI and supports high-performance queries with caching, pagination, and multiple data sources.

## Features

- **Multiple Data Types**: Tick data, K-line data, Level-2 market depth
- **Multi-tier Storage**: Hot (Redis), Warm (ClickHouse), Cold (MinIO/S3) storage layers
- **High Performance**: Sub-millisecond query response times with caching
- **Pagination**: Cursor-based pagination for large datasets
- **Comprehensive Filtering**: Time range, symbol, exchange, and data type filtering
- **Auto-generated Documentation**: OpenAPI/Swagger documentation
- **Error Handling**: Comprehensive error responses with detailed messages

## Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Start the API server
python src/api/run_server.py
```

### 2. Access Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 3. Basic Usage

```python
import aiohttp
import asyncio

async def get_tick_data():
    async with aiohttp.ClientSession() as session:
        async with session.get(
            "http://localhost:8000/api/v1/tick-data",
            params={"symbol": "CU2409", "exchange": "SHFE", "limit": 100}
        ) as response:
            data = await response.json()
            print(f"Retrieved {len(data['data'])} tick records")

asyncio.run(get_tick_data())
```

## API Endpoints

### Health Check

**GET** `/health`

Check API server health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-25T08:30:00.000Z"
}
```

### Tick Data

**GET** `/api/v1/tick-data`

Query historical tick data with microsecond precision.

**Parameters:**
- `symbol` (required): Contract symbol (e.g., "CU2409")
- `exchange` (optional): Exchange code (SHFE, DCE, CZCE, CFFEX, SSE, SZSE)
- `start_time` (optional): Start timestamp in ISO format
- `end_time` (optional): End timestamp in ISO format
- `limit` (optional): Maximum records (1-10000, default: 1000)
- `cursor` (optional): Pagination cursor for next page

**Example:**
```bash
curl "http://localhost:8000/api/v1/tick-data?symbol=CU2409&exchange=SHFE&limit=5"
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "timestamp": 1721446200123456789,
      "symbol": "CU2409",
      "exchange": "SHFE",
      "last_price": 78560.0,
      "volume": 12580,
      "turnover": **********.0,
      "open_interest": 156789,
      "bids": [
        {"price": 78550.0, "volume": 10, "order_count": 5}
      ],
      "asks": [
        {"price": 78570.0, "volume": 8, "order_count": 3}
      ],
      "sequence": 123456,
      "trade_flag": "buy_open"
    }
  ],
  "pagination": {
    "has_next": true,
    "next_cursor": "bmV4dF9jdXJzb3I=",
    "page_size": 1
  },
  "message": "Retrieved 1 tick records from hot storage",
  "timestamp": "2025-07-25T08:30:00.000Z"
}
```

### K-line Data

**GET** `/api/v1/kline-data`

Query historical K-line (candlestick) data.

**Parameters:**
- `symbol` (required): Contract symbol
- `period` (optional): K-line period (1m, 5m, 15m, 30m, 1h, 4h, 1d, default: 1m)
- `exchange` (optional): Exchange code
- `start_time` (optional): Start timestamp in ISO format
- `end_time` (optional): End timestamp in ISO format
- `limit` (optional): Maximum records (1-10000, default: 1000)
- `cursor` (optional): Pagination cursor

**Example:**
```bash
curl "http://localhost:8000/api/v1/kline-data?symbol=CU2409&period=1m&limit=5"
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "timestamp": 1721446200000000000,
      "symbol": "CU2409",
      "exchange": "SHFE",
      "period": "1m",
      "open": 78550.0,
      "high": 78580.0,
      "low": 78540.0,
      "close": 78560.0,
      "volume": 1258,
      "turnover": 98765432.0,
      "open_interest": 156789
    }
  ],
  "pagination": {
    "has_next": false,
    "next_cursor": null,
    "page_size": 1
  }
}
```

### Level-2 Data

**GET** `/api/v1/level2-data`

Query Level-2 market depth data with order book information.

**Parameters:**
- `symbol` (required): Contract symbol
- `exchange` (optional): Exchange code
- `start_time` (optional): Start timestamp in ISO format
- `end_time` (optional): End timestamp in ISO format
- `limit` (optional): Maximum records (1-5000, default: 1000)
- `cursor` (optional): Pagination cursor

**Example:**
```bash
curl "http://localhost:8000/api/v1/level2-data?symbol=CU2409&limit=5"
```

### Symbols

**GET** `/api/v1/symbols`

Get available symbols/contracts.

**Parameters:**
- `exchange` (optional): Filter by exchange
- `product_type` (optional): Filter by product type (futures, stock, option, forex)

**Example:**
```bash
curl "http://localhost:8000/api/v1/symbols?exchange=SHFE&product_type=futures"
```

### Exchanges

**GET** `/api/v1/exchanges`

Get list of available exchanges.

**Example:**
```bash
curl "http://localhost:8000/api/v1/exchanges"
```

**Response:**
```json
["SHFE", "DCE", "CZCE", "CFFEX", "SSE", "SZSE"]
```

## Data Models

### Tick Data Model

```json
{
  "timestamp": 1721446200123456789,  // Nanosecond timestamp
  "symbol": "CU2409",               // Contract symbol
  "exchange": "SHFE",               // Exchange code
  "last_price": 78560.0,            // Last traded price
  "volume": 12580,                  // Cumulative volume
  "turnover": **********.0,         // Cumulative turnover
  "open_interest": 156789,          // Open interest (optional)
  "bids": [                         // Bid levels
    {
      "price": 78550.0,
      "volume": 10,
      "order_count": 5
    }
  ],
  "asks": [                         // Ask levels
    {
      "price": 78570.0,
      "volume": 8,
      "order_count": 3
    }
  ],
  "sequence": 123456,               // Sequence number
  "trade_flag": "buy_open"          // Trade flag (optional)
}
```

### K-line Data Model

```json
{
  "timestamp": 1721446200000000000,  // Nanosecond timestamp
  "symbol": "CU2409",               // Contract symbol
  "exchange": "SHFE",               // Exchange code
  "period": "1m",                   // K-line period
  "open": 78550.0,                  // Opening price
  "high": 78580.0,                  // Highest price
  "low": 78540.0,                   // Lowest price
  "close": 78560.0,                 // Closing price
  "volume": 1258,                   // Volume
  "turnover": 98765432.0,           // Turnover
  "open_interest": 156789           // Open interest (optional)
}
```

### Level-2 Data Model

```json
{
  "timestamp": 1721446200123456789,  // Nanosecond timestamp
  "symbol": "CU2409",               // Contract symbol
  "exchange": "SHFE",               // Exchange code
  "bids": [                         // Bid levels (up to 10)
    {
      "price": 78550.0,
      "volume": 10,
      "order_count": 5
    }
  ],
  "asks": [                         // Ask levels (up to 10)
    {
      "price": 78570.0,
      "volume": 8,
      "order_count": 3
    }
  ],
  "last_price": 78560.0,            // Last traded price (optional)
  "sequence": 123456                // Sequence number
}
```

## Pagination

The API uses cursor-based pagination for efficient handling of large datasets.

### Request Parameters
- `limit`: Number of records per page (default: 1000)
- `cursor`: Opaque cursor string for next page

### Response Format
```json
{
  "data": [...],
  "pagination": {
    "has_next": true,
    "next_cursor": "bmV4dF9jdXJzb3I=",
    "page_size": 1000
  }
}
```

### Usage Example
```python
# First page
response1 = await client.get("/api/v1/tick-data?symbol=CU2409&limit=1000")

# Next page
if response1["pagination"]["has_next"]:
    cursor = response1["pagination"]["next_cursor"]
    response2 = await client.get(f"/api/v1/tick-data?symbol=CU2409&limit=1000&cursor={cursor}")
```

## Caching

The API implements intelligent caching to improve query performance:

- **Tick Data**: 5-minute cache TTL
- **K-line Data**: 10-minute cache TTL  
- **Level-2 Data**: 2-minute cache TTL

Cache keys are generated based on query parameters, ensuring accurate cache hits while maintaining data freshness.

## Error Handling

### Error Response Format

```json
{
  "error": "Error message description",
  "status_code": 400,
  "timestamp": "2025-07-25T08:30:00.000Z",
  "details": {
    "additional": "error details"
  }
}
```

### Common Error Codes

- **400 Bad Request**: Invalid parameters or validation errors
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Request validation failed
- **500 Internal Server Error**: Server-side errors
- **503 Service Unavailable**: Service dependencies unavailable

### Error Examples

```bash
# Empty symbol
curl "http://localhost:8000/api/v1/tick-data?symbol="
# Response: 400 - "Symbol cannot be empty"

# Invalid period
curl "http://localhost:8000/api/v1/kline-data?symbol=CU2409&period=invalid"
# Response: 400 - "Invalid period. Must be one of: 1m, 5m, 15m, 30m, 1h, 4h, 1d"

# Invalid exchange
curl "http://localhost:8000/api/v1/tick-data?symbol=CU2409&exchange=INVALID"
# Response: 400 - "Invalid exchange. Must be one of: ['SHFE', 'DCE', 'CZCE', 'CFFEX', 'SSE', 'SZSE']"
```

## Performance Considerations

### Query Optimization

1. **Use specific time ranges** to limit data scanning
2. **Specify exchange** when known to improve query performance
3. **Use appropriate limit values** to balance performance and data needs
4. **Leverage caching** by using consistent query parameters

### Storage Tiers

The API automatically routes queries to the appropriate storage tier:

- **Hot Storage (Redis)**: Recent 7 days, sub-millisecond response
- **Warm Storage (ClickHouse)**: 2 years, optimized for analytical queries
- **Cold Storage (MinIO/S3)**: Long-term archive, batch processing

### Rate Limiting

Consider implementing rate limiting in production:
- Recommended: 1000 requests per minute per client
- Burst allowance: 100 requests per second

## Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ClickHouse Configuration
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=9000
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=market_data

# S3/MinIO Configuration
S3_ENDPOINT=http://localhost:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_BUCKET=market-data

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=false

# Cache TTL (seconds)
CACHE_TTL_TICK=300
CACHE_TTL_KLINE=600
CACHE_TTL_LEVEL2=120
```

## Development

### Running Tests

```bash
# Run all tests
python -m pytest tests/api_test.py -v

# Run specific test
python -m pytest tests/api_test.py::TestAPIEndpoints::test_get_tick_data_success -v

# Run with coverage
python -m pytest tests/api_test.py --cov=src/api --cov-report=html
```

### Code Quality

```bash
# Format code
black src/api/

# Lint code
flake8 src/api/

# Type checking
mypy src/api/
```

### Demo Script

```bash
# Run API demo
python examples/api_demo.py
```

## Production Deployment

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
EXPOSE 8000

CMD ["python", "src/api/run_server.py"]
```

### Load Balancing

For high availability, deploy multiple API instances behind a load balancer:

```nginx
upstream api_backend {
    server api1:8000;
    server api2:8000;
    server api3:8000;
}

server {
    listen 80;
    location / {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Monitoring

Recommended monitoring setup:
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **ELK Stack**: Log aggregation and analysis
- **Health checks**: Regular endpoint monitoring

## Support

For technical support and questions:
- Check the API documentation at `/docs`
- Review error messages and status codes
- Consult the source code in `src/api/`
- Run the demo script for usage examples

## License

This API is part of the Financial Data Service system. Please refer to the main project license for usage terms.