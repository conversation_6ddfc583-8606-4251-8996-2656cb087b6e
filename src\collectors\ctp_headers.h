#pragma once

// CTP API头文件包装器
// 这个文件用于统一包含CTP API的所有必要头文件

#ifdef _WIN32
    // Windows平台 - 只包含行情API相关头文件
    #include "ThostFtdcMdApi.h"
    #include "ThostFtdcUserApiStruct.h"
    #include "ThostFtdcUserApiDataType.h"
#else
    // Linux平台
    #include "ThostFtdcMdApi.h"
    #include "ThostFtdcUserApiStruct.h"
    #include "ThostFtdcUserApiDataType.h"
#endif

// 只链接行情API库，不链接交易API库
#ifdef _WIN32
    #pragma comment(lib, "thostmduserapi_se.lib")
#endif