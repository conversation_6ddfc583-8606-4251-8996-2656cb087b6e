#pragma once

#include <iostream>
#include <fstream>
#include <sstream>
#include <memory>
#include <mutex>
#include <chrono>
#include <iomanip>

namespace simple_log {

enum class Level {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
};

class Logger {
private:
    Level level_;
    std::string name_;
    mutable std::mutex mutex_;
    
public:
    Logger(const std::string& name, Level level = Level::INFO) 
        : level_(level), name_(name) {}
    
    template<typename... Args>
    void debug(const std::string& format, Args&&... args) const {
        log(Level::DEBUG, format, std::forward<Args>(args)...);
    }
    
    template<typename... Args>
    void info(const std::string& format, Args&&... args) const {
        log(Level::INFO, format, std::forward<Args>(args)...);
    }
    
    template<typename... Args>
    void warn(const std::string& format, Args&&... args) const {
        log(Level::WARN, format, std::forward<Args>(args)...);
    }
    
    template<typename... Args>
    void error(const std::string& format, Args&&... args) const {
        log(Level::ERROR, format, std::forward<Args>(args)...);
    }
    
    void set_level(Level level) { level_ = level; }

private:
    template<typename... Args>
    void log(Level msg_level, const std::string& format, Args&&... args) const {
        if (msg_level < level_) return;
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        std::cout << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
                  << "." << std::setfill('0') << std::setw(3) << ms.count() << "] "
                  << "[" << name_ << "] "
                  << "[" << level_to_string(msg_level) << "] ";
        
        print_formatted(format, std::forward<Args>(args)...);
        std::cout << std::endl;
    }
    
    void print_formatted(const std::string& format) const {
        std::cout << format;
    }
    
    template<typename T, typename... Args>
    void print_formatted(const std::string& format, T&& value, Args&&... args) const {
        size_t pos = format.find("{}");
        if (pos != std::string::npos) {
            std::cout << format.substr(0, pos) << value;
            print_formatted(format.substr(pos + 2), std::forward<Args>(args)...);
        } else {
            std::cout << format;
        }
    }
    
    const char* level_to_string(Level level) const {
        switch (level) {
            case Level::DEBUG: return "DEBUG";
            case Level::INFO:  return "INFO";
            case Level::WARN:  return "WARN";
            case Level::ERROR: return "ERROR";
            default: return "UNKNOWN";
        }
    }
};

inline std::shared_ptr<Logger> get(const std::string& name) {
    static std::shared_ptr<Logger> logger = std::make_shared<Logger>(name);
    return logger;
}

inline std::shared_ptr<Logger> stdout_color_mt(const std::string& name) {
    return std::make_shared<Logger>(name);
}

} // namespace simple_log

// Compatibility macros for spdlog
namespace spdlog = simple_log;