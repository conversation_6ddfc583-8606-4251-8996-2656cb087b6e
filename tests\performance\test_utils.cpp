/**
 * @file test_utils.cpp
 * @brief Test utilities and mock implementations for performance testing
 */

#include "test_utils.h"
#include <random>
#include <chrono>
#include <thread>
#include <iostream>
#include <algorithm>

namespace performance_tests {

// TestTick implementation
TestTick::TestTick() 
    : timestamp(0), sequence(0), last_price(0.0), volume(0), turnover(0.0) {}

size_t TestTick::GetSerializedSize() const {
    // Estimate serialized size (symbol + timestamp + price + volume + etc.)
    return symbol.length() + sizeof(timestamp) + sizeof(sequence) + 
           sizeof(last_price) + sizeof(volume) + sizeof(turnover) + 100; // Overhead
}

// MockDataBus implementation
class MockDataBus::Impl {
public:
    std::atomic<bool> active_{true};
    std::queue<TestTick> message_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    class MockConsumer {
    public:
        MockConsumer(Impl* bus) : bus_(bus) {}
        
        std::optional<TestTick> ConsumeMessage(std::chrono::milliseconds timeout) {
            std::unique_lock<std::mutex> lock(bus_->queue_mutex_);
            
            if (bus_->queue_cv_.wait_for(lock, timeout, [this] { 
                return !bus_->message_queue_.empty() || !bus_->active_; 
            })) {
                if (!bus_->message_queue_.empty()) {
                    auto message = bus_->message_queue_.front();
                    bus_->message_queue_.pop();
                    return message;
                }
            }
            return std::nullopt;
        }
        
        bool HasPendingMessages() {
            std::lock_guard<std::mutex> lock(bus_->queue_mutex_);
            return !bus_->message_queue_.empty();
        }
        
    private:
        Impl* bus_;
    };
};

MockDataBus::MockDataBus() : impl_(std::make_unique<Impl>()) {}
MockDataBus::~MockDataBus() = default;

bool MockDataBus::Publish(const TestTick& tick) {
    if (!impl_->active_) return false;
    
    {
        std::lock_guard<std::mutex> lock(impl_->queue_mutex_);
        impl_->message_queue_.push(tick);
    }
    impl_->queue_cv_.notify_one();
    return true;
}

std::unique_ptr<MockDataBus::Consumer> MockDataBus::CreateConsumer() {
    return std::make_unique<MockDataBus::Impl::MockConsumer>(impl_.get());
}

// MockWebSocketServer implementation
MockWebSocketServer::MockWebSocketServer() : active_(true), message_count_(0) {}

bool MockWebSocketServer::BroadcastTick(const TestTick& tick) {
    if (!active_) return false;
    
    message_count_++;
    // Simulate broadcast delay
    std::this_thread::sleep_for(std::chrono::microseconds(10));
    return true;
}

uint64_t MockWebSocketServer::GetProcessedMessageCount() const {
    return message_count_.load();
}

// MockWebSocketClient implementation
MockWebSocketClient::MockWebSocketClient() 
    : connected_(false), message_count_(0) {}

bool MockWebSocketClient::Connect(const std::string& url) {
    // Simulate connection time
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    connected_ = true;
    return true;
}

bool MockWebSocketClient::Subscribe(const std::vector<std::string>& symbols) {
    if (!connected_) return false;
    subscribed_symbols_ = symbols;
    return true;
}

std::optional<std::string> MockWebSocketClient::WaitForMessage(std::chrono::milliseconds timeout) {
    if (!connected_) return std::nullopt;
    
    // Simulate message reception
    std::this_thread::sleep_for(std::chrono::microseconds(50));
    message_count_++;
    return "{\"symbol\":\"CU2409\",\"price\":78560.0}";
}

uint64_t MockWebSocketClient::GetReceivedMessageCount() const {
    return message_count_.load();
}

bool MockWebSocketClient::IsConnected() const {
    return connected_.load();
}

bool MockWebSocketClient::WaitForReconnection(std::chrono::milliseconds timeout) {
    // Simulate reconnection attempt
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    connected_ = true;
    return true;
}

// MockGrpcServer implementation
MockGrpcServer::MockGrpcServer() : active_(true), message_count_(0) {}

bool MockGrpcServer::StreamTick(const TestTick& tick) {
    if (!active_) return false;
    
    message_count_++;
    // Simulate gRPC processing delay
    std::this_thread::sleep_for(std::chrono::microseconds(20));
    return true;
}

uint64_t MockGrpcServer::GetProcessedMessageCount() const {
    return message_count_.load();
}

// MockGrpcClient implementation
MockGrpcClient::MockGrpcClient() : connected_(false) {}

bool MockGrpcClient::Connect(const std::string& endpoint) {
    // Simulate gRPC connection
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    connected_ = true;
    return true;
}

std::vector<TestTick> MockGrpcClient::GetHistoricalTicks(
    const std::string& symbol,
    std::chrono::system_clock::time_point start,
    std::chrono::system_clock::time_point end,
    uint32_t limit) {
    
    if (!connected_) return {};
    
    // Simulate query processing time
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // Generate mock historical data
    std::vector<TestTick> result;
    result.reserve(limit);
    
    for (uint32_t i = 0; i < limit; ++i) {
        TestTick tick;
        tick.symbol = symbol;
        tick.sequence = i + 1;
        tick.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            start.time_since_epoch()).count() + i * 1000000;
        tick.last_price = 78560.0 + (i % 100) * 0.1;
        tick.volume = 100 + (i % 50);
        result.push_back(tick);
    }
    
    return result;
}

class MockGrpcClient::MockTickStream {
public:
    MockTickStream(const std::vector<std::string>& symbols) : symbols_(symbols) {}
    
    std::optional<TestTick> WaitForTick(std::chrono::milliseconds timeout) {
        // Simulate streaming delay
        std::this_thread::sleep_for(std::chrono::microseconds(30));
        
        TestTick tick;
        tick.symbol = symbols_.empty() ? "CU2409" : symbols_[0];
        tick.sequence = sequence_++;
        tick.timestamp = std::chrono::system_clock::now().time_since_epoch().count();
        tick.last_price = 78560.0;
        tick.volume = 100;
        
        return tick;
    }
    
private:
    std::vector<std::string> symbols_;
    uint64_t sequence_ = 1;
};

std::unique_ptr<MockGrpcClient::TickStream> MockGrpcClient::OpenTickStream(
    const std::vector<std::string>& symbols) {
    return std::make_unique<MockTickStream>(symbols);
}

// MockRedisClient implementation
MockRedisClient::MockRedisClient(const std::string& instance_name) 
    : instance_name_(instance_name), active_(true) {}

bool MockRedisClient::StoreTick(const TestTick& tick) {
    if (!active_) return false;
    
    // Simulate Redis write latency
    std::this_thread::sleep_for(std::chrono::microseconds(50));
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    stored_ticks_[tick.symbol][tick.sequence] = tick;
    return true;
}

std::optional<TestTick> MockRedisClient::GetLatestTick(const std::string& symbol) {
    if (!active_) return std::nullopt;
    
    // Simulate Redis read latency
    std::this_thread::sleep_for(std::chrono::microseconds(20));
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    auto symbol_it = stored_ticks_.find(symbol);
    if (symbol_it != stored_ticks_.end() && !symbol_it->second.empty()) {
        return symbol_it->second.rbegin()->second; // Return latest (highest sequence)
    }
    return std::nullopt;
}

std::optional<TestTick> MockRedisClient::GetTickBySequence(const std::string& symbol, uint64_t sequence) {
    if (!active_) return std::nullopt;
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    auto symbol_it = stored_ticks_.find(symbol);
    if (symbol_it != stored_ticks_.end()) {
        auto tick_it = symbol_it->second.find(sequence);
        if (tick_it != symbol_it->second.end()) {
            return tick_it->second;
        }
    }
    return std::nullopt;
}

void MockRedisClient::FlushPipeline() {
    // Simulate pipeline flush
    std::this_thread::sleep_for(std::chrono::microseconds(100));
}

void MockRedisClient::SimulateFailure() {
    active_ = false;
}

bool MockRedisClient::WaitForActivation(std::chrono::milliseconds timeout) {
    // Simulate activation delay
    std::this_thread::sleep_for(timeout);
    active_ = true;
    return true;
}

bool MockRedisClient::IsActive() const {
    return active_.load();
}

// MockClickHouseClient implementation
MockClickHouseClient::MockClickHouseClient(const std::string& instance_name)
    : instance_name_(instance_name), active_(true) {}

bool MockClickHouseClient::StoreTick(const TestTick& tick) {
    if (!active_) return false;
    
    // Simulate ClickHouse write latency
    std::this_thread::sleep_for(std::chrono::microseconds(200));
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    stored_ticks_.push_back(tick);
    return true;
}

bool MockClickHouseClient::BatchInsertTicks(const std::vector<TestTick>& ticks) {
    if (!active_) return false;
    
    // Simulate batch insert latency
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    stored_ticks_.insert(stored_ticks_.end(), ticks.begin(), ticks.end());
    return true;
}

std::vector<TestTick> MockClickHouseClient::QueryHistoricalTicks(
    const std::string& symbol,
    std::chrono::system_clock::time_point start,
    std::chrono::system_clock::time_point end,
    uint32_t limit) {
    
    if (!active_) return {};
    
    // Simulate query latency
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    std::vector<TestTick> result;
    
    auto start_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(start.time_since_epoch()).count();
    auto end_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end.time_since_epoch()).count();
    
    for (const auto& tick : stored_ticks_) {
        if (tick.symbol == symbol && 
            tick.timestamp >= start_ns && 
            tick.timestamp <= end_ns &&
            result.size() < limit) {
            result.push_back(tick);
        }
    }
    
    return result;
}

std::optional<TestTick> MockClickHouseClient::GetTickBySequence(const std::string& symbol, uint64_t sequence) {
    if (!active_) return std::nullopt;
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    for (const auto& tick : stored_ticks_) {
        if (tick.symbol == symbol && tick.sequence == sequence) {
            return tick;
        }
    }
    return std::nullopt;
}

void MockClickHouseClient::SimulateFailure() {
    active_ = false;
}

bool MockClickHouseClient::WaitForActivation(std::chrono::milliseconds timeout) {
    std::this_thread::sleep_for(timeout);
    active_ = true;
    return true;
}

bool MockClickHouseClient::IsActive() const {
    return active_.load();
}

// TickGenerator implementation
TickGenerator::TickGenerator() : rng_(std::random_device{}()) {}

TestTick TickGenerator::GenerateTestTick() {
    TestTick tick;
    
    static const std::vector<std::string> symbols = {
        "CU2409", "AL2409", "ZN2409", "AU2409", "AG2409",
        "RB2409", "HC2409", "I2409", "J2409", "JM2409"
    };
    
    std::uniform_int_distribution<> symbol_dist(0, symbols.size() - 1);
    std::uniform_real_distribution<> price_dist(50000.0, 100000.0);
    std::uniform_int_distribution<> volume_dist(1, 1000);
    
    tick.symbol = symbols[symbol_dist(rng_)];
    tick.timestamp = std::chrono::system_clock::now().time_since_epoch().count();
    tick.sequence = sequence_counter_++;
    tick.last_price = price_dist(rng_);
    tick.volume = volume_dist(rng_);
    tick.turnover = tick.last_price * tick.volume;
    
    return tick;
}

TestTick TickGenerator::GenerateRandomTick() {
    return GenerateTestTick();
}

std::vector<TestTick> TickGenerator::GenerateBatch(uint32_t count) {
    std::vector<TestTick> batch;
    batch.reserve(count);
    
    for (uint32_t i = 0; i < count; ++i) {
        batch.push_back(GenerateTestTick());
    }
    
    return batch;
}

// SystemResourceUsage implementation
SystemResourceUsage TestUtils::GetSystemResourceUsage() {
    SystemResourceUsage usage;
    
    // Mock system resource usage
    static std::random_device rd;
    static std::mt19937 gen(rd());
    std::uniform_real_distribution<> cpu_dist(20.0, 80.0);
    std::uniform_real_distribution<> mem_dist(500.0, 2000.0);
    
    usage.cpu_usage_percent = cpu_dist(gen);
    usage.memory_usage_mb = mem_dist(gen);
    
    return usage;
}

// TestUtils implementation
TestUtils::TestUtils() = default;
TestUtils::~TestUtils() = default;

std::unique_ptr<MockDataBus> TestUtils::CreateMockDataBus() {
    return std::make_unique<MockDataBus>();
}

std::unique_ptr<MockWebSocketServer> TestUtils::CreateMockWebSocketServer() {
    return std::make_unique<MockWebSocketServer>();
}

std::unique_ptr<MockWebSocketClient> TestUtils::CreateWebSocketTestClient() {
    return std::make_unique<MockWebSocketClient>();
}

std::unique_ptr<MockGrpcServer> TestUtils::CreateMockGrpcServer() {
    return std::make_unique<MockGrpcServer>();
}

std::unique_ptr<MockGrpcClient> TestUtils::CreateGrpcTestClient() {
    return std::make_unique<MockGrpcClient>();
}

std::unique_ptr<MockRedisClient> TestUtils::CreateRedisTestClient(const std::string& instance) {
    return std::make_unique<MockRedisClient>(instance);
}

std::unique_ptr<MockClickHouseClient> TestUtils::CreateClickHouseTestClient(const std::string& instance) {
    return std::make_unique<MockClickHouseClient>(instance);
}

std::unique_ptr<TickGenerator> TestUtils::CreateTickGenerator() {
    return std::make_unique<TickGenerator>();
}

TestTick TestUtils::GenerateTestTick() {
    auto generator = CreateTickGenerator();
    return generator->GenerateTestTick();
}

} // namespace performance_tests