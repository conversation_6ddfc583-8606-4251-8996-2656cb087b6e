#include "data_bus.h"
#include <iostream>
#include <algorithm>
#include <chrono>

namespace financial_data {
namespace databus {

DataBus::DataBus(const DataBusConfig& config) 
    : config_(config) {
    statistics_.Reset();
    start_time_ = std::chrono::steady_clock::now();
    last_throughput_check_ = start_time_;
    last_batch_time_ = start_time_;
    
    if (config_.enable_batching) {
        current_batch_.reserve(config_.batch_size);
    }
}

DataBus::~DataBus() {
    Stop();
}

bool DataBus::Start() {
    if (running_) {
        return true;
    }
    
    if (!InitializeComponents()) {
        std::cerr << "Failed to initialize DataBus components" << std::endl;
        return false;
    }
    
    running_ = true;
    
    // 启动工作线程
    worker_threads_.reserve(config_.worker_thread_count);
    for (size_t i = 0; i < config_.worker_thread_count; ++i) {
        worker_threads_.emplace_back(&DataBus::WorkerLoop, this, i);
    }
    
    // 启动批处理线程
    if (config_.enable_batching) {
        batch_threads_.emplace_back(&DataBus::BatchWorkerLoop, this);
    }
    
    // 启动统计线程
    if (config_.enable_monitoring) {
        statistics_thread_ = std::thread(&DataBus::StatisticsLoop, this);
    }
    
    // 启动子组件
    if (kafka_integration_) {
        kafka_integration_->StartWorkers();
    }
    
    if (data_router_) {
        data_router_->Start();
    }
    
    if (backpressure_controller_) {
        backpressure_controller_->Start();
    }
    
    TriggerEvent(DataBusEvent::STARTED, "DataBus started successfully");
    std::cout << "DataBus started with " << config_.worker_thread_count << " worker threads" << std::endl;
    
    return true;
}

void DataBus::Stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    // 停止子组件
    if (backpressure_controller_) {
        backpressure_controller_->Stop();
    }
    
    if (storage_manager_) {
        storage_manager_->Shutdown();
    }
    
    if (data_router_) {
        data_router_->Stop();
    }
    
    if (kafka_integration_) {
        kafka_integration_->StopWorkers();
    }
    
    // 通知批处理线程
    if (config_.enable_batching) {
        batch_cv_.notify_all();
    }
    
    // 等待工作线程结束
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    worker_threads_.clear();
    
    // 等待批处理线程结束
    for (auto& thread : batch_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    batch_threads_.clear();
    
    // 等待统计线程结束
    if (statistics_thread_.joinable()) {
        statistics_thread_.join();
    }
    
    // 刷新剩余数据
    Flush();
    
    // 清理组件
    CleanupComponents();
    
    TriggerEvent(DataBusEvent::STOPPED, "DataBus stopped");
    std::cout << "DataBus stopped" << std::endl;
}

bool DataBus::PushTick(const StandardTick& tick) {
    MarketDataWrapper wrapper(tick);
    return PushData(wrapper);
}

bool DataBus::PushLevel2(const Level2Data& level2) {
    MarketDataWrapper wrapper(level2);
    return PushData(wrapper);
}

bool DataBus::PushData(const MarketDataWrapper& data) {
    statistics_.total_messages_received++;
    
    // 检查背压
    if (backpressure_controller_ && backpressure_controller_->ShouldDropMessage()) {
        statistics_.total_messages_dropped++;
        backpressure_controller_->RecordDroppedMessage();
        return false;
    }
    
    // 应用限流
    if (backpressure_controller_ && backpressure_controller_->ShouldThrottle()) {
        backpressure_controller_->RecordThrottledMessage();
        std::this_thread::sleep_for(std::chrono::microseconds(10));
    }
    
    // 推送到输入队列
    if (!input_queue_->TryPush(data)) {
        statistics_.total_messages_dropped++;
        return false;
    }
    
    return true;
}

bool DataBus::PushBatch(const std::vector<MarketDataWrapper>& data_batch) {
    size_t pushed_count = 0;
    
    for (const auto& data : data_batch) {
        if (PushData(data)) {
            pushed_count++;
        }
    }
    
    return pushed_count == data_batch.size();
}

bool DataBus::RegisterClient(const std::string& client_id, 
                            const std::string& client_type,
                            std::function<bool(const MarketDataWrapper&)> callback) {
    if (!data_router_) {
        return false;
    }
    
    bool success = data_router_->RegisterClient(client_id, client_type, std::move(callback));
    if (success) {
        statistics_.active_clients++;
        TriggerEvent(DataBusEvent::CLIENT_CONNECTED, client_id);
    }
    
    return success;
}

bool DataBus::UnregisterClient(const std::string& client_id) {
    if (!data_router_) {
        return false;
    }
    
    bool success = data_router_->UnregisterClient(client_id);
    if (success) {
        statistics_.active_clients--;
        TriggerEvent(DataBusEvent::CLIENT_DISCONNECTED, client_id);
    }
    
    return success;
}

bool DataBus::Subscribe(const std::string& client_id, 
                       const std::vector<std::string>& symbols,
                       const std::vector<std::string>& exchanges) {
    if (!data_router_) {
        return false;
    }
    
    bool success = data_router_->Subscribe(client_id, symbols, exchanges);
    if (success) {
        statistics_.total_subscriptions += symbols.size() + exchanges.size();
    }
    
    return success;
}

bool DataBus::Unsubscribe(const std::string& client_id, 
                         const std::vector<std::string>& symbols,
                         const std::vector<std::string>& exchanges) {
    if (!data_router_) {
        return false;
    }
    
    bool success = data_router_->Unsubscribe(client_id, symbols, exchanges);
    if (success) {
        statistics_.total_subscriptions -= std::min(statistics_.total_subscriptions.load(), 
                                                   symbols.size() + exchanges.size());
    }
    
    return success;
}

void DataBus::ClientHeartbeat(const std::string& client_id) {
    if (data_router_) {
        data_router_->ClientHeartbeat(client_id);
    }
}

std::shared_ptr<ClientInfo> DataBus::GetClientInfo(const std::string& client_id) {
    return data_router_ ? data_router_->GetClientInfo(client_id) : nullptr;
}

std::vector<std::string> DataBus::GetActiveClients() {
    return data_router_ ? data_router_->GetActiveClients() : std::vector<std::string>{};
}

bool DataBus::AddRoutingRule(const RoutingRule& rule) {
    return data_router_ ? data_router_->AddRoutingRule(rule) : false;
}

bool DataBus::RemoveRoutingRule(const std::string& rule_id) {
    return data_router_ ? data_router_->RemoveRoutingRule(rule_id) : false;
}

DataBusStatistics DataBus::GetStatistics() const {
    return statistics_;
}

void DataBus::ResetStatistics() {
    statistics_.Reset();
    start_time_ = std::chrono::steady_clock::now();
    last_throughput_check_ = start_time_;
}

DataBus::QueueStatus DataBus::GetQueueStatus() const {
    QueueStatus status;
    
    if (input_queue_) {
        status.input_queue_size = input_queue_->Size();
        status.input_queue_usage = static_cast<double>(status.input_queue_size) / 
                                  input_queue_->Capacity() * 100.0;
    }
    
    if (output_queue_) {
        status.output_queue_size = output_queue_->Size();
        status.output_queue_usage = static_cast<double>(status.output_queue_size) / 
                                   output_queue_->Capacity() * 100.0;
    }
    
    if (batch_queue_) {
        status.batch_queue_size = batch_queue_->Size();
    }
    
    if (data_router_) {
        auto router_status = data_router_->GetQueueStatus();
        status.total_client_queues_size = router_status.total_client_queues_size;
    }
    
    return status;
}

KafkaProducer::Statistics DataBus::GetKafkaStatistics() const {
    if (kafka_integration_ && kafka_integration_->GetProducer()) {
        return kafka_integration_->GetProducer()->GetStatistics();
    }
    return {};
}

RoutingStatistics DataBus::GetRoutingStatistics() const {
    return data_router_ ? data_router_->GetStatistics() : RoutingStatistics{};
}

BackpressureStatistics DataBus::GetBackpressureStatistics() const {
    return backpressure_controller_ ? backpressure_controller_->GetStatistics() : BackpressureStatistics{};
}

StorageStatistics DataBus::GetStorageStatistics() const {
    return storage_manager_ ? storage_manager_->GetStatistics() : StorageStatistics{};
}

void DataBus::AddEventHandler(DataBusEventHandler handler) {
    std::lock_guard<std::mutex> lock(event_handlers_mutex_);
    event_handlers_.push_back(std::move(handler));
}

void DataBus::RemoveEventHandler(DataBusEventHandler handler) {
    std::lock_guard<std::mutex> lock(event_handlers_mutex_);
    // 注意：这里无法直接比较std::function，实际实现中需要使用其他方式标识处理器
    // 这里只是示例代码
}

bool DataBus::UpdateConfig(const DataBusConfig& config) {
    if (running_) {
        std::cerr << "Cannot update config while DataBus is running" << std::endl;
        return false;
    }
    
    config_ = config;
    return true;
}

void DataBus::Flush() {
    // 刷新批次数据
    if (config_.enable_batching) {
        FlushBatch();
    }
    
    // 刷新Kafka缓冲区
    if (kafka_integration_ && kafka_integration_->GetProducer()) {
        kafka_integration_->GetProducer()->Flush();
    }
}

DataBus::HealthStatus DataBus::GetHealthStatus() const {
    HealthStatus status;
    status.overall_healthy = true;
    status.error_message = "";
    
    // 检查队列健康状态
    auto queue_status = GetQueueStatus();
    status.queues_healthy = queue_status.input_queue_usage < 90.0 && 
                           queue_status.output_queue_usage < 90.0;
    
    if (!status.queues_healthy) {
        status.overall_healthy = false;
        status.error_message += "Queue usage too high; ";
    }
    
    // 检查Kafka健康状态
    if (config_.enable_kafka && kafka_integration_) {
        auto kafka_stats = GetKafkaStatistics();
        status.kafka_healthy = kafka_stats.messages_failed < kafka_stats.messages_sent * 0.01; // 失败率小于1%
        
        if (!status.kafka_healthy) {
            status.overall_healthy = false;
            status.error_message += "Kafka error rate too high; ";
        }
    } else {
        status.kafka_healthy = true;
    }
    
    // 检查路由器健康状态
    if (data_router_) {
        auto routing_stats = GetRoutingStatistics();
        status.router_healthy = routing_stats.routing_errors.load() < 
                               routing_stats.total_messages_routed.load() * 0.01; // 错误率小于1%
        
        if (!status.router_healthy) {
            status.overall_healthy = false;
            status.error_message += "Router error rate too high; ";
        }
    } else {
        status.router_healthy = true;
    }
    
    // 检查背压控制器健康状态
    if (config_.enable_backpressure && backpressure_controller_) {
        auto bp_stats = GetBackpressureStatistics();
        status.backpressure_healthy = bp_stats.current_state != BackpressureState::EMERGENCY;
        
        if (!status.backpressure_healthy) {
            status.overall_healthy = false;
            status.error_message += "System under emergency backpressure; ";
        }
    } else {
        status.backpressure_healthy = true;
    }
    
    // 检查存储健康状态
    if (config_.enable_storage && storage_manager_) {
        auto storage_health = storage_manager_->GetHealthStatus();
        status.storage_healthy = storage_health.overall_healthy;
        
        if (!status.storage_healthy) {
            status.overall_healthy = false;
            status.error_message += "Storage system unhealthy: " + storage_health.error_message;
        }
    } else {
        status.storage_healthy = true;
    }
    
    return status;
}

bool DataBus::InitializeComponents() {
    // 初始化队列
    input_queue_ = std::make_unique<WrapperQueue>();
    output_queue_ = std::make_unique<WrapperQueue>();
    batch_queue_ = std::make_unique<BatchQueue>();
    
    // 初始化Kafka集成
    if (config_.enable_kafka) {
        kafka_integration_ = std::make_unique<KafkaIntegration>(config_.kafka_config);
        if (!kafka_integration_->Initialize()) {
            std::cerr << "Failed to initialize Kafka integration" << std::endl;
            return false;
        }
    }
    
    // 初始化数据路由器
    data_router_ = std::make_unique<DataRouter>(config_.router_thread_count, 
                                               config_.client_heartbeat_timeout_ms);
    
    // 初始化存储管理器
    if (config_.enable_storage) {
        storage_manager_ = std::make_unique<StorageManager>(config_.storage_config);
        if (!storage_manager_->Initialize()) {
            std::cerr << "Failed to initialize storage manager" << std::endl;
            return false;
        }
    }
    
    // 初始化背压控制器
    if (config_.enable_backpressure) {
        backpressure_controller_ = std::make_unique<BackpressureController>(config_.backpressure_config);
        
        // 添加队列监控
        backpressure_controller_->AddMonitoredQueue(CreateQueueMonitor(input_queue_));
        backpressure_controller_->AddMonitoredQueue(CreateQueueMonitor(output_queue_));
        backpressure_controller_->AddMonitoredQueue(CreateQueueMonitor(batch_queue_));
        
        // 设置背压状态变化回调
        backpressure_controller_->SetStateChangeCallback(
            [this](BackpressureState old_state, BackpressureState new_state) {
                if (new_state >= BackpressureState::WARNING) {
                    TriggerEvent(DataBusEvent::BACKPRESSURE_TRIGGERED, 
                               "Backpressure state: " + std::to_string(static_cast<int>(new_state)));
                } else if (old_state >= BackpressureState::WARNING && new_state == BackpressureState::NORMAL) {
                    TriggerEvent(DataBusEvent::BACKPRESSURE_RELIEVED, "Backpressure relieved");
                }
            });
    }
    
    return true;
}

void DataBus::CleanupComponents() {
    backpressure_controller_.reset();
    storage_manager_.reset();
    data_router_.reset();
    kafka_integration_.reset();
    batch_queue_.reset();
    output_queue_.reset();
    input_queue_.reset();
}

void DataBus::WorkerLoop(size_t worker_id) {
    MarketDataWrapper data;
    
    while (running_) {
        if (input_queue_->TryPop(data)) {
            auto start_time = std::chrono::high_resolution_clock::now();
            
            try {
                ProcessData(data);
                statistics_.total_messages_processed++;
            } catch (const std::exception& e) {
                std::cerr << "Worker " << worker_id << " processing error: " << e.what() << std::endl;
                statistics_.processing_errors++;
                TriggerEvent(DataBusEvent::ERROR_OCCURRED, e.what());
            }
            
            auto end_time = std::chrono::high_resolution_clock::now();
            auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                end_time - start_time).count();
            
            RecordProcessingLatency(latency_ns);
            
            if (backpressure_controller_) {
                backpressure_controller_->RecordMessage(latency_ns / 1000); // 转换为微秒
            }
        } else {
            // 队列为空，短暂休眠
            std::this_thread::sleep_for(std::chrono::microseconds(1));
        }
    }
    
    std::cout << "DataBus worker thread " << worker_id << " stopped" << std::endl;
}

void DataBus::BatchWorkerLoop() {
    while (running_) {
        std::unique_lock<std::mutex> lock(batch_mutex_);
        
        // 等待批次满或超时
        batch_cv_.wait_for(lock, std::chrono::milliseconds(config_.batch_timeout_ms), [this] {
            return !running_ || current_batch_.size() >= config_.batch_size;
        });
        
        if (!current_batch_.empty()) {
            FlushBatch();
        }
    }
    
    // 处理剩余批次
    if (!current_batch_.empty()) {
        FlushBatch();
    }
    
    std::cout << "DataBus batch worker thread stopped" << std::endl;
}

void DataBus::StatisticsLoop() {
    while (running_) {
        UpdateStatistics();
        std::this_thread::sleep_for(std::chrono::milliseconds(config_.statistics_interval_ms));
    }
    
    std::cout << "DataBus statistics thread stopped" << std::endl;
}

void DataBus::ProcessData(const MarketDataWrapper& data) {
    // 存储数据到Redis和ClickHouse - 这是新增的存储逻辑！
    if (storage_manager_) {
        if (data.type == MarketDataWrapper::DataType::TICK) {
            // 异步存储tick数据
            storage_manager_->StoreTickAsync(data.tick_data);
        } else if (data.type == MarketDataWrapper::DataType::LEVEL2) {
            // 异步存储level2数据
            storage_manager_->StoreLevel2Async(data.level2_data);
        }
    }
    
    // 路由数据到客户端
    if (data_router_) {
        if (!data_router_->PushData(data)) {
            statistics_.routing_errors++;
        }
    }
    
    // 发送到Kafka
    if (kafka_integration_) {
        if (data.type == MarketDataWrapper::DataType::TICK) {
            if (!kafka_integration_->PushTick(data.tick_data)) {
                statistics_.kafka_errors++;
            }
        } else if (data.type == MarketDataWrapper::DataType::LEVEL2) {
            if (!kafka_integration_->PushLevel2(data.level2_data)) {
                statistics_.kafka_errors++;
            }
        }
    }
    
    // 批处理
    if (config_.enable_batching) {
        std::lock_guard<std::mutex> lock(batch_mutex_);
        current_batch_.push_back(data);
        
        if (current_batch_.size() >= config_.batch_size) {
            batch_cv_.notify_one();
        }
    }
    
    statistics_.total_messages_sent++;
}

void DataBus::ProcessBatch(const MarketDataBatch& batch) {
    // 发送批次到Kafka
    if (kafka_integration_) {
        if (!kafka_integration_->PushBatch(batch)) {
            statistics_.kafka_errors++;
        }
    }
}

void DataBus::FlushBatch() {
    if (current_batch_.empty()) {
        return;
    }
    
    MarketDataBatch batch;
    batch.batch_sequence = static_cast<uint32_t>(statistics_.total_messages_processed.load());
    
    for (const auto& data : current_batch_) {
        batch.AddData(data);
    }
    
    ProcessBatch(batch);
    current_batch_.clear();
    last_batch_time_ = std::chrono::steady_clock::now();
}

void DataBus::UpdateStatistics() {
    // 更新队列统计
    if (input_queue_) {
        statistics_.input_queue_size = input_queue_->Size();
    }
    
    if (output_queue_) {
        statistics_.output_queue_size = output_queue_->Size();
    }
    
    // 更新吞吐量统计
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_throughput_check_);
    
    if (elapsed.count() >= 1) {
        uint64_t current_messages = statistics_.total_messages_processed.load();
        uint64_t messages_per_sec = current_messages - last_message_count_.load();
        
        statistics_.throughput_per_second = messages_per_sec;
        last_message_count_ = current_messages;
        last_throughput_check_ = now;
    }
    
    // 更新客户端统计
    if (data_router_) {
        statistics_.active_clients = data_router_->GetActiveClients().size();
    }
}

void DataBus::TriggerEvent(DataBusEvent event, const std::string& message) {
    std::lock_guard<std::mutex> lock(event_handlers_mutex_);
    
    for (const auto& handler : event_handlers_) {
        try {
            handler(event, message);
        } catch (const std::exception& e) {
            std::cerr << "Event handler error: " << e.what() << std::endl;
        }
    }
}

void DataBus::CheckBackpressure() {
    if (!backpressure_controller_) {
        return;
    }
    
    BackpressureState state = backpressure_controller_->GetCurrentState();
    
    if (state >= BackpressureState::WARNING) {
        // 触发背压处理
        TriggerEvent(DataBusEvent::BACKPRESSURE_TRIGGERED, 
                    "Backpressure state: " + std::to_string(static_cast<int>(state)));
    }
}

void DataBus::RecordProcessingLatency(uint64_t latency_ns) {
    uint64_t current_avg = statistics_.avg_processing_latency_ns.load();
    uint64_t new_avg = (current_avg + latency_ns) / 2;
    statistics_.avg_processing_latency_ns = new_avg;
    
    uint64_t current_max = statistics_.max_processing_latency_ns.load();
    while (latency_ns > current_max && 
           !statistics_.max_processing_latency_ns.compare_exchange_weak(current_max, latency_ns)) {
        // CAS循环
    }
}

// DataBusFactory实现
std::unique_ptr<DataBus> DataBusFactory::CreateDefault() {
    DataBusConfig config;
    config.worker_thread_count = 4;
    config.enable_kafka = true;
    config.enable_backpressure = true;
    config.enable_batching = true;
    
    return std::make_unique<DataBus>(config);
}

std::unique_ptr<DataBus> DataBusFactory::CreateHighPerformance() {
    DataBusConfig config;
    config.worker_thread_count = std::thread::hardware_concurrency();
    config.router_thread_count = 4;
    config.input_queue_size = 262144;  // 256K
    config.output_queue_size = 131072; // 128K
    config.enable_kafka = true;
    config.enable_backpressure = true;
    config.enable_batching = true;
    config.batch_size = 200;
    config.batch_timeout_ms = 5;
    
    // 高性能背压配置
    config.backpressure_config = BackpressureControllerFactory::CreateHighPerformance()->GetConfig();
    
    return std::make_unique<DataBus>(config);
}

std::unique_ptr<DataBus> DataBusFactory::CreateLowLatency() {
    DataBusConfig config;
    config.worker_thread_count = 2;
    config.router_thread_count = 1;
    config.input_queue_size = 65536;   // 64K
    config.output_queue_size = 32768;  // 32K
    config.enable_kafka = false;       // 禁用Kafka以降低延迟
    config.enable_backpressure = true;
    config.enable_batching = false;    // 禁用批处理以降低延迟
    
    // 低延迟背压配置
    config.backpressure_config = BackpressureControllerFactory::CreateLowLatency()->GetConfig();
    
    return std::make_unique<DataBus>(config);
}

std::unique_ptr<DataBus> DataBusFactory::CreateMemoryOptimized() {
    DataBusConfig config;
    config.worker_thread_count = 2;
    config.router_thread_count = 1;
    config.input_queue_size = 32768;   // 32K
    config.output_queue_size = 16384;  // 16K
    config.batch_queue_size = 1024;    // 1K
    config.enable_kafka = true;
    config.enable_backpressure = true;
    config.enable_batching = true;
    config.batch_size = 50;
    
    // 内存敏感背压配置
    config.backpressure_config = BackpressureControllerFactory::CreateMemorySensitive()->GetConfig();
    
    return std::make_unique<DataBus>(config);
}

std::unique_ptr<DataBus> DataBusFactory::CreateFromConfig(const std::string& config_file) {
    // TODO: 实现从配置文件读取参数
    return CreateDefault();
}

} // namespace databus
} // namespace financial_data