{"development": {"description": "开发环境配置", "server": {"host": "0.0.0.0", "port": 8080, "threads": 2, "debug": true}, "logging": {"level": "debug", "file": "logs/dev.log", "console": true}, "redis": {"host": "localhost", "port": 6379, "database": 0, "password": "", "pool_size": 5}, "clickhouse": {"host": "localhost", "port": 9000, "database": "market_data_dev", "username": "admin", "password": "password123"}, "kafka": {"brokers": ["localhost:9092"], "topics": {"market_data": "market_data_dev", "alerts": "alerts_dev"}}, "storage": {"enable_redis": true, "enable_clickhouse": true, "enable_s3": false, "batch_size": 1000, "flush_interval": 5}, "monitoring": {"prometheus_port": 9090, "health_check_interval": 30, "metrics_enabled": true}, "features": {"ctp_collector": true, "pytdx_collector": true, "websocket_server": true, "grpc_server": false, "web_admin": true}}, "testing": {"description": "测试环境配置", "server": {"host": "0.0.0.0", "port": 8080, "threads": 4, "debug": false}, "logging": {"level": "info", "file": "logs/test.log", "console": true}, "redis": {"host": "redis", "port": 6379, "database": 1, "password": "", "pool_size": 10}, "clickhouse": {"host": "clickhouse", "port": 9000, "database": "market_data_test", "username": "admin", "password": "password123"}, "kafka": {"brokers": ["kafka:9092"], "topics": {"market_data": "market_data_test", "alerts": "alerts_test"}}, "storage": {"enable_redis": true, "enable_clickhouse": true, "enable_s3": false, "batch_size": 5000, "flush_interval": 10}, "monitoring": {"prometheus_port": 9090, "health_check_interval": 30, "metrics_enabled": true}, "features": {"ctp_collector": true, "pytdx_collector": true, "websocket_server": true, "grpc_server": true, "web_admin": true}, "testing": {"mock_data": true, "test_symbols": ["000001", "000002", "CU2409"], "test_duration": 300, "performance_thresholds": {"max_latency_ms": 50, "min_throughput_per_sec": 10000}}}, "production": {"description": "生产环境配置", "server": {"host": "0.0.0.0", "port": 8080, "threads": 8, "debug": false}, "logging": {"level": "info", "file": "logs/production.log", "console": false, "max_file_size_mb": 100, "max_files": 10}, "redis": {"cluster": true, "nodes": [{"host": "redis-1", "port": 6379}, {"host": "redis-2", "port": 6379}, {"host": "redis-3", "port": 6379}], "password": "${REDIS_PASSWORD}", "pool_size": 20}, "clickhouse": {"cluster": true, "nodes": [{"host": "clickhouse-1", "port": 9000}, {"host": "clickhouse-2", "port": 9000}, {"host": "clickhouse-3", "port": 9000}], "database": "market_data", "username": "${CLICKHOUSE_USER}", "password": "${CLICKHOUSE_PASSWORD}"}, "kafka": {"brokers": ["kafka-1:9092", "kafka-2:9092", "kafka-3:9092"], "topics": {"market_data": "market_data_prod", "alerts": "alerts_prod"}, "replication_factor": 3}, "storage": {"enable_redis": true, "enable_clickhouse": true, "enable_s3": true, "batch_size": 10000, "flush_interval": 1, "s3": {"endpoint": "${S3_ENDPOINT}", "access_key": "${S3_ACCESS_KEY}", "secret_key": "${S3_SECRET_KEY}", "bucket": "market-data-archive"}}, "monitoring": {"prometheus_port": 9090, "health_check_interval": 10, "metrics_enabled": true, "alerting": {"enabled": true, "webhook_url": "${ALERT_WEBHOOK_URL}", "email_recipients": ["<EMAIL>"]}}, "security": {"tls_enabled": true, "jwt_secret": "${JWT_SECRET}", "api_rate_limit": 1000, "cors_origins": ["https://dashboard.company.com"]}, "features": {"ctp_collector": true, "pytdx_collector": true, "websocket_server": true, "grpc_server": true, "web_admin": true}, "performance": {"max_concurrent_clients": 10000, "connection_timeout": 30, "read_timeout": 60, "write_timeout": 30}}}