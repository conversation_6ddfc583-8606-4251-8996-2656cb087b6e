#include "data_router.h"
#include <algorithm>
#include <iostream>
#include <chrono>
#include <regex>

namespace financial_data {
namespace databus {

// RoutingRule实现
bool RoutingRule::Matches(const std::string& symbol, const std::string& exchange, 
                         const std::string& client_id, const StandardTick& tick) const {
    if (!enabled) {
        return false;
    }
    
    // 检查合约匹配
    if (!DataRouter::WildcardMatch(symbol_pattern, symbol)) {
        return false;
    }
    
    // 检查交易所匹配
    if (!DataRouter::WildcardMatch(exchange_pattern, exchange)) {
        return false;
    }
    
    // 检查客户端匹配
    if (!DataRouter::WildcardMatch(client_pattern, client_id)) {
        return false;
    }
    
    // 检查价格范围
    if (tick.last_price < min_price || tick.last_price > max_price) {
        return false;
    }
    
    // 检查成交量范围
    if (tick.volume < min_volume || tick.volume > max_volume) {
        return false;
    }
    
    return true;
}

// DataRouter实现
DataRouter::DataRouter(size_t worker_count, int64_t heartbeat_timeout_ms) 
    : worker_count_(worker_count), 
      heartbeat_timeout_ns_(heartbeat_timeout_ms * 1000000) {
    input_queue_ = std::make_unique<WrapperQueue>();
}

DataRouter::~DataRouter() {
    Stop();
}

bool DataRouter::Start() {
    if (running_) {
        return true;
    }
    
    running_ = true;
    
    // 启动工作线程
    worker_threads_.reserve(worker_count_);
    for (size_t i = 0; i < worker_count_; ++i) {
        worker_threads_.emplace_back(&DataRouter::WorkerLoop, this, i);
    }
    
    // 启动心跳检查线程
    heartbeat_thread_ = std::thread(&DataRouter::HeartbeatLoop, this);
    
    std::cout << "DataRouter started with " << worker_count_ << " worker threads" << std::endl;
    return true;
}

void DataRouter::Stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    // 等待工作线程结束
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    worker_threads_.clear();
    
    // 等待心跳线程结束
    if (heartbeat_thread_.joinable()) {
        heartbeat_thread_.join();
    }
    
    // 清理客户端
    {
        std::unique_lock<std::shared_mutex> lock(clients_mutex_);
        clients_.clear();
    }
    
    std::cout << "DataRouter stopped" << std::endl;
}

bool DataRouter::RegisterClient(const std::string& client_id, 
                               const std::string& client_type,
                               std::function<bool(const MarketDataWrapper&)> callback) {
    std::unique_lock<std::shared_mutex> lock(clients_mutex_);
    
    if (clients_.find(client_id) != clients_.end()) {
        std::cerr << "Client " << client_id << " already registered" << std::endl;
        return false;
    }
    
    auto client_info = std::make_shared<ClientInfo>(client_id, client_type);
    client_info->data_callback = std::move(callback);
    clients_[client_id] = client_info;
    
    statistics_.total_clients_served++;
    
    std::cout << "Client " << client_id << " (" << client_type << ") registered" << std::endl;
    return true;
}

bool DataRouter::UnregisterClient(const std::string& client_id) {
    std::unique_lock<std::shared_mutex> lock(clients_mutex_);
    
    auto it = clients_.find(client_id);
    if (it == clients_.end()) {
        return false;
    }
    
    // 标记为非活跃
    it->second->active = false;
    
    // 从订阅索引中移除
    {
        std::unique_lock<std::shared_mutex> sub_lock(subscription_mutex_);
        
        for (auto& [symbol, subscribers] : symbol_subscribers_) {
            subscribers.erase(client_id);
        }
        
        for (auto& [exchange, subscribers] : exchange_subscribers_) {
            subscribers.erase(client_id);
        }
    }
    
    clients_.erase(it);
    
    std::cout << "Client " << client_id << " unregistered" << std::endl;
    return true;
}

bool DataRouter::Subscribe(const std::string& client_id, 
                          const std::vector<std::string>& symbols,
                          const std::vector<std::string>& exchanges) {
    std::shared_lock<std::shared_mutex> client_lock(clients_mutex_);
    
    auto client_it = clients_.find(client_id);
    if (client_it == clients_.end()) {
        std::cerr << "Client " << client_id << " not found" << std::endl;
        return false;
    }
    
    auto client_info = client_it->second;
    client_lock.unlock();
    
    // 更新客户端订阅信息
    for (const auto& symbol : symbols) {
        client_info->subscribed_symbols.insert(symbol);
    }
    
    for (const auto& exchange : exchanges) {
        client_info->subscribed_exchanges.insert(exchange);
    }
    
    // 更新订阅索引
    {
        std::unique_lock<std::shared_mutex> sub_lock(subscription_mutex_);
        
        for (const auto& symbol : symbols) {
            symbol_subscribers_[symbol].insert(client_id);
        }
        
        for (const auto& exchange : exchanges) {
            exchange_subscribers_[exchange].insert(client_id);
        }
    }
    
    std::cout << "Client " << client_id << " subscribed to " 
              << symbols.size() << " symbols and " 
              << exchanges.size() << " exchanges" << std::endl;
    return true;
}

bool DataRouter::Unsubscribe(const std::string& client_id, 
                            const std::vector<std::string>& symbols,
                            const std::vector<std::string>& exchanges) {
    std::shared_lock<std::shared_mutex> client_lock(clients_mutex_);
    
    auto client_it = clients_.find(client_id);
    if (client_it == clients_.end()) {
        return false;
    }
    
    auto client_info = client_it->second;
    client_lock.unlock();
    
    // 更新客户端订阅信息
    for (const auto& symbol : symbols) {
        client_info->subscribed_symbols.erase(symbol);
    }
    
    for (const auto& exchange : exchanges) {
        client_info->subscribed_exchanges.erase(exchange);
    }
    
    // 更新订阅索引
    {
        std::unique_lock<std::shared_mutex> sub_lock(subscription_mutex_);
        
        for (const auto& symbol : symbols) {
            auto it = symbol_subscribers_.find(symbol);
            if (it != symbol_subscribers_.end()) {
                it->second.erase(client_id);
                if (it->second.empty()) {
                    symbol_subscribers_.erase(it);
                }
            }
        }
        
        for (const auto& exchange : exchanges) {
            auto it = exchange_subscribers_.find(exchange);
            if (it != exchange_subscribers_.end()) {
                it->second.erase(client_id);
                if (it->second.empty()) {
                    exchange_subscribers_.erase(it);
                }
            }
        }
    }
    
    std::cout << "Client " << client_id << " unsubscribed from " 
              << symbols.size() << " symbols and " 
              << exchanges.size() << " exchanges" << std::endl;
    return true;
}

std::vector<std::string> DataRouter::GetSubscriptions(const std::string& client_id) const {
    std::shared_lock<std::shared_mutex> lock(clients_mutex_);
    
    auto it = clients_.find(client_id);
    if (it == clients_.end()) {
        return {};
    }
    
    std::vector<std::string> subscriptions;
    
    for (const auto& symbol : it->second->subscribed_symbols) {
        subscriptions.push_back("symbol:" + symbol);
    }
    
    for (const auto& exchange : it->second->subscribed_exchanges) {
        subscriptions.push_back("exchange:" + exchange);
    }
    
    return subscriptions;
}

bool DataRouter::PushData(const MarketDataWrapper& data) {
    statistics_.total_messages_received++;
    
    if (!input_queue_->TryPush(data)) {
        statistics_.total_messages_dropped++;
        return false;
    }
    
    return true;
}

bool DataRouter::PushBatch(const std::vector<MarketDataWrapper>& data_batch) {
    size_t pushed_count = 0;
    
    for (const auto& data : data_batch) {
        if (PushData(data)) {
            pushed_count++;
        }
    }
    
    return pushed_count == data_batch.size();
}

bool DataRouter::AddRoutingRule(const RoutingRule& rule) {
    std::unique_lock<std::shared_mutex> lock(rules_mutex_);
    
    // 检查规则ID是否已存在
    auto it = std::find_if(routing_rules_.begin(), routing_rules_.end(),
                          [&rule](const RoutingRule& r) { return r.rule_id == rule.rule_id; });
    
    if (it != routing_rules_.end()) {
        std::cerr << "Routing rule " << rule.rule_id << " already exists" << std::endl;
        return false;
    }
    
    routing_rules_.push_back(rule);
    
    // 按优先级排序
    std::sort(routing_rules_.begin(), routing_rules_.end(),
             [](const RoutingRule& a, const RoutingRule& b) {
                 return a.priority > b.priority;
             });
    
    std::cout << "Added routing rule " << rule.rule_id << " with priority " << rule.priority << std::endl;
    return true;
}

bool DataRouter::RemoveRoutingRule(const std::string& rule_id) {
    std::unique_lock<std::shared_mutex> lock(rules_mutex_);
    
    auto it = std::find_if(routing_rules_.begin(), routing_rules_.end(),
                          [&rule_id](const RoutingRule& r) { return r.rule_id == rule_id; });
    
    if (it == routing_rules_.end()) {
        return false;
    }
    
    routing_rules_.erase(it);
    std::cout << "Removed routing rule " << rule_id << std::endl;
    return true;
}

bool DataRouter::UpdateRoutingRule(const RoutingRule& rule) {
    std::unique_lock<std::shared_mutex> lock(rules_mutex_);
    
    auto it = std::find_if(routing_rules_.begin(), routing_rules_.end(),
                          [&rule](const RoutingRule& r) { return r.rule_id == rule.rule_id; });
    
    if (it == routing_rules_.end()) {
        return false;
    }
    
    *it = rule;
    
    // 重新排序
    std::sort(routing_rules_.begin(), routing_rules_.end(),
             [](const RoutingRule& a, const RoutingRule& b) {
                 return a.priority > b.priority;
             });
    
    std::cout << "Updated routing rule " << rule.rule_id << std::endl;
    return true;
}

std::vector<RoutingRule> DataRouter::GetRoutingRules() const {
    std::shared_lock<std::shared_mutex> lock(rules_mutex_);
    return routing_rules_;
}

void DataRouter::ClientHeartbeat(const std::string& client_id) {
    std::shared_lock<std::shared_mutex> lock(clients_mutex_);
    
    auto it = clients_.find(client_id);
    if (it != clients_.end()) {
        it->second->UpdateHeartbeat();
    }
}

std::shared_ptr<ClientInfo> DataRouter::GetClientInfo(const std::string& client_id) const {
    std::shared_lock<std::shared_mutex> lock(clients_mutex_);
    
    auto it = clients_.find(client_id);
    if (it != clients_.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::vector<std::string> DataRouter::GetActiveClients() const {
    std::shared_lock<std::shared_mutex> lock(clients_mutex_);
    
    std::vector<std::string> active_clients;
    
    for (const auto& [client_id, client_info] : clients_) {
        if (client_info->active && client_info->IsAlive(heartbeat_timeout_ns_)) {
            active_clients.push_back(client_id);
        }
    }
    
    return active_clients;
}

RoutingStatistics DataRouter::GetStatistics() const {
    return statistics_;
}

void DataRouter::ResetStatistics() {
    statistics_.Reset();
}

DataRouter::QueueStatus DataRouter::GetQueueStatus() const {
    QueueStatus status;
    status.input_queue_size = input_queue_->Size();
    status.total_client_queues_size = 0;
    
    std::shared_lock<std::shared_mutex> lock(clients_mutex_);
    
    for (const auto& [client_id, client_info] : clients_) {
        size_t queue_size = client_info->client_queue->Size();
        status.client_queue_sizes[client_id] = queue_size;
        status.total_client_queues_size += queue_size;
    }
    
    return status;
}

void DataRouter::WorkerLoop(size_t worker_id) {
    MarketDataWrapper data;
    
    while (running_) {
        if (input_queue_->TryPop(data)) {
            auto start_time = std::chrono::high_resolution_clock::now();
            
            RouteData(data);
            
            auto end_time = std::chrono::high_resolution_clock::now();
            auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                end_time - start_time).count();
            
            statistics_.UpdateLatency(latency_ns);
            statistics_.total_messages_routed++;
        } else {
            // 队列为空，短暂休眠
            std::this_thread::sleep_for(std::chrono::microseconds(1));
        }
    }
    
    std::cout << "Worker thread " << worker_id << " stopped" << std::endl;
}

void DataRouter::RouteData(const MarketDataWrapper& data) {
    // 查找匹配的客户端
    auto matching_clients = FindMatchingClients(data);
    
    if (matching_clients.empty()) {
        statistics_.total_messages_dropped++;
        return;
    }
    
    // 发送数据到匹配的客户端
    for (const auto& client_id : matching_clients) {
        if (!SendToClient(client_id, data)) {
            statistics_.routing_errors++;
        }
    }
}

std::vector<std::string> DataRouter::FindMatchingClients(const MarketDataWrapper& data) const {
    std::vector<std::string> matching_clients;
    
    std::string symbol, exchange;
    
    // 提取合约和交易所信息
    if (data.type == MarketDataWrapper::DataType::TICK) {
        symbol = data.tick_data.symbol;
        exchange = data.tick_data.exchange;
    } else if (data.type == MarketDataWrapper::DataType::LEVEL2) {
        symbol = data.level2_data.symbol;
        exchange = data.level2_data.exchange;
    } else {
        return matching_clients;
    }
    
    std::unordered_set<std::string> candidates;
    
    // 从订阅索引中查找候选客户端
    {
        std::shared_lock<std::shared_mutex> sub_lock(subscription_mutex_);
        
        // 按合约查找
        auto symbol_it = symbol_subscribers_.find(symbol);
        if (symbol_it != symbol_subscribers_.end()) {
            candidates.insert(symbol_it->second.begin(), symbol_it->second.end());
        }
        
        // 按交易所查找
        auto exchange_it = exchange_subscribers_.find(exchange);
        if (exchange_it != exchange_subscribers_.end()) {
            candidates.insert(exchange_it->second.begin(), exchange_it->second.end());
        }
    }
    
    // 检查客户端是否真正匹配
    {
        std::shared_lock<std::shared_mutex> client_lock(clients_mutex_);
        
        for (const auto& client_id : candidates) {
            auto client_it = clients_.find(client_id);
            if (client_it == clients_.end() || !client_it->second->active) {
                continue;
            }
            
            auto client_info = client_it->second;
            
            // 检查订阅匹配
            if (client_info->IsSubscribed(symbol, exchange)) {
                // 应用路由规则
                if (ApplyRoutingRules(client_id, data)) {
                    matching_clients.push_back(client_id);
                }
            }
        }
    }
    
    return matching_clients;
}

bool DataRouter::ApplyRoutingRules(const std::string& client_id, const MarketDataWrapper& data) const {
    std::shared_lock<std::shared_mutex> lock(rules_mutex_);
    
    if (routing_rules_.empty()) {
        return true;  // 没有规则时默认允许
    }
    
    std::string symbol, exchange;
    StandardTick tick;
    
    if (data.type == MarketDataWrapper::DataType::TICK) {
        symbol = data.tick_data.symbol;
        exchange = data.tick_data.exchange;
        tick = data.tick_data;
    } else if (data.type == MarketDataWrapper::DataType::LEVEL2) {
        symbol = data.level2_data.symbol;
        exchange = data.level2_data.exchange;
        // Level2数据转换为Tick进行规则匹配
        tick.symbol = symbol;
        tick.exchange = exchange;
        tick.timestamp_ns = data.level2_data.timestamp_ns;
        if (!data.level2_data.asks.empty()) {
            tick.last_price = data.level2_data.asks[0].price;
        }
    } else {
        return false;
    }
    
    // 按优先级顺序应用规则
    for (const auto& rule : routing_rules_) {
        if (rule.Matches(symbol, exchange, client_id, tick)) {
            return true;  // 匹配到规则，允许路由
        }
    }
    
    return false;  // 没有匹配的规则，拒绝路由
}

bool DataRouter::SendToClient(const std::string& client_id, const MarketDataWrapper& data) {
    std::shared_lock<std::shared_mutex> lock(clients_mutex_);
    
    auto client_it = clients_.find(client_id);
    if (client_it == clients_.end() || !client_it->second->active) {
        return false;
    }
    
    auto client_info = client_it->second;
    lock.unlock();
    
    // 尝试推送到客户端队列
    if (!client_info->client_queue->TryPush(data)) {
        client_info->messages_dropped++;
        return false;
    }
    
    // 调用客户端回调
    if (client_info->data_callback) {
        try {
            if (client_info->data_callback(data)) {
                client_info->messages_sent++;
                return true;
            } else {
                client_info->messages_dropped++;
                return false;
            }
        } catch (const std::exception& e) {
            std::cerr << "Client callback error for " << client_id << ": " << e.what() << std::endl;
            client_info->messages_dropped++;
            return false;
        }
    }
    
    client_info->messages_sent++;
    return true;
}

void DataRouter::HeartbeatLoop() {
    while (running_) {
        CleanupInactiveClients();
        std::this_thread::sleep_for(std::chrono::seconds(10));  // 每10秒检查一次
    }
}

void DataRouter::CleanupInactiveClients() {
    std::vector<std::string> inactive_clients;
    
    {
        std::shared_lock<std::shared_mutex> lock(clients_mutex_);
        
        for (const auto& [client_id, client_info] : clients_) {
            if (!client_info->IsAlive(heartbeat_timeout_ns_)) {
                inactive_clients.push_back(client_id);
            }
        }
    }
    
    // 清理非活跃客户端
    for (const auto& client_id : inactive_clients) {
        std::cout << "Cleaning up inactive client: " << client_id << std::endl;
        UnregisterClient(client_id);
    }
}

size_t DataRouter::SelectWorkerThread() const {
    return round_robin_counter_.fetch_add(1) % worker_count_;
}

bool DataRouter::WildcardMatch(const std::string& pattern, const std::string& text) {
    if (pattern == "*") {
        return true;
    }
    
    if (pattern.empty()) {
        return text.empty();
    }
    
    // 简单的通配符匹配实现
    try {
        std::string regex_pattern = pattern;
        
        // 转义特殊字符
        std::string special_chars = "\\^$.|?+()[]{}";
        for (char c : special_chars) {
            if (c != '*' && c != '?') {
                size_t pos = 0;
                std::string target(1, c);
                std::string replacement = "\\" + target;
                while ((pos = regex_pattern.find(target, pos)) != std::string::npos) {
                    regex_pattern.replace(pos, 1, replacement);
                    pos += replacement.length();
                }
            }
        }
        
        // 替换通配符
        size_t pos = 0;
        while ((pos = regex_pattern.find("*", pos)) != std::string::npos) {
            regex_pattern.replace(pos, 1, ".*");
            pos += 2;
        }
        
        pos = 0;
        while ((pos = regex_pattern.find("?", pos)) != std::string::npos) {
            regex_pattern.replace(pos, 1, ".");
            pos += 1;
        }
        
        std::regex regex(regex_pattern);
        return std::regex_match(text, regex);
    } catch (const std::exception& e) {
        std::cerr << "Regex error in wildcard match: " << e.what() << std::endl;
        return pattern == text;  // 回退到精确匹配
    }
}

// DataRouterFactory实现
std::unique_ptr<DataRouter> DataRouterFactory::CreateDefault() {
    return std::make_unique<DataRouter>(4, 30000);  // 4个工作线程，30秒心跳超时
}

std::unique_ptr<DataRouter> DataRouterFactory::CreateHighPerformance() {
    size_t worker_count = std::thread::hardware_concurrency();
    if (worker_count == 0) worker_count = 8;
    
    return std::make_unique<DataRouter>(worker_count, 60000);  // 使用所有CPU核心，60秒心跳超时
}

std::unique_ptr<DataRouter> DataRouterFactory::CreateLowLatency() {
    return std::make_unique<DataRouter>(2, 10000);  // 2个工作线程，10秒心跳超时
}

std::unique_ptr<DataRouter> DataRouterFactory::CreateFromConfig(const std::string& config_file) {
    // TODO: 实现从配置文件读取参数
    return CreateDefault();
}

} // namespace databus
} // namespace financial_data