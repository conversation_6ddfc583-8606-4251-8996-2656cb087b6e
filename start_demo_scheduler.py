#!/usr/bin/env python3
"""
演示调度器
展示WSL环境下的金融数据服务基本功能
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime, timedelta
import signal

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

class DemoScheduler:
    def __init__(self):
        self.running = False
        self.tasks = []
        self.redis_client = None
        self.logger = None
        self.setup_logging()
        self.setup_redis()
    
    def setup_logging(self):
        """设置日志"""
        os.makedirs('logs', exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/demo_scheduler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('DemoScheduler')
    
    def setup_redis(self):
        """设置Redis连接"""
        try:
            import redis
            self.redis_client = redis.Redis(
                host='localhost', 
                port=6379, 
                db=0, 
                socket_timeout=5,
                decode_responses=True
            )
            
            # 测试连接
            self.redis_client.ping()
            self.logger.info("Redis连接成功")
            
        except Exception as e:
            self.logger.error(f"Redis连接失败: {e}")
            self.redis_client = None
    
    async def mock_data_collection_task(self, task_name, interval=30):
        """模拟数据采集任务"""
        while self.running:
            try:
                # 模拟数据采集
                mock_data = {
                    'task': task_name,
                    'timestamp': datetime.now().isoformat(),
                    'data': {
                        'symbol': '000001',
                        'price': 12.34 + (hash(str(datetime.now())) % 100) / 100,
                        'volume': 1000000 + (hash(str(datetime.now())) % 500000),
                        'status': 'active'
                    }
                }
                
                # 存储到Redis
                if self.redis_client:
                    key = f"market_data:{task_name}:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    self.redis_client.setex(key, 3600, json.dumps(mock_data))  # 1小时过期
                    
                    # 更新最新数据
                    latest_key = f"latest:{task_name}"
                    self.redis_client.setex(latest_key, 300, json.dumps(mock_data))  # 5分钟过期
                
                self.logger.info(f"✅ {task_name} 数据采集完成 - 价格: {mock_data['data']['price']:.2f}")
                
                # 等待下次执行
                await asyncio.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"❌ {task_name} 数据采集失败: {e}")
                await asyncio.sleep(5)  # 错误后短暂等待
    
    async def system_monitor_task(self):
        """系统监控任务"""
        while self.running:
            try:
                # 检查Redis状态
                if self.redis_client:
                    info = self.redis_client.info()
                    memory_usage = info.get('used_memory_human', 'N/A')
                    connected_clients = info.get('connected_clients', 0)
                    
                    # 获取存储的数据量
                    keys_count = len(self.redis_client.keys('market_data:*'))
                    
                    self.logger.info(f"📊 系统状态 - Redis内存: {memory_usage}, 客户端: {connected_clients}, 数据条数: {keys_count}")
                    
                    # 存储监控数据
                    monitor_data = {
                        'timestamp': datetime.now().isoformat(),
                        'memory_usage': memory_usage,
                        'connected_clients': connected_clients,
                        'data_count': keys_count,
                        'tasks_running': len(self.tasks)
                    }
                    
                    self.redis_client.setex('system_monitor', 60, json.dumps(monitor_data))
                
                await asyncio.sleep(60)  # 每分钟监控一次
                
            except Exception as e:
                self.logger.error(f"❌ 系统监控失败: {e}")
                await asyncio.sleep(30)
    
    async def data_cleanup_task(self):
        """数据清理任务"""
        while self.running:
            try:
                if self.redis_client:
                    # 清理过期的市场数据（保留最近1小时）
                    cutoff_time = datetime.now() - timedelta(hours=1)
                    cutoff_str = cutoff_time.strftime('%Y%m%d_%H%M%S')
                    
                    # 获取所有市场数据键
                    keys = self.redis_client.keys('market_data:*')
                    cleaned_count = 0
                    
                    for key in keys:
                        # 从键名中提取时间戳
                        try:
                            timestamp_part = key.split(':')[-1]
                            if timestamp_part < cutoff_str:
                                self.redis_client.delete(key)
                                cleaned_count += 1
                        except:
                            continue
                    
                    if cleaned_count > 0:
                        self.logger.info(f"🧹 数据清理完成 - 清理了 {cleaned_count} 条过期数据")
                
                await asyncio.sleep(1800)  # 每30分钟清理一次
                
            except Exception as e:
                self.logger.error(f"❌ 数据清理失败: {e}")
                await asyncio.sleep(300)
    
    async def start(self):
        """启动调度器"""
        self.running = True
        self.logger.info("🚀 演示调度器启动")
        
        # 创建任务
        self.tasks = [
            asyncio.create_task(self.mock_data_collection_task("股票数据", 10)),
            asyncio.create_task(self.mock_data_collection_task("指数数据", 15)),
            asyncio.create_task(self.mock_data_collection_task("期货数据", 20)),
            asyncio.create_task(self.system_monitor_task()),
            asyncio.create_task(self.data_cleanup_task())
        ]
        
        self.logger.info(f"✅ 启动了 {len(self.tasks)} 个任务")
        
        # 等待所有任务完成
        try:
            await asyncio.gather(*self.tasks)
        except asyncio.CancelledError:
            self.logger.info("任务被取消")
    
    async def stop(self):
        """停止调度器"""
        self.logger.info("🛑 正在停止调度器...")
        self.running = False
        
        # 取消所有任务
        for task in self.tasks:
            task.cancel()
        
        # 等待任务清理
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        self.logger.info("✅ 调度器已停止")

def signal_handler(scheduler):
    """信号处理器"""
    def handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭...")
        asyncio.create_task(scheduler.stop())
    return handler

async def main():
    """主函数"""
    print("=" * 60)
    print("    金融数据服务 - 演示调度器")
    print("=" * 60)
    print()
    print("🎯 功能演示:")
    print("  • 模拟股票/指数/期货数据采集")
    print("  • Redis数据存储和管理")
    print("  • 系统状态监控")
    print("  • 自动数据清理")
    print("  • 异步任务调度")
    print()
    print("📊 监控信息:")
    print("  • 数据采集频率: 股票10s, 指数15s, 期货20s")
    print("  • 系统监控频率: 60s")
    print("  • 数据清理频率: 30分钟")
    print()
    print("🔧 Redis访问:")
    print("  • 实时数据: latest:* 键")
    print("  • 历史数据: market_data:* 键")
    print("  • 系统监控: system_monitor 键")
    print()
    print("按 Ctrl+C 停止服务")
    print("=" * 60)
    print()
    
    scheduler = DemoScheduler()
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler(scheduler))
    signal.signal(signal.SIGTERM, signal_handler(scheduler))
    
    try:
        await scheduler.start()
    except KeyboardInterrupt:
        print("\n收到中断信号...")
    except Exception as e:
        print(f"❌ 调度器异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await scheduler.stop()
        print("✅ 演示调度器已安全关闭")

if __name__ == "__main__":
    asyncio.run(main())