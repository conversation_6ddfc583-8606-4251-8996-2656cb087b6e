#include "grpc_server.h"
#include <spdlog/spdlog.h>
#include <random>
#include <algorithm>
#include <iomanip>
#include <sstream>

namespace financial_data {

// Enhanced StreamFlowController implementation
StreamFlowController::StreamFlowController(int32_t buffer_size, int32_t max_queue_size) 
    : pending_messages_(0), buffer_size_(buffer_size), max_queue_size_(max_queue_size),
      backpressure_active_(false), client_latency_us_(0.0) {}

bool StreamFlowController::CanSend() const {
    if (backpressure_active_.load()) {
        return false;
    }
    return pending_messages_.load() < buffer_size_.load();
}

void StreamFlowController::OnMessageSent() {
    int32_t current = pending_messages_.fetch_add(1);
    
    // Check if we need to apply backpressure
    if (current >= buffer_size_.load() * 0.8) { // 80% threshold
        ApplyBackpressure();
    }
}

void StreamFlowController::OnClientAck() {
    int32_t current = pending_messages_.load();
    if (current > 0) {
        pending_messages_.fetch_sub(1);
        
        // Release backpressure if queue is manageable
        if (current <= buffer_size_.load() * 0.5) { // 50% threshold
            ReleaseBackpressure();
        }
    }
}

void StreamFlowController::SetBufferSize(int32_t size) {
    buffer_size_.store(std::max(1, std::min(size, max_queue_size_.load())));
}

bool StreamFlowController::IsBackpressureActive() const {
    return backpressure_active_.load();
}

void StreamFlowController::ApplyBackpressure() {
    if (!backpressure_active_.exchange(true)) {
        spdlog::warn("Backpressure activated - client buffer full");
    }
}

void StreamFlowController::ReleaseBackpressure() {
    if (backpressure_active_.exchange(false)) {
        spdlog::info("Backpressure released - client buffer recovered");
    }
}

double StreamFlowController::GetUtilizationRatio() const {
    return static_cast<double>(pending_messages_.load()) / buffer_size_.load();
}

void StreamFlowController::UpdateClientLatency(double latency_us) {
    client_latency_us_.store(latency_us);
    AdjustBufferSize();
}

void StreamFlowController::AdjustBufferSize() {
    double latency = client_latency_us_.load();
    int32_t current_size = buffer_size_.load();
    
    // Adaptive buffer sizing based on client latency
    if (latency > 1000.0) { // High latency (>1ms)
        SetBufferSize(std::min(current_size * 2, max_queue_size_.load()));
    } else if (latency < 100.0) { // Low latency (<100μs)
        SetBufferSize(std::max(current_size / 2, 100));
    }
}

// Enhanced ClientConnectionManager implementation
void ClientConnectionManager::RegisterClient(const std::string& client_id, const std::string& server_address, bool is_primary) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    clients_[client_id] = {
        client_id,
        server_address,
        std::chrono::steady_clock::now(),
        0,
        0.0,
        0.0,
        0,
        true,
        is_primary
    };
    spdlog::info("Registered gRPC client: {} (server: {}, primary: {})", client_id, server_address, is_primary);
}

void ClientConnectionManager::UnregisterClient(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    auto it = clients_.find(client_id);
    if (it != clients_.end()) {
        if (it->second.primary) {
            spdlog::warn("Primary client {} unregistered, triggering failover", client_id);
            TriggerFailover(client_id);
        }
        clients_.erase(it);
        spdlog::info("Unregistered gRPC client: {}", client_id);
    }
}

void ClientConnectionManager::UpdateClientHealth(const std::string& client_id, bool healthy) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    auto it = clients_.find(client_id);
    if (it != clients_.end()) {
        bool was_healthy = it->second.healthy;
        it->second.healthy = healthy;
        it->second.last_seen = std::chrono::steady_clock::now();
        
        if (was_healthy && !healthy && it->second.primary) {
            spdlog::warn("Primary client {} became unhealthy, triggering failover", client_id);
            TriggerFailover(client_id);
        }
    }
}

void ClientConnectionManager::UpdateClientStats(const std::string& client_id, double latency_us, int32_t active_streams) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    auto it = clients_.find(client_id);
    if (it != clients_.end()) {
        it->second.avg_latency_us = latency_us;
        it->second.active_streams = active_streams;
        it->second.last_seen = std::chrono::steady_clock::now();
    }
}

void ClientConnectionManager::ReportClientError(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    auto it = clients_.find(client_id);
    if (it != clients_.end()) {
        it->second.error_count++;
        
        // Mark as unhealthy if too many errors
        if (it->second.error_count > 5) {
            it->second.healthy = false;
            if (it->second.primary) {
                spdlog::warn("Primary client {} has too many errors, triggering failover", client_id);
                TriggerFailover(client_id);
            }
        }
    }
}

std::string ClientConnectionManager::SelectBestClient() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    std::string best_client;
    double best_score = std::numeric_limits<double>::max();
    
    for (const auto& [client_id, info] : clients_) {
        if (info.healthy) {
            double score = CalculateClientScore(info);
            if (score < best_score) {
                best_score = score;
                best_client = client_id;
            }
        }
    }
    
    return best_client;
}

std::string ClientConnectionManager::SelectPrimaryClient() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    for (const auto& [client_id, info] : clients_) {
        if (info.healthy && info.primary) {
            return client_id;
        }
    }
    
    return "";
}

std::vector<std::string> ClientConnectionManager::GetHealthyClients() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    std::vector<std::string> healthy_clients;
    for (const auto& [client_id, info] : clients_) {
        if (info.healthy) {
            healthy_clients.push_back(client_id);
        }
    }
    
    return healthy_clients;
}

ClientConnectionManager::LoadBalancingStats ClientConnectionManager::GetStats() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    LoadBalancingStats stats = {};
    stats.total_clients = clients_.size();
    
    double total_latency = 0.0;
    int32_t healthy_count = 0;
    
    for (const auto& [client_id, info] : clients_) {
        if (info.healthy) {
            healthy_count++;
            total_latency += info.avg_latency_us;
            stats.total_streams += info.active_streams;
            
            if (info.primary) {
                stats.primary_server = info.server_address;
            }
        }
    }
    
    stats.healthy_clients = healthy_count;
    stats.avg_latency_us = healthy_count > 0 ? total_latency / healthy_count : 0.0;
    
    return stats;
}

void ClientConnectionManager::TriggerFailover(const std::string& failed_client_id) {
    if (failover_in_progress_.exchange(true)) {
        return; // Failover already in progress
    }
    
    last_failover_time_ = std::chrono::steady_clock::now();
    spdlog::warn("Failover triggered for client: {}", failed_client_id);
    
    // Find a healthy backup client to promote to primary
    for (auto& [client_id, info] : clients_) {
        if (info.healthy && !info.primary && client_id != failed_client_id) {
            info.primary = true;
            spdlog::info("Promoted client {} to primary", client_id);
            break;
        }
    }
    
    // Reset failover flag after a delay
    std::thread([this]() {
        std::this_thread::sleep_for(std::chrono::seconds(10));
        failover_in_progress_.store(false);
    }).detach();
}

bool ClientConnectionManager::IsFailoverInProgress() const {
    return failover_in_progress_.load();
}

double ClientConnectionManager::CalculateClientScore(const ClientInfo& client) const {
    // Lower score is better
    double score = 0.0;
    
    // Factor in active streams (more streams = higher score)
    score += client.active_streams * 10.0;
    
    // Factor in latency (higher latency = higher score)
    score += client.avg_latency_us * 0.01;
    
    // Factor in error count (more errors = higher score)
    score += client.error_count * 100.0;
    
    // Prefer primary clients (lower score)
    if (client.primary) {
        score *= 0.5;
    }
    
    return score;
}

// DataStreamProcessor implementation
DataStreamProcessor::DataStreamProcessor() : running_(false) {}

DataStreamProcessor::~DataStreamProcessor() {
    Stop();
}

void DataStreamProcessor::Start() {
    if (running_.exchange(true)) {
        return; // Already running
    }
    
    processor_thread_ = std::thread(&DataStreamProcessor::ProcessorLoop, this);
    spdlog::info("Data stream processor started");
}

void DataStreamProcessor::Stop() {
    if (!running_.exchange(false)) {
        return; // Already stopped
    }
    
    queue_cv_.notify_all();
    
    if (processor_thread_.joinable()) {
        processor_thread_.join();
    }
    
    spdlog::info("Data stream processor stopped");
}

void DataStreamProcessor::AddTickData(const TickData& tick) {
    {
        std::lock_guard<std::mutex> lock(queues_mutex_);
        tick_queue_.push(tick);
    }
    queue_cv_.notify_one();
}

void DataStreamProcessor::AddKlineData(const KlineData& kline) {
    {
        std::lock_guard<std::mutex> lock(queues_mutex_);
        kline_queue_.push(kline);
    }
    queue_cv_.notify_one();
}

void DataStreamProcessor::AddLevel2Data(const Level2Data& level2) {
    {
        std::lock_guard<std::mutex> lock(queues_mutex_);
        level2_queue_.push(level2);
    }
    queue_cv_.notify_one();
}

void DataStreamProcessor::SubscribeTickData(const std::string& stream_id,
                                           const TickDataRequest& request,
                                           grpc::ServerWriter<TickDataResponse>* writer) {
    std::lock_guard<std::mutex> lock(subscriptions_mutex_);
    
    auto subscription = std::make_unique<StreamSubscription>();
    subscription->stream_id = stream_id;
    subscription->type = "tick";
    subscription->symbols.assign(request.symbols().begin(), request.symbols().end());
    subscription->exchange = request.exchange();
    subscription->flow_controller = std::make_unique<StreamFlowController>(request.buffer_size());
    subscription->writer = static_cast<void*>(writer);
    subscription->last_activity = std::chrono::steady_clock::now();
    subscription->active = true;
    
    subscriptions_[stream_id] = std::move(subscription);
    spdlog::info("Subscribed to tick data stream: {}", stream_id);
}

void DataStreamProcessor::SubscribeKlineData(const std::string& stream_id,
                                            const KlineDataRequest& request,
                                            grpc::ServerWriter<KlineDataResponse>* writer) {
    std::lock_guard<std::mutex> lock(subscriptions_mutex_);
    
    auto subscription = std::make_unique<StreamSubscription>();
    subscription->stream_id = stream_id;
    subscription->type = "kline";
    subscription->symbols.assign(request.symbols().begin(), request.symbols().end());
    subscription->exchange = request.exchange();
    subscription->flow_controller = std::make_unique<StreamFlowController>(request.buffer_size());
    subscription->writer = static_cast<void*>(writer);
    subscription->last_activity = std::chrono::steady_clock::now();
    subscription->active = true;
    
    subscriptions_[stream_id] = std::move(subscription);
    spdlog::info("Subscribed to kline data stream: {}", stream_id);
}

void DataStreamProcessor::SubscribeLevel2Data(const std::string& stream_id,
                                             const Level2DataRequest& request,
                                             grpc::ServerWriter<Level2DataResponse>* writer) {
    std::lock_guard<std::mutex> lock(subscriptions_mutex_);
    
    auto subscription = std::make_unique<StreamSubscription>();
    subscription->stream_id = stream_id;
    subscription->type = "level2";
    subscription->symbols.assign(request.symbols().begin(), request.symbols().end());
    subscription->exchange = request.exchange();
    subscription->flow_controller = std::make_unique<StreamFlowController>(request.buffer_size());
    subscription->writer = static_cast<void*>(writer);
    subscription->last_activity = std::chrono::steady_clock::now();
    subscription->active = true;
    
    subscriptions_[stream_id] = std::move(subscription);
    spdlog::info("Subscribed to level2 data stream: {}", stream_id);
}

void DataStreamProcessor::UnsubscribeStream(const std::string& stream_id) {
    std::lock_guard<std::mutex> lock(subscriptions_mutex_);
    subscriptions_.erase(stream_id);
    spdlog::info("Unsubscribed from stream: {}", stream_id);
}

void DataStreamProcessor::ProcessorLoop() {
    while (running_) {
        std::unique_lock<std::mutex> lock(queues_mutex_);
        queue_cv_.wait(lock, [this] {
            return !running_ || !tick_queue_.empty() || !kline_queue_.empty() || !level2_queue_.empty();
        });
        
        if (!running_) break;
        
        ProcessTickData();
        ProcessKlineData();
        ProcessLevel2Data();
        
        lock.unlock();
        
        // Cleanup inactive streams periodically
        CleanupInactiveStreams();
        
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void DataStreamProcessor::ProcessTickData() {
    while (!tick_queue_.empty()) {
        TickData tick = tick_queue_.front();
        tick_queue_.pop();
        
        std::lock_guard<std::mutex> sub_lock(subscriptions_mutex_);
        for (auto& [stream_id, subscription] : subscriptions_) {
            if (subscription->type != "tick" || !subscription->active) continue;
            
            // Check if symbol matches subscription
            bool symbol_match = subscription->symbols.empty() ||
                std::find(subscription->symbols.begin(), subscription->symbols.end(), 
                         tick.symbol()) != subscription->symbols.end();
            
            if (!symbol_match || subscription->exchange != tick.exchange()) continue;
            
            // Check flow control
            if (!subscription->flow_controller->CanSend()) {
                spdlog::warn("Flow control: dropping tick data for stream {}", stream_id);
                continue;
            }
            
            try {
                TickDataResponse response;
                response.add_ticks()->CopyFrom(tick);
                response.set_has_more(true);
                *response.mutable_metadata() = CreateResponseMetadata();
                
                auto* writer = static_cast<grpc::ServerWriter<TickDataResponse>*>(subscription->writer);
                if (writer->Write(response)) {
                    subscription->flow_controller->OnMessageSent();
                    subscription->last_activity = std::chrono::steady_clock::now();
                } else {
                    subscription->active = false;
                    spdlog::warn("Failed to write to tick stream: {}", stream_id);
                }
            } catch (const std::exception& e) {
                spdlog::error("Error processing tick data for stream {}: {}", stream_id, e.what());
                subscription->active = false;
            }
        }
    }
}

void DataStreamProcessor::ProcessKlineData() {
    while (!kline_queue_.empty()) {
        KlineData kline = kline_queue_.front();
        kline_queue_.pop();
        
        std::lock_guard<std::mutex> sub_lock(subscriptions_mutex_);
        for (auto& [stream_id, subscription] : subscriptions_) {
            if (subscription->type != "kline" || !subscription->active) continue;
            
            bool symbol_match = subscription->symbols.empty() ||
                std::find(subscription->symbols.begin(), subscription->symbols.end(), 
                         kline.symbol()) != subscription->symbols.end();
            
            if (!symbol_match || subscription->exchange != kline.exchange()) continue;
            
            if (!subscription->flow_controller->CanSend()) {
                spdlog::warn("Flow control: dropping kline data for stream {}", stream_id);
                continue;
            }
            
            try {
                KlineDataResponse response;
                response.add_klines()->CopyFrom(kline);
                response.set_has_more(true);
                *response.mutable_metadata() = CreateResponseMetadata();
                
                auto* writer = static_cast<grpc::ServerWriter<KlineDataResponse>*>(subscription->writer);
                if (writer->Write(response)) {
                    subscription->flow_controller->OnMessageSent();
                    subscription->last_activity = std::chrono::steady_clock::now();
                } else {
                    subscription->active = false;
                    spdlog::warn("Failed to write to kline stream: {}", stream_id);
                }
            } catch (const std::exception& e) {
                spdlog::error("Error processing kline data for stream {}: {}", stream_id, e.what());
                subscription->active = false;
            }
        }
    }
}

void DataStreamProcessor::ProcessLevel2Data() {
    while (!level2_queue_.empty()) {
        Level2Data level2 = level2_queue_.front();
        level2_queue_.pop();
        
        std::lock_guard<std::mutex> sub_lock(subscriptions_mutex_);
        for (auto& [stream_id, subscription] : subscriptions_) {
            if (subscription->type != "level2" || !subscription->active) continue;
            
            bool symbol_match = subscription->symbols.empty() ||
                std::find(subscription->symbols.begin(), subscription->symbols.end(), 
                         level2.symbol()) != subscription->symbols.end();
            
            if (!symbol_match || subscription->exchange != level2.exchange()) continue;
            
            if (!subscription->flow_controller->CanSend()) {
                spdlog::warn("Flow control: dropping level2 data for stream {}", stream_id);
                continue;
            }
            
            try {
                Level2DataResponse response;
                response.add_level2_data()->CopyFrom(level2);
                *response.mutable_metadata() = CreateResponseMetadata();
                
                auto* writer = static_cast<grpc::ServerWriter<Level2DataResponse>*>(subscription->writer);
                if (writer->Write(response)) {
                    subscription->flow_controller->OnMessageSent();
                    subscription->last_activity = std::chrono::steady_clock::now();
                } else {
                    subscription->active = false;
                    spdlog::warn("Failed to write to level2 stream: {}", stream_id);
                }
            } catch (const std::exception& e) {
                spdlog::error("Error processing level2 data for stream {}: {}", stream_id, e.what());
                subscription->active = false;
            }
        }
    }
}

void DataStreamProcessor::CleanupInactiveStreams() {
    std::lock_guard<std::mutex> lock(subscriptions_mutex_);
    auto now = std::chrono::steady_clock::now();
    
    auto it = subscriptions_.begin();
    while (it != subscriptions_.end()) {
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(
            now - it->second->last_activity).count();
        
        if (!it->second->active || duration > 300) { // 5 minutes timeout
            spdlog::info("Cleaning up inactive stream: {}", it->first);
            it = subscriptions_.erase(it);
        } else {
            ++it;
        }
    }
}

ResponseMetadata DataStreamProcessor::CreateResponseMetadata() const {
    ResponseMetadata metadata;
    metadata.set_server_timestamp(
        std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
    metadata.set_server_id("grpc-server-1");
    
    // Use a simple sequence counter (thread-safe)
    static std::atomic<int32_t> sequence_counter{0};
    metadata.set_sequence_number(sequence_counter.fetch_add(1));
    
    // Calculate processing latency (simplified)
    static auto start_time = std::chrono::high_resolution_clock::now();
    auto current_time = std::chrono::high_resolution_clock::now();
    auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
        current_time - start_time).count();
    metadata.set_processing_latency_us(static_cast<double>(latency % 1000)); // Simplified latency
    
    return metadata;
}

// MarketDataServiceImpl implementation
MarketDataServiceImpl::MarketDataServiceImpl() 
    : stream_processor_(std::make_unique<DataStreamProcessor>()),
      connection_manager_(std::make_unique<ClientConnectionManager>()),
      running_(false) {
    stream_processor_->Start();
}

MarketDataServiceImpl::~MarketDataServiceImpl() {
    Stop();
}

grpc::Status MarketDataServiceImpl::StreamTickData(grpc::ServerContext* context,
                                                  const TickDataRequest* request,
                                                  grpc::ServerWriter<TickDataResponse>* writer) {
    if (!ValidateRequest(*request)) {
        return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "Invalid request parameters");
    }
    
    std::string stream_id = GenerateStreamId();
    spdlog::info("Starting tick data stream: {} for symbols: {}", 
                stream_id, request->symbols_size());
    
    stream_processor_->SubscribeTickData(stream_id, *request, writer);
    
    // Keep the stream alive until client disconnects
    while (!context->IsCancelled()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    stream_processor_->UnsubscribeStream(stream_id);
    spdlog::info("Tick data stream ended: {}", stream_id);
    
    return grpc::Status::OK;
}

grpc::Status MarketDataServiceImpl::GetHistoricalTickData(grpc::ServerContext* context,
                                                         const HistoricalTickDataRequest* request,
                                                         grpc::ServerWriter<TickDataResponse>* writer) {
    // TODO: Implement historical data retrieval from storage
    spdlog::info("Historical tick data request for symbol: {}", request->symbol());
    
    // Placeholder implementation
    TickDataResponse response;
    response.set_has_more(false);
    *response.mutable_metadata() = CreateResponseMetadata();
    
    writer->Write(response);
    
    return grpc::Status::OK;
}

grpc::Status MarketDataServiceImpl::StreamKlineData(grpc::ServerContext* context,
                                                   const KlineDataRequest* request,
                                                   grpc::ServerWriter<KlineDataResponse>* writer) {
    if (!ValidateRequest(*request)) {
        return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "Invalid request parameters");
    }
    
    std::string stream_id = GenerateStreamId();
    spdlog::info("Starting kline data stream: {} for symbols: {}", 
                stream_id, request->symbols_size());
    
    stream_processor_->SubscribeKlineData(stream_id, *request, writer);
    
    while (!context->IsCancelled()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    stream_processor_->UnsubscribeStream(stream_id);
    spdlog::info("Kline data stream ended: {}", stream_id);
    
    return grpc::Status::OK;
}

grpc::Status MarketDataServiceImpl::StreamLevel2Data(grpc::ServerContext* context,
                                                    const Level2DataRequest* request,
                                                    grpc::ServerWriter<Level2DataResponse>* writer) {
    if (!ValidateRequest(*request)) {
        return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "Invalid request parameters");
    }
    
    std::string stream_id = GenerateStreamId();
    spdlog::info("Starting level2 data stream: {} for symbols: {}", 
                stream_id, request->symbols_size());
    
    stream_processor_->SubscribeLevel2Data(stream_id, *request, writer);
    
    while (!context->IsCancelled()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    stream_processor_->UnsubscribeStream(stream_id);
    spdlog::info("Level2 data stream ended: {}", stream_id);
    
    return grpc::Status::OK;
}

grpc::Status MarketDataServiceImpl::HealthCheck(grpc::ServerContext* context,
                                               const HealthCheckRequest* request,
                                               HealthCheckResponse* response) {
    response->set_status(HealthCheckResponse::SERVING);
    response->set_message("Service is healthy");
    return grpc::Status::OK;
}

void MarketDataServiceImpl::Start(const std::string& server_address) {
    if (running_.exchange(true)) {
        return;
    }
    
    grpc::EnableDefaultHealthCheckService(true);
    grpc::reflection::InitProtoReflectionServerBuilderPlugin();
    
    grpc::ServerBuilder builder;
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    builder.RegisterService(this);
    
    // Configure server options
    builder.SetMaxReceiveMessageSize(4 * 1024 * 1024); // 4MB
    builder.SetMaxSendMessageSize(4 * 1024 * 1024);    // 4MB
    builder.AddChannelArgument(GRPC_ARG_KEEPALIVE_TIME_MS, 30000);
    builder.AddChannelArgument(GRPC_ARG_KEEPALIVE_TIMEOUT_MS, 5000);
    builder.AddChannelArgument(GRPC_ARG_KEEPALIVE_PERMIT_WITHOUT_CALLS, 1);
    
    server_ = builder.BuildAndStart();
    spdlog::info("gRPC server listening on {}", server_address);
}

void MarketDataServiceImpl::Stop() {
    if (!running_.exchange(false)) {
        return;
    }
    
    if (server_) {
        server_->Shutdown();
        server_.reset();
    }
    
    spdlog::info("gRPC server stopped");
}

void MarketDataServiceImpl::WaitForTermination() {
    if (server_) {
        server_->Wait();
    }
}

std::string MarketDataServiceImpl::GenerateStreamId() const {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);
    
    std::stringstream ss;
    ss << "stream_";
    for (int i = 0; i < 8; ++i) {
        ss << std::hex << dis(gen);
    }
    return ss.str();
}

ResponseMetadata MarketDataServiceImpl::CreateResponseMetadata() const {
    return stream_processor_->CreateResponseMetadata();
}

bool MarketDataServiceImpl::ValidateRequest(const TickDataRequest& request) const {
    return !request.exchange().empty() && request.buffer_size() > 0;
}

bool MarketDataServiceImpl::ValidateRequest(const KlineDataRequest& request) const {
    return !request.exchange().empty() && !request.period().empty() && request.buffer_size() > 0;
}

bool MarketDataServiceImpl::ValidateRequest(const Level2DataRequest& request) const {
    return !request.exchange().empty() && request.depth() > 0 && request.buffer_size() > 0;
}

// GrpcServer implementation
GrpcServer::GrpcServer() : running_(false) {}

GrpcServer::~GrpcServer() {
    Stop();
}

bool GrpcServer::Initialize(const std::string& server_address,
                           int max_receive_message_size,
                           int max_send_message_size) {
    server_address_ = server_address;
    max_receive_message_size_ = max_receive_message_size;
    max_send_message_size_ = max_send_message_size;
    
    service_impl_ = std::make_unique<MarketDataServiceImpl>();
    return true;
}

void GrpcServer::Start() {
    if (running_.exchange(true)) {
        return;
    }
    
    if (service_impl_) {
        service_impl_->Start(server_address_);
        spdlog::info("gRPC server started successfully");
    }
}

void GrpcServer::Stop() {
    if (!running_.exchange(false)) {
        return;
    }
    
    if (service_impl_) {
        service_impl_->Stop();
        service_impl_.reset();
    }
    
    spdlog::info("gRPC server stopped successfully");
}

void GrpcServer::WaitForTermination() {
    if (service_impl_) {
        service_impl_->WaitForTermination();
    }
}

} // namespace financial_data