"""
Utility functions for data conversion and processing
"""

import pandas as pd
import numpy as np
from typing import Union, List, Dict, Any, Optional
from datetime import datetime, timezone
import time
import json
import logging

logger = logging.getLogger(__name__)


class DataConverter:
    """
    Utility class for converting between different data formats and time representations
    """
    
    @staticmethod
    def to_nanoseconds(timestamp: Union[int, str, pd.Timestamp, datetime]) -> int:
        """
        Convert various timestamp formats to nanoseconds
        
        Args:
            timestamp: Timestamp in various formats
            
        Returns:
            Timestamp in nanoseconds since epoch
        """
        if timestamp is None:
            return int(time.time() * 1_000_000_000)
        
        if isinstance(timestamp, int):
            # Assume it's already in nanoseconds if > 1e15, otherwise seconds
            if timestamp > 1e15:
                return timestamp
            else:
                return timestamp * 1_000_000_000
        
        elif isinstance(timestamp, str):
            # Parse string timestamp
            try:
                dt = pd.to_datetime(timestamp)
                return int(dt.timestamp() * 1_000_000_000)
            except Exception as e:
                logger.error(f"Failed to parse timestamp string: {timestamp}, error: {e}")
                return int(time.time() * 1_000_000_000)
        
        elif isinstance(timestamp, pd.Timestamp):
            return int(timestamp.timestamp() * 1_000_000_000)
        
        elif isinstance(timestamp, datetime):
            return int(timestamp.timestamp() * 1_000_000_000)
        
        else:
            logger.warning(f"Unknown timestamp format: {type(timestamp)}")
            return int(time.time() * 1_000_000_000)
    
    @staticmethod
    def from_nanoseconds(timestamp_ns: int) -> datetime:
        """
        Convert nanoseconds timestamp to datetime
        
        Args:
            timestamp_ns: Timestamp in nanoseconds
            
        Returns:
            datetime object
        """
        return datetime.fromtimestamp(timestamp_ns / 1_000_000_000, tz=timezone.utc)
    
    @staticmethod
    def to_pandas_timestamp(timestamp: Union[int, str, datetime]) -> pd.Timestamp:
        """
        Convert timestamp to pandas Timestamp
        
        Args:
            timestamp: Timestamp in various formats
            
        Returns:
            pandas Timestamp
        """
        if isinstance(timestamp, int):
            # Assume nanoseconds if > 1e15, otherwise seconds
            if timestamp > 1e15:
                return pd.Timestamp(timestamp, unit='ns')
            else:
                return pd.Timestamp(timestamp, unit='s')
        else:
            return pd.Timestamp(timestamp)
    
    @staticmethod
    def normalize_symbol(symbol: str, exchange: str = None) -> str:
        """
        Normalize trading symbol format
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            
        Returns:
            Normalized symbol
        """
        # Remove common prefixes/suffixes and normalize case
        normalized = symbol.upper().strip()
        
        # Remove exchange prefixes if present
        if '.' in normalized:
            normalized = normalized.split('.')[0]
        
        # Add exchange suffix if provided
        if exchange:
            normalized = f"{normalized}.{exchange.upper()}"
        
        return normalized
    
    @staticmethod
    def convert_price_precision(price: float, tick_size: float) -> float:
        """
        Round price to appropriate tick size
        
        Args:
            price: Original price
            tick_size: Minimum price increment
            
        Returns:
            Price rounded to tick size
        """
        if tick_size <= 0:
            return price
        
        return round(price / tick_size) * tick_size
    
    @staticmethod
    def calculate_returns(prices: Union[pd.Series, np.ndarray], 
                         method: str = 'simple') -> Union[pd.Series, np.ndarray]:
        """
        Calculate returns from price series
        
        Args:
            prices: Price series
            method: 'simple' or 'log' returns
            
        Returns:
            Returns series
        """
        if isinstance(prices, pd.Series):
            if method == 'simple':
                return prices.pct_change()
            elif method == 'log':
                return np.log(prices / prices.shift(1))
        else:
            if method == 'simple':
                return np.diff(prices) / prices[:-1]
            elif method == 'log':
                return np.diff(np.log(prices))
        
        raise ValueError(f"Unknown return method: {method}")
    
    @staticmethod
    def resample_data(df: pd.DataFrame, 
                     frequency: str, 
                     price_col: str = 'close',
                     volume_col: str = 'volume') -> pd.DataFrame:
        """
        Resample tick data to different frequencies
        
        Args:
            df: DataFrame with tick data
            frequency: Pandas frequency string (e.g., '1T', '5T', '1H')
            price_col: Price column name
            volume_col: Volume column name
            
        Returns:
            Resampled DataFrame with OHLCV data
        """
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'datetime' in df.columns:
                df = df.set_index('datetime')
            elif 'timestamp' in df.columns:
                df['datetime'] = pd.to_datetime(df['timestamp'], unit='ns')
                df = df.set_index('datetime')
            else:
                raise ValueError("DataFrame must have datetime index or datetime/timestamp column")
        
        # Resample to OHLCV format
        resampled = df.resample(frequency).agg({
            price_col: ['first', 'max', 'min', 'last'],
            volume_col: 'sum' if volume_col in df.columns else lambda x: 0
        })
        
        # Flatten column names
        resampled.columns = ['open', 'high', 'low', 'close', 'volume']
        
        # Remove rows with no data
        resampled = resampled.dropna()
        
        return resampled


class DataValidator:
    """
    Data validation utilities for financial data
    """
    
    @staticmethod
    def validate_tick_data(tick_data: Dict) -> List[str]:
        """
        Validate tick data for common issues
        
        Args:
            tick_data: Tick data dictionary
            
        Returns:
            List of validation errors
        """
        errors = []
        
        # Required fields
        required_fields = ['timestamp', 'symbol', 'last_price', 'volume']
        for field in required_fields:
            if field not in tick_data or tick_data[field] is None:
                errors.append(f"Missing required field: {field}")
        
        # Price validation
        if 'last_price' in tick_data:
            price = tick_data['last_price']
            if not isinstance(price, (int, float)) or price <= 0:
                errors.append(f"Invalid price: {price}")
        
        # Volume validation
        if 'volume' in tick_data:
            volume = tick_data['volume']
            if not isinstance(volume, (int, float)) or volume < 0:
                errors.append(f"Invalid volume: {volume}")
        
        # Timestamp validation
        if 'timestamp' in tick_data:
            timestamp = tick_data['timestamp']
            if not isinstance(timestamp, (int, float)):
                errors.append(f"Invalid timestamp: {timestamp}")
            elif timestamp < 1e15:  # Likely not in nanoseconds
                errors.append(f"Timestamp appears to be in wrong unit: {timestamp}")
        
        # Bid/Ask validation
        if 'bid_prices' in tick_data and 'ask_prices' in tick_data:
            bids = tick_data['bid_prices']
            asks = tick_data['ask_prices']
            
            if bids and asks:
                if bids[0] >= asks[0]:
                    errors.append(f"Best bid ({bids[0]}) >= best ask ({asks[0]})")
        
        return errors
    
    @staticmethod
    def validate_kline_data(kline_data: Dict) -> List[str]:
        """
        Validate K-line data for common issues
        
        Args:
            kline_data: K-line data dictionary
            
        Returns:
            List of validation errors
        """
        errors = []
        
        # Required fields
        required_fields = ['timestamp', 'symbol', 'open', 'high', 'low', 'close', 'volume']
        for field in required_fields:
            if field not in kline_data or kline_data[field] is None:
                errors.append(f"Missing required field: {field}")
        
        # OHLC validation
        if all(field in kline_data for field in ['open', 'high', 'low', 'close']):
            o, h, l, c = kline_data['open'], kline_data['high'], kline_data['low'], kline_data['close']
            
            if not all(isinstance(x, (int, float)) and x > 0 for x in [o, h, l, c]):
                errors.append("Invalid OHLC values")
            elif h < max(o, c) or l > min(o, c):
                errors.append(f"Invalid OHLC relationship: O={o}, H={h}, L={l}, C={c}")
        
        return errors
    
    @staticmethod
    def detect_outliers(data: Union[pd.Series, np.ndarray], 
                       method: str = 'iqr',
                       threshold: float = 1.5) -> Union[pd.Series, np.ndarray]:
        """
        Detect outliers in data series
        
        Args:
            data: Data series
            method: 'iqr' or 'zscore'
            threshold: Threshold for outlier detection
            
        Returns:
            Boolean series/array indicating outliers
        """
        if method == 'iqr':
            if isinstance(data, pd.Series):
                Q1 = data.quantile(0.25)
                Q3 = data.quantile(0.75)
                IQR = Q3 - Q1
                return (data < (Q1 - threshold * IQR)) | (data > (Q3 + threshold * IQR))
            else:
                Q1 = np.percentile(data, 25)
                Q3 = np.percentile(data, 75)
                IQR = Q3 - Q1
                return (data < (Q1 - threshold * IQR)) | (data > (Q3 + threshold * IQR))
        
        elif method == 'zscore':
            if isinstance(data, pd.Series):
                z_scores = np.abs((data - data.mean()) / data.std())
                return z_scores > threshold
            else:
                z_scores = np.abs((data - np.mean(data)) / np.std(data))
                return z_scores > threshold
        
        else:
            raise ValueError(f"Unknown outlier detection method: {method}")


class PerformanceProfiler:
    """
    Performance profiling utilities for SDK operations
    """
    
    def __init__(self):
        self.timings = {}
        self.counters = {}
    
    def time_operation(self, operation_name: str):
        """
        Context manager for timing operations
        
        Args:
            operation_name: Name of the operation
        """
        return TimingContext(self, operation_name)
    
    def record_timing(self, operation_name: str, duration: float):
        """
        Record timing for an operation
        
        Args:
            operation_name: Name of the operation
            duration: Duration in seconds
        """
        if operation_name not in self.timings:
            self.timings[operation_name] = []
        self.timings[operation_name].append(duration)
    
    def increment_counter(self, counter_name: str, value: int = 1):
        """
        Increment a counter
        
        Args:
            counter_name: Name of the counter
            value: Value to increment by
        """
        if counter_name not in self.counters:
            self.counters[counter_name] = 0
        self.counters[counter_name] += value
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics
        
        Returns:
            Dictionary with performance stats
        """
        stats = {
            'timings': {},
            'counters': dict(self.counters)
        }
        
        for operation, times in self.timings.items():
            if times:
                stats['timings'][operation] = {
                    'count': len(times),
                    'total': sum(times),
                    'average': sum(times) / len(times),
                    'min': min(times),
                    'max': max(times),
                    'p50': np.percentile(times, 50),
                    'p95': np.percentile(times, 95),
                    'p99': np.percentile(times, 99)
                }
        
        return stats
    
    def reset(self):
        """Reset all statistics"""
        self.timings.clear()
        self.counters.clear()


class TimingContext:
    """Context manager for timing operations"""
    
    def __init__(self, profiler: PerformanceProfiler, operation_name: str):
        self.profiler = profiler
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.profiler.record_timing(self.operation_name, duration)


class ConfigManager:
    """
    Configuration management for the SDK
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_file: Path to configuration file
        """
        self.config = self._load_default_config()
        
        if config_file:
            self.load_from_file(config_file)
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration"""
        return {
            'servers': ['localhost:50051'],
            'max_retries': 3,
            'connection_timeout': 5.0,
            'cache': {
                'enabled': True,
                'max_size': 10000,
                'ttl_seconds': 300
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            'data_validation': {
                'enabled': True,
                'strict_mode': False
            },
            'performance': {
                'profiling_enabled': False,
                'batch_size': 1000
            }
        }
    
    def load_from_file(self, config_file: str):
        """
        Load configuration from file
        
        Args:
            config_file: Path to configuration file
        """
        try:
            with open(config_file, 'r') as f:
                file_config = json.load(f)
            
            # Merge with default config
            self._merge_config(self.config, file_config)
            
        except Exception as e:
            logger.error(f"Failed to load config file {config_file}: {e}")
    
    def _merge_config(self, base: Dict, override: Dict):
        """Recursively merge configuration dictionaries"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value
        
        Args:
            key: Configuration key (supports dot notation)
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        Set configuration value
        
        Args:
            key: Configuration key (supports dot notation)
            value: Value to set
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary"""
        return dict(self.config)


# Global instances
default_profiler = PerformanceProfiler()
default_config = ConfigManager()


# Utility functions
def setup_logging(level: str = 'INFO'):
    """
    Setup logging for the SDK
    
    Args:
        level: Logging level
    """
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def format_number(value: Union[int, float], precision: int = 2) -> str:
    """
    Format number with appropriate precision and thousand separators
    
    Args:
        value: Number to format
        precision: Decimal precision
        
    Returns:
        Formatted number string
    """
    if isinstance(value, int):
        return f"{value:,}"
    else:
        return f"{value:,.{precision}f}"


def calculate_statistics(data: Union[pd.Series, np.ndarray]) -> Dict[str, float]:
    """
    Calculate basic statistics for data series
    
    Args:
        data: Data series
        
    Returns:
        Dictionary with statistics
    """
    if isinstance(data, pd.Series):
        return {
            'count': len(data),
            'mean': data.mean(),
            'std': data.std(),
            'min': data.min(),
            'max': data.max(),
            'median': data.median(),
            'q25': data.quantile(0.25),
            'q75': data.quantile(0.75)
        }
    else:
        return {
            'count': len(data),
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'median': np.median(data),
            'q25': np.percentile(data, 25),
            'q75': np.percentile(data, 75)
        }