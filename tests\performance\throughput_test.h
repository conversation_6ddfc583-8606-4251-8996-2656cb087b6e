/**
 * @file throughput_test.h
 * @brief Throughput testing for financial data service
 */

#pragma once

#include "benchmark_runner.h"
#include <memory>

namespace performance_tests {

class TestUtils;

/**
 * @class ThroughputTest
 * @brief Comprehensive throughput testing for all system components
 * 
 * Tests system throughput to verify it meets the 1M msg/s requirement
 */
class ThroughputTest {
public:
    ThroughputTest();
    ~ThroughputTest();
    
    /**
     * @brief Test data ingestion throughput
     * @return ThroughputResult with ingestion performance metrics
     * 
     * Requirement: System must handle 1M msg/s ingestion (Req 2.1)
     */
    ThroughputResult TestDataIngestionThroughput();
    
    /**
     * @brief Test WebSocket broadcast throughput
     * @return ThroughputResult for WebSocket message broadcasting
     */
    ThroughputResult TestWebSocketBroadcastThroughput();
    
    /**
     * @brief Test storage write throughput
     * @return ThroughputResult for storage write operations
     */
    ThroughputResult TestStorageWriteThroughput();
    
    /**
     * @brief Test query throughput
     * @return ThroughputResult for data query operations
     */
    ThroughputResult TestQueryThroughput();

private:
    std::unique_ptr<TestUtils> test_utils_;
};

} // namespace performance_tests