/**
 * 端到端集成测试 - C++版本
 * 测试完整的数据采集到存储流程，验证多数据源协调工作，测试故障转移和恢复机制
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <chrono>
#include <thread>
#include <future>
#include <vector>
#include <memory>
#include <fstream>
#include <json/json.h>

#include "../../src/storage/unified_data_access.h"
#include "../../src/storage/storage_layer_selector.h"
#include "../../src/storage/query_performance_optimizer.h"
#include "../../src/config/config_manager.h"
#include "../../src/data_models.h"

using namespace std::chrono_literals;
using testing::_;
using testing::Return;
using testing::InSequence;

class EndToEndIntegrationTest : public ::testing::Test {
protected:
    void SetUpTestSuite() {
        // 初始化测试配置
        setupTestConfig();
        
        // 初始化组件
        config_manager_ = std::make_unique<ConfigManager>();
        config_manager_->LoadFromFile("config/test_config.json");
        
        unified_access_ = std::make_unique<UnifiedDataAccessInterface>(
            config_manager_->GetStorageConfig()
        );
        
        storage_selector_ = std::make_unique<StorageLayerSelector>(
            config_manager_->GetStorageConfig()
        );
        
        query_optimizer_ = std::make_unique<QueryPerformanceOptimizer>(
            config_manager_->GetStorageConfig()
        );
        
        // 测试数据
        test_symbols_ = {"000001", "000002", "600000"};
        test_start_timestamp_ = 1704067200000000000LL; // 2024-01-01 00:00:00 UTC
        test_end_timestamp_ = 1706745600000000000LL;   // 2024-02-01 00:00:00 UTC
        
        std::cout << "End-to-end integration test setup completed" << std::endl;
    }
    
    void TearDownTestSuite() {
        // 清理测试数据
        cleanupTestData();
        std::cout << "End-to-end integration test cleanup completed" << std::endl;
    }
    
    void SetUp() override {
        test_start_time_ = std::chrono::steady_clock::now();
        std::cout << "Starting test: " << ::testing::UnitTest::GetInstance()
                     ->current_test_info()->name() << std::endl;
    }
    
    void TearDown() override {
        auto test_duration = std::chrono::steady_clock::now() - test_start_time_;
        auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(test_duration);
        std::cout << "Test completed in " << duration_ms.count() << "ms" << std::endl;
    }

private:
    void setupTestConfig() {
        Json::Value config;
        config["storage"]["redis"]["host"] = "localhost";
        config["storage"]["redis"]["port"] = 6379;
        config["storage"]["redis"]["db"] = 1; // 测试数据库
        
        config["storage"]["clickhouse"]["host"] = "localhost";
        config["storage"]["clickhouse"]["port"] = 8123;
        config["storage"]["clickhouse"]["database"] = "test_market_data";
        config["storage"]["clickhouse"]["user"] = "default";
        
        config["storage"]["s3"]["bucket"] = "test-market-data";
        config["storage"]["s3"]["region"] = "us-east-1";
        
        config["storage"]["hot_storage_days"] = 7;
        config["storage"]["warm_storage_days"] = 730;
        
        // 保存测试配置
        std::ofstream config_file("config/test_config.json");
        config_file << config;
    }
    
    void cleanupTestData() {
        // 清理Redis测试数据
        // 清理ClickHouse测试数据
        // 清理S3测试数据
    }

protected:
    static std::unique_ptr<ConfigManager> config_manager_;
    static std::unique_ptr<UnifiedDataAccessInterface> unified_access_;
    static std::unique_ptr<StorageLayerSelector> storage_selector_;
    static std::unique_ptr<QueryPerformanceOptimizer> query_optimizer_;
    
    static std::vector<std::string> test_symbols_;
    static int64_t test_start_timestamp_;
    static int64_t test_end_timestamp_;
    
    std::chrono::steady_clock::time_point test_start_time_;
};

// 静态成员定义
std::unique_ptr<ConfigManager> EndToEndIntegrationTest::config_manager_;
std::unique_ptr<UnifiedDataAccessInterface> EndToEndIntegrationTest::unified_access_;
std::unique_ptr<StorageLayerSelector> EndToEndIntegrationTest::storage_selector_;
std::unique_ptr<QueryPerformanceOptimizer> EndToEndIntegrationTest::query_optimizer_;
std::vector<std::string> EndToEndIntegrationTest::test_symbols_;
int64_t EndToEndIntegrationTest::test_start_timestamp_;
int64_t EndToEndIntegrationTest::test_end_timestamp_;

TEST_F(EndToEndIntegrationTest, CompleteDataFlowTest) {
    std::cout << "Testing complete data flow from collection to storage" << std::endl;
    
    // 1. 创建测试数据
    std::vector<StandardTick> test_ticks;
    for (int i = 0; i < 1000; ++i) {
        StandardTick tick;
        tick.symbol = test_symbols_[0];
        tick.timestamp_ns = test_start_timestamp_ + i * 1000000000LL; // 每秒一个tick
        tick.price = 10.0 + (i % 100) * 0.01;
        tick.volume = 1000 + i;
        tick.bid_price = tick.price - 0.01;
        tick.ask_price = tick.price + 0.01;
        tick.bid_volume = tick.volume / 2;
        tick.ask_volume = tick.volume / 2;
        
        test_ticks.push_back(tick);
    }
    
    // 2. 测试数据存储
    auto store_future = unified_access_->StoreBatchAsync(test_ticks);
    auto store_result = store_future.get();
    
    EXPECT_TRUE(store_result.success) << "数据存储失败: " << store_result.error_message;
    EXPECT_EQ(store_result.stored_count, test_ticks.size()) 
        << "存储数量不匹配: 期望 " << test_ticks.size() << ", 实际 " << store_result.stored_count;
    
    std::cout << "Successfully stored " << store_result.stored_count << " ticks" << std::endl;
    
    // 3. 测试数据查询
    QueryRequest query_request;
    query_request.symbol = test_symbols_[0];
    query_request.data_type = "tick";
    query_request.start_timestamp_ns = test_start_timestamp_;
    query_request.end_timestamp_ns = test_end_timestamp_;
    query_request.limit = 1000;
    
    auto query_future = unified_access_->QueryData(query_request);
    auto query_response = query_future.get();
    
    EXPECT_TRUE(query_response.success) << "数据查询失败: " << query_response.error_message;
    EXPECT_GT(query_response.ticks.size(), 0) << "查询结果为空";
    EXPECT_LE(query_response.query_time.count(), 1000) << "查询时间过长: " 
        << query_response.query_time.count() << "ms";
    
    std::cout << "Successfully queried " << query_response.ticks.size() 
              << " ticks in " << query_response.query_time.count() << "ms" << std::endl;
    
    // 4. 验证数据完整性
    EXPECT_GE(query_response.ticks.size(), test_ticks.size() * 0.9) 
        << "查询到的数据量过少";
    
    // 验证数据顺序
    for (size_t i = 1; i < query_response.ticks.size(); ++i) {
        EXPECT_GE(query_response.ticks[i].timestamp_ns, 
                 query_response.ticks[i-1].timestamp_ns)
            << "数据时间戳顺序错误";
    }
    
    std::cout << "Complete data flow test passed" << std::endl;
}

TEST_F(EndToEndIntegrationTest, MultiSourceCoordinationTest) {
    std::cout << "Testing multi-source coordination" << std::endl;
    
    // 模拟多个数据源并发写入
    std::vector<std::future<bool>> futures;
    
    for (const auto& symbol : test_symbols_) {
        auto future = std::async(std::launch::async, [this, symbol]() {
            return simulateDataSourceCollection(symbol);
        });
        futures.push_back(std::move(future));
    }
    
    // 等待所有数据源完成
    int success_count = 0;
    for (auto& future : futures) {
        if (future.get()) {
            success_count++;
        }
    }
    
    EXPECT_EQ(success_count, test_symbols_.size()) 
        << "多数据源协调失败: " << success_count << "/" << test_symbols_.size() << " 成功";
    
    std::cout << "Multi-source coordination test passed: " 
              << success_count << "/" << test_symbols_.size() << " sources successful" << std::endl;
}

TEST_F(EndToEndIntegrationTest, FailoverAndRecoveryTest) {
    std::cout << "Testing failover and recovery mechanisms" << std::endl;
    
    // 1. 测试存储层故障转移
    std::string test_symbol = "FAILOVER_TEST";
    
    // 创建测试数据
    std::vector<StandardTick> test_ticks;
    for (int i = 0; i < 100; ++i) {
        StandardTick tick;
        tick.symbol = test_symbol;
        tick.timestamp_ns = test_start_timestamp_ + i * 1000000000LL;
        tick.price = 10.0 + i * 0.01;
        tick.volume = 1000 + i;
        
        test_ticks.push_back(tick);
    }
    
    // 模拟主存储故障，测试故障转移
    // 这里应该有实际的故障注入逻辑
    auto store_result = unified_access_->StoreBatchAsync(test_ticks).get();
    
    // 即使主存储故障，也应该能够通过备用存储成功
    EXPECT_TRUE(store_result.success || store_result.fallback_used) 
        << "故障转移机制失败";
    
    // 2. 测试数据恢复
    QueryRequest recovery_query;
    recovery_query.symbol = test_symbol;
    recovery_query.data_type = "tick";
    recovery_query.start_timestamp_ns = test_start_timestamp_;
    recovery_query.end_timestamp_ns = test_end_timestamp_;
    recovery_query.limit = 1000;
    
    auto recovery_response = unified_access_->QueryData(recovery_query).get();
    
    EXPECT_TRUE(recovery_response.success) << "数据恢复查询失败";
    EXPECT_GT(recovery_response.ticks.size(), 0) << "恢复的数据为空";
    
    std::cout << "Failover and recovery test passed" << std::endl;
}

TEST_F(EndToEndIntegrationTest, StorageLayerRoutingTest) {
    std::cout << "Testing storage layer routing" << std::endl;
    
    auto now = std::chrono::system_clock::now();
    auto now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        now.time_since_epoch()).count();
    
    // 测试不同时间范围的数据路由到不同存储层
    struct TestCase {
        std::string name;
        int64_t timestamp_ns;
        std::string expected_layer;
    };
    
    std::vector<TestCase> test_cases = {
        {"hot_storage", now_ns - 24 * 3600 * 1000000000LL, "hot"},      // 1天前
        {"warm_storage", now_ns - 30 * 24 * 3600 * 1000000000LL, "warm"}, // 30天前
        {"cold_storage", now_ns - 800 * 24 * 3600 * 1000000000LL, "cold"}  // 800天前
    };
    
    for (const auto& test_case : test_cases) {
        auto selected_layer = storage_selector_->SelectStorageLayer(test_case.timestamp_ns);
        
        EXPECT_EQ(selected_layer, test_case.expected_layer)
            << "存储层路由错误: " << test_case.name 
            << " 应该路由到 " << test_case.expected_layer
            << ", 实际路由到 " << selected_layer;
    }
    
    std::cout << "Storage layer routing test passed" << std::endl;
}

TEST_F(EndToEndIntegrationTest, QueryPerformanceTest) {
    std::cout << "Testing query performance optimization" << std::endl;
    
    // 1. 准备大量测试数据
    std::vector<StandardTick> large_dataset;
    for (int i = 0; i < 10000; ++i) {
        StandardTick tick;
        tick.symbol = test_symbols_[0];
        tick.timestamp_ns = test_start_timestamp_ + i * 1000000000LL;
        tick.price = 10.0 + (i % 1000) * 0.01;
        tick.volume = 1000 + i;
        
        large_dataset.push_back(tick);
    }
    
    // 存储数据
    auto store_result = unified_access_->StoreBatchAsync(large_dataset).get();
    EXPECT_TRUE(store_result.success) << "大数据集存储失败";
    
    // 2. 测试查询性能
    QueryRequest perf_query;
    perf_query.symbol = test_symbols_[0];
    perf_query.data_type = "tick";
    perf_query.start_timestamp_ns = test_start_timestamp_;
    perf_query.end_timestamp_ns = test_end_timestamp_;
    perf_query.limit = 5000;
    
    auto start_time = std::chrono::steady_clock::now();
    auto query_response = unified_access_->QueryData(perf_query).get();
    auto query_duration = std::chrono::steady_clock::now() - start_time;
    
    auto query_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(query_duration);
    
    EXPECT_TRUE(query_response.success) << "性能测试查询失败";
    EXPECT_GT(query_response.ticks.size(), 0) << "性能测试查询结果为空";
    EXPECT_LT(query_time_ms.count(), 5000) << "查询时间过长: " << query_time_ms.count() << "ms";
    
    std::cout << "Query performance test passed: " 
              << query_response.ticks.size() << " records in " 
              << query_time_ms.count() << "ms" << std::endl;
    
    // 3. 测试查询优化器
    auto optimization_stats = query_optimizer_->GetOptimizationStats();
    
    EXPECT_GE(optimization_stats.cache_hit_rate, 0.0) << "缓存命中率统计异常";
    EXPECT_GE(optimization_stats.avg_query_time_ms, 0.0) << "平均查询时间统计异常";
    
    std::cout << "Query optimization stats - Cache hit rate: " 
              << optimization_stats.cache_hit_rate * 100 << "%, "
              << "Avg query time: " << optimization_stats.avg_query_time_ms << "ms" << std::endl;
}

TEST_F(EndToEndIntegrationTest, DataConsistencyTest) {
    std::cout << "Testing data consistency across storage layers" << std::endl;
    
    std::string consistency_symbol = "CONSISTENCY_TEST";
    
    // 1. 创建包含重复数据的测试集
    std::vector<StandardTick> test_data_with_duplicates;
    
    // 添加正常数据
    for (int i = 0; i < 100; ++i) {
        StandardTick tick;
        tick.symbol = consistency_symbol;
        tick.timestamp_ns = test_start_timestamp_ + i * 1000000000LL;
        tick.price = 10.0 + i * 0.01;
        tick.volume = 1000 + i;
        
        test_data_with_duplicates.push_back(tick);
    }
    
    // 添加重复数据（相同时间戳，不同价格）
    for (int i = 0; i < 10; ++i) {
        StandardTick duplicate_tick;
        duplicate_tick.symbol = consistency_symbol;
        duplicate_tick.timestamp_ns = test_start_timestamp_ + i * 1000000000LL; // 重复时间戳
        duplicate_tick.price = 10.0 + i * 0.01 + 0.001; // 略微不同的价格
        duplicate_tick.volume = 1000 + i + 1;
        
        test_data_with_duplicates.push_back(duplicate_tick);
    }
    
    // 2. 存储数据（应该触发去重逻辑）
    auto store_result = unified_access_->StoreBatchAsync(test_data_with_duplicates).get();
    EXPECT_TRUE(store_result.success) << "一致性测试数据存储失败";
    
    // 3. 查询数据验证去重效果
    QueryRequest consistency_query;
    consistency_query.symbol = consistency_symbol;
    consistency_query.data_type = "tick";
    consistency_query.start_timestamp_ns = test_start_timestamp_;
    consistency_query.end_timestamp_ns = test_end_timestamp_;
    consistency_query.limit = 1000;
    
    auto query_response = unified_access_->QueryData(consistency_query).get();
    
    EXPECT_TRUE(query_response.success) << "一致性测试查询失败";
    
    // 验证去重效果：应该只有100条唯一记录
    std::set<int64_t> unique_timestamps;
    for (const auto& tick : query_response.ticks) {
        unique_timestamps.insert(tick.timestamp_ns);
    }
    
    EXPECT_EQ(unique_timestamps.size(), 100) 
        << "数据去重失败: 期望100个唯一时间戳, 实际" << unique_timestamps.size() << "个";
    
    std::cout << "Data consistency test passed: " 
              << unique_timestamps.size() << " unique records from " 
              << test_data_with_duplicates.size() << " input records" << std::endl;
}

TEST_F(EndToEndIntegrationTest, ConcurrentAccessTest) {
    std::cout << "Testing concurrent access patterns" << std::endl;
    
    const int num_threads = 5;
    const int operations_per_thread = 100;
    
    std::vector<std::future<bool>> futures;
    std::atomic<int> success_count{0};
    
    // 启动多个并发线程进行读写操作
    for (int thread_id = 0; thread_id < num_threads; ++thread_id) {
        auto future = std::async(std::launch::async, [this, thread_id, operations_per_thread, &success_count]() {
            bool thread_success = true;
            
            for (int op = 0; op < operations_per_thread; ++op) {
                // 创建线程特定的测试数据
                StandardTick tick;
                tick.symbol = "CONCURRENT_" + std::to_string(thread_id);
                tick.timestamp_ns = test_start_timestamp_ + op * 1000000000LL;
                tick.price = 10.0 + thread_id + op * 0.01;
                tick.volume = 1000 + thread_id * 1000 + op;
                
                // 写入操作
                auto store_result = unified_access_->StoreBatchAsync({tick}).get();
                if (!store_result.success) {
                    thread_success = false;
                    break;
                }
                
                // 读取操作
                QueryRequest query;
                query.symbol = tick.symbol;
                query.data_type = "tick";
                query.start_timestamp_ns = tick.timestamp_ns;
                query.end_timestamp_ns = tick.timestamp_ns + 1;
                query.limit = 1;
                
                auto query_result = unified_access_->QueryData(query).get();
                if (!query_result.success) {
                    thread_success = false;
                    break;
                }
            }
            
            if (thread_success) {
                success_count++;
            }
            
            return thread_success;
        });
        
        futures.push_back(std::move(future));
    }
    
    // 等待所有线程完成
    for (auto& future : futures) {
        future.get();
    }
    
    EXPECT_EQ(success_count.load(), num_threads) 
        << "并发访问测试失败: " << success_count.load() << "/" << num_threads << " 线程成功";
    
    std::cout << "Concurrent access test passed: " 
              << success_count.load() << "/" << num_threads << " threads successful" << std::endl;
}

// 辅助方法
bool EndToEndIntegrationTest::simulateDataSourceCollection(const std::string& symbol) {
    try {
        // 模拟数据采集
        std::vector<StandardTick> ticks;
        for (int i = 0; i < 50; ++i) {
            StandardTick tick;
            tick.symbol = symbol;
            tick.timestamp_ns = test_start_timestamp_ + i * 1000000000LL;
            tick.price = 10.0 + i * 0.01;
            tick.volume = 1000 + i;
            
            ticks.push_back(tick);
        }
        
        // 存储数据
        auto store_result = unified_access_->StoreBatchAsync(ticks).get();
        return store_result.success;
        
    } catch (const std::exception& e) {
        std::cerr << "Data source simulation failed for " << symbol << ": " << e.what() << std::endl;
        return false;
    }
}

// 测试报告生成
class IntegrationTestReporter {
public:
    static void GenerateReport(const ::testing::TestResult& result) {
        Json::Value report;
        report["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        report["total_tests"] = result.total_part_count();
        report["failed_tests"] = result.failed_part_count();
        report["success_rate"] = (result.total_part_count() - result.failed_part_count()) * 100.0 / result.total_part_count();
        
        // 保存报告
        std::ofstream report_file("tests/integration/cpp_integration_test_report.json");
        report_file << report;
        
        std::cout << "Integration test report generated" << std::endl;
    }
};

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    // 设置测试环境
    setenv("TESTING", "1", 1);
    
    // 运行测试
    int result = RUN_ALL_TESTS();
    
    return result;
}