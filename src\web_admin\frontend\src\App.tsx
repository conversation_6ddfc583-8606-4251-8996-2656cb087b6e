import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout, Menu, Avatar, Dropdown, message, theme } from 'antd';
import {
  DashboardOutlined,
  MonitorOutlined,
  UserOutlined,
  BellOutlined,
  SettingOutlined,
  SearchOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

import Login from './components/Login';
import Dashboard from './components/Dashboard';
import SystemMonitor from './components/SystemMonitor';
import UserManagement from './components/UserManagement';
import AlertManagement from './components/AlertManagement';
import DataQuery from './components/DataQuery';
import SystemConfig from './components/SystemConfig';
import { AuthProvider, useAuth } from './contexts/AuthContext';

import './App.css';

const { Header, Sider, Content } = Layout;

const App: React.FC = () => {
  return (
    <AuthProvider>
      <Router>
        <AppContent />
      </Router>
    </AuthProvider>
  );
};

const AppContent: React.FC = () => {
  const { user, logout } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  if (!user) {
    return <Login />;
  }

  const menuItems: MenuProps['items'] = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '系统概览',
    },
    {
      key: '/monitor',
      icon: <MonitorOutlined />,
      label: '系统监控',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
      disabled: user.role !== 'admin',
    },
    {
      key: '/alerts',
      icon: <BellOutlined />,
      label: '告警管理',
    },
    {
      key: '/query',
      icon: <SearchOutlined />,
      label: '数据查询',
    },
    {
      key: '/config',
      icon: <SettingOutlined />,
      label: '系统配置',
      disabled: user.role === 'viewer',
    },
  ];

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        logout();
        message.success('已退出登录');
      },
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div className="logo">
          <h2 style={{ color: 'white', textAlign: 'center', margin: '16px 0' }}>
            {collapsed ? 'FDS' : '金融数据服务'}
          </h2>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={['/']}
          items={menuItems}
          onClick={({ key }) => {
            window.location.hash = key;
          }}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: 0, background: colorBgContainer }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '100%' }}>
            <div>
              {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
                className: 'trigger',
                onClick: () => setCollapsed(!collapsed),
                style: { fontSize: '18px', padding: '0 24px' }
              })}
            </div>
            <div style={{ paddingRight: '24px' }}>
              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>
                  <Avatar icon={<UserOutlined />} style={{ marginRight: '8px' }} />
                  <span>{user.username}</span>
                </div>
              </Dropdown>
            </div>
          </div>
        </Header>
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
          }}
        >
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/monitor" element={<SystemMonitor />} />
            <Route path="/users" element={<UserManagement />} />
            <Route path="/alerts" element={<AlertManagement />} />
            <Route path="/query" element={<DataQuery />} />
            <Route path="/config" element={<SystemConfig />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

export default App;