"""
Pydantic models for API request/response schemas
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class ExchangeEnum(str, Enum):
    """Supported exchanges"""
    SHFE = "SHFE"  # Shanghai Futures Exchange
    DCE = "DCE"    # Dalian Commodity Exchange
    CZCE = "CZCE"  # Zhengzhou Commodity Exchange
    CFFEX = "CFFEX"  # China Financial Futures Exchange
    SSE = "SSE"    # Shanghai Stock Exchange
    SZSE = "SZSE"  # Shenzhen Stock Exchange


class ProductTypeEnum(str, Enum):
    """Product types"""
    FUTURES = "futures"
    STOCK = "stock"
    OPTION = "option"
    FOREX = "forex"


class PeriodEnum(str, Enum):
    """K-line periods"""
    MIN_1 = "1m"
    MIN_5 = "5m"
    MIN_15 = "15m"
    MIN_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"


class PriceLevel(BaseModel):
    """Price level for order book"""
    price: float = Field(..., description="Price level")
    volume: int = Field(..., description="Volume at this price level")
    order_count: Optional[int] = Field(None, description="Number of orders at this level")


class TickData(BaseModel):
    """Tick data model"""
    timestamp: int = Field(..., description="Timestamp in nanoseconds")
    symbol: str = Field(..., description="Contract symbol")
    exchange: str = Field(..., description="Exchange code")
    last_price: float = Field(..., description="Last traded price")
    volume: int = Field(..., description="Cumulative volume")
    turnover: float = Field(..., description="Cumulative turnover")
    open_interest: Optional[int] = Field(None, description="Open interest")
    bids: List[PriceLevel] = Field(default_factory=list, description="Bid levels")
    asks: List[PriceLevel] = Field(default_factory=list, description="Ask levels")
    sequence: int = Field(..., description="Sequence number")
    trade_flag: Optional[str] = Field(None, description="Trade flag")


class KlineData(BaseModel):
    """K-line data model"""
    timestamp: int = Field(..., description="Timestamp in nanoseconds")
    symbol: str = Field(..., description="Contract symbol")
    exchange: str = Field(..., description="Exchange code")
    period: str = Field(..., description="K-line period")
    open: float = Field(..., description="Opening price")
    high: float = Field(..., description="Highest price")
    low: float = Field(..., description="Lowest price")
    close: float = Field(..., description="Closing price")
    volume: int = Field(..., description="Volume")
    turnover: float = Field(..., description="Turnover")
    open_interest: Optional[int] = Field(None, description="Open interest")


class Level2Data(BaseModel):
    """Level-2 market depth data"""
    timestamp: int = Field(..., description="Timestamp in nanoseconds")
    symbol: str = Field(..., description="Contract symbol")
    exchange: str = Field(..., description="Exchange code")
    bids: List[PriceLevel] = Field(..., description="Bid levels (up to 10 levels)")
    asks: List[PriceLevel] = Field(..., description="Ask levels (up to 10 levels)")
    last_price: Optional[float] = Field(None, description="Last traded price")
    sequence: int = Field(..., description="Sequence number")


class PaginationInfo(BaseModel):
    """Pagination information"""
    has_next: bool = Field(..., description="Whether there are more records")
    next_cursor: Optional[str] = Field(None, description="Cursor for next page")
    total_count: Optional[int] = Field(None, description="Total count if available")
    page_size: int = Field(..., description="Current page size")


class BaseRequest(BaseModel):
    """Base request model"""
    symbol: str = Field(..., description="Contract symbol")
    exchange: Optional[str] = Field(None, description="Exchange code")
    start_time: Optional[datetime] = Field(None, description="Start timestamp")
    end_time: Optional[datetime] = Field(None, description="End timestamp")
    limit: int = Field(1000, ge=1, le=10000, description="Maximum number of records")
    cursor: Optional[str] = Field(None, description="Pagination cursor")

    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Symbol cannot be empty')
        return v.strip().upper()

    @field_validator('exchange')
    @classmethod
    def validate_exchange(cls, v):
        if v is not None:
            v = v.strip().upper()
            if v not in [e.value for e in ExchangeEnum]:
                raise ValueError(f'Invalid exchange. Must be one of: {[e.value for e in ExchangeEnum]}')
        return v


class TickDataRequest(BaseRequest):
    """Request model for tick data queries"""
    pass


class KlineDataRequest(BaseRequest):
    """Request model for K-line data queries"""
    period: str = Field("1m", description="K-line period")

    @field_validator('period')
    @classmethod
    def validate_period(cls, v):
        if v not in [p.value for p in PeriodEnum]:
            raise ValueError(f'Invalid period. Must be one of: {[p.value for p in PeriodEnum]}')
        return v


class Level2DataRequest(BaseRequest):
    """Request model for Level-2 data queries"""
    limit: int = Field(1000, ge=1, le=5000, description="Maximum number of records")


class BaseResponse(BaseModel):
    """Base response model"""
    success: bool = Field(True, description="Whether the request was successful")
    message: Optional[str] = Field(None, description="Response message")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    pagination: Optional[PaginationInfo] = Field(None, description="Pagination information")


class TickDataResponse(BaseResponse):
    """Response model for tick data queries"""
    data: List[TickData] = Field(default_factory=list, description="Tick data records")


class KlineDataResponse(BaseResponse):
    """Response model for K-line data queries"""
    data: List[KlineData] = Field(default_factory=list, description="K-line data records")


class Level2DataResponse(BaseResponse):
    """Response model for Level-2 data queries"""
    data: List[Level2Data] = Field(default_factory=list, description="Level-2 data records")


class PaginationResponse(BaseModel):
    """Generic pagination response"""
    data: List[Dict[str, Any]] = Field(default_factory=list, description="Data records")
    pagination: PaginationInfo = Field(..., description="Pagination information")


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    status_code: int = Field(..., description="HTTP status code")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat(), description="Error timestamp")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class SymbolInfo(BaseModel):
    """Symbol/contract information"""
    symbol: str = Field(..., description="Contract symbol")
    exchange: str = Field(..., description="Exchange code")
    product_type: str = Field(..., description="Product type")
    underlying: Optional[str] = Field(None, description="Underlying asset")
    expiry_date: Optional[datetime] = Field(None, description="Expiry date")
    contract_size: Optional[float] = Field(None, description="Contract size")
    tick_size: Optional[float] = Field(None, description="Minimum tick size")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class QueryStats(BaseModel):
    """Query statistics"""
    query_time_ms: float = Field(..., description="Query execution time in milliseconds")
    records_returned: int = Field(..., description="Number of records returned")
    cache_hit: bool = Field(..., description="Whether result was served from cache")
    data_source: str = Field(..., description="Data source used (hot/warm/cold)")