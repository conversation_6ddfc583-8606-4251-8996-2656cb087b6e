# CTP行情采集器使用指南

## 概述

CTP行情采集器是金融数据服务系统的核心组件，负责从CTP（综合交易平台）接收实时行情数据，并将其转换为标准化格式供系统其他组件使用。

## 主要特性

- **多交易所支持**: 支持上期所、大商所、郑商所、中金所等主要期货交易所
- **微秒级延迟**: 端到端延迟小于50微秒
- **自动重连**: 5秒内自动重连，支持数据回补
- **数据完整性**: 零数据丢失，支持序列号检查
- **高并发**: 支持1000个并发客户端连接
- **实时监控**: 提供心跳检测和连接状态监控

## 快速开始

### 1. 配置文件

创建CTP配置文件 `config/ctp_config.json`:

```json
{
  "ctp": {
    "front_address": "tcp://***************:10131",
    "broker_id": "9999",
    "user_id": "your_user_id",
    "password": "your_password",
    "flow_path": "./flow/",
    "heartbeat_interval": 30,
    "reconnect_interval": 5,
    "max_reconnect_attempts": 10,
    "enable_level2": true
  }
}
```

### 2. 基本使用

```cpp
#include "collectors/ctp_collector.h"

using namespace financial_data;

int main() {
    // 创建采集器
    auto collector = CTPCollectorFactory::CreateFromFile("config/ctp_config.json");
    
    // 设置数据回调
    collector->SetDataCallback([](const MarketDataWrapper& data) {
        if (data.type == MarketDataWrapper::DataType::TICK) {
            const auto& tick = data.tick_data;
            std::cout << "Received tick: " << tick.symbol 
                      << " Price: " << tick.last_price 
                      << " Volume: " << tick.volume << std::endl;
        }
    });
    
    // 设置状态回调
    collector->SetStatusCallback([](ConnectionStatus status, const std::string& message) {
        std::cout << "Status changed: " << static_cast<int>(status) 
                  << " - " << message << std::endl;
    });
    
    // 启动并连接
    collector->Start();
    collector->Connect();
    
    // 订阅合约
    std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409"};
    collector->Subscribe(symbols);
    
    // 保持运行
    std::this_thread::sleep_for(std::chrono::minutes(5));
    
    // 关闭
    collector->Shutdown();
    
    return 0;
}
```

### 3. 运行示例程序

```bash
# 编译项目
mkdir build && cd build
cmake ..
make

# 运行示例
./examples/ctp_collector_demo config/ctp_config.json CU2409 AL2409 ZN2409
```

## API参考

### CTPMarketDataCollector类

#### 初始化方法

```cpp
// 从配置对象初始化
bool Initialize(const CTPConfig& config);

// 从配置文件初始化
bool Initialize(const std::string& config_path);
```

#### 连接管理

```cpp
// 连接到CTP服务器
bool Connect();

// 断开连接
void Disconnect();

// 检查连接状态
bool IsConnected() const;
ConnectionStatus GetConnectionStatus() const;
```

#### 订阅管理

```cpp
// 订阅单个合约
bool Subscribe(const std::string& symbol);

// 批量订阅
bool Subscribe(const std::vector<std::string>& symbols);

// 取消订阅
bool Unsubscribe(const std::string& symbol);
bool Unsubscribe(const std::vector<std::string>& symbols);

// 获取已订阅合约列表
std::vector<std::string> GetSubscribedSymbols() const;

// 清空所有订阅
void ClearSubscriptions();
```

#### 回调设置

```cpp
// 设置数据回调函数
void SetDataCallback(const MarketDataCallback& callback);

// 设置状态变化回调函数
void SetStatusCallback(const ConnectionStatusCallback& callback);
```

#### 控制方法

```cpp
// 启动采集器
void Start();

// 停止采集器
void Stop();

// 关闭采集器
void Shutdown();
```

#### 监控方法

```cpp
// 获取统计信息
Statistics GetStatistics() const;

// 重置统计信息
void ResetStatistics();

// 健康检查
bool IsHealthy() const;
std::string GetHealthStatus() const;
```

### 数据结构

#### StandardTick - 标准化Tick数据

```cpp
struct StandardTick {
    int64_t timestamp_ns;           // 纳秒级时间戳
    std::string symbol;             // 合约代码
    std::string exchange;           // 交易所代码
    double last_price;              // 最新价
    uint64_t volume;                // 成交量
    double turnover;                // 成交额
    uint64_t open_interest;         // 持仓量
    uint32_t sequence;              // 序列号
    std::string trade_flag;         // 成交标志
    std::array<PriceLevel, 5> bids; // 五档买盘
    std::array<PriceLevel, 5> asks; // 五档卖盘
};
```

#### Level2Data - Level2深度数据

```cpp
struct Level2Data {
    int64_t timestamp_ns;           // 纳秒级时间戳
    std::string symbol;             // 合约代码
    std::string exchange;           // 交易所代码
    std::vector<PriceLevel> bids;   // 买盘档位（最多10档）
    std::vector<PriceLevel> asks;   // 卖盘档位（最多10档）
    uint32_t sequence;              // 序列号
};
```

#### MarketDataWrapper - 市场数据包装器

```cpp
struct MarketDataWrapper {
    enum class DataType { TICK, LEVEL2, UNKNOWN };
    
    DataType type;
    StandardTick tick_data;
    Level2Data level2_data;
    int64_t receive_time_ns;        // 接收时间戳
    std::string source;             // 数据源标识
};
```

### 配置选项

#### CTPConfig配置结构

```cpp
struct CTPConfig {
    std::string front_address;          // 前置机地址
    std::string broker_id;              // 经纪商代码
    std::string user_id;                // 用户代码
    std::string password;               // 密码
    std::string flow_path;              // 流文件保存路径
    int heartbeat_interval = 30;        // 心跳间隔(秒)
    int reconnect_interval = 5;         // 重连间隔(秒)
    int max_reconnect_attempts = 10;    // 最大重连次数
    bool enable_level2 = true;          // 是否启用Level2数据
};
```

## 性能优化

### 1. 内存优化

- 使用对象池减少内存分配
- 预分配数据队列空间
- 避免频繁的字符串拷贝

```cpp
// 设置队列预分配大小
MarketDataBatch batch;
batch.data.reserve(1000);  // 预分配1000个元素空间
```

### 2. 延迟优化

- 使用Lock-Free队列
- 减少数据拷贝
- 优化序列化过程

```cpp
// 使用移动语义避免拷贝
MarketDataWrapper wrapper(std::move(tick_data));
data_queue_.push(std::move(wrapper));
```

### 3. 吞吐量优化

- 批量处理数据
- 多线程并行处理
- 异步I/O操作

```cpp
// 批量处理数据
while (!data_queue_.empty() && batch_size < MAX_BATCH_SIZE) {
    auto data = std::move(data_queue_.front());
    data_queue_.pop();
    batch.AddData(std::move(data));
    batch_size++;
}
```

## 错误处理

### 1. 连接错误

```cpp
collector->SetStatusCallback([](ConnectionStatus status, const std::string& message) {
    if (status == ConnectionStatus::ERROR) {
        std::cerr << "Connection error: " << message << std::endl;
        // 实施错误恢复策略
    }
});
```

### 2. 数据错误

```cpp
collector->SetDataCallback([](const MarketDataWrapper& data) {
    if (!data.IsValid()) {
        std::cerr << "Invalid data received" << std::endl;
        return;
    }
    
    // 处理有效数据
    ProcessMarketData(data);
});
```

### 3. 网络错误

采集器会自动处理网络错误：
- 自动重连机制
- 数据回补功能
- 心跳检测

## 监控和诊断

### 1. 统计信息监控

```cpp
auto stats = collector->GetStatistics();
std::cout << "Received: " << stats.total_received << std::endl;
std::cout << "Processed: " << stats.total_processed << std::endl;
std::cout << "Errors: " << stats.total_errors << std::endl;
std::cout << "Rate: " << stats.messages_per_second << " msg/s" << std::endl;
```

### 2. 健康状态检查

```cpp
if (!collector->IsHealthy()) {
    std::string status = collector->GetHealthStatus();
    std::cerr << "Collector unhealthy: " << status << std::endl;
}
```

### 3. 日志记录

采集器使用spdlog进行日志记录：

```cpp
// 设置日志级别
spdlog::set_level(spdlog::level::debug);

// 日志会自动记录到控制台和文件
```

## 最佳实践

### 1. 生产环境部署

- 使用专用的CTP账户
- 配置适当的心跳间隔
- 启用数据持久化
- 设置监控告警

### 2. 性能调优

- 根据数据量调整队列大小
- 使用多个采集器实例负载均衡
- 优化网络配置

### 3. 故障恢复

- 实施主备切换机制
- 配置数据备份策略
- 建立故障恢复流程

## 常见问题

### Q: 如何处理CTP登录失败？

A: 检查配置文件中的用户名、密码和经纪商代码是否正确，确保CTP账户状态正常。

### Q: 数据延迟过高怎么办？

A: 检查网络连接质量，优化数据处理逻辑，考虑使用更高性能的硬件。

### Q: 如何确保数据完整性？

A: 启用序列号检查，监控数据间隔，实施数据回补机制。

### Q: 支持哪些交易所？

A: 目前支持上期所(SHFE)、大商所(DCE)、郑商所(CZCE)、中金所(CFFEX)等主要期货交易所。

## 技术支持

如有问题，请联系技术支持团队或查看项目文档：
- GitHub Issues: [项目地址]
- 技术文档: `docs/`目录
- 示例代码: `examples/`目录