#pragma once

#include "security_config.h"
#include <string>
#include <unordered_map>
#include <chrono>
#include <memory>

namespace financial_data {
namespace security {

struct UserInfo {
    std::string user_id;
    std::string username;
    std::string email;
    std::vector<std::string> roles;
    std::unordered_map<std::string, std::string> attributes;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point last_login;
    bool mfa_enabled;
    std::string mfa_secret;
};

struct TokenInfo {
    std::string token;
    std::string refresh_token;
    std::chrono::system_clock::time_point expires_at;
    std::chrono::system_clock::time_point refresh_expires_at;
    std::string user_id;
    std::vector<std::string> scopes;
};

class JWTAuth {
public:
    explicit JWTAuth(const JWTConfig& config);
    ~JWTAuth();

    // 初始化JWT认证系统
    bool Initialize();
    
    // 用户认证
    TokenInfo Authenticate(const std::string& username, const std::string& password);
    
    // 多因素认证
    bool VerifyMFA(const std::string& user_id, const std::string& mfa_code);
    
    // 验证JWT令牌
    bool VerifyToken(const std::string& token, UserInfo& user_info);
    
    // 刷新令牌
    TokenInfo RefreshToken(const std::string& refresh_token);
    
    // 撤销令牌
    bool RevokeToken(const std::string& token);
    
    // 生成JWT令牌
    std::string GenerateToken(const UserInfo& user_info, const std::vector<std::string>& scopes);
    
    // 解析JWT令牌
    bool ParseToken(const std::string& token, std::unordered_map<std::string, std::string>& claims);
    
    // 用户管理
    bool CreateUser(const UserInfo& user_info, const std::string& password);
    bool UpdateUser(const UserInfo& user_info);
    bool DeleteUser(const std::string& user_id);
    UserInfo GetUser(const std::string& user_id);
    
    // MFA管理
    std::string GenerateMFASecret(const std::string& user_id);
    bool EnableMFA(const std::string& user_id, const std::string& secret);
    bool DisableMFA(const std::string& user_id);

private:
    JWTConfig config_;
    std::unordered_map<std::string, UserInfo> users_;
    std::unordered_map<std::string, std::string> password_hashes_;
    std::unordered_map<std::string, TokenInfo> active_tokens_;
    std::unordered_map<std::string, std::string> revoked_tokens_;
    bool initialized_;
    
    // 密码哈希
    std::string HashPassword(const std::string& password, const std::string& salt);
    std::string GenerateSalt();
    bool VerifyPassword(const std::string& password, const std::string& hash);
    
    // JWT操作
    std::string CreateJWT(const std::unordered_map<std::string, std::string>& claims);
    bool ValidateJWT(const std::string& token);
    
    // MFA操作
    std::string GenerateTOTP(const std::string& secret, uint64_t timestamp);
    bool VerifyTOTP(const std::string& secret, const std::string& code);
    
    // 令牌管理
    void CleanupExpiredTokens();
    bool IsTokenRevoked(const std::string& token);
    
    // 数据持久化
    bool LoadUsers();
    bool SaveUsers();
};

} // namespace security
} // namespace financial_data