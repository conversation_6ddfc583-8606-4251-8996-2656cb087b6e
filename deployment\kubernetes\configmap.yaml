apiVersion: v1
kind: ConfigMap
metadata:
  name: market-data-config
  namespace: market-data
data:
  unified_config.json: |
    {
      "collection": {
        "pytdx": {
          "enabled": true,
          "servers": [
            {"host": "**************", "port": 7709},
            {"host": "************", "port": 7709},
            {"host": "*************", "port": 7709}
          ],
          "batch_size": 1000,
          "concurrent_requests": 5,
          "archive_enabled": true,
          "archive_batch_size": 5000
        },
        "ctp": {
          "enabled": true,
          "config_path": "/app/config/ctp_config.json",
          "failover_timeout": 30
        },
        "coordination": {
          "priority_source": "ctp",
          "overlap_tolerance_seconds": 300,
          "enable_data_merge": true
        }
      },
      "storage": {
        "hot_storage": {
          "type": "redis",
          "retention_days": 7,
          "config": {
            "host": "redis-service",
            "port": 6379,
            "db": 0,
            "password": "",
            "max_connections": 100
          }
        },
        "warm_storage": {
          "type": "clickhouse",
          "retention_days": 730,
          "config": {
            "host": "clickhouse-service",
            "port": 9000,
            "database": "market_data",
            "username": "market_user",
            "password": "market_password"
          }
        },
        "cold_storage": {
          "type": "s3",
          "config": {
            "endpoint": "http://minio-service:9000",
            "access_key": "minioadmin",
            "secret_key": "minioadmin123",
            "bucket": "market-data-archive",
            "region": "us-east-1"
          }
        }
      },
      "scheduling": {
        "historical_update": {
          "cron": "0 2 * * *",
          "symbols": ["all"],
          "lookback_days": 1
        },
        "data_migration": {
          "cron": "0 3 * * *",
          "batch_size": 10000
        }
      },
      "monitoring": {
        "enable_metrics": true,
        "alert_thresholds": {
          "data_delay_seconds": 60,
          "error_rate_percent": 5.0
        }
      }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: market-data
data:
  redis.conf: |
    bind 0.0.0.0
    port 6379
    timeout 0
    tcp-keepalive 300
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    databases 16
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir ./
    maxmemory 2gb
    maxmemory-policy allkeys-lru