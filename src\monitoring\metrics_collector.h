#pragma once

#include "prometheus_metrics.h"
#include "latency_monitor.h"
#include "data_integrity_checker.h"
#include "resource_monitor.h"
#include "alert_manager.h"
#include <memory>
#include <atomic>
#include <thread>
#include <chrono>

namespace monitoring {

struct MonitoringConfig {
    // Prometheus configuration
    std::string prometheus_bind_address = "0.0.0.0:9090";
    
    // Latency monitoring
    double latency_threshold_microseconds = 50.0;
    std::chrono::seconds latency_alert_cooldown{30};
    
    // Data integrity monitoring
    std::chrono::seconds missing_data_timeout{5};
    std::chrono::seconds integrity_alert_cooldown{30};
    uint64_t max_sequence_gap = 100;
    
    // Resource monitoring
    std::chrono::seconds resource_monitoring_interval{5};
    double cpu_threshold = 85.0;
    double memory_threshold = 85.0;
    double disk_threshold = 85.0;
    std::chrono::seconds resource_alert_cooldown{300};
    
    // Alert manager
    int max_alert_retries = 3;
    std::chrono::seconds alert_retry_delay{5};
    std::chrono::seconds alert_rate_limit_window{60};
    int max_alerts_per_window = 10;
};

class MetricsCollector {
public:
    explicit MetricsCollector(const MonitoringConfig& config = MonitoringConfig{});
    ~MetricsCollector();
    
    // Lifecycle management
    bool initialize();
    bool start();
    void stop();
    bool isRunning() const { return running_.load(); }
    
    // Component access
    PrometheusMetrics& getPrometheusMetrics() { return PrometheusMetrics::getInstance(); }
    LatencyMonitor& getLatencyMonitor() { return *latency_monitor_; }
    DataIntegrityChecker& getDataIntegrityChecker() { return *data_integrity_checker_; }
    ResourceMonitor& getResourceMonitor() { return *resource_monitor_; }
    AlertManager& getAlertManager() { return *alert_manager_; }
    
    // Configuration updates
    void updateConfig(const MonitoringConfig& config);
    const MonitoringConfig& getConfig() const { return config_; }
    
    // Convenience methods for common operations
    void recordLatency(const std::string& operation, double latency_microseconds, 
                      const std::string& symbol = "");
    uint64_t startLatencyMeasurement(const std::string& operation, const std::string& symbol = "");
    void endLatencyMeasurement(uint64_t measurement_id);
    
    void recordDataMessage(const std::string& symbol, uint64_t sequence_number, 
                          const std::string& source = "", const std::string& message_type = "tick");
    
    void sendAlert(const std::string& type, const std::string& severity, 
                  const std::string& message, 
                  const std::unordered_map<std::string, std::string>& metadata = {});
    
    // Statistics and health check
    struct SystemHealth {
        bool prometheus_healthy;
        bool latency_monitor_healthy;
        bool data_integrity_healthy;
        bool resource_monitor_healthy;
        bool alert_manager_healthy;
        
        double average_latency_microseconds;
        double max_latency_microseconds;
        uint64_t latency_violations;
        
        uint64_t total_messages_received;
        uint64_t total_messages_lost;
        double data_loss_rate;
        
        double current_cpu_usage;
        double current_memory_usage;
        double current_disk_usage;
        
        uint64_t total_alerts_sent;
        uint64_t total_alerts_failed;
        uint64_t alerts_rate_limited;
    };
    
    SystemHealth getSystemHealth() const;
    void resetAllStatistics();
    
    // Alert channel management
    void addEmailAlerts(const std::string& smtp_server, int port,
                       const std::string& username, const std::string& password,
                       const std::vector<std::string>& recipients);
    void addWebhookAlerts(const std::string& webhook_url);
    void addSlackAlerts(const std::string& webhook_url, const std::string& channel);
    
private:
    MonitoringConfig config_;
    std::atomic<bool> running_{false};
    std::atomic<bool> initialized_{false};
    
    // Monitoring components
    std::shared_ptr<AlertManager> alert_manager_;
    std::unique_ptr<LatencyMonitor> latency_monitor_;
    std::unique_ptr<DataIntegrityChecker> data_integrity_checker_;
    std::unique_ptr<ResourceMonitor> resource_monitor_;
    
    // Health check thread
    std::thread health_check_thread_;
    std::chrono::seconds health_check_interval_{30};
    
    // Initialization and cleanup
    bool initializeComponents();
    void cleanupComponents();
    
    // Health monitoring
    void healthCheckLoop();
    void performHealthCheck();
    bool checkComponentHealth(const std::string& component_name, 
                            const std::function<bool()>& health_check);
    
    // Configuration application
    void applyLatencyConfig();
    void applyIntegrityConfig();
    void applyResourceConfig();
    void applyAlertConfig();
};

} // namespace monitoring