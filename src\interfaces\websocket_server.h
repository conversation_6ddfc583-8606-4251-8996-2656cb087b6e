#pragma once

#include <memory>
#include <thread>
#include <atomic>
#include <functional>
#include <spdlog/spdlog.h>

#include "websocket_handler.h"
#include "websocket_types.h"
#include "data_types.h"
#include "data_bus.h"

namespace financial_data {
namespace interfaces {

/**
 * @brief WebSocket服务器
 * 
 * 高性能WebSocket服务器，支持1000个并发连接，提供实时市场数据分发
 */
class WebSocketServer {
private:
    WebSocketConfig config_;
    std::unique_ptr<WebSocketHandler> handler_;
    std::shared_ptr<databus::DataBus> data_bus_;
    
    std::thread server_thread_;
    std::atomic<bool> running_{false};
    
    std::shared_ptr<spdlog::logger> logger_;

public:
    explicit WebSocketServer(const WebSocketConfig& config = WebSocketConfig{});
    ~WebSocketServer();

    /**
     * @brief 初始化服务器
     */
    bool Initialize();

    /**
     * @brief 启动服务器
     */
    bool Start();

    /**
     * @brief 停止服务器
     */
    void Stop();

    /**
     * @brief 检查是否正在运行
     */
    bool IsRunning() const { return running_.load(); }

    /**
     * @brief 设置数据总线
     */
    void SetDataBus(std::shared_ptr<databus::DataBus> data_bus);

    /**
     * @brief 获取WebSocket处理器
     */
    std::shared_ptr<WebSocketHandler> GetHandler() const {
        return std::shared_ptr<WebSocketHandler>(handler_.get(), [](WebSocketHandler*){});
    }

    /**
     * @brief 获取订阅管理器
     */
    std::shared_ptr<SubscriptionManager> GetSubscriptionManager() const;

    /**
     * @brief 获取心跳管理器
     */
    std::shared_ptr<HeartbeatManager> GetHeartbeatManager() const;

    /**
     * @brief 获取连接数量
     */
    size_t GetConnectionCount() const;

    /**
     * @brief 获取活跃连接数量
     */
    size_t GetActiveConnectionCount() const;

    /**
     * @brief 广播消息
     */
    void BroadcastMessage(const std::string& message);

    /**
     * @brief 发送消息到指定客户端
     */
    bool SendMessageToClient(const std::string& client_id, const std::string& message);

    /**
     * @brief 断开客户端连接
     */
    bool DisconnectClient(const std::string& client_id, const std::string& reason = "");

    /**
     * @brief 获取配置
     */
    WebSocketConfig GetConfig() const { return config_; }

    /**
     * @brief 更新配置
     */
    bool UpdateConfig(const WebSocketConfig& config);

    /**
     * @brief 获取统计信息
     */
    WebSocketStatistics GetStatistics() const;

    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

    /**
     * @brief 获取统计摘要
     */
    std::string GetStatisticsSummary() const;

    /**
     * @brief 获取健康状态
     */
    WebSocketHandler::HealthStatus GetHealthStatus() const;

    /**
     * @brief 运行性能测试
     */
    struct PerformanceTestResult {
        bool success;
        uint64_t total_messages_sent;
        uint64_t total_clients_connected;
        double avg_latency_ms;
        double max_latency_ms;
        double throughput_msg_per_sec;
        uint64_t test_duration_ms;
        std::string error_message;
    };
    
    PerformanceTestResult RunPerformanceTest(size_t num_clients = 100, 
                                           size_t messages_per_client = 1000,
                                           uint32_t test_duration_ms = 60000);

private:
    /**
     * @brief 服务器运行线程
     */
    void ServerLoop();

    /**
     * @brief 数据总线回调处理器
     */
    bool HandleDataBusMessage(const MarketDataWrapper& data);

    /**
     * @brief 注册数据总线回调
     */
    void RegisterDataBusCallback();

    /**
     * @brief 取消注册数据总线回调
     */
    void UnregisterDataBusCallback();
};

/**
 * @brief WebSocket服务器工厂
 */
class WebSocketServerFactory {
public:
    /**
     * @brief 创建默认配置的服务器
     */
    static std::unique_ptr<WebSocketServer> CreateDefault(uint16_t port = 8080);

    /**
     * @brief 创建高性能配置的服务器
     */
    static std::unique_ptr<WebSocketServer> CreateHighPerformance(uint16_t port = 8080);

    /**
     * @brief 创建低延迟配置的服务器
     */
    static std::unique_ptr<WebSocketServer> CreateLowLatency(uint16_t port = 8080);

    /**
     * @brief 从配置文件创建服务器
     */
    static std::unique_ptr<WebSocketServer> CreateFromConfig(const std::string& config_file);

    /**
     * @brief 从配置创建服务器
     */
    static std::unique_ptr<WebSocketServer> CreateFromConfig(const WebSocketConfig& config);
};

} // namespace interfaces
} // namespace financial_data