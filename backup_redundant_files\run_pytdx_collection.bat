@echo off
chcp 65001 > nul
echo ========================================
echo PyTDX数据采集系统
echo ========================================
echo.

echo 第一步: 更新代码表
echo ----------------------------------------
python update_symbol_lists.py
if %errorlevel% neq 0 (
    echo ❌ 代码表更新失败
    pause
    exit /b 1
)

echo.
echo 第二步: 采集历史数据
echo ----------------------------------------
python collect_historical_data.py
if %errorlevel% neq 0 (
    echo ❌ 历史数据采集失败
    pause
    exit /b 1
)

echo.
echo ✅ 数据采集完成！
echo 数据已保存到数据库和本地文件
echo.
pause