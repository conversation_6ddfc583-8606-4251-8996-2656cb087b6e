#pragma once

#include "security_config.h"
#include "tls_manager.h"
#include "encryption_manager.h"
#include "jwt_auth.h"
#include "rbac_manager.h"
#include "audit_logger.h"
#include <memory>
#include <string>
#include <unordered_map>

namespace financial_data {
namespace security {

class SecurityManager {
public:
    explicit SecurityManager(const SecurityConfig& config);
    ~SecurityManager();

    // 初始化安全管理器
    bool Initialize();
    
    // 获取各个组件
    TLSManager* GetTLSManager() { return tls_manager_.get(); }
    EncryptionManager* GetEncryptionManager() { return encryption_manager_.get(); }
    JWTAuth* GetJWTAuth() { return jwt_auth_.get(); }
    RBACManager* GetRBACManager() { return rbac_manager_.get(); }
    AuditLogger* GetAuditLogger() { return audit_logger_.get(); }
    
    // 统一的认证和授权接口
    struct AuthResult {
        bool success;
        std::string user_id;
        std::string session_id;
        std::vector<std::string> roles;
        std::string error_message;
    };
    
    AuthResult AuthenticateUser(const std::string& username, const std::string& password,
                               const std::string& source_ip, const std::string& user_agent);
    
    bool AuthorizeAction(const std::string& user_id, const std::string& resource,
                        const std::string& action, const std::unordered_map<std::string, std::string>& context = {});
    
    bool ValidateSession(const std::string& token, std::string& user_id);
    
    void LogoutUser(const std::string& user_id, const std::string& session_id);
    
    // 数据加密/解密接口
    bool EncryptSensitiveData(const std::string& data, std::string& encrypted_data);
    bool DecryptSensitiveData(const std::string& encrypted_data, std::string& data);
    
    // 安全配置管理
    bool UpdateSecurityConfig(const SecurityConfig& new_config);
    SecurityConfig GetSecurityConfig() const { return config_; }
    
    // 安全状态监控
    struct SecurityStatus {
        bool tls_enabled;
        bool encryption_enabled;
        bool jwt_enabled;
        bool rbac_enabled;
        bool audit_enabled;
        int active_sessions;
        int failed_login_attempts_last_hour;
        std::chrono::system_clock::time_point last_security_incident;
    };
    
    SecurityStatus GetSecurityStatus();
    
    // 安全事件处理
    void HandleSecurityIncident(const std::string& incident_type, const std::string& description,
                               const std::string& user_id = "", const std::string& source_ip = "");
    
    // 清理和维护
    void PerformSecurityMaintenance();

private:
    SecurityConfig config_;
    std::unique_ptr<TLSManager> tls_manager_;
    std::unique_ptr<EncryptionManager> encryption_manager_;
    std::unique_ptr<JWTAuth> jwt_auth_;
    std::unique_ptr<RBACManager> rbac_manager_;
    std::unique_ptr<AuditLogger> audit_logger_;
    
    // 会话管理
    struct SessionInfo {
        std::string user_id;
        std::string session_id;
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point last_activity;
        std::string source_ip;
        std::string user_agent;
    };
    
    std::unordered_map<std::string, SessionInfo> active_sessions_;
    std::mutex sessions_mutex_;
    
    // 安全统计
    std::unordered_map<std::string, int> failed_login_attempts_;
    std::mutex security_stats_mutex_;
    
    bool initialized_;
    
    // 内部方法
    std::string GenerateSessionId();
    void CleanupExpiredSessions();
    void UpdateFailedLoginAttempts(const std::string& source_ip, bool success);
    bool IsIPBlocked(const std::string& source_ip);
    
    // 权限映射
    Permission MapActionToPermission(const std::string& action);
    ResourceType MapResourceToType(const std::string& resource);
    Action MapStringToAction(const std::string& action);
};

} // namespace security
} // namespace financial_data