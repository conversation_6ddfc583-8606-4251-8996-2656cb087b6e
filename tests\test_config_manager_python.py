"""
Unit tests for Python configuration manager
"""

import unittest
import tempfile
import os
import json
import time
import threading
from unittest.mock import Mock, patch
from src.config.config_manager_python import (
    PythonConfigManager, ConfigChangeType, ConfigChangeEvent,
    ConfigValidator, ConfigChangeListener, ValidationResult
)
from src.config.python_validators import (
    PytdxConfigValidator, CollectionConfigValidator,
    StorageConfigValidator, SchedulingConfigValidator,
    MonitoringConfigValidator
)


class MockConfigValidator(ConfigValidator):
    """Mock配置验证器"""
    
    def __init__(self, is_valid=True, errors=None, warnings=None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
    
    def validate(self, config):
        result = ValidationResult()
        result.is_valid = self.is_valid
        result.errors = self.errors.copy()
        result.warnings = self.warnings.copy()
        return result
    
    def get_validator_name(self):
        return "MockConfigValidator"


class MockConfigChangeListener(ConfigChangeListener):
    """Mock配置变更监听器"""
    
    def __init__(self):
        self.events = []
    
    def on_config_changed(self, event):
        self.events.append(event)


class TestPythonConfigManager(unittest.TestCase):
    """Python配置管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config = {
            "server": {
                "host": "localhost",
                "port": 8080,
                "threads": 4
            },
            "redis": {
                "host": "127.0.0.1",
                "port": 6379,
                "database": 0
            },
            "logging": {
                "level": "info",
                "file": "test.log"
            }
        }
        
        # 创建临时配置文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_config, self.temp_file, indent=4)
        self.temp_file.close()
        
        # 创建配置管理器实例
        self.config_manager = PythonConfigManager()
        self.config_manager.shutdown()  # 重置状态
    
    def tearDown(self):
        """测试后清理"""
        self.config_manager.shutdown()
        
        # 清理临时文件
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_initialize_and_load_config(self):
        """测试初始化和加载配置"""
        self.assertTrue(self.config_manager.initialize(self.temp_file.name))
        
        # 验证配置加载
        self.assertEqual(self.config_manager.get_value("server.host"), "localhost")
        self.assertEqual(self.config_manager.get_value("server.port"), 8080)
        self.assertEqual(self.config_manager.get_value("server.threads"), 4)
        self.assertEqual(self.config_manager.get_value("redis.host"), "127.0.0.1")
        self.assertEqual(self.config_manager.get_value("redis.port"), 6379)
    
    def test_set_and_get_values(self):
        """测试设置和获取配置值"""
        self.config_manager.initialize(self.temp_file.name)
        
        # 测试设置新值
        self.assertTrue(self.config_manager.set_value("new_key", "new_value"))
        self.assertEqual(self.config_manager.get_value("new_key"), "new_value")
        
        # 测试修改现有值
        self.assertTrue(self.config_manager.set_value("server.port", 9090))
        self.assertEqual(self.config_manager.get_value("server.port"), 9090)
        
        # 测试嵌套键设置
        self.assertTrue(self.config_manager.set_value("database.mysql.host", "mysql.example.com"))
        self.assertEqual(self.config_manager.get_value("database.mysql.host"), "mysql.example.com")
        
        # 测试默认值
        self.assertEqual(self.config_manager.get_value("non_existent_key", "default"), "default")
        self.assertEqual(self.config_manager.get_value("non_existent_int", 42), 42)
    
    def test_has_key_and_remove_key(self):
        """测试键存在性检查和删除"""
        self.config_manager.initialize(self.temp_file.name)
        
        # 测试键存在性
        self.assertTrue(self.config_manager.has_key("server.host"))
        self.assertTrue(self.config_manager.has_key("redis.port"))
        self.assertFalse(self.config_manager.has_key("non_existent_key"))
        
        # 测试删除键
        self.assertTrue(self.config_manager.remove_key("server.threads"))
        self.assertFalse(self.config_manager.has_key("server.threads"))
        self.assertFalse(self.config_manager.remove_key("non_existent_key"))
    
    def test_section_operations(self):
        """测试配置节操作"""
        self.config_manager.initialize(self.temp_file.name)
        
        # 获取配置节
        server_config = self.config_manager.get_section("server")
        self.assertIn("host", server_config)
        self.assertIn("port", server_config)
        
        # 设置新配置节
        new_section = {
            "username": "admin",
            "password": "secret",
            "timeout": 30
        }
        
        self.assertTrue(self.config_manager.set_section("database", new_section))
        
        database_config = self.config_manager.get_section("database")
        self.assertEqual(database_config["username"], "admin")
        self.assertEqual(database_config["password"], "secret")
        self.assertEqual(database_config["timeout"], 30)
        
        # 删除配置节
        self.assertTrue(self.config_manager.remove_section("database"))
        self.assertEqual(self.config_manager.get_section("database"), {})
        
        # 获取配置节名称
        section_names = self.config_manager.get_section_names()
        self.assertIn("server", section_names)
        self.assertIn("redis", section_names)
        self.assertIn("logging", section_names)
    
    def test_config_validation(self):
        """测试配置验证"""
        self.config_manager.initialize(self.temp_file.name)
        
        # 注册验证器
        valid_validator = MockConfigValidator(is_valid=True)
        invalid_validator = MockConfigValidator(
            is_valid=False, 
            errors=["Test error"], 
            warnings=["Test warning"]
        )
        
        self.assertTrue(self.config_manager.register_validator("server", valid_validator))
        self.assertTrue(self.config_manager.register_validator("redis", invalid_validator))
        
        # 验证配置
        result = self.config_manager.validate_config()
        self.assertFalse(result.is_valid)
        self.assertIn("[redis] Test error", result.errors)
        self.assertIn("[redis] Test warning", result.warnings)
        
        # 验证特定节
        server_result = self.config_manager.validate_section("server")
        self.assertTrue(server_result.is_valid)
        
        redis_result = self.config_manager.validate_section("redis")
        self.assertFalse(redis_result.is_valid)
        
        # 注销验证器
        self.config_manager.unregister_validator("server")
        self.config_manager.unregister_validator("redis")
    
    def test_config_change_listener(self):
        """测试配置变更监听"""
        self.config_manager.initialize(self.temp_file.name)
        
        mock_listener = MockConfigChangeListener()
        self.config_manager.register_change_listener(mock_listener)
        
        # 修改配置触发通知
        self.config_manager.set_value("server.port", 9090)
        
        # 验证监听器收到通知
        self.assertEqual(len(mock_listener.events), 1)
        event = mock_listener.events[0]
        self.assertEqual(event.type, ConfigChangeType.MODIFIED)
        self.assertEqual(event.key, "server.port")
        self.assertEqual(event.old_value, 8080)
        self.assertEqual(event.new_value, 9090)
        
        self.config_manager.unregister_change_listener(mock_listener)
    
    def test_version_management(self):
        """测试版本管理"""
        self.config_manager.initialize(self.temp_file.name)
        
        # 验证初始值
        initial_port = self.config_manager.get_value("server.port")
        self.assertEqual(initial_port, 8080)
        
        # 创建快照
        version1 = self.config_manager.create_snapshot("Initial version")
        self.assertIsNotNone(version1)
        self.assertTrue(len(version1) > 0)
        
        # 修改配置
        self.config_manager.set_value("server.port", 9090)
        self.config_manager.set_value("new_key", "new_value")
        
        # 验证修改后的值
        self.assertEqual(self.config_manager.get_value("server.port"), 9090)
        self.assertTrue(self.config_manager.has_key("new_key"))
        
        # 创建另一个快照
        version2 = self.config_manager.create_snapshot("Modified version")
        self.assertIsNotNone(version2)
        self.assertNotEqual(version1, version2)
        
        # 获取版本历史
        history = self.config_manager.get_version_history()
        self.assertEqual(len(history), 2)
        
        # 恢复到第一个版本
        self.assertTrue(self.config_manager.restore_from_snapshot(version1))
        self.assertEqual(self.config_manager.get_value("server.port"), 8080)
        self.assertFalse(self.config_manager.has_key("new_key"))
        
        # 删除快照
        self.assertTrue(self.config_manager.delete_snapshot(version2))
        history = self.config_manager.get_version_history()
        self.assertEqual(len(history), 1)
        
        # 设置最大版本历史
        self.config_manager.set_max_version_history(1)
        self.config_manager.create_snapshot("New version")
        history = self.config_manager.get_version_history()
        self.assertEqual(len(history), 1)
    
    def test_config_merging(self):
        """测试配置合并"""
        self.config_manager.initialize(self.temp_file.name)
        
        # 准备要合并的配置
        merge_config = {
            "server": {
                "port": 9090,
                "ssl": True
            },
            "database": {
                "host": "db.example.com",
                "port": 3306
            }
        }
        
        # 合并配置（覆盖模式）
        self.assertTrue(self.config_manager.merge_config(merge_config, True))
        
        self.assertEqual(self.config_manager.get_value("server.port"), 9090)
        self.assertEqual(self.config_manager.get_value("server.ssl"), True)
        self.assertEqual(self.config_manager.get_value("server.host"), "localhost")  # 保持原值
        self.assertEqual(self.config_manager.get_value("database.host"), "db.example.com")
        
        # 从文件合并
        merge_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(merge_config, merge_file, indent=4)
        merge_file.close()
        
        try:
            self.assertTrue(self.config_manager.merge_from_file(merge_file.name, False))
        finally:
            os.unlink(merge_file.name)
    
    def test_environment_variables(self):
        """测试环境变量支持"""
        # 设置环境变量
        os.environ['TEST_HOST'] = 'env.example.com'
        os.environ['MARKET_DATA_PORT'] = '8888'
        
        try:
            # 创建包含环境变量的配置
            env_config = {
                "server": {
                    "host": "${TEST_HOST}",
                    "port": "${PORT}",
                    "backup_port": "${MARKET_DATA_PORT}"
                }
            }
            
            env_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
            json.dump(env_config, env_config_file, indent=4)
            env_config_file.close()
            
            try:
                # 启用环境变量支持
                self.config_manager.enable_environment_variables(True)
                self.config_manager.set_environment_prefix("MARKET_DATA_")
                
                self.assertTrue(self.config_manager.initialize(env_config_file.name))
                
                # 验证环境变量解析
                self.assertEqual(self.config_manager.get_value("server.host"), "env.example.com")
                self.assertEqual(self.config_manager.get_value("server.backup_port"), "8888")
                
            finally:
                os.unlink(env_config_file.name)
        
        finally:
            # 清理环境变量
            del os.environ['TEST_HOST']
            del os.environ['MARKET_DATA_PORT']
    
    def test_config_export(self):
        """测试配置导出"""
        self.config_manager.initialize(self.temp_file.name)
        
        # 导出为字符串
        exported = self.config_manager.export_to_string(True)
        self.assertIsNotNone(exported)
        self.assertIn("server", exported)
        self.assertIn("redis", exported)
        
        # 导出到文件
        export_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        export_file.close()
        
        try:
            self.assertTrue(self.config_manager.export_to_file(export_file.name, True))
            self.assertTrue(os.path.exists(export_file.name))
            
            # 验证导出的文件可以重新加载
            test_manager = PythonConfigManager()
            self.assertTrue(test_manager.load_from_file(export_file.name))
            
        finally:
            os.unlink(export_file.name)
    
    def test_statistics(self):
        """测试统计信息"""
        self.config_manager.initialize(self.temp_file.name)
        
        stats = self.config_manager.get_statistics()
        self.assertGreater(stats['total_keys'], 0)
        self.assertGreater(stats['total_sections'], 0)
        
        # 修改配置增加变更计数
        old_change_count = stats['change_count']
        self.config_manager.set_value("test_key", "test_value")
        
        updated_stats = self.config_manager.get_statistics()
        self.assertGreater(updated_stats['change_count'], old_change_count)
    
    def test_hot_reload(self):
        """测试热更新功能"""
        self.config_manager.initialize(self.temp_file.name)
        
        mock_listener = MockConfigChangeListener()
        self.config_manager.register_change_listener(mock_listener)
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        self.config_manager.set_file_watch_interval(0.1)  # 100ms间隔
        
        self.assertTrue(self.config_manager.is_hot_reload_enabled())
        
        # 修改配置文件
        time.sleep(0.15)  # 等待初始检查
        
        modified_config = self.test_config.copy()
        modified_config['server']['host'] = 'modified.example.com'
        modified_config['server']['port'] = 9999
        
        with open(self.temp_file.name, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 等待文件监控检测到变更
        time.sleep(0.2)
        
        # 验证配置已更新
        self.assertEqual(self.config_manager.get_value("server.host"), "modified.example.com")
        self.assertEqual(self.config_manager.get_value("server.port"), 9999)
        
        # 验证监听器收到重新加载通知
        reload_events = [e for e in mock_listener.events if e.type == ConfigChangeType.RELOADED]
        self.assertGreater(len(reload_events), 0)
        
        # 禁用热更新
        self.config_manager.enable_hot_reload(False)
        self.assertFalse(self.config_manager.is_hot_reload_enabled())
        
        self.config_manager.unregister_change_listener(mock_listener)


class TestConfigValidators(unittest.TestCase):
    """配置验证器测试"""
    
    def test_pytdx_config_validator(self):
        """测试pytdx配置验证器"""
        validator = PytdxConfigValidator()
        
        # 有效配置
        valid_config = {
            "enabled": True,
            "servers": [
                {"host": "**************", "port": 7709},
                {"host": "************", "port": 7709}
            ],
            "batch_size": 1000,
            "concurrent_requests": 5,
            "timeout_seconds": 30,
            "symbols": ["all"],
            "data_types": ["tick", "kline"]
        }
        
        result = validator.validate(valid_config)
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.errors), 0)
        
        # 无效配置
        invalid_config = {
            "enabled": "true",  # 应该是布尔值
            "servers": [
                {"host": "invalid_ip", "port": 70000}  # 无效端口
            ],
            "batch_size": -1,  # 无效批次大小
            "concurrent_requests": 100,  # 超出范围
            "data_types": ["invalid_type"]  # 无效数据类型
        }
        
        result = validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
    
    def test_collection_config_validator(self):
        """测试采集配置验证器"""
        validator = CollectionConfigValidator()
        
        # 有效配置
        valid_config = {
            "pytdx": {
                "enabled": True,
                "servers": [{"host": "127.0.0.1", "port": 7709}],
                "batch_size": 1000
            },
            "ctp": {
                "enabled": True,
                "config_path": "config/ctp_config.json",
                "failover_timeout": 30
            },
            "coordination": {
                "priority_source": "ctp",
                "overlap_tolerance_seconds": 300,
                "enable_data_merge": True
            }
        }
        
        result = validator.validate(valid_config)
        self.assertTrue(result.is_valid)
        
        # 无效配置
        invalid_config = {
            "pytdx": {
                "enabled": True,
                "servers": [{"host": "127.0.0.1", "port": 70000}],  # 无效端口
                "batch_size": -1  # 无效批次大小
            },
            "coordination": {
                "priority_source": "invalid_source",  # 无效优先级源
                "overlap_tolerance_seconds": -1  # 无效容忍度
            }
        }
        
        result = validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
    
    def test_storage_config_validator(self):
        """测试存储配置验证器"""
        validator = StorageConfigValidator()
        
        # 有效配置
        valid_config = {
            "hot_storage": {
                "type": "redis",
                "retention_days": 7,
                "config": {
                    "host": "127.0.0.1",
                    "port": 6379,
                    "database": 0,
                    "pool_size": 10
                }
            },
            "warm_storage": {
                "type": "clickhouse",
                "retention_days": 730,
                "config": {
                    "host": "127.0.0.1",
                    "port": 9000,
                    "database": "market_data",
                    "username": "admin",
                    "max_connections": 20
                }
            },
            "migration": {
                "enabled": True,
                "batch_size": 10000,
                "parallel_workers": 4
            }
        }
        
        result = validator.validate(valid_config)
        self.assertTrue(result.is_valid)
        
        # 无效配置
        invalid_config = {
            "hot_storage": {
                "type": "invalid_type",  # 无效存储类型
                "retention_days": -1,  # 无效保留天数
                "config": {
                    "host": "127.0.0.1",
                    "port": 70000,  # 无效端口
                    "database": 20  # Redis数据库编号超出范围
                }
            },
            "migration": {
                "batch_size": 50,  # 批次大小太小
                "parallel_workers": 100  # 并行工作者数量太多
            }
        }
        
        result = validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
    
    def test_scheduling_config_validator(self):
        """测试调度配置验证器"""
        validator = SchedulingConfigValidator()
        
        # 有效配置
        valid_config = {
            "historical_update": {
                "enabled": True,
                "cron": "0 2 * * *",
                "lookback_days": 1,
                "max_parallel_tasks": 5,
                "timeout_minutes": 60
            },
            "data_migration": {
                "enabled": True,
                "cron": "0 3 * * *",
                "batch_size": 10000,
                "max_parallel_workers": 4
            },
            "health_check": {
                "enabled": True,
                "cron": "*/5 * * * *",
                "timeout_seconds": 30,
                "alert_on_failure": True
            }
        }
        
        result = validator.validate(valid_config)
        self.assertTrue(result.is_valid)
        
        # 无效配置
        invalid_config = {
            "historical_update": {
                "enabled": "true",  # 应该是布尔值
                "cron": "invalid cron",  # 无效cron表达式
                "lookback_days": 400,  # 超出范围
                "timeout_minutes": 2000  # 超出范围
            },
            "data_migration": {
                "batch_size": 50,  # 批次大小太小
                "max_parallel_workers": 20  # 并行工作者太多
            }
        }
        
        result = validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
    
    def test_monitoring_config_validator(self):
        """测试监控配置验证器"""
        validator = MonitoringConfigValidator()
        
        # 有效配置
        valid_config = {
            "enable_metrics": True,
            "prometheus": {
                "bind_address": "0.0.0.0:9090",
                "metrics_path": "/metrics"
            },
            "alert_thresholds": {
                "data_delay_seconds": 60,
                "error_rate_percent": 5.0,
                "cpu_threshold_percent": 85.0
            },
            "alerting": {
                "enabled": True,
                "channels": {
                    "console": {
                        "enabled": True
                    },
                    "email": {
                        "enabled": True,
                        "smtp_server": "smtp.example.com",
                        "port": 587,
                        "username": "<EMAIL>",
                        "password": "password",
                        "recipients": ["<EMAIL>"]
                    }
                },
                "rate_limiting": {
                    "max_alerts_per_minute": 10,
                    "cooldown_minutes": 5
                }
            }
        }
        
        result = validator.validate(valid_config)
        self.assertTrue(result.is_valid)
        
        # 无效配置
        invalid_config = {
            "enable_metrics": "true",  # 应该是布尔值
            "prometheus": {
                "bind_address": "invalid_address",  # 无效地址格式
                "metrics_path": "invalid_path"  # 路径应该以/开头
            },
            "alert_thresholds": {
                "error_rate_percent": 150.0,  # 超出范围
                "cpu_threshold_percent": -10.0  # 负值
            },
            "alerting": {
                "channels": {
                    "email": {
                        "enabled": True,
                        "port": 70000,  # 无效端口
                        "recipients": ["invalid_email"]  # 无效邮箱格式
                    }
                },
                "rate_limiting": {
                    "max_alerts_per_minute": 2000,  # 超出范围
                    "cooldown_minutes": 2000  # 超出范围
                }
            }
        }
        
        result = validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)


if __name__ == '__main__':
    unittest.main()