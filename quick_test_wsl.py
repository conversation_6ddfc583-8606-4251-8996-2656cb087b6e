#!/usr/bin/env python3
"""
WSL环境快速测试脚本
用于验证金融数据服务在WSL环境下的基本功能
"""

import asyncio
import sys
import os
import subprocess
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

def print_header():
    print("=" * 60)
    print("    金融数据服务 - WSL环境快速测试")
    print("=" * 60)
    print()

def print_info(msg):
    print(f"[INFO] {msg}")

def print_success(msg):
    print(f"[SUCCESS] ✅ {msg}")

def print_error(msg):
    print(f"[ERROR] ❌ {msg}")

def print_warning(msg):
    print(f"[WARNING] ⚠️  {msg}")

def check_system_requirements():
    """检查系统要求"""
    print_info("检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 8:
        print_success(f"Python版本: {python_version.major}.{python_version.minor}")
    else:
        print_error(f"Python版本过低: {python_version.major}.{python_version.minor}, 需要3.8+")
        return False
    
    # 检查必要的命令
    commands = ['redis-cli', 'docker', 'pip3']
    for cmd in commands:
        try:
            subprocess.run([cmd, '--version'], capture_output=True, check=True)
            print_success(f"{cmd} 可用")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print_warning(f"{cmd} 不可用或未安装")
    
    return True

def test_redis_connection():
    """测试Redis连接"""
    print_info("测试Redis连接...")
    
    try:
        result = subprocess.run(['redis-cli', 'ping'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0 and 'PONG' in result.stdout:
            print_success("Redis连接正常")
            return True
        else:
            print_error("Redis连接失败")
            return False
    except Exception as e:
        print_error(f"Redis测试异常: {e}")
        return False

def test_python_imports():
    """测试Python模块导入"""
    print_info("测试Python模块导入...")
    
    modules = [
        ('asyncio', '异步IO'),
        ('json', 'JSON处理'),
        ('datetime', '日期时间'),
        ('logging', '日志记录')
    ]
    
    optional_modules = [
        ('redis', 'Redis客户端'),
        ('pandas', '数据处理'),
        ('pytdx', 'TDX数据接口'),
        ('fastapi', 'Web框架'),
        ('croniter', '定时任务')
    ]
    
    # 测试必需模块
    for module, desc in modules:
        try:
            __import__(module)
            print_success(f"{desc} ({module})")
        except ImportError:
            print_error(f"{desc} ({module}) - 导入失败")
            return False
    
    # 测试可选模块
    for module, desc in optional_modules:
        try:
            __import__(module)
            print_success(f"{desc} ({module})")
        except ImportError:
            print_warning(f"{desc} ({module}) - 未安装")
    
    return True

async def test_basic_async_functionality():
    """测试基本异步功能"""
    print_info("测试异步功能...")
    
    try:
        # 简单的异步任务
        async def simple_task():
            await asyncio.sleep(0.1)
            return "异步任务完成"
        
        result = await simple_task()
        print_success(f"异步测试: {result}")
        
        # 并发任务测试
        async def concurrent_task(n):
            await asyncio.sleep(0.1)
            return f"任务{n}完成"
        
        tasks = [concurrent_task(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        print_success(f"并发测试: {len(results)}个任务完成")
        
        return True
        
    except Exception as e:
        print_error(f"异步功能测试失败: {e}")
        return False

def test_file_system():
    """测试文件系统操作"""
    print_info("测试文件系统...")
    
    try:
        # 创建测试目录
        test_dirs = ['logs', 'config', 'data']
        for dir_name in test_dirs:
            os.makedirs(dir_name, exist_ok=True)
            if os.path.exists(dir_name):
                print_success(f"目录创建: {dir_name}")
            else:
                print_error(f"目录创建失败: {dir_name}")
                return False
        
        # 测试文件写入
        test_file = 'logs/test.log'
        with open(test_file, 'w') as f:
            f.write(f"测试时间: {datetime.now()}\n")
        
        if os.path.exists(test_file):
            print_success("文件写入测试通过")
            os.remove(test_file)  # 清理测试文件
        else:
            print_error("文件写入测试失败")
            return False
        
        return True
        
    except Exception as e:
        print_error(f"文件系统测试失败: {e}")
        return False

def test_docker_services():
    """测试Docker服务"""
    print_info("测试Docker服务...")
    
    try:
        # 检查Docker是否运行
        result = subprocess.run(['docker', 'info'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success("Docker服务运行正常")
        else:
            print_warning("Docker服务未运行或无权限")
            return False
        
        # 检查是否有运行的容器
        result = subprocess.run(['docker', 'ps'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            container_count = len(lines) - 1  # 减去标题行
            print_success(f"Docker容器: {container_count}个运行中")
        
        return True
        
    except Exception as e:
        print_error(f"Docker测试失败: {e}")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print_info("测试网络连接...")
    
    # 测试本地端口
    ports_to_test = [
        (6379, 'Redis'),
        (8123, 'ClickHouse HTTP'),
        (9000, 'ClickHouse Native')
    ]
    
    import socket
    
    for port, service in ports_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print_success(f"{service} (端口{port}): 可连接")
            else:
                print_warning(f"{service} (端口{port}): 不可连接")
        except Exception as e:
            print_warning(f"{service} (端口{port}): 测试异常 - {e}")
    
    return True

async def run_comprehensive_test():
    """运行综合测试"""
    print_header()
    
    test_results = []
    
    # 系统要求检查
    test_results.append(("系统要求", check_system_requirements()))
    
    # Python模块测试
    test_results.append(("Python模块", test_python_imports()))
    
    # 文件系统测试
    test_results.append(("文件系统", test_file_system()))
    
    # 异步功能测试
    test_results.append(("异步功能", await test_basic_async_functionality()))
    
    # Redis连接测试
    test_results.append(("Redis连接", test_redis_connection()))
    
    # Docker服务测试
    test_results.append(("Docker服务", test_docker_services()))
    
    # 网络连接测试
    test_results.append(("网络连接", test_network_connectivity()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print_success("🎉 所有测试通过！WSL环境准备就绪")
        print("\n下一步:")
        print("1. 运行 ./deploy_wsl_test.sh 进行完整部署")
        print("2. 或运行 python3 test_enhanced_features.py 测试数据采集功能")
    else:
        print_warning("⚠️  部分测试失败，请检查环境配置")
        print("\n建议:")
        print("1. 安装缺失的依赖: pip3 install -r requirements.txt")
        print("2. 启动Redis服务: sudo service redis-server start")
        print("3. 启动Docker服务: sudo service docker start")
    
    return passed == total

def main():
    """主函数"""
    try:
        success = asyncio.run(run_comprehensive_test())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print_error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()