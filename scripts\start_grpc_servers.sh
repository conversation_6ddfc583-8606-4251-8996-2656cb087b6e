#!/bin/bash

# 启动多个gRPC服务器实例以支持负载均衡和故障转移

set -e

# 配置参数
BASE_PORT=50051
NUM_SERVERS=3
SERVER_BINARY="./build/financial_data_server"
LOG_DIR="./logs"
PID_DIR="./pids"

# 创建必要的目录
mkdir -p "$LOG_DIR"
mkdir -p "$PID_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务器二进制文件是否存在
check_binary() {
    if [ ! -f "$SERVER_BINARY" ]; then
        log_error "Server binary not found: $SERVER_BINARY"
        log_info "Please build the server first: cmake --build build"
        exit 1
    fi
}

# 启动单个服务器实例
start_server() {
    local port=$1
    local server_id=$2
    local log_file="$LOG_DIR/grpc_server_$server_id.log"
    local pid_file="$PID_DIR/grpc_server_$server_id.pid"
    
    log_info "Starting gRPC server $server_id on port $port..."
    
    # 启动服务器并记录PID
    nohup "$SERVER_BINARY" \
        --port="$port" \
        --server-id="grpc-server-$server_id" \
        --log-level=info \
        --max-connections=1000 \
        --keepalive-time=30 \
        --keepalive-timeout=5 \
        > "$log_file" 2>&1 &
    
    local pid=$!
    echo $pid > "$pid_file"
    
    # 等待服务器启动
    sleep 2
    
    # 检查服务器是否成功启动
    if kill -0 $pid 2>/dev/null; then
        log_info "Server $server_id started successfully (PID: $pid, Port: $port)"
    else
        log_error "Failed to start server $server_id"
        return 1
    fi
}

# 停止所有服务器
stop_servers() {
    log_info "Stopping all gRPC servers..."
    
    for i in $(seq 1 $NUM_SERVERS); do
        local pid_file="$PID_DIR/grpc_server_$i.pid"
        
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 $pid 2>/dev/null; then
                log_info "Stopping server $i (PID: $pid)..."
                kill $pid
                
                # 等待进程结束
                local count=0
                while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                    sleep 1
                    count=$((count + 1))
                done
                
                # 如果进程仍然存在，强制杀死
                if kill -0 $pid 2>/dev/null; then
                    log_warn "Force killing server $i..."
                    kill -9 $pid
                fi
            fi
            rm -f "$pid_file"
        fi
    done
    
    log_info "All servers stopped"
}

# 检查服务器状态
check_status() {
    log_info "Checking server status..."
    
    local running_count=0
    
    for i in $(seq 1 $NUM_SERVERS); do
        local port=$((BASE_PORT + i - 1))
        local pid_file="$PID_DIR/grpc_server_$i.pid"
        
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 $pid 2>/dev/null; then
                log_info "Server $i: RUNNING (PID: $pid, Port: $port)"
                running_count=$((running_count + 1))
            else
                log_warn "Server $i: STOPPED (Port: $port)"
                rm -f "$pid_file"
            fi
        else
            log_warn "Server $i: NOT STARTED (Port: $port)"
        fi
    done
    
    log_info "Running servers: $running_count/$NUM_SERVERS"
}

# 重启所有服务器
restart_servers() {
    log_info "Restarting all gRPC servers..."
    stop_servers
    sleep 2
    start_all_servers
}

# 启动所有服务器
start_all_servers() {
    check_binary
    
    log_info "Starting $NUM_SERVERS gRPC server instances..."
    
    for i in $(seq 1 $NUM_SERVERS); do
        local port=$((BASE_PORT + i - 1))
        start_server $port $i
    done
    
    log_info "All servers started successfully"
    log_info "Server endpoints:"
    for i in $(seq 1 $NUM_SERVERS); do
        local port=$((BASE_PORT + i - 1))
        echo "  - Server $i: localhost:$port"
    done
}

# 显示日志
show_logs() {
    local server_id=${1:-1}
    local log_file="$LOG_DIR/grpc_server_$server_id.log"
    
    if [ -f "$log_file" ]; then
        log_info "Showing logs for server $server_id:"
        tail -f "$log_file"
    else
        log_error "Log file not found: $log_file"
    fi
}

# 健康检查
health_check() {
    log_info "Performing health check on all servers..."
    
    for i in $(seq 1 $NUM_SERVERS); do
        local port=$((BASE_PORT + i - 1))
        log_info "Checking server $i (localhost:$port)..."
        
        # 使用grpcurl进行健康检查（如果可用）
        if command -v grpcurl >/dev/null 2>&1; then
            if grpcurl -plaintext localhost:$port financial_data.MarketDataService/HealthCheck >/dev/null 2>&1; then
                log_info "Server $i: HEALTHY"
            else
                log_warn "Server $i: UNHEALTHY"
            fi
        else
            # 简单的端口检查
            if nc -z localhost $port 2>/dev/null; then
                log_info "Server $i: PORT OPEN"
            else
                log_warn "Server $i: PORT CLOSED"
            fi
        fi
    done
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 {start|stop|restart|status|logs|health|help}"
    echo ""
    echo "Commands:"
    echo "  start    - Start all gRPC server instances"
    echo "  stop     - Stop all gRPC server instances"
    echo "  restart  - Restart all gRPC server instances"
    echo "  status   - Check status of all servers"
    echo "  logs     - Show logs for a server (default: server 1)"
    echo "  health   - Perform health check on all servers"
    echo "  help     - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                 # Start all servers"
    echo "  $0 logs 2               # Show logs for server 2"
    echo "  $0 status               # Check all server status"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_all_servers
            ;;
        stop)
            stop_servers
            ;;
        restart)
            restart_servers
            ;;
        status)
            check_status
            ;;
        logs)
            show_logs "${2:-1}"
            ;;
        health)
            health_check
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log_info "Received interrupt signal, stopping servers..."; stop_servers; exit 0' INT TERM

# 执行主函数
main "$@"