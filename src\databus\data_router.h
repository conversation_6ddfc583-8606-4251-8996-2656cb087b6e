#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <shared_mutex>
#include <functional>
#include <queue>
#include <condition_variable>
#include "data_types.h"
#include "lock_free_queue.h"

namespace financial_data {
namespace databus {

/**
 * @brief 客户端信息结构
 */
struct ClientInfo {
    std::string client_id;
    std::string client_type;        // "websocket", "tcp", "grpc", etc.
    std::unordered_set<std::string> subscribed_symbols;
    std::unordered_set<std::string> subscribed_exchanges;
    std::function<bool(const MarketDataWrapper&)> data_callback;
    std::atomic<uint64_t> messages_sent{0};
    std::atomic<uint64_t> messages_dropped{0};
    std::atomic<bool> active{true};
    int64_t last_heartbeat_ns{0};
    
    // 客户端特定的队列
    std::unique_ptr<WrapperQueue> client_queue;
    
    ClientInfo(const std::string& id, const std::string& type)
        : client_id(id), client_type(type) {
        client_queue = std::make_unique<WrapperQueue>();
        last_heartbeat_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    bool IsSubscribed(const std::string& symbol, const std::string& exchange) const {
        return (subscribed_symbols.empty() || subscribed_symbols.count(symbol)) &&
               (subscribed_exchanges.empty() || subscribed_exchanges.count(exchange));
    }
    
    void UpdateHeartbeat() {
        last_heartbeat_ns = StandardTick::GetCurrentTimestampNs();
    }
    
    bool IsAlive(int64_t timeout_ns) const {
        return (StandardTick::GetCurrentTimestampNs() - last_heartbeat_ns) < timeout_ns;
    }
};

/**
 * @brief 路由规则结构
 */
struct RoutingRule {
    std::string rule_id;
    std::string symbol_pattern;     // 支持通配符，如 "CU*", "AL2409"
    std::string exchange_pattern;   // 支持通配符，如 "SHFE", "*"
    std::string client_pattern;     // 支持通配符，如 "websocket_*", "*"
    int priority = 0;               // 优先级，数值越大优先级越高
    bool enabled = true;
    
    // 过滤条件
    double min_price = 0.0;
    double max_price = std::numeric_limits<double>::max();
    uint64_t min_volume = 0;
    uint64_t max_volume = std::numeric_limits<uint64_t>::max();
    
    bool Matches(const std::string& symbol, const std::string& exchange, 
                const std::string& client_id, const StandardTick& tick) const;
};

/**
 * @brief 数据路由统计信息
 */
struct RoutingStatistics {
    std::atomic<uint64_t> total_messages_received{0};
    std::atomic<uint64_t> total_messages_routed{0};
    std::atomic<uint64_t> total_messages_dropped{0};
    std::atomic<uint64_t> total_clients_served{0};
    std::atomic<uint64_t> routing_errors{0};
    
    // 性能指标
    std::atomic<uint64_t> avg_routing_latency_ns{0};
    std::atomic<uint64_t> max_routing_latency_ns{0};
    std::atomic<uint64_t> min_routing_latency_ns{std::numeric_limits<uint64_t>::max()};
    
    void UpdateLatency(uint64_t latency_ns) {
        avg_routing_latency_ns = (avg_routing_latency_ns.load() + latency_ns) / 2;
        
        uint64_t current_max = max_routing_latency_ns.load();
        while (latency_ns > current_max && 
               !max_routing_latency_ns.compare_exchange_weak(current_max, latency_ns)) {
            // CAS循环
        }
        
        uint64_t current_min = min_routing_latency_ns.load();
        while (latency_ns < current_min && 
               !min_routing_latency_ns.compare_exchange_weak(current_min, latency_ns)) {
            // CAS循环
        }
    }
    
    void Reset() {
        total_messages_received = 0;
        total_messages_routed = 0;
        total_messages_dropped = 0;
        total_clients_served = 0;
        routing_errors = 0;
        avg_routing_latency_ns = 0;
        max_routing_latency_ns = 0;
        min_routing_latency_ns = std::numeric_limits<uint64_t>::max();
    }
};

/**
 * @brief 智能数据路由器
 * 
 * 支持按合约、按客户端的智能数据分发，包含订阅管理、路由规则、
 * 负载均衡和故障转移功能
 */
class DataRouter {
private:
    // 客户端管理
    std::unordered_map<std::string, std::shared_ptr<ClientInfo>> clients_;
    mutable std::shared_mutex clients_mutex_;
    
    // 订阅索引 - 按合约快速查找订阅客户端
    std::unordered_map<std::string, std::unordered_set<std::string>> symbol_subscribers_;
    std::unordered_map<std::string, std::unordered_set<std::string>> exchange_subscribers_;
    mutable std::shared_mutex subscription_mutex_;
    
    // 路由规则
    std::vector<RoutingRule> routing_rules_;
    mutable std::shared_mutex rules_mutex_;
    
    // 输入队列
    std::unique_ptr<WrapperQueue> input_queue_;
    
    // 工作线程
    std::vector<std::thread> worker_threads_;
    std::atomic<bool> running_{false};
    size_t worker_count_;
    
    // 统计信息
    RoutingStatistics statistics_;
    
    // 心跳检查
    std::thread heartbeat_thread_;
    int64_t heartbeat_timeout_ns_;
    
    // 负载均衡
    std::atomic<size_t> round_robin_counter_{0};

public:
    explicit DataRouter(size_t worker_count = 4, 
                       int64_t heartbeat_timeout_ms = 30000);
    ~DataRouter();

    /**
     * @brief 启动数据路由器
     */
    bool Start();

    /**
     * @brief 停止数据路由器
     */
    void Stop();

    /**
     * @brief 注册客户端
     */
    bool RegisterClient(const std::string& client_id, 
                       const std::string& client_type,
                       std::function<bool(const MarketDataWrapper&)> callback);

    /**
     * @brief 注销客户端
     */
    bool UnregisterClient(const std::string& client_id);

    /**
     * @brief 客户端订阅合约
     */
    bool Subscribe(const std::string& client_id, 
                  const std::vector<std::string>& symbols,
                  const std::vector<std::string>& exchanges = {});

    /**
     * @brief 客户端取消订阅
     */
    bool Unsubscribe(const std::string& client_id, 
                    const std::vector<std::string>& symbols,
                    const std::vector<std::string>& exchanges = {});

    /**
     * @brief 获取客户端订阅信息
     */
    std::vector<std::string> GetSubscriptions(const std::string& client_id) const;

    /**
     * @brief 推送数据到路由器
     */
    bool PushData(const MarketDataWrapper& data);

    /**
     * @brief 批量推送数据
     */
    bool PushBatch(const std::vector<MarketDataWrapper>& data_batch);

    /**
     * @brief 添加路由规则
     */
    bool AddRoutingRule(const RoutingRule& rule);

    /**
     * @brief 删除路由规则
     */
    bool RemoveRoutingRule(const std::string& rule_id);

    /**
     * @brief 更新路由规则
     */
    bool UpdateRoutingRule(const RoutingRule& rule);

    /**
     * @brief 获取所有路由规则
     */
    std::vector<RoutingRule> GetRoutingRules() const;

    /**
     * @brief 客户端心跳
     */
    void ClientHeartbeat(const std::string& client_id);

    /**
     * @brief 获取客户端信息
     */
    std::shared_ptr<ClientInfo> GetClientInfo(const std::string& client_id) const;

    /**
     * @brief 获取所有活跃客户端
     */
    std::vector<std::string> GetActiveClients() const;

    /**
     * @brief 获取路由统计信息
     */
    RoutingStatistics GetStatistics() const;

    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

    /**
     * @brief 获取队列使用情况
     */
    struct QueueStatus {
        size_t input_queue_size;
        size_t total_client_queues_size;
        std::unordered_map<std::string, size_t> client_queue_sizes;
    };
    
    QueueStatus GetQueueStatus() const;

private:
    /**
     * @brief 工作线程主循环
     */
    void WorkerLoop(size_t worker_id);

    /**
     * @brief 路由单条数据
     */
    void RouteData(const MarketDataWrapper& data);

    /**
     * @brief 查找匹配的客户端
     */
    std::vector<std::string> FindMatchingClients(const MarketDataWrapper& data) const;

    /**
     * @brief 应用路由规则
     */
    bool ApplyRoutingRules(const std::string& client_id, const MarketDataWrapper& data) const;

    /**
     * @brief 发送数据到客户端
     */
    bool SendToClient(const std::string& client_id, const MarketDataWrapper& data);

    /**
     * @brief 心跳检查循环
     */
    void HeartbeatLoop();

    /**
     * @brief 清理非活跃客户端
     */
    void CleanupInactiveClients();

    /**
     * @brief 负载均衡选择工作线程
     */
    size_t SelectWorkerThread() const;

    /**
     * @brief 通配符匹配
     */
    static bool WildcardMatch(const std::string& pattern, const std::string& text);
};

/**
 * @brief 数据路由器工厂类
 */
class DataRouterFactory {
public:
    /**
     * @brief 创建默认配置的数据路由器
     */
    static std::unique_ptr<DataRouter> CreateDefault();

    /**
     * @brief 创建高性能配置的数据路由器
     */
    static std::unique_ptr<DataRouter> CreateHighPerformance();

    /**
     * @brief 创建低延迟配置的数据路由器
     */
    static std::unique_ptr<DataRouter> CreateLowLatency();

    /**
     * @brief 从配置文件创建数据路由器
     */
    static std::unique_ptr<DataRouter> CreateFromConfig(const std::string& config_file);
};

} // namespace databus
} // namespace financial_data