/**
 * @file data_integrity_test.cpp
 * @brief Data integrity testing implementation
 */

#include "data_integrity_test.h"
#include "test_utils.h"
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <future>
#include <iostream>
#include <algorithm>
#include <set>
#include <unordered_map>

namespace performance_tests {

DataIntegrityTest::DataIntegrityTest() : test_utils_(std::make_unique<TestUtils>()) {}

DataIntegrityTest::~DataIntegrityTest() = default;

DataIntegrityResult DataIntegrityTest::TestZeroDataLoss() {
    std::cout << "    Testing zero data loss..." << std::endl;
    
    const uint32_t test_messages = 100000;
    const uint32_t num_producers = 4;
    const uint32_t num_consumers = 2;
    
    // Setup test environment
    auto data_bus = test_utils_->CreateMockDataBus();
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    std::atomic<uint64_t> messages_sent{0};
    std::atomic<uint64_t> messages_received{0};
    std::atomic<bool> production_complete{false};
    
    // Track sent messages
    std::vector<std::set<uint64_t>> sent_sequences(num_producers);
    std::mutex sent_mutex;
    
    // Track received messages
    std::set<uint64_t> received_sequences;
    std::mutex received_mutex;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Producer threads
    std::vector<std::future<void>> producer_futures;
    for (uint32_t i = 0; i < num_producers; ++i) {
        producer_futures.push_back(std::async(std::launch::async, [&, i]() {
            uint32_t messages_per_producer = test_messages / num_producers;
            uint64_t base_sequence = i * 1000000; // Ensure unique sequences per producer
            
            for (uint32_t j = 0; j < messages_per_producer; ++j) {
                auto tick = tick_generator->GenerateTestTick();
                tick.sequence = base_sequence + j;
                
                if (data_bus->Publish(tick)) {
                    {
                        std::lock_guard<std::mutex> lock(sent_mutex);
                        sent_sequences[i].insert(tick.sequence);
                    }
                    messages_sent.fetch_add(1);
                }
                
                // Small delay to simulate realistic data rates
                if (j % 1000 == 0) {
                    std::this_thread::sleep_for(std::chrono::microseconds(100));
                }
            }
        }));
    }
    
    // Consumer threads
    std::vector<std::future<void>> consumer_futures;
    for (uint32_t i = 0; i < num_consumers; ++i) {
        consumer_futures.push_back(std::async(std::launch::async, [&]() {
            auto consumer = data_bus->CreateConsumer();
            
            while (!production_complete.load() || consumer->HasPendingMessages()) {
                auto tick = consumer->ConsumeMessage(std::chrono::milliseconds(100));
                
                if (tick.has_value()) {
                    {
                        std::lock_guard<std::mutex> lock(received_mutex);
                        received_sequences.insert(tick->sequence);
                    }
                    messages_received.fetch_add(1);
                }
            }
        }));
    }
    
    // Wait for producers to complete
    for (auto& future : producer_futures) {
        future.wait();
    }
    production_complete.store(true);
    
    // Wait for consumers to finish processing
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Stop consumers
    for (auto& future : consumer_futures) {
        future.wait();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto test_duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time).count();
    
    // Analyze results
    std::set<uint64_t> all_sent_sequences;
    {
        std::lock_guard<std::mutex> lock(sent_mutex);
        for (const auto& producer_sequences : sent_sequences) {
            all_sent_sequences.insert(producer_sequences.begin(), producer_sequences.end());
        }
    }
    
    uint64_t messages_lost = 0;
    {
        std::lock_guard<std::mutex> lock(received_mutex);
        for (uint64_t seq : all_sent_sequences) {
            if (received_sequences.find(seq) == received_sequences.end()) {
                messages_lost++;
            }
        }
    }
    
    DataIntegrityResult result;
    result.messages_sent = messages_sent.load();
    result.messages_received = messages_received.load();
    result.messages_lost = messages_lost;
    result.consistency_rate = messages_lost == 0 ? 1.0 : 
                             static_cast<double>(messages_received.load()) / messages_sent.load();
    
    std::cout << "      Messages sent: " << result.messages_sent << std::endl;
    std::cout << "      Messages received: " << result.messages_received << std::endl;
    std::cout << "      Messages lost: " << result.messages_lost << std::endl;
    std::cout << "      Test duration: " << test_duration << "s" << std::endl;
    
    return result;
}

DataIntegrityResult DataIntegrityTest::TestSequenceNumberContinuity() {
    std::cout << "    Testing sequence number continuity..." << std::endl;
    
    const uint32_t test_duration_seconds = 30;
    const std::string test_symbol = "CU2409";
    
    // Setup test environment
    auto data_bus = test_utils_->CreateMockDataBus();
    auto tick_generator = test_utils_->CreateTickGenerator();
    auto storage = test_utils_->CreateMockStorage();
    
    std::atomic<uint64_t> current_sequence{1};
    std::atomic<bool> stop_test{false};
    
    // Producer thread with sequential sequence numbers
    auto producer_future = std::async(std::launch::async, [&]() {
        while (!stop_test.load()) {
            auto tick = tick_generator->GenerateTestTick();
            tick.symbol = test_symbol;
            tick.sequence = current_sequence.fetch_add(1);
            
            data_bus->Publish(tick);
            storage->StoreTick(tick);
            
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    });
    
    // Run test
    std::this_thread::sleep_for(std::chrono::seconds(test_duration_seconds));
    stop_test.store(true);
    producer_future.wait();
    
    // Wait for storage to flush
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Retrieve stored data and check sequence continuity
    auto stored_ticks = storage->GetAllTicksForSymbol(test_symbol);
    
    // Sort by sequence number
    std::sort(stored_ticks.begin(), stored_ticks.end(), 
              [](const auto& a, const auto& b) { return a.sequence < b.sequence; });
    
    uint32_t gaps_found = 0;
    uint64_t expected_sequence = 1;
    
    for (const auto& tick : stored_ticks) {
        if (tick.sequence != expected_sequence) {
            gaps_found++;
            std::cout << "      Gap found: expected " << expected_sequence 
                      << ", got " << tick.sequence << std::endl;
        }
        expected_sequence = tick.sequence + 1;
    }
    
    DataIntegrityResult result;
    result.messages_sent = current_sequence.load() - 1;
    result.messages_received = stored_ticks.size();
    result.gaps_found = gaps_found;
    result.consistency_rate = gaps_found == 0 ? 1.0 : 
                             static_cast<double>(stored_ticks.size()) / (current_sequence.load() - 1);
    
    std::cout << "      Total messages: " << result.messages_sent << std::endl;
    std::cout << "      Stored messages: " << result.messages_received << std::endl;
    std::cout << "      Sequence gaps: " << gaps_found << std::endl;
    
    return result;
}

DataIntegrityResult DataIntegrityTest::TestDataConsistencyAcrossLayers() {
    std::cout << "    Testing data consistency across storage layers..." << std::endl;
    
    const uint32_t test_messages = 10000;
    const std::vector<std::string> test_symbols = {"CU2409", "AL2409", "ZN2409"};
    
    // Setup storage layers
    auto redis_client = test_utils_->CreateRedisTestClient();
    auto clickhouse_client = test_utils_->CreateClickHouseTestClient();
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    // Generate and store test data
    std::vector<TestTick> test_data;
    test_data.reserve(test_messages);
    
    for (uint32_t i = 0; i < test_messages; ++i) {
        auto tick = tick_generator->GenerateTestTick();
        tick.symbol = test_symbols[i % test_symbols.size()];
        tick.sequence = i + 1;
        tick.timestamp = std::chrono::system_clock::now().time_since_epoch().count() + i * 1000000; // 1ms intervals
        
        test_data.push_back(tick);
        
        // Store in both layers
        redis_client->StoreTick(tick);
        clickhouse_client->StoreTick(tick);
    }
    
    // Wait for storage operations to complete
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Verify consistency between layers
    uint64_t consistent_records = 0;
    uint64_t inconsistent_records = 0;
    uint64_t missing_from_redis = 0;
    uint64_t missing_from_clickhouse = 0;
    
    for (const auto& original_tick : test_data) {
        // Retrieve from Redis
        auto redis_tick = redis_client->GetTickBySequence(original_tick.symbol, original_tick.sequence);
        
        // Retrieve from ClickHouse
        auto clickhouse_tick = clickhouse_client->GetTickBySequence(original_tick.symbol, original_tick.sequence);
        
        bool redis_exists = redis_tick.has_value();
        bool clickhouse_exists = clickhouse_tick.has_value();
        
        if (!redis_exists) {
            missing_from_redis++;
        }
        if (!clickhouse_exists) {
            missing_from_clickhouse++;
        }
        
        if (redis_exists && clickhouse_exists) {
            // Compare data consistency
            bool consistent = (redis_tick->symbol == clickhouse_tick->symbol &&
                             redis_tick->sequence == clickhouse_tick->sequence &&
                             std::abs(redis_tick->last_price - clickhouse_tick->last_price) < 0.001 &&
                             redis_tick->volume == clickhouse_tick->volume);
            
            if (consistent) {
                consistent_records++;
            } else {
                inconsistent_records++;
                std::cout << "      Inconsistency found for sequence " << original_tick.sequence 
                          << " of " << original_tick.symbol << std::endl;
            }
        }
    }
    
    DataIntegrityResult result;
    result.messages_sent = test_messages;
    result.messages_received = consistent_records + inconsistent_records;
    result.consistency_rate = static_cast<double>(consistent_records) / test_messages;
    result.corrupted_messages = inconsistent_records;
    
    std::cout << "      Total test messages: " << test_messages << std::endl;
    std::cout << "      Consistent records: " << consistent_records << std::endl;
    std::cout << "      Inconsistent records: " << inconsistent_records << std::endl;
    std::cout << "      Missing from Redis: " << missing_from_redis << std::endl;
    std::cout << "      Missing from ClickHouse: " << missing_from_clickhouse << std::endl;
    std::cout << "      Consistency rate: " << (result.consistency_rate * 100) << "%" << std::endl;
    
    return result;
}

DataIntegrityResult DataIntegrityTest::TestTimestampAccuracy() {
    std::cout << "    Testing timestamp accuracy..." << std::endl;
    
    const uint32_t test_messages = 5000;
    const uint64_t expected_interval_ns = 1000000; // 1ms intervals
    
    // Setup test environment
    auto data_bus = test_utils_->CreateMockDataBus();
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    std::vector<TestTick> received_ticks;
    std::mutex received_mutex;
    
    // Consumer to collect timestamped data
    auto consumer_future = std::async(std::launch::async, [&]() {
        auto consumer = data_bus->CreateConsumer();
        
        for (uint32_t i = 0; i < test_messages; ++i) {
            auto tick = consumer->ConsumeMessage(std::chrono::seconds(1));
            
            if (tick.has_value()) {
                std::lock_guard<std::mutex> lock(received_mutex);
                received_ticks.push_back(*tick);
            }
        }
    });
    
    // Producer with precise timing
    auto producer_future = std::async(std::launch::async, [&]() {
        auto start_time = std::chrono::high_resolution_clock::now();
        
        for (uint32_t i = 0; i < test_messages; ++i) {
            auto tick = tick_generator->GenerateTestTick();
            tick.sequence = i + 1;
            
            // Set precise timestamp
            auto current_time = std::chrono::high_resolution_clock::now();
            tick.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
                current_time.time_since_epoch()).count();
            
            data_bus->Publish(tick);
            
            // Wait for next interval
            std::this_thread::sleep_for(std::chrono::nanoseconds(expected_interval_ns));
        }
    });
    
    // Wait for completion
    producer_future.wait();
    consumer_future.wait();
    
    // Analyze timestamp accuracy
    std::sort(received_ticks.begin(), received_ticks.end(),
              [](const auto& a, const auto& b) { return a.timestamp < b.timestamp; });
    
    uint64_t accurate_timestamps = 0;
    uint64_t timestamp_errors = 0;
    double max_deviation_ns = 0.0;
    double total_deviation_ns = 0.0;
    
    for (size_t i = 1; i < received_ticks.size(); ++i) {
        uint64_t actual_interval = received_ticks[i].timestamp - received_ticks[i-1].timestamp;
        double deviation = std::abs(static_cast<double>(actual_interval) - expected_interval_ns);
        
        total_deviation_ns += deviation;
        max_deviation_ns = std::max(max_deviation_ns, deviation);
        
        // Consider accurate if within 100μs (100,000ns)
        if (deviation <= 100000) {
            accurate_timestamps++;
        } else {
            timestamp_errors++;
        }
    }
    
    double average_deviation_ns = received_ticks.size() > 1 ? 
                                 total_deviation_ns / (received_ticks.size() - 1) : 0.0;
    
    DataIntegrityResult result;
    result.messages_sent = test_messages;
    result.messages_received = received_ticks.size();
    result.consistency_rate = received_ticks.size() > 1 ? 
                             static_cast<double>(accurate_timestamps) / (received_ticks.size() - 1) : 1.0;
    result.corrupted_messages = timestamp_errors;
    
    std::cout << "      Messages processed: " << received_ticks.size() << std::endl;
    std::cout << "      Accurate timestamps: " << accurate_timestamps << std::endl;
    std::cout << "      Timestamp errors: " << timestamp_errors << std::endl;
    std::cout << "      Average deviation: " << (average_deviation_ns / 1000.0) << "μs" << std::endl;
    std::cout << "      Max deviation: " << (max_deviation_ns / 1000.0) << "μs" << std::endl;
    std::cout << "      Accuracy rate: " << (result.consistency_rate * 100) << "%" << std::endl;
    
    return result;
}

} // namespace performance_tests