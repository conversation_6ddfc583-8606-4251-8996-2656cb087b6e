#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTDX数据采集脚本
功能：
1. 更新各市场的代码表（股票、指数、期货、基金、债券）
2. 逐个更新历史行情数据（日线、分钟线）
3. 支持增量更新和全量更新
4. 数据质量控制和去重
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from collectors.pytdx_collector import PyTDXCollector, PyTDXConfig
from storage.python_storage_manager import StorageManagerFactory

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('pytdx_collector.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class PyTDXDataCollector:
    """PyTDX数据采集管理器"""
    
    def __init__(self):
        # 创建配置
        self.config = PyTDXConfig()
        
        # 创建存储管理器
        try:
            self.storage_manager = StorageManagerFactory.create_default()
            logger.info("使用标准存储管理器")
        except Exception as e:
            logger.warning(f"创建存储管理器失败，使用模拟存储: {e}")
            from collectors.pytdx_collector import MockStorageManager
            self.storage_manager = MockStorageManager()
        
        # 创建采集器
        self.collector = PyTDXCollector(self.config, self.storage_manager)
        
        # 数据统计
        self.stats = {
            'symbol_lists_updated': 0,
            'symbols_processed': 0,
            'data_points_collected': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }
    
    async def initialize(self) -> bool:
        """初始化采集器"""
        try:
            logger.info("初始化PyTDX数据采集器...")
            success = await self.collector.initialize()
            if success:
                logger.info("✅ PyTDX采集器初始化成功")
                return True
            else:
                logger.error("❌ PyTDX采集器初始化失败")
                return False
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    async def update_symbol_lists(self, symbol_types: List[str] = None) -> Dict[str, List[Dict]]:
        """
        更新代码表
        
        Args:
            symbol_types: 要更新的代码表类型，默认为所有类型
            
        Returns:
            更新后的代码表字典
        """
        if symbol_types is None:
            symbol_types = ['stock', 'index', 'futures', 'fund', 'bond']
        
        logger.info(f"开始更新代码表: {symbol_types}")
        
        try:
            # 获取所有代码表
            symbol_lists = await self.collector.get_all_symbol_lists(symbol_types)
            
            # 统计信息
            total_symbols = 0
            for symbol_type, symbols in symbol_lists.items():
                count = len(symbols)
                total_symbols += count
                logger.info(f"✅ {symbol_type}代码表: {count}个")
            
            self.stats['symbol_lists_updated'] = len(symbol_lists)
            logger.info(f"✅ 代码表更新完成，共获取 {total_symbols} 个标的")
            
            return symbol_lists
            
        except Exception as e:
            logger.error(f"❌ 代码表更新失败: {e}")
            self.stats['errors'] += 1
            raise
    
    async def collect_historical_data(self, 
                                    symbol_lists: Dict[str, List[Dict]], 
                                    data_types: List[str] = None,
                                    periods: List[str] = None,
                                    days_back: int = 30) -> bool:
        """
        采集历史数据
        
        Args:
            symbol_lists: 代码表字典
            data_types: 数据类型列表 ['stock', 'index', 'futures']
            periods: K线周期列表 ['daily', '60min', '30min', '15min', '5min', '1min']
            days_back: 回溯天数
            
        Returns:
            是否成功
        """
        if data_types is None:
            data_types = ['stock', 'index']  # 默认只采集股票和指数
        
        if periods is None:
            periods = ['daily', '60min', '30min']  # 默认采集日线和小时线
        
        logger.info(f"开始采集历史数据: {data_types}, 周期: {periods}, 回溯: {days_back}天")
        
        success_count = 0
        error_count = 0
        
        try:
            for data_type in data_types:
                if data_type not in symbol_lists:
                    logger.warning(f"代码表中没有 {data_type} 类型数据")
                    continue
                
                symbols = symbol_lists[data_type]
                logger.info(f"开始采集 {data_type} 数据，共 {len(symbols)} 个标的")
                
                for i, symbol_info in enumerate(symbols):
                    symbol = symbol_info.get('code', '')
                    name = symbol_info.get('name', '')
                    
                    if not symbol:
                        continue
                    
                    logger.info(f"[{i+1}/{len(symbols)}] 采集 {symbol} ({name})")
                    
                    try:
                        # 采集不同周期的数据
                        for period in periods:
                            await self._collect_symbol_data(symbol, data_type, period, days_back)
                        
                        success_count += 1
                        self.stats['symbols_processed'] += 1
                        
                        # 避免请求过于频繁
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        logger.error(f"采集 {symbol} 数据失败: {e}")
                        error_count += 1
                        self.stats['errors'] += 1
                        continue
            
            logger.info(f"✅ 历史数据采集完成: 成功 {success_count}, 失败 {error_count}")
            return error_count == 0
            
        except Exception as e:
            logger.error(f"❌ 历史数据采集失败: {e}")
            self.stats['errors'] += 1
            return False
    
    async def _collect_symbol_data(self, symbol: str, data_type: str, period: str, days_back: int):
        """采集单个标的的数据"""
        try:
            # 计算开始日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # 根据数据类型选择采集方法
            if data_type == 'stock':
                data = await self.collector.get_k_data(
                    symbol=symbol,
                    period=period,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=end_date.strftime('%Y-%m-%d')
                )
            elif data_type == 'index':
                data = await self.collector.get_index_data(
                    symbol=symbol,
                    period=period,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=end_date.strftime('%Y-%m-%d')
                )
            elif data_type == 'futures':
                data = await self.collector.get_futures_data(
                    symbol=symbol,
                    period=period,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=end_date.strftime('%Y-%m-%d')
                )
            else:
                logger.warning(f"不支持的数据类型: {data_type}")
                return
            
            if data is not None and not data.empty:
                # 归档数据
                if self.collector.archiver:
                    await self.collector.archiver.archive_k_data(
                        symbol=symbol,
                        data=data,
                        data_type=f"{data_type}_{period}"
                    )
                
                self.stats['data_points_collected'] += len(data)
                logger.info(f"  ✅ {symbol} {period} 数据: {len(data)} 条")
            else:
                logger.warning(f"  ⚠️ {symbol} {period} 无数据")
                
        except Exception as e:
            logger.error(f"采集 {symbol} {period} 数据失败: {e}")
            raise
    
    async def run_full_collection(self, 
                                symbol_types: List[str] = None,
                                data_types: List[str] = None,
                                periods: List[str] = None,
                                days_back: int = 30) -> bool:
        """
        运行完整的数据采集流程
        
        Args:
            symbol_types: 代码表类型
            data_types: 数据类型
            periods: K线周期
            days_back: 回溯天数
            
        Returns:
            是否成功
        """
        self.stats['start_time'] = datetime.now()
        
        try:
            logger.info("🚀 开始PyTDX数据采集")
            logger.info("=" * 60)
            
            # 1. 初始化
            if not await self.initialize():
                return False
            
            # 2. 更新代码表
            logger.info("\n📋 第一步: 更新代码表")
            symbol_lists = await self.update_symbol_lists(symbol_types)
            
            # 3. 采集历史数据
            logger.info("\n📊 第二步: 采集历史数据")
            success = await self.collect_historical_data(
                symbol_lists=symbol_lists,
                data_types=data_types,
                periods=periods,
                days_back=days_back
            )
            
            # 4. 显示统计信息
            await self._show_statistics()
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 数据采集失败: {e}")
            return False
        finally:
            self.stats['end_time'] = datetime.now()
            await self.cleanup()
    
    async def _show_statistics(self):
        """显示统计信息"""
        logger.info("\n📈 采集统计")
        logger.info("=" * 60)
        logger.info(f"代码表更新: {self.stats['symbol_lists_updated']} 个")
        logger.info(f"标的处理: {self.stats['symbols_processed']} 个")
        logger.info(f"数据点采集: {self.stats['data_points_collected']} 条")
        logger.info(f"错误数量: {self.stats['errors']} 个")
        
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            logger.info(f"总耗时: {duration}")
        
        # 显示采集器统计
        collector_stats = self.collector.get_stats()
        logger.info(f"采集器统计: {collector_stats}")
        
        # 显示存储统计
        if hasattr(self.storage_manager, 'get_statistics'):
            storage_stats = self.storage_manager.get_statistics()
            logger.info(f"存储统计: {storage_stats}")
        
        # 显示归档统计
        if self.collector.archiver:
            archive_stats = self.collector.archiver.get_stats()
            logger.info(f"归档统计: {archive_stats}")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.collector:
                await self.collector.close()
            logger.info("✅ 资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


async def main():
    """主函数"""
    print("PyTDX数据采集系统")
    print("=" * 60)
    print("功能:")
    print("1. 更新各市场代码表（股票、指数、期货、基金、债券）")
    print("2. 采集历史行情数据（日线、分钟线）")
    print("3. 数据质量控制和去重")
    print("4. 自动存储到数据库")
    print()
    
    # 创建采集器
    collector = PyTDXDataCollector()
    
    try:
        # 配置采集参数
        symbol_types = ['stock', 'index']  # 采集股票和指数代码表
        data_types = ['stock', 'index']    # 采集股票和指数数据
        periods = ['daily', '60min', '30min']  # 采集日线、60分钟、30分钟数据
        days_back = 30  # 回溯30天
        
        # 运行采集
        success = await collector.run_full_collection(
            symbol_types=symbol_types,
            data_types=data_types,
            periods=periods,
            days_back=days_back
        )
        
        if success:
            print("\n🎉 数据采集完成！")
            return 0
        else:
            print("\n❌ 数据采集失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断采集")
        return 1
    except Exception as e:
        print(f"\n❌ 采集过程出错: {e}")
        return 1


if __name__ == "__main__":
    # 运行采集
    exit_code = asyncio.run(main())
    sys.exit(exit_code)