#pragma once

#include <memory>
#include <string>
#include <vector>
#include <functional>
#include <future>
#include <chrono>
#include <atomic>
#include <unordered_map>

#include "data_types.h"

namespace financial_data {
namespace sdk {

// Forward declarations
class MarketDataClient;
class ConnectionPool;
class AsyncClient;
class ErrorHandler;

// Error codes
enum class ErrorCode {
    SUCCESS = 0,
    CONNECTION_FAILED,
    AUTHENTICATION_FAILED,
    SUBSCRIPTION_FAILED,
    TIMEOUT,
    INVALID_PARAMETER,
    NETWORK_ERROR,
    SERVER_ERROR,
    UNKNOWN_ERROR
};

// Error information
struct ErrorInfo {
    ErrorCode code;
    std::string message;
    std::chrono::system_clock::time_point timestamp;
    
    ErrorInfo(ErrorCode c, const std::string& msg) 
        : code(c), message(msg), timestamp(std::chrono::system_clock::now()) {}
};

// Connection configuration
struct ConnectionConfig {
    std::string server_address = "localhost:50051";
    std::string auth_token;
    std::chrono::milliseconds connect_timeout{5000};
    std::chrono::milliseconds request_timeout{10000};
    int max_retry_attempts = 3;
    std::chrono::milliseconds retry_interval{1000};
    bool enable_compression = true;
    bool enable_keepalive = true;
    std::chrono::seconds keepalive_time{30};
    std::chrono::seconds keepalive_timeout{5};
    int max_receive_message_size = 64 * 1024 * 1024; // 64MB
    int max_send_message_size = 16 * 1024 * 1024;    // 16MB
};

// Subscription configuration
struct SubscriptionConfig {
    std::vector<std::string> symbols;
    std::string exchange;
    bool include_level2 = false;
    int buffer_size = 1000;
    std::chrono::milliseconds heartbeat_interval{10000};
};

// Data callback types
using TickDataCallback = std::function<void(const StandardTick&)>;
using Level2DataCallback = std::function<void(const Level2Data&)>;
using ErrorCallback = std::function<void(const ErrorInfo&)>;
using ConnectionStatusCallback = std::function<void(bool connected)>;

// Connection status
enum class ConnectionStatus {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    RECONNECTING,
    FAILED
};

// Main SDK client interface
class FinancialDataSDK {
public:
    explicit FinancialDataSDK(const ConnectionConfig& config = ConnectionConfig{});
    ~FinancialDataSDK();

    // Non-copyable, movable
    FinancialDataSDK(const FinancialDataSDK&) = delete;
    FinancialDataSDK& operator=(const FinancialDataSDK&) = delete;
    FinancialDataSDK(FinancialDataSDK&&) = default;
    FinancialDataSDK& operator=(FinancialDataSDK&&) = default;

    // Connection management
    bool Connect();
    void Disconnect();
    bool IsConnected() const;
    ConnectionStatus GetConnectionStatus() const;

    // Synchronous API
    std::vector<StandardTick> GetLatestTicks(const std::vector<std::string>& symbols, 
                                           const std::string& exchange = "");
    std::vector<StandardTick> GetHistoricalTicks(const std::string& symbol,
                                               const std::string& exchange,
                                               int64_t start_timestamp,
                                               int64_t end_timestamp,
                                               int limit = 1000);
    std::vector<Level2Data> GetLevel2Data(const std::vector<std::string>& symbols,
                                        const std::string& exchange = "",
                                        int depth = 5);

    // Asynchronous API
    std::future<std::vector<StandardTick>> GetLatestTicksAsync(
        const std::vector<std::string>& symbols, 
        const std::string& exchange = "");
    std::future<std::vector<StandardTick>> GetHistoricalTicksAsync(
        const std::string& symbol,
        const std::string& exchange,
        int64_t start_timestamp,
        int64_t end_timestamp,
        int limit = 1000);

    // Streaming subscriptions
    bool SubscribeTickData(const SubscriptionConfig& config, 
                          TickDataCallback callback);
    bool SubscribeLevel2Data(const SubscriptionConfig& config,
                           Level2DataCallback callback);
    bool UnsubscribeTickData(const std::vector<std::string>& symbols);
    bool UnsubscribeLevel2Data(const std::vector<std::string>& symbols);
    void UnsubscribeAll();

    // Callback management
    void SetErrorCallback(ErrorCallback callback);
    void SetConnectionStatusCallback(ConnectionStatusCallback callback);

    // Statistics and monitoring
    struct Statistics {
        uint64_t messages_received = 0;
        uint64_t messages_sent = 0;
        uint64_t connection_count = 0;
        uint64_t reconnection_count = 0;
        std::chrono::microseconds avg_latency{0};
        std::chrono::microseconds max_latency{0};
        std::chrono::system_clock::time_point last_message_time;
    };
    
    Statistics GetStatistics() const;
    void ResetStatistics();

    // Health check
    bool HealthCheck();

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

// Utility functions
namespace utils {
    // Convert timestamp formats
    int64_t SystemTimeToNanoseconds(const std::chrono::system_clock::time_point& tp);
    std::chrono::system_clock::time_point NanosecondsToSystemTime(int64_t ns);
    
    // Data validation
    bool ValidateSymbol(const std::string& symbol);
    bool ValidateExchange(const std::string& exchange);
    bool ValidateTimestamp(int64_t timestamp_ns);
    
    // Performance utilities
    class LatencyMeasurer {
    public:
        LatencyMeasurer();
        void Start();
        std::chrono::microseconds Stop();
        std::chrono::microseconds GetLastLatency() const;
        
    private:
        std::chrono::high_resolution_clock::time_point start_time_;
        std::chrono::microseconds last_latency_{0};
    };
}

} // namespace sdk
} // namespace financial_data