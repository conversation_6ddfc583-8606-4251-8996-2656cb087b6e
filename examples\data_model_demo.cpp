#include <iostream>
#include <chrono>
#include "proto/data_types.h"
#include "proto/serializer.h"
#include "proto/validator.h"

using namespace financial_data;

void DemoBasicDataTypes() {
    std::cout << "=== Basic Data Types Demo ===" << std::endl;
    
    // 创建StandardTick数据
    StandardTick tick;
    tick.symbol = "AAPL";
    tick.exchange = "NASDAQ";
    tick.SetCurrentTimestamp();
    tick.last_price = 150.25;
    tick.volume = 1000;
    tick.turnover = 150250.0;
    tick.open_interest = 50000;
    tick.sequence = 1;
    tick.trade_flag = "0";
    
    // 设置五档买卖盘
    tick.bids[0] = PriceLevel(150.20, 100, 5);
    tick.bids[1] = PriceLevel(150.15, 200, 8);
    tick.asks[0] = PriceLevel(150.30, 120, 6);
    tick.asks[1] = PriceLevel(150.35, 180, 4);
    
    std::cout << "Created StandardTick for " << tick.symbol 
              << " at price " << tick.last_price << std::endl;
    std::cout << "Tick is valid: " << (tick.IsValid() ? "Yes" : "No") << std::endl;
    
    // 创建Level2数据
    Level2Data level2;
    level2.symbol = "MSFT";
    level2.exchange = "NASDAQ";
    level2.SetCurrentTimestamp();
    level2.sequence = 1;
    
    // 添加买卖盘档位
    for (int i = 0; i < 5; ++i) {
        level2.bids.emplace_back(300.0 - i * 0.05, 100 + i * 50, 3 + i);
        level2.asks.emplace_back(300.05 + i * 0.05, 120 + i * 30, 4 + i);
    }
    
    std::cout << "Created Level2Data for " << level2.symbol 
              << " with " << level2.bids.size() << " bid levels" << std::endl;
    std::cout << "Level2 is valid: " << (level2.IsValid() ? "Yes" : "No") << std::endl;
}

void DemoSerialization() {
    std::cout << "\n=== Serialization Demo ===" << std::endl;
    
    // 创建测试数据
    StandardTick tick;
    tick.symbol = "GOOGL";
    tick.exchange = "NASDAQ";
    tick.SetCurrentTimestamp();
    tick.last_price = 2500.75;
    tick.volume = 500;
    tick.sequence = 100;
    
    MarketDataWrapper wrapper(tick);
    wrapper.source = "Demo";
    
    // 使用FastSerializer进行序列化
    FastSerializer serializer;
    bool serialize_result = serializer.SerializeToBuffer(wrapper);
    
    if (serialize_result) {
        std::cout << "Serialization successful, buffer size: " 
                  << serializer.GetBufferSize() << " bytes" << std::endl;
        
        // 反序列化
        MarketDataWrapper deserialized;
        bool deserialize_result = serializer.DeserializeFromBuffer(
            serializer.GetSerializedData(), &deserialized);
        
        if (deserialize_result) {
            std::cout << "Deserialization successful" << std::endl;
            std::cout << "Original symbol: " << tick.symbol 
                      << ", Deserialized symbol: " << deserialized.tick_data.symbol << std::endl;
            std::cout << "Original price: " << tick.last_price 
                      << ", Deserialized price: " << deserialized.tick_data.last_price << std::endl;
        } else {
            std::cout << "Deserialization failed" << std::endl;
        }
    } else {
        std::cout << "Serialization failed" << std::endl;
    }
}

void DemoValidation() {
    std::cout << "\n=== Validation Demo ===" << std::endl;
    
    DataValidator validator;
    
    // 配置验证器
    PriceAnomalyConfig price_config;
    price_config.max_price_change_ratio = 0.1; // 10%最大价格变动
    price_config.min_price = 0.01;
    price_config.max_price = 10000.0;
    validator.SetPriceConfig(price_config);
    
    // 测试有效数据
    StandardTick valid_tick;
    valid_tick.symbol = "TSLA";
    valid_tick.exchange = "NASDAQ";
    valid_tick.SetCurrentTimestamp();
    valid_tick.last_price = 800.0;
    valid_tick.volume = 1000;
    valid_tick.sequence = 1;
    
    ValidationResult result = validator.ValidateTick(valid_tick);
    std::cout << "Valid tick validation result: " 
              << DataValidator::ValidationResultToString(result) << std::endl;
    
    // 测试无效数据（价格为0）
    StandardTick invalid_tick = valid_tick;
    invalid_tick.last_price = 0.0;
    invalid_tick.sequence = 2;
    
    result = validator.ValidateTick(invalid_tick);
    std::cout << "Invalid tick (price=0) validation result: " 
              << DataValidator::ValidationResultToString(result) << std::endl;
    
    // 测试价格异常（价格跳跃过大）
    StandardTick anomaly_tick = valid_tick;
    anomaly_tick.last_price = 1200.0; // 50%的价格跳跃
    anomaly_tick.sequence = 3;
    
    result = validator.ValidateTick(anomaly_tick);
    std::cout << "Price anomaly tick validation result: " 
              << DataValidator::ValidationResultToString(result) << std::endl;
}

void DemoQualityMonitoring() {
    std::cout << "\n=== Quality Monitoring Demo ===" << std::endl;
    
    DataQualityMonitor monitor;
    
    // 模拟一批数据
    for (int i = 0; i < 10; ++i) {
        StandardTick tick;
        tick.symbol = "DEMO" + std::to_string(i % 3); // 3个不同的符号
        tick.exchange = "TEST";
        tick.SetCurrentTimestamp();
        tick.last_price = 100.0 + i;
        tick.volume = 1000;
        tick.sequence = i + 1;
        
        // 故意制造一些无效数据
        if (i == 3) tick.symbol = ""; // 无效符号
        if (i == 7) tick.last_price = 0.0; // 无效价格
        
        MarketDataWrapper wrapper(tick);
        ValidationResult result = monitor.MonitorData(wrapper);
        
        std::cout << "Data " << i << " validation: " 
                  << DataValidator::ValidationResultToString(result) << std::endl;
    }
    
    // 生成质量报告
    std::cout << "\n" << monitor.GenerateQualityReport() << std::endl;
}

void DemoBatchProcessing() {
    std::cout << "\n=== Batch Processing Demo ===" << std::endl;
    
    MarketDataBatch batch;
    
    // 添加多种类型的数据到批次
    for (int i = 0; i < 5; ++i) {
        StandardTick tick;
        tick.symbol = "BATCH" + std::to_string(i);
        tick.exchange = "TEST";
        tick.SetCurrentTimestamp();
        tick.last_price = 50.0 + i;
        tick.volume = 100 * (i + 1);
        tick.sequence = i + 1;
        
        batch.AddTick(tick);
    }
    
    // 添加Level2数据
    Level2Data level2;
    level2.symbol = "LEVEL2_BATCH";
    level2.exchange = "TEST";
    level2.SetCurrentTimestamp();
    level2.sequence = 10;
    level2.bids.emplace_back(99.95, 1000, 10);
    level2.asks.emplace_back(100.05, 1200, 12);
    
    batch.AddLevel2(level2);
    
    std::cout << "Created batch with " << batch.Size() << " data items" << std::endl;
    
    // 序列化批次
    FastSerializer serializer;
    bool serialize_result = serializer.SerializeToBuffer(batch);
    
    if (serialize_result) {
        std::cout << "Batch serialization successful, size: " 
                  << serializer.GetBufferSize() << " bytes" << std::endl;
        
        // 反序列化
        MarketDataBatch deserialized_batch;
        bool deserialize_result = serializer.DeserializeFromBuffer(
            serializer.GetSerializedData(), &deserialized_batch);
        
        if (deserialize_result) {
            std::cout << "Batch deserialization successful" << std::endl;
            std::cout << "Original batch size: " << batch.Size() 
                      << ", Deserialized batch size: " << deserialized_batch.Size() << std::endl;
        }
    }
}

void DemoMemoryPool() {
    std::cout << "\n=== Memory Pool Demo ===" << std::endl;
    
    MemoryPool<StandardTick> pool(5);
    
    std::cout << "Initial pool state - Total: " << pool.TotalSize() 
              << ", Available: " << pool.AvailableSize() 
              << ", Used: " << pool.UsedSize() << std::endl;
    
    // 获取一些对象
    std::vector<StandardTick*> ticks;
    for (int i = 0; i < 3; ++i) {
        StandardTick* tick = pool.Acquire();
        tick->symbol = "POOL" + std::to_string(i);
        tick->last_price = 100.0 + i;
        ticks.push_back(tick);
    }
    
    std::cout << "After acquiring 3 objects - Available: " << pool.AvailableSize() 
              << ", Used: " << pool.UsedSize() << std::endl;
    
    // 释放对象
    for (auto* tick : ticks) {
        pool.Release(tick);
    }
    
    std::cout << "After releasing objects - Available: " << pool.AvailableSize() 
              << ", Used: " << pool.UsedSize() << std::endl;
}

int main() {
    std::cout << "Financial Data Service - Data Model Demo" << std::endl;
    std::cout << "=========================================" << std::endl;
    
    try {
        DemoBasicDataTypes();
        DemoSerialization();
        DemoValidation();
        DemoQualityMonitoring();
        DemoBatchProcessing();
        DemoMemoryPool();
        
        std::cout << "\n=== Demo completed successfully ===" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Demo failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}