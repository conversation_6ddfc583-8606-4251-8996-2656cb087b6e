# 统一配置管理框架

本框架提供了一个统一的配置管理解决方案，支持热更新、版本管理、配置验证等高级功能。

## 特性

- **统一配置格式**: 支持JSON格式的层次化配置
- **热更新**: 自动监控配置文件变更并重新加载
- **版本管理**: 支持配置快照和版本回滚
- **配置验证**: 内置多种配置验证器，确保配置正确性
- **环境变量支持**: 支持在配置中使用环境变量
- **变更监听**: 支持配置变更事件监听
- **多语言支持**: 提供C++和Python两种接口
- **线程安全**: 支持多线程并发访问

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    配置管理框架                              │
├─────────────────────────────────────────────────────────────┤
│  C++ ConfigManager          │  Python ConfigManager         │
│  ├─ 配置加载/保存            │  ├─ 配置加载/保存              │
│  ├─ 热更新监控              │  ├─ 热更新监控                │
│  ├─ 版本管理                │  ├─ 版本管理                  │
│  ├─ 配置验证                │  ├─ 配置验证                  │
│  └─ 变更通知                │  └─ 变更通知                  │
├─────────────────────────────────────────────────────────────┤
│                    配置验证器                                │
│  ├─ ServerConfigValidator   │  ├─ PytdxConfigValidator      │
│  ├─ RedisConfigValidator    │  ├─ CollectionConfigValidator │
│  ├─ ClickHouseValidator     │  ├─ StorageConfigValidator    │
│  ├─ MonitoringValidator     │  ├─ SchedulingConfigValidator │
│  └─ ...                     │  └─ MonitoringConfigValidator │
├─────────────────────────────────────────────────────────────┤
│                    统一配置文件                              │
│              config/unified_config.json                     │
└─────────────────────────────────────────────────────────────┘
```

## 快速开始

### C++ 使用示例

```cpp
#include "config/config_manager.h"
#include "config/config_validators.h"

using namespace config;

int main() {
    // 获取配置管理器实例
    auto& config_manager = ConfigManager::Instance();
    
    // 初始化
    if (!config_manager.Initialize("config/unified_config.json")) {
        std::cerr << "Failed to initialize config manager" << std::endl;
        return -1;
    }
    
    // 注册验证器
    auto server_validator = std::make_shared<ServerConfigValidator>();
    config_manager.RegisterValidator("server", server_validator);
    
    // 读取配置
    std::string host = config_manager.GetValue<std::string>("server.host", "localhost");
    int port = config_manager.GetValue<int>("server.port", 8080);
    
    // 修改配置
    config_manager.SetValue("server.port", 9090);
    
    // 启用热更新
    config_manager.EnableHotReload(true);
    
    // 创建快照
    std::string snapshot_id = config_manager.CreateSnapshot("Initial config");
    
    return 0;
}
```

### Python 使用示例

```python
from config.config_manager_python import initialize_config, get_config, set_config
from config.python_validators import CollectionConfigValidator

# 初始化配置管理器
initialize_config("config/unified_config.json")

# 读取配置
host = get_config("server.host", "localhost")
port = get_config("server.port", 8080)

# 修改配置
set_config("server.port", 9090)

# 使用配置管理器实例进行高级操作
from config.config_manager_python import PythonConfigManager

config_manager = PythonConfigManager()

# 注册验证器
validator = CollectionConfigValidator()
config_manager.register_validator("collection", validator)

# 启用热更新
config_manager.enable_hot_reload(True)

# 创建快照
snapshot_id = config_manager.create_snapshot("Initial config")
```

## 配置文件格式

配置文件采用JSON格式，支持层次化结构：

```json
{
  "version": "1.0.0",
  "environment": "development",
  "server": {
    "host": "0.0.0.0",
    "port": 8080,
    "threads": 4
  },
  "collection": {
    "pytdx": {
      "enabled": true,
      "servers": [
        {"host": "**************", "port": 7709}
      ],
      "batch_size": 1000
    },
    "ctp": {
      "enabled": true,
      "config_path": "config/ctp_config.json"
    }
  },
  "storage": {
    "hot_storage": {
      "type": "redis",
      "retention_days": 7,
      "config": {
        "host": "127.0.0.1",
        "port": 6379
      }
    }
  }
}
```

## 环境变量支持

配置文件中可以使用环境变量：

```json
{
  "database": {
    "host": "${DB_HOST}",
    "password": "${DB_PASSWORD}",
    "port": "${DB_PORT}"
  }
}
```

支持环境变量前缀：

```python
config_manager.enable_environment_variables(True)
config_manager.set_environment_prefix("MARKET_DATA_")

# 配置中的 ${PORT} 会查找 MARKET_DATA_PORT 环境变量
```

## 配置验证

框架提供了多种内置验证器：

### C++ 验证器

- `ServerConfigValidator`: 验证服务器配置
- `RedisConfigValidator`: 验证Redis配置
- `ClickHouseConfigValidator`: 验证ClickHouse配置
- `CTPConfigValidator`: 验证CTP配置
- `MonitoringConfigValidator`: 验证监控配置

### Python 验证器

- `PytdxConfigValidator`: 验证pytdx采集器配置
- `CollectionConfigValidator`: 验证采集配置
- `StorageConfigValidator`: 验证存储配置
- `SchedulingConfigValidator`: 验证调度配置
- `MonitoringConfigValidator`: 验证监控配置

### 自定义验证器

```cpp
// C++
class CustomValidator : public ConfigValidator {
public:
    ValidationResult Validate(const nlohmann::json& config) const override {
        ValidationResult result;
        // 实现验证逻辑
        return result;
    }
    
    std::string GetValidatorName() const override {
        return "CustomValidator";
    }
};
```

```python
# Python
class CustomValidator(ConfigValidator):
    def validate(self, config):
        result = ValidationResult()
        # 实现验证逻辑
        return result
    
    def get_validator_name(self):
        return "CustomValidator"
```

## 热更新

启用热更新后，系统会自动监控配置文件变更：

```cpp
// C++
config_manager.EnableHotReload(true);
config_manager.SetFileWatchInterval(std::chrono::milliseconds(1000));
```

```python
# Python
config_manager.enable_hot_reload(True)
config_manager.set_file_watch_interval(1.0)  # 1秒间隔
```

## 版本管理

支持配置快照和版本回滚：

```cpp
// C++
// 创建快照
std::string snapshot_id = config_manager.CreateSnapshot("Before update");

// 恢复快照
config_manager.RestoreFromSnapshot(snapshot_id);

// 获取版本历史
auto history = config_manager.GetVersionHistory();
```

```python
# Python
# 创建快照
snapshot_id = config_manager.create_snapshot("Before update")

# 恢复快照
config_manager.restore_from_snapshot(snapshot_id)

# 获取版本历史
history = config_manager.get_version_history()
```

## 变更监听

支持监听配置变更事件：

```cpp
// C++
class MyListener : public ConfigChangeListener {
public:
    void OnConfigChanged(const ConfigChangeEvent& event) override {
        std::cout << "Config changed: " << event.key << std::endl;
    }
};

auto listener = std::make_shared<MyListener>();
config_manager.RegisterChangeListener(listener);
```

```python
# Python
class MyListener(ConfigChangeListener):
    def on_config_changed(self, event):
        print(f"Config changed: {event.key}")

listener = MyListener()
config_manager.register_change_listener(listener)
```

## 性能特性

- **读取性能**: 支持高并发读取，使用读写锁优化
- **内存效率**: 使用智能指针和RAII管理资源
- **线程安全**: 所有操作都是线程安全的
- **缓存优化**: 配置值缓存，减少重复解析

## 最佳实践

1. **配置分层**: 将不同功能的配置分组到不同的节中
2. **环境变量**: 敏感信息使用环境变量，不要硬编码
3. **验证器**: 为每个配置节注册相应的验证器
4. **版本管理**: 在重要变更前创建快照
5. **监听器**: 使用变更监听器实现配置热更新
6. **错误处理**: 始终检查配置操作的返回值

## 故障排除

### 常见问题

1. **配置文件不存在**
   - 检查文件路径是否正确
   - 确保文件具有读取权限

2. **JSON格式错误**
   - 使用JSON验证工具检查格式
   - 注意逗号和引号的使用

3. **环境变量未解析**
   - 确保启用了环境变量支持
   - 检查环境变量名称和前缀设置

4. **热更新不工作**
   - 检查文件监控是否启用
   - 确认文件系统支持文件变更通知

5. **验证失败**
   - 查看验证错误信息
   - 检查配置值的类型和范围

### 调试技巧

1. **启用详细日志**
2. **使用配置导出功能检查当前配置**
3. **查看统计信息了解系统状态**
4. **使用版本历史追踪配置变更**

## 扩展开发

### 添加新的验证器

1. 继承相应的基类
2. 实现验证逻辑
3. 注册到配置管理器

### 添加新的配置节

1. 在统一配置文件中添加新节
2. 创建相应的验证器
3. 更新文档和示例

### 集成到现有系统

1. 替换现有的配置读取代码
2. 添加配置验证
3. 启用热更新和版本管理

## 测试

运行单元测试：

```bash
# C++ 测试
mkdir build && cd build
cmake ..
make
./test_config_manager

# Python 测试
python -m pytest tests/test_config_manager_python.py -v
```

## 许可证

本项目采用 MIT 许可证。