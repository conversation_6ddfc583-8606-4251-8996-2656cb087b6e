# 行情采集模块完善设计文档

## 概述

本设计文档基于现有的金融数据服务系统架构，针对行情采集模块的完善需求，提出了一个统一的数据采集、存储和访问解决方案。设计重点解决pytdx历史数据采集器缺少归档功能、定期更新机制以及与CTP实时采集器协调工作的问题。

### 设计目标

1. **数据完整性**: 确保历史数据和实时数据的无缝衔接
2. **存储优化**: 实现热、温、冷存储的自动分层管理
3. **性能提升**: 通过批处理和异步操作提高数据处理效率
4. **可维护性**: 提供统一的配置管理和监控接口
5. **扩展性**: 支持新的数据源和存储后端的接入

## 架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "数据源层"
        A[pytdx历史数据] --> C[数据采集协调器]
        B[CTP实时数据] --> C
    end
    
    subgraph "数据处理层"
        C --> D[数据标准化器]
        D --> E[数据去重器]
        E --> F[数据验证器]
        F --> G[存储路由器]
    end
    
    subgraph "存储层"
        G --> H[Redis热存储<br/>7天内数据]
        G --> I[ClickHouse温存储<br/>7天-2年数据]
        G --> J[S3冷存储<br/>2年以上数据]
    end
    
    subgraph "服务层"
        K[统一数据访问接口] --> H
        K --> I
        K --> J
        L[定时任务调度器] --> C
        M[数据迁移管理器] --> H
        M --> I
        M --> J
    end
    
    subgraph "监控层"
        N[采集监控] --> C
        O[存储监控] --> G
        P[性能监控] --> K
    end
```

### 核心组件设计

#### 1. 数据采集协调器 (DataCollectionCoordinator)

负责协调pytdx和CTP两个采集器的工作，避免数据重复和冲突。

**主要功能:**
- 管理采集器生命周期
- 协调实时和历史数据采集
- 处理数据源切换和故障转移
- 提供统一的数据输出接口

**接口设计:**
```cpp
class DataCollectionCoordinator {
public:
    struct CoordinatorConfig {
        bool enable_pytdx = true;
        bool enable_ctp = true;
        int data_overlap_tolerance_seconds = 300;  // 5分钟重叠容忍度
        int failover_timeout_seconds = 30;
        std::string priority_source = "ctp";  // 优先数据源
    };
    
    bool Initialize(const CoordinatorConfig& config);
    void Start();
    void Stop();
    
    // 数据采集控制
    bool StartHistoricalCollection(const std::vector<std::string>& symbols, 
                                  const std::string& start_date, 
                                  const std::string& end_date);
    bool StartRealtimeCollection(const std::vector<std::string>& symbols);
    
    // 数据协调
    void SetDataCallback(std::function<void(const MarketDataWrapper&)> callback);
    void SetStatusCallback(std::function<void(const std::string&, const std::string&)> callback);
    
    // 监控接口
    struct CollectionStatus {
        bool pytdx_active;
        bool ctp_active;
        uint64_t total_data_points;
        uint64_t duplicate_data_points;
        std::chrono::steady_clock::time_point last_update;
    };
    
    CollectionStatus GetStatus() const;
};
```

#### 2. 历史数据归档管理器 (HistoricalDataArchiver)

扩展现有的pytdx_collector.py，增加数据归档功能。

**Python实现设计:**
```python
class HistoricalDataArchiver:
    def __init__(self, config: ArchiverConfig):
        self.config = config
        self.storage_manager = StorageManager(config.storage_config)
        self.data_validator = DataValidator()
        self.deduplicator = DataDeduplicator()
        
    async def archive_k_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """归档K线数据到存储系统"""
        try:
            # 数据验证
            validated_data = await self.data_validator.validate_k_data(data)
            
            # 数据去重
            deduplicated_data = await self.deduplicator.deduplicate_k_data(
                symbol, validated_data
            )
            
            # 转换为标准格式
            standard_ticks = self._convert_to_standard_ticks(
                symbol, deduplicated_data
            )
            
            # 批量存储
            return await self.storage_manager.store_batch_async(standard_ticks)
            
        except Exception as e:
            logger.error(f"Failed to archive K data for {symbol}: {e}")
            return False
    
    async def archive_tick_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """归档tick数据到存储系统"""
        # 类似实现
        pass
    
    def setup_periodic_archiving(self, symbols: List[str], 
                                schedule: str = "0 2 * * *") -> None:
        """设置定期归档任务"""
        pass
```

#### 3. 定时任务调度器 (ScheduledTaskManager)

管理定期数据更新和维护任务。

**设计特点:**
- 支持cron表达式配置
- 任务失败重试机制
- 任务执行状态监控
- 支持任务优先级和依赖关系

```cpp
class ScheduledTaskManager {
public:
    enum class TaskType {
        HISTORICAL_UPDATE,
        DATA_MIGRATION,
        DATA_CLEANUP,
        HEALTH_CHECK
    };
    
    struct TaskConfig {
        TaskType type;
        std::string cron_expression;
        std::vector<std::string> symbols;
        std::map<std::string, std::string> parameters;
        int priority = 0;
        int max_retries = 3;
        std::chrono::seconds retry_delay{60};
    };
    
    bool ScheduleTask(const std::string& task_id, const TaskConfig& config);
    bool UnscheduleTask(const std::string& task_id);
    void Start();
    void Stop();
    
    // 任务状态查询
    struct TaskStatus {
        std::string task_id;
        TaskType type;
        std::string status;  // scheduled, running, completed, failed
        std::chrono::system_clock::time_point last_run;
        std::chrono::system_clock::time_point next_run;
        int retry_count;
        std::string error_message;
    };
    
    std::vector<TaskStatus> GetAllTaskStatus() const;
    TaskStatus GetTaskStatus(const std::string& task_id) const;
};
```

#### 4. 统一数据访问接口 (UnifiedDataAccessInterface)

提供透明的热、温、冷存储访问。

**接口设计:**
```cpp
class UnifiedDataAccessInterface {
public:
    struct QueryRequest {
        std::string symbol;
        std::string data_type;  // tick, kline, level2
        int64_t start_timestamp_ns;
        int64_t end_timestamp_ns;
        int limit = 1000;
        std::string cursor;  // 分页游标
    };
    
    struct QueryResponse {
        std::vector<StandardTick> ticks;
        std::vector<Level2Data> level2_data;
        std::string next_cursor;
        bool has_more;
        std::string storage_source;  // hot, warm, cold
        std::chrono::milliseconds query_time;
    };
    
    // 查询接口
    std::future<QueryResponse> QueryData(const QueryRequest& request);
    std::future<StandardTick> GetLatestTick(const std::string& symbol);
    std::future<std::vector<StandardTick>> GetRecentTicks(
        const std::string& symbol, int count = 100
    );
    
    // 批量查询
    std::future<std::map<std::string, StandardTick>> GetLatestTicks(
        const std::vector<std::string>& symbols
    );
    
private:
    // 存储层路由逻辑
    StorageLayer DetermineStorageLayer(int64_t timestamp_ns) const;
    std::future<QueryResponse> QueryFromHotStorage(const QueryRequest& request);
    std::future<QueryResponse> QueryFromWarmStorage(const QueryRequest& request);
    std::future<QueryResponse> QueryFromColdStorage(const QueryRequest& request);
};
```

#### 5. 数据质量管理器 (DataQualityManager)

确保数据的完整性和一致性。

**功能模块:**
- 数据验证器 (DataValidator)
- 数据去重器 (DataDeduplicator)  
- 数据修复器 (DataRepairer)
- 质量报告生成器 (QualityReporter)

```cpp
class DataQualityManager {
public:
    struct QualityMetrics {
        double completeness_ratio;      // 数据完整性比例
        double accuracy_ratio;          // 数据准确性比例
        uint64_t duplicate_count;       // 重复数据数量
        uint64_t invalid_count;         // 无效数据数量
        std::vector<std::string> quality_issues;  // 质量问题列表
    };
    
    // 数据验证
    bool ValidateTickData(const StandardTick& tick);
    bool ValidateLevel2Data(const Level2Data& level2);
    
    // 数据去重
    std::vector<StandardTick> DeduplicateTicks(
        const std::vector<StandardTick>& ticks
    );
    
    // 质量检查
    QualityMetrics CheckDataQuality(
        const std::string& symbol,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
    
    // 数据修复
    bool RepairMissingData(
        const std::string& symbol,
        int64_t start_timestamp,
        int64_t end_timestamp
    );
};
```

## 组件接口设计

### 1. 配置管理

**统一配置结构:**
```json
{
  "collection": {
    "pytdx": {
      "enabled": true,
      "servers": [...],
      "batch_size": 1000,
      "concurrent_requests": 5,
      "archive_enabled": true,
      "archive_batch_size": 5000
    },
    "ctp": {
      "enabled": true,
      "config_path": "config/ctp_config.json",
      "failover_timeout": 30
    },
    "coordination": {
      "priority_source": "ctp",
      "overlap_tolerance_seconds": 300,
      "enable_data_merge": true
    }
  },
  "storage": {
    "hot_storage": {
      "type": "redis",
      "retention_days": 7,
      "config": {...}
    },
    "warm_storage": {
      "type": "clickhouse", 
      "retention_days": 730,
      "config": {...}
    },
    "cold_storage": {
      "type": "s3",
      "config": {...}
    }
  },
  "scheduling": {
    "historical_update": {
      "cron": "0 2 * * *",
      "symbols": ["all"],
      "lookback_days": 1
    },
    "data_migration": {
      "cron": "0 3 * * *",
      "batch_size": 10000
    }
  },
  "monitoring": {
    "enable_metrics": true,
    "alert_thresholds": {
      "data_delay_seconds": 60,
      "error_rate_percent": 5.0
    }
  }
}
```

### 2. 数据模型扩展

**扩展现有的StandardTick结构:**
```cpp
struct EnhancedStandardTick : public StandardTick {
    // 数据源信息
    std::string data_source;        // pytdx, ctp
    std::string collection_method;  // realtime, historical
    int64_t collection_timestamp_ns; // 采集时间戳
    
    // 数据质量标记
    bool is_validated = false;
    bool is_deduplicated = false;
    std::vector<std::string> quality_flags;
    
    // 存储层信息
    std::string storage_layer;      // hot, warm, cold
    int64_t storage_timestamp_ns;   // 存储时间戳
    
    // 数据血缘
    std::string batch_id;           // 批次ID
    uint32_t batch_sequence;        // 批次内序号
};
```

### 3. 错误处理和监控

**错误分类和处理策略:**
```cpp
enum class ErrorType {
    NETWORK_ERROR,          // 网络连接错误
    DATA_FORMAT_ERROR,      // 数据格式错误
    STORAGE_ERROR,          // 存储错误
    VALIDATION_ERROR,       // 数据验证错误
    COORDINATION_ERROR      // 协调错误
};

class ErrorHandler {
public:
    struct ErrorPolicy {
        ErrorType type;
        int max_retries;
        std::chrono::seconds retry_delay;
        bool enable_fallback;
        std::string fallback_action;
    };
    
    void HandleError(ErrorType type, const std::string& message, 
                    const std::string& context);
    void SetErrorPolicy(ErrorType type, const ErrorPolicy& policy);
    
    // 错误统计
    struct ErrorStatistics {
        std::map<ErrorType, uint64_t> error_counts;
        std::map<ErrorType, std::chrono::system_clock::time_point> last_occurrence;
        double overall_error_rate;
    };
    
    ErrorStatistics GetErrorStatistics() const;
};
```

## 数据模型

### 1. 数据流转模型

```mermaid
sequenceDiagram
    participant P as pytdx_collector
    participant C as CTP_collector
    participant DC as DataCoordinator
    participant DQ as DataQuality
    participant SM as StorageManager
    participant R as Redis
    participant CH as ClickHouse
    
    P->>DC: Historical data
    C->>DC: Realtime data
    DC->>DQ: Coordinated data
    DQ->>DQ: Validate & Deduplicate
    DQ->>SM: Clean data
    SM->>R: Store to hot storage
    SM->>CH: Store to warm storage
    
    Note over SM: Automatic migration
    R->>CH: Migrate aged data
    CH->>S3: Archive old data
```

### 2. 存储分层策略

| 存储层 | 时间范围 | 存储介质 | 访问延迟 | 数据格式 | 压缩比例 |
|--------|----------|----------|----------|----------|----------|
| 热存储 | 0-7天 | Redis | <1ms | 原始格式 | 无压缩 |
| 温存储 | 7天-2年 | ClickHouse | <10ms | 列式存储 | 5:1 |
| 冷存储 | >2年 | S3 | <100ms | Parquet | 10:1 |

### 3. 数据一致性保证

**一致性级别:**
- **强一致性**: 实时数据写入，确保数据不丢失
- **最终一致性**: 历史数据归档，允许短暂延迟
- **读一致性**: 查询时优先返回最新数据

**冲突解决策略:**
1. 时间戳优先: 相同数据以最新时间戳为准
2. 数据源优先: CTP实时数据优先于pytdx历史数据
3. 质量优先: 经过验证的数据优先于未验证数据

## 错误处理

### 1. 错误分类

**采集错误:**
- 网络连接超时
- 数据源服务不可用
- 数据格式异常
- 认证失败

**存储错误:**
- 存储空间不足
- 数据库连接失败
- 写入超时
- 数据冲突

**协调错误:**
- 数据源冲突
- 时间同步问题
- 配置不一致

### 2. 错误恢复机制

**自动恢复:**
- 连接重试: 指数退避算法
- 数据补偿: 自动检测并补充缺失数据
- 服务降级: 在部分组件故障时继续提供基础服务

**手动干预:**
- 错误告警: 关键错误立即通知
- 状态监控: 实时显示系统健康状态
- 手动修复: 提供数据修复工具

## 测试策略

### 1. 单元测试

**测试覆盖范围:**
- 数据采集器功能测试
- 数据验证和去重逻辑测试
- 存储接口测试
- 配置管理测试

### 2. 集成测试

**测试场景:**
- 多数据源协调测试
- 存储层切换测试
- 故障转移测试
- 性能压力测试

### 3. 端到端测试

**测试流程:**
- 完整数据流测试
- 定时任务执行测试
- 监控告警测试
- 数据一致性验证

**测试数据:**
- 模拟历史数据
- 实时数据流
- 异常数据场景
- 大批量数据测试