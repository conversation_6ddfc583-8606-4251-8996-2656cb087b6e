user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 4096;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # MinIO API负载均衡
    upstream minio {
        least_conn;
        server minio1:9000;
        server minio2:9000;
        server minio3:9000;
        server minio4:9000;
    }
    
    # MinIO Console负载均衡
    upstream minio-console {
        least_conn;
        server minio1:9001;
        server minio2:9001;
        server minio3:9001;
        server minio4:9001;
    }
    
    server {
        listen 9000;
        listen [::]:9000;
        server_name localhost;
        
        # 允许大文件上传
        client_max_body_size 1000m;
        
        # 忽略客户端中断
        ignore_invalid_headers off;
        
        # 支持任意大小的分块上传
        proxy_buffering off;
        proxy_request_buffering off;
        
        location / {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 300;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            
            proxy_pass http://minio;
        }
    }
    
    server {
        listen 9001;
        listen [::]:9001;
        server_name localhost;
        
        # 允许大文件上传
        client_max_body_size 1000m;
        
        # 忽略客户端中断
        ignore_invalid_headers off;
        
        # 支持任意大小的分块上传
        proxy_buffering off;
        proxy_request_buffering off;
        
        location / {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-NginX-Proxy true;
            
            # 这对于支持websocket很重要
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
            proxy_read_timeout 300;
            send_timeout 300;
            
            proxy_pass http://minio-console;
        }
    }
}