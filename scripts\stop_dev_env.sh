#!/bin/bash

echo "停止金融数据服务开发环境..."
echo

echo "1. 停止所有开发环境容器..."
docker-compose -f docker-compose.dev.yml down

echo
echo "2. 清理未使用的网络..."
docker network prune -f

echo
echo "3. 显示剩余容器..."
docker ps -a --filter "name=financial-*-dev"

echo
echo "========================================"
echo "开发环境已停止！"
echo "========================================"
echo
echo "如需完全清理数据，请运行:"
echo "  docker-compose -f docker-compose.dev.yml down -v"
echo