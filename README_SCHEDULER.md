# 金融数据服务 - 任务调度器

Financial Data Service - Task Scheduler

## 概述

任务调度器是金融数据服务系统的核心组件之一，负责管理和执行各种定时任务，包括：

- 历史数据定时更新
- 数据质量检查
- 数据清理和归档
- 系统健康检查
- 故障恢复任务

## 功能特性

### 🚀 核心功能
- **Cron表达式调度**: 支持标准cron表达式，灵活配置任务执行时间
- **任务依赖管理**: 支持任务间的依赖关系，确保执行顺序
- **故障恢复**: 自动重试机制和故障转移
- **并发控制**: 可配置的并发任务数量限制
- **实时监控**: 任务执行状态和性能监控

### 📊 数据采集
- **多数据源支持**: 集成PyTDX等数据源
- **批量处理**: 高效的批量数据采集和处理
- **数据质量控制**: 内置数据验证和去重机制
- **增量更新**: 支持增量和全量数据更新

### 🔧 运维管理
- **多种部署方式**: 支持系统服务、Docker容器等部署方式
- **配置热更新**: 支持配置文件热更新
- **日志管理**: 详细的日志记录和轮转
- **健康检查**: 内置健康检查和告警机制

## 快速开始

### 1. 环境要求

- Python 3.8+
- Redis (可选)
- ClickHouse (可选)

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置文件

复制并修改配置文件：

```bash
cp config/scheduler_config.json config/scheduler_config.local.json
```

### 4. 启动服务

#### 方式一：直接启动
```bash
python scripts/start_scheduler.py --config config/scheduler_config.local.json
```

#### 方式二：使用启动脚本 (Linux/macOS)
```bash
./scripts/start_scheduler.sh --config config/scheduler_config.local.json
```

#### 方式三：使用启动脚本 (Windows)
```cmd
scripts\start_scheduler.bat --config config\scheduler_config.local.json
```

## 配置说明

### 基本配置

```json
{
  "scheduler": {
    "max_concurrent_tasks": 5,
    "task_timeout_seconds": 3600,
    "health_check_interval": 30
  }
}
```

### 任务配置

```json
{
  "tasks": {
    "daily_data_update": {
      "enabled": true,
      "cron": "0 18 * * 1-5",
      "description": "工作日收盘后更新当日数据",
      "symbols": ["000001", "000002"],
      "priority": "HIGH",
      "max_retries": 3,
      "timeout_seconds": 1800
    }
  }
}
```

### 数据源配置

```json
{
  "pytdx": {
    "batch_size": 800,
    "concurrent_requests": 5,
    "archive_enabled": true
  }
}
```

## 任务类型

### 1. 历史数据更新 (HISTORICAL_UPDATE)
- **用途**: 更新历史K线数据
- **执行时间**: 通常在收盘后
- **配置示例**:
```json
{
  "cron": "0 18 * * 1-5",
  "symbols": ["000001", "000002"],
  "parameters": {
    "data_types": ["kline_D", "kline_60"],
    "update_days": 5
  }
}
```

### 2. 增量数据更新 (INCREMENTAL_UPDATE)
- **用途**: 实时或准实时数据更新
- **执行时间**: 交易时间内定期执行
- **配置示例**:
```json
{
  "cron": "*/30 9-15 * * 1-5",
  "parameters": {
    "data_types": ["kline_5", "kline_15"],
    "realtime": true
  }
}
```

### 3. 数据质量检查 (QUALITY_CHECK)
- **用途**: 检查数据完整性和准确性
- **执行时间**: 每日凌晨
- **配置示例**:
```json
{
  "cron": "0 2 * * *",
  "parameters": {
    "check_days": 7,
    "quality_threshold": 0.95
  }
}
```

### 4. 数据清理 (DATA_CLEANUP)
- **用途**: 清理过期和无效数据
- **执行时间**: 每周执行
- **配置示例**:
```json
{
  "cron": "0 3 * * 0",
  "parameters": {
    "cleanup_types": ["expired_cache", "duplicate_data"],
    "retention_days": 90
  }
}
```

### 5. 系统健康检查 (HEALTH_CHECK)
- **用途**: 检查系统组件健康状态
- **执行时间**: 每5分钟
- **配置示例**:
```json
{
  "cron": "*/5 * * * *",
  "parameters": {
    "check_components": ["pytdx", "storage", "scheduler"]
  }
}
```

## 部署方式

### 1. 系统服务部署

#### 自动安装
```bash
sudo ./deployment/install.sh
```

#### 手动安装
```bash
# 创建用户
sudo useradd -r -s /bin/false financial-data

# 复制文件
sudo cp -r . /opt/financial-data-service
sudo chown -R financial-data:financial-data /opt/financial-data-service

# 安装systemd服务
sudo cp deployment/systemd/financial-data-scheduler.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable financial-data-scheduler
sudo systemctl start financial-data-scheduler
```

#### 服务管理
```bash
# 启动服务
sudo systemctl start financial-data-scheduler

# 停止服务
sudo systemctl stop financial-data-scheduler

# 重启服务
sudo systemctl restart financial-data-scheduler

# 查看状态
sudo systemctl status financial-data-scheduler

# 查看日志
sudo journalctl -u financial-data-scheduler -f
```

### 2. Docker部署

#### 构建镜像
```bash
docker build -f deployment/docker/scheduler.Dockerfile -t financial-data-scheduler:latest .
```

#### 使用Docker Compose
```bash
cd deployment/docker
docker-compose -f docker-compose.scheduler.yml up -d
```

#### 管理容器
```bash
# 查看状态
docker-compose -f docker-compose.scheduler.yml ps

# 查看日志
docker-compose -f docker-compose.scheduler.yml logs -f scheduler

# 停止服务
docker-compose -f docker-compose.scheduler.yml down
```

### 3. 开发模式

```bash
# 前台运行
python scripts/start_scheduler.py --log-level DEBUG

# 后台运行 (Linux/macOS)
./scripts/start_scheduler.sh --daemon

# 查看状态
./scripts/start_scheduler.sh --status

# 停止服务
./scripts/start_scheduler.sh --stop
```

## 监控和管理

### 1. 任务状态查询

```python
from services.scheduler_service import SchedulerService

service = SchedulerService()
status = service.get_status()
print(status)
```

### 2. 日志查看

```bash
# 查看调度器日志
tail -f logs/scheduler_service.log

# 查看系统日志 (systemd)
journalctl -u financial-data-scheduler -f

# 查看Docker日志
docker logs -f financial-data-scheduler
```

### 3. 性能监控

调度器提供以下监控指标：

- 任务执行次数和成功率
- 任务执行时间统计
- 系统资源使用情况
- 数据采集量统计

### 4. 告警配置

```json
{
  "monitoring": {
    "alert_channels": ["log", "email"],
    "performance_thresholds": {
      "task_duration_warning": 1800,
      "task_duration_critical": 3600,
      "failure_rate_warning": 0.1,
      "failure_rate_critical": 0.2
    }
  }
}
```

## 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查日志
journalctl -u financial-data-scheduler -n 50

# 检查配置文件
python -m json.tool config/scheduler_config.json

# 检查Python环境
python -c "import asyncio, croniter, redis"
```

#### 2. 任务执行失败
```bash
# 查看任务执行历史
python scripts/start_scheduler.py --status

# 检查数据源连接
python -c "from collectors.pytdx_collector import PyTDXCollector; print('PyTDX OK')"
```

#### 3. 内存使用过高
- 调整 `max_concurrent_tasks` 参数
- 增加 `batch_size` 以减少请求次数
- 启用数据缓存以减少重复请求

#### 4. 任务执行超时
- 增加 `timeout_seconds` 参数
- 检查网络连接和数据源响应时间
- 优化任务逻辑，减少处理时间

### 调试模式

```bash
# 启用调试日志
python scripts/start_scheduler.py --log-level DEBUG

# 单次执行任务（不使用调度器）
python -c "
from services.scheduler_service import SchedulerTaskExecutor
from collectors.pytdx_collector import PyTDXCollector
import asyncio

async def test():
    collector = PyTDXCollector()
    executor = SchedulerTaskExecutor(collector, None)
    # 执行测试任务
    
asyncio.run(test())
"
```

## API参考

### SchedulerService类

```python
class SchedulerService:
    async def initialize() -> bool
    async def start()
    async def shutdown()
    def get_status() -> Dict[str, Any]
```

### 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `max_concurrent_tasks` | int | 5 | 最大并发任务数 |
| `task_timeout_seconds` | int | 3600 | 任务超时时间(秒) |
| `health_check_interval` | int | 30 | 健康检查间隔(秒) |
| `batch_size` | int | 800 | 数据批量大小 |
| `max_retries` | int | 3 | 最大重试次数 |

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 支持

如有问题或建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索 [Issues](https://github.com/your-org/financial-data-service/issues)
3. 创建新的 Issue
4. 联系维护团队

---

**金融数据服务团队**  
Financial Data Service Team