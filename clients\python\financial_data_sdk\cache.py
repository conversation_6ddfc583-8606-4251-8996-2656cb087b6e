"""
Data caching system for financial data with LRU and time-based expiration
"""

import asyncio
import time
import threading
from typing import Any, Optional, Dict, List
from collections import OrderedDict
import pickle
import hashlib
import logging

logger = logging.getLogger(__name__)


class DataCache:
    """
    Thread-safe LRU cache with time-based expiration for financial data
    """
    
    def __init__(self, max_size: int = 10000, ttl_seconds: int = 300):
        """
        Initialize data cache
        
        Args:
            max_size: Maximum number of cached items
            ttl_seconds: Time-to-live for cached items in seconds
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache = OrderedDict()
        self.timestamps = {}
        self.lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
    
    def _is_expired(self, key: str) -> bool:
        """Check if cache entry is expired"""
        if key not in self.timestamps:
            return True
        return time.time() - self.timestamps[key] > self.ttl_seconds
    
    def _evict_expired(self):
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.timestamps.items()
            if current_time - timestamp > self.ttl_seconds
        ]
        
        for key in expired_keys:
            self.cache.pop(key, None)
            self.timestamps.pop(key, None)
    
    def _evict_lru(self):
        """Remove least recently used entries to maintain max_size"""
        while len(self.cache) >= self.max_size:
            oldest_key = next(iter(self.cache))
            self.cache.pop(oldest_key)
            self.timestamps.pop(oldest_key, None)
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get item from cache
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        with self.lock:
            if key in self.cache and not self._is_expired(key):
                # Move to end (most recently used)
                value = self.cache.pop(key)
                self.cache[key] = value
                self.hit_count += 1
                return value
            else:
                # Remove expired entry
                if key in self.cache:
                    self.cache.pop(key)
                    self.timestamps.pop(key, None)
                self.miss_count += 1
                return None
    
    def put(self, key: str, value: Any):
        """
        Put item in cache
        
        Args:
            key: Cache key
            value: Value to cache
        """
        with self.lock:
            # Clean up expired entries periodically
            if len(self.cache) % 100 == 0:
                self._evict_expired()
            
            # Evict LRU entries if needed
            self._evict_lru()
            
            # Add new entry
            self.cache[key] = value
            self.timestamps[key] = time.time()
    
    def delete(self, key: str) -> bool:
        """
        Delete item from cache
        
        Args:
            key: Cache key
            
        Returns:
            True if item was deleted, False if not found
        """
        with self.lock:
            if key in self.cache:
                self.cache.pop(key)
                self.timestamps.pop(key, None)
                return True
            return False
    
    def clear(self):
        """Clear all cache entries"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()
            self.hit_count = 0
            self.miss_count = 0
    
    def size(self) -> int:
        """Get current cache size"""
        with self.lock:
            return len(self.cache)
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_count': self.hit_count,
                'miss_count': self.miss_count,
                'hit_rate': hit_rate,
                'ttl_seconds': self.ttl_seconds
            }
    
    def keys(self) -> List[str]:
        """Get all cache keys"""
        with self.lock:
            return list(self.cache.keys())


class AsyncDataCache:
    """
    Async version of DataCache for use with asyncio
    """
    
    def __init__(self, max_size: int = 10000, ttl_seconds: int = 300):
        """
        Initialize async data cache
        
        Args:
            max_size: Maximum number of cached items
            ttl_seconds: Time-to-live for cached items in seconds
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache = OrderedDict()
        self.timestamps = {}
        self.lock = asyncio.Lock()
        self.hit_count = 0
        self.miss_count = 0
    
    def _is_expired(self, key: str) -> bool:
        """Check if cache entry is expired"""
        if key not in self.timestamps:
            return True
        return time.time() - self.timestamps[key] > self.ttl_seconds
    
    def _evict_expired(self):
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.timestamps.items()
            if current_time - timestamp > self.ttl_seconds
        ]
        
        for key in expired_keys:
            self.cache.pop(key, None)
            self.timestamps.pop(key, None)
    
    def _evict_lru(self):
        """Remove least recently used entries to maintain max_size"""
        while len(self.cache) >= self.max_size:
            oldest_key = next(iter(self.cache))
            self.cache.pop(oldest_key)
            self.timestamps.pop(oldest_key, None)
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Async get item from cache
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        async with self.lock:
            if key in self.cache and not self._is_expired(key):
                # Move to end (most recently used)
                value = self.cache.pop(key)
                self.cache[key] = value
                self.hit_count += 1
                return value
            else:
                # Remove expired entry
                if key in self.cache:
                    self.cache.pop(key)
                    self.timestamps.pop(key, None)
                self.miss_count += 1
                return None
    
    async def put(self, key: str, value: Any):
        """
        Async put item in cache
        
        Args:
            key: Cache key
            value: Value to cache
        """
        async with self.lock:
            # Clean up expired entries periodically
            if len(self.cache) % 100 == 0:
                self._evict_expired()
            
            # Evict LRU entries if needed
            self._evict_lru()
            
            # Add new entry
            self.cache[key] = value
            self.timestamps[key] = time.time()
    
    async def delete(self, key: str) -> bool:
        """
        Async delete item from cache
        
        Args:
            key: Cache key
            
        Returns:
            True if item was deleted, False if not found
        """
        async with self.lock:
            if key in self.cache:
                self.cache.pop(key)
                self.timestamps.pop(key, None)
                return True
            return False
    
    async def clear(self):
        """Async clear all cache entries"""
        async with self.lock:
            self.cache.clear()
            self.timestamps.clear()
            self.hit_count = 0
            self.miss_count = 0
    
    async def size(self) -> int:
        """Async get current cache size"""
        async with self.lock:
            return len(self.cache)
    
    async def stats(self) -> Dict[str, Any]:
        """Async get cache statistics"""
        async with self.lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_count': self.hit_count,
                'miss_count': self.miss_count,
                'hit_rate': hit_rate,
                'ttl_seconds': self.ttl_seconds
            }


class PersistentCache:
    """
    Persistent cache that saves data to disk
    """
    
    def __init__(self, cache_dir: str = "./cache", max_size: int = 10000):
        """
        Initialize persistent cache
        
        Args:
            cache_dir: Directory to store cache files
            max_size: Maximum number of cached items in memory
        """
        import os
        self.cache_dir = cache_dir
        self.memory_cache = DataCache(max_size)
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_file_path(self, key: str) -> str:
        """Get file path for cache key"""
        import os
        # Create a safe filename from the key
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{safe_key}.cache")
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get item from cache (memory first, then disk)
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        # Try memory cache first
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # Try disk cache
        try:
            file_path = self._get_file_path(key)
            with open(file_path, 'rb') as f:
                value = pickle.load(f)
            
            # Put back in memory cache
            self.memory_cache.put(key, value)
            return value
            
        except (FileNotFoundError, pickle.PickleError, EOFError):
            return None
    
    def put(self, key: str, value: Any):
        """
        Put item in cache (both memory and disk)
        
        Args:
            key: Cache key
            value: Value to cache
        """
        # Put in memory cache
        self.memory_cache.put(key, value)
        
        # Save to disk
        try:
            file_path = self._get_file_path(key)
            with open(file_path, 'wb') as f:
                pickle.dump(value, f)
        except Exception as e:
            logger.error(f"Failed to save cache to disk: {e}")
    
    def delete(self, key: str) -> bool:
        """
        Delete item from cache (both memory and disk)
        
        Args:
            key: Cache key
            
        Returns:
            True if item was deleted, False if not found
        """
        import os
        
        # Delete from memory
        memory_deleted = self.memory_cache.delete(key)
        
        # Delete from disk
        disk_deleted = False
        try:
            file_path = self._get_file_path(key)
            if os.path.exists(file_path):
                os.remove(file_path)
                disk_deleted = True
        except Exception as e:
            logger.error(f"Failed to delete cache file: {e}")
        
        return memory_deleted or disk_deleted
    
    def clear(self):
        """Clear all cache entries (memory and disk)"""
        import os
        import glob
        
        # Clear memory cache
        self.memory_cache.clear()
        
        # Clear disk cache
        try:
            cache_files = glob.glob(os.path.join(self.cache_dir, "*.cache"))
            for file_path in cache_files:
                os.remove(file_path)
        except Exception as e:
            logger.error(f"Failed to clear disk cache: {e}")
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        import os
        import glob
        
        memory_stats = self.memory_cache.stats()
        
        # Count disk cache files
        try:
            cache_files = glob.glob(os.path.join(self.cache_dir, "*.cache"))
            disk_size = len(cache_files)
            
            # Calculate total disk usage
            total_disk_size = sum(
                os.path.getsize(f) for f in cache_files
                if os.path.exists(f)
            )
        except Exception:
            disk_size = 0
            total_disk_size = 0
        
        return {
            **memory_stats,
            'disk_entries': disk_size,
            'disk_size_bytes': total_disk_size,
            'cache_dir': self.cache_dir
        }


class BatchCache:
    """
    Specialized cache for batch operations
    """
    
    def __init__(self, base_cache: DataCache):
        """
        Initialize batch cache
        
        Args:
            base_cache: Underlying cache implementation
        """
        self.base_cache = base_cache
        self.batch_operations = []
        self.in_batch = False
    
    def begin_batch(self):
        """Begin batch operation"""
        self.in_batch = True
        self.batch_operations = []
    
    def put(self, key: str, value: Any):
        """Put item in batch"""
        if self.in_batch:
            self.batch_operations.append(('put', key, value))
        else:
            self.base_cache.put(key, value)
    
    def delete(self, key: str):
        """Delete item in batch"""
        if self.in_batch:
            self.batch_operations.append(('delete', key, None))
        else:
            self.base_cache.delete(key)
    
    def commit_batch(self):
        """Commit all batch operations"""
        if not self.in_batch:
            return
        
        for operation, key, value in self.batch_operations:
            if operation == 'put':
                self.base_cache.put(key, value)
            elif operation == 'delete':
                self.base_cache.delete(key)
        
        self.batch_operations = []
        self.in_batch = False
    
    def rollback_batch(self):
        """Rollback batch operations"""
        self.batch_operations = []
        self.in_batch = False
    
    def get(self, key: str) -> Optional[Any]:
        """Get item (always from base cache)"""
        return self.base_cache.get(key)


# Utility functions
def create_cache_key(*args, **kwargs) -> str:
    """
    Create a cache key from arguments
    
    Args:
        *args: Positional arguments
        **kwargs: Keyword arguments
        
    Returns:
        Cache key string
    """
    key_parts = []
    
    # Add positional arguments
    for arg in args:
        if arg is not None:
            key_parts.append(str(arg))
    
    # Add keyword arguments (sorted for consistency)
    for key, value in sorted(kwargs.items()):
        if value is not None:
            key_parts.append(f"{key}={value}")
    
    return ":".join(key_parts)


def cache_decorator(cache: DataCache, ttl: Optional[int] = None):
    """
    Decorator for caching function results
    
    Args:
        cache: Cache instance to use
        ttl: Time-to-live override
        
    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Create cache key
            key = f"{func.__name__}:{create_cache_key(*args, **kwargs)}"
            
            # Try to get from cache
            result = cache.get(key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.put(key, result)
            
            return result
        
        return wrapper
    return decorator