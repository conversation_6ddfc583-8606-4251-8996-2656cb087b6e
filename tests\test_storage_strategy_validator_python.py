"""
Tests for storage strategy configuration validator (Python implementation)
"""

import unittest
import sys
import os
import json
from unittest.mock import Mock, patch

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src', 'config'))

from storage_strategy_validator_python import StorageStrategyValidator
from config_manager_python import ValidationResult


class TestStorageStrategyValidator(unittest.TestCase):
    """测试存储策略验证器"""
    
    def setUp(self):
        """设置测试环境"""
        self.validator = StorageStrategyValidator()
    
    def test_valid_configuration(self):
        """测试有效配置"""
        valid_config = {
            "storage": {
                "strategy": {
                    "selection_strategy": "time_based",
                    "enable_automatic_failover": True,
                    "enable_load_balancing": False,
                    "health_check_interval_seconds": 30,
                    "max_consecutive_failures": 3,
                    "load_balance_threshold": 0.8,
                    "thresholds": {
                        "hot_storage_days": 7,
                        "warm_storage_days": 730,
                        "max_response_time_ms": 1000.0,
                        "min_success_rate": 0.90,
                        "health_threshold_success_rate": 0.95,
                        "degraded_threshold_success_rate": 0.80
                    },
                    "data_type_configs": {
                        "tick": {
                            "hot_storage_days": 7,
                            "warm_storage_days": 365,
                            "priority_storage": "hot",
                            "compression_enabled": False,
                            "batch_size": 1000,
                            "max_response_time_ms": 100.0
                        },
                        "kline": {
                            "hot_storage_days": 30,
                            "warm_storage_days": 1095,
                            "priority_storage": "warm",
                            "compression_enabled": True,
                            "batch_size": 5000,
                            "max_response_time_ms": 500.0
                        }
                    },
                    "migration_policies": {
                        "tick": {
                            "hot_to_warm_hours": 168,
                            "warm_to_cold_days": 365,
                            "auto_migration": True,
                            "migration_batch_size": 50000,
                            "migration_schedule": "0 2 * * *"
                        },
                        "kline": {
                            "hot_to_warm_hours": 720,
                            "warm_to_cold_days": 1095,
                            "auto_migration": True,
                            "migration_batch_size": 100000,
                            "migration_schedule": "0 3 * * *"
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(valid_config)
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.errors), 0)
    
    def test_missing_storage_section(self):
        """测试缺少storage配置节"""
        invalid_config = {
            "other_section": {}
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("Missing 'storage' configuration section", result.errors[0])
    
    def test_missing_strategy_section(self):
        """测试缺少strategy配置节"""
        config_without_strategy = {
            "storage": {
                "hot_storage": {}
            }
        }
        
        result = self.validator.validate(config_without_strategy)
        self.assertTrue(result.is_valid)  # 应该只是警告，不是错误
        self.assertGreater(len(result.warnings), 0)
        self.assertIn("Missing 'strategy' configuration", result.warnings[0])
    
    def test_invalid_selection_strategy(self):
        """测试无效的选择策略"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "selection_strategy": "invalid_strategy"
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("Invalid selection_strategy", result.errors[0])
    
    def test_invalid_boolean_config(self):
        """测试无效的布尔配置"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "enable_automatic_failover": "not_a_boolean"
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("enable_automatic_failover must be a boolean value", result.errors[0])
    
    def test_invalid_numeric_config(self):
        """测试无效的数值配置"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "health_check_interval_seconds": "not_a_number"
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("health_check_interval_seconds must be a numeric value", result.errors[0])
    
    def test_out_of_range_numeric_config(self):
        """测试超出范围的数值配置"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "health_check_interval_seconds": 5000  # 超出范围
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("health_check_interval_seconds must be between", result.errors[0])
    
    def test_invalid_thresholds(self):
        """测试无效的阈值配置"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "thresholds": {
                        "hot_storage_days": 100,
                        "warm_storage_days": 50  # 小于hot_storage_days
                    }
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("hot_storage_days must be less than warm_storage_days", result.errors[0])
    
    def test_invalid_success_rate_thresholds(self):
        """测试无效的成功率阈值配置"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "thresholds": {
                        "health_threshold_success_rate": 0.80,
                        "degraded_threshold_success_rate": 0.90  # 大于health_threshold
                    }
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("health_threshold_success_rate must be greater than degraded_threshold_success_rate", result.errors[0])
    
    def test_invalid_data_type_config_structure(self):
        """测试无效的数据类型配置结构"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "data_type_configs": "not_an_object"
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("data_type_configs must be an object", result.errors[0])
    
    def test_unknown_data_type(self):
        """测试未知数据类型"""
        config_with_unknown_type = {
            "storage": {
                "strategy": {
                    "data_type_configs": {
                        "unknown_type": {
                            "hot_storage_days": 7,
                            "warm_storage_days": 365,
                            "priority_storage": "hot"
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(config_with_unknown_type)
        self.assertTrue(result.is_valid)  # 应该只是警告
        self.assertGreater(len(result.warnings), 0)
        self.assertIn("Unknown data type: unknown_type", result.warnings[0])
    
    def test_missing_required_data_type_fields(self):
        """测试缺少必需的数据类型字段"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "data_type_configs": {
                        "tick": {
                            "hot_storage_days": 7
                            # 缺少warm_storage_days和priority_storage
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
        self.assertTrue(any("warm_storage_days is required" in error for error in result.errors))
        self.assertTrue(any("priority_storage is required" in error for error in result.errors))
    
    def test_invalid_priority_storage(self):
        """测试无效的优先存储层"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "data_type_configs": {
                        "tick": {
                            "hot_storage_days": 7,
                            "warm_storage_days": 365,
                            "priority_storage": "invalid_layer"
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("priority_storage must be one of: hot, warm, cold", result.errors[0])
    
    def test_invalid_data_type_time_consistency(self):
        """测试数据类型时间配置一致性"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "data_type_configs": {
                        "tick": {
                            "hot_storage_days": 100,
                            "warm_storage_days": 50,  # 小于hot_storage_days
                            "priority_storage": "hot"
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("hot_storage_days must be less than warm_storage_days", result.errors[0])
    
    def test_invalid_migration_policy_structure(self):
        """测试无效的迁移策略结构"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "migration_policies": "not_an_object"
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("migration_policies must be an object", result.errors[0])
    
    def test_missing_required_migration_fields(self):
        """测试缺少必需的迁移策略字段"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "migration_policies": {
                        "tick": {
                            "hot_to_warm_hours": 168
                            # 缺少warm_to_cold_days和auto_migration
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
        self.assertTrue(any("warm_to_cold_days is required" in error for error in result.errors))
        self.assertTrue(any("auto_migration is required" in error for error in result.errors))
    
    def test_invalid_migration_time_consistency(self):
        """测试迁移策略时间配置一致性"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "migration_policies": {
                        "tick": {
                            "hot_to_warm_hours": 1000,
                            "warm_to_cold_days": 1,  # 转换为小时后小于hot_to_warm_hours
                            "auto_migration": True
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("hot_to_warm_hours must be less than warm_to_cold_days", result.errors[0])
    
    def test_invalid_cron_expression(self):
        """测试无效的cron表达式"""
        invalid_config = {
            "storage": {
                "strategy": {
                    "migration_policies": {
                        "tick": {
                            "hot_to_warm_hours": 168,
                            "warm_to_cold_days": 365,
                            "auto_migration": True,
                            "migration_schedule": "invalid cron expression"
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        self.assertFalse(result.is_valid)
        self.assertIn("migration_schedule contains invalid cron expression", result.errors[0])
    
    def test_valid_cron_expressions(self):
        """测试有效的cron表达式"""
        valid_cron_expressions = [
            "0 2 * * *",      # 每天2点
            "*/15 * * * *",   # 每15分钟
            "0 0 1 * *",      # 每月1号
            "0 0 * * 0",      # 每周日
            "30 14 * * 1-5"   # 工作日14:30
        ]
        
        for cron_expr in valid_cron_expressions:
            with self.subTest(cron_expr=cron_expr):
                self.assertTrue(self.validator._validate_cron_expression(cron_expr))
    
    def test_invalid_cron_expressions(self):
        """测试无效的cron表达式"""
        invalid_cron_expressions = [
            "invalid",
            "0 25 * * *",     # 无效小时
            "60 * * * *",     # 无效分钟
            "0 0 32 * *",     # 无效日期
            "0 0 * 13 *",     # 无效月份
            "0 0 * * 8"       # 无效星期
        ]
        
        for cron_expr in invalid_cron_expressions:
            with self.subTest(cron_expr=cron_expr):
                self.assertFalse(self.validator._validate_cron_expression(cron_expr))
    
    def test_time_consistency_warnings(self):
        """测试时间配置一致性警告"""
        config_with_inconsistency = {
            "storage": {
                "strategy": {
                    "thresholds": {
                        "hot_storage_days": 7,
                        "warm_storage_days": 730
                    },
                    "data_type_configs": {
                        "tick": {
                            "hot_storage_days": 30,  # 与全局配置差异较大
                            "warm_storage_days": 365,
                            "priority_storage": "hot"
                        }
                    },
                    "migration_policies": {
                        "tick": {
                            "hot_to_warm_hours": 1000,  # 与存储配置不匹配
                            "warm_to_cold_days": 365,
                            "auto_migration": True,
                            "migration_schedule": "0 2 * * *"
                        }
                    }
                }
            }
        }
        
        result = self.validator.validate(config_with_inconsistency)
        self.assertTrue(result.is_valid)  # 应该是有效的，但有警告
        self.assertGreater(len(result.warnings), 0)
        
        # 检查是否有时间一致性警告
        warning_messages = " ".join(result.warnings)
        self.assertIn("differs significantly from global setting", warning_messages)
        self.assertIn("much larger than hot_storage_days", warning_messages)
    
    def test_validator_name(self):
        """测试验证器名称"""
        self.assertEqual(self.validator.get_validator_name(), "StorageStrategyValidator")
    
    def test_supported_data_types(self):
        """测试支持的数据类型"""
        expected_types = {"tick", "kline", "level2", "fundamental", "news", "announcement"}
        self.assertEqual(self.validator.SUPPORTED_DATA_TYPES, expected_types)
    
    def test_supported_storage_layers(self):
        """测试支持的存储层"""
        expected_layers = {"hot", "warm", "cold"}
        self.assertEqual(self.validator.SUPPORTED_STORAGE_LAYERS, expected_layers)
    
    def test_supported_selection_strategies(self):
        """测试支持的选择策略"""
        expected_strategies = {"time_based", "performance_based", "load_balanced", "failover_only"}
        self.assertEqual(self.validator.SUPPORTED_SELECTION_STRATEGIES, expected_strategies)
    
    def test_numeric_range_validation(self):
        """测试数值范围验证"""
        # 测试有效范围
        self.assertTrue(self.validator._validate_numeric_range(5.0, 1.0, 10.0))
        self.assertTrue(self.validator._validate_numeric_range(1.0, 1.0, 10.0))  # 边界值
        self.assertTrue(self.validator._validate_numeric_range(10.0, 1.0, 10.0))  # 边界值
        
        # 测试无效范围
        self.assertFalse(self.validator._validate_numeric_range(0.5, 1.0, 10.0))
        self.assertFalse(self.validator._validate_numeric_range(15.0, 1.0, 10.0))
    
    def test_storage_layer_validation(self):
        """测试存储层验证"""
        # 测试有效存储层
        self.assertTrue(self.validator._validate_storage_layer("hot"))
        self.assertTrue(self.validator._validate_storage_layer("warm"))
        self.assertTrue(self.validator._validate_storage_layer("cold"))
        
        # 测试无效存储层
        self.assertFalse(self.validator._validate_storage_layer("invalid"))
        self.assertFalse(self.validator._validate_storage_layer(""))
    
    def test_selection_strategy_validation(self):
        """测试选择策略验证"""
        # 测试有效策略
        self.assertTrue(self.validator._validate_selection_strategy("time_based"))
        self.assertTrue(self.validator._validate_selection_strategy("performance_based"))
        self.assertTrue(self.validator._validate_selection_strategy("load_balanced"))
        self.assertTrue(self.validator._validate_selection_strategy("failover_only"))
        
        # 测试无效策略
        self.assertFalse(self.validator._validate_selection_strategy("invalid"))
        self.assertFalse(self.validator._validate_selection_strategy(""))


if __name__ == '__main__':
    unittest.main()