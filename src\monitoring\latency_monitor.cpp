#include "latency_monitor.h"
#include "alert_manager.h"
#include "prometheus_metrics.h"
#include <iostream>
#include <sstream>
#include <iomanip>

namespace monitoring {

LatencyMonitor::LatencyMonitor(std::shared_ptr<AlertManager> alert_manager)
    : alert_manager_(alert_manager) {
}

LatencyMonitor::~LatencyMonitor() {
    stop();
}

bool LatencyMonitor::start() {
    if (running_.load()) {
        return true;
    }
    
    running_.store(true);
    processing_thread_ = std::thread(&LatencyMonitor::processingLoop, this);
    
    std::cout << "Latency monitor started with threshold: " 
              << latency_threshold_microseconds_.load() << " microseconds" << std::endl;
    return true;
}

void LatencyMonitor::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    queue_cv_.notify_all();
    
    if (processing_thread_.joinable()) {
        processing_thread_.join();
    }
    
    std::cout << "Latency monitor stopped" << std::endl;
}

void LatencyMonitor::recordLatency(const LatencyMeasurement& measurement) {
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        measurement_queue_.push(measurement);
    }
    queue_cv_.notify_one();
}

uint64_t LatencyMonitor::startMeasurement(const std::string& operation, const std::string& symbol) {
    uint64_t measurement_id = next_measurement_id_.fetch_add(1);
    
    ActiveMeasurement active_measurement;
    active_measurement.operation = operation;
    active_measurement.symbol = symbol;
    active_measurement.start_time = std::chrono::high_resolution_clock::now();
    
    {
        std::lock_guard<std::mutex> lock(active_measurements_mutex_);
        active_measurements_[measurement_id] = active_measurement;
    }
    
    return measurement_id;
}

void LatencyMonitor::endMeasurement(uint64_t measurement_id) {
    auto end_time = std::chrono::high_resolution_clock::now();
    
    ActiveMeasurement active_measurement;
    bool found = false;
    
    {
        std::lock_guard<std::mutex> lock(active_measurements_mutex_);
        auto it = active_measurements_.find(measurement_id);
        if (it != active_measurements_.end()) {
            active_measurement = it->second;
            active_measurements_.erase(it);
            found = true;
        }
    }
    
    if (found) {
        LatencyMeasurement measurement;
        measurement.operation = active_measurement.operation;
        measurement.symbol = active_measurement.symbol;
        measurement.start_time = active_measurement.start_time;
        measurement.end_time = end_time;
        measurement.latency_microseconds = calculateMicroseconds(
            active_measurement.start_time, end_time);
        measurement.sequence_id = measurement_id;
        
        recordLatency(measurement);
    }
}

void LatencyMonitor::processingLoop() {
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        queue_cv_.wait(lock, [this] { 
            return !measurement_queue_.empty() || !running_.load(); 
        });
        
        while (!measurement_queue_.empty() && running_.load()) {
            LatencyMeasurement measurement = measurement_queue_.front();
            measurement_queue_.pop();
            lock.unlock();
            
            processLatencyMeasurement(measurement);
            
            lock.lock();
        }
    }
}

void LatencyMonitor::processLatencyMeasurement(const LatencyMeasurement& measurement) {
    // Update statistics
    updateStatistics(measurement.latency_microseconds);
    
    // Record to Prometheus
    auto& metrics = PrometheusMetrics::getInstance();
    metrics.recordLatency(measurement.operation, measurement.latency_microseconds);
    
    if (measurement.operation == "end_to_end") {
        metrics.recordEndToEndLatency(measurement.latency_microseconds);
    }
    
    // Check for threshold violations
    checkThresholdViolation(measurement);
}

void LatencyMonitor::updateStatistics(double latency_microseconds) {
    total_measurements_.fetch_add(1);
    
    // Update max latency
    double current_max = max_latency_.load();
    while (latency_microseconds > current_max && 
           !max_latency_.compare_exchange_weak(current_max, latency_microseconds)) {
        // Retry if another thread updated max_latency_
    }
    
    // Update average latency using exponential moving average
    double current_avg = average_latency_.load();
    double alpha = 0.1; // Smoothing factor
    double new_avg = alpha * latency_microseconds + (1.0 - alpha) * current_avg;
    average_latency_.store(new_avg);
}

void LatencyMonitor::checkThresholdViolation(const LatencyMeasurement& measurement) {
    if (measurement.latency_microseconds > latency_threshold_microseconds_.load()) {
        violation_count_.fetch_add(1);
        sendLatencyAlert(measurement);
    }
}

void LatencyMonitor::sendLatencyAlert(const LatencyMeasurement& measurement) {
    std::lock_guard<std::mutex> lock(alert_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    if (now - last_alert_time_ < alert_cooldown_) {
        return; // Still in cooldown period
    }
    
    last_alert_time_ = now;
    
    // Create alert message
    std::ostringstream message;
    message << std::fixed << std::setprecision(2);
    message << "Latency threshold violation detected!\n";
    message << "Operation: " << measurement.operation << "\n";
    message << "Symbol: " << measurement.symbol << "\n";
    message << "Latency: " << measurement.latency_microseconds << " μs\n";
    message << "Threshold: " << latency_threshold_microseconds_.load() << " μs\n";
    message << "Sequence ID: " << measurement.sequence_id;
    
    // Send alert
    if (alert_manager_) {
        alert_manager_->sendAlert(
            "latency_violation",
            "CRITICAL",
            message.str(),
            {
                {"operation", measurement.operation},
                {"symbol", measurement.symbol},
                {"latency_microseconds", std::to_string(measurement.latency_microseconds)},
                {"threshold_microseconds", std::to_string(latency_threshold_microseconds_.load())}
            }
        );
    }
    
    // Record alert metric
    auto& metrics = PrometheusMetrics::getInstance();
    metrics.incrementAlert("latency_violation", "critical");
    
    std::cout << "ALERT: " << message.str() << std::endl;
}

double LatencyMonitor::calculateMicroseconds(
    const std::chrono::high_resolution_clock::time_point& start,
    const std::chrono::high_resolution_clock::time_point& end) {
    
    auto duration = end - start;
    return std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count() / 1000.0;
}

} // namespace monitoring