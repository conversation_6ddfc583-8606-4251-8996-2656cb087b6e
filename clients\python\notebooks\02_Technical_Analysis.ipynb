{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Financial Data SDK - Technical Analysis\n", "\n", "This notebook demonstrates advanced technical analysis capabilities using the Financial Data SDK.\n", "\n", "## Features Covered\n", "- Comprehensive technical indicators\n", "- Signal generation and backtesting\n", "- Multi-timeframe analysis\n", "- Custom indicator development\n", "- Performance visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import Financial Data SDK\n", "from financial_data_sdk.indicators import (\n", "    TechnicalIndicators, \n", "    Indicator<PERSON><PERSON><PERSON><PERSON>, \n", "    calculate_all_indicators\n", ")\n", "from financial_data_sdk.utils import DataConverter\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✓ Libraries imported successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Generate Sample Market Data\n", "\n", "Create realistic market data for analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate realistic market data\n", "np.random.seed(42)\n", "\n", "# Parameters\n", "n_periods = 500\n", "start_date = datetime.now() - <PERSON><PERSON><PERSON>(days=n_periods//24)\n", "dates = pd.date_range(start_date, periods=n_periods, freq='1H')\n", "\n", "# Generate price data with trend and volatility\n", "base_price = 100.0\n", "trend = 0.0001  # Small upward trend\n", "volatility = 0.02\n", "\n", "# Generate returns with some autocorrelation\n", "returns = np.random.normal(trend, volatility, n_periods)\n", "for i in range(1, len(returns)):\n", "    returns[i] += 0.1 * returns[i-1]  # Add some momentum\n", "\n", "# Calculate prices\n", "prices = [base_price]\n", "for ret in returns[1:]:\n", "    prices.append(prices[-1] * (1 + ret))\n", "\n", "# Generate OHLC data\n", "ohlc_data = []\n", "for i, price in enumerate(prices):\n", "    # Add some intraday volatility\n", "    high_factor = 1 + abs(np.random.normal(0, 0.005))\n", "    low_factor = 1 - abs(np.random.normal(0, 0.005))\n", "    \n", "    if i == 0:\n", "        open_price = price\n", "    else:\n", "        open_price = prices[i-1] * (1 + np.random.normal(0, 0.001))\n", "    \n", "    high = max(open_price, price) * high_factor\n", "    low = min(open_price, price) * low_factor\n", "    close = price\n", "    volume = np.random.randint(1000, 10000)\n", "    \n", "    ohlc_data.append({\n", "        'datetime': dates[i],\n", "        'open': open_price,\n", "        'high': high,\n", "        'low': low,\n", "        'close': close,\n", "        'volume': volume\n", "    })\n", "\n", "# Create DataFrame\n", "df = pd.DataFrame(ohlc_data)\n", "df.set_index('datetime', inplace=True)\n", "\n", "print(f\"✓ Generated {len(df)} periods of market data\")\n", "print(f\"Date range: {df.index[0]} to {df.index[-1]}\")\n", "print(f\"Price range: ${df['low'].min():.2f} - ${df['high'].max():.2f}\")\n", "\n", "# Display sample data\n", "display(df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Basic Technical Indicators\n", "\n", "Calculate and visualize fundamental technical indicators."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate basic indicators\n", "close = df['close']\n", "high = df['high']\n", "low = df['low']\n", "volume = df['volume']\n", "\n", "# Moving Averages\n", "df['sma_20'] = TechnicalIndicators.sma(close, 20)\n", "df['sma_50'] = TechnicalIndicators.sma(close, 50)\n", "df['ema_12'] = TechnicalIndicators.ema(close, 12)\n", "df['ema_26'] = TechnicalIndicators.ema(close, 26)\n", "\n", "# Momentum Indicators\n", "df['rsi'] = TechnicalIndicators.rsi(close, 14)\n", "macd_line, signal_line, histogram = TechnicalIndicators.macd(close)\n", "df['macd'] = macd_line\n", "df['macd_signal'] = signal_line\n", "df['macd_histogram'] = histogram\n", "\n", "# Volatility Indicators\n", "bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(close, 20, 2)\n", "df['bb_upper'] = bb_upper\n", "df['bb_middle'] = bb_middle\n", "df['bb_lower'] = bb_lower\n", "df['atr'] = TechnicalIndicators.atr(high, low, close, 14)\n", "\n", "# Oscillators\n", "stoch_k, stoch_d = TechnicalIndicators.stochastic(high, low, close, 14, 3)\n", "df['stoch_k'] = stoch_k\n", "df['stoch_d'] = stoch_d\n", "df['williams_r'] = TechnicalIndicators.williams_r(high, low, close, 14)\n", "df['cci'] = TechnicalIndicators.cci(high, low, close, 20)\n", "\n", "# Volume Indicators\n", "df['obv'] = TechnicalIndicators.obv(close, volume)\n", "df['vwap'] = TechnicalIndicators.vwap(high, low, close, volume)\n", "\n", "print(\"✓ Basic technical indicators calculated\")\n", "print(f\"Total indicators: {len([col for col in df.columns if col not in ['open', 'high', 'low', 'close', 'volume']])}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize price with moving averages and Bollinger Bands\n", "fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "# Price and moving averages\n", "ax.plot(df.index, df['close'], label='Close Price', linewidth=1.5, alpha=0.8)\n", "ax.plot(df.index, df['sma_20'], label='SMA(20)', linewidth=1.2)\n", "ax.plot(df.index, df['sma_50'], label='SMA(50)', linewidth=1.2)\n", "ax.plot(df.index, df['ema_12'], label='EMA(12)', linewidth=1.2, linestyle='--')\n", "ax.plot(df.index, df['vwap'], label='VWAP', linewidth=1.2, alpha=0.7)\n", "\n", "# Bollinger Bands\n", "ax.fill_between(df.index, df['bb_upper'], df['bb_lower'], \n", "               alpha=0.1, color='gray', label='Bollinger Bands')\n", "ax.plot(df.index, df['bb_upper'], '--', alpha=0.5, color='red', linewidth=0.8)\n", "ax.plot(df.index, df['bb_lower'], '--', alpha=0.5, color='green', linewidth=0.8)\n", "\n", "ax.set_title('Price Chart with Moving Averages and Bollinger Bands', fontsize=16)\n", "ax.set_ylabel('Price ($)', fontsize=12)\n", "ax.legend(loc='upper left')\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Momentum and Oscillator Analysis\n", "\n", "Analyze momentum indicators and oscillators."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create subplot for momentum indicators\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# RSI\n", "ax1.plot(df.index, df['rsi'], label='RSI', color='orange', linewidth=1.5)\n", "ax1.axhline(y=70, color='r', linestyle='--', alpha=0.7, label='Overbought (70)')\n", "ax1.axhline(y=30, color='g', linestyle='--', alpha=0.7, label='Oversold (30)')\n", "ax1.axhline(y=50, color='black', linestyle='-', alpha=0.3)\n", "ax1.set_title('Relative Strength Index (RSI)', fontsize=14)\n", "ax1.set_ylabel('RSI')\n", "ax1.set_ylim(0, 100)\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# MACD\n", "ax2.plot(df.index, df['macd'], label='MACD', color='blue', linewidth=1.5)\n", "ax2.plot(df.index, df['macd_signal'], label='Signal', color='red', linewidth=1.5)\n", "ax2.bar(df.index, df['macd_histogram'], alpha=0.3, label='Histogram', width=0.5)\n", "ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "ax2.set_title('MACD (Moving Average Convergence Divergence)', fontsize=14)\n", "ax2.set_ylabel('MACD')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Stochastic Oscillator\n", "ax3.plot(df.index, df['stoch_k'], label='%K', color='blue', linewidth=1.5)\n", "ax3.plot(df.index, df['stoch_d'], label='%D', color='red', linewidth=1.5)\n", "ax3.axhline(y=80, color='r', linestyle='--', alpha=0.7, label='Overbought (80)')\n", "ax3.axhline(y=20, color='g', linestyle='--', alpha=0.7, label='Oversold (20)')\n", "ax3.set_title('Stochastic Oscillator', fontsize=14)\n", "ax3.set_ylabel('Stochastic')\n", "ax3.set_ylim(0, 100)\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# Williams %R\n", "ax4.plot(df.index, df['williams_r'], label='Williams %R', color='purple', linewidth=1.5)\n", "ax4.axhline(y=-20, color='r', linestyle='--', alpha=0.7, label='Overbought (-20)')\n", "ax4.axhline(y=-80, color='g', linestyle='--', alpha=0.7, label='Oversold (-80)')\n", "ax4.set_title('Williams %R', fontsize=14)\n", "ax4.set_ylabel('Williams %R')\n", "ax4.set_ylim(-100, 0)\n", "ax4.legend()\n", "ax4.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Volume Analysis\n", "\n", "Analyze volume-based indicators."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Volume analysis\n", "fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 12))\n", "\n", "# Price and Volume\n", "ax1.plot(df.index, df['close'], label='Close Price', color='blue', linewidth=1.5)\n", "ax1_vol = ax1.twinx()\n", "ax1_vol.bar(df.index, df['volume'], alpha=0.3, color='gray', width=0.5, label='Volume')\n", "ax1.set_title('Price and Volume', fontsize=14)\n", "ax1.set_ylabel('Price ($)', color='blue')\n", "ax1_vol.set_ylabel('Volume', color='gray')\n", "ax1.legend(loc='upper left')\n", "ax1_vol.legend(loc='upper right')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# On-Balance Volume (OBV)\n", "ax2.plot(df.index, df['obv'], label='OBV', color='green', linewidth=1.5)\n", "ax2.set_title('On-Balance Volume (OBV)', fontsize=14)\n", "ax2.set_ylabel('OBV')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Price vs VWAP\n", "ax3.plot(df.index, df['close'], label='Close Price', color='blue', linewidth=1.5)\n", "ax3.plot(df.index, df['vwap'], label='VWAP', color='orange', linewidth=1.5)\n", "ax3.fill_between(df.index, df['close'], df['vwap'], \n", "                where=(df['close'] > df['vwap']), alpha=0.3, color='green', label='Above VWAP')\n", "ax3.fill_between(df.index, df['close'], df['vwap'], \n", "                where=(df['close'] <= df['vwap']), alpha=0.3, color='red', label='Below VWAP')\n", "ax3.set_title('Price vs Volume Weighted Average Price (VWAP)', fontsize=14)\n", "ax3.set_ylabel('Price ($)')\n", "ax3.set_xlabel('Time')\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Signal Generation\n", "\n", "Generate trading signals based on technical indicators."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate trading signals\n", "signals_df = IndicatorAnalyzer.generate_signals(\n", "    df, \n", "    indicators=['rsi', 'macd', 'bollinger'],\n", "    thresholds={\n", "        'rsi_oversold': 30,\n", "        'rsi_overbought': 70,\n", "        'macd_signal': 0,\n", "        'bb_lower': 0.02,\n", "        'bb_upper': 0.02\n", "    }\n", ")\n", "\n", "# Count signals\n", "rsi_buy_signals = signals_df['rsi_buy'].sum()\n", "rsi_sell_signals = signals_df['rsi_sell'].sum()\n", "macd_buy_signals = signals_df['macd_buy'].sum()\n", "macd_sell_signals = signals_df['macd_sell'].sum()\n", "bb_buy_signals = signals_df['bb_buy'].sum()\n", "bb_sell_signals = signals_df['bb_sell'].sum()\n", "\n", "print(\"Trading Signals Generated:\")\n", "print(f\"RSI Signals - Buy: {rsi_buy_signals}, Sell: {rsi_sell_signals}\")\n", "print(f\"MACD Signals - Buy: {macd_buy_signals}, Sell: {macd_sell_signals}\")\n", "print(f\"Bollinger Bands Signals - Buy: {bb_buy_signals}, Sell: {bb_sell_signals}\")\n", "\n", "# Create combined signals\n", "signals_df['combined_buy'] = (\n", "    signals_df['rsi_buy'] | \n", "    signals_df['macd_buy'] | \n", "    signals_df['bb_buy']\n", ")\n", "\n", "signals_df['combined_sell'] = (\n", "    signals_df['rsi_sell'] | \n", "    signals_df['macd_sell'] | \n", "    signals_df['bb_sell']\n", ")\n", "\n", "combined_buy_signals = signals_df['combined_buy'].sum()\n", "combined_sell_signals = signals_df['combined_sell'].sum()\n", "\n", "print(f\"\\nCombined Signals - Buy: {combined_buy_signals}, Sell: {combined_sell_signals}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize signals on price chart\n", "fig, ax = plt.subplots(figsize=(16, 10))\n", "\n", "# Price chart\n", "ax.plot(signals_df.index, signals_df['close'], label='Close Price', linewidth=1.5, color='blue')\n", "ax.plot(signals_df.index, signals_df['sma_20'], label='SMA(20)', alpha=0.7)\n", "ax.plot(signals_df.index, signals_df['bb_upper'], '--', alpha=0.5, color='red', linewidth=0.8)\n", "ax.plot(signals_df.index, signals_df['bb_lower'], '--', alpha=0.5, color='green', linewidth=0.8)\n", "\n", "# Buy signals\n", "buy_points = signals_df[signals_df['combined_buy']]\n", "ax.scatter(buy_points.index, buy_points['close'], \n", "          color='green', marker='^', s=100, label=f'Buy Signals ({len(buy_points)})', zorder=5)\n", "\n", "# Sell signals\n", "sell_points = signals_df[signals_df['combined_sell']]\n", "ax.scatter(sell_points.index, sell_points['close'], \n", "          color='red', marker='v', s=100, label=f'Sell Signals ({len(sell_points)})', zorder=5)\n", "\n", "ax.set_title('Trading Signals on Price Chart', fontsize=16)\n", "ax.set_ylabel('Price ($)', fontsize=12)\n", "ax.set_xlabel('Time', fontsize=12)\n", "ax.legend()\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Strategy Backtesting\n", "\n", "Backtest trading strategies using generated signals."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Backtest different strategies\n", "strategies = {\n", "    'RSI Strategy': {\n", "        'buy_signals': signals_df['rsi_buy'],\n", "        'sell_signals': signals_df['rsi_sell']\n", "    },\n", "    'MACD Strategy': {\n", "        'buy_signals': signals_df['macd_buy'],\n", "        'sell_signals': signals_df['macd_sell']\n", "    },\n", "    'Bollinger Bands Strategy': {\n", "        'buy_signals': signals_df['bb_buy'],\n", "        'sell_signals': signals_df['bb_sell']\n", "    },\n", "    'Combined Strategy': {\n", "        'buy_signals': signals_df['combined_buy'],\n", "        'sell_signals': signals_df['combined_sell']\n", "    }\n", "}\n", "\n", "backtest_results = {}\n", "initial_capital = 100000\n", "\n", "print(\"Backtesting Results:\")\n", "print(\"=\" * 60)\n", "\n", "for strategy_name, signals in strategies.items():\n", "    result = IndicatorAnalyzer.backtest_strategy(\n", "        signals_df,\n", "        signals['buy_signals'],\n", "        signals['sell_signals'],\n", "        initial_capital=initial_capital\n", "    )\n", "    \n", "    backtest_results[strategy_name] = result\n", "    \n", "    print(f\"\\n{strategy_name}:\")\n", "    print(f\"  Initial Capital: ${result['initial_capital']:,.2f}\")\n", "    print(f\"  Final Value: ${result['final_value']:,.2f}\")\n", "    print(f\"  Total Return: {result['total_return']:.2%}\")\n", "    print(f\"  Number of Trades: {result['num_trades']}\")\n", "    \n", "    if result['num_trades'] > 0:\n", "        profit_per_trade = (result['final_value'] - result['initial_capital']) / result['num_trades']\n", "        print(f\"  Profit per Trade: ${profit_per_trade:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize backtest results\n", "strategy_names = list(backtest_results.keys())\n", "returns = [backtest_results[name]['total_return'] for name in strategy_names]\n", "num_trades = [backtest_results[name]['num_trades'] for name in strategy_names]\n", "\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# Returns comparison\n", "bars1 = ax1.bar(strategy_names, returns, color=['blue', 'green', 'orange', 'red'])\n", "ax1.set_title('Strategy Returns Comparison', fontsize=14)\n", "ax1.set_ylabel('Total Return')\n", "ax1.tick_params(axis='x', rotation=45)\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for bar, ret in zip(bars1, returns):\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.001,\n", "             f'{ret:.1%}', ha='center', va='bottom')\n", "\n", "# Number of trades comparison\n", "bars2 = ax2.bar(strategy_names, num_trades, color=['blue', 'green', 'orange', 'red'])\n", "ax2.set_title('Number of Trades Comparison', fontsize=14)\n", "ax2.set_ylabel('Number of Trades')\n", "ax2.tick_params(axis='x', rotation=45)\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for bar, trades in zip(bars2, num_trades):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "             f'{trades}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Advanced Indicator Analysis\n", "\n", "Analyze indicator correlations and effectiveness."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate indicator correlations\n", "indicator_columns = ['rsi', 'macd', 'stoch_k', 'williams_r', 'cci', 'atr']\n", "correlation_matrix = signals_df[indicator_columns].corr()\n", "\n", "# Plot correlation heatmap\n", "fig, ax = plt.subplots(figsize=(10, 8))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,\n", "            square=True, linewidths=0.5, cbar_kws={\"shrink\": .8})\n", "ax.set_title('Technical Indicators Correlation Matrix', fontsize=16)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Indicator Correlations:\")\n", "print(\"=\" * 40)\n", "for i in range(len(indicator_columns)):\n", "    for j in range(i+1, len(indicator_columns)):\n", "        corr = correlation_matrix.iloc[i, j]\n", "        print(f\"{indicator_columns[i]} vs {indicator_columns[j]}: {corr:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze indicator effectiveness\n", "def analyze_indicator_effectiveness(df, indicator_col, buy_threshold, sell_threshold, higher_is_bullish=True):\n", "    \"\"\"Analyze how well an indicator predicts future price movements\"\"\"\n", "    \n", "    # Generate signals\n", "    if higher_is_bullish:\n", "        buy_signals = df[indicator_col] < buy_threshold\n", "        sell_signals = df[indicator_col] > sell_threshold\n", "    else:\n", "        buy_signals = df[indicator_col] > buy_threshold\n", "        sell_signals = df[indicator_col] < sell_threshold\n", "    \n", "    # Calculate future returns (next 5 periods)\n", "    future_returns = df['close'].pct_change(5).shift(-5)\n", "    \n", "    # Analyze signal effectiveness\n", "    buy_signal_returns = future_returns[buy_signals].dropna()\n", "    sell_signal_returns = future_returns[sell_signals].dropna()\n", "    \n", "    results = {\n", "        'indicator': indicator_col,\n", "        'buy_signals': len(buy_signal_returns),\n", "        'sell_signals': len(sell_signal_returns),\n", "        'buy_success_rate': (buy_signal_returns > 0).mean() if len(buy_signal_returns) > 0 else 0,\n", "        'sell_success_rate': (sell_signal_returns < 0).mean() if len(sell_signal_returns) > 0 else 0,\n", "        'avg_buy_return': buy_signal_returns.mean() if len(buy_signal_returns) > 0 else 0,\n", "        'avg_sell_return': sell_signal_returns.mean() if len(sell_signal_returns) > 0 else 0\n", "    }\n", "    \n", "    return results\n", "\n", "# Analyze different indicators\n", "effectiveness_results = []\n", "\n", "# RSI\n", "rsi_results = analyze_indicator_effectiveness(signals_df, 'rsi', 30, 70, False)\n", "effectiveness_results.append(rsi_results)\n", "\n", "# Stochastic %K\n", "stoch_results = analyze_indicator_effectiveness(signals_df, 'stoch_k', 20, 80, False)\n", "effectiveness_results.append(stoch_results)\n", "\n", "# Williams %R\n", "williams_results = analyze_indicator_effectiveness(signals_df, 'williams_r', -80, -20, True)\n", "effectiveness_results.append(williams_results)\n", "\n", "# Display results\n", "print(\"Indicator Effectiveness Analysis:\")\n", "print(\"=\" * 80)\n", "print(f\"{'Indicator':<15} {'Buy Signals':<12} {'Sell Signals':<13} {'Buy Success':<12} {'Sell Success':<13} {'Avg Buy Ret':<12} {'Avg Sell Ret':<12}\")\n", "print(\"=\" * 80)\n", "\n", "for result in effectiveness_results:\n", "    print(f\"{result['indicator']:<15} {result['buy_signals']:<12} {result['sell_signals']:<13} \"\n", "          f\"{result['buy_success_rate']:<12.1%} {result['sell_success_rate']:<13.1%} \"\n", "          f\"{result['avg_buy_return']:<12.3f} {result['avg_sell_return']:<12.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Custom Indicator Development\n", "\n", "Create custom technical indicators."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Custom indicator: Price Rate of Change (ROC)\n", "def price_roc(prices, period=12):\n", "    \"\"\"Calculate Price Rate of Change\"\"\"\n", "    return ((prices - prices.shift(period)) / prices.shift(period)) * 100\n", "\n", "# Custom indicator: Commodity Channel Index with custom parameters\n", "def custom_cci(high, low, close, period=20, multiplier=0.015):\n", "    \"\"\"Custom CCI with adjustable multiplier\"\"\"\n", "    typical_price = (high + low + close) / 3\n", "    sma_tp = typical_price.rolling(window=period).mean()\n", "    mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))\n", "    return (typical_price - sma_tp) / (multiplier * mad)\n", "\n", "# Custom indicator: Volatility-adjusted RSI\n", "def volatility_adjusted_rsi(prices, rsi_period=14, vol_period=20):\n", "    \"\"\"RSI adjusted for volatility\"\"\"\n", "    rsi = TechnicalIndicators.rsi(prices, rsi_period)\n", "    volatility = prices.rolling(window=vol_period).std()\n", "    vol_normalized = (volatility - volatility.rolling(window=50).mean()) / volatility.rolling(window=50).std()\n", "    \n", "    # Adjust RSI based on volatility\n", "    adjusted_rsi = rsi + (vol_normalized * 10)  # Scale factor of 10\n", "    return np.clip(adjusted_rsi, 0, 100)\n", "\n", "# Calculate custom indicators\n", "signals_df['price_roc'] = price_roc(signals_df['close'], 12)\n", "signals_df['custom_cci'] = custom_cci(signals_df['high'], signals_df['low'], signals_df['close'])\n", "signals_df['vol_adj_rsi'] = volatility_adjusted_rsi(signals_df['close'])\n", "\n", "print(\"✓ Custom indicators calculated\")\n", "print(f\"Price ROC range: {signals_df['price_roc'].min():.2f} to {signals_df['price_roc'].max():.2f}\")\n", "print(f\"Custom CCI range: {signals_df['custom_cci'].min():.2f} to {signals_df['custom_cci'].max():.2f}\")\n", "print(f\"Vol-Adjusted RSI range: {signals_df['vol_adj_rsi'].min():.2f} to {signals_df['vol_adj_rsi'].max():.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize custom indicators\n", "fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 12))\n", "\n", "# Price Rate of Change\n", "ax1.plot(signals_df.index, signals_df['price_roc'], label='Price ROC (12)', color='blue', linewidth=1.5)\n", "ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "ax1.axhline(y=10, color='r', linestyle='--', alpha=0.7, label='Overbought (+10%)')\n", "ax1.axhline(y=-10, color='g', linestyle='--', alpha=0.7, label='Oversold (-10%)')\n", "ax1.set_title('Price Rate of Change (ROC)', fontsize=14)\n", "ax1.set_ylabel('ROC (%)')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Custom CCI\n", "ax2.plot(signals_df.index, signals_df['custom_cci'], label='Custom CCI', color='orange', linewidth=1.5)\n", "ax2.axhline(y=100, color='r', linestyle='--', alpha=0.7, label='Overbought (+100)')\n", "ax2.axhline(y=-100, color='g', linestyle='--', alpha=0.7, label='Oversold (-100)')\n", "ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "ax2.set_title('Custom Commodity Channel Index', fontsize=14)\n", "ax2.set_ylabel('CCI')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Volatility-Adjusted RSI vs Regular RSI\n", "ax3.plot(signals_df.index, signals_df['rsi'], label='Regular RSI', color='blue', linewidth=1.5, alpha=0.7)\n", "ax3.plot(signals_df.index, signals_df['vol_adj_rsi'], label='Volatility-Adjusted RSI', color='red', linewidth=1.5)\n", "ax3.axhline(y=70, color='r', linestyle='--', alpha=0.5)\n", "ax3.axhline(y=30, color='g', linestyle='--', alpha=0.5)\n", "ax3.set_title('Regular RSI vs Volatility-Adjusted RSI', fontsize=14)\n", "ax3.set_ylabel('RSI')\n", "ax3.set_xlabel('Time')\n", "ax3.set_ylim(0, 100)\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Multi-Timeframe Analysis\n", "\n", "Analyze indicators across different timeframes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Resample data to different timeframes\n", "timeframes = {\n", "    '4H': df.resample('4H').agg({\n", "        'open': 'first',\n", "        'high': 'max',\n", "        'low': 'min',\n", "        'close': 'last',\n", "        'volume': 'sum'\n", "    }).dropna(),\n", "    '1D': df.resample('1D').agg({\n", "        'open': 'first',\n", "        'high': 'max',\n", "        'low': 'min',\n", "        'close': 'last',\n", "        'volume': 'sum'\n", "    }).dropna()\n", "}\n", "\n", "# Calculate RSI for different timeframes\n", "rsi_data = {\n", "    '1H': TechnicalIndicators.rsi(df['close']),\n", "    '4H': TechnicalIndicators.rsi(timeframes['4H']['close']),\n", "    '1D': TechnicalIndicators.rsi(timeframes['1D']['close'])\n", "}\n", "\n", "print(\"Multi-timeframe RSI Analysis:\")\n", "for tf, rsi_series in rsi_data.items():\n", "    latest_rsi = rsi_series.iloc[-1] if len(rsi_series) > 0 else np.nan\n", "    print(f\"{tf} RSI: {latest_rsi:.1f}\")\n", "\n", "# Plot multi-timeframe RSI\n", "fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "# Plot RSI for each timeframe\n", "ax.plot(rsi_data['1H'].index, rsi_data['1H'], label='1H RSI', linewidth=1, alpha=0.7)\n", "\n", "# Resample higher timeframe data to match 1H index for plotting\n", "rsi_4h_resampled = rsi_data['4H'].reindex(rsi_data['1H'].index, method='ffill')\n", "rsi_1d_resampled = rsi_data['1D'].reindex(rsi_data['1H'].index, method='ffill')\n", "\n", "ax.plot(rsi_data['1H'].index, rsi_4h_resampled, label='4H RSI', linewidth=2)\n", "ax.plot(rsi_data['1H'].index, rsi_1d_resampled, label='1D RSI', linewidth=3, alpha=0.8)\n", "\n", "# Add reference lines\n", "ax.axhline(y=70, color='r', linestyle='--', alpha=0.5, label='Overbought')\n", "ax.axhline(y=30, color='g', linestyle='--', alpha=0.5, label='Oversold')\n", "ax.axhline(y=50, color='black', linestyle='-', alpha=0.3)\n", "\n", "ax.set_title('Multi-Timeframe RSI Analysis', fontsize=16)\n", "ax.set_ylabel('RSI')\n", "ax.set_xlabel('Time')\n", "ax.set_ylim(0, 100)\n", "ax.legend()\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. <PERSON><PERSON><PERSON> and Key Insights\n", "\n", "Summarize the technical analysis findings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate summary statistics\n", "print(\"Technical Analysis Summary\")\n", "print(\"=\" * 50)\n", "\n", "# Price statistics\n", "price_change = (df['close'].iloc[-1] - df['close'].iloc[0]) / df['close'].iloc[0]\n", "volatility = df['close'].pct_change().std() * np.sqrt(24)  # Annualized volatility\n", "max_drawdown = ((df['close'] / df['close'].expanding().max()) - 1).min()\n", "\n", "print(f\"\\nPrice Performance:\")\n", "print(f\"  Total Return: {price_change:.2%}\")\n", "print(f\"  Annualized Volatility: {volatility:.2%}\")\n", "print(f\"  Maximum Drawdown: {max_drawdown:.2%}\")\n", "\n", "# Current indicator readings\n", "print(f\"\\nCurrent Indicator Readings:\")\n", "print(f\"  RSI: {signals_df['rsi'].iloc[-1]:.1f}\")\n", "print(f\"  MACD: {signals_df['macd'].iloc[-1]:.4f}\")\n", "print(f\"  Stochastic %K: {signals_df['stoch_k'].iloc[-1]:.1f}\")\n", "print(f\"  Williams %R: {signals_df['williams_r'].iloc[-1]:.1f}\")\n", "print(f\"  CCI: {signals_df['cci'].iloc[-1]:.1f}\")\n", "\n", "# Best performing strategy\n", "best_strategy = max(backtest_results.items(), key=lambda x: x[1]['total_return'])\n", "print(f\"\\nBest Performing Strategy: {best_strategy[0]}\")\n", "print(f\"  Return: {best_strategy[1]['total_return']:.2%}\")\n", "print(f\"  Trades: {best_strategy[1]['num_trades']}\")\n", "\n", "# Market regime analysis\n", "recent_trend = signals_df['close'].iloc[-20:].pct_change().mean()\n", "recent_volatility = signals_df['close'].iloc[-20:].pct_change().std()\n", "\n", "print(f\"\\nRecent Market Regime (Last 20 periods):\")\n", "print(f\"  Average Return: {recent_trend:.4f}\")\n", "print(f\"  Volatility: {recent_volatility:.4f}\")\n", "\n", "if recent_trend > 0.001:\n", "    trend_desc = \"Bullish\"\n", "elif recent_trend < -0.001:\n", "    trend_desc = \"Bearish\"\n", "else:\n", "    trend_desc = \"Sideways\"\n", "\n", "vol_desc = \"High\" if recent_volatility > volatility/np.sqrt(24) else \"Low\"\n", "\n", "print(f\"  Market Regime: {trend_desc} trend with {vol_desc} volatility\")\n", "\n", "print(f\"\\n🎯 Analysis Complete!\")\n", "print(f\"\\nKey Takeaways:\")\n", "print(f\"- Generated and analyzed {len([col for col in signals_df.columns if col not in ['open', 'high', 'low', 'close', 'volume']])} technical indicators\")\n", "print(f\"- Backtested {len(strategies)} different trading strategies\")\n", "print(f\"- Best strategy achieved {best_strategy[1]['total_return']:.2%} return\")\n", "print(f\"- Current market regime appears {trend_desc.lower()} with {vol_desc.lower()} volatility\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}