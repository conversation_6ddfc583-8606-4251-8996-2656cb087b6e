version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: financial-data-admin-db
    environment:
      POSTGRES_DB: financial_data_admin
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - admin-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: financial-data-admin-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - admin-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: financial-data-admin-backend
    environment:
      - JWT_SECRET_KEY=your-secret-key-change-in-production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=admin
      - DB_PASSWORD=password
      - DB_NAME=financial_data_admin
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - admin-network

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: financial-data-admin-frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - admin-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: financial-data-admin-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - admin-network

volumes:
  postgres_data:
  redis_data:

networks:
  admin-network:
    driver: bridge