#pragma once

#include <prometheus/counter.h>
#include <prometheus/gauge.h>
#include <prometheus/histogram.h>
#include <prometheus/registry.h>
#include <prometheus/exposer.h>
#include <memory>
#include <string>
#include <unordered_map>

namespace monitoring {

class PrometheusMetrics {
public:
    static PrometheusMetrics& getInstance();
    
    // Initialize Prometheus metrics server
    bool initialize(const std::string& bind_address = "0.0.0.0:9090");
    
    // Latency metrics
    void recordLatency(const std::string& operation, double latency_microseconds);
    void recordEndToEndLatency(double latency_microseconds);
    
    // Data integrity metrics
    void incrementDataReceived(const std::string& symbol);
    void incrementDataLoss(const std::string& symbol);
    void recordSequenceGap(const std::string& symbol, int gap_size);
    
    // Resource usage metrics
    void updateCpuUsage(double percentage);
    void updateMemoryUsage(double percentage);
    void updateDiskUsage(double percentage);
    void updateNetworkBandwidth(double bytes_per_second);
    
    // System health metrics
    void incrementConnectionCount();
    void decrementConnectionCount();
    void updateQueueSize(const std::string& queue_name, int size);
    void recordThroughput(const std::string& component, double messages_per_second);
    
    // Alert metrics
    void incrementAlert(const std::string& alert_type, const std::string& severity);
    
private:
    PrometheusMetrics() = default;
    ~PrometheusMetrics() = default;
    PrometheusMetrics(const PrometheusMetrics&) = delete;
    PrometheusMetrics& operator=(const PrometheusMetrics&) = delete;
    
    std::shared_ptr<prometheus::Registry> registry_;
    std::unique_ptr<prometheus::Exposer> exposer_;
    
    // Latency metrics
    prometheus::Family<prometheus::Histogram>* latency_histogram_family_;
    prometheus::Family<prometheus::Histogram>* end_to_end_latency_family_;
    
    // Data integrity metrics
    prometheus::Family<prometheus::Counter>* data_received_family_;
    prometheus::Family<prometheus::Counter>* data_loss_family_;
    prometheus::Family<prometheus::Histogram>* sequence_gap_family_;
    
    // Resource metrics
    prometheus::Gauge* cpu_usage_gauge_;
    prometheus::Gauge* memory_usage_gauge_;
    prometheus::Gauge* disk_usage_gauge_;
    prometheus::Gauge* network_bandwidth_gauge_;
    
    // System health metrics
    prometheus::Gauge* connection_count_gauge_;
    prometheus::Family<prometheus::Gauge>* queue_size_family_;
    prometheus::Family<prometheus::Gauge>* throughput_family_;
    
    // Alert metrics
    prometheus::Family<prometheus::Counter>* alert_counter_family_;
    
    // Metric caches
    std::unordered_map<std::string, prometheus::Histogram*> latency_histograms_;
    std::unordered_map<std::string, prometheus::Counter*> data_received_counters_;
    std::unordered_map<std::string, prometheus::Counter*> data_loss_counters_;
    std::unordered_map<std::string, prometheus::Gauge*> queue_size_gauges_;
    std::unordered_map<std::string, prometheus::Gauge*> throughput_gauges_;
    
    void initializeMetrics();
    prometheus::Histogram::BucketBoundaries createLatencyBuckets();
};

} // namespace monitoring