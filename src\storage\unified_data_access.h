#pragma once

#include <string>
#include <vector>
#include <memory>
#include <future>
#include <chrono>
#include <unordered_map>
#include <atomic>
#include <mutex>
#include <functional>

#include "../data_models.h"
#include "redis_storage.h"
#include "clickhouse_storage.h"
#include "cold_storage.hpp"
#include "storage_layer_selector.h"

// Forward declaration to avoid circular dependency
namespace financial_data {
    class QueryPerformanceOptimizer;
}

namespace financial_data {

/**
 * @brief 查询请求结构
 */
struct QueryRequest {
    std::string symbol;
    std::string exchange;
    std::string data_type = "tick";  // tick, level2, kline
    int64_t start_timestamp_ns = 0;
    int64_t end_timestamp_ns = 0;
    int limit = 1000;
    std::string cursor;  // 分页游标
    
    // 查询选项
    bool include_level2 = false;
    bool enable_cache = true;
    std::string kline_period = "1m";  // 1m, 5m, 15m, 1h, 1d
    
    QueryRequest() = default;
    QueryRequest(const std::string& sym, int64_t start_ts, int64_t end_ts, int lmt = 1000)
        : symbol(sym), start_timestamp_ns(start_ts), end_timestamp_ns(end_ts), limit(lmt) {}
};

/**
 * @brief 查询响应结构
 */
struct QueryResponse {
    std::vector<StandardTick> ticks;
    std::vector<Level2Data> level2_data;
    std::string next_cursor;
    bool has_more = false;
    std::string storage_source;  // hot, warm, cold, mixed
    std::chrono::milliseconds query_time{0};
    size_t total_records = 0;
    
    // 查询统计信息
    struct QueryStats {
        size_t hot_storage_hits = 0;
        size_t warm_storage_hits = 0;
        size_t cold_storage_hits = 0;
        std::chrono::milliseconds hot_query_time{0};
        std::chrono::milliseconds warm_query_time{0};
        std::chrono::milliseconds cold_query_time{0};
    } stats;
    
    QueryResponse() = default;
    
    bool IsEmpty() const {
        return ticks.empty() && level2_data.empty();
    }
    
    size_t GetTotalSize() const {
        return ticks.size() + level2_data.size();
    }
};

/**
 * @brief 存储层枚举
 */
enum class StorageLayer {
    HOT = 1,     // Redis - 0-7天
    WARM = 2,    // ClickHouse - 7天-2年
    COLD = 3,    // S3/MinIO - >2年
    AUTO = 0     // 自动选择
};

/**
 * @brief 存储层配置
 */
struct StorageLayerConfig {
    // 时间阈值配置（天数）
    int hot_storage_days = 7;      // 热存储保留天数
    int warm_storage_days = 730;   // 温存储保留天数（2年）
    
    // 存储层健康检查配置
    std::chrono::seconds health_check_interval{60};
    int max_retry_attempts = 3;
    std::chrono::seconds retry_delay{1};
    
    // 查询优化配置
    bool enable_query_cache = true;
    size_t cache_size = 10000;
    std::chrono::minutes cache_ttl{30};
    
    // 并发配置
    size_t max_concurrent_queries = 100;
    std::chrono::seconds query_timeout{30};
};

/**
 * @brief 查询缓存项
 */
struct CacheItem {
    QueryResponse response;
    std::chrono::steady_clock::time_point timestamp;
    std::string query_hash;
    
    bool IsExpired(std::chrono::minutes ttl) const {
        auto now = std::chrono::steady_clock::now();
        return (now - timestamp) > ttl;
    }
};

/**
 * @brief 统一数据访问接口
 * 
 * 提供透明的热、温、冷存储访问，自动路由查询到合适的存储层
 */
class UnifiedDataAccessInterface : public std::enable_shared_from_this<UnifiedDataAccessInterface> {
public:
    explicit UnifiedDataAccessInterface(const StorageLayerConfig& config = StorageLayerConfig{});
    ~UnifiedDataAccessInterface();
    
    // 初始化和生命周期管理
    bool Initialize(std::shared_ptr<RedisHotStorage> hot_storage,
                   std::shared_ptr<ClickHouseStorage> warm_storage,
                   std::shared_ptr<storage::ColdDataStorage> cold_storage);
    bool Initialize(std::shared_ptr<RedisHotStorage> hot_storage,
                   std::shared_ptr<ClickHouseStorage> warm_storage,
                   std::shared_ptr<storage::ColdDataStorage> cold_storage,
                   std::shared_ptr<StorageLayerSelector> layer_selector);
    bool Initialize(std::shared_ptr<RedisHotStorage> hot_storage,
                   std::shared_ptr<ClickHouseStorage> warm_storage,
                   std::shared_ptr<storage::ColdDataStorage> cold_storage,
                   std::shared_ptr<StorageLayerSelector> layer_selector,
                   std::shared_ptr<QueryPerformanceOptimizer> query_optimizer);
    void Shutdown();
    bool IsInitialized() const { return initialized_.load(); }
    
    // 主要查询接口
    std::future<QueryResponse> QueryData(const QueryRequest& request);
    std::future<StandardTick> GetLatestTick(const std::string& symbol, const std::string& exchange = "");
    std::future<Level2Data> GetLatestLevel2(const std::string& symbol, const std::string& exchange = "");
    
    // 批量查询接口
    std::future<std::unordered_map<std::string, StandardTick>> GetLatestTicks(
        const std::vector<std::string>& symbols, const std::string& exchange = "");
    std::future<std::unordered_map<std::string, Level2Data>> GetLatestLevel2Data(
        const std::vector<std::string>& symbols, const std::string& exchange = "");
    
    // 时间范围查询
    std::future<QueryResponse> GetRecentTicks(const std::string& symbol, 
                                             const std::string& exchange = "",
                                             int count = 100);
    std::future<QueryResponse> GetHistoricalData(const std::string& symbol,
                                                 const std::string& exchange,
                                                 int64_t start_timestamp_ns,
                                                 int64_t end_timestamp_ns,
                                                 int limit = 1000);
    
    // 优化查询接口
    std::future<QueryResponse> OptimizedQuery(const QueryRequest& request);
    std::future<std::vector<QueryResponse>> BatchQuery(const std::vector<QueryRequest>& requests);
    
    // 分页查询支持
    std::future<QueryResponse> GetNextPage(const std::string& cursor);
    std::future<QueryResponse> PaginatedQuery(const QueryRequest& request, const std::string& cursor = "");
    std::string CreatePaginationCursor(const QueryRequest& request);
    
    // 存储层健康检查
    struct HealthStatus {
        bool overall_healthy = false;
        bool hot_storage_healthy = false;
        bool warm_storage_healthy = false;
        bool cold_storage_healthy = false;
        std::string error_message;
        std::chrono::system_clock::time_point last_check;
        
        struct LayerStats {
            std::chrono::milliseconds avg_response_time{0};
            double success_rate = 0.0;
            size_t total_queries = 0;
            size_t failed_queries = 0;
        };
        
        LayerStats hot_stats;
        LayerStats warm_stats;
        LayerStats cold_stats;
    };
    
    HealthStatus GetHealthStatus() const;
    void TriggerHealthCheck();
    
    // 性能统计
    struct PerformanceMetrics {
        std::atomic<uint64_t> total_queries{0};
        std::atomic<uint64_t> cache_hits{0};
        std::atomic<uint64_t> cache_misses{0};
        std::atomic<uint64_t> hot_storage_queries{0};
        std::atomic<uint64_t> warm_storage_queries{0};
        std::atomic<uint64_t> cold_storage_queries{0};
        std::atomic<uint64_t> mixed_storage_queries{0};
        std::atomic<double> avg_query_time_ms{0.0};
        
        void Reset() {
            total_queries = 0;
            cache_hits = 0;
            cache_misses = 0;
            hot_storage_queries = 0;
            warm_storage_queries = 0;
            cold_storage_queries = 0;
            mixed_storage_queries = 0;
            avg_query_time_ms = 0.0;
        }
    };
    
    PerformanceMetrics GetMetrics() const;
    void ResetMetrics();
    
    // 配置管理
    bool UpdateConfig(const StorageLayerConfig& config);
    StorageLayerConfig GetConfig() const { return config_; }
    
    // 缓存管理
    void ClearCache();
    size_t GetCacheSize() const;
    double GetCacheHitRate() const;

private:
    // 存储层路由逻辑
    StorageLayer DetermineStorageLayer(int64_t timestamp_ns) const;
    std::vector<StorageLayer> DetermineStorageLayers(int64_t start_timestamp_ns, 
                                                    int64_t end_timestamp_ns) const;
    
    // 单层存储查询
    std::future<QueryResponse> QueryFromHotStorage(const QueryRequest& request);
    std::future<QueryResponse> QueryFromWarmStorage(const QueryRequest& request);
    std::future<QueryResponse> QueryFromColdStorage(const QueryRequest& request);
    
    // 跨层存储查询
    std::future<QueryResponse> QueryFromMultipleLayers(const QueryRequest& request,
                                                       const std::vector<StorageLayer>& layers);
    
    // 查询缓存
    std::string GenerateQueryHash(const QueryRequest& request) const;
    bool GetFromCache(const std::string& query_hash, QueryResponse& response);
    void PutToCache(const std::string& query_hash, const QueryResponse& response);
    void CleanupExpiredCache();
    
    // 健康检查
    void PerformHealthCheck();
    void CheckStorageLayerHealth(StorageLayer layer);
    
    // 错误处理和重试
    std::future<QueryResponse> RetryQuery(const QueryRequest& request, 
                                         StorageLayer failed_layer,
                                         int retry_count = 0);
    
    // 响应合并
    QueryResponse MergeResponses(const std::vector<QueryResponse>& responses);
    void SortResponseByTimestamp(QueryResponse& response);
    
    // 时间戳转换辅助方法
    int64_t GetCurrentTimestampNs() const;
    int64_t DaysToNanoseconds(int days) const;
    bool IsTimestampInRange(int64_t timestamp_ns, int64_t start_ns, int64_t end_ns) const;
    
    // 配置和状态
    StorageLayerConfig config_;
    std::atomic<bool> initialized_{false};
    std::atomic<bool> shutdown_requested_{false};
    
    // 存储层引用
    std::shared_ptr<RedisHotStorage> hot_storage_;
    std::shared_ptr<ClickHouseStorage> warm_storage_;
    std::shared_ptr<storage::ColdDataStorage> cold_storage_;
    std::shared_ptr<StorageLayerSelector> layer_selector_;
    std::shared_ptr<QueryPerformanceOptimizer> query_optimizer_;
    
    // 查询缓存
    mutable std::mutex cache_mutex_;
    std::unordered_map<std::string, CacheItem> query_cache_;
    
    // 健康检查
    mutable std::mutex health_mutex_;
    HealthStatus health_status_;
    std::thread health_check_thread_;
    
    // 性能统计
    mutable PerformanceMetrics metrics_;
    
    // 并发控制
    std::atomic<size_t> active_queries_{0};
    
    // 分页游标管理
    mutable std::mutex cursor_mutex_;
    std::unordered_map<std::string, QueryRequest> cursor_map_;
    
    // 日志记录器
    std::shared_ptr<spdlog::logger> logger_;
    
    void InitializeLogger();
    void UpdateQueryMetrics(StorageLayer layer, std::chrono::milliseconds query_time, bool success);
};

/**
 * @brief 统一数据访问接口工厂
 */
class UnifiedDataAccessFactory {
public:
    static std::unique_ptr<UnifiedDataAccessInterface> CreateDefault();
    static std::unique_ptr<UnifiedDataAccessInterface> CreateHighPerformance();
    static std::unique_ptr<UnifiedDataAccessInterface> CreateLowLatency();
    static std::unique_ptr<UnifiedDataAccessInterface> CreateFromConfig(const std::string& config_file);
};

} // namespace financial_data