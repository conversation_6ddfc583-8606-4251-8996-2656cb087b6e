#!/usr/bin/env python3
"""
调度器服务启动脚本
Financial Data Service - Scheduler Startup Script
"""

import os
import sys
import argparse
import asyncio
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))

from services.scheduler_service import SchedulerService


def setup_logging(log_level: str = "INFO"):
    """设置日志"""
    # 创建日志目录
    log_dir = project_root / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_dir / 'scheduler_startup.log')
        ]
    )


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='金融数据服务 - 任务调度器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python scripts/start_scheduler.py                    # 使用默认配置启动
  python scripts/start_scheduler.py --config custom.json  # 使用自定义配置
  python scripts/start_scheduler.py --log-level DEBUG     # 设置日志级别
  python scripts/start_scheduler.py --daemon              # 后台运行模式
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        default='config/scheduler_config.json',
        help='配置文件路径 (默认: config/scheduler_config.json)'
    )
    
    parser.add_argument(
        '--log-level', '-l',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--daemon', '-d',
        action='store_true',
        help='后台运行模式'
    )
    
    parser.add_argument(
        '--pid-file',
        default='logs/scheduler.pid',
        help='PID文件路径 (默认: logs/scheduler.pid)'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示服务状态'
    )
    
    parser.add_argument(
        '--stop',
        action='store_true',
        help='停止服务'
    )
    
    return parser.parse_args()


def write_pid_file(pid_file: str):
    """写入PID文件"""
    try:
        pid_path = Path(pid_file)
        pid_path.parent.mkdir(exist_ok=True)
        
        with open(pid_path, 'w') as f:
            f.write(str(os.getpid()))
        
        print(f"PID文件已写入: {pid_path}")
    except Exception as e:
        print(f"写入PID文件失败: {e}")


def read_pid_file(pid_file: str) -> int:
    """读取PID文件"""
    try:
        with open(pid_file, 'r') as f:
            return int(f.read().strip())
    except (FileNotFoundError, ValueError):
        return None


def check_service_status(pid_file: str):
    """检查服务状态"""
    pid = read_pid_file(pid_file)
    
    if pid is None:
        print("服务未运行 (PID文件不存在)")
        return False
    
    try:
        # 检查进程是否存在
        os.kill(pid, 0)
        print(f"服务正在运行 (PID: {pid})")
        return True
    except OSError:
        print(f"服务未运行 (PID {pid} 不存在)")
        # 清理无效的PID文件
        try:
            os.remove(pid_file)
        except OSError:
            pass
        return False


def stop_service(pid_file: str):
    """停止服务"""
    pid = read_pid_file(pid_file)
    
    if pid is None:
        print("服务未运行")
        return
    
    try:
        import signal
        os.kill(pid, signal.SIGTERM)
        print(f"已发送停止信号给进程 {pid}")
        
        # 等待进程结束
        import time
        for i in range(30):  # 等待最多30秒
            try:
                os.kill(pid, 0)
                time.sleep(1)
            except OSError:
                print("服务已停止")
                try:
                    os.remove(pid_file)
                except OSError:
                    pass
                return
        
        # 如果进程仍然存在，强制终止
        print("正常停止超时，强制终止进程")
        os.kill(pid, signal.SIGKILL)
        
    except OSError as e:
        print(f"停止服务失败: {e}")


def run_daemon(config_path: str, pid_file: str):
    """后台运行模式"""
    try:
        # 检查是否已经在运行
        if check_service_status(pid_file):
            print("服务已在运行")
            return
        
        # 创建子进程
        pid = os.fork()
        
        if pid > 0:
            # 父进程
            print(f"调度器服务已启动 (PID: {pid})")
            return
        
        # 子进程
        os.setsid()  # 创建新会话
        
        # 第二次fork
        pid = os.fork()
        if pid > 0:
            sys.exit(0)
        
        # 重定向标准输入输出
        sys.stdout.flush()
        sys.stderr.flush()
        
        # 写入PID文件
        write_pid_file(pid_file)
        
        # 运行服务
        asyncio.run(run_service(config_path))
        
    except OSError as e:
        print(f"创建守护进程失败: {e}")
        sys.exit(1)


async def run_service(config_path: str):
    """运行服务"""
    logger = logging.getLogger(__name__)
    
    try:
        # 创建服务实例
        service = SchedulerService(config_path)
        
        # 初始化服务
        if not await service.initialize():
            logger.error("服务初始化失败")
            sys.exit(1)
        
        # 启动服务
        await service.start()
        
        # 运行服务
        await service.run()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"服务运行异常: {e}")
        raise
    finally:
        if 'service' in locals():
            await service.shutdown()


def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 转换为绝对路径
    config_path = str(project_root / args.config)
    pid_file = str(project_root / args.pid_file)
    
    # 检查配置文件
    if not os.path.exists(config_path):
        print(f"错误: 配置文件不存在: {config_path}")
        sys.exit(1)
    
    # 处理不同的命令
    if args.status:
        check_service_status(pid_file)
        return
    
    if args.stop:
        stop_service(pid_file)
        return
    
    # 启动服务
    print("=" * 50)
    print("金融数据服务 - 任务调度器")
    print("=" * 50)
    print(f"配置文件: {config_path}")
    print(f"日志级别: {args.log_level}")
    print(f"PID文件: {pid_file}")
    print("=" * 50)
    
    if args.daemon:
        print("启动后台服务...")
        if os.name == 'nt':  # Windows
            print("Windows系统不支持后台模式，使用前台模式启动")
            asyncio.run(run_service(config_path))
        else:  # Unix/Linux
            run_daemon(config_path, pid_file)
    else:
        print("启动前台服务...")
        write_pid_file(pid_file)
        try:
            asyncio.run(run_service(config_path))
        finally:
            # 清理PID文件
            try:
                os.remove(pid_file)
            except OSError:
                pass


if __name__ == "__main__":
    main()