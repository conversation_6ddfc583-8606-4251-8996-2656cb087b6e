#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "failover/failover_manager.h"
#include "failover/health_checker.h"
#include "failover/data_sync.h"
#include <thread>
#include <chrono>

using namespace financial_data;

class FailoverManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.local_node_id = "node1";
        config_.peer_nodes = {"node2", "node3"};
        config_.heartbeat_interval_ms = 100;
        config_.heartbeat_timeout_ms = 500;
        config_.failover_timeout_ms = 1000;
    }
    
    FailoverConfig config_;
};

TEST_F(FailoverManagerTest, Initialization) {
    FailoverManager manager(config_);
    
    EXPECT_EQ(manager.GetCurrentRole(), NodeRole::UNKNOWN);
    EXPECT_EQ(manager.GetCurrentStatus(), NodeStatus::UNKNOWN);
    EXPECT_FALSE(manager.IsPrimary());
    EXPECT_FALSE(manager.IsSecondary());
}

TEST_F(FailoverManagerTest, StartAndStop) {
    FailoverManager manager(config_);
    
    EXPECT_TRUE(manager.Start());
    EXPECT_TRUE(manager.IsSecondary());  // 应该启动为备节点
    
    // 等待一段时间让线程启动
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    manager.Stop();
    EXPECT_EQ(manager.GetCurrentStatus(), NodeStatus::DISCONNECTED);
}

TEST_F(FailoverManagerTest, ManualFailover) {
    FailoverManager manager(config_);
    
    bool failover_called = false;
    NodeRole old_role, new_role;
    
    manager.SetFailoverCallback([&](NodeRole old_r, NodeRole new_r) {
        failover_called = true;
        old_role = old_r;
        new_role = new_r;
    });
    
    manager.Start();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    EXPECT_TRUE(manager.TriggerFailover("Manual test failover"));
    
    // 等待故障转移完成
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    EXPECT_TRUE(failover_called);
    EXPECT_EQ(old_role, NodeRole::SECONDARY);
    EXPECT_EQ(new_role, NodeRole::PRIMARY);
    EXPECT_TRUE(manager.IsPrimary());
    
    manager.Stop();
}

TEST_F(FailoverManagerTest, ClusterStatus) {
    FailoverManager manager(config_);
    
    manager.Start();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto cluster_status = manager.GetClusterStatus();
    EXPECT_EQ(cluster_status.size(), 3);  // 本地节点 + 2个对等节点
    
    // 检查本地节点信息
    bool found_local = false;
    for (const auto& node : cluster_status) {
        if (node.node_id == config_.local_node_id) {
            found_local = true;
            EXPECT_EQ(node.role, NodeRole::SECONDARY);
            EXPECT_EQ(node.status, NodeStatus::HEALTHY);
            break;
        }
    }
    EXPECT_TRUE(found_local);
    
    manager.Stop();
}

class HealthCheckerTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.check_interval_ms = 100;
        config_.component_timeout_ms = 500;
    }
    
    HealthCheckConfig config_;
};

TEST_F(HealthCheckerTest, Initialization) {
    HealthChecker checker(config_);
    
    EXPECT_FALSE(checker.IsSystemHealthy());  // 初始状态未知
}

TEST_F(HealthCheckerTest, StartAndStop) {
    HealthChecker checker(config_);
    
    EXPECT_TRUE(checker.Start());
    
    // 等待健康检查运行
    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    
    checker.Stop();
}

TEST_F(HealthCheckerTest, ComponentRegistration) {
    HealthChecker checker(config_);
    
    bool check_called = false;
    checker.RegisterComponent("test_component", [&]() {
        check_called = true;
        ComponentHealth health;
        health.overall_status = HealthStatus::HEALTHY;
        return health;
    });
    
    checker.Start();
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    EXPECT_TRUE(check_called);
    
    auto component_health = checker.GetComponentHealth("test_component");
    EXPECT_EQ(component_health.overall_status, HealthStatus::HEALTHY);
    
    checker.Stop();
}

TEST_F(HealthCheckerTest, SystemHealthCalculation) {
    HealthChecker checker(config_);
    
    // 注册健康组件
    checker.RegisterComponent("healthy_component", []() {
        ComponentHealth health;
        health.overall_status = HealthStatus::HEALTHY;
        return health;
    });
    
    // 注册警告组件
    checker.RegisterComponent("warning_component", []() {
        ComponentHealth health;
        health.overall_status = HealthStatus::WARNING;
        return health;
    });
    
    checker.Start();
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    auto system_health = checker.GetSystemHealth();
    EXPECT_EQ(system_health.overall_status, HealthStatus::WARNING);  // 最差状态
    EXPECT_EQ(system_health.healthy_components, 1);
    EXPECT_EQ(system_health.warning_components, 1);
    
    checker.Stop();
}

TEST_F(HealthCheckerTest, MetricUpdate) {
    HealthChecker checker(config_);
    
    checker.UpdateMetric("test_component", "cpu_usage", 75.0, 80.0, 95.0);
    
    auto component_health = checker.GetComponentHealth("test_component");
    EXPECT_EQ(component_health.overall_status, HealthStatus::HEALTHY);
    EXPECT_EQ(component_health.metrics.size(), 1);
    EXPECT_EQ(component_health.metrics[0].value, 75.0);
    
    // 更新到警告阈值
    checker.UpdateMetric("test_component", "cpu_usage", 85.0, 80.0, 95.0);
    component_health = checker.GetComponentHealth("test_component");
    EXPECT_EQ(component_health.overall_status, HealthStatus::WARNING);
    
    // 更新到临界阈值
    checker.UpdateMetric("test_component", "cpu_usage", 98.0, 80.0, 95.0);
    component_health = checker.GetComponentHealth("test_component");
    EXPECT_EQ(component_health.overall_status, HealthStatus::CRITICAL);
}

class DataSyncManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.peer_nodes = {"node2", "node3"};
        config_.sync_interval_ms = 100;
        config_.sync_timeout_ms = 1000;
    }
    
    DataSyncConfig config_;
};

TEST_F(DataSyncManagerTest, Initialization) {
    DataSyncManager sync_manager(config_);
    
    EXPECT_EQ(sync_manager.GetPendingOperations(), 0);
    
    auto stats = sync_manager.GetSyncStats();
    EXPECT_EQ(stats.total_operations, 0);
    EXPECT_EQ(stats.successful_operations, 0);
    EXPECT_EQ(stats.failed_operations, 0);
}

TEST_F(DataSyncManagerTest, StartAndStop) {
    DataSyncManager sync_manager(config_);
    
    EXPECT_TRUE(sync_manager.Start());
    
    // 等待同步线程启动
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    sync_manager.Stop();
}

TEST_F(DataSyncManagerTest, DataProviderRegistration) {
    DataSyncManager sync_manager(config_);
    
    bool provider_called = false;
    sync_manager.RegisterDataProvider("test_data", [&](const std::string& data_type) {
        provider_called = true;
        EXPECT_EQ(data_type, "test_data");
        return std::vector<uint8_t>{1, 2, 3, 4, 5};
    });
    
    sync_manager.Start();
    
    EXPECT_TRUE(sync_manager.RequestSync("node2", "test_data"));
    
    // 等待处理
    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    
    EXPECT_TRUE(provider_called);
    
    auto stats = sync_manager.GetSyncStats();
    EXPECT_GT(stats.total_operations, 0);
    
    sync_manager.Stop();
}

TEST_F(DataSyncManagerTest, SyncToAllNodes) {
    DataSyncManager sync_manager(config_);
    
    int provider_call_count = 0;
    sync_manager.RegisterDataProvider("test_data", [&](const std::string& data_type) {
        provider_call_count++;
        return std::vector<uint8_t>{1, 2, 3, 4, 5};
    });
    
    sync_manager.Start();
    
    EXPECT_TRUE(sync_manager.RequestSyncToAll("test_data"));
    
    // 等待处理
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    EXPECT_EQ(provider_call_count, 2);  // 应该为每个对等节点调用一次
    
    auto stats = sync_manager.GetSyncStats();
    EXPECT_EQ(stats.total_operations, 2);
    
    sync_manager.Stop();
}

TEST_F(DataSyncManagerTest, FullSync) {
    DataSyncManager sync_manager(config_);
    
    int tick_provider_calls = 0;
    int level2_provider_calls = 0;
    
    sync_manager.RegisterDataProvider("tick_data", [&](const std::string&) {
        tick_provider_calls++;
        return std::vector<uint8_t>{1, 2, 3};
    });
    
    sync_manager.RegisterDataProvider("level2_data", [&](const std::string&) {
        level2_provider_calls++;
        return std::vector<uint8_t>{4, 5, 6};
    });
    
    sync_manager.Start();
    
    EXPECT_TRUE(sync_manager.TriggerFullSync());
    
    // 等待处理
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 每种数据类型应该为每个对等节点调用一次
    EXPECT_EQ(tick_provider_calls, 2);
    EXPECT_EQ(level2_provider_calls, 2);
    
    auto stats = sync_manager.GetSyncStats();
    EXPECT_EQ(stats.total_operations, 4);  // 2种数据类型 × 2个节点
    
    sync_manager.Stop();
}

// 集成测试
class FailoverIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        failover_config_.local_node_id = "node1";
        failover_config_.peer_nodes = {"node2"};
        failover_config_.heartbeat_interval_ms = 100;
        failover_config_.heartbeat_timeout_ms = 500;
        
        health_config_.check_interval_ms = 100;
        
        sync_config_.peer_nodes = {"node2"};
        sync_config_.sync_interval_ms = 100;
    }
    
    FailoverConfig failover_config_;
    HealthCheckConfig health_config_;
    DataSyncConfig sync_config_;
};

TEST_F(FailoverIntegrationTest, FailoverWithHealthCheckAndSync) {
    FailoverManager failover_manager(failover_config_);
    HealthChecker health_checker(health_config_);
    DataSyncManager sync_manager(sync_config_);
    
    // 设置健康检查回调
    failover_manager.SetHealthCheckCallback([&]() {
        return health_checker.IsSystemHealthy();
    });
    
    // 设置数据同步回调
    failover_manager.SetDataSyncCallback([&](const std::string& peer_node) {
        return sync_manager.RequestSyncToAll("config_data");
    });
    
    // 注册数据提供者
    sync_manager.RegisterDataProvider("config_data", [](const std::string&) {
        return std::vector<uint8_t>{1, 2, 3, 4, 5};
    });
    
    // 启动所有组件
    EXPECT_TRUE(health_checker.Start());
    EXPECT_TRUE(sync_manager.Start());
    EXPECT_TRUE(failover_manager.Start());
    
    // 等待初始化
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 触发故障转移
    EXPECT_TRUE(failover_manager.TriggerFailover("Integration test"));
    
    // 等待故障转移完成
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    EXPECT_TRUE(failover_manager.IsPrimary());
    
    // 停止所有组件
    failover_manager.Stop();
    sync_manager.Stop();
    health_checker.Stop();
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}