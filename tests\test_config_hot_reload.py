"""
Enhanced hot reload functionality tests
"""

import unittest
import tempfile
import os
import json
import time
import threading
import shutil
from unittest.mock import Mock, patch, MagicMock
from src.config.config_manager_python import (
    PythonConfigManager, ConfigChangeType, ConfigChangeEvent,
    ConfigValidator, ConfigChangeListener, ValidationResult,
    ConfigFileWatcher
)


class TestConfigValidator(ConfigValidator):
    """测试用配置验证器"""
    
    def __init__(self, is_valid=True, errors=None, warnings=None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
    
    def validate(self, config):
        result = ValidationResult()
        result.is_valid = self.is_valid
        result.errors = self.errors.copy()
        result.warnings = self.warnings.copy()
        return result
    
    def get_validator_name(self):
        return "TestConfigValidator"


class TestConfigChangeListener(ConfigChangeListener):
    """测试用配置变更监听器"""
    
    def __init__(self):
        self.events = []
        self.lock = threading.Lock()
    
    def on_config_changed(self, event):
        with self.lock:
            self.events.append(event)
    
    def get_events_by_type(self, event_type):
        with self.lock:
            return [e for e in self.events if e.type == event_type]
    
    def clear_events(self):
        with self.lock:
            self.events.clear()


class TestConfigHotReload(unittest.TestCase):
    """配置热更新功能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config = {
            "server": {
                "host": "localhost",
                "port": 8080,
                "threads": 4
            },
            "redis": {
                "host": "127.0.0.1",
                "port": 6379,
                "database": 0
            },
            "features": {
                "hot_reload": True,
                "backup_on_change": True
            }
        }
        
        # 创建临时目录和配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
        self.backup_dir = os.path.join(self.temp_dir, "backups")
        
        with open(self.config_file, 'w') as f:
            json.dump(self.test_config, f, indent=4)
        
        # 创建配置管理器实例
        self.config_manager = PythonConfigManager()
        self.config_manager.shutdown()  # 重置状态
        
        # 设置备份目录
        self.config_manager.set_backup_directory(self.backup_dir)
        self.config_manager.set_backup_enabled(True)
        self.config_manager.set_max_backups(3)
    
    def tearDown(self):
        """测试后清理"""
        self.config_manager.shutdown()
        
        # 清理临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_enhanced_hot_reload_basic(self):
        """测试基本热更新功能"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 创建监听器
        listener = TestConfigChangeListener()
        self.config_manager.register_change_listener(listener)
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        self.assertTrue(self.config_manager.is_hot_reload_enabled())
        
        # 等待文件监控器启动
        time.sleep(0.1)
        
        # 修改配置文件
        modified_config = self.test_config.copy()
        modified_config['server']['host'] = 'modified.example.com'
        modified_config['server']['port'] = 9999
        modified_config['new_section'] = {'key': 'value'}
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 等待热更新生效
        time.sleep(0.8)  # 考虑防抖延迟
        
        # 验证配置已更新
        self.assertEqual(self.config_manager.get_value("server.host"), "modified.example.com")
        self.assertEqual(self.config_manager.get_value("server.port"), 9999)
        self.assertEqual(self.config_manager.get_value("new_section.key"), "value")
        
        # 验证监听器收到重新加载通知
        reload_events = listener.get_events_by_type(ConfigChangeType.RELOADED)
        self.assertGreater(len(reload_events), 0)
        
        self.config_manager.unregister_change_listener(listener)
    
    def test_hot_reload_with_validation_failure(self):
        """测试热更新时验证失败的处理"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 注册一个会失败的验证器
        failing_validator = TestConfigValidator(
            is_valid=False,
            errors=["Test validation error"]
        )
        self.config_manager.register_validator("server", failing_validator)
        
        # 启用安全重载
        self.config_manager.set_safe_reload_enabled(True)
        
        # 创建重载回调
        reload_results = []
        def reload_callback(success, message):
            reload_results.append((success, message))
        
        self.config_manager.register_reload_callback(reload_callback)
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        
        # 等待文件监控器启动
        time.sleep(0.1)
        
        # 记录原始值
        original_host = self.config_manager.get_value("server.host")
        
        # 修改配置文件（会触发验证失败）
        modified_config = self.test_config.copy()
        modified_config['server']['host'] = 'invalid.example.com'
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 等待热更新处理
        time.sleep(0.8)
        
        # 验证配置没有被更新（因为验证失败）
        self.assertEqual(self.config_manager.get_value("server.host"), original_host)
        
        # 验证重载回调收到失败通知
        self.assertGreater(len(reload_results), 0)
        success, message = reload_results[-1]
        self.assertFalse(success)
        self.assertIn("validation failed", message.lower())
        
        self.config_manager.unregister_reload_callback(reload_callback)
    
    def test_hot_reload_with_invalid_json(self):
        """测试热更新时JSON格式错误的处理"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 创建重载回调
        reload_results = []
        def reload_callback(success, message):
            reload_results.append((success, message))
        
        self.config_manager.register_reload_callback(reload_callback)
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        
        # 等待文件监控器启动
        time.sleep(0.1)
        
        # 记录原始值
        original_host = self.config_manager.get_value("server.host")
        
        # 写入无效的JSON
        with open(self.config_file, 'w') as f:
            f.write('{ "invalid": json, }')
        
        # 等待热更新处理
        time.sleep(0.8)
        
        # 验证配置没有被更新
        self.assertEqual(self.config_manager.get_value("server.host"), original_host)
        
        # 验证重载回调收到失败通知
        self.assertGreater(len(reload_results), 0)
        success, message = reload_results[-1]
        self.assertFalse(success)
        self.assertIn("invalid json", message.lower())
        
        self.config_manager.unregister_reload_callback(reload_callback)
    
    def test_config_backup_functionality(self):
        """测试配置备份功能"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        
        # 等待文件监控器启动
        time.sleep(0.1)
        
        # 修改配置文件触发备份
        modified_config = self.test_config.copy()
        modified_config['server']['port'] = 9999
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 等待热更新和备份
        time.sleep(0.8)
        
        # 验证备份目录存在且包含备份文件
        self.assertTrue(os.path.exists(self.backup_dir))
        backup_files = [f for f in os.listdir(self.backup_dir) 
                       if f.startswith("config_backup_") and f.endswith(".json")]
        self.assertGreater(len(backup_files), 0)
        
        # 验证备份文件内容
        backup_file = os.path.join(self.backup_dir, backup_files[0])
        with open(backup_file, 'r') as f:
            backup_config = json.load(f)
        
        # 备份应该包含原始配置
        self.assertEqual(backup_config['server']['port'], 8080)
    
    def test_backup_cleanup(self):
        """测试备份清理功能"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 设置最大备份数为2
        self.config_manager.set_max_backups(2)
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        
        # 等待文件监控器启动
        time.sleep(0.1)
        
        # 创建多个备份（通过多次修改配置）
        for i in range(4):
            modified_config = self.test_config.copy()
            modified_config['server']['port'] = 8080 + i
            
            with open(self.config_file, 'w') as f:
                json.dump(modified_config, f, indent=4)
            
            time.sleep(0.8)  # 等待处理
        
        # 验证只保留了最大数量的备份
        backup_files = [f for f in os.listdir(self.backup_dir) 
                       if f.startswith("config_backup_") and f.endswith(".json")]
        self.assertLessEqual(len(backup_files), 2)
    
    def test_file_watcher_debounce(self):
        """测试文件监控防抖功能"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 创建监听器
        listener = TestConfigChangeListener()
        self.config_manager.register_change_listener(listener)
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        
        # 等待文件监控器启动
        time.sleep(0.1)
        
        # 快速连续修改配置文件多次
        for i in range(5):
            modified_config = self.test_config.copy()
            modified_config['server']['port'] = 8080 + i
            
            with open(self.config_file, 'w') as f:
                json.dump(modified_config, f, indent=4)
            
            time.sleep(0.05)  # 短间隔
        
        # 等待防抖处理完成
        time.sleep(1.0)
        
        # 验证只触发了少量的重载事件（防抖生效）
        reload_events = listener.get_events_by_type(ConfigChangeType.RELOADED)
        self.assertLessEqual(len(reload_events), 2)  # 应该远少于5次
        
        # 验证最终配置是最后一次修改的结果
        self.assertEqual(self.config_manager.get_value("server.port"), 8084)
        
        self.config_manager.unregister_change_listener(listener)
    
    def test_checksum_based_reload_skip(self):
        """测试基于校验和的重复加载跳过"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 创建重载回调
        reload_count = [0]
        def reload_callback(success, message):
            if success:
                reload_count[0] += 1
        
        self.config_manager.register_reload_callback(reload_callback)
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        
        # 等待文件监控器启动
        time.sleep(0.1)
        
        # 第一次修改配置
        modified_config = self.test_config.copy()
        modified_config['server']['port'] = 9999
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        time.sleep(0.8)
        initial_reload_count = reload_count[0]
        
        # 写入相同内容（不应该触发重载）
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        time.sleep(0.8)
        
        # 验证重载次数没有增加
        self.assertEqual(reload_count[0], initial_reload_count)
        
        self.config_manager.unregister_reload_callback(reload_callback)
    
    def test_safe_reload_mode(self):
        """测试安全重载模式"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 启用安全重载
        self.config_manager.set_safe_reload_enabled(True)
        
        # 注册验证器
        validator = TestConfigValidator(is_valid=True)
        self.config_manager.register_validator("server", validator)
        
        # 启用热更新
        self.config_manager.enable_hot_reload(True)
        
        # 等待文件监控器启动
        time.sleep(0.1)
        
        # 修改配置文件
        modified_config = self.test_config.copy()
        modified_config['server']['host'] = 'safe.example.com'
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 等待热更新
        time.sleep(0.8)
        
        # 验证配置已更新（验证通过）
        self.assertEqual(self.config_manager.get_value("server.host"), "safe.example.com")
        
        # 现在让验证器失败
        validator.is_valid = False
        validator.errors = ["Validation failed"]
        
        # 再次修改配置
        modified_config['server']['host'] = 'unsafe.example.com'
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 等待热更新
        time.sleep(0.8)
        
        # 验证配置没有更新（验证失败）
        self.assertEqual(self.config_manager.get_value("server.host"), "safe.example.com")
    
    @patch('src.config.config_manager_python.Observer')
    def test_fallback_to_polling_watcher(self, mock_observer):
        """测试当watchdog不可用时回退到轮询监控"""
        # 模拟watchdog不可用
        mock_observer.side_effect = ImportError("watchdog not available")
        
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 启用热更新（应该回退到轮询模式）
        self.config_manager.enable_hot_reload(True)
        self.assertTrue(self.config_manager.is_hot_reload_enabled())
        
        # 验证传统文件监控器启动
        self.assertTrue(self.config_manager._file_watcher_running)
    
    def test_reload_callback_management(self):
        """测试重载回调管理"""
        # 初始化配置管理器
        self.assertTrue(self.config_manager.initialize(self.config_file))
        
        # 创建多个回调
        callback_results = []
        
        def callback1(success, message):
            callback_results.append(('callback1', success, message))
        
        def callback2(success, message):
            callback_results.append(('callback2', success, message))
        
        # 注册回调
        self.config_manager.register_reload_callback(callback1)
        self.config_manager.register_reload_callback(callback2)
        
        # 修改配置文件内容以触发重载
        modified_config = self.test_config.copy()
        modified_config['test_key'] = 'test_value'
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 触发重载
        self.config_manager.load_from_file(self.config_file)
        
        # 验证两个回调都被调用
        callback1_calls = [r for r in callback_results if r[0] == 'callback1']
        callback2_calls = [r for r in callback_results if r[0] == 'callback2']
        
        self.assertGreater(len(callback1_calls), 0)
        self.assertGreater(len(callback2_calls), 0)
        
        # 注销一个回调
        callback_results.clear()
        self.config_manager.unregister_reload_callback(callback1)
        
        # 修改配置文件再次触发重载
        modified_config['test_key2'] = 'test_value2'
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=4)
        
        # 再次触发重载
        self.config_manager.load_from_file(self.config_file)
        
        # 验证只有callback2被调用
        callback1_calls = [r for r in callback_results if r[0] == 'callback1']
        callback2_calls = [r for r in callback_results if r[0] == 'callback2']
        
        self.assertEqual(len(callback1_calls), 0)
        self.assertGreater(len(callback2_calls), 0)


class TestConfigFileWatcher(unittest.TestCase):
    """配置文件监控器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
        
        test_config = {"test": "value"}
        with open(self.config_file, 'w') as f:
            json.dump(test_config, f)
        
        self.mock_config_manager = Mock()
        self.mock_config_manager._create_backup.return_value = "backup_path"
        self.mock_config_manager.load_config.return_value = True
        self.mock_config_manager._restore_from_backup.return_value = True
        
        self.file_watcher = ConfigFileWatcher(self.mock_config_manager, self.config_file)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_file_integrity_validation(self):
        """测试文件完整性验证"""
        # 有效JSON文件
        self.assertTrue(self.file_watcher._validate_file_integrity())
        
        # 无效JSON文件
        with open(self.config_file, 'w') as f:
            f.write('{ invalid json }')
        
        self.assertFalse(self.file_watcher._validate_file_integrity())
        
        # 文件不存在
        os.remove(self.config_file)
        self.assertFalse(self.file_watcher._validate_file_integrity())
    
    def test_debounce_mechanism(self):
        """测试防抖机制"""
        # 模拟快速连续的文件修改事件
        mock_event = Mock()
        mock_event.is_directory = False
        mock_event.src_path = self.config_file
        
        # 第一次修改
        self.file_watcher.on_modified(mock_event)
        
        # 快速连续修改（应该被防抖）
        time.sleep(0.1)
        self.file_watcher.on_modified(mock_event)
        
        time.sleep(0.1)
        self.file_watcher.on_modified(mock_event)
        
        # 等待防抖延迟
        time.sleep(0.6)
        
        # 验证配置管理器的load_config只被调用了合理次数
        # 由于防抖机制，调用次数应该少于事件次数
        self.assertLessEqual(self.mock_config_manager.load_config.call_count, 2)


if __name__ == '__main__':
    # 设置日志级别
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    unittest.main()