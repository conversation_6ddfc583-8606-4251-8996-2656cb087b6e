@echo off
echo Building Financial Data Service...

if not exist build mkdir build

echo Configuring CMake...
cmake -B build -DCMAKE_BUILD_TYPE=Release

if %errorlevel% neq 0 (
    echo CMake configuration failed!
    exit /b 1
)

echo Building project...
cmake --build build --config Release -j

if %errorlevel% neq 0 (
    echo Build failed!
    exit /b 1
)

echo Build completed successfully!
echo.
echo Running tests...
cd build
ctest -C Release --output-on-failure

if %errorlevel% neq 0 (
    echo Tests failed!
    exit /b 1
)

echo All tests passed!
cd ..
echo Build and test completed successfully!