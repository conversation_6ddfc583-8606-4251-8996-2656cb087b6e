"""
PyTDX Collector Storage Integration Tests

Tests the integration between PyTDX collector and the storage manager,
verifying that data is properly converted and stored.
"""

import asyncio
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from collectors.pytdx_collector import (
    PyTDXCollector, 
    PyTDXConfig, 
    ArchiverConfig,
    MockStorageManager,
    STORAGE_MANAGER_AVAILABLE
)

if STORAGE_MANAGER_AVAILABLE:
    from storage.python_storage_manager import (
        StorageManagerFactory,
        StandardTickPython
    )


class TestPyTDXStorageIntegration:
    """Test PyTDX collector integration with storage manager"""
    
    @pytest.fixture
    def mock_storage_manager(self):
        """Create a mock storage manager for testing"""
        storage = MockStorageManager()
        return storage
    
    @pytest.fixture
    def pytdx_config(self):
        """Create PyTDX configuration for testing"""
        archiver_config = ArchiverConfig(
            enable_data_validation=True,
            enable_deduplication=True,
            archive_batch_size=100,
            max_retry_attempts=2
        )
        
        config = PyTDXConfig(
            batch_size=50,
            concurrent_requests=2,
            archive_enabled=True,
            archiver_config=archiver_config
        )
        return config
    
    @pytest.fixture
    def sample_k_data(self):
        """Create sample K-line data for testing"""
        dates = pd.date_range(start='2024-01-01', periods=10, freq='D')
        data = {
            'open': np.random.uniform(10, 20, 10),
            'high': np.random.uniform(15, 25, 10),
            'low': np.random.uniform(8, 15, 10),
            'close': np.random.uniform(10, 20, 10),
            'volume': np.random.randint(1000, 10000, 10),
            'amount': np.random.uniform(10000, 100000, 10)
        }
        
        # Ensure OHLC relationships are valid
        for i in range(10):
            data['high'][i] = max(data['open'][i], data['high'][i], data['low'][i], data['close'][i])
            data['low'][i] = min(data['open'][i], data['high'][i], data['low'][i], data['close'][i])
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    @pytest.fixture
    def sample_tick_data(self):
        """Create sample tick data for testing"""
        times = pd.date_range(start='2024-01-01 09:30:00', periods=100, freq='1S')
        data = {
            'price': np.random.uniform(10, 20, 100),
            'volume': np.random.randint(100, 1000, 100),
            'direction': np.random.choice([0, 1, 2], 100)  # 0: buy, 1: sell, 2: unknown
        }
        
        df = pd.DataFrame(data, index=times)
        return df
    
    @pytest.fixture
    def sample_realtime_quotes(self):
        """Create sample realtime quotes for testing"""
        quotes = []
        for i in range(5):
            quote = {
                'code': f'00000{i}',
                'name': f'Test Stock {i}',
                'price': 15.0 + i,
                'last_close': 14.0 + i,
                'open': 14.5 + i,
                'high': 16.0 + i,
                'low': 13.5 + i,
                'volume': 10000 + i * 1000,
                'amount': 150000 + i * 10000,
                'bid1': 14.9 + i,
                'ask1': 15.1 + i,
                'bid1_vol': 1000,
                'ask1_vol': 1000,
                'timestamp': datetime.now().isoformat()
            }
            quotes.append(quote)
        return quotes
    
    @pytest.mark.asyncio
    async def test_batch_store_k_data(self, mock_storage_manager, pytdx_config, sample_k_data):
        """Test batch storage of K-line data"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        # Test batch storage
        symbols = ['000001', '000002']
        data_list = [sample_k_data, sample_k_data.copy()]
        
        success = await collector.batch_store_data_async(data_list, symbols, 'kline_D')
        
        assert success is True
        
        # Verify data was stored
        stored_data = mock_storage_manager.get_stored_data()
        assert len(stored_data) > 0
        
        # Verify data format
        for tick in stored_data:
            assert 'timestamp_ns' in tick
            assert 'symbol' in tick
            assert 'exchange' in tick
            assert 'last_price' in tick
            assert 'data_type' in tick
            assert tick['data_type'] == 'kline_D'
            assert tick['exchange'] == 'PYTDX'
            assert tick['data_source'] == 'pytdx'
            assert tick['collection_method'] == 'historical'
    
    @pytest.mark.asyncio
    async def test_batch_store_tick_data(self, mock_storage_manager, pytdx_config, sample_tick_data):
        """Test batch storage of tick data"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        # Test batch storage
        symbols = ['000001']
        data_list = [sample_tick_data]
        
        success = await collector.batch_store_data_async(data_list, symbols, 'tick')
        
        assert success is True
        
        # Verify data was stored
        stored_data = mock_storage_manager.get_stored_data()
        assert len(stored_data) > 0
        
        # Verify data format
        for tick in stored_data:
            assert 'timestamp_ns' in tick
            assert 'symbol' in tick
            assert 'last_price' in tick
            assert tick['data_type'] == 'tick'
            assert tick['storage_layer'] == 'hot'
    
    @pytest.mark.asyncio
    async def test_store_realtime_quotes(self, mock_storage_manager, pytdx_config, sample_realtime_quotes):
        """Test storage of realtime quotes"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        success = await collector.store_realtime_data_async(sample_realtime_quotes)
        
        assert success is True
        
        # Verify data was stored
        stored_data = mock_storage_manager.get_stored_data()
        assert len(stored_data) == len(sample_realtime_quotes)
        
        # Verify data format
        for tick in stored_data:
            assert 'timestamp_ns' in tick
            assert 'symbol' in tick
            assert 'last_price' in tick
            assert 'bid_price' in tick
            assert 'ask_price' in tick
            assert tick['data_type'] == 'realtime'
            assert tick['collection_method'] == 'realtime'
    
    @pytest.mark.asyncio
    async def test_data_validation_in_conversion(self, mock_storage_manager, pytdx_config):
        """Test data validation during conversion"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        # Create invalid K-line data
        dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
        invalid_data = pd.DataFrame({
            'open': [10, -5, 15, 0, 12],  # Negative and zero prices
            'high': [15, 20, 18, 10, 15],
            'low': [8, 3, 12, 5, 10],
            'close': [12, 18, 16, 8, 14],
            'volume': [1000, -100, 2000, 500, 1500],  # Negative volume
            'amount': [15000, 25000, 20000, 8000, 18000]
        }, index=dates)
        
        # Fix OHLC relationships for valid rows
        for i in range(5):
            if invalid_data.iloc[i]['open'] > 0:
                invalid_data.iloc[i, invalid_data.columns.get_loc('high')] = max(
                    invalid_data.iloc[i]['open'], invalid_data.iloc[i]['high'], 
                    invalid_data.iloc[i]['low'], invalid_data.iloc[i]['close']
                )
                invalid_data.iloc[i, invalid_data.columns.get_loc('low')] = min(
                    invalid_data.iloc[i]['open'], invalid_data.iloc[i]['high'], 
                    invalid_data.iloc[i]['low'], invalid_data.iloc[i]['close']
                )
        
        success = await collector.batch_store_data_async([invalid_data], ['000001'], 'kline_D')
        
        assert success is True  # Should succeed but filter out invalid data
        
        # Verify only valid data was stored
        stored_data = mock_storage_manager.get_stored_data()
        
        # All stored ticks should have positive prices
        for tick in stored_data:
            assert tick['last_price'] > 0
            assert tick['volume'] >= 0
    
    @pytest.mark.asyncio
    async def test_storage_statistics(self, mock_storage_manager, pytdx_config, sample_k_data):
        """Test storage statistics tracking"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        # Store some data
        await collector.batch_store_data_async([sample_k_data], ['000001'], 'kline_D')
        
        # Get statistics
        stats = collector.get_storage_statistics()
        
        assert 'storage' in stats
        assert 'total_requests' in stats
        assert 'archived_data_points' in stats
        assert stats['archived_data_points'] > 0
    
    @pytest.mark.asyncio
    async def test_storage_health_check(self, mock_storage_manager, pytdx_config):
        """Test storage health check"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        health = collector.is_storage_healthy()
        assert health is True
    
    @pytest.mark.asyncio
    async def test_empty_data_handling(self, mock_storage_manager, pytdx_config):
        """Test handling of empty data"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        # Test with empty DataFrame
        empty_df = pd.DataFrame()
        success = await collector.batch_store_data_async([empty_df], ['000001'], 'kline_D')
        
        assert success is False  # Should return False for invalid input
        
        # Test with empty quotes list
        success = await collector.store_realtime_data_async([])
        
        assert success is True  # Should return True for empty but valid input
    
    @pytest.mark.asyncio
    async def test_data_quality_flags(self, mock_storage_manager, pytdx_config):
        """Test data quality flag generation"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        # Create data with quality issues
        dates = pd.date_range(start='2024-01-01', periods=3, freq='D')
        problematic_data = pd.DataFrame({
            'open': [10, 15, 12],
            'high': [8, 18, 15],  # First row: high < open (invalid OHLC)
            'low': [12, 12, 10],  # First row: low > open (invalid OHLC)
            'close': [11, 16, 14],
            'volume': [1000, 2000, 1500],
            'amount': [15000, 25000, 18000]
        }, index=dates)
        
        success = await collector.batch_store_data_async([problematic_data], ['000001'], 'kline_D')
        
        assert success is True
        
        # Check that invalid data was filtered out
        stored_data = mock_storage_manager.get_stored_data()
        
        # Should have fewer records due to filtering
        assert len(stored_data) < len(problematic_data)
        
        # All stored data should be valid
        for tick in stored_data:
            assert tick['high_price'] >= tick['low_price']
            assert tick['high_price'] >= tick['open_price']
            assert tick['high_price'] >= tick['close_price']
            assert tick['low_price'] <= tick['open_price']
            assert tick['low_price'] <= tick['close_price']
    
    @pytest.mark.asyncio
    async def test_batch_id_consistency(self, mock_storage_manager, pytdx_config, sample_k_data):
        """Test that batch_id is consistent within a batch"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        success = await collector.batch_store_data_async([sample_k_data], ['000001'], 'kline_D')
        
        assert success is True
        
        stored_data = mock_storage_manager.get_stored_data()
        
        # All ticks in the same batch should have the same batch_id
        if len(stored_data) > 1:
            first_batch_id = stored_data[0]['batch_id']
            for tick in stored_data:
                assert tick['batch_id'] == first_batch_id
        
        # Batch sequences should be sequential
        sequences = [tick['batch_sequence'] for tick in stored_data]
        assert sequences == list(range(len(sequences)))
    
    @pytest.mark.asyncio
    async def test_concurrent_storage_operations(self, mock_storage_manager, pytdx_config, sample_k_data):
        """Test concurrent storage operations"""
        collector = PyTDXCollector(pytdx_config, mock_storage_manager)
        
        # Create multiple concurrent storage tasks
        tasks = []
        for i in range(3):
            symbol = f'00000{i}'
            task = collector.batch_store_data_async([sample_k_data.copy()], [symbol], 'kline_D')
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        assert all(results)
        
        # Verify all data was stored
        stored_data = mock_storage_manager.get_stored_data()
        
        # Should have data from all 3 symbols
        symbols_in_data = set(tick['symbol'] for tick in stored_data)
        assert len(symbols_in_data) == 3


@pytest.mark.skipif(not STORAGE_MANAGER_AVAILABLE, reason="New storage manager not available")
class TestNewStorageManagerIntegration:
    """Test integration with the new storage manager implementation"""
    
    @pytest.mark.asyncio
    async def test_storage_manager_factory(self):
        """Test storage manager factory"""
        # Test mock storage manager creation
        storage = StorageManagerFactory.create_storage_manager("mock")
        assert storage is not None
        
        health = storage.is_healthy()
        assert health is True
        
        # Test batch storage
        test_ticks = [
            {
                'timestamp_ns': 1640995200000000000,
                'symbol': '000001',
                'exchange': 'PYTDX',
                'last_price': 15.0
            }
        ]
        
        success = await storage.store_batch_async(test_ticks)
        assert success is True
    
    @pytest.mark.asyncio
    async def test_standard_tick_python_conversion(self):
        """Test StandardTickPython data structure"""
        tick_data = {
            'timestamp_ns': 1640995200000000000,
            'symbol': '000001',
            'exchange': 'PYTDX',
            'last_price': 15.0,
            'volume': 1000
        }
        
        tick = StandardTickPython.from_dict(tick_data)
        assert tick.is_valid()
        
        tick_dict = tick.to_dict()
        assert 'batch_id' in tick_dict
        assert 'collection_timestamp_ns' in tick_dict


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])