#include <gtest/gtest.h>
#include "proto/serializer.h"
#include "proto/data_types.h"
#include "proto/market_data.pb.h"
#include <chrono>

using namespace financial_data;

class SerializerTest : public ::testing::Test {
protected:
    void SetUp() override {
        serializer_ = std::make_unique<FastSerializer>();
    }
    
    StandardTick CreateTestTick() {
        StandardTick tick;
        tick.symbol = "AAPL";
        tick.exchange = "NASDAQ";
        tick.timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();
        tick.last_price = 150.25;
        tick.volume = 1000;
        tick.turnover = 150250.0;
        tick.open_interest = 50000;
        tick.sequence = 12345;
        tick.trade_flag = "0";
        
        // 设置五档买卖盘
        tick.bids[0] = PriceLevel(150.20, 100, 5);
        tick.bids[1] = PriceLevel(150.15, 200, 8);
        tick.bids[2] = PriceLevel(150.10, 150, 3);
        tick.bids[3] = PriceLevel(150.05, 300, 12);
        tick.bids[4] = PriceLevel(150.00, 250, 7);
        
        tick.asks[0] = PriceLevel(150.30, 120, 6);
        tick.asks[1] = PriceLevel(150.35, 180, 4);
        tick.asks[2] = PriceLevel(150.40, 90, 2);
        tick.asks[3] = PriceLevel(150.45, 220, 9);
        tick.asks[4] = PriceLevel(150.50, 160, 5);
        
        return tick;
    }
    
    Level2Data CreateTestLevel2() {
        Level2Data level2;
        level2.symbol = "MSFT";
        level2.exchange = "NASDAQ";
        level2.timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();
        level2.sequence = 67890;
        
        // 添加10档买盘
        for (int i = 0; i < 10; ++i) {
            double price = 300.0 - i * 0.05;
            uint32_t volume = 100 + i * 50;
            uint32_t order_count = 3 + i;
            level2.bids.emplace_back(price, volume, order_count);
        }
        
        // 添加10档卖盘
        for (int i = 0; i < 10; ++i) {
            double price = 300.05 + i * 0.05;
            uint32_t volume = 120 + i * 30;
            uint32_t order_count = 4 + i;
            level2.asks.emplace_back(price, volume, order_count);
        }
        
        return level2;
    }
    
    std::unique_ptr<FastSerializer> serializer_;
};

// 测试StandardTick序列化和反序列化
TEST_F(SerializerTest, TickSerializationRoundTrip) {
    StandardTick original_tick = CreateTestTick();
    
    // 创建Protocol Buffer对象
    TickData pb_tick;
    
    // 序列化
    bool serialize_result = Serializer::SerializeTick(original_tick, &pb_tick);
    EXPECT_TRUE(serialize_result);
    
    // 验证序列化结果
    EXPECT_EQ(pb_tick.symbol(), original_tick.symbol);
    EXPECT_EQ(pb_tick.exchange(), original_tick.exchange);
    EXPECT_EQ(pb_tick.timestamp_ns(), original_tick.timestamp_ns);
    EXPECT_DOUBLE_EQ(pb_tick.last_price(), original_tick.last_price);
    EXPECT_EQ(pb_tick.volume(), original_tick.volume);
    EXPECT_DOUBLE_EQ(pb_tick.turnover(), original_tick.turnover);
    EXPECT_EQ(pb_tick.open_interest(), original_tick.open_interest);
    EXPECT_EQ(pb_tick.sequence(), original_tick.sequence);
    EXPECT_EQ(pb_tick.trade_flag(), original_tick.trade_flag);
    
    // 反序列化
    StandardTick deserialized_tick;
    bool deserialize_result = Serializer::DeserializeTick(pb_tick, &deserialized_tick);
    EXPECT_TRUE(deserialize_result);
    
    // 验证反序列化结果
    EXPECT_EQ(deserialized_tick.symbol, original_tick.symbol);
    EXPECT_EQ(deserialized_tick.exchange, original_tick.exchange);
    EXPECT_EQ(deserialized_tick.timestamp_ns, original_tick.timestamp_ns);
    EXPECT_DOUBLE_EQ(deserialized_tick.last_price, original_tick.last_price);
    EXPECT_EQ(deserialized_tick.volume, original_tick.volume);
    EXPECT_DOUBLE_EQ(deserialized_tick.turnover, original_tick.turnover);
    EXPECT_EQ(deserialized_tick.open_interest, original_tick.open_interest);
    EXPECT_EQ(deserialized_tick.sequence, original_tick.sequence);
    EXPECT_EQ(deserialized_tick.trade_flag, original_tick.trade_flag);
}

// 测试Level2Data序列化和反序列化
TEST_F(SerializerTest, Level2SerializationRoundTrip) {
    Level2Data original_level2 = CreateTestLevel2();
    
    // 创建Protocol Buffer对象
    Level2Data pb_level2;
    
    // 序列化
    bool serialize_result = Serializer::SerializeLevel2(original_level2, &pb_level2);
    EXPECT_TRUE(serialize_result);
    
    // 验证序列化结果
    EXPECT_EQ(pb_level2.symbol(), original_level2.symbol);
    EXPECT_EQ(pb_level2.exchange(), original_level2.exchange);
    EXPECT_EQ(pb_level2.timestamp_ns(), original_level2.timestamp_ns);
    EXPECT_EQ(pb_level2.sequence(), original_level2.sequence);
    EXPECT_EQ(pb_level2.bids_size(), static_cast<int>(original_level2.bids.size()));
    EXPECT_EQ(pb_level2.asks_size(), static_cast<int>(original_level2.asks.size()));
    
    // 验证买盘数据
    for (int i = 0; i < pb_level2.bids_size(); ++i) {
        const auto& pb_bid = pb_level2.bids(i);
        const auto& original_bid = original_level2.bids[i];
        EXPECT_DOUBLE_EQ(pb_bid.price(), original_bid.price);
        EXPECT_EQ(pb_bid.volume(), original_bid.volume);
        EXPECT_EQ(pb_bid.order_count(), original_bid.order_count);
    }
    
    // 验证卖盘数据
    for (int i = 0; i < pb_level2.asks_size(); ++i) {
        const auto& pb_ask = pb_level2.asks(i);
        const auto& original_ask = original_level2.asks[i];
        EXPECT_DOUBLE_EQ(pb_ask.price(), original_ask.price);
        EXPECT_EQ(pb_ask.volume(), original_ask.volume);
        EXPECT_EQ(pb_ask.order_count(), original_ask.order_count);
    }
    
    // 反序列化
    Level2Data deserialized_level2;
    bool deserialize_result = Serializer::DeserializeLevel2(pb_level2, &deserialized_level2);
    EXPECT_TRUE(deserialize_result);
    
    // 验证反序列化结果
    EXPECT_EQ(deserialized_level2.symbol, original_level2.symbol);
    EXPECT_EQ(deserialized_level2.exchange, original_level2.exchange);
    EXPECT_EQ(deserialized_level2.timestamp_ns, original_level2.timestamp_ns);
    EXPECT_EQ(deserialized_level2.sequence, original_level2.sequence);
    EXPECT_EQ(deserialized_level2.bids.size(), original_level2.bids.size());
    EXPECT_EQ(deserialized_level2.asks.size(), original_level2.asks.size());
    
    // 验证买盘数据
    for (size_t i = 0; i < deserialized_level2.bids.size(); ++i) {
        EXPECT_DOUBLE_EQ(deserialized_level2.bids[i].price, original_level2.bids[i].price);
        EXPECT_EQ(deserialized_level2.bids[i].volume, original_level2.bids[i].volume);
        EXPECT_EQ(deserialized_level2.bids[i].order_count, original_level2.bids[i].order_count);
    }
    
    // 验证卖盘数据
    for (size_t i = 0; i < deserialized_level2.asks.size(); ++i) {
        EXPECT_DOUBLE_EQ(deserialized_level2.asks[i].price, original_level2.asks[i].price);
        EXPECT_EQ(deserialized_level2.asks[i].volume, original_level2.asks[i].volume);
        EXPECT_EQ(deserialized_level2.asks[i].order_count, original_level2.asks[i].order_count);
    }
}

// 测试MarketDataWrapper序列化
TEST_F(SerializerTest, MarketDataWrapperSerialization) {
    StandardTick tick = CreateTestTick();
    MarketDataWrapper wrapper(tick);
    wrapper.source = "CTP";
    
    // 使用FastSerializer序列化
    bool serialize_result = serializer_->SerializeToBuffer(wrapper);
    EXPECT_TRUE(serialize_result);
    EXPECT_GT(serializer_->GetBufferSize(), 0);
    
    // 反序列化
    MarketDataWrapper deserialized_wrapper;
    bool deserialize_result = serializer_->DeserializeFromBuffer(
        serializer_->GetSerializedData(), &deserialized_wrapper);
    EXPECT_TRUE(deserialize_result);
    
    // 验证结果
    EXPECT_EQ(deserialized_wrapper.type, MarketDataWrapper::DataType::TICK);
    EXPECT_EQ(deserialized_wrapper.source, wrapper.source);
    EXPECT_EQ(deserialized_wrapper.tick_data.symbol, tick.symbol);
    EXPECT_DOUBLE_EQ(deserialized_wrapper.tick_data.last_price, tick.last_price);
}

// 测试批量数据序列化
TEST_F(SerializerTest, BatchSerialization) {
    MarketDataBatch batch;
    
    // 添加多个数据
    StandardTick tick1 = CreateTestTick();
    tick1.symbol = "AAPL";
    tick1.sequence = 1;
    batch.AddTick(tick1);
    
    Level2Data level2 = CreateTestLevel2();
    level2.symbol = "MSFT";
    level2.sequence = 2;
    batch.AddLevel2(level2);
    
    StandardTick tick2 = CreateTestTick();
    tick2.symbol = "GOOGL";
    tick2.sequence = 3;
    batch.AddTick(tick2);
    
    batch.batch_sequence = 100;
    
    // 序列化
    bool serialize_result = serializer_->SerializeToBuffer(batch);
    EXPECT_TRUE(serialize_result);
    EXPECT_GT(serializer_->GetBufferSize(), 0);
    
    // 反序列化
    MarketDataBatch deserialized_batch;
    bool deserialize_result = serializer_->DeserializeFromBuffer(
        serializer_->GetSerializedData(), &deserialized_batch);
    EXPECT_TRUE(deserialize_result);
    
    // 验证结果
    EXPECT_EQ(deserialized_batch.Size(), batch.Size());
    EXPECT_EQ(deserialized_batch.batch_sequence, batch.batch_sequence);
    
    // 验证第一个数据（Tick）
    EXPECT_EQ(deserialized_batch.data[0].type, MarketDataWrapper::DataType::TICK);
    EXPECT_EQ(deserialized_batch.data[0].tick_data.symbol, "AAPL");
    EXPECT_EQ(deserialized_batch.data[0].tick_data.sequence, 1);
    
    // 验证第二个数据（Level2）
    EXPECT_EQ(deserialized_batch.data[1].type, MarketDataWrapper::DataType::LEVEL2);
    EXPECT_EQ(deserialized_batch.data[1].level2_data.symbol, "MSFT");
    EXPECT_EQ(deserialized_batch.data[1].level2_data.sequence, 2);
    
    // 验证第三个数据（Tick）
    EXPECT_EQ(deserialized_batch.data[2].type, MarketDataWrapper::DataType::TICK);
    EXPECT_EQ(deserialized_batch.data[2].tick_data.symbol, "GOOGL");
    EXPECT_EQ(deserialized_batch.data[2].tick_data.sequence, 3);
}

// 测试零拷贝缓冲区
TEST_F(SerializerTest, ZeroCopyBuffer) {
    ZeroCopyBuffer buffer(1024);
    
    EXPECT_EQ(buffer.Capacity(), 1024);
    EXPECT_EQ(buffer.Size(), 0);
    EXPECT_NE(buffer.Data(), nullptr);
    
    // 测试容量扩展
    bool expand_result = buffer.EnsureCapacity(2048);
    EXPECT_TRUE(expand_result);
    EXPECT_GE(buffer.Capacity(), 2048);
    
    // 测试数据设置
    const char* test_data = "Hello, World!";
    size_t test_size = strlen(test_data);
    memcpy(buffer.Data(), test_data, test_size);
    buffer.SetSize(test_size);
    
    EXPECT_EQ(buffer.Size(), test_size);
    EXPECT_EQ(buffer.GetStringView(), std::string_view(test_data, test_size));
    
    auto span = buffer.GetSpan();
    EXPECT_EQ(span.size(), test_size);
    EXPECT_EQ(memcmp(span.data(), test_data, test_size), 0);
    
    // 测试重置
    buffer.Reset();
    EXPECT_EQ(buffer.Size(), 0);
}

// 测试内存池
TEST_F(SerializerTest, MemoryPool) {
    MemoryPool<MarketDataWrapper> pool(5);
    
    EXPECT_EQ(pool.TotalSize(), 5);
    EXPECT_EQ(pool.AvailableSize(), 5);
    EXPECT_EQ(pool.UsedSize(), 0);
    
    // 获取对象
    std::vector<MarketDataWrapper*> objects;
    for (int i = 0; i < 3; ++i) {
        objects.push_back(pool.Acquire());
        EXPECT_NE(objects.back(), nullptr);
    }
    
    EXPECT_EQ(pool.AvailableSize(), 2);
    EXPECT_EQ(pool.UsedSize(), 3);
    
    // 释放对象
    pool.Release(objects[0]);
    EXPECT_EQ(pool.AvailableSize(), 3);
    EXPECT_EQ(pool.UsedSize(), 2);
    
    // 测试池扩展
    for (int i = 0; i < 8; ++i) {
        objects.push_back(pool.Acquire());
    }
    
    EXPECT_EQ(pool.TotalSize(), 10); // 应该扩展
    
    // 清理
    for (auto* obj : objects) {
        if (obj) pool.Release(obj);
    }
}

// 性能测试
TEST_F(SerializerTest, PerformanceTest) {
    const int num_iterations = 1000;
    StandardTick tick = CreateTestTick();
    MarketDataWrapper wrapper(tick);
    
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_iterations; ++i) {
        serializer_->Reset();
        bool result = serializer_->SerializeToBuffer(wrapper);
        EXPECT_TRUE(result);
        
        MarketDataWrapper deserialized;
        result = serializer_->DeserializeFromBuffer(
            serializer_->GetSerializedData(), &deserialized);
        EXPECT_TRUE(result);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "Serialization/Deserialization performance: " 
              << num_iterations << " iterations in " 
              << duration.count() << " microseconds" << std::endl;
    std::cout << "Average time per iteration: " 
              << static_cast<double>(duration.count()) / num_iterations 
              << " microseconds" << std::endl;
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}