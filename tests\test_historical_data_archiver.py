"""
历史数据归档器单元测试

测试HistoricalDataArchiver类的各项功能：
- 数据验证
- 数据去重
- K线数据归档
- tick数据归档
- 错误处理
"""

import asyncio
import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src', 'collectors'))

from pytdx_collector import (
    HistoricalDataArchiver, 
    ArchiverConfig, 
    DataValidator, 
    DataDeduplicator,
    MockStorageManager
)


class TestDataValidator(unittest.TestCase):
    """数据验证器测试"""
    
    def setUp(self):
        self.config = ArchiverConfig()
        self.validator = DataValidator(self.config)
    
    def test_validate_k_data_valid(self):
        """测试有效K线数据验证"""
        # 创建有效的K线数据
        dates = pd.date_range('2024-01-01', periods=5, freq='D')
        data = pd.DataFrame({
            'open': [100.0, 101.0, 102.0, 103.0, 104.0],
            'high': [105.0, 106.0, 107.0, 108.0, 109.0],
            'low': [95.0, 96.0, 97.0, 98.0, 99.0],
            'close': [102.0, 103.0, 104.0, 105.0, 106.0],
            'volume': [1000, 1100, 1200, 1300, 1400]
        }, index=dates)
        
        # 异步测试
        async def run_test():
            result = await self.validator.validate_k_data('TEST001', data)
            self.assertEqual(len(result), 5)
            self.assertTrue(all(result['high'] >= result['low']))
            self.assertTrue(all(result['volume'] >= 0))
        
        asyncio.run(run_test())
    
    def test_validate_k_data_invalid_ohlc(self):
        """测试无效OHLC数据验证"""
        dates = pd.date_range('2024-01-01', periods=3, freq='D')
        data = pd.DataFrame({
            'open': [100.0, 101.0, 102.0],
            'high': [95.0, 96.0, 97.0],  # 高价小于开盘价，无效
            'low': [105.0, 106.0, 107.0],  # 低价大于开盘价，无效
            'close': [102.0, 103.0, 104.0],
            'volume': [1000, 1100, 1200]
        }, index=dates)
        
        async def run_test():
            result = await self.validator.validate_k_data('TEST001', data)
            self.assertEqual(len(result), 0)  # 所有数据都应该被过滤掉
        
        asyncio.run(run_test())
    
    def test_validate_tick_data_valid(self):
        """测试有效tick数据验证"""
        dates = pd.date_range('2024-01-01 09:30:00', periods=3, freq='1min')
        data = pd.DataFrame({
            'price': [100.5, 100.6, 100.4],
            'volume': [100, 200, 150]
        }, index=dates)
        
        async def run_test():
            result = await self.validator.validate_tick_data('TEST001', data)
            self.assertEqual(len(result), 3)
            self.assertTrue(all(result['price'] > 0))
            self.assertTrue(all(result['volume'] >= 0))
        
        asyncio.run(run_test())


class TestDataDeduplicator(unittest.TestCase):
    """数据去重器测试"""
    
    def setUp(self):
        self.config = ArchiverConfig()
        self.deduplicator = DataDeduplicator(self.config)
    
    def test_deduplicate_k_data(self):
        """测试K线数据去重"""
        dates = pd.date_range('2024-01-01', periods=3, freq='D')
        # 创建包含重复数据的DataFrame
        data = pd.DataFrame({
            'open': [100.0, 101.0, 101.0],  # 后两行数据相同
            'high': [105.0, 106.0, 106.0],
            'low': [95.0, 96.0, 96.0],
            'close': [102.0, 103.0, 103.0],
            'volume': [1000, 1100, 1100]
        }, index=dates)
        
        async def run_test():
            result = await self.deduplicator.deduplicate_k_data('TEST001', data)
            self.assertEqual(len(result), 2)  # 应该去掉一条重复数据
        
        asyncio.run(run_test())
    
    def test_deduplicate_tick_data(self):
        """测试tick数据去重"""
        dates = pd.date_range('2024-01-01 09:30:00', periods=4, freq='1min')
        # 创建包含重复数据的DataFrame
        data = pd.DataFrame({
            'price': [100.5, 100.6, 100.6, 100.4],  # 中间两行价格相同
            'volume': [100, 200, 200, 150]  # 中间两行成交量也相同
        }, index=dates)
        
        async def run_test():
            result = await self.deduplicator.deduplicate_tick_data('TEST001', data)
            self.assertEqual(len(result), 3)  # 应该去掉一条重复数据
        
        asyncio.run(run_test())


class TestHistoricalDataArchiver(unittest.TestCase):
    """历史数据归档器测试"""
    
    def setUp(self):
        self.config = ArchiverConfig(
            enable_data_validation=True,
            enable_deduplication=True,
            archive_batch_size=100
        )
        self.storage_manager = MockStorageManager()
        self.archiver = HistoricalDataArchiver(self.config, self.storage_manager)
    
    def test_archive_k_data_success(self):
        """测试成功归档K线数据"""
        dates = pd.date_range('2024-01-01', periods=5, freq='D')
        data = pd.DataFrame({
            'open': [100.0, 101.0, 102.0, 103.0, 104.0],
            'high': [105.0, 106.0, 107.0, 108.0, 109.0],
            'low': [95.0, 96.0, 97.0, 98.0, 99.0],
            'close': [102.0, 103.0, 104.0, 105.0, 106.0],
            'volume': [1000, 1100, 1200, 1300, 1400]
        }, index=dates)
        
        async def run_test():
            result = await self.archiver.archive_k_data('TEST001', data)
            self.assertTrue(result)
            
            # 检查统计信息
            stats = self.archiver.get_stats()
            self.assertEqual(stats['successful_archives'], 1)
            self.assertEqual(stats['total_archived'], 5)
            
            # 检查存储的数据
            self.assertEqual(len(self.storage_manager.stored_data), 5)
            
            # 验证存储的数据格式
            stored_tick = self.storage_manager.stored_data[0]
            self.assertEqual(stored_tick['symbol'], 'TEST001')
            self.assertEqual(stored_tick['exchange'], 'PYTDX')
            self.assertEqual(stored_tick['source'], 'pytdx_historical')
            self.assertGreater(stored_tick['timestamp_ns'], 0)
            self.assertGreater(stored_tick['last_price'], 0)
        
        asyncio.run(run_test())
    
    def test_archive_tick_data_success(self):
        """测试成功归档tick数据"""
        dates = pd.date_range('2024-01-01 09:30:00', periods=3, freq='1min')
        data = pd.DataFrame({
            'price': [100.5, 100.6, 100.4],
            'volume': [100, 200, 150],
            'direction': [0, 1, 0]
        }, index=dates)
        
        async def run_test():
            result = await self.archiver.archive_tick_data('TEST001', data)
            self.assertTrue(result)
            
            # 检查统计信息
            stats = self.archiver.get_stats()
            self.assertEqual(stats['successful_archives'], 1)
            self.assertEqual(stats['total_archived'], 3)
            
            # 检查存储的数据
            self.assertEqual(len(self.storage_manager.stored_data), 3)
            
            # 验证存储的数据格式
            stored_tick = self.storage_manager.stored_data[0]
            self.assertEqual(stored_tick['symbol'], 'TEST001')
            self.assertEqual(stored_tick['data_type'], 'tick')
            self.assertIn('direction', stored_tick)
        
        asyncio.run(run_test())
    
    def test_archive_empty_data(self):
        """测试归档空数据"""
        empty_data = pd.DataFrame()
        
        async def run_test():
            result = await self.archiver.archive_k_data('TEST001', empty_data)
            self.assertTrue(result)  # 空数据应该返回True但不做任何操作
            
            stats = self.archiver.get_stats()
            self.assertEqual(stats['total_archived'], 0)
        
        asyncio.run(run_test())
    
    def test_archive_invalid_data(self):
        """测试归档无效数据"""
        dates = pd.date_range('2024-01-01', periods=2, freq='D')
        # 创建无效数据（价格为负数）
        data = pd.DataFrame({
            'open': [-100.0, -101.0],
            'high': [-95.0, -96.0],
            'low': [-105.0, -106.0],
            'close': [-102.0, -103.0],
            'volume': [1000, 1100]
        }, index=dates)
        
        async def run_test():
            result = await self.archiver.archive_k_data('TEST001', data)
            self.assertFalse(result)  # 应该返回False因为所有数据都无效
            
            stats = self.archiver.get_stats()
            self.assertEqual(stats['validation_errors'], 1)
        
        asyncio.run(run_test())
    
    def test_stats_and_reset(self):
        """测试统计信息和重置功能"""
        # 初始统计信息应该为0
        stats = self.archiver.get_stats()
        self.assertEqual(stats['total_archived'], 0)
        self.assertEqual(stats['successful_archives'], 0)
        
        # 重置统计信息
        self.archiver.reset_stats()
        stats = self.archiver.get_stats()
        self.assertEqual(stats['total_archived'], 0)


class TestArchiverConfig(unittest.TestCase):
    """归档器配置测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = ArchiverConfig()
        self.assertTrue(config.enable_redis_storage)
        self.assertTrue(config.enable_clickhouse_storage)
        self.assertFalse(config.enable_s3_storage)
        self.assertEqual(config.archive_batch_size, 5000)
        self.assertTrue(config.enable_data_validation)
        self.assertTrue(config.enable_deduplication)
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = ArchiverConfig(
            archive_batch_size=1000,
            enable_data_validation=False,
            max_retry_attempts=5
        )
        self.assertEqual(config.archive_batch_size, 1000)
        self.assertFalse(config.enable_data_validation)
        self.assertEqual(config.max_retry_attempts, 5)


if __name__ == '__main__':
    # 设置日志级别
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    unittest.main()