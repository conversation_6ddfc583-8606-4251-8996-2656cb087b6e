syntax = "proto3";

package financial_data;

option cc_enable_arenas = true;
option optimize_for = SPEED;

// 价格档位信息 (protobuf版本)
message ProtoPriceLevel {
    double price = 1;           // 价格
    uint32 volume = 2;          // 数量
    uint32 order_count = 3;     // 订单数量
}

// 基础Tick数据 (protobuf版本)
message ProtoTickData {
    int64 timestamp_ns = 1;     // 纳秒级时间戳
    string symbol = 2;          // 合约代码
    string exchange = 3;        // 交易所代码
    double last_price = 4;      // 最新价
    uint64 volume = 5;          // 成交量
    double turnover = 6;        // 成交额
    uint64 open_interest = 7;   // 持仓量
    uint32 sequence = 8;        // 序列号
    string trade_flag = 9;      // 成交标志
}

// Level2深度数据 (protobuf版本)
message ProtoLevel2Data {
    int64 timestamp_ns = 1;     // 纳秒级时间戳
    string symbol = 2;          // 合约代码
    string exchange = 3;        // 交易所代码
    repeated ProtoPriceLevel bids = 4;   // 买盘档位(最多10档)
    repeated ProtoPriceLevel asks = 5;   // 卖盘档位(最多10档)
    uint32 sequence = 6;        // 序列号
}

// 完整市场数据 (protobuf版本)
message ProtoMarketData {
    oneof data_type {
        ProtoTickData tick = 1;
        ProtoLevel2Data level2 = 2;
    }
    int64 receive_time_ns = 3;  // 接收时间戳
    string source = 4;          // 数据源标识
}

// 批量市场数据 (protobuf版本)
message ProtoMarketDataBatch {
    repeated ProtoMarketData data = 1;
    int64 batch_timestamp_ns = 2;
    uint32 batch_sequence = 3;
}

// 数据订阅请求 (protobuf版本)
message ProtoSubscriptionRequest {
    repeated string symbols = 1;    // 订阅合约列表
    repeated string data_types = 2; // 数据类型: tick, level2, kline
    bool include_history = 3;       // 是否包含历史数据
    int64 start_time_ns = 4;       // 开始时间
}

// 数据订阅响应 (protobuf版本)
message ProtoSubscriptionResponse {
    bool success = 1;
    string message = 2;
    string subscription_id = 3;
}