#pragma once

#include <string>
#include <fstream>
#include <iostream>
#include "redis_storage.h"

namespace financial_data {

class RedisConfigLoader {
public:
    static bool LoadConfig(const std::string& config_file, RedisConfig& config) {
        // 简化的JSON解析实现
        // 在实际项目中应该使用专业的JSON库如nlohmann/json
        
        std::ifstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open config file: " << config_file << std::endl;
            return false;
        }
        
        // 设置默认配置用于开发环境
        config.host = "127.0.0.1";
        config.port = 6379;
        config.max_connections = 20;
        config.connection_timeout_ms = 5000;
        config.command_timeout_ms = 1000;
        config.hot_data_ttl_seconds = 7 * 24 * 3600;  // 7天
        config.batch_size = 1000;
        config.write_worker_count = 8;
        config.max_queue_size = 50000;
        config.enable_compression = true;
        config.enable_pipelining = true;
        
        // 集群配置（用于生产环境）
        config.enable_cluster = false;  // 默认单节点模式
        config.cluster_nodes = {
            "127.0.0.1:7001",
            "127.0.0.1:7002", 
            "127.0.0.1:7003",
            "127.0.0.1:7004",
            "127.0.0.1:7005",
            "127.0.0.1:7006"
        };
        
        std::cout << "Redis configuration loaded successfully" << std::endl;
        std::cout << "Mode: " << (config.enable_cluster ? "Cluster" : "Single Node") << std::endl;
        std::cout << "TTL: " << config.hot_data_ttl_seconds << " seconds" << std::endl;
        std::cout << "Workers: " << config.write_worker_count << std::endl;
        
        return true;
    }
    
    static RedisConfig GetDefaultConfig() {
        RedisConfig config;
        config.host = "127.0.0.1";
        config.port = 6379;
        config.max_connections = 20;
        config.connection_timeout_ms = 5000;
        config.command_timeout_ms = 1000;
        config.hot_data_ttl_seconds = 7 * 24 * 3600;
        config.batch_size = 1000;
        config.write_worker_count = 8;
        config.max_queue_size = 50000;
        config.enable_compression = true;
        config.enable_pipelining = true;
        config.enable_cluster = false;
        return config;
    }
    
    static RedisConfig GetClusterConfig() {
        RedisConfig config = GetDefaultConfig();
        config.enable_cluster = true;
        config.max_connections = 50;
        config.cluster_nodes = {
            "127.0.0.1:7001",
            "127.0.0.1:7002",
            "127.0.0.1:7003",
            "127.0.0.1:7004",
            "127.0.0.1:7005",
            "127.0.0.1:7006"
        };
        return config;
    }
};

} // namespace financial_data