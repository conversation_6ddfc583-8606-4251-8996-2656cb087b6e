#!/bin/bash
# Comprehensive performance test runner for Linux/Unix
# This script runs both C++ and Python performance tests

set -e

echo "========================================"
echo "Financial Data Service Performance Tests"
echo "========================================"
echo

# Set test configuration
TEST_DURATION=300
RESULTS_DIR="test_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create results directory
mkdir -p "$RESULTS_DIR"

echo "Starting performance test suite at $(date)"
echo "Results will be saved to: $RESULTS_DIR"
echo

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if performance test executable exists
if [ ! -f "./performance_tests" ]; then
    echo "ERROR: performance_tests executable not found"
    echo "Please build the project first using: cmake --build . --target performance_tests"
    echo
    exit 1
fi

# Run C++ performance tests
echo "[1/3] Running C++ Performance Tests..."
echo "====================================="
if ./performance_tests > "$RESULTS_DIR/cpp_results_$TIMESTAMP.txt" 2>&1; then
    CPP_EXIT_CODE=0
    echo "✓ C++ tests completed successfully"
else
    CPP_EXIT_CODE=$?
    echo "✗ C++ tests failed with exit code $CPP_EXIT_CODE"
fi
echo

# Run Python performance tests (if Python is available)
echo "[2/3] Running Python Performance Tests..."
echo "========================================="
if command_exists python3; then
    if python3 run_python_tests.py --export "$RESULTS_DIR/python_results_$TIMESTAMP.json" > "$RESULTS_DIR/python_output_$TIMESTAMP.txt" 2>&1; then
        PYTHON_EXIT_CODE=0
        echo "✓ Python tests completed successfully"
    else
        PYTHON_EXIT_CODE=$?
        echo "✗ Python tests failed with exit code $PYTHON_EXIT_CODE"
    fi
elif command_exists python; then
    if python run_python_tests.py --export "$RESULTS_DIR/python_results_$TIMESTAMP.json" > "$RESULTS_DIR/python_output_$TIMESTAMP.txt" 2>&1; then
        PYTHON_EXIT_CODE=0
        echo "✓ Python tests completed successfully"
    else
        PYTHON_EXIT_CODE=$?
        echo "✗ Python tests failed with exit code $PYTHON_EXIT_CODE"
    fi
else
    echo "⚠ Python not found, skipping Python tests"
    PYTHON_EXIT_CODE=0
fi
echo

# Generate summary report
echo "[3/3] Generating Summary Report..."
echo "=================================="

cat > "$RESULTS_DIR/summary_$TIMESTAMP.txt" << EOF
Performance Test Summary
========================
Test Date: $(date)

C++ Tests:
EOF

if [ $CPP_EXIT_CODE -eq 0 ]; then
    echo "  Status: PASSED" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
else
    echo "  Status: FAILED" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
fi
echo "  Results: cpp_results_$TIMESTAMP.txt" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
echo >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"

echo "Python Tests:" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
if [ $PYTHON_EXIT_CODE -eq 0 ]; then
    echo "  Status: PASSED" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
else
    echo "  Status: FAILED" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
fi
echo "  Results: python_results_$TIMESTAMP.json" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
echo >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"

# Extract key metrics from C++ results
echo "Key Performance Metrics:" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
echo "=======================" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
if [ -f "$RESULTS_DIR/cpp_results_$TIMESTAMP.txt" ]; then
    grep -E "(End-to-end latency:|Data ingestion throughput:|Concurrent connections:|Data loss:|Failover time:)" "$RESULTS_DIR/cpp_results_$TIMESTAMP.txt" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt" 2>/dev/null || true
fi

echo
echo "========================================"
echo "Performance Test Suite Complete"
echo "========================================"

# Calculate overall result
OVERALL_RESULT=0
if [ $CPP_EXIT_CODE -ne 0 ]; then
    OVERALL_RESULT=1
fi
if [ $PYTHON_EXIT_CODE -ne 0 ]; then
    OVERALL_RESULT=1
fi

if [ $OVERALL_RESULT -eq 0 ]; then
    echo "✓ All tests PASSED"
    echo
    echo "Key Requirements Validation:"
    echo "- End-to-end latency < 50μs: Check cpp_results_$TIMESTAMP.txt"
    echo "- Throughput > 1M msg/s: Check cpp_results_$TIMESTAMP.txt"
    echo "- 1000 concurrent connections: Check cpp_results_$TIMESTAMP.txt"
    echo "- Zero data loss: Check cpp_results_$TIMESTAMP.txt"
    echo "- Failover < 5s: Check cpp_results_$TIMESTAMP.txt"
else
    echo "✗ Some tests FAILED"
    echo "Please review the detailed results in $RESULTS_DIR"
fi

echo
echo "Results saved to: $RESULTS_DIR/summary_$TIMESTAMP.txt"
echo

# Make the script return the overall result
exit $OVERALL_RESULT