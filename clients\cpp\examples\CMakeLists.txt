cmake_minimum_required(VERSION 3.16)

# Find required packages
find_package(Protobuf REQUIRED)
find_package(gRPC REQUIRED)
find_package(Threads REQUIRED)

# Include SDK headers
include_directories(../include)
include_directories(../../../src/proto)

# Basic usage example
add_executable(basic_usage basic_usage.cpp)
target_link_libraries(basic_usage 
    financial_data_sdk
    gRPC::grpc++
    protobuf::libprotobuf
    Threads::Threads
)

# Streaming example
add_executable(streaming_example streaming_example.cpp)
target_link_libraries(streaming_example 
    financial_data_sdk
    gRPC::grpc++
    protobuf::libprotobuf
    Threads::Threads
)

# Performance benchmark
add_executable(performance_benchmark performance_benchmark.cpp)
target_link_libraries(performance_benchmark 
    financial_data_sdk
    gRPC::grpc++
    protobuf::libprotobuf
    Threads::Threads
)

# Async example
add_executable(async_example async_example.cpp)
target_link_libraries(async_example 
    financial_data_sdk
    gRPC::grpc++
    protobuf::libprotobuf
    Threads::Threads
)