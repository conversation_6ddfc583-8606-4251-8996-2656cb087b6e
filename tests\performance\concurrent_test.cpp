/**
 * @file concurrent_test.cpp
 * @brief Concurrent connection testing implementation
 */

#include "concurrent_test.h"
#include "test_utils.h"
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <future>
#include <iostream>
#include <algorithm>
#include <random>

namespace performance_tests {

ConcurrentTest::ConcurrentTest() : test_utils_(std::make_unique<TestUtils>()) {}

ConcurrentTest::~ConcurrentTest() = default;

ConcurrentResult ConcurrentTest::TestConcurrentWebSocketConnections(uint32_t target_connections) {
    std::cout << "    Testing " << target_connections << " concurrent WebSocket connections..." << std::endl;
    
    // Setup WebSocket server
    auto ws_server = test_utils_->CreateMockWebSocketServer();
    
    std::atomic<uint32_t> successful_connections{0};
    std::atomic<uint32_t> failed_connections{0};
    std::vector<double> connection_times;
    std::mutex connection_times_mutex;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Launch connection threads in batches to avoid overwhelming the system
    const uint32_t batch_size = 50;
    const uint32_t num_batches = (target_connections + batch_size - 1) / batch_size;
    
    std::vector<std::future<void>> connection_futures;
    
    for (uint32_t batch = 0; batch < num_batches; ++batch) {
        uint32_t batch_start = batch * batch_size;
        uint32_t batch_end = std::min(batch_start + batch_size, target_connections);
        
        for (uint32_t i = batch_start; i < batch_end; ++i) {
            connection_futures.push_back(std::async(std::launch::async, [&, i]() {
                auto client = test_utils_->CreateWebSocketTestClient();
                
                auto connect_start = std::chrono::high_resolution_clock::now();
                
                try {
                    bool connected = client->Connect("ws://localhost:8080/market-data");
                    
                    auto connect_end = std::chrono::high_resolution_clock::now();
                    auto connect_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                        connect_end - connect_start).count();
                    
                    if (connected) {
                        successful_connections.fetch_add(1);
                        
                        {
                            std::lock_guard<std::mutex> lock(connection_times_mutex);
                            connection_times.push_back(static_cast<double>(connect_time));
                        }
                        
                        // Subscribe to some symbols
                        client->Subscribe({"CU2409", "AL2409", "ZN2409"});
                        
                        // Keep connection alive for the test duration
                        std::this_thread::sleep_for(std::chrono::seconds(30));
                        
                        // Test message reception
                        auto message_count = client->GetReceivedMessageCount();
                        if (message_count > 0) {
                            // Connection is working properly
                        }
                        
                    } else {
                        failed_connections.fetch_add(1);
                    }
                } catch (const std::exception& e) {
                    failed_connections.fetch_add(1);
                    std::cout << "      Connection " << i << " failed: " << e.what() << std::endl;
                }
            }));
        }
        
        // Small delay between batches to avoid connection storms
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // Generate some test traffic during the connection test
    auto traffic_future = std::async(std::launch::async, [&]() {
        auto tick_generator = test_utils_->CreateTickGenerator();
        
        for (int i = 0; i < 1000; ++i) {
            auto tick = tick_generator->GenerateRandomTick();
            ws_server->BroadcastTick(tick);
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    });
    
    // Wait for all connections to complete
    for (auto& future : connection_futures) {
        future.wait();
    }
    
    traffic_future.wait();
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time).count();
    
    // Calculate statistics
    double success_rate = static_cast<double>(successful_connections.load()) / target_connections;
    
    double avg_connection_time = 0.0;
    if (!connection_times.empty()) {
        avg_connection_time = std::accumulate(connection_times.begin(), connection_times.end(), 0.0) 
                            / connection_times.size();
    }
    
    auto resource_usage = test_utils_->GetSystemResourceUsage();
    
    ConcurrentResult result;
    result.concurrent_connections = successful_connections.load();
    result.success_rate = success_rate;
    result.failed_connections = failed_connections.load();
    result.average_response_time_ms = avg_connection_time;
    result.peak_memory_mb = resource_usage.memory_usage_mb;
    result.peak_cpu_percent = resource_usage.cpu_usage_percent;
    
    std::cout << "      Successful connections: " << successful_connections.load() 
              << "/" << target_connections << std::endl;
    std::cout << "      Success rate: " << (success_rate * 100) << "%" << std::endl;
    std::cout << "      Average connection time: " << avg_connection_time << "ms" << std::endl;
    
    return result;
}

ConcurrentResult ConcurrentTest::TestConcurrentGrpcClients(uint32_t target_clients) {
    std::cout << "    Testing " << target_clients << " concurrent gRPC clients..." << std::endl;
    
    // Setup gRPC server
    auto grpc_server = test_utils_->CreateMockGrpcServer();
    
    std::atomic<uint32_t> successful_clients{0};
    std::atomic<uint32_t> failed_clients{0};
    std::vector<double> response_times;
    std::mutex response_times_mutex;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Launch client threads
    std::vector<std::future<void>> client_futures;
    
    for (uint32_t i = 0; i < target_clients; ++i) {
        client_futures.push_back(std::async(std::launch::async, [&, i]() {
            auto client = test_utils_->CreateGrpcTestClient();
            
            try {
                // Test connection
                auto connect_start = std::chrono::high_resolution_clock::now();
                bool connected = client->Connect("localhost:50051");
                auto connect_end = std::chrono::high_resolution_clock::now();
                
                if (connected) {
                    successful_clients.fetch_add(1);
                    
                    // Test streaming
                    auto stream = client->OpenTickStream({"CU2409", "AL2409"});
                    
                    // Perform multiple requests to test sustained performance
                    std::vector<double> request_times;
                    
                    for (int req = 0; req < 10; ++req) {
                        auto req_start = std::chrono::high_resolution_clock::now();
                        
                        // Make a historical data request
                        auto historical_data = client->GetHistoricalTicks(
                            "CU2409",
                            std::chrono::system_clock::now() - std::chrono::hours(1),
                            std::chrono::system_clock::now(),
                            100
                        );
                        
                        auto req_end = std::chrono::high_resolution_clock::now();
                        auto req_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                            req_end - req_start).count();
                        
                        request_times.push_back(static_cast<double>(req_time));
                        
                        std::this_thread::sleep_for(std::chrono::milliseconds(100));
                    }
                    
                    // Calculate average response time for this client
                    double avg_response_time = std::accumulate(request_times.begin(), 
                                                             request_times.end(), 0.0) / request_times.size();
                    
                    {
                        std::lock_guard<std::mutex> lock(response_times_mutex);
                        response_times.push_back(avg_response_time);
                    }
                    
                } else {
                    failed_clients.fetch_add(1);
                }
                
            } catch (const std::exception& e) {
                failed_clients.fetch_add(1);
                std::cout << "      gRPC client " << i << " failed: " << e.what() << std::endl;
            }
        }));
        
        // Small delay to stagger client connections
        if (i % 10 == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }
    
    // Wait for all clients to complete
    for (auto& future : client_futures) {
        future.wait();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    
    // Calculate statistics
    double success_rate = static_cast<double>(successful_clients.load()) / target_clients;
    
    double avg_response_time = 0.0;
    if (!response_times.empty()) {
        avg_response_time = std::accumulate(response_times.begin(), response_times.end(), 0.0) 
                          / response_times.size();
    }
    
    auto resource_usage = test_utils_->GetSystemResourceUsage();
    
    ConcurrentResult result;
    result.concurrent_connections = successful_clients.load();
    result.success_rate = success_rate;
    result.failed_connections = failed_clients.load();
    result.average_response_time_ms = avg_response_time;
    result.peak_memory_mb = resource_usage.memory_usage_mb;
    result.peak_cpu_percent = resource_usage.cpu_usage_percent;
    
    std::cout << "      Successful clients: " << successful_clients.load() 
              << "/" << target_clients << std::endl;
    std::cout << "      Success rate: " << (success_rate * 100) << "%" << std::endl;
    std::cout << "      Average response time: " << avg_response_time << "ms" << std::endl;
    
    return result;
}

ConcurrentResult ConcurrentTest::TestConcurrentRestApiClients(uint32_t target_clients) {
    std::cout << "    Testing " << target_clients << " concurrent REST API clients..." << std::endl;
    
    // Setup REST API server
    auto api_server = test_utils_->CreateMockRestApiServer();
    
    std::atomic<uint32_t> successful_requests{0};
    std::atomic<uint32_t> failed_requests{0};
    std::vector<double> response_times;
    std::mutex response_times_mutex;
    
    // Launch client threads
    std::vector<std::future<void>> client_futures;
    
    for (uint32_t i = 0; i < target_clients; ++i) {
        client_futures.push_back(std::async(std::launch::async, [&, i]() {
            auto client = test_utils_->CreateHttpTestClient();
            
            std::vector<std::string> endpoints = {
                "/api/v1/ticks/latest/CU2409",
                "/api/v1/klines/CU2409?period=1m&limit=100",
                "/api/v1/depth/CU2409",
                "/api/v1/ticks/CU2409?start=1721446200&end=1721449800"
            };
            
            std::vector<double> client_response_times;
            
            // Each client makes multiple requests
            for (int req = 0; req < 20; ++req) {
                const auto& endpoint = endpoints[req % endpoints.size()];
                
                auto req_start = std::chrono::high_resolution_clock::now();
                
                try {
                    auto response = client->Get(endpoint);
                    
                    auto req_end = std::chrono::high_resolution_clock::now();
                    auto req_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                        req_end - req_start).count();
                    
                    if (response.status_code == 200) {
                        successful_requests.fetch_add(1);
                        client_response_times.push_back(static_cast<double>(req_time));
                    } else {
                        failed_requests.fetch_add(1);
                    }
                    
                } catch (const std::exception& e) {
                    failed_requests.fetch_add(1);
                }
                
                // Rate limiting
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
            
            // Calculate average response time for this client
            if (!client_response_times.empty()) {
                double avg_client_time = std::accumulate(client_response_times.begin(), 
                                                       client_response_times.end(), 0.0) / client_response_times.size();
                
                std::lock_guard<std::mutex> lock(response_times_mutex);
                response_times.push_back(avg_client_time);
            }
        }));
        
        // Stagger client start times
        if (i % 5 == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(25));
        }
    }
    
    // Wait for all clients to complete
    for (auto& future : client_futures) {
        future.wait();
    }
    
    // Calculate statistics
    uint32_t total_requests = successful_requests.load() + failed_requests.load();
    double success_rate = total_requests > 0 ? 
                         static_cast<double>(successful_requests.load()) / total_requests : 0.0;
    
    double avg_response_time = 0.0;
    if (!response_times.empty()) {
        avg_response_time = std::accumulate(response_times.begin(), response_times.end(), 0.0) 
                          / response_times.size();
    }
    
    auto resource_usage = test_utils_->GetSystemResourceUsage();
    
    ConcurrentResult result;
    result.concurrent_connections = target_clients;
    result.success_rate = success_rate;
    result.failed_connections = failed_requests.load();
    result.average_response_time_ms = avg_response_time;
    result.peak_memory_mb = resource_usage.memory_usage_mb;
    result.peak_cpu_percent = resource_usage.cpu_usage_percent;
    
    std::cout << "      Successful requests: " << successful_requests.load() 
              << "/" << total_requests << std::endl;
    std::cout << "      Success rate: " << (success_rate * 100) << "%" << std::endl;
    std::cout << "      Average response time: " << avg_response_time << "ms" << std::endl;
    
    return result;
}

ConcurrentResult ConcurrentTest::TestMemoryUsageUnderLoad() {
    std::cout << "    Testing memory usage under concurrent load..." << std::endl;
    
    const uint32_t test_duration_seconds = 60;
    const uint32_t num_ws_clients = 200;
    const uint32_t num_grpc_clients = 100;
    
    // Setup servers
    auto ws_server = test_utils_->CreateMockWebSocketServer();
    auto grpc_server = test_utils_->CreateMockGrpcServer();
    auto tick_generator = test_utils_->CreateTickGenerator();
    
    std::atomic<bool> stop_test{false};
    std::vector<double> memory_samples;
    std::vector<double> cpu_samples;
    std::mutex samples_mutex;
    
    // Memory monitoring thread
    auto monitor_future = std::async(std::launch::async, [&]() {
        while (!stop_test.load()) {
            auto resource_usage = test_utils_->GetSystemResourceUsage();
            
            {
                std::lock_guard<std::mutex> lock(samples_mutex);
                memory_samples.push_back(resource_usage.memory_usage_mb);
                cpu_samples.push_back(resource_usage.cpu_usage_percent);
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    });
    
    // Launch WebSocket clients
    std::vector<std::future<void>> ws_futures;
    for (uint32_t i = 0; i < num_ws_clients; ++i) {
        ws_futures.push_back(std::async(std::launch::async, [&]() {
            auto client = test_utils_->CreateWebSocketTestClient();
            
            if (client->Connect("ws://localhost:8080/market-data")) {
                client->Subscribe({"CU2409", "AL2409", "ZN2409", "AU2409", "AG2409"});
                
                while (!stop_test.load()) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
            }
        }));
    }
    
    // Launch gRPC clients
    std::vector<std::future<void>> grpc_futures;
    for (uint32_t i = 0; i < num_grpc_clients; ++i) {
        grpc_futures.push_back(std::async(std::launch::async, [&]() {
            auto client = test_utils_->CreateGrpcTestClient();
            
            if (client->Connect("localhost:50051")) {
                while (!stop_test.load()) {
                    // Make periodic requests
                    try {
                        auto data = client->GetHistoricalTicks(
                            "CU2409",
                            std::chrono::system_clock::now() - std::chrono::minutes(5),
                            std::chrono::system_clock::now(),
                            50
                        );
                    } catch (...) {
                        // Ignore errors during stress test
                    }
                    
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }
            }
        }));
    }
    
    // Data generation thread
    auto data_future = std::async(std::launch::async, [&]() {
        while (!stop_test.load()) {
            auto tick = tick_generator->GenerateRandomTick();
            ws_server->BroadcastTick(tick);
            grpc_server->StreamTick(tick);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    });
    
    // Run test for specified duration
    std::this_thread::sleep_for(std::chrono::seconds(test_duration_seconds));
    stop_test.store(true);
    
    // Wait for all threads to complete
    monitor_future.wait();
    data_future.wait();
    
    for (auto& future : ws_futures) {
        future.wait();
    }
    for (auto& future : grpc_futures) {
        future.wait();
    }
    
    // Calculate peak usage
    double peak_memory = 0.0;
    double peak_cpu = 0.0;
    
    {
        std::lock_guard<std::mutex> lock(samples_mutex);
        if (!memory_samples.empty()) {
            peak_memory = *std::max_element(memory_samples.begin(), memory_samples.end());
        }
        if (!cpu_samples.empty()) {
            peak_cpu = *std::max_element(cpu_samples.begin(), cpu_samples.end());
        }
    }
    
    ConcurrentResult result;
    result.concurrent_connections = num_ws_clients + num_grpc_clients;
    result.success_rate = 1.0; // Assume success if we completed the test
    result.failed_connections = 0;
    result.peak_memory_mb = peak_memory;
    result.peak_cpu_percent = peak_cpu;
    result.average_response_time_ms = 0.0; // Not applicable for this test
    
    std::cout << "      Peak memory usage: " << peak_memory << " MB" << std::endl;
    std::cout << "      Peak CPU usage: " << peak_cpu << "%" << std::endl;
    std::cout << "      Total concurrent connections: " << (num_ws_clients + num_grpc_clients) << std::endl;
    
    return result;
}

} // namespace performance_tests