#include <iostream>
#include <string>
#include <chrono>
#include <thread>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#endif

class SimpleConnectionTester {
private:
    bool InitializeWinsock() {
#ifdef _WIN32
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            std::cerr << "WSAStartup failed: " << result << std::endl;
            return false;
        }
#endif
        return true;
    }
    
    void CleanupWinsock() {
#ifdef _WIN32
        WSACleanup();
#endif
    }
    
    bool TestTCPConnection(const std::string& host, int port, const std::string& service_name) {
        std::cout << "Testing " << service_name << " connection to " << host << ":" << port << "..." << std::endl;
        
#ifdef _WIN32
        SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (sock == INVALID_SOCKET) {
            std::cerr << "Failed to create socket for " << service_name << std::endl;
            return false;
        }
#else
        int sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock < 0) {
            std::cerr << "Failed to create socket for " << service_name << std::endl;
            return false;
        }
#endif
        
        struct sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port);
        
        if (inet_pton(AF_INET, host.c_str(), &server_addr.sin_addr) <= 0) {
            // 如果不是IP地址，尝试localhost
            if (inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr) <= 0) {
                std::cerr << "Invalid address for " << service_name << std::endl;
#ifdef _WIN32
                closesocket(sock);
#else
                close(sock);
#endif
                return false;
            }
        }
        
        // 设置连接超时
#ifdef _WIN32
        DWORD timeout = 3000; // 3秒
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));
#else
        struct timeval timeout;
        timeout.tv_sec = 3;
        timeout.tv_usec = 0;
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
#endif
        
        int result = connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr));
        
#ifdef _WIN32
        closesocket(sock);
#else
        close(sock);
#endif
        
        if (result == 0) {
            std::cout << "✓ " << service_name << " connection successful" << std::endl;
            return true;
        } else {
            std::cout << "✗ " << service_name << " connection failed" << std::endl;
            return false;
        }
    }
    
public:
    void RunConnectionTests() {
        if (!InitializeWinsock()) {
            return;
        }
        
        std::cout << "========================================" << std::endl;
        std::cout << "Financial Data Service Connection Test" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << std::endl;
        
        bool all_passed = true;
        
        // 测试 Redis 连接
        std::cout << "1. Testing Redis Connection" << std::endl;
        std::cout << "----------------------------" << std::endl;
        bool redis_ok = TestTCPConnection("localhost", 6379, "Redis");
        all_passed &= redis_ok;
        std::cout << std::endl;
        
        // 测试 ClickHouse 连接
        std::cout << "2. Testing ClickHouse Connection" << std::endl;
        std::cout << "---------------------------------" << std::endl;
        bool ch_native_ok = TestTCPConnection("localhost", 9000, "ClickHouse Native");
        bool ch_http_ok = TestTCPConnection("localhost", 8123, "ClickHouse HTTP");
        all_passed &= (ch_native_ok && ch_http_ok);
        std::cout << std::endl;
        
        // 测试 Kafka 连接
        std::cout << "3. Testing Kafka Connection" << std::endl;
        std::cout << "----------------------------" << std::endl;
        bool kafka_ok = TestTCPConnection("localhost", 9092, "Kafka");
        all_passed &= kafka_ok;
        std::cout << std::endl;
        
        // 测试 MinIO 连接
        std::cout << "4. Testing MinIO Connection" << std::endl;
        std::cout << "----------------------------" << std::endl;
        bool minio_console_ok = TestTCPConnection("localhost", 9001, "MinIO Console");
        bool minio_api_ok = TestTCPConnection("localhost", 9002, "MinIO API");
        std::cout << std::endl;
        
        // 总结
        std::cout << "========================================" << std::endl;
        std::cout << "Connection Test Summary" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "Redis:      " << (redis_ok ? "✓ PASS" : "✗ FAIL") << std::endl;
        std::cout << "ClickHouse: " << (ch_native_ok && ch_http_ok ? "✓ PASS" : "✗ FAIL") << std::endl;
        std::cout << "Kafka:      " << (kafka_ok ? "✓ PASS" : "✗ FAIL") << std::endl;
        std::cout << "MinIO:      " << (minio_console_ok && minio_api_ok ? "✓ PASS" : "✗ FAIL") << std::endl;
        std::cout << std::endl;
        
        if (all_passed) {
            std::cout << "🎉 All core services are accessible!" << std::endl;
            std::cout << "The main application should be able to connect to all required services." << std::endl;
        } else {
            std::cout << "⚠️  Some services are not accessible." << std::endl;
            std::cout << "Please ensure all required services are running before starting the main application." << std::endl;
            std::cout << std::endl;
            std::cout << "To start the development environment:" << std::endl;
            std::cout << "  Windows: scripts\\start_dev_env.bat" << std::endl;
            std::cout << "  Linux:   ./scripts/start_dev_env.sh" << std::endl;
        }
        
        CleanupWinsock();
    }
};

int main() {
    std::cout << "Financial Data Service - Simple Connection Tester v1.0" << std::endl;
    std::cout << std::endl;
    
    SimpleConnectionTester tester;
    tester.RunConnectionTests();
    
    std::cout << std::endl;
    std::cout << "Press Enter to exit...";
    std::cin.get();
    
    return 0;
}