#pragma once

#include "config_validators.h"
#include <string>
#include <vector>
#include <unordered_set>
#include <nlohmann/json.hpp>

namespace config {

/**
 * @brief 存储策略配置验证器
 * 
 * 验证存储策略相关的配置参数，包括：
 * - 存储层阈值配置
 * - 数据迁移策略配置
 * - 不同数据类型的差异化配置
 */
class StorageStrategyValidator : public ConfigValidator {
public:
    StorageStrategyValidator();
    ~StorageStrategyValidator() override = default;
    
    ValidationResult Validate(const nlohmann::json& config) const override;
    std::string GetValidatorName() const override;

private:
    // 验证存储策略基本配置
    void ValidateBasicStrategy(const nlohmann::json& strategy_config, 
                              ValidationResult& result) const;
    
    // 验证存储阈值配置
    void ValidateThresholds(const nlohmann::json& thresholds_config, 
                           ValidationResult& result) const;
    
    // 验证数据类型配置
    void ValidateDataTypeConfigs(const nlohmann::json& data_type_configs, 
                                ValidationResult& result) const;
    
    // 验证单个数据类型配置
    void ValidateDataTypeConfig(const std::string& data_type,
                               const nlohmann::json& config,
                               ValidationResult& result) const;
    
    // 验证迁移策略配置
    void ValidateMigrationPolicies(const nlohmann::json& migration_policies, 
                                  ValidationResult& result) const;
    
    // 验证单个迁移策略配置
    void ValidateMigrationPolicy(const std::string& data_type,
                                const nlohmann::json& policy,
                                ValidationResult& result) const;
    
    // 验证cron表达式
    bool ValidateCronExpression(const std::string& cron_expr) const;
    
    // 验证cron字段
    bool ValidateCronField(const std::string& field, int min_val, int max_val) const;
    
    // 验证存储层名称
    bool ValidateStorageLayer(const std::string& layer) const;
    
    // 验证选择策略
    bool ValidateSelectionStrategy(const std::string& strategy) const;
    
    // 验证数值范围
    bool ValidateNumericRange(double value, double min_val, double max_val) const;
    
    // 验证时间配置的一致性
    void ValidateTimeConsistency(const nlohmann::json& config, 
                                ValidationResult& result) const;
    
    // 支持的数据类型
    static const std::unordered_set<std::string> SUPPORTED_DATA_TYPES;
    
    // 支持的存储层
    static const std::unordered_set<std::string> SUPPORTED_STORAGE_LAYERS;
    
    // 支持的选择策略
    static const std::unordered_set<std::string> SUPPORTED_SELECTION_STRATEGIES;
};

} // namespace config