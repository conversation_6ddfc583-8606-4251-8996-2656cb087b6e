# 测试环境 Docker Compose 配置 - 最小化版本
services:
  # Redis - 基础缓存服务
  redis:
    image: redis:7-alpine
    container_name: financial-redis-test
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    restart: unless-stopped

  # ClickHouse - 数据存储
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: financial-clickhouse-test
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - ./config/clickhouse-init-dev.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      CLICKHOUSE_DB: market_data
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: password123
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    restart: unless-stopped

networks:
  default:
    driver: bridge