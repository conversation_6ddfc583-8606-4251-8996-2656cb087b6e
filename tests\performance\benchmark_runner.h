/**
 * @file benchmark_runner.h
 * @brief Performance testing and benchmarking suite runner
 */

#pragma once

#include <cstdint>
#include <string>

namespace performance_tests {

// Result structures for different test categories
struct LatencyResult {
    double mean_latency_us = 0.0;
    double p50_latency_us = 0.0;
    double p95_latency_us = 0.0;
    double p99_latency_us = 0.0;
    double max_latency_us = 0.0;
    uint64_t sample_count = 0;
};

struct ThroughputResult {
    uint64_t messages_per_second = 0;
    uint64_t bytes_per_second = 0;
    double cpu_usage_percent = 0.0;
    double memory_usage_mb = 0.0;
    uint64_t total_messages = 0;
    double test_duration_seconds = 0.0;
};

struct ConcurrentResult {
    uint32_t concurrent_connections = 0;
    double success_rate = 0.0;
    uint32_t failed_connections = 0;
    double peak_memory_mb = 0.0;
    double peak_cpu_percent = 0.0;
    double average_response_time_ms = 0.0;
};

struct DataIntegrityResult {
    uint64_t messages_sent = 0;
    uint64_t messages_received = 0;
    uint64_t messages_lost = 0;
    uint32_t gaps_found = 0;
    double consistency_rate = 0.0;
    uint64_t corrupted_messages = 0;
};

struct FailoverResult {
    uint32_t failover_time_ms = 0;
    bool failover_success = false;
    uint64_t messages_lost_during_failover = 0;
    uint32_t recovery_time_ms = 0;
    bool recovery_success = false;
    uint64_t recovered_messages = 0;
};

// Aggregated results structure
struct LatencyResults {
    LatencyResult end_to_end_latency;
    LatencyResult websocket_latency;
    LatencyResult grpc_latency;
    LatencyResult rest_api_latency;
};

struct ThroughputResults {
    ThroughputResult ingestion_throughput;
    ThroughputResult broadcast_throughput;
    ThroughputResult storage_throughput;
    ThroughputResult query_throughput;
};

struct ConcurrentResults {
    ConcurrentResult websocket_connections;
    ConcurrentResult grpc_clients;
    ConcurrentResult rest_api_clients;
    ConcurrentResult memory_usage;
};

struct IntegrityResults {
    DataIntegrityResult data_loss;
    DataIntegrityResult sequence_continuity;
    DataIntegrityResult data_consistency;
    DataIntegrityResult timestamp_accuracy;
};

struct FailoverResults {
    FailoverResult primary_failover;
    FailoverResult database_failover;
    FailoverResult network_failover;
    FailoverResult data_recovery;
};

struct BenchmarkResults {
    LatencyResults latency_results;
    ThroughputResults throughput_results;
    ConcurrentResults concurrent_results;
    IntegrityResults integrity_results;
    FailoverResults failover_results;
    
    uint32_t passed_tests = 0;
    uint32_t failed_tests = 0;
    std::string test_timestamp;
    std::string system_info;
};

/**
 * @class BenchmarkRunner
 * @brief Main class for running comprehensive performance tests
 */
class BenchmarkRunner {
public:
    BenchmarkRunner();
    
    /**
     * @brief Run all performance tests
     */
    void RunAllTests();
    
    /**
     * @brief Get the results of the last test run
     * @return BenchmarkResults structure containing all test results
     */
    const BenchmarkResults& GetResults() const { return results_; }
    
    /**
     * @brief Export results to JSON file
     * @param filename Output filename
     */
    void ExportResultsToJson(const std::string& filename) const;
    
    /**
     * @brief Export results to CSV file
     * @param filename Output filename
     */
    void ExportResultsToCsv(const std::string& filename) const;

private:
    void RunLatencyTests();
    void RunThroughputTests();
    void RunConcurrentTests();
    void RunDataIntegrityTests();
    void RunFailoverTests();
    void PrintSummary();
    
    BenchmarkResults results_;
};

} // namespace performance_tests