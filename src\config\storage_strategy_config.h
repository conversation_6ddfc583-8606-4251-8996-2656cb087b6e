#pragma once

#include "config_manager.h"
#include "../storage/storage_layer_selector.h"
#include <string>
#include <unordered_map>
#include <memory>
#include <chrono>

namespace config {

/**
 * @brief 数据类型存储配置
 */
struct DataTypeStorageConfig {
    int hot_storage_days = 7;
    int warm_storage_days = 730;
    std::string priority_storage = "hot";
    bool compression_enabled = false;
    int batch_size = 1000;
    double max_response_time_ms = 1000.0;
    
    // 从JSON配置加载
    bool LoadFromJson(const nlohmann::json& config);
    
    // 转换为JSON
    nlohmann::json ToJson() const;
    
    // 验证配置有效性
    bool IsValid() const;
};

/**
 * @brief 数据类型迁移策略配置
 */
struct DataTypeMigrationPolicy {
    double hot_to_warm_hours = 168.0;  // 7天
    double warm_to_cold_days = 730.0;  // 2年
    bool auto_migration = true;
    int migration_batch_size = 50000;
    std::string migration_schedule = "0 2 * * *";
    
    // 从JSON配置加载
    bool LoadFromJson(const nlohmann::json& config);
    
    // 转换为JSON
    nlohmann::json ToJson() const;
    
    // 验证配置有效性
    bool IsValid() const;
};

/**
 * @brief 存储策略配置管理器
 * 
 * 提供对存储策略配置的统一访问和管理接口
 */
class StorageStrategyConfig {
public:
    StorageStrategyConfig();
    ~StorageStrategyConfig() = default;
    
    // 初始化和加载配置
    bool Initialize(std::shared_ptr<ConfigManager> config_manager);
    bool LoadFromConfig();
    bool SaveToConfig();
    
    // 基本策略配置访问
    std::string GetSelectionStrategy() const { return selection_strategy_; }
    bool SetSelectionStrategy(const std::string& strategy);
    
    bool IsAutomaticFailoverEnabled() const { return enable_automatic_failover_; }
    void SetAutomaticFailoverEnabled(bool enabled) { enable_automatic_failover_ = enabled; }
    
    bool IsLoadBalancingEnabled() const { return enable_load_balancing_; }
    void SetLoadBalancingEnabled(bool enabled) { enable_load_balancing_ = enabled; }
    
    // 阈值配置访问
    int GetHotStorageDays() const { return hot_storage_days_; }
    bool SetHotStorageDays(int days);
    
    int GetWarmStorageDays() const { return warm_storage_days_; }
    bool SetWarmStorageDays(int days);
    
    double GetMaxResponseTimeMs() const { return max_response_time_ms_; }
    void SetMaxResponseTimeMs(double ms) { max_response_time_ms_ = ms; }
    
    double GetMinSuccessRate() const { return min_success_rate_; }
    void SetMinSuccessRate(double rate) { min_success_rate_ = rate; }
    
    // 健康检查配置
    std::chrono::seconds GetHealthCheckInterval() const { 
        return std::chrono::seconds(health_check_interval_seconds_); 
    }
    void SetHealthCheckInterval(std::chrono::seconds interval) { 
        health_check_interval_seconds_ = static_cast<int>(interval.count()); 
    }
    
    int GetMaxConsecutiveFailures() const { return max_consecutive_failures_; }
    void SetMaxConsecutiveFailures(int failures) { max_consecutive_failures_ = failures; }
    
    // 数据类型配置管理
    bool HasDataTypeConfig(const std::string& data_type) const;
    DataTypeStorageConfig GetDataTypeConfig(const std::string& data_type) const;
    bool SetDataTypeConfig(const std::string& data_type, const DataTypeStorageConfig& config);
    bool RemoveDataTypeConfig(const std::string& data_type);
    std::vector<std::string> GetConfiguredDataTypes() const;
    
    // 迁移策略管理
    bool HasMigrationPolicy(const std::string& data_type) const;
    DataTypeMigrationPolicy GetMigrationPolicy(const std::string& data_type) const;
    bool SetMigrationPolicy(const std::string& data_type, const DataTypeMigrationPolicy& policy);
    bool RemoveMigrationPolicy(const std::string& data_type);
    
    // 根据数据类型获取存储层选择配置
    financial_data::StorageSelectionConfig GetStorageSelectionConfig(
        const std::string& data_type = "") const;
    
    // 配置热更新支持
    void RegisterConfigChangeCallback(std::function<void()> callback);
    
    // 配置验证
    bool ValidateConfiguration() const;
    std::vector<std::string> GetValidationErrors() const;
    
    // 配置导出和导入
    nlohmann::json ExportConfiguration() const;
    bool ImportConfiguration(const nlohmann::json& config);
    
    // 重置为默认配置
    void ResetToDefaults();
    
    // 获取配置统计信息
    struct ConfigStatistics {
        int total_data_types;
        int configured_data_types;
        int migration_policies;
        bool has_global_thresholds;
        std::chrono::system_clock::time_point last_updated;
    };
    
    ConfigStatistics GetStatistics() const;

private:
    // 配置变更监听器
    class ConfigChangeListener : public config::ConfigChangeListener {
    public:
        explicit ConfigChangeListener(StorageStrategyConfig* parent) : parent_(parent) {}
        void OnConfigChanged(const ConfigChangeEvent& event) override;
    private:
        StorageStrategyConfig* parent_;
    };
    
    // 从配置管理器加载基本配置
    void LoadBasicConfig();
    void LoadThresholds();
    void LoadDataTypeConfigs();
    void LoadMigrationPolicies();
    
    // 保存配置到配置管理器
    void SaveBasicConfig();
    void SaveThresholds();
    void SaveDataTypeConfigs();
    void SaveMigrationPolicies();
    
    // 配置验证辅助方法
    bool ValidateBasicConfig() const;
    bool ValidateThresholds() const;
    bool ValidateDataTypeConfigs() const;
    bool ValidateMigrationPolicies() const;
    
    // 通知配置变更回调
    void NotifyConfigChanged();
    
    // 配置管理器
    std::shared_ptr<ConfigManager> config_manager_;
    std::shared_ptr<ConfigChangeListener> change_listener_;
    
    // 基本策略配置
    std::string selection_strategy_ = "time_based";
    bool enable_automatic_failover_ = true;
    bool enable_load_balancing_ = false;
    int health_check_interval_seconds_ = 30;
    int health_check_timeout_seconds_ = 5;
    int max_consecutive_failures_ = 3;
    int failover_cooldown_seconds_ = 60;
    int max_failover_attempts_ = 2;
    double load_balance_threshold_ = 0.8;
    
    // 全局阈值配置
    int hot_storage_days_ = 7;
    int warm_storage_days_ = 730;
    double max_response_time_ms_ = 1000.0;
    double min_success_rate_ = 0.90;
    double health_threshold_success_rate_ = 0.95;
    double degraded_threshold_success_rate_ = 0.80;
    
    // 数据类型配置
    std::unordered_map<std::string, DataTypeStorageConfig> data_type_configs_;
    
    // 迁移策略配置
    std::unordered_map<std::string, DataTypeMigrationPolicy> migration_policies_;
    
    // 配置变更回调
    std::vector<std::function<void()>> config_change_callbacks_;
    
    // 配置状态
    mutable std::mutex config_mutex_;
    std::chrono::system_clock::time_point last_updated_;
    mutable std::vector<std::string> validation_errors_;
};

} // namespace config