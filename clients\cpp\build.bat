@echo off
echo Building Financial Data C++ SDK
echo ===============================

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with vcpkg if available
if exist "%VCPKG_ROOT%\scripts\buildsystems\vcpkg.cmake" (
    echo Using vcpkg toolchain...
    cmake .. -DCMAKE_TOOLCHAIN_FILE=%VCPKG_ROOT%\scripts\buildsystems\vcpkg.cmake -DCMAKE_BUILD_TYPE=Release
) else (
    echo Using system packages...
    cmake .. -DCMAKE_BUILD_TYPE=Release
)

REM Build the project
cmake --build . --config Release --parallel

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build completed successfully!
    echo.
    echo Available executables:
    echo - examples\basic_usage.exe
    echo - examples\streaming_example.exe  
    echo - examples\async_example.exe
    echo - examples\performance_benchmark.exe
    echo - tests\unit_tests.exe
    echo - tests\integration_tests.exe
    echo.
) else (
    echo.
    echo Build failed with error code %ERRORLEVEL%
    echo.
)

cd ..
pause