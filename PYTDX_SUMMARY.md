# PyTDX数据采集系统 - 完整方案

## 📋 项目概述

我为你创建了一套完整的PyTDX数据采集系统，实现了以下核心功能：

### 🎯 核心功能
1. **代码表更新** - 获取各市场最新的股票、指数、期货、基金、债券代码
2. **历史数据采集** - 逐个采集标的的历史行情数据（日线、分钟线）
3. **数据质量控制** - 自动验证、去重和清洗数据
4. **多种存储格式** - 支持JSON、CSV、Parquet格式
5. **数据库存储** - 自动存储到Redis和ClickHouse

## 📁 文件结构

```
项目根目录/
├── pytdx_data_collector.py          # 完整的数据采集管理器
├── update_symbol_lists.py           # 代码表更新脚本
├── collect_historical_data.py       # 历史数据采集脚本
├── pytdx_enhanced_collector.py      # 增强版采集器（支持配置文件）
├── test_pytdx_collection.py         # 功能测试脚本
├── pytdx_config.json               # 配置文件
├── run_pytdx_collection.bat        # Windows批处理脚本
├── run_pytdx_collection.sh         # Linux/Mac运行脚本
├── PYTDX_COLLECTION_GUIDE.md       # 详细使用指南
└── PYTDX_SUMMARY.md                # 本文档
```

## 🚀 快速开始

### 方法一：一键运行（推荐）
```bash
# Windows
run_pytdx_collection.bat

# Linux/Mac
./run_pytdx_collection.sh
```

### 方法二：分步执行
```bash
# 1. 更新代码表
python update_symbol_lists.py

# 2. 采集历史数据
python collect_historical_data.py
```

### 方法三：使用增强版
```bash
python pytdx_enhanced_collector.py
```

### 方法四：功能测试
```bash
python test_pytdx_collection.py
```

## 📊 数据采集流程

### 第一步：更新代码表
```
🔄 连接PyTDX服务器
📋 获取股票代码表 (沪深A股)
📈 获取指数代码表 (主要指数)
🔮 获取期货代码表 (各交易所)
💰 获取基金代码表
📄 获取债券代码表
💾 保存到本地文件
```

### 第二步：采集历史数据
```
📖 读取代码表文件
🔄 逐个处理标的
📊 采集多周期K线数据
  ├── 日线数据
  ├── 60分钟数据
  ├── 30分钟数据
  ├── 15分钟数据
  └── 5分钟数据
🔍 数据质量控制
💾 存储到数据库
📁 保存到本地文件
```

## ⚙️ 配置说明

### 基本配置 (pytdx_config.json)
```json
{
  "collection": {
    "symbol_types": ["stock", "index"],      // 代码表类型
    "data_types": ["stock", "index"],        // 数据类型
    "periods": ["daily", "60min", "30min"],  // K线周期
    "days_back": 30,                         // 回溯天数
    "request_interval": 0.05                 // 请求间隔
  }
}
```

### 服务器配置
- 支持多个通达信服务器
- 自动故障转移
- 负载均衡

### 存储配置
- 本地文件存储（JSON/CSV/Parquet）
- Redis缓存
- ClickHouse时序数据库

## 📈 输出数据

### 代码表文件
```
data/symbols/
├── stock_symbols_20240109_143022.json    # 股票代码表
├── index_symbols_20240109_143022.json    # 指数代码表
└── ...
```

### 历史数据文件
```
data/historical/
├── stock/
│   ├── 000001/                           # 平安银行
│   │   ├── 000001_daily.json            # 日线数据
│   │   ├── 000001_60min.json            # 60分钟数据
│   │   └── ...
│   └── ...
└── index/
    └── ...
```

### 数据格式示例
```json
{
  "2024-01-09": {
    "open": 10.50,
    "high": 10.80,
    "low": 10.40,
    "close": 10.75,
    "volume": 1000000,
    "amount": 10750000.0
  }
}
```

## 🔧 高级功能

### 1. 数据质量控制
- ✅ 价格合理性验证
- ✅ 成交量验证
- ✅ 时间序列完整性检查
- ✅ 重复数据去除
- ✅ 异常数据过滤

### 2. 性能优化
- ✅ 异步并发处理
- ✅ 连接池管理
- ✅ 智能重试机制
- ✅ 请求频率控制
- ✅ 内存优化

### 3. 监控和日志
- ✅ 详细的采集日志
- ✅ 实时进度显示
- ✅ 统计信息汇总
- ✅ 错误追踪
- ✅ 性能监控

## 📊 统计信息示例

```
📈 采集统计
============================================================
代码表更新: 5 个
标的处理: 4532 个
数据点采集: 1,234,567 条
文件保存: 18,128 个
成功标的: 4,520 个
失败标的: 12 个
错误数量: 3 个
总耗时: 0:45:23
```

## ⚠️ 注意事项

### 1. 网络要求
- 确保网络连接稳定
- 通达信服务器可能有访问限制
- 建议在非交易时间进行大量采集

### 2. 存储空间
- 全量数据需要较大存储空间
- 建议定期清理旧数据
- 可选择性保存数据格式

### 3. 请求频率
- 默认请求间隔0.05秒
- 避免过于频繁的请求
- 可根据网络情况调整

### 4. 数据完整性
- 自动处理数据缺失
- 支持增量更新
- 建议定期全量更新

## 🛠️ 故障排除

### 常见问题及解决方案

1. **连接失败**
   - 检查网络连接
   - 尝试更换服务器
   - 检查防火墙设置

2. **数据为空**
   - 确认代码是否正确
   - 检查日期范围
   - 验证是否为交易日

3. **内存不足**
   - 减少批处理大小
   - 分批处理数据
   - 优化数据结构

## 🔄 使用建议

### 日常使用流程
1. **每日更新**: 运行代码表更新（建议每天早上8点）
2. **历史数据**: 根据需要采集历史数据
3. **增量更新**: 只采集新增或变更的数据
4. **数据验证**: 定期检查数据质量和完整性

### 生产环境部署
1. 配置定时任务（cron/任务计划程序）
2. 设置监控和告警
3. 配置数据备份
4. 优化服务器资源

## 📞 技术支持

如遇到问题，请按以下步骤排查：
1. 查看日志文件中的错误信息
2. 运行测试脚本验证功能
3. 检查配置文件是否正确
4. 确认网络连接状态

## 🎉 总结

这套PyTDX数据采集系统为你提供了：

✅ **完整的数据采集流程** - 从代码表更新到历史数据采集
✅ **高质量的数据处理** - 自动验证、去重和清洗
✅ **灵活的配置选项** - 支持多种采集参数和存储格式
✅ **详细的日志监控** - 完整的采集过程记录
✅ **易于使用的接口** - 简单的命令行操作

现在你可以开始使用这套系统来采集金融数据了！建议先运行测试脚本验证功能，然后根据需要调整配置参数。