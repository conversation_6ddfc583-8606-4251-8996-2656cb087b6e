#include "storage_strategy_config.h"
#include <algorithm>
#include <regex>

namespace config {

// DataTypeStorageConfig 实现
bool DataTypeStorageConfig::LoadFromJson(const nlohmann::json& config) {
    try {
        if (config.contains("hot_storage_days")) {
            hot_storage_days = config["hot_storage_days"];
        }
        if (config.contains("warm_storage_days")) {
            warm_storage_days = config["warm_storage_days"];
        }
        if (config.contains("priority_storage")) {
            priority_storage = config["priority_storage"];
        }
        if (config.contains("compression_enabled")) {
            compression_enabled = config["compression_enabled"];
        }
        if (config.contains("batch_size")) {
            batch_size = config["batch_size"];
        }
        if (config.contains("max_response_time_ms")) {
            max_response_time_ms = config["max_response_time_ms"];
        }
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

nlohmann::json DataTypeStorageConfig::ToJson() const {
    nlohmann::json config;
    config["hot_storage_days"] = hot_storage_days;
    config["warm_storage_days"] = warm_storage_days;
    config["priority_storage"] = priority_storage;
    config["compression_enabled"] = compression_enabled;
    config["batch_size"] = batch_size;
    config["max_response_time_ms"] = max_response_time_ms;
    return config;
}

bool DataTypeStorageConfig::IsValid() const {
    return hot_storage_days > 0 && 
           warm_storage_days > hot_storage_days &&
           batch_size > 0 &&
           max_response_time_ms > 0.0 &&
           (priority_storage == "hot" || priority_storage == "warm" || priority_storage == "cold");
}

// DataTypeMigrationPolicy 实现
bool DataTypeMigrationPolicy::LoadFromJson(const nlohmann::json& config) {
    try {
        if (config.contains("hot_to_warm_hours")) {
            hot_to_warm_hours = config["hot_to_warm_hours"];
        }
        if (config.contains("warm_to_cold_days")) {
            warm_to_cold_days = config["warm_to_cold_days"];
        }
        if (config.contains("auto_migration")) {
            auto_migration = config["auto_migration"];
        }
        if (config.contains("migration_batch_size")) {
            migration_batch_size = config["migration_batch_size"];
        }
        if (config.contains("migration_schedule")) {
            migration_schedule = config["migration_schedule"];
        }
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

nlohmann::json DataTypeMigrationPolicy::ToJson() const {
    nlohmann::json config;
    config["hot_to_warm_hours"] = hot_to_warm_hours;
    config["warm_to_cold_days"] = warm_to_cold_days;
    config["auto_migration"] = auto_migration;
    config["migration_batch_size"] = migration_batch_size;
    config["migration_schedule"] = migration_schedule;
    return config;
}

bool DataTypeMigrationPolicy::IsValid() const {
    // 验证cron表达式
    std::regex cron_regex(R"(^(\*|[0-5]?\d|\*\/\d+)\s+(\*|[01]?\d|2[0-3]|\*\/\d+)\s+(\*|[0-2]?\d|3[01]|\*\/\d+)\s+(\*|[0]?\d|1[0-2]|\*\/\d+)\s+(\*|[0-6]|\*\/\d+)$)");
    
    return hot_to_warm_hours > 0.0 &&
           warm_to_cold_days > 0.0 &&
           hot_to_warm_hours < warm_to_cold_days * 24.0 &&
           migration_batch_size > 0 &&
           std::regex_match(migration_schedule, cron_regex);
}

// StorageStrategyConfig 实现
StorageStrategyConfig::StorageStrategyConfig() 
    : last_updated_(std::chrono::system_clock::now()) {
}

bool StorageStrategyConfig::Initialize(std::shared_ptr<ConfigManager> config_manager) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    config_manager_ = config_manager;
    
    // 注册配置变更监听器
    change_listener_ = std::make_shared<ConfigChangeListener>(this);
    config_manager_->RegisterChangeListener(change_listener_);
    
    return LoadFromConfig();
}

bool StorageStrategyConfig::LoadFromConfig() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (!config_manager_) {
        return false;
    }
    
    try {
        LoadBasicConfig();
        LoadThresholds();
        LoadDataTypeConfigs();
        LoadMigrationPolicies();
        
        last_updated_ = std::chrono::system_clock::now();
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool StorageStrategyConfig::SaveToConfig() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (!config_manager_) {
        return false;
    }
    
    try {
        SaveBasicConfig();
        SaveThresholds();
        SaveDataTypeConfigs();
        SaveMigrationPolicies();
        
        last_updated_ = std::chrono::system_clock::now();
        return config_manager_->SaveConfig();
    } catch (const std::exception&) {
        return false;
    }
}

void StorageStrategyConfig::LoadBasicConfig() {
    selection_strategy_ = config_manager_->GetValue<std::string>(
        "storage.strategy.selection_strategy", "time_based");
    enable_automatic_failover_ = config_manager_->GetValue<bool>(
        "storage.strategy.enable_automatic_failover", true);
    enable_load_balancing_ = config_manager_->GetValue<bool>(
        "storage.strategy.enable_load_balancing", false);
    health_check_interval_seconds_ = config_manager_->GetValue<int>(
        "storage.strategy.health_check_interval_seconds", 30);
    health_check_timeout_seconds_ = config_manager_->GetValue<int>(
        "storage.strategy.health_check_timeout_seconds", 5);
    max_consecutive_failures_ = config_manager_->GetValue<int>(
        "storage.strategy.max_consecutive_failures", 3);
    failover_cooldown_seconds_ = config_manager_->GetValue<int>(
        "storage.strategy.failover_cooldown_seconds", 60);
    max_failover_attempts_ = config_manager_->GetValue<int>(
        "storage.strategy.max_failover_attempts", 2);
    load_balance_threshold_ = config_manager_->GetValue<double>(
        "storage.strategy.load_balance_threshold", 0.8);
}

void StorageStrategyConfig::LoadThresholds() {
    hot_storage_days_ = config_manager_->GetValue<int>(
        "storage.strategy.thresholds.hot_storage_days", 7);
    warm_storage_days_ = config_manager_->GetValue<int>(
        "storage.strategy.thresholds.warm_storage_days", 730);
    max_response_time_ms_ = config_manager_->GetValue<double>(
        "storage.strategy.thresholds.max_response_time_ms", 1000.0);
    min_success_rate_ = config_manager_->GetValue<double>(
        "storage.strategy.thresholds.min_success_rate", 0.90);
    health_threshold_success_rate_ = config_manager_->GetValue<double>(
        "storage.strategy.thresholds.health_threshold_success_rate", 0.95);
    degraded_threshold_success_rate_ = config_manager_->GetValue<double>(
        "storage.strategy.thresholds.degraded_threshold_success_rate", 0.80);
}

void StorageStrategyConfig::LoadDataTypeConfigs() {
    data_type_configs_.clear();
    
    auto data_type_configs_json = config_manager_->GetSection("storage.strategy.data_type_configs");
    
    for (const auto& [data_type, config_json] : data_type_configs_json.items()) {
        DataTypeStorageConfig config;
        if (config.LoadFromJson(config_json)) {
            data_type_configs_[data_type] = config;
        }
    }
}

void StorageStrategyConfig::LoadMigrationPolicies() {
    migration_policies_.clear();
    
    auto migration_policies_json = config_manager_->GetSection("storage.strategy.migration_policies");
    
    for (const auto& [data_type, policy_json] : migration_policies_json.items()) {
        DataTypeMigrationPolicy policy;
        if (policy.LoadFromJson(policy_json)) {
            migration_policies_[data_type] = policy;
        }
    }
}

void StorageStrategyConfig::SaveBasicConfig() {
    config_manager_->SetValue("storage.strategy.selection_strategy", selection_strategy_);
    config_manager_->SetValue("storage.strategy.enable_automatic_failover", enable_automatic_failover_);
    config_manager_->SetValue("storage.strategy.enable_load_balancing", enable_load_balancing_);
    config_manager_->SetValue("storage.strategy.health_check_interval_seconds", health_check_interval_seconds_);
    config_manager_->SetValue("storage.strategy.health_check_timeout_seconds", health_check_timeout_seconds_);
    config_manager_->SetValue("storage.strategy.max_consecutive_failures", max_consecutive_failures_);
    config_manager_->SetValue("storage.strategy.failover_cooldown_seconds", failover_cooldown_seconds_);
    config_manager_->SetValue("storage.strategy.max_failover_attempts", max_failover_attempts_);
    config_manager_->SetValue("storage.strategy.load_balance_threshold", load_balance_threshold_);
}

void StorageStrategyConfig::SaveThresholds() {
    config_manager_->SetValue("storage.strategy.thresholds.hot_storage_days", hot_storage_days_);
    config_manager_->SetValue("storage.strategy.thresholds.warm_storage_days", warm_storage_days_);
    config_manager_->SetValue("storage.strategy.thresholds.max_response_time_ms", max_response_time_ms_);
    config_manager_->SetValue("storage.strategy.thresholds.min_success_rate", min_success_rate_);
    config_manager_->SetValue("storage.strategy.thresholds.health_threshold_success_rate", health_threshold_success_rate_);
    config_manager_->SetValue("storage.strategy.thresholds.degraded_threshold_success_rate", degraded_threshold_success_rate_);
}

void StorageStrategyConfig::SaveDataTypeConfigs() {
    nlohmann::json data_type_configs_json;
    
    for (const auto& [data_type, config] : data_type_configs_) {
        data_type_configs_json[data_type] = config.ToJson();
    }
    
    config_manager_->SetSection("storage.strategy.data_type_configs", data_type_configs_json);
}

void StorageStrategyConfig::SaveMigrationPolicies() {
    nlohmann::json migration_policies_json;
    
    for (const auto& [data_type, policy] : migration_policies_) {
        migration_policies_json[data_type] = policy.ToJson();
    }
    
    config_manager_->SetSection("storage.strategy.migration_policies", migration_policies_json);
}

bool StorageStrategyConfig::SetSelectionStrategy(const std::string& strategy) {
    std::vector<std::string> valid_strategies = {
        "time_based", "performance_based", "load_balanced", "failover_only"
    };
    
    if (std::find(valid_strategies.begin(), valid_strategies.end(), strategy) == valid_strategies.end()) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(config_mutex_);
    selection_strategy_ = strategy;
    NotifyConfigChanged();
    return true;
}

bool StorageStrategyConfig::SetHotStorageDays(int days) {
    if (days <= 0 || days >= warm_storage_days_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(config_mutex_);
    hot_storage_days_ = days;
    NotifyConfigChanged();
    return true;
}

bool StorageStrategyConfig::SetWarmStorageDays(int days) {
    if (days <= hot_storage_days_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(config_mutex_);
    warm_storage_days_ = days;
    NotifyConfigChanged();
    return true;
}

bool StorageStrategyConfig::HasDataTypeConfig(const std::string& data_type) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return data_type_configs_.find(data_type) != data_type_configs_.end();
}

DataTypeStorageConfig StorageStrategyConfig::GetDataTypeConfig(const std::string& data_type) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    auto it = data_type_configs_.find(data_type);
    if (it != data_type_configs_.end()) {
        return it->second;
    }
    
    // 返回基于全局配置的默认配置
    DataTypeStorageConfig default_config;
    default_config.hot_storage_days = hot_storage_days_;
    default_config.warm_storage_days = warm_storage_days_;
    default_config.max_response_time_ms = max_response_time_ms_;
    return default_config;
}

bool StorageStrategyConfig::SetDataTypeConfig(const std::string& data_type, 
                                            const DataTypeStorageConfig& config) {
    if (!config.IsValid()) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(config_mutex_);
    data_type_configs_[data_type] = config;
    NotifyConfigChanged();
    return true;
}

bool StorageStrategyConfig::RemoveDataTypeConfig(const std::string& data_type) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    auto it = data_type_configs_.find(data_type);
    if (it != data_type_configs_.end()) {
        data_type_configs_.erase(it);
        NotifyConfigChanged();
        return true;
    }
    
    return false;
}

std::vector<std::string> StorageStrategyConfig::GetConfiguredDataTypes() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    std::vector<std::string> data_types;
    for (const auto& [data_type, config] : data_type_configs_) {
        data_types.push_back(data_type);
    }
    
    return data_types;
}

bool StorageStrategyConfig::HasMigrationPolicy(const std::string& data_type) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return migration_policies_.find(data_type) != migration_policies_.end();
}

DataTypeMigrationPolicy StorageStrategyConfig::GetMigrationPolicy(const std::string& data_type) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    auto it = migration_policies_.find(data_type);
    if (it != migration_policies_.end()) {
        return it->second;
    }
    
    // 返回默认迁移策略
    DataTypeMigrationPolicy default_policy;
    default_policy.hot_to_warm_hours = hot_storage_days_ * 24.0;
    default_policy.warm_to_cold_days = warm_storage_days_;
    return default_policy;
}

bool StorageStrategyConfig::SetMigrationPolicy(const std::string& data_type, 
                                             const DataTypeMigrationPolicy& policy) {
    if (!policy.IsValid()) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(config_mutex_);
    migration_policies_[data_type] = policy;
    NotifyConfigChanged();
    return true;
}

bool StorageStrategyConfig::RemoveMigrationPolicy(const std::string& data_type) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    auto it = migration_policies_.find(data_type);
    if (it != migration_policies_.end()) {
        migration_policies_.erase(it);
        NotifyConfigChanged();
        return true;
    }
    
    return false;
}

financial_data::StorageSelectionConfig StorageStrategyConfig::GetStorageSelectionConfig(
    const std::string& data_type) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    financial_data::StorageSelectionConfig config;
    
    // 设置基本策略
    if (selection_strategy_ == "time_based") {
        config.strategy = financial_data::StorageSelectionStrategy::TIME_BASED;
    } else if (selection_strategy_ == "performance_based") {
        config.strategy = financial_data::StorageSelectionStrategy::PERFORMANCE_BASED;
    } else if (selection_strategy_ == "load_balanced") {
        config.strategy = financial_data::StorageSelectionStrategy::LOAD_BALANCED;
    } else if (selection_strategy_ == "failover_only") {
        config.strategy = financial_data::StorageSelectionStrategy::FAILOVER_ONLY;
    }
    
    // 设置时间阈值
    if (!data_type.empty() && data_type_configs_.find(data_type) != data_type_configs_.end()) {
        const auto& type_config = data_type_configs_.at(data_type);
        config.hot_storage_days = type_config.hot_storage_days;
        config.warm_storage_days = type_config.warm_storage_days;
        config.max_acceptable_response_time_ms = type_config.max_response_time_ms;
    } else {
        config.hot_storage_days = hot_storage_days_;
        config.warm_storage_days = warm_storage_days_;
        config.max_acceptable_response_time_ms = max_response_time_ms_;
    }
    
    // 设置其他配置
    config.enable_automatic_failover = enable_automatic_failover_;
    config.enable_load_balancing = enable_load_balancing_;
    config.health_check_interval = std::chrono::seconds(health_check_interval_seconds_);
    config.health_check_timeout = std::chrono::seconds(health_check_timeout_seconds_);
    config.max_consecutive_failures = max_consecutive_failures_;
    config.failover_cooldown = std::chrono::seconds(failover_cooldown_seconds_);
    config.max_failover_attempts = max_failover_attempts_;
    config.load_balance_threshold = load_balance_threshold_;
    config.min_acceptable_success_rate = min_success_rate_;
    
    return config;
}

void StorageStrategyConfig::RegisterConfigChangeCallback(std::function<void()> callback) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_change_callbacks_.push_back(callback);
}

bool StorageStrategyConfig::ValidateConfiguration() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    validation_errors_.clear();
    
    bool is_valid = ValidateBasicConfig() && 
                   ValidateThresholds() && 
                   ValidateDataTypeConfigs() && 
                   ValidateMigrationPolicies();
    
    return is_valid;
}

std::vector<std::string> StorageStrategyConfig::GetValidationErrors() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return validation_errors_;
}

bool StorageStrategyConfig::ValidateBasicConfig() const {
    std::vector<std::string> valid_strategies = {
        "time_based", "performance_based", "load_balanced", "failover_only"
    };
    
    if (std::find(valid_strategies.begin(), valid_strategies.end(), selection_strategy_) == valid_strategies.end()) {
        validation_errors_.push_back("Invalid selection strategy: " + selection_strategy_);
        return false;
    }
    
    if (health_check_interval_seconds_ <= 0 || health_check_interval_seconds_ > 3600) {
        validation_errors_.push_back("Invalid health check interval");
        return false;
    }
    
    if (max_consecutive_failures_ <= 0 || max_consecutive_failures_ > 100) {
        validation_errors_.push_back("Invalid max consecutive failures");
        return false;
    }
    
    return true;
}

bool StorageStrategyConfig::ValidateThresholds() const {
    if (hot_storage_days_ <= 0 || hot_storage_days_ >= warm_storage_days_) {
        validation_errors_.push_back("Invalid storage day thresholds");
        return false;
    }
    
    if (min_success_rate_ < 0.0 || min_success_rate_ > 1.0) {
        validation_errors_.push_back("Invalid success rate threshold");
        return false;
    }
    
    if (health_threshold_success_rate_ <= degraded_threshold_success_rate_) {
        validation_errors_.push_back("Health threshold must be greater than degraded threshold");
        return false;
    }
    
    return true;
}

bool StorageStrategyConfig::ValidateDataTypeConfigs() const {
    for (const auto& [data_type, config] : data_type_configs_) {
        if (!config.IsValid()) {
            validation_errors_.push_back("Invalid configuration for data type: " + data_type);
            return false;
        }
    }
    
    return true;
}

bool StorageStrategyConfig::ValidateMigrationPolicies() const {
    for (const auto& [data_type, policy] : migration_policies_) {
        if (!policy.IsValid()) {
            validation_errors_.push_back("Invalid migration policy for data type: " + data_type);
            return false;
        }
    }
    
    return true;
}

void StorageStrategyConfig::NotifyConfigChanged() {
    last_updated_ = std::chrono::system_clock::now();
    
    for (const auto& callback : config_change_callbacks_) {
        try {
            callback();
        } catch (const std::exception&) {
            // 忽略回调错误
        }
    }
}

nlohmann::json StorageStrategyConfig::ExportConfiguration() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    nlohmann::json config;
    
    // 基本配置
    config["selection_strategy"] = selection_strategy_;
    config["enable_automatic_failover"] = enable_automatic_failover_;
    config["enable_load_balancing"] = enable_load_balancing_;
    config["health_check_interval_seconds"] = health_check_interval_seconds_;
    config["max_consecutive_failures"] = max_consecutive_failures_;
    
    // 阈值配置
    config["thresholds"]["hot_storage_days"] = hot_storage_days_;
    config["thresholds"]["warm_storage_days"] = warm_storage_days_;
    config["thresholds"]["max_response_time_ms"] = max_response_time_ms_;
    config["thresholds"]["min_success_rate"] = min_success_rate_;
    
    // 数据类型配置
    for (const auto& [data_type, type_config] : data_type_configs_) {
        config["data_type_configs"][data_type] = type_config.ToJson();
    }
    
    // 迁移策略
    for (const auto& [data_type, policy] : migration_policies_) {
        config["migration_policies"][data_type] = policy.ToJson();
    }
    
    return config;
}

bool StorageStrategyConfig::ImportConfiguration(const nlohmann::json& config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    try {
        // 导入基本配置
        if (config.contains("selection_strategy")) {
            selection_strategy_ = config["selection_strategy"];
        }
        if (config.contains("enable_automatic_failover")) {
            enable_automatic_failover_ = config["enable_automatic_failover"];
        }
        if (config.contains("enable_load_balancing")) {
            enable_load_balancing_ = config["enable_load_balancing"];
        }
        
        // 导入阈值配置
        if (config.contains("thresholds")) {
            const auto& thresholds = config["thresholds"];
            if (thresholds.contains("hot_storage_days")) {
                hot_storage_days_ = thresholds["hot_storage_days"];
            }
            if (thresholds.contains("warm_storage_days")) {
                warm_storage_days_ = thresholds["warm_storage_days"];
            }
        }
        
        // 导入数据类型配置
        if (config.contains("data_type_configs")) {
            data_type_configs_.clear();
            for (const auto& [data_type, type_config] : config["data_type_configs"].items()) {
                DataTypeStorageConfig storage_config;
                if (storage_config.LoadFromJson(type_config)) {
                    data_type_configs_[data_type] = storage_config;
                }
            }
        }
        
        // 导入迁移策略
        if (config.contains("migration_policies")) {
            migration_policies_.clear();
            for (const auto& [data_type, policy_config] : config["migration_policies"].items()) {
                DataTypeMigrationPolicy migration_policy;
                if (migration_policy.LoadFromJson(policy_config)) {
                    migration_policies_[data_type] = migration_policy;
                }
            }
        }
        
        NotifyConfigChanged();
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

void StorageStrategyConfig::ResetToDefaults() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    // 重置为默认值
    selection_strategy_ = "time_based";
    enable_automatic_failover_ = true;
    enable_load_balancing_ = false;
    health_check_interval_seconds_ = 30;
    health_check_timeout_seconds_ = 5;
    max_consecutive_failures_ = 3;
    failover_cooldown_seconds_ = 60;
    max_failover_attempts_ = 2;
    load_balance_threshold_ = 0.8;
    
    hot_storage_days_ = 7;
    warm_storage_days_ = 730;
    max_response_time_ms_ = 1000.0;
    min_success_rate_ = 0.90;
    health_threshold_success_rate_ = 0.95;
    degraded_threshold_success_rate_ = 0.80;
    
    data_type_configs_.clear();
    migration_policies_.clear();
    
    NotifyConfigChanged();
}

StorageStrategyConfig::ConfigStatistics StorageStrategyConfig::GetStatistics() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    ConfigStatistics stats;
    stats.total_data_types = 6; // 支持的数据类型数量
    stats.configured_data_types = static_cast<int>(data_type_configs_.size());
    stats.migration_policies = static_cast<int>(migration_policies_.size());
    stats.has_global_thresholds = true;
    stats.last_updated = last_updated_;
    
    return stats;
}

// ConfigChangeListener 实现
void StorageStrategyConfig::ConfigChangeListener::OnConfigChanged(const ConfigChangeEvent& event) {
    if (event.section == "storage" || event.key.find("storage.strategy") == 0) {
        parent_->LoadFromConfig();
    }
}

} // namespace config