#pragma once

#include "security_config.h"
#include <openssl/ssl.h>
#include <openssl/err.h>
#include <memory>
#include <string>

namespace financial_data {
namespace security {

class TLSManager {
public:
    explicit TLSManager(const TLSConfig& config);
    ~TLSManager();

    // 初始化TLS上下文
    bool Initialize();
    
    // 创建服务器SSL上下文
    SSL_CTX* CreateServerContext();
    
    // 创建客户端SSL上下文
    SSL_CTX* CreateClientContext();
    
    // 验证证书
    bool VerifyCertificate(SSL* ssl);
    
    // 获取连接信息
    std::string GetConnectionInfo(SSL* ssl);
    
    // 检查TLS版本
    bool IsTLS13(SSL* ssl);
    
    // 清理资源
    void Cleanup();

private:
    TLSConfig config_;
    SSL_CTX* server_ctx_;
    SSL_CTX* client_ctx_;
    bool initialized_;
    
    // 加载证书和私钥
    bool LoadCertificates(SSL_CTX* ctx);
    
    // 配置密码套件
    bool ConfigureCipherSuites(SSL_CTX* ctx);
    
    // 设置验证回调
    static int VerifyCallback(int preverify_ok, X509_STORE_CTX* ctx);
    
    // 错误处理
    void LogSSLError(const std::string& operation);
};

} // namespace security
} // namespace financial_data