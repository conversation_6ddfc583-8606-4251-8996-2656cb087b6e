package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/status"
)

// Mock protobuf types (replace with actual generated types)
type TickDataRequest struct {
	Symbols    []string
	Exchange   string
	BufferSize int32
}

type TickDataResponse struct {
	Ticks    []*TickData
	HasMore  bool
	Metadata *ResponseMetadata
}

type TickData struct {
	Timestamp   int64
	Symbol      string
	Exchange    string
	LastPrice   float64
	Volume      int64
	Turnover    float64
	Bids        []*PriceLevel
	Asks        []*PriceLevel
	Sequence    uint32
}

type PriceLevel struct {
	Price  float64
	Volume int32
}

type ResponseMetadata struct {
	ServerTimestamp    int64
	ServerId          string
	SequenceNumber    int32
	ProcessingLatencyUs float64
}

// Mock gRPC service client interface
type MarketDataServiceClient interface {
	StreamTickData(ctx context.Context, req *TickDataRequest, opts ...grpc.CallOption) (MarketDataService_StreamTickDataClient, error)
}

type MarketDataService_StreamTickDataClient interface {
	Recv() (*TickDataResponse, error)
	grpc.ClientStream
}

// GrpcClient provides high-performance gRPC client with load balancing
type GrpcClient struct {
	serverAddresses []string
	connections     []*grpc.ClientConn
	clients         []MarketDataServiceClient
	currentIndex    int
	maxRetries      int
	mutex           sync.RWMutex
	logger          *log.Logger
}

// NewGrpcClient creates a new gRPC client with multiple server connections
func NewGrpcClient(serverAddresses []string, maxRetries int) (*GrpcClient, error) {
	client := &GrpcClient{
		serverAddresses: serverAddresses,
		connections:     make([]*grpc.ClientConn, len(serverAddresses)),
		clients:         make([]MarketDataServiceClient, len(serverAddresses)),
		maxRetries:      maxRetries,
		logger:          log.New(log.Writer(), "[GrpcClient] ", log.LstdFlags),
	}

	// Initialize connections to all servers
	for i, addr := range serverAddresses {
		conn, err := client.createConnection(addr)
		if err != nil {
			client.logger.Printf("Failed to connect to %s: %v", addr, err)
			continue
		}
		
		client.connections[i] = conn
		// client.clients[i] = NewMarketDataServiceClient(conn) // Would use generated client
		client.logger.Printf("Connected to server: %s", addr)
	}

	return client, nil
}

// createConnection creates a gRPC connection with optimized settings
func (c *GrpcClient) createConnection(address string) (*grpc.ClientConn, error) {
	// Configure keepalive parameters for high-performance streaming
	kacp := keepalive.ClientParameters{
		Time:                30 * time.Second, // Send pings every 30 seconds
		Timeout:             5 * time.Second,  // Wait 5 seconds for ping ack
		PermitWithoutStream: true,             // Send pings even without active streams
	}

	// Configure connection options
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithKeepaliveParams(kacp),
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(4*1024*1024), // 4MB
			grpc.MaxCallSendMsgSize(4*1024*1024), // 4MB
		),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return grpc.DialContext(ctx, address, opts...)
}

// getCurrentClient returns the current active client with failover logic
func (c *GrpcClient) getCurrentClient() (MarketDataServiceClient, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	attempts := len(c.serverAddresses)
	for i := 0; i < attempts; i++ {
		client := c.clients[c.currentIndex]
		if client != nil {
			// Test connection health (simplified)
			return client, nil
		}
		
		c.logger.Printf("Server %d unavailable, trying next", c.currentIndex)
		c.currentIndex = (c.currentIndex + 1) % len(c.serverAddresses)
	}

	return nil, fmt.Errorf("all servers unavailable")
}

// StreamTickData streams real-time tick data with flow control and error handling
func (c *GrpcClient) StreamTickData(ctx context.Context, symbols []string, exchange string, bufferSize int32) (<-chan *TickData, <-chan error) {
	tickChan := make(chan *TickData, bufferSize)
	errorChan := make(chan error, 1)

	go func() {
		defer close(tickChan)
		defer close(errorChan)

		request := &TickDataRequest{
			Symbols:    symbols,
			Exchange:   exchange,
			BufferSize: bufferSize,
		}

		retryCount := 0
		for retryCount < c.maxRetries {
			client, err := c.getCurrentClient()
			if err != nil {
				errorChan <- fmt.Errorf("failed to get client: %w", err)
				return
			}

			c.logger.Printf("Starting tick data stream for symbols: %v", symbols)

			// Mock stream implementation (replace with actual gRPC call)
			// stream, err := client.StreamTickData(ctx, request)
			
			// Mock data generation for demonstration
			go func() {
				defer func() {
					if r := recover(); r != nil {
						c.logger.Printf("Stream panic recovered: %v", r)
					}
				}()
				
				for i := 0; i < 1000 && ctx.Err() == nil; i++ {
					// Generate mock tick data
					tick := &TickData{
						Timestamp: time.Now().UnixNano(),
						Symbol:    symbols[0],
						Exchange:  exchange,
						LastPrice: 50000.0 + float64(i%100),
						Volume:    int64(100 + i),
						Turnover:  float64(5000000 + i*1000),
						Sequence:  uint32(i),
					}
					
					select {
					case tickChan <- tick:
						// Successfully sent tick
					case <-ctx.Done():
						return
					default:
						// Channel full, apply backpressure
						c.logger.Println("Tick channel full, dropping message")
					}
					
					time.Sleep(time.Millisecond * 10) // 100 ticks per second
				}
			}()
			
			// Wait for context cancellation
			<-ctx.Done()
			return
		}

		if retryCount >= c.maxRetries {
			errorChan <- fmt.Errorf("exceeded maximum retries (%d)", c.maxRetries)
		}
	}()

	return tickChan, errorChan
}

// StreamKlineData streams K-line data with flow control
func (c *GrpcClient) StreamKlineData(ctx context.Context, symbols []string, exchange string, period string, bufferSize int32) (<-chan *KlineData, <-chan error) {
	klineChan := make(chan *KlineData, bufferSize)
	errorChan := make(chan error, 1)

	go func() {
		defer close(klineChan)
		defer close(errorChan)

		c.logger.Printf("Starting kline data stream for symbols: %v, period: %s", symbols, period)

		// Mock kline data generation
		for i := 0; i < 100 && ctx.Err() == nil; i++ {
			kline := &KlineData{
				Symbol:    symbols[0],
				Exchange:  exchange,
				Period:    period,
				Timestamp: time.Now().UnixNano(),
				Open:      50000.0 + float64(i),
				High:      50100.0 + float64(i),
				Low:       49900.0 + float64(i),
				Close:     50050.0 + float64(i),
				Volume:    int64(1000 + i*10),
				Turnover:  float64(50000000 + i*1000000),
			}

			select {
			case klineChan <- kline:
				// Successfully sent kline
			case <-ctx.Done():
				return
			default:
				c.logger.Println("Kline channel full, dropping message")
			}

			time.Sleep(time.Second) // 1 kline per second
		}
	}()

	return klineChan, errorChan
}

// KlineData represents K-line/candlestick data
type KlineData struct {
	Symbol      string
	Exchange    string
	Period      string
	Timestamp   int64
	Open        float64
	High        float64
	Low         float64
	Close       float64
	Volume      int64
	Turnover    float64
	OpenInterest int64
}

// FlowControlledConsumer provides flow control and backpressure handling
type FlowControlledConsumer struct {
	client     *GrpcClient
	bufferSize int32
	stats      *ProcessingStats
	mutex      sync.RWMutex
}

// ProcessingStats tracks processing statistics
type ProcessingStats struct {
	MessagesReceived  int64
	MessagesProcessed int64
	ProcessingErrors  int64
	AvgLatencyUs      float64
	StartTime         time.Time
}

// NewFlowControlledConsumer creates a new consumer with flow control
func NewFlowControlledConsumer(client *GrpcClient, bufferSize int32) *FlowControlledConsumer {
	return &FlowControlledConsumer{
		client:     client,
		bufferSize: bufferSize,
		stats: &ProcessingStats{
			StartTime: time.Now(),
		},
	}
}

// ConsumeTickData consumes tick data with flow control
func (fc *FlowControlledConsumer) ConsumeTickData(ctx context.Context, symbols []string, exchange string, handler func(*TickData) error) error {
	tickChan, errorChan := fc.client.StreamTickData(ctx, symbols, exchange, fc.bufferSize)

	for {
		select {
		case tick, ok := <-tickChan:
			if !ok {
				log.Println("Tick channel closed")
				return nil
			}

			fc.mutex.Lock()
			fc.stats.MessagesReceived++
			fc.mutex.Unlock()

			// Process tick with timing
			startTime := time.Now()
			
			if err := handler(tick); err != nil {
				fc.mutex.Lock()
				fc.stats.ProcessingErrors++
				fc.mutex.Unlock()
				log.Printf("Error processing tick: %v", err)
				continue
			}

			// Update processing stats
			processingTime := time.Since(startTime)
			fc.mutex.Lock()
			fc.stats.MessagesProcessed++
			
			// Update average latency
			if fc.stats.MessagesProcessed == 1 {
				fc.stats.AvgLatencyUs = float64(processingTime.Microseconds())
			} else {
				fc.stats.AvgLatencyUs = (fc.stats.AvgLatencyUs*float64(fc.stats.MessagesProcessed-1) + 
					float64(processingTime.Microseconds())) / float64(fc.stats.MessagesProcessed)
			}
			fc.mutex.Unlock()

		case err, ok := <-errorChan:
			if !ok {
				log.Println("Error channel closed")
				return nil
			}
			return fmt.Errorf("stream error: %w", err)

		case <-ctx.Done():
			log.Println("Consumer cancelled by context")
			return ctx.Err()
		}
	}
}

// GetStats returns current processing statistics
func (fc *FlowControlledConsumer) GetStats() ProcessingStats {
	fc.mutex.RLock()
	defer fc.mutex.RUnlock()
	
	stats := *fc.stats
	stats.StartTime = fc.stats.StartTime
	return stats
}

// Close closes all gRPC connections
func (c *GrpcClient) Close() error {
	var errors []error
	
	for i, conn := range c.connections {
		if conn != nil {
			if err := conn.Close(); err != nil {
				errors = append(errors, fmt.Errorf("failed to close connection %d: %w", i, err))
			}
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors closing connections: %v", errors)
	}

	c.logger.Println("All gRPC connections closed")
	return nil
}

func main() {
	// Configure multiple server addresses for load balancing
	serverAddresses := []string{
		"localhost:50051",
		"localhost:50052", 
		"localhost:50053",
	}

	// Create client with failover support
	client, err := NewGrpcClient(serverAddresses, 3)
	if err != nil {
		log.Fatalf("Failed to create gRPC client: %v", err)
	}
	defer client.Close()

	// Create flow-controlled consumer
	consumer := NewFlowControlledConsumer(client, 1000)

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Define tick data handler
	tickHandler := func(tick *TickData) error {
		fmt.Printf("Processing tick: %s @ %.4f (volume: %d, sequence: %d)\n",
			tick.Symbol, tick.LastPrice, tick.Volume, tick.Sequence)
		
		// Add your custom processing logic here
		// e.g., store to database, trigger alerts, etc.
		
		return nil
	}

	// Start consuming tick data
	symbols := []string{"BTCUSDT", "ETHUSDT", "ADAUSDT"}
	exchange := "binance"

	fmt.Printf("Starting to consume tick data for %v from %s\n", symbols, exchange)

	// Handle graceful shutdown
	go func() {
		// Simulate running for some time then cancel
		time.Sleep(30 * time.Second)
		cancel()
	}()

	// Run consumer
	if err := consumer.ConsumeTickData(ctx, symbols, exchange, tickHandler); err != nil {
		if err != context.Canceled && err != context.DeadlineExceeded {
			log.Printf("Consumer error: %v", err)
		}
	}

	// Print final statistics
	stats := consumer.GetStats()
	duration := time.Since(stats.StartTime)
	
	fmt.Printf("\nProcessing completed. Stats:\n")
	fmt.Printf("  Duration: %v\n", duration)
	fmt.Printf("  Messages Received: %d\n", stats.MessagesReceived)
	fmt.Printf("  Messages Processed: %d\n", stats.MessagesProcessed)
	fmt.Printf("  Processing Errors: %d\n", stats.ProcessingErrors)
	fmt.Printf("  Average Latency: %.2f μs\n", stats.AvgLatencyUs)
	
	if duration.Seconds() > 0 {
		fmt.Printf("  Throughput: %.2f messages/sec\n", 
			float64(stats.MessagesProcessed)/duration.Seconds())
	}
}