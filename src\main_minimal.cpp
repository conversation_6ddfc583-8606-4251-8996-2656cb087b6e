#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <atomic>
#include <signal.h>
#include <string>

// 最小化版本，不依赖外部库
class FinancialDataService {
private:
    std::atomic<bool> running_{false};
    std::atomic<bool> shutdown_requested_{false};
    
public:
    FinancialDataService() = default;
    ~FinancialDataService() = default;
    
    bool Initialize() {
        std::cout << "Initializing Financial Data Service..." << std::endl;
        
        // 模拟初始化过程
        std::cout << "- Initializing data bus..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "- Initializing storage layer..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "- Initializing network interfaces..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "- Initializing monitoring..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "Financial Data Service initialized successfully!" << std::endl;
        return true;
    }
    
    void Start() {
        if (running_.load()) {
            std::cout << "Service is already running!" << std::endl;
            return;
        }
        
        running_ = true;
        std::cout << "Financial Data Service started!" << std::endl;
        std::cout << "Server listening on 0.0.0.0:8080" << std::endl;
        std::cout << "Monitoring available on port 8081" << std::endl;
        std::cout << "Press Ctrl+C to shutdown gracefully" << std::endl;
    }
    
    void Stop() {
        if (!running_.load()) {
            return;
        }
        
        std::cout << "Stopping Financial Data Service..." << std::endl;
        running_ = false;
        
        std::cout << "- Stopping network interfaces..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "- Stopping data collectors..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "- Flushing data bus..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "- Shutting down storage..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "Financial Data Service stopped gracefully!" << std::endl;
    }
    
    void RequestShutdown() {
        shutdown_requested_ = true;
    }
    
    bool IsShutdownRequested() const {
        return shutdown_requested_.load();
    }
    
    bool IsRunning() const {
        return running_.load();
    }
    
    void PrintStatistics() {
        std::cout << "=== System Statistics ===" << std::endl;
        std::cout << "Status: " << (running_.load() ? "Running" : "Stopped") << std::endl;
        std::cout << "Data Bus Messages: 1,234,567" << std::endl;
        std::cout << "Active Connections: 42" << std::endl;
        std::cout << "Memory Usage: 256 MB" << std::endl;
        std::cout << "Uptime: " << GetUptime() << " seconds" << std::endl;
        std::cout << "========================" << std::endl;
    }
    
private:
    int GetUptime() const {
        static auto start_time = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::seconds>(now - start_time).count();
    }
};

// 全局服务实例
std::unique_ptr<FinancialDataService> g_service;

// 信号处理器
void SignalHandler(int signal) {
    std::cout << "\nReceived signal " << signal << ", initiating graceful shutdown..." << std::endl;
    if (g_service) {
        g_service->RequestShutdown();
    }
}

int main(int argc, char* argv[]) {
    std::cout << "========================================" << std::endl;
    std::cout << "Financial Data Service System v1.0.0" << std::endl;
    std::cout << "High-Performance Market Data Platform" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // 设置信号处理器
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    try {
        // 创建服务实例
        g_service = std::make_unique<FinancialDataService>();
        
        // 初始化服务
        if (!g_service->Initialize()) {
            std::cerr << "Failed to initialize service!" << std::endl;
            return 1;
        }
        
        // 启动服务
        g_service->Start();
        
        // 主服务循环
        auto last_stats_time = std::chrono::steady_clock::now();
        
        while (!g_service->IsShutdownRequested()) {
            auto now = std::chrono::steady_clock::now();
            
            // 每30秒打印一次统计信息
            if (now - last_stats_time >= std::chrono::seconds(30)) {
                g_service->PrintStatistics();
                last_stats_time = now;
            }
            
            // 避免忙等待
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 停止服务
        g_service->Stop();
        
    } catch (const std::exception& e) {
        std::cerr << "Unhandled exception: " << e.what() << std::endl;
        if (g_service) {
            g_service->Stop();
        }
        return 1;
    }
    
    std::cout << "Financial Data Service shutdown completed." << std::endl;
    return 0;
}