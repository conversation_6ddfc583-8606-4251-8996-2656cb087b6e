# 历史行情数据存储方案设计

## 1. 存储需求分析

### 1.1 数据规模估算

#### 1.1.1 每日数据量
| 数据类型 | 频率 | 每条约 | 每日条数 | 每日大小 |
|----------|------|--------|----------|----------|
| **股票Tick** | 500ms | 128B | 576万 | 720MB |
| **期货Tick** | 250ms | 256B | 1152万 | 2.9GB |
| **Level2深度** | 3ms | 1KB | 9600万 | 96GB |
| **逐笔成交** | 实时 | 64B | 5000万 | 3.2GB |
| **委托簿** | 实时 | 256B | 2000万 | 5.1GB |

**总计每日**: 约107GB原始数据
**年化数据**: 约39TB原始数据
**5年历史**: 约195TB原始数据

#### 1.1.2 存储增长率
- **年增长率**: 30% (新上市品种+频率提升)
- **压缩比**: 8:1 (<PERSON><PERSON><PERSON>+ZSTD)
- **有效存储**: 24TB/年压缩后

### 1.2 性能需求

#### 1.2.1 查询性能
| 查询类型 | 响应时间 | 并发度 |
|----------|----------|--------|
| **单条查询** | < 1ms | 1000 |
| **范围查询** | < 100ms | 100 |
| **聚合查询** | < 1s | 50 |
| **全表扫描** | < 10s | 10 |

#### 1.2.2 写入性能
- **峰值写入**: 100万条/秒
- **平均写入**: 10万条/秒
- **批量写入**: 支持10万条批量写入

## 2. 存储架构设计

### 2.1 分层存储架构

#### 2.1.1 三层存储模型
```
┌─────────────────────────────────────────────────────────────┐
│                    应用层接口                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   热数据层      │    温数据层      │       冷数据层           │
│  (内存+SSD)     │   (SSD+HDD)      │       (对象存储)         │
├─────────────────┼─────────────────┼─────────────────────────┤
│ • 最近7天数据   │ • 7天-2年数据   │ • 2年以上历史数据        │
│ • 内存数据库    │ • 时间序列DB    │ • 对象存储+元数据        │
│ • 微秒级延迟    │ • 毫秒级延迟    │ • 秒级延迟               │
│ • 100%可用      │ • 99.9%可用     │ • 99%可用                │
└─────────────────┴─────────────────┴─────────────────────────┘
```

#### 2.1.2 存储技术选型

**热数据层**
- **技术**: Redis + TimescaleDB
- **特点**: 内存存储，SSD持久化
- **延迟**: < 100μs读取，< 1ms写入
- **容量**: 1TB (压缩后)

**温数据层**
- **技术**: ClickHouse + Apache Parquet
- **特点**: 列式存储，高压缩比
- **延迟**: < 10ms读取，< 100ms写入
- **容量**: 50TB (压缩后)

**冷数据层**
- **技术**: AWS S3 + MinIO + Apache Iceberg
- **特点**: 对象存储，极低成本
- **延迟**: < 1s读取，批量写入
- **容量**: 200TB+ (压缩后)

### 2.2 数据库设计

#### 2.2.1 Redis热数据设计
```redis
# 数据结构
# 时间序列: symbol:timestamp > data
HSET cu2409:20240720093000123 price 78560 volume 10 bid 78550 ask 78570

# 最近N条记录: symbol:latest > [data1, data2, ...]
LPUSH cu2409:latest "{...}"
LTRIM cu2409:latest 0 999

# 索引: symbol:index > [timestamp1, timestamp2, ...]
ZADD cu2409:index 1721446200123 "20240720093000123"
```

#### 2.2.2 ClickHouse温数据设计
```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS market_data;

-- 期货tick数据表
CREATE TABLE market_data.futures_tick (
    timestamp DateTime64(9),
    symbol String,
    exchange String,
    last_price Float64,
    volume UInt64,
    turnover Float64,
    open_interest Float64,
    bid_price Float64,
    bid_volume UInt32,
    ask_price Float64,
    ask_volume UInt32,
    INDEX idx_symbol (symbol) TYPE bloom_filter(0.01),
    INDEX idx_time (timestamp) TYPE minmax GRANULARITY 3
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR;

-- Level2深度数据表
CREATE TABLE market_data.level2_depth (
    timestamp DateTime64(9),
    symbol String,
    exchange String,
    bids Nested(
        price Float64,
        volume UInt32,
        order_count UInt16
    ),
    asks Nested(
        price Float64,
        volume UInt32,
        order_count UInt16
    ),
    INDEX idx_symbol (symbol) TYPE bloom_filter(0.01)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
TTL timestamp + INTERVAL 2 YEAR;
```

#### 2.2.3 Parquet冷数据设计
```python
import pyarrow as pa
import pyarrow.parquet as pq

# 定义schema
schema = pa.schema([
    ('timestamp', pa.timestamp('ns')),
    ('symbol', pa.string()),
    ('exchange', pa.string()),
    ('last_price', pa.float64()),
    ('volume', pa.int64()),
    ('turnover', pa.float64()),
    ('open_interest', pa.float64()),
    ('bid_price', pa.float64()),
    ('bid_volume', pa.int32()),
    ('ask_price', pa.float64()),
    ('ask_volume', pa.int32()),
    ('year', pa.int32()),
    ('month', pa.int32()),
    ('day', pa.int32()),
    ('hour', pa.int32())
])

# 分区策略
partition_cols = ['year', 'month', 'day', 'symbol']
```

## 3. 数据压缩与编码

### 3.1 压缩算法选择

#### 3.1.1 压缩算法对比
| 算法 | 压缩比 | 压缩速度 | 解压速度 | 适用场景 |
|------|--------|----------|----------|----------|
| **ZSTD** | 8:1 | 500MB/s | 1500MB/s | 通用场景 |
| **LZ4** | 4:1 | 1000MB/s | 3000MB/s | 低延迟场景 |
| **Snappy** | 3:1 | 800MB/s | 2000MB/s | 实时场景 |
| **GZIP** | 10:1 | 100MB/s | 500MB/s | 归档场景 |

#### 3.1.2 列式存储优化
```python
# 使用字典编码优化字符串列
dict_encoded = pc.dictionary_encode(table['symbol'])

# 使用bit-packing优化数值列
bit_packed = pc.bit_pack(table['volume'])

# 使用delta编码优化时间戳
delta_encoded = pc.delta_encode(table['timestamp'])
```

### 3.2 数据分区策略

#### 3.2.1 时间分区
```
market_data/
├── year=2024/
│   ├── month=07/
│   │   ├── day=20/
│   │   │   ├── symbol=CU2409/
│   │   │   │   └── part-00000.parquet
│   │   │   └── symbol=AL2409/
│   │   │       └── part-00000.parquet
│   │   └── day=21/
│   └── month=08/
└── year=2023/
```

#### 3.2.2 哈希分区
```python
# 基于symbol的哈希分区
hash_value = hash(symbol) % num_partitions
partition_path = f"symbol_hash={hash_value}"
```

## 4. 查询优化

### 4.1 索引策略

#### 4.1.1 ClickHouse索引
```sql
-- 主键索引
ORDER BY (symbol, timestamp)

-- 二级索引
INDEX idx_symbol (symbol) TYPE bloom_filter(0.01),
INDEX idx_price (last_price) TYPE minmax GRANULARITY 3,
INDEX idx_time (timestamp) TYPE minmax GRANULARITY 3

-- 跳数索引
INDEX idx_skip (symbol, toStartOfHour(timestamp)) TYPE set(100) GRANULARITY 1
```

#### 4.1.2 Parquet索引
```python
# 使用pyarrow的row group统计信息
import pyarrow.dataset as ds

# 创建数据集
dataset = ds.dataset("/data/market_data", format="parquet")

# 使用分区过滤
filtered = dataset.to_table(
    filter=ds.field('symbol') == 'CU2409',
    columns=['timestamp', 'last_price', 'volume']
)
```

### 4.2 查询引擎优化

#### 4.2.1 时序查询优化
```sql
-- 使用物化视图加速查询
CREATE MATERIALIZED VIEW market_data.daily_summary
ENGINE = AggregatingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, toDate(timestamp))
AS SELECT
    symbol,
    toDate(timestamp) as date,
    argMin(last_price, timestamp) as open_price,
    argMax(last_price, timestamp) as close_price,
    max(last_price) as high_price,
    min(last_price) as low_price,
    sum(volume) as total_volume,
    sum(turnover) as total_turnover
FROM market_data.futures_tick
GROUP BY symbol, toDate(timestamp);
```

#### 4.2.2 并行查询优化
```python
# 使用Dask进行并行计算
import dask.dataframe as dd

# 读取分区数据
ddf = dd.read_parquet(
    "/data/market_data",
    engine="pyarrow",
    filters=[('symbol', '==', 'CU2409')],
    columns=['timestamp', 'last_price', 'volume']
)

# 并行计算VWAP
vwap = (ddf['last_price'] * ddf['volume']).sum() / ddf['volume'].sum()
result = vwap.compute()
```

## 5. 数据生命周期管理

### 5.1 自动分层策略

#### 5.1.1 生命周期规则
```json
{
  "lifecycle_rules": [
    {
      "name": "hot_to_warm",
      "condition": "age > 7 days",
      "action": "move_to_clickhouse",
      "compression": "zstd"
    },
    {
      "name": "warm_to_cold",
      "condition": "age > 2 years",
      "action": "move_to_s3",
      "compression": "gzip",
      "tier": "glacier"
    }
  ]
}
```

#### 5.1.2 自动化脚本
```python
class DataLifecycleManager:
    def __init__(self):
        self.hot_retention_days = 7
        self.warm_retention_days = 730
        
    def migrate_old_data(self):
        # 迁移7天前的数据到温存储
        cutoff_date = datetime.now() - timedelta(days=7)
        self.migrate_to_clickhouse(cutoff_date)
        
        # 迁移2年前的数据到冷存储
        cutoff_date = datetime.now() - timedelta(days=730)
        self.migrate_to_s3(cutoff_date)
```

### 5.2 备份策略

#### 5.2.1 多级备份
```bash
#!/bin/bash
# 每日备份脚本
DATE=$(date +%Y%m%d)

# 本地备份
cp -r /data/market_data /backup/local/$DATE

# 远程备份
aws s3 sync /data/market_data s3://market-data-backup/$DATE/

# 增量备份
rsync -avz --delete /data/market_data backup-server:/backup/remote/
```

#### 5.2.2 备份验证
```python
def validate_backup(backup_path):
    """验证备份完整性"""
    original_md5 = calculate_md5(original_path)
    backup_md5 = calculate_md5(backup_path)
    
    if original_md5 != backup_md5:
        raise ValueError("Backup verification failed")
    
    # 验证数据可读取
    sample_data = read_sample_data(backup_path)
    if not validate_data_integrity(sample_data):
        raise ValueError("Data integrity check failed")
```

## 6. 性能基准测试

### 6.1 写入性能测试

#### 6.1.1 ClickHouse写入测试
```sql
-- 测试批量插入性能
INSERT INTO market_data.futures_tick
SELECT 
    now64(9) + INTERVAL number MICROSECOND as timestamp,
    'CU2409' as symbol,
    'SHFE' as exchange,
    78560 + rand() % 100 as last_price,
    rand() % 1000 as volume,
    78560000000 + rand() % 1000000000 as turnover,
    150000 + rand() % 10000 as open_interest,
    78550 + rand() % 100 as bid_price,
    rand() % 100 as bid_volume,
    78570 + rand() % 100 as ask_price,
    rand() % 100 as ask_volume
FROM numbers(1000000);
```

#### 6.1.2 Parquet写入测试
```python
import time
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq

# 生成测试数据
n_rows = 10_000_000
df = pd.DataFrame({
    'timestamp': pd.date_range('2024-01-01', periods=n_rows, freq='1ms'),
    'symbol': ['CU2409'] * n_rows,
    'last_price': np.random.uniform(78000, 79000, n_rows),
    'volume': np.random.randint(1, 1000, n_rows)
})

# 写入性能测试
start_time = time.time()
table = pa.Table.from_pandas(df)
pq.write_table(table, '/tmp/test.parquet', compression='zstd')
write_time = time.time() - start_time
print(f"Write speed: {n_rows/write_time:.0f} rows/sec")
```

### 6.2 查询性能测试

#### 6.2.1 时间范围查询
```sql
-- 测试一天数据查询
SELECT count(), avg(last_price), max(last_price), min(last_price)
FROM market_data.futures_tick
WHERE symbol = 'CU2409'
  AND timestamp >= '2024-07-20 00:00:00'
  AND timestamp < '2024-07-21 00:00:00';
```

#### 6.2.2 聚合查询测试
```sql
-- 测试分钟级聚合
SELECT 
    toStartOfMinute(timestamp) as minute,
    argMin(last_price, timestamp) as open,
    argMax(last_price, timestamp) as close,
    max(last_price) as high,
    min(last_price) as low,
    sum(volume) as volume
FROM market_data.futures_tick
WHERE symbol = 'CU2409'
  AND timestamp >= '2024-07-20 09:00:00'
  AND timestamp < '2024-07-20 15:00:00'
GROUP BY minute
ORDER BY minute;
```

## 7. 监控与告警

### 7.1 存储监控

#### 7.1.1 存储容量监控
```python
class StorageMonitor:
    def __init__(self):
        self.thresholds = {
            'disk_usage': 0.85,
            'memory_usage': 0.90,
            'write_latency': 100,  # ms
            'read_latency': 50     # ms
        }
    
    def check_storage_health(self):
        disk_usage = psutil.disk_usage('/data').percent
        memory_usage = psutil.virtual_memory().percent
        
        if disk_usage > self.thresholds['disk_usage']:
            self.send_alert("Disk usage high", disk_usage)
        
        if memory_usage > self.thresholds['memory_usage']:
            self.send_alert("Memory usage high", memory_usage)
```

#### 7.1.2 查询性能监控
```python
class QueryMonitor:
    def monitor_query_performance(self, query):
        start_time = time.time()
        result = execute_query(query)
        query_time = time.time() - start_time
        
        if query_time > 1.0:  # 1秒阈值
            logger.warning(f"Slow query detected: {query_time}s - {query}")
        
        return result
```

### 7.2 告警配置

#### 7.2.1 Prometheus告警规则
```yaml
groups:
  - name: market_data_storage
    rules:
      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes{mountpoint="/data"} - node_filesystem_avail_bytes{mountpoint="/data"}) / node_filesystem_size_bytes{mountpoint="/data"} > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Market data storage disk usage is high"
          description: "Disk usage is above 85% for 5 minutes"
      
      - alert: SlowQueries
        expr: clickhouse_query_duration_seconds_bucket{le="1.0"} < 0.95
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Slow queries detected in ClickHouse"
          description: "More than 5% of queries are taking longer than 1 second"
```

通过以上设计，可以实现高性能、高可用、低成本的历史行情数据存储系统，满足量化投资和高频交易的长期数据需求。