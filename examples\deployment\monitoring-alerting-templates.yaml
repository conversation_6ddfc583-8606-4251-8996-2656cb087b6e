# Market Data Collection Enhancement - Monitoring and Alerting Templates

# Prometheus Configuration Template
prometheus_config: |
  global:
    scrape_interval: 15s
    evaluation_interval: 15s
    external_labels:
      cluster: 'market-data-production'
      environment: 'production'

  rule_files:
    - "market_data_alerts.yml"

  scrape_configs:
    # Market Data Collector
    - job_name: 'market-data-collector'
      static_configs:
        - targets: ['market-data-collector-service:9090']
      scrape_interval: 5s
      metrics_path: /metrics
      scrape_timeout: 10s

    # Redis Hot Storage
    - job_name: 'redis-exporter'
      static_configs:
        - targets: ['redis-exporter:9121']
      scrape_interval: 10s

    # ClickHouse Warm Storage
    - job_name: 'clickhouse-exporter'
      static_configs:
        - targets: ['clickhouse-exporter:9116']
      scrape_interval: 30s

    # MinIO Cold Storage
    - job_name: 'minio-exporter'
      static_configs:
        - targets: ['minio-service:9000']
      scrape_interval: 30s
      metrics_path: /minio/v2/metrics/cluster

    # Kubernetes Metrics
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
        - role: pod
          namespaces:
            names:
              - market-data
      relabel_configs:
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true

  alerting:
    alertmanagers:
      - static_configs:
          - targets:
            - alertmanager:9093

---
# Alerting Rules Template
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-alert-rules
  namespace: market-data
data:
  market_data_alerts.yml: |
    groups:
    - name: market_data_collection
      rules:
      # Data Collection Alerts
      - alert: DataCollectionStopped
        expr: increase(market_data_collection_total[5m]) == 0
        for: 2m
        labels:
          severity: critical
          component: collection
        annotations:
          summary: "Data collection has stopped"
          description: "No data has been collected in the last 5 minutes for collector {{ $labels.collector_type }}"
          runbook_url: "https://docs.company.com/runbooks/data-collection-stopped"

      - alert: HighCollectionErrorRate
        expr: rate(market_data_collection_errors_total[5m]) / rate(market_data_collection_total[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          component: collection
        annotations:
          summary: "High error rate in data collection"
          description: "Error rate is {{ $value | humanizePercentage }} for collector {{ $labels.collector_type }}"

      - alert: CollectionLatencyHigh
        expr: histogram_quantile(0.95, rate(collection_duration_seconds_bucket[5m])) > 30
        for: 5m
        labels:
          severity: warning
          component: collection
        annotations:
          summary: "High collection latency detected"
          description: "95th percentile collection latency is {{ $value }}s"

      # Storage Alerts
      - alert: RedisConnectionFailure
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
          component: storage
          storage_layer: hot
        annotations:
          summary: "Redis connection failure"
          description: "Cannot connect to Redis hot storage"

      - alert: RedisMemoryUsageHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          component: storage
          storage_layer: hot
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      - alert: ClickHouseConnectionFailure
        expr: clickhouse_up == 0
        for: 2m
        labels:
          severity: critical
          component: storage
          storage_layer: warm
        annotations:
          summary: "ClickHouse connection failure"
          description: "Cannot connect to ClickHouse warm storage"

      - alert: ClickHouseQueryLatencyHigh
        expr: histogram_quantile(0.95, rate(clickhouse_query_duration_seconds_bucket[5m])) > 10
        for: 5m
        labels:
          severity: warning
          component: storage
          storage_layer: warm
        annotations:
          summary: "ClickHouse query latency is high"
          description: "95th percentile query latency is {{ $value }}s"

      - alert: StorageSpaceRunningLow
        expr: (node_filesystem_avail_bytes{mountpoint="/data"} / node_filesystem_size_bytes{mountpoint="/data"}) < 0.1
        for: 5m
        labels:
          severity: critical
          component: storage
        annotations:
          summary: "Storage space running low"
          description: "Available storage space is {{ $value | humanizePercentage }}"

      # API Performance Alerts
      - alert: APILatencyHigh
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "API latency is high"
          description: "95th percentile API latency is {{ $value }}s for endpoint {{ $labels.endpoint }}"

      - alert: APIErrorRateHigh
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High API error rate"
          description: "API error rate is {{ $value | humanizePercentage }}"

      # System Resource Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.85
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
          component: kubernetes
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is restarting frequently"

    - name: market_data_business_logic
      rules:
      # Business Logic Alerts
      - alert: DataGapDetected
        expr: increase(data_gap_detected_total[5m]) > 0
        for: 1m
        labels:
          severity: warning
          component: data_quality
        annotations:
          summary: "Data gap detected"
          description: "Data gap detected for symbol {{ $labels.symbol }} at {{ $labels.timestamp }}"

      - alert: DataQualityIssue
        expr: rate(data_validation_failures_total[5m]) > 0.01
        for: 3m
        labels:
          severity: warning
          component: data_quality
        annotations:
          summary: "Data quality issues detected"
          description: "Data validation failure rate is {{ $value | humanizePercentage }}"

      - alert: UnusualTradingVolume
        expr: abs(trading_volume_current - trading_volume_average_7d) / trading_volume_average_7d > 5
        for: 2m
        labels:
          severity: info
          component: market_analysis
        annotations:
          summary: "Unusual trading volume detected"
          description: "Trading volume for {{ $labels.symbol }} is {{ $value }}x the 7-day average"

---
# Alertmanager Configuration Template
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: market-data
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.company.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'password'

    route:
      group_by: ['alertname', 'component']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default-receiver'
      routes:
      - match:
          severity: critical
        receiver: 'critical-alerts'
        group_wait: 5s
        repeat_interval: 30m
      - match:
          component: collection
        receiver: 'collection-team'
      - match:
          component: storage
        receiver: 'infrastructure-team'
      - match:
          component: api
        receiver: 'api-team'

    receivers:
    - name: 'default-receiver'
      email_configs:
      - to: '<EMAIL>'
        subject: '[Market Data] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}

    - name: 'critical-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] Market Data Alert'
        body: |
          CRITICAL ALERT TRIGGERED
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
      slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#critical-alerts'
        title: 'Critical Market Data Alert'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          {{ end }}

    - name: 'collection-team'
      email_configs:
      - to: '<EMAIL>'
        subject: '[Collection] {{ .GroupLabels.alertname }}'

    - name: 'infrastructure-team'
      email_configs:
      - to: '<EMAIL>'
        subject: '[Infrastructure] {{ .GroupLabels.alertname }}'

    - name: 'api-team'
      email_configs:
      - to: '<EMAIL>'
        subject: '[API] {{ .GroupLabels.alertname }}'

    inhibit_rules:
    - source_match:
        severity: 'critical'
      target_match:
        severity: 'warning'
      equal: ['alertname', 'component']

---
# Grafana Dashboard Template
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-market-data
  namespace: market-data
data:
  market-data-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Market Data Collection Enhancement",
        "tags": ["market-data", "collection", "performance"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Data Collection Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(rate(market_data_collection_total[5m]))",
                "legendFormat": "Records/sec"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "reqps",
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "yellow", "value": 50},
                    {"color": "green", "value": 100}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Collection Error Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(market_data_collection_errors_total[5m]) / rate(market_data_collection_total[5m])",
                "legendFormat": "Error Rate"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percentunit",
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": 0},
                    {"color": "yellow", "value": 0.01},
                    {"color": "red", "value": 0.05}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Storage Layer Performance",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(storage_write_duration_seconds_bucket[5m]))",
                "legendFormat": "{{storage_layer}} - 95th percentile"
              },
              {
                "expr": "histogram_quantile(0.50, rate(storage_write_duration_seconds_bucket[5m]))",
                "legendFormat": "{{storage_layer}} - 50th percentile"
              }
            ],
            "yAxes": [
              {
                "label": "Duration (seconds)",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "API Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (endpoint) (rate(http_requests_total[5m]))",
                "legendFormat": "{{endpoint}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
          },
          {
            "id": 5,
            "title": "System Resource Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
                "legendFormat": "CPU Usage %"
              },
              {
                "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
                "legendFormat": "Memory Usage %"
              }
            ],
            "yAxes": [
              {
                "label": "Percentage",
                "min": 0,
                "max": 100
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }

---
# Custom Metrics Exporter Template
apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-metrics-config
  namespace: market-data
data:
  metrics_config.yaml: |
    # Custom metrics configuration for market data specific monitoring
    custom_metrics:
      # Data collection metrics
      - name: market_data_collection_total
        help: "Total number of market data points collected"
        type: counter
        labels: [collector_type, symbol, data_type]

      - name: market_data_collection_errors_total
        help: "Total number of collection errors"
        type: counter
        labels: [collector_type, error_type, symbol]

      - name: collection_duration_seconds
        help: "Time spent collecting data"
        type: histogram
        labels: [collector_type, symbol]
        buckets: [0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0]

      # Storage metrics
      - name: storage_write_duration_seconds
        help: "Time spent writing to storage"
        type: histogram
        labels: [storage_layer, operation]
        buckets: [0.001, 0.01, 0.1, 0.5, 1.0, 2.0, 5.0]

      - name: storage_operations_total
        help: "Total number of storage operations"
        type: counter
        labels: [storage_layer, operation, status]

      # Data quality metrics
      - name: data_validation_failures_total
        help: "Total number of data validation failures"
        type: counter
        labels: [validation_type, symbol]

      - name: data_gap_detected_total
        help: "Total number of data gaps detected"
        type: counter
        labels: [symbol, gap_duration]

      # API metrics
      - name: http_requests_total
        help: "Total number of HTTP requests"
        type: counter
        labels: [method, endpoint, status]

      - name: http_request_duration_seconds
        help: "HTTP request duration"
        type: histogram
        labels: [method, endpoint]
        buckets: [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]

      # Business metrics
      - name: trading_volume_current
        help: "Current trading volume"
        type: gauge
        labels: [symbol, exchange]

      - name: trading_volume_average_7d
        help: "7-day average trading volume"
        type: gauge
        labels: [symbol, exchange]

    # Alert thresholds
    alert_thresholds:
      collection_error_rate: 0.05
      api_latency_p95: 5.0
      storage_latency_p95: 2.0
      memory_usage: 0.85
      cpu_usage: 0.80
      disk_usage: 0.90

    # Notification channels
    notification_channels:
      email:
        enabled: true
        smtp_server: "smtp.company.com:587"
        from_address: "<EMAIL>"
        recipients:
          critical: ["<EMAIL>"]
          warning: ["<EMAIL>"]
          info: ["<EMAIL>"]

      slack:
        enabled: true
        webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
        channels:
          critical: "#critical-alerts"
          warning: "#alerts"
          info: "#monitoring"

      pagerduty:
        enabled: false
        integration_key: "YOUR_PAGERDUTY_INTEGRATION_KEY"
        severity_mapping:
          critical: "critical"
          warning: "warning"
          info: "info"