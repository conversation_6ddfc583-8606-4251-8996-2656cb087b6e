"""
Python configuration validators
"""

import re
import ipaddress
from typing import Dict, Any, List, Set
from src.config.config_manager_python import ConfigValidator, ValidationResult


class PytdxConfigValidator(ConfigValidator):
    """pytdx采集器配置验证器"""
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        result = ValidationResult()
        
        # 验证必需字段
        required_fields = ['enabled', 'servers', 'batch_size']
        for field in required_fields:
            if field not in config:
                result.add_error(f"Missing required field: {field}")
        
        # 验证enabled字段
        if 'enabled' in config and not isinstance(config['enabled'], bool):
            result.add_error("Field 'enabled' must be boolean")
        
        # 验证servers配置
        if 'servers' in config:
            if not isinstance(config['servers'], list):
                result.add_error("Field 'servers' must be a list")
            else:
                for i, server in enumerate(config['servers']):
                    if not isinstance(server, dict):
                        result.add_error(f"Server {i} must be a dictionary")
                        continue
                    
                    if 'host' not in server:
                        result.add_error(f"Server {i} missing 'host' field")
                    elif not isinstance(server['host'], str):
                        result.add_error(f"Server {i} 'host' must be string")
                    else:
                        # 验证IP地址或域名
                        try:
                            ipaddress.ip_address(server['host'])
                        except ValueError:
                            # 验证域名格式
                            if not re.match(r'^[a-zA-Z0-9.-]+$', server['host']):
                                result.add_error(f"Server {i} invalid host format")
                    
                    if 'port' not in server:
                        result.add_error(f"Server {i} missing 'port' field")
                    elif not isinstance(server['port'], int):
                        result.add_error(f"Server {i} 'port' must be integer")
                    elif not (1 <= server['port'] <= 65535):
                        result.add_error(f"Server {i} port must be between 1 and 65535")
        
        # 验证batch_size
        if 'batch_size' in config:
            if not isinstance(config['batch_size'], int):
                result.add_error("Field 'batch_size' must be integer")
            elif not (1 <= config['batch_size'] <= 10000):
                result.add_error("Field 'batch_size' must be between 1 and 10000")
        
        # 验证concurrent_requests
        if 'concurrent_requests' in config:
            if not isinstance(config['concurrent_requests'], int):
                result.add_error("Field 'concurrent_requests' must be integer")
            elif not (1 <= config['concurrent_requests'] <= 50):
                result.add_error("Field 'concurrent_requests' must be between 1 and 50")
        
        # 验证timeout_seconds
        if 'timeout_seconds' in config:
            if not isinstance(config['timeout_seconds'], int):
                result.add_error("Field 'timeout_seconds' must be integer")
            elif not (1 <= config['timeout_seconds'] <= 300):
                result.add_error("Field 'timeout_seconds' must be between 1 and 300")
        
        # 验证retry_attempts
        if 'retry_attempts' in config:
            if not isinstance(config['retry_attempts'], int):
                result.add_error("Field 'retry_attempts' must be integer")
            elif not (0 <= config['retry_attempts'] <= 10):
                result.add_error("Field 'retry_attempts' must be between 0 and 10")
        
        # 验证symbols配置
        if 'symbols' in config:
            if isinstance(config['symbols'], list):
                for i, symbol in enumerate(config['symbols']):
                    if not isinstance(symbol, str):
                        result.add_error(f"Symbol {i} must be string")
                    elif symbol != "all" and not re.match(r'^[A-Za-z0-9]+$', symbol):
                        result.add_warning(f"Symbol {i} format may be invalid: {symbol}")
            elif config['symbols'] != "all":
                result.add_error("Field 'symbols' must be list or 'all'")
        
        # 验证data_types
        if 'data_types' in config:
            if not isinstance(config['data_types'], list):
                result.add_error("Field 'data_types' must be list")
            else:
                valid_types = {'tick', 'kline', 'level2'}
                for data_type in config['data_types']:
                    if data_type not in valid_types:
                        result.add_error(f"Invalid data type: {data_type}")
        
        return result
    
    def get_validator_name(self) -> str:
        return "PytdxConfigValidator"


class CollectionConfigValidator(ConfigValidator):
    """采集配置验证器"""
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        result = ValidationResult()
        
        # 验证pytdx配置
        if 'pytdx' in config:
            pytdx_validator = PytdxConfigValidator()
            pytdx_result = pytdx_validator.validate(config['pytdx'])
            
            if not pytdx_result.is_valid:
                result.is_valid = False
            
            for error in pytdx_result.errors:
                result.errors.append(f"[pytdx] {error}")
            
            for warning in pytdx_result.warnings:
                result.warnings.append(f"[pytdx] {warning}")
        
        # 验证CTP配置
        if 'ctp' in config:
            ctp_config = config['ctp']
            
            if 'enabled' in ctp_config and not isinstance(ctp_config['enabled'], bool):
                result.add_error("[ctp] Field 'enabled' must be boolean")
            
            if 'config_path' in ctp_config:
                if not isinstance(ctp_config['config_path'], str):
                    result.add_error("[ctp] Field 'config_path' must be string")
                elif not ctp_config['config_path'].endswith('.json'):
                    result.add_warning("[ctp] Config path should end with .json")
            
            if 'failover_timeout' in ctp_config:
                if not isinstance(ctp_config['failover_timeout'], int):
                    result.add_error("[ctp] Field 'failover_timeout' must be integer")
                elif not (5 <= ctp_config['failover_timeout'] <= 300):
                    result.add_error("[ctp] Field 'failover_timeout' must be between 5 and 300")
        
        # 验证coordination配置
        if 'coordination' in config:
            coord_config = config['coordination']
            
            if 'priority_source' in coord_config:
                if coord_config['priority_source'] not in ['ctp', 'pytdx']:
                    result.add_error("[coordination] priority_source must be 'ctp' or 'pytdx'")
            
            if 'overlap_tolerance_seconds' in coord_config:
                if not isinstance(coord_config['overlap_tolerance_seconds'], int):
                    result.add_error("[coordination] overlap_tolerance_seconds must be integer")
                elif not (0 <= coord_config['overlap_tolerance_seconds'] <= 3600):
                    result.add_error("[coordination] overlap_tolerance_seconds must be between 0 and 3600")
            
            if 'enable_data_merge' in coord_config:
                if not isinstance(coord_config['enable_data_merge'], bool):
                    result.add_error("[coordination] enable_data_merge must be boolean")
        
        return result
    
    def get_validator_name(self) -> str:
        return "CollectionConfigValidator"


class StorageConfigValidator(ConfigValidator):
    """存储配置验证器"""
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        result = ValidationResult()
        
        storage_layers = ['hot_storage', 'warm_storage', 'cold_storage']
        valid_storage_types = {'redis', 'clickhouse', 's3', 'file'}
        
        for layer in storage_layers:
            if layer in config:
                layer_config = config[layer]
                
                if not isinstance(layer_config, dict):
                    result.add_error(f"[{layer}] must be a dictionary")
                    continue
                
                # 验证type字段
                if 'type' not in layer_config:
                    result.add_error(f"[{layer}] missing 'type' field")
                elif layer_config['type'] not in valid_storage_types:
                    result.add_error(f"[{layer}] invalid storage type: {layer_config['type']}")
                
                # 验证retention_days
                if 'retention_days' in layer_config:
                    if not isinstance(layer_config['retention_days'], int):
                        result.add_error(f"[{layer}] retention_days must be integer")
                    elif layer_config['retention_days'] < 1:
                        result.add_error(f"[{layer}] retention_days must be positive")
                
                # 验证config字段
                if 'config' in layer_config:
                    if not isinstance(layer_config['config'], dict):
                        result.add_error(f"[{layer}] config must be dictionary")
                    else:
                        self._validate_storage_config(
                            layer_config['type'], 
                            layer_config['config'], 
                            result, 
                            layer
                        )
        
        # 验证migration配置
        if 'migration' in config:
            migration_config = config['migration']
            
            if 'enabled' in migration_config:
                if not isinstance(migration_config['enabled'], bool):
                    result.add_error("[migration] enabled must be boolean")
            
            if 'batch_size' in migration_config:
                if not isinstance(migration_config['batch_size'], int):
                    result.add_error("[migration] batch_size must be integer")
                elif not (100 <= migration_config['batch_size'] <= 100000):
                    result.add_error("[migration] batch_size must be between 100 and 100000")
            
            if 'parallel_workers' in migration_config:
                if not isinstance(migration_config['parallel_workers'], int):
                    result.add_error("[migration] parallel_workers must be integer")
                elif not (1 <= migration_config['parallel_workers'] <= 50):
                    result.add_error("[migration] parallel_workers must be between 1 and 50")
        
        return result
    
    def _validate_storage_config(self, storage_type: str, storage_config: Dict[str, Any], 
                                result: ValidationResult, layer: str):
        """验证特定存储类型的配置"""
        if storage_type == 'redis':
            self._validate_redis_config(storage_config, result, layer)
        elif storage_type == 'clickhouse':
            self._validate_clickhouse_config(storage_config, result, layer)
        elif storage_type == 's3':
            self._validate_s3_config(storage_config, result, layer)
    
    def _validate_redis_config(self, config: Dict[str, Any], result: ValidationResult, layer: str):
        """验证Redis配置"""
        required_fields = ['host', 'port']
        for field in required_fields:
            if field not in config:
                result.add_error(f"[{layer}] Redis config missing '{field}' field")
        
        if 'port' in config:
            if not isinstance(config['port'], int):
                result.add_error(f"[{layer}] Redis port must be integer")
            elif not (1 <= config['port'] <= 65535):
                result.add_error(f"[{layer}] Redis port must be between 1 and 65535")
        
        if 'database' in config:
            if not isinstance(config['database'], int):
                result.add_error(f"[{layer}] Redis database must be integer")
            elif not (0 <= config['database'] <= 15):
                result.add_error(f"[{layer}] Redis database must be between 0 and 15")
        
        if 'pool_size' in config:
            if not isinstance(config['pool_size'], int):
                result.add_error(f"[{layer}] Redis pool_size must be integer")
            elif not (1 <= config['pool_size'] <= 100):
                result.add_error(f"[{layer}] Redis pool_size must be between 1 and 100")
    
    def _validate_clickhouse_config(self, config: Dict[str, Any], result: ValidationResult, layer: str):
        """验证ClickHouse配置"""
        required_fields = ['host', 'port', 'database', 'username']
        for field in required_fields:
            if field not in config:
                result.add_error(f"[{layer}] ClickHouse config missing '{field}' field")
        
        if 'port' in config:
            if not isinstance(config['port'], int):
                result.add_error(f"[{layer}] ClickHouse port must be integer")
            elif not (1 <= config['port'] <= 65535):
                result.add_error(f"[{layer}] ClickHouse port must be between 1 and 65535")
        
        if 'database' in config:
            if not isinstance(config['database'], str):
                result.add_error(f"[{layer}] ClickHouse database must be string")
            elif not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', config['database']):
                result.add_error(f"[{layer}] ClickHouse database name format invalid")
        
        if 'max_connections' in config:
            if not isinstance(config['max_connections'], int):
                result.add_error(f"[{layer}] ClickHouse max_connections must be integer")
            elif not (1 <= config['max_connections'] <= 1000):
                result.add_error(f"[{layer}] ClickHouse max_connections must be between 1 and 1000")
    
    def _validate_s3_config(self, config: Dict[str, Any], result: ValidationResult, layer: str):
        """验证S3配置"""
        required_fields = ['bucket', 'region']
        for field in required_fields:
            if field not in config:
                result.add_error(f"[{layer}] S3 config missing '{field}' field")
        
        if 'bucket' in config:
            if not isinstance(config['bucket'], str):
                result.add_error(f"[{layer}] S3 bucket must be string")
            elif not re.match(r'^[a-z0-9.-]+$', config['bucket']):
                result.add_error(f"[{layer}] S3 bucket name format invalid")
        
        if 'region' in config:
            if not isinstance(config['region'], str):
                result.add_error(f"[{layer}] S3 region must be string")
    
    def get_validator_name(self) -> str:
        return "StorageConfigValidator"


class SchedulingConfigValidator(ConfigValidator):
    """调度配置验证器"""
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        result = ValidationResult()
        
        scheduling_tasks = ['historical_update', 'data_migration', 'data_cleanup', 'health_check']
        
        for task in scheduling_tasks:
            if task in config:
                task_config = config[task]
                
                if not isinstance(task_config, dict):
                    result.add_error(f"[{task}] must be a dictionary")
                    continue
                
                # 验证enabled字段
                if 'enabled' in task_config:
                    if not isinstance(task_config['enabled'], bool):
                        result.add_error(f"[{task}] enabled must be boolean")
                
                # 验证cron表达式
                if 'cron' in task_config:
                    if not isinstance(task_config['cron'], str):
                        result.add_error(f"[{task}] cron must be string")
                    elif not self._validate_cron_expression(task_config['cron']):
                        result.add_error(f"[{task}] invalid cron expression: {task_config['cron']}")
                
                # 验证特定任务配置
                if task == 'historical_update':
                    self._validate_historical_update_config(task_config, result)
                elif task == 'data_migration':
                    self._validate_data_migration_config(task_config, result)
                elif task == 'health_check':
                    self._validate_health_check_config(task_config, result)
        
        return result
    
    def _validate_cron_expression(self, cron_expr: str) -> bool:
        """验证cron表达式"""
        # 简单的cron表达式验证 (分 时 日 月 周)
        pattern = r'^(\*|[0-5]?\d|\*/\d+)\s+(\*|[01]?\d|2[0-3]|\*/\d+)\s+(\*|[0-2]?\d|3[01]|\*/\d+)\s+(\*|[0]?\d|1[0-2]|\*/\d+)\s+(\*|[0-6]|\*/\d+)$'
        return bool(re.match(pattern, cron_expr))
    
    def _validate_historical_update_config(self, config: Dict[str, Any], result: ValidationResult):
        """验证历史数据更新配置"""
        if 'lookback_days' in config:
            if not isinstance(config['lookback_days'], int):
                result.add_error("[historical_update] lookback_days must be integer")
            elif not (1 <= config['lookback_days'] <= 365):
                result.add_error("[historical_update] lookback_days must be between 1 and 365")
        
        if 'max_parallel_tasks' in config:
            if not isinstance(config['max_parallel_tasks'], int):
                result.add_error("[historical_update] max_parallel_tasks must be integer")
            elif not (1 <= config['max_parallel_tasks'] <= 20):
                result.add_error("[historical_update] max_parallel_tasks must be between 1 and 20")
        
        if 'timeout_minutes' in config:
            if not isinstance(config['timeout_minutes'], int):
                result.add_error("[historical_update] timeout_minutes must be integer")
            elif not (1 <= config['timeout_minutes'] <= 1440):
                result.add_error("[historical_update] timeout_minutes must be between 1 and 1440")
    
    def _validate_data_migration_config(self, config: Dict[str, Any], result: ValidationResult):
        """验证数据迁移配置"""
        if 'batch_size' in config:
            if not isinstance(config['batch_size'], int):
                result.add_error("[data_migration] batch_size must be integer")
            elif not (100 <= config['batch_size'] <= 100000):
                result.add_error("[data_migration] batch_size must be between 100 and 100000")
        
        if 'max_parallel_workers' in config:
            if not isinstance(config['max_parallel_workers'], int):
                result.add_error("[data_migration] max_parallel_workers must be integer")
            elif not (1 <= config['max_parallel_workers'] <= 10):
                result.add_error("[data_migration] max_parallel_workers must be between 1 and 10")
    
    def _validate_health_check_config(self, config: Dict[str, Any], result: ValidationResult):
        """验证健康检查配置"""
        if 'timeout_seconds' in config:
            if not isinstance(config['timeout_seconds'], int):
                result.add_error("[health_check] timeout_seconds must be integer")
            elif not (1 <= config['timeout_seconds'] <= 300):
                result.add_error("[health_check] timeout_seconds must be between 1 and 300")
        
        if 'alert_on_failure' in config:
            if not isinstance(config['alert_on_failure'], bool):
                result.add_error("[health_check] alert_on_failure must be boolean")
    
    def get_validator_name(self) -> str:
        return "SchedulingConfigValidator"


class MonitoringConfigValidator(ConfigValidator):
    """监控配置验证器"""
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        result = ValidationResult()
        
        # 验证enable_metrics
        if 'enable_metrics' in config:
            if not isinstance(config['enable_metrics'], bool):
                result.add_error("enable_metrics must be boolean")
        
        # 验证prometheus配置
        if 'prometheus' in config:
            prometheus_config = config['prometheus']
            
            if 'bind_address' in prometheus_config:
                if not isinstance(prometheus_config['bind_address'], str):
                    result.add_error("[prometheus] bind_address must be string")
                elif not re.match(r'^[\w\.-]+:\d+$', prometheus_config['bind_address']):
                    result.add_error("[prometheus] bind_address format invalid")
            
            if 'metrics_path' in prometheus_config:
                if not isinstance(prometheus_config['metrics_path'], str):
                    result.add_error("[prometheus] metrics_path must be string")
                elif not prometheus_config['metrics_path'].startswith('/'):
                    result.add_error("[prometheus] metrics_path must start with '/'")
        
        # 验证alert_thresholds
        if 'alert_thresholds' in config:
            thresholds = config['alert_thresholds']
            
            threshold_fields = {
                'data_delay_seconds': (1, 3600),
                'error_rate_percent': (0.0, 100.0),
                'cpu_threshold_percent': (0.0, 100.0),
                'memory_threshold_percent': (0.0, 100.0),
                'disk_threshold_percent': (0.0, 100.0),
                'connection_failure_rate': (0.0, 100.0)
            }
            
            for field, (min_val, max_val) in threshold_fields.items():
                if field in thresholds:
                    value = thresholds[field]
                    if not isinstance(value, (int, float)):
                        result.add_error(f"[alert_thresholds] {field} must be number")
                    elif not (min_val <= value <= max_val):
                        result.add_error(f"[alert_thresholds] {field} must be between {min_val} and {max_val}")
        
        # 验证alerting配置
        if 'alerting' in config:
            alerting_config = config['alerting']
            
            if 'enabled' in alerting_config:
                if not isinstance(alerting_config['enabled'], bool):
                    result.add_error("[alerting] enabled must be boolean")
            
            if 'channels' in alerting_config:
                self._validate_alert_channels(alerting_config['channels'], result)
            
            if 'rate_limiting' in alerting_config:
                self._validate_rate_limiting(alerting_config['rate_limiting'], result)
        
        return result
    
    def _validate_alert_channels(self, channels: Dict[str, Any], result: ValidationResult):
        """验证告警通道配置"""
        valid_channels = {'console', 'email', 'webhook', 'slack'}
        
        for channel_name, channel_config in channels.items():
            if channel_name not in valid_channels:
                result.add_warning(f"[alerting.channels] Unknown channel: {channel_name}")
                continue
            
            if not isinstance(channel_config, dict):
                result.add_error(f"[alerting.channels.{channel_name}] must be dictionary")
                continue
            
            if 'enabled' in channel_config:
                if not isinstance(channel_config['enabled'], bool):
                    result.add_error(f"[alerting.channels.{channel_name}] enabled must be boolean")
            
            # 验证特定通道配置
            if channel_name == 'email':
                self._validate_email_channel(channel_config, result)
            elif channel_name == 'webhook':
                self._validate_webhook_channel(channel_config, result)
    
    def _validate_email_channel(self, config: Dict[str, Any], result: ValidationResult):
        """验证邮件通道配置"""
        if config.get('enabled', False):
            required_fields = ['smtp_server', 'port', 'username', 'password', 'recipients']
            for field in required_fields:
                if field not in config:
                    result.add_error(f"[alerting.channels.email] missing required field: {field}")
            
            if 'port' in config:
                if not isinstance(config['port'], int):
                    result.add_error("[alerting.channels.email] port must be integer")
                elif not (1 <= config['port'] <= 65535):
                    result.add_error("[alerting.channels.email] port must be between 1 and 65535")
            
            if 'recipients' in config:
                if not isinstance(config['recipients'], list):
                    result.add_error("[alerting.channels.email] recipients must be list")
                else:
                    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                    for i, email in enumerate(config['recipients']):
                        if not isinstance(email, str):
                            result.add_error(f"[alerting.channels.email] recipient {i} must be string")
                        elif not re.match(email_pattern, email):
                            result.add_error(f"[alerting.channels.email] invalid email format: {email}")
    
    def _validate_webhook_channel(self, config: Dict[str, Any], result: ValidationResult):
        """验证Webhook通道配置"""
        if config.get('enabled', False):
            if 'url' not in config:
                result.add_error("[alerting.channels.webhook] missing required field: url")
            elif not isinstance(config['url'], str):
                result.add_error("[alerting.channels.webhook] url must be string")
            elif not config['url'].startswith(('http://', 'https://')):
                result.add_error("[alerting.channels.webhook] url must start with http:// or https://")
            
            if 'timeout_seconds' in config:
                if not isinstance(config['timeout_seconds'], int):
                    result.add_error("[alerting.channels.webhook] timeout_seconds must be integer")
                elif not (1 <= config['timeout_seconds'] <= 300):
                    result.add_error("[alerting.channels.webhook] timeout_seconds must be between 1 and 300")
    
    def _validate_rate_limiting(self, config: Dict[str, Any], result: ValidationResult):
        """验证速率限制配置"""
        if 'max_alerts_per_minute' in config:
            if not isinstance(config['max_alerts_per_minute'], int):
                result.add_error("[alerting.rate_limiting] max_alerts_per_minute must be integer")
            elif not (1 <= config['max_alerts_per_minute'] <= 1000):
                result.add_error("[alerting.rate_limiting] max_alerts_per_minute must be between 1 and 1000")
        
        if 'cooldown_minutes' in config:
            if not isinstance(config['cooldown_minutes'], int):
                result.add_error("[alerting.rate_limiting] cooldown_minutes must be integer")
            elif not (1 <= config['cooldown_minutes'] <= 1440):
                result.add_error("[alerting.rate_limiting] cooldown_minutes must be between 1 and 1440")
    
    def get_validator_name(self) -> str:
        return "MonitoringConfigValidator"