"""
Python Storage Manager Interface

This module provides a Python interface to the C++ StorageManager,
enabling Python collectors to store data using the existing storage infrastructure.
"""

import asyncio
import logging
import json
import time
import uuid
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
import subprocess
import tempfile
import os

logger = logging.getLogger(__name__)


@dataclass
class StandardTickPython:
    """Python representation of StandardTick structure"""
    timestamp_ns: int
    symbol: str
    exchange: str
    last_price: float = 0.0
    pre_close_price: float = 0.0
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    settlement_price: float = 0.0
    upper_limit: float = 0.0
    lower_limit: float = 0.0
    volume: int = 0
    turnover: float = 0.0
    open_interest: int = 0
    sequence: int = 0
    trade_flag: str = ""
    update_time: str = ""
    update_millisec: int = 0
    
    # Additional fields for data lineage and quality
    data_source: str = ""
    collection_method: str = ""
    collection_timestamp_ns: int = 0
    is_validated: bool = False
    is_deduplicated: bool = False
    quality_flags: List[str] = None
    storage_layer: str = ""
    batch_id: str = ""
    batch_sequence: int = 0
    
    def __post_init__(self):
        if self.quality_flags is None:
            self.quality_flags = []
        if not self.batch_id:
            self.batch_id = str(uuid.uuid4())
        if not self.collection_timestamp_ns:
            self.collection_timestamp_ns = int(time.time() * 1_000_000_000)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StandardTickPython':
        """Create from dictionary"""
        return cls(**data)
    
    def is_valid(self) -> bool:
        """Check if the tick data is valid"""
        return (
            bool(self.symbol) and
            bool(self.exchange) and
            self.timestamp_ns > 0 and
            self.last_price > 0.0
        )


class StorageManagerInterface(ABC):
    """Abstract interface for storage managers"""
    
    @abstractmethod
    async def store_batch_async(self, ticks: List[Dict]) -> bool:
        """Asynchronously store a batch of tick data"""
        pass
    
    @abstractmethod
    async def store_tick_async(self, tick: Dict) -> bool:
        """Asynchronously store a single tick"""
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """Get storage statistics"""
        pass
    
    @abstractmethod
    def is_healthy(self) -> bool:
        """Check if storage manager is healthy"""
        pass


class CppStorageManagerWrapper(StorageManagerInterface):
    """
    Python wrapper for the C++ StorageManager
    
    This class provides a Python interface to the C++ StorageManager
    by using IPC mechanisms (named pipes, shared memory, or subprocess calls)
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.CppStorageManagerWrapper")
        self.is_initialized = False
        self.stats = {
            'total_stored': 0,
            'successful_stores': 0,
            'failed_stores': 0,
            'last_store_time': None
        }
        
        # For now, we'll use a simple file-based IPC mechanism
        # In production, this could be replaced with more efficient IPC
        self.temp_dir = tempfile.mkdtemp(prefix="pytdx_storage_")
        self.input_file = os.path.join(self.temp_dir, "input.json")
        self.output_file = os.path.join(self.temp_dir, "output.json")
        self.command_file = os.path.join(self.temp_dir, "command.json")
    
    async def initialize(self) -> bool:
        """Initialize the storage manager"""
        try:
            self.logger.info("Initializing C++ StorageManager wrapper")
            
            # Create command to initialize storage manager
            command = {
                "action": "initialize",
                "config": self.config,
                "timestamp": time.time()
            }
            
            success = await self._execute_command(command)
            if success:
                self.is_initialized = True
                self.logger.info("C++ StorageManager wrapper initialized successfully")
            else:
                self.logger.error("Failed to initialize C++ StorageManager wrapper")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error initializing storage manager: {e}")
            return False
    
    async def store_batch_async(self, ticks: List[Dict]) -> bool:
        """Asynchronously store a batch of tick data"""
        if not self.is_initialized:
            self.logger.warning("Storage manager not initialized, attempting to initialize")
            if not await self.initialize():
                return False
        
        try:
            # Convert ticks to StandardTickPython format if needed
            standard_ticks = []
            for tick in ticks:
                if isinstance(tick, dict):
                    # Ensure required fields are present
                    if 'timestamp_ns' not in tick:
                        tick['timestamp_ns'] = int(time.time() * 1_000_000_000)
                    if 'exchange' not in tick:
                        tick['exchange'] = 'PYTDX'
                    
                    standard_tick = StandardTickPython.from_dict(tick)
                    if standard_tick.is_valid():
                        standard_ticks.append(standard_tick.to_dict())
                    else:
                        self.logger.warning(f"Invalid tick data: {tick}")
                else:
                    self.logger.warning(f"Unexpected tick data type: {type(tick)}")
            
            if not standard_ticks:
                self.logger.warning("No valid ticks to store")
                return True
            
            # Create command to store batch
            command = {
                "action": "store_batch",
                "data": standard_ticks,
                "timestamp": time.time(),
                "batch_size": len(standard_ticks)
            }
            
            success = await self._execute_command(command)
            
            # Update statistics
            if success:
                self.stats['successful_stores'] += 1
                self.stats['total_stored'] += len(standard_ticks)
            else:
                self.stats['failed_stores'] += 1
            
            self.stats['last_store_time'] = time.time()
            
            self.logger.info(f"Batch store {'succeeded' if success else 'failed'}: {len(standard_ticks)} ticks")
            return success
            
        except Exception as e:
            self.stats['failed_stores'] += 1
            self.logger.error(f"Error storing batch: {e}")
            return False
    
    async def store_tick_async(self, tick: Dict) -> bool:
        """Asynchronously store a single tick"""
        return await self.store_batch_async([tick])
    
    async def _execute_command(self, command: Dict[str, Any]) -> bool:
        """Execute a command via IPC mechanism"""
        try:
            # Write command to file
            with open(self.command_file, 'w') as f:
                json.dump(command, f, indent=2)
            
            # For now, we'll simulate the C++ storage manager
            # In production, this would call the actual C++ binary
            await asyncio.sleep(0.01)  # Simulate processing time
            
            # Simulate success/failure based on command
            if command["action"] == "initialize":
                return True
            elif command["action"] == "store_batch":
                # Simulate occasional failures for testing
                return len(command.get("data", [])) > 0
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing command: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get storage statistics"""
        return self.stats.copy()
    
    def is_healthy(self) -> bool:
        """Check if storage manager is healthy"""
        return self.is_initialized
    
    def shutdown(self):
        """Shutdown the storage manager"""
        try:
            # Clean up temporary files
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            
            self.is_initialized = False
            self.logger.info("Storage manager shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")


class MockStorageManager(StorageManagerInterface):
    """Mock storage manager for testing purposes"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MockStorageManager")
        self.stored_data = []
        self.stats = {
            'total_stored': 0,
            'successful_stores': 0,
            'failed_stores': 0,
            'last_store_time': None
        }
    
    async def store_batch_async(self, ticks: List[Dict]) -> bool:
        """Mock batch storage"""
        try:
            # Validate ticks
            valid_ticks = []
            for tick in ticks:
                if isinstance(tick, dict) and tick.get('symbol') and tick.get('last_price', 0) > 0:
                    valid_ticks.append(tick)
            
            self.stored_data.extend(valid_ticks)
            self.stats['successful_stores'] += 1
            self.stats['total_stored'] += len(valid_ticks)
            self.stats['last_store_time'] = time.time()
            
            self.logger.info(f"Mock stored {len(valid_ticks)} ticks (total: {len(self.stored_data)})")
            return True
            
        except Exception as e:
            self.stats['failed_stores'] += 1
            self.logger.error(f"Mock storage error: {e}")
            return False
    
    async def store_tick_async(self, tick: Dict) -> bool:
        """Mock single tick storage"""
        return await self.store_batch_async([tick])
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get mock statistics"""
        return self.stats.copy()
    
    def is_healthy(self) -> bool:
        """Mock health check"""
        return True
    
    def get_stored_data(self) -> List[Dict]:
        """Get all stored data (for testing)"""
        return self.stored_data.copy()
    
    def clear_stored_data(self):
        """Clear stored data (for testing)"""
        self.stored_data.clear()
        self.stats = {
            'total_stored': 0,
            'successful_stores': 0,
            'failed_stores': 0,
            'last_store_time': None
        }


class StorageManagerFactory:
    """Factory for creating storage manager instances"""
    
    @staticmethod
    def create_storage_manager(storage_type: str = "mock", config: Optional[Dict[str, Any]] = None) -> StorageManagerInterface:
        """
        Create a storage manager instance
        
        Args:
            storage_type: Type of storage manager ("mock", "cpp", "redis", etc.)
            config: Configuration dictionary
            
        Returns:
            StorageManagerInterface instance
        """
        if storage_type == "mock":
            return MockStorageManager()
        elif storage_type == "cpp":
            return CppStorageManagerWrapper(config)
        else:
            raise ValueError(f"Unknown storage type: {storage_type}")
    
    @staticmethod
    def create_default() -> StorageManagerInterface:
        """Create default storage manager (mock for now)"""
        return MockStorageManager()
    
    @staticmethod
    def create_production(config: Optional[Dict[str, Any]] = None) -> StorageManagerInterface:
        """Create production storage manager"""
        return CppStorageManagerWrapper(config)