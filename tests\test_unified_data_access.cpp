#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <chrono>
#include <thread>

#include "../src/storage/unified_data_access.h"
#include "../src/storage/redis_storage.h"
#include "../src/storage/clickhouse_storage.h"
#include "../src/storage/cold_storage.hpp"

using namespace financial_data;
using namespace testing;

// Mock classes for testing
class MockRedisHotStorage : public RedisHotStorage {
public:
    MockRedisHotStorage() : RedisHotStorage(RedisConfig{}) {}
    
    MOCK_METHOD(bool, GetLatestTick, (const std::string& symbol, StandardTick& tick), (override));
    MOCK_METHOD(bool, GetLatestLevel2, (const std::string& symbol, Level2Data& level2), (override));
    MOCK_METHOD(QueryResult, QueryTicks, (const std::string& symbol, const QueryOptions& options), (override));
    MOCK_METHOD(QueryResult, QueryLevel2, (const std::string& symbol, const QueryOptions& options), (override));
    MOCK_METHOD(std::unordered_map<std::string, StandardTick>, GetLatestTicks, (const std::vector<std::string>& symbols), (override));
    MOCK_METHOD(StorageStats, GetStats, (), (const, override));
};

class MockClickHouseStorage : public ClickHouseStorage {
public:
    MockClickHouseStorage() : ClickHouseStorage(ClickHouseConfig{}) {}
    
    MOCK_METHOD(bool, IsConnected, (), (const, override));
    MOCK_METHOD(QueryResult<StandardizedTick>, QueryTickData, 
                (const std::string& symbol, const std::string& exchange,
                 int64_t start_timestamp, int64_t end_timestamp,
                 size_t limit, const std::string& cursor), (override));
    MOCK_METHOD(PerformanceMetrics, GetMetrics, (), (const, override));
};

class MockColdDataStorage : public storage::ColdDataStorage {
public:
    MockColdDataStorage() : storage::ColdDataStorage(storage::ColdStorageConfig{}) {}
    
    MOCK_METHOD(std::future<storage::TickDataBatch>, RetrieveData,
                (const std::string& symbol, const std::string& exchange,
                 const std::chrono::system_clock::time_point& start_time,
                 const std::chrono::system_clock::time_point& end_time), (override));
};

class UnifiedDataAccessTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建mock存储层
        mock_hot_storage_ = std::make_shared<MockRedisHotStorage>();
        mock_warm_storage_ = std::make_shared<MockClickHouseStorage>();
        mock_cold_storage_ = std::make_shared<MockColdDataStorage>();
        
        // 创建统一数据访问接口
        config_.hot_storage_days = 7;
        config_.warm_storage_days = 730;
        config_.enable_query_cache = true;
        config_.cache_size = 1000;
        config_.max_concurrent_queries = 10;
        
        unified_access_ = std::make_unique<UnifiedDataAccessInterface>(config_);
        
        // 初始化
        ASSERT_TRUE(unified_access_->Initialize(mock_hot_storage_, mock_warm_storage_, mock_cold_storage_));
    }
    
    void TearDown() override {
        unified_access_->Shutdown();
    }
    
    // 创建测试数据
    StandardTick CreateTestTick(const std::string& symbol, int64_t timestamp_ns, double price) {
        StandardTick tick;
        tick.symbol = symbol;
        tick.exchange = "TEST";
        tick.timestamp_ns = timestamp_ns;
        tick.last_price = price;
        tick.volume = 1000;
        tick.turnover = price * 1000;
        tick.bids[0] = PriceLevel(price - 0.01, 500);
        tick.asks[0] = PriceLevel(price + 0.01, 500);
        return tick;
    }
    
    Level2Data CreateTestLevel2(const std::string& symbol, int64_t timestamp_ns) {
        Level2Data level2;
        level2.symbol = symbol;
        level2.exchange = "TEST";
        level2.timestamp_ns = timestamp_ns;
        level2.bids.push_back(PriceLevel(100.0, 1000));
        level2.asks.push_back(PriceLevel(100.1, 1000));
        return level2;
    }
    
    int64_t GetCurrentTimestampNs() {
        auto now = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::nanoseconds>(
            now.time_since_epoch()).count();
    }
    
    int64_t DaysToNanoseconds(int days) {
        return static_cast<int64_t>(days) * 24 * 3600 * 1000000000LL;
    }

protected:
    std::shared_ptr<MockRedisHotStorage> mock_hot_storage_;
    std::shared_ptr<MockClickHouseStorage> mock_warm_storage_;
    std::shared_ptr<MockColdDataStorage> mock_cold_storage_;
    std::unique_ptr<UnifiedDataAccessInterface> unified_access_;
    StorageLayerConfig config_;
};

// 测试初始化
TEST_F(UnifiedDataAccessTest, InitializationTest) {
    EXPECT_TRUE(unified_access_->IsInitialized());
    
    // 测试重复初始化
    EXPECT_TRUE(unified_access_->Initialize(mock_hot_storage_, mock_warm_storage_, mock_cold_storage_));
}

// 测试获取最新tick数据
TEST_F(UnifiedDataAccessTest, GetLatestTickTest) {
    std::string symbol = "TEST001";
    StandardTick expected_tick = CreateTestTick(symbol, GetCurrentTimestampNs(), 100.0);
    
    // 设置mock期望
    EXPECT_CALL(*mock_hot_storage_, GetLatestTick(symbol, _))
        .WillOnce(DoAll(SetArgReferee<1>(expected_tick), Return(true)));
    
    // 执行测试
    auto future = unified_access_->GetLatestTick(symbol);
    auto result = future.get();
    
    EXPECT_EQ(result.symbol, symbol);
    EXPECT_EQ(result.last_price, 100.0);
    EXPECT_EQ(result.exchange, "TEST");
}

// 测试获取最新Level2数据
TEST_F(UnifiedDataAccessTest, GetLatestLevel2Test) {
    std::string symbol = "TEST001";
    Level2Data expected_level2 = CreateTestLevel2(symbol, GetCurrentTimestampNs());
    
    // 设置mock期望
    EXPECT_CALL(*mock_hot_storage_, GetLatestLevel2(symbol, _))
        .WillOnce(DoAll(SetArgReferee<1>(expected_level2), Return(true)));
    
    // 执行测试
    auto future = unified_access_->GetLatestLevel2(symbol);
    auto result = future.get();
    
    EXPECT_EQ(result.symbol, symbol);
    EXPECT_EQ(result.exchange, "TEST");
    EXPECT_FALSE(result.bids.empty());
}

// 测试批量获取最新tick数据
TEST_F(UnifiedDataAccessTest, GetLatestTicksBatchTest) {
    std::vector<std::string> symbols = {"TEST001", "TEST002", "TEST003"};
    std::unordered_map<std::string, StandardTick> expected_ticks;
    
    for (const auto& symbol : symbols) {
        expected_ticks[symbol] = CreateTestTick(symbol, GetCurrentTimestampNs(), 100.0 + symbols.size());
    }
    
    // 设置mock期望
    EXPECT_CALL(*mock_hot_storage_, GetLatestTicks(symbols))
        .WillOnce(Return(expected_ticks));
    
    // 执行测试
    auto future = unified_access_->GetLatestTicks(symbols);
    auto result = future.get();
    
    EXPECT_EQ(result.size(), symbols.size());
    for (const auto& symbol : symbols) {
        EXPECT_TRUE(result.find(symbol) != result.end());
    }
}

// 测试热存储查询
TEST_F(UnifiedDataAccessTest, HotStorageQueryTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(1); // 1天前
    int64_t end_time = current_time;
    
    QueryRequest request(symbol, start_time, end_time, 100);
    
    // 准备mock返回数据
    QueryResult mock_result;
    mock_result.ticks.push_back(CreateTestTick(symbol, start_time + 1000000, 100.0));
    mock_result.ticks.push_back(CreateTestTick(symbol, start_time + 2000000, 100.1));
    mock_result.has_more = false;
    
    // 设置mock期望
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillOnce(Return(mock_result));
    
    // 执行测试
    auto future = unified_access_->QueryData(request);
    auto response = future.get();
    
    EXPECT_EQ(response.storage_source, "hot");
    EXPECT_EQ(response.ticks.size(), 2);
    EXPECT_FALSE(response.has_more);
}

// 测试温存储查询
TEST_F(UnifiedDataAccessTest, WarmStorageQueryTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(30); // 30天前
    int64_t end_time = current_time - DaysToNanoseconds(10);   // 10天前
    
    QueryRequest request(symbol, start_time, end_time, 100);
    request.exchange = "TEST";
    
    // 准备mock返回数据
    QueryResult<StandardizedTick> mock_result;
    StandardizedTick standardized_tick;
    standardized_tick.symbol = symbol;
    standardized_tick.exchange = "TEST";
    standardized_tick.timestamp_ns = start_time + 1000000;
    standardized_tick.last_price = 100.0;
    standardized_tick.volume = 1000;
    standardized_tick.bid_prices = {99.9};
    standardized_tick.bid_volumes = {500};
    standardized_tick.ask_prices = {100.1};
    standardized_tick.ask_volumes = {500};
    
    mock_result.data.push_back(standardized_tick);
    mock_result.has_more = false;
    mock_result.total_rows = 1;
    
    // 设置mock期望
    EXPECT_CALL(*mock_warm_storage_, QueryTickData(symbol, "TEST", start_time, end_time, 100, ""))
        .WillOnce(Return(mock_result));
    
    // 执行测试
    auto future = unified_access_->QueryData(request);
    auto response = future.get();
    
    EXPECT_EQ(response.storage_source, "warm");
    EXPECT_EQ(response.ticks.size(), 1);
    EXPECT_EQ(response.ticks[0].symbol, symbol);
    EXPECT_EQ(response.ticks[0].last_price, 100.0);
}

// 测试冷存储查询
TEST_F(UnifiedDataAccessTest, ColdStorageQueryTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(800); // 800天前
    int64_t end_time = current_time - DaysToNanoseconds(750);   // 750天前
    
    QueryRequest request(symbol, start_time, end_time, 100);
    request.exchange = "TEST";
    
    // 准备mock返回数据
    storage::TickDataBatch mock_batch;
    mock_batch.timestamps.push_back(start_time + 1000000);
    mock_batch.symbols.push_back(symbol);
    mock_batch.exchanges.push_back("TEST");
    mock_batch.last_prices.push_back(100.0);
    mock_batch.volumes.push_back(1000);
    mock_batch.turnovers.push_back(100000.0);
    mock_batch.open_interests.push_back(5000);
    mock_batch.sequences.push_back(1);
    mock_batch.bid_prices.push_back({99.9});
    mock_batch.bid_volumes.push_back({500});
    mock_batch.ask_prices.push_back({100.1});
    mock_batch.ask_volumes.push_back({500});
    
    // 创建future并设置返回值
    std::promise<storage::TickDataBatch> promise;
    promise.set_value(mock_batch);
    auto mock_future = promise.get_future();
    
    // 设置mock期望
    EXPECT_CALL(*mock_cold_storage_, RetrieveData(symbol, "TEST", _, _))
        .WillOnce(Return(ByMove(std::move(mock_future))));
    
    // 执行测试
    auto future = unified_access_->QueryData(request);
    auto response = future.get();
    
    EXPECT_EQ(response.storage_source, "cold");
    EXPECT_EQ(response.ticks.size(), 1);
    EXPECT_EQ(response.ticks[0].symbol, symbol);
    EXPECT_EQ(response.ticks[0].last_price, 100.0);
}

// 测试跨层查询
TEST_F(UnifiedDataAccessTest, MultiLayerQueryTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(30); // 30天前（温存储）
    int64_t end_time = current_time - DaysToNanoseconds(1);    // 1天前（热存储）
    
    QueryRequest request(symbol, start_time, end_time, 100);
    request.exchange = "TEST";
    
    // 准备热存储mock数据
    QueryResult hot_result;
    hot_result.ticks.push_back(CreateTestTick(symbol, current_time - DaysToNanoseconds(1), 101.0));
    
    // 准备温存储mock数据
    QueryResult<StandardizedTick> warm_result;
    StandardizedTick standardized_tick;
    standardized_tick.symbol = symbol;
    standardized_tick.exchange = "TEST";
    standardized_tick.timestamp_ns = current_time - DaysToNanoseconds(15);
    standardized_tick.last_price = 100.0;
    standardized_tick.volume = 1000;
    warm_result.data.push_back(standardized_tick);
    
    // 设置mock期望
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillOnce(Return(hot_result));
    EXPECT_CALL(*mock_warm_storage_, QueryTickData(symbol, "TEST", _, _, _, _))
        .WillOnce(Return(warm_result));
    
    // 执行测试
    auto future = unified_access_->QueryData(request);
    auto response = future.get();
    
    EXPECT_EQ(response.storage_source, "mixed");
    EXPECT_EQ(response.ticks.size(), 2);
    
    // 验证数据按时间戳排序
    EXPECT_LT(response.ticks[0].timestamp_ns, response.ticks[1].timestamp_ns);
}

// 测试查询缓存
TEST_F(UnifiedDataAccessTest, QueryCacheTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    int64_t start_time = current_time - DaysToNanoseconds(1);
    int64_t end_time = current_time;
    
    QueryRequest request(symbol, start_time, end_time, 100);
    
    // 准备mock数据
    QueryResult mock_result;
    mock_result.ticks.push_back(CreateTestTick(symbol, start_time + 1000000, 100.0));
    
    // 第一次查询应该调用存储层
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillOnce(Return(mock_result));
    
    // 执行第一次查询
    auto future1 = unified_access_->QueryData(request);
    auto response1 = future1.get();
    
    EXPECT_EQ(response1.ticks.size(), 1);
    
    // 第二次查询应该从缓存返回，不调用存储层
    auto future2 = unified_access_->QueryData(request);
    auto response2 = future2.get();
    
    EXPECT_EQ(response2.ticks.size(), 1);
    
    // 验证缓存命中率
    EXPECT_GT(unified_access_->GetCacheHitRate(), 0.0);
}

// 测试健康检查
TEST_F(UnifiedDataAccessTest, HealthCheckTest) {
    // 设置mock期望
    RedisHotStorage::StorageStats redis_stats;
    redis_stats.active_connections = 5;
    redis_stats.total_queries = 100;
    
    ClickHouseStorage::PerformanceMetrics ch_metrics;
    ch_metrics.total_queries = 50;
    ch_metrics.failed_queries = 2;
    
    EXPECT_CALL(*mock_hot_storage_, GetStats())
        .WillRepeatedly(Return(redis_stats));
    EXPECT_CALL(*mock_warm_storage_, IsConnected())
        .WillRepeatedly(Return(true));
    EXPECT_CALL(*mock_warm_storage_, GetMetrics())
        .WillRepeatedly(Return(ch_metrics));
    
    // 触发健康检查
    unified_access_->TriggerHealthCheck();
    
    // 等待健康检查完成
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto health_status = unified_access_->GetHealthStatus();
    EXPECT_TRUE(health_status.overall_healthy);
    EXPECT_TRUE(health_status.hot_storage_healthy);
    EXPECT_TRUE(health_status.warm_storage_healthy);
    EXPECT_TRUE(health_status.cold_storage_healthy);
}

// 测试性能指标
TEST_F(UnifiedDataAccessTest, PerformanceMetricsTest) {
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    QueryRequest request(symbol, current_time - 1000000, current_time, 10);
    
    // 准备mock数据
    QueryResult mock_result;
    mock_result.ticks.push_back(CreateTestTick(symbol, current_time - 500000, 100.0));
    
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillOnce(Return(mock_result));
    
    // 执行查询
    auto future = unified_access_->QueryData(request);
    auto response = future.get();
    
    // 检查性能指标
    auto metrics = unified_access_->GetMetrics();
    EXPECT_GT(metrics.total_queries.load(), 0);
    EXPECT_GT(metrics.hot_storage_queries.load(), 0);
    EXPECT_GE(metrics.avg_query_time_ms.load(), 0.0);
}

// 测试并发查询限制
TEST_F(UnifiedDataAccessTest, ConcurrentQueryLimitTest) {
    // 设置较小的并发限制用于测试
    StorageLayerConfig test_config;
    test_config.max_concurrent_queries = 2;
    
    auto limited_access = std::make_unique<UnifiedDataAccessInterface>(test_config);
    ASSERT_TRUE(limited_access->Initialize(mock_hot_storage_, mock_warm_storage_, mock_cold_storage_));
    
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    QueryRequest request(symbol, current_time - 1000000, current_time, 10);
    
    // 准备mock数据（延迟返回以测试并发限制）
    QueryResult mock_result;
    mock_result.ticks.push_back(CreateTestTick(symbol, current_time - 500000, 100.0));
    
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(symbol, _))
        .WillRepeatedly(Return(mock_result));
    
    // 启动多个并发查询
    std::vector<std::future<QueryResponse>> futures;
    for (int i = 0; i < 5; ++i) {
        futures.push_back(limited_access->QueryData(request));
    }
    
    // 等待所有查询完成
    int successful_queries = 0;
    for (auto& future : futures) {
        auto response = future.get();
        if (response.storage_source != "error") {
            successful_queries++;
        }
    }
    
    // 由于并发限制，应该有一些查询被拒绝
    EXPECT_LT(successful_queries, 5);
    
    limited_access->Shutdown();
}

// 测试配置更新
TEST_F(UnifiedDataAccessTest, ConfigUpdateTest) {
    StorageLayerConfig new_config;
    new_config.hot_storage_days = 14;  // 从7天改为14天
    new_config.warm_storage_days = 1460; // 从730天改为1460天
    new_config.cache_size = 5000;
    
    EXPECT_TRUE(unified_access_->UpdateConfig(new_config));
    
    auto current_config = unified_access_->GetConfig();
    EXPECT_EQ(current_config.hot_storage_days, 14);
    EXPECT_EQ(current_config.warm_storage_days, 1460);
    EXPECT_EQ(current_config.cache_size, 5000);
}

// 测试缓存管理
TEST_F(UnifiedDataAccessTest, CacheManagementTest) {
    // 清空缓存
    unified_access_->ClearCache();
    EXPECT_EQ(unified_access_->GetCacheSize(), 0);
    
    // 执行一些查询以填充缓存
    std::string symbol = "TEST001";
    int64_t current_time = GetCurrentTimestampNs();
    
    QueryResult mock_result;
    mock_result.ticks.push_back(CreateTestTick(symbol, current_time - 500000, 100.0));
    
    EXPECT_CALL(*mock_hot_storage_, QueryTicks(_, _))
        .WillRepeatedly(Return(mock_result));
    
    // 执行多个不同的查询
    for (int i = 0; i < 5; ++i) {
        QueryRequest request(symbol + std::to_string(i), current_time - 1000000, current_time, 10);
        auto future = unified_access_->QueryData(request);
        future.get();
    }
    
    EXPECT_GT(unified_access_->GetCacheSize(), 0);
    
    // 清空缓存
    unified_access_->ClearCache();
    EXPECT_EQ(unified_access_->GetCacheSize(), 0);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}