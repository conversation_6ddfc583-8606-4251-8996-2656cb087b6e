@echo off
REM MinIO集群部署脚本
REM 用于在Windows环境下部署MinIO冷数据存储集群

echo Starting MinIO cluster deployment...

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker is not installed or not in PATH
    exit /b 1
)

REM 检查Docker Compose是否安装
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker Compose is not installed or not in PATH
    exit /b 1
)

REM 设置环境变量
set MINIO_ROOT_USER=admin
set MINIO_ROOT_PASSWORD=admin123456

REM 创建必要的目录
if not exist "data\minio" mkdir data\minio
if not exist "logs\minio" mkdir logs\minio

echo Deploying MinIO cluster...

REM 启动MinIO集群
docker-compose -f config\minio-cluster.yml up -d

if %errorlevel% neq 0 (
    echo Error: Failed to start MinIO cluster
    exit /b 1
)

echo Waiting for MinIO cluster to be ready...
timeout /t 30 /nobreak >nul

REM 检查MinIO集群状态
echo Checking MinIO cluster status...
docker-compose -f config\minio-cluster.yml ps

REM 等待MinIO服务完全启动
echo Waiting for MinIO services to be fully ready...
:wait_loop
curl -s http://localhost:9000/minio/health/live >nul 2>&1
if %errorlevel% neq 0 (
    echo MinIO is not ready yet, waiting...
    timeout /t 5 /nobreak >nul
    goto wait_loop
)

echo MinIO cluster is ready!

REM 配置MinIO客户端
echo Configuring MinIO client...
docker run --rm --network host minio/mc:latest alias set local http://localhost:9000 %MINIO_ROOT_USER% %MINIO_ROOT_PASSWORD%

REM 创建必要的存储桶
echo Creating storage buckets...
docker run --rm --network host minio/mc:latest mb local/market-data --ignore-existing
docker run --rm --network host minio/mc:latest mb local/backup --ignore-existing
docker run --rm --network host minio/mc:latest mb local/archive --ignore-existing

REM 设置存储桶策略
echo Setting bucket policies...
docker run --rm --network host minio/mc:latest policy set public local/market-data

REM 启用版本控制
echo Enabling versioning...
docker run --rm --network host minio/mc:latest version enable local/market-data
docker run --rm --network host minio/mc:latest version enable local/backup

REM 配置生命周期策略
echo Configuring lifecycle policies...
echo ^{
echo   "Rules": [
echo     ^{
echo       "ID": "ArchiveOldData",
echo       "Status": "Enabled",
echo       "Filter": ^{
echo         "Prefix": "archive/"
echo       ^},
echo       "Transitions": [
echo         ^{
echo           "Days": 30,
echo           "StorageClass": "STANDARD_IA"
echo         ^},
echo         ^{
echo           "Days": 90,
echo           "StorageClass": "GLACIER"
echo         ^}
echo       ]
echo     ^}
echo   ]
echo ^} > lifecycle.json

docker run --rm --network host -v %cd%:/data minio/mc:latest ilm import local/market-data < /data/lifecycle.json
del lifecycle.json

REM 显示集群信息
echo.
echo ========================================
echo MinIO Cluster Information
echo ========================================
echo Console URL: http://localhost:9001
echo API Endpoint: http://localhost:9000
echo Username: %MINIO_ROOT_USER%
echo Password: %MINIO_ROOT_PASSWORD%
echo.
echo Available Buckets:
docker run --rm --network host minio/mc:latest ls local/
echo.
echo Cluster Status:
docker run --rm --network host minio/mc:latest admin info local
echo ========================================

REM 创建监控脚本
echo Creating monitoring script...
echo @echo off > monitor-minio.bat
echo echo Checking MinIO cluster health... >> monitor-minio.bat
echo docker-compose -f config\minio-cluster.yml ps >> monitor-minio.bat
echo echo. >> monitor-minio.bat
echo echo MinIO cluster statistics: >> monitor-minio.bat
echo docker run --rm --network host minio/mc:latest admin info local >> monitor-minio.bat

echo.
echo MinIO cluster deployment completed successfully!
echo.
echo To monitor the cluster, run: monitor-minio.bat
echo To stop the cluster, run: docker-compose -f config\minio-cluster.yml down
echo To view logs, run: docker-compose -f config\minio-cluster.yml logs -f
echo.

pause