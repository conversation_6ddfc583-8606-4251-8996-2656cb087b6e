"""
Python implementation of storage strategy configuration manager
"""

import json
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from config_manager_python import <PERSON><PERSON>onfig<PERSON>ana<PERSON>, ConfigChangeListener, ConfigChangeEvent
from storage_strategy_validator_python import StorageStrategyValidator


@dataclass
class DataTypeStorageConfig:
    """数据类型存储配置"""
    hot_storage_days: int = 7
    warm_storage_days: int = 730
    priority_storage: str = "hot"
    compression_enabled: bool = False
    batch_size: int = 1000
    max_response_time_ms: float = 1000.0
    
    def is_valid(self) -> bool:
        """验证配置有效性"""
        return (self.hot_storage_days > 0 and 
                self.warm_storage_days > self.hot_storage_days and
                self.batch_size > 0 and
                self.max_response_time_ms > 0.0 and
                self.priority_storage in {"hot", "warm", "cold"})
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataTypeStorageConfig':
        """从字典创建实例"""
        return cls(**data)


@dataclass
class DataTypeMigrationPolicy:
    """数据类型迁移策略配置"""
    hot_to_warm_hours: float = 168.0  # 7天
    warm_to_cold_days: float = 730.0  # 2年
    auto_migration: bool = True
    migration_batch_size: int = 50000
    migration_schedule: str = "0 2 * * *"
    
    def is_valid(self) -> bool:
        """验证配置有效性"""
        return (self.hot_to_warm_hours > 0.0 and
                self.warm_to_cold_days > 0.0 and
                self.hot_to_warm_hours < self.warm_to_cold_days * 24.0 and
                self.migration_batch_size > 0 and
                self._validate_cron_expression(self.migration_schedule))
    
    def _validate_cron_expression(self, cron_expr: str) -> bool:
        """验证cron表达式"""
        # 分割cron表达式
        parts = cron_expr.strip().split()
        if len(parts) != 5:
            return False
        
        # 验证每个部分
        ranges = [(0, 59), (0, 23), (1, 31), (1, 12), (0, 6)]  # 分, 时, 日, 月, 周
        
        for i, (part, (min_val, max_val)) in enumerate(zip(parts, ranges)):
            if not self._validate_cron_field(part, min_val, max_val):
                return False
        
        return True
    
    def _validate_cron_field(self, field: str, min_val: int, max_val: int) -> bool:
        """验证cron字段"""
        if field == "*":
            return True
        
        # 处理步长 (*/n)
        if field.startswith("*/"):
            try:
                step = int(field[2:])
                return step > 0 and step <= max_val
            except ValueError:
                return False
        
        # 处理范围和列表
        for part in field.split(","):
            if "-" in part:
                # 范围 (n-m)
                try:
                    start, end = map(int, part.split("-"))
                    if not (min_val <= start <= max_val and min_val <= end <= max_val and start <= end):
                        return False
                except ValueError:
                    return False
            else:
                # 单个数值
                try:
                    value = int(part)
                    if not (min_val <= value <= max_val):
                        return False
                except ValueError:
                    return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataTypeMigrationPolicy':
        """从字典创建实例"""
        return cls(**data)


@dataclass
class ConfigStatistics:
    """配置统计信息"""
    total_data_types: int
    configured_data_types: int
    migration_policies: int
    has_global_thresholds: bool
    last_updated: float


class StorageStrategyConfig:
    """存储策略配置管理器"""
    
    def __init__(self):
        self.config_manager: Optional[PythonConfigManager] = None
        self.change_listener: Optional['StorageStrategyConfigChangeListener'] = None
        
        # 基本策略配置
        self.selection_strategy = "time_based"
        self.enable_automatic_failover = True
        self.enable_load_balancing = False
        self.health_check_interval_seconds = 30
        self.health_check_timeout_seconds = 5
        self.max_consecutive_failures = 3
        self.failover_cooldown_seconds = 60
        self.max_failover_attempts = 2
        self.load_balance_threshold = 0.8
        
        # 全局阈值配置
        self.hot_storage_days = 7
        self.warm_storage_days = 730
        self.max_response_time_ms = 1000.0
        self.min_success_rate = 0.90
        self.health_threshold_success_rate = 0.95
        self.degraded_threshold_success_rate = 0.80
        
        # 数据类型配置
        self.data_type_configs: Dict[str, DataTypeStorageConfig] = {}
        
        # 迁移策略配置
        self.migration_policies: Dict[str, DataTypeMigrationPolicy] = {}
        
        # 配置变更回调
        self.config_change_callbacks: List[Callable[[], None]] = []
        
        # 配置状态
        self.last_updated = time.time()
        self.validation_errors: List[str] = []
    
    def initialize(self, config_manager: PythonConfigManager) -> bool:
        """初始化配置管理器"""
        self.config_manager = config_manager
        
        # 注册配置变更监听器
        self.change_listener = StorageStrategyConfigChangeListener(self)
        config_manager.register_change_listener(self.change_listener)
        
        return self.load_from_config()
    
    def load_from_config(self) -> bool:
        """从配置管理器加载配置"""
        if not self.config_manager:
            return False
        
        try:
            self._load_basic_config()
            self._load_thresholds()
            self._load_data_type_configs()
            self._load_migration_policies()
            
            self.last_updated = time.time()
            return True
        except Exception:
            return False
    
    def save_to_config(self) -> bool:
        """保存配置到配置管理器"""
        if not self.config_manager:
            return False
        
        try:
            self._save_basic_config()
            self._save_thresholds()
            self._save_data_type_configs()
            self._save_migration_policies()
            
            self.last_updated = time.time()
            return self.config_manager.save_config()
        except Exception:
            return False
    
    def _load_basic_config(self):
        """加载基本配置"""
        self.selection_strategy = self.config_manager.get_value(
            "storage.strategy.selection_strategy", "time_based")
        self.enable_automatic_failover = self.config_manager.get_value(
            "storage.strategy.enable_automatic_failover", True)
        self.enable_load_balancing = self.config_manager.get_value(
            "storage.strategy.enable_load_balancing", False)
        self.health_check_interval_seconds = self.config_manager.get_value(
            "storage.strategy.health_check_interval_seconds", 30)
        self.health_check_timeout_seconds = self.config_manager.get_value(
            "storage.strategy.health_check_timeout_seconds", 5)
        self.max_consecutive_failures = self.config_manager.get_value(
            "storage.strategy.max_consecutive_failures", 3)
        self.failover_cooldown_seconds = self.config_manager.get_value(
            "storage.strategy.failover_cooldown_seconds", 60)
        self.max_failover_attempts = self.config_manager.get_value(
            "storage.strategy.max_failover_attempts", 2)
        self.load_balance_threshold = self.config_manager.get_value(
            "storage.strategy.load_balance_threshold", 0.8)
    
    def _load_thresholds(self):
        """加载阈值配置"""
        self.hot_storage_days = self.config_manager.get_value(
            "storage.strategy.thresholds.hot_storage_days", 7)
        self.warm_storage_days = self.config_manager.get_value(
            "storage.strategy.thresholds.warm_storage_days", 730)
        self.max_response_time_ms = self.config_manager.get_value(
            "storage.strategy.thresholds.max_response_time_ms", 1000.0)
        self.min_success_rate = self.config_manager.get_value(
            "storage.strategy.thresholds.min_success_rate", 0.90)
        self.health_threshold_success_rate = self.config_manager.get_value(
            "storage.strategy.thresholds.health_threshold_success_rate", 0.95)
        self.degraded_threshold_success_rate = self.config_manager.get_value(
            "storage.strategy.thresholds.degraded_threshold_success_rate", 0.80)
    
    def _load_data_type_configs(self):
        """加载数据类型配置"""
        self.data_type_configs.clear()
        
        data_type_configs_dict = self.config_manager.get_section("storage.strategy.data_type_configs")
        
        for data_type, config_dict in data_type_configs_dict.items():
            try:
                config = DataTypeStorageConfig.from_dict(config_dict)
                self.data_type_configs[data_type] = config
            except (TypeError, ValueError):
                # 忽略无效配置
                pass
    
    def _load_migration_policies(self):
        """加载迁移策略配置"""
        self.migration_policies.clear()
        
        migration_policies_dict = self.config_manager.get_section("storage.strategy.migration_policies")
        
        for data_type, policy_dict in migration_policies_dict.items():
            try:
                policy = DataTypeMigrationPolicy.from_dict(policy_dict)
                self.migration_policies[data_type] = policy
            except (TypeError, ValueError):
                # 忽略无效配置
                pass
    
    def _save_basic_config(self):
        """保存基本配置"""
        self.config_manager.set_value("storage.strategy.selection_strategy", self.selection_strategy)
        self.config_manager.set_value("storage.strategy.enable_automatic_failover", self.enable_automatic_failover)
        self.config_manager.set_value("storage.strategy.enable_load_balancing", self.enable_load_balancing)
        self.config_manager.set_value("storage.strategy.health_check_interval_seconds", self.health_check_interval_seconds)
        self.config_manager.set_value("storage.strategy.health_check_timeout_seconds", self.health_check_timeout_seconds)
        self.config_manager.set_value("storage.strategy.max_consecutive_failures", self.max_consecutive_failures)
        self.config_manager.set_value("storage.strategy.failover_cooldown_seconds", self.failover_cooldown_seconds)
        self.config_manager.set_value("storage.strategy.max_failover_attempts", self.max_failover_attempts)
        self.config_manager.set_value("storage.strategy.load_balance_threshold", self.load_balance_threshold)
    
    def _save_thresholds(self):
        """保存阈值配置"""
        self.config_manager.set_value("storage.strategy.thresholds.hot_storage_days", self.hot_storage_days)
        self.config_manager.set_value("storage.strategy.thresholds.warm_storage_days", self.warm_storage_days)
        self.config_manager.set_value("storage.strategy.thresholds.max_response_time_ms", self.max_response_time_ms)
        self.config_manager.set_value("storage.strategy.thresholds.min_success_rate", self.min_success_rate)
        self.config_manager.set_value("storage.strategy.thresholds.health_threshold_success_rate", self.health_threshold_success_rate)
        self.config_manager.set_value("storage.strategy.thresholds.degraded_threshold_success_rate", self.degraded_threshold_success_rate)
    
    def _save_data_type_configs(self):
        """保存数据类型配置"""
        data_type_configs_dict = {}
        
        for data_type, config in self.data_type_configs.items():
            data_type_configs_dict[data_type] = config.to_dict()
        
        self.config_manager.set_section("storage.strategy.data_type_configs", data_type_configs_dict)
    
    def _save_migration_policies(self):
        """保存迁移策略配置"""
        migration_policies_dict = {}
        
        for data_type, policy in self.migration_policies.items():
            migration_policies_dict[data_type] = policy.to_dict()
        
        self.config_manager.set_section("storage.strategy.migration_policies", migration_policies_dict)
    
    # 基本配置访问方法
    def get_selection_strategy(self) -> str:
        return self.selection_strategy
    
    def set_selection_strategy(self, strategy: str) -> bool:
        valid_strategies = {"time_based", "performance_based", "load_balanced", "failover_only"}
        if strategy not in valid_strategies:
            return False
        
        self.selection_strategy = strategy
        self._notify_config_changed()
        return True
    
    def is_automatic_failover_enabled(self) -> bool:
        return self.enable_automatic_failover
    
    def set_automatic_failover_enabled(self, enabled: bool):
        self.enable_automatic_failover = enabled
        self._notify_config_changed()
    
    def is_load_balancing_enabled(self) -> bool:
        return self.enable_load_balancing
    
    def set_load_balancing_enabled(self, enabled: bool):
        self.enable_load_balancing = enabled
        self._notify_config_changed()
    
    def get_hot_storage_days(self) -> int:
        return self.hot_storage_days
    
    def set_hot_storage_days(self, days: int) -> bool:
        if days <= 0 or days >= self.warm_storage_days:
            return False
        
        self.hot_storage_days = days
        self._notify_config_changed()
        return True
    
    def get_warm_storage_days(self) -> int:
        return self.warm_storage_days
    
    def set_warm_storage_days(self, days: int) -> bool:
        if days <= self.hot_storage_days:
            return False
        
        self.warm_storage_days = days
        self._notify_config_changed()
        return True
    
    def get_max_response_time_ms(self) -> float:
        return self.max_response_time_ms
    
    def set_max_response_time_ms(self, ms: float):
        self.max_response_time_ms = ms
        self._notify_config_changed()
    
    def get_min_success_rate(self) -> float:
        return self.min_success_rate
    
    def set_min_success_rate(self, rate: float):
        self.min_success_rate = rate
        self._notify_config_changed()
    
    # 数据类型配置管理
    def has_data_type_config(self, data_type: str) -> bool:
        return data_type in self.data_type_configs
    
    def get_data_type_config(self, data_type: str) -> DataTypeStorageConfig:
        if data_type in self.data_type_configs:
            return self.data_type_configs[data_type]
        
        # 返回基于全局配置的默认配置
        return DataTypeStorageConfig(
            hot_storage_days=self.hot_storage_days,
            warm_storage_days=self.warm_storage_days,
            max_response_time_ms=self.max_response_time_ms
        )
    
    def set_data_type_config(self, data_type: str, config: DataTypeStorageConfig) -> bool:
        if not config.is_valid():
            return False
        
        self.data_type_configs[data_type] = config
        self._notify_config_changed()
        return True
    
    def remove_data_type_config(self, data_type: str) -> bool:
        if data_type in self.data_type_configs:
            del self.data_type_configs[data_type]
            self._notify_config_changed()
            return True
        return False
    
    def get_configured_data_types(self) -> List[str]:
        return list(self.data_type_configs.keys())
    
    # 迁移策略管理
    def has_migration_policy(self, data_type: str) -> bool:
        return data_type in self.migration_policies
    
    def get_migration_policy(self, data_type: str) -> DataTypeMigrationPolicy:
        if data_type in self.migration_policies:
            return self.migration_policies[data_type]
        
        # 返回默认迁移策略
        return DataTypeMigrationPolicy(
            hot_to_warm_hours=self.hot_storage_days * 24.0,
            warm_to_cold_days=self.warm_storage_days
        )
    
    def set_migration_policy(self, data_type: str, policy: DataTypeMigrationPolicy) -> bool:
        if not policy.is_valid():
            return False
        
        self.migration_policies[data_type] = policy
        self._notify_config_changed()
        return True
    
    def remove_migration_policy(self, data_type: str) -> bool:
        if data_type in self.migration_policies:
            del self.migration_policies[data_type]
            self._notify_config_changed()
            return True
        return False
    
    # 配置验证
    def validate_configuration(self) -> bool:
        """验证整个配置"""
        self.validation_errors.clear()
        
        is_valid = (self._validate_basic_config() and 
                   self._validate_thresholds() and 
                   self._validate_data_type_configs() and 
                   self._validate_migration_policies())
        
        return is_valid
    
    def get_validation_errors(self) -> List[str]:
        return self.validation_errors.copy()
    
    def _validate_basic_config(self) -> bool:
        """验证基本配置"""
        valid_strategies = {"time_based", "performance_based", "load_balanced", "failover_only"}
        
        if self.selection_strategy not in valid_strategies:
            self.validation_errors.append(f"Invalid selection strategy: {self.selection_strategy}")
            return False
        
        if self.health_check_interval_seconds <= 0 or self.health_check_interval_seconds > 3600:
            self.validation_errors.append("Invalid health check interval")
            return False
        
        if self.max_consecutive_failures <= 0 or self.max_consecutive_failures > 100:
            self.validation_errors.append("Invalid max consecutive failures")
            return False
        
        return True
    
    def _validate_thresholds(self) -> bool:
        """验证阈值配置"""
        if self.hot_storage_days <= 0 or self.hot_storage_days >= self.warm_storage_days:
            self.validation_errors.append("Invalid storage day thresholds")
            return False
        
        if self.min_success_rate < 0.0 or self.min_success_rate > 1.0:
            self.validation_errors.append("Invalid success rate threshold")
            return False
        
        if self.health_threshold_success_rate <= self.degraded_threshold_success_rate:
            self.validation_errors.append("Health threshold must be greater than degraded threshold")
            return False
        
        return True
    
    def _validate_data_type_configs(self) -> bool:
        """验证数据类型配置"""
        for data_type, config in self.data_type_configs.items():
            if not config.is_valid():
                self.validation_errors.append(f"Invalid configuration for data type: {data_type}")
                return False
        
        return True
    
    def _validate_migration_policies(self) -> bool:
        """验证迁移策略配置"""
        for data_type, policy in self.migration_policies.items():
            if not policy.is_valid():
                self.validation_errors.append(f"Invalid migration policy for data type: {data_type}")
                return False
        
        return True
    
    # 配置导出和导入
    def export_configuration(self) -> Dict[str, Any]:
        """导出配置"""
        config = {
            "selection_strategy": self.selection_strategy,
            "enable_automatic_failover": self.enable_automatic_failover,
            "enable_load_balancing": self.enable_load_balancing,
            "health_check_interval_seconds": self.health_check_interval_seconds,
            "max_consecutive_failures": self.max_consecutive_failures,
            "thresholds": {
                "hot_storage_days": self.hot_storage_days,
                "warm_storage_days": self.warm_storage_days,
                "max_response_time_ms": self.max_response_time_ms,
                "min_success_rate": self.min_success_rate
            },
            "data_type_configs": {
                data_type: config.to_dict() 
                for data_type, config in self.data_type_configs.items()
            },
            "migration_policies": {
                data_type: policy.to_dict() 
                for data_type, policy in self.migration_policies.items()
            }
        }
        
        return config
    
    def import_configuration(self, config: Dict[str, Any]) -> bool:
        """导入配置"""
        try:
            # 导入基本配置
            if "selection_strategy" in config:
                self.selection_strategy = config["selection_strategy"]
            if "enable_automatic_failover" in config:
                self.enable_automatic_failover = config["enable_automatic_failover"]
            if "enable_load_balancing" in config:
                self.enable_load_balancing = config["enable_load_balancing"]
            
            # 导入阈值配置
            if "thresholds" in config:
                thresholds = config["thresholds"]
                if "hot_storage_days" in thresholds:
                    self.hot_storage_days = thresholds["hot_storage_days"]
                if "warm_storage_days" in thresholds:
                    self.warm_storage_days = thresholds["warm_storage_days"]
            
            # 导入数据类型配置
            if "data_type_configs" in config:
                self.data_type_configs.clear()
                for data_type, type_config in config["data_type_configs"].items():
                    storage_config = DataTypeStorageConfig.from_dict(type_config)
                    self.data_type_configs[data_type] = storage_config
            
            # 导入迁移策略
            if "migration_policies" in config:
                self.migration_policies.clear()
                for data_type, policy_config in config["migration_policies"].items():
                    migration_policy = DataTypeMigrationPolicy.from_dict(policy_config)
                    self.migration_policies[data_type] = migration_policy
            
            self._notify_config_changed()
            return True
        except Exception:
            return False
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        # 重置为默认值
        self.selection_strategy = "time_based"
        self.enable_automatic_failover = True
        self.enable_load_balancing = False
        self.health_check_interval_seconds = 30
        self.health_check_timeout_seconds = 5
        self.max_consecutive_failures = 3
        self.failover_cooldown_seconds = 60
        self.max_failover_attempts = 2
        self.load_balance_threshold = 0.8
        
        self.hot_storage_days = 7
        self.warm_storage_days = 730
        self.max_response_time_ms = 1000.0
        self.min_success_rate = 0.90
        self.health_threshold_success_rate = 0.95
        self.degraded_threshold_success_rate = 0.80
        
        self.data_type_configs.clear()
        self.migration_policies.clear()
        
        self._notify_config_changed()
    
    def get_statistics(self) -> ConfigStatistics:
        """获取配置统计信息"""
        return ConfigStatistics(
            total_data_types=6,  # 支持的数据类型数量
            configured_data_types=len(self.data_type_configs),
            migration_policies=len(self.migration_policies),
            has_global_thresholds=True,
            last_updated=self.last_updated
        )
    
    def register_config_change_callback(self, callback: Callable[[], None]):
        """注册配置变更回调"""
        if callback not in self.config_change_callbacks:
            self.config_change_callbacks.append(callback)
    
    def unregister_config_change_callback(self, callback: Callable[[], None]):
        """注销配置变更回调"""
        if callback in self.config_change_callbacks:
            self.config_change_callbacks.remove(callback)
    
    def _notify_config_changed(self):
        """通知配置变更回调"""
        self.last_updated = time.time()
        
        for callback in self.config_change_callbacks:
            try:
                callback()
            except Exception:
                # 忽略回调错误
                pass


class StorageStrategyConfigChangeListener(ConfigChangeListener):
    """存储策略配置变更监听器"""
    
    def __init__(self, parent: StorageStrategyConfig):
        self.parent = parent
    
    def on_config_changed(self, event: ConfigChangeEvent):
        """配置变更回调"""
        if (event.section == "storage" or 
            (event.key and event.key.startswith("storage.strategy"))):
            self.parent.load_from_config()


def register_storage_strategy_validator(config_manager: PythonConfigManager):
    """注册存储策略验证器"""
    validator = StorageStrategyValidator()
    config_manager.register_validator("storage", validator)