#!/usr/bin/env python3
"""
CTP数据采集器主入口
用于Docker容器启动
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

def setup_logging():
    """设置日志"""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('/app/logs/ctp_collector.log', encoding='utf-8')
        ]
    )
    
    return logging.getLogger('CTPCollector')

def load_config():
    """加载配置"""
    config_path = os.getenv('CONFIG_PATH', '/app/config/ctp_collector_config.json')
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"配置加载失败: {e}")
        return None

class CTPCollectorService:
    """CTP采集器服务"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('CTPCollectorService')
        self.running = False
        self.tasks = []
    
    async def initialize(self):
        """初始化服务"""
        self.logger.info("🚀 CTP数据采集器启动")
        self.logger.info(f"版本: {self.config['collector']['version']}")
        self.logger.info(f"模式: {self.config['collector']['mode']}")
        
        # 检查Redis连接
        if await self.check_redis():
            self.logger.info("✅ Redis连接正常")
        else:
            self.logger.error("❌ Redis连接失败")
            return False
        
        # 检查ClickHouse连接
        if await self.check_clickhouse():
            self.logger.info("✅ ClickHouse连接正常")
        else:
            self.logger.error("❌ ClickHouse连接失败")
            return False
        
        self.logger.info("✅ CTP采集器初始化完成")
        return True
    
    async def check_redis(self):
        """检查Redis连接"""
        try:
            import redis.asyncio as redis
            
            redis_config = self.config['storage']['redis']
            r = redis.Redis(
                host=redis_config['host'],
                port=redis_config['port'],
                db=redis_config['db'],
                socket_timeout=5
            )
            
            await r.ping()
            await r.close()
            return True
            
        except Exception as e:
            self.logger.error(f"Redis连接检查失败: {e}")
            return False
    
    async def check_clickhouse(self):
        """检查ClickHouse连接"""
        try:
            import aiohttp
            
            clickhouse_config = self.config['storage']['clickhouse']
            url = f"http://{clickhouse_config['host']}:{clickhouse_config.get('http_port', 8123)}/ping"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        return True
                    else:
                        return False
                        
        except Exception as e:
            self.logger.error(f"ClickHouse连接检查失败: {e}")
            return False
    
    async def mock_data_collection_task(self, task_name, interval=30):
        """模拟数据采集任务"""
        while self.running:
            try:
                # 模拟数据采集
                mock_data = {
                    'task': task_name,
                    'timestamp': datetime.now().isoformat(),
                    'data': {
                        'symbol': '000001',
                        'price': 12.34 + (hash(str(datetime.now())) % 100) / 100,
                        'volume': 1000000 + (hash(str(datetime.now())) % 500000),
                        'status': 'active',
                        'source': 'ctp_docker'
                    }
                }
                
                # 存储到Redis
                try:
                    import redis.asyncio as redis
                    
                    redis_config = self.config['storage']['redis']
                    r = redis.Redis(
                        host=redis_config['host'],
                        port=redis_config['port'],
                        db=redis_config['db']
                    )
                    
                    key = f"ctp_data:{task_name}:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    await r.setex(key, 3600, json.dumps(mock_data))
                    
                    # 更新最新数据
                    latest_key = f"ctp_latest:{task_name}"
                    await r.setex(latest_key, 300, json.dumps(mock_data))
                    
                    await r.close()
                    
                except Exception as e:
                    self.logger.error(f"数据存储失败: {e}")
                
                self.logger.info(f"✅ {task_name} 数据采集完成 - 价格: {mock_data['data']['price']:.2f}")
                
                # 等待下次执行
                await asyncio.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"❌ {task_name} 数据采集失败: {e}")
                await asyncio.sleep(5)
    
    async def system_monitor_task(self):
        """系统监控任务"""
        while self.running:
            try:
                import psutil
                
                # 获取系统信息
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                monitor_data = {
                    'timestamp': datetime.now().isoformat(),
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used': memory.used,
                    'disk_percent': disk.percent,
                    'tasks_running': len(self.tasks)
                }
                
                # 存储监控数据
                try:
                    import redis.asyncio as redis
                    
                    redis_config = self.config['storage']['redis']
                    r = redis.Redis(
                        host=redis_config['host'],
                        port=redis_config['port'],
                        db=redis_config['db']
                    )
                    
                    await r.setex('ctp_system_monitor', 60, json.dumps(monitor_data))
                    await r.close()
                    
                except Exception as e:
                    self.logger.error(f"监控数据存储失败: {e}")
                
                self.logger.info(f"📊 系统监控 - CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%, 磁盘: {disk.percent:.1f}%")
                
                await asyncio.sleep(60)  # 每分钟监控一次
                
            except Exception as e:
                self.logger.error(f"❌ 系统监控失败: {e}")
                await asyncio.sleep(30)
    
    async def start(self):
        """启动服务"""
        self.running = True
        
        # 创建任务
        self.tasks = [
            asyncio.create_task(self.mock_data_collection_task("股票数据", 15)),
            asyncio.create_task(self.mock_data_collection_task("期货数据", 20)),
            asyncio.create_task(self.mock_data_collection_task("指数数据", 25)),
            asyncio.create_task(self.system_monitor_task())
        ]
        
        self.logger.info(f"✅ 启动了 {len(self.tasks)} 个采集任务")
        
        # 等待所有任务完成
        try:
            await asyncio.gather(*self.tasks)
        except asyncio.CancelledError:
            self.logger.info("任务被取消")
    
    async def stop(self):
        """停止服务"""
        self.logger.info("🛑 正在停止CTP采集器...")
        self.running = False
        
        # 取消所有任务
        for task in self.tasks:
            task.cancel()
        
        # 等待任务清理
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        self.logger.info("✅ CTP采集器已停止")

async def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    
    # 加载配置
    config = load_config()
    if not config:
        logger.error("配置加载失败，退出")
        sys.exit(1)
    
    # 创建服务
    service = CTPCollectorService(config)
    
    try:
        # 初始化服务
        if not await service.initialize():
            logger.error("服务初始化失败")
            sys.exit(1)
        
        # 启动服务
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号...")
    except Exception as e:
        logger.error(f"服务运行异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await service.stop()
        logger.info("✅ 服务已安全关闭")

if __name__ == "__main__":
    asyncio.run(main())