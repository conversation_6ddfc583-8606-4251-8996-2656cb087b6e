@echo off
REM Comprehensive performance test runner for Windows
REM This script runs both C++ and Python performance tests

echo ========================================
echo Financial Data Service Performance Tests
echo ========================================
echo.

REM Set test configuration
set TEST_DURATION=300
set RESULTS_DIR=test_results
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Create results directory
if not exist %RESULTS_DIR% mkdir %RESULTS_DIR%

echo Starting performance test suite at %date% %time%
echo Results will be saved to: %RESULTS_DIR%
echo.

REM Check if performance test executable exists
if not exist "performance_tests.exe" (
    echo ERROR: performance_tests.exe not found
    echo Please build the project first using: cmake --build . --target performance_tests
    echo.
    pause
    exit /b 1
)

REM Run C++ performance tests
echo [1/3] Running C++ Performance Tests...
echo =====================================
performance_tests.exe > %RESULTS_DIR%\cpp_results_%TIMESTAMP%.txt 2>&1
set CPP_EXIT_CODE=%ERRORLEVEL%

if %CPP_EXIT_CODE% equ 0 (
    echo ✓ C++ tests completed successfully
) else (
    echo ✗ C++ tests failed with exit code %CPP_EXIT_CODE%
)
echo.

REM Run Python performance tests (if Python is available)
echo [2/3] Running Python Performance Tests...
echo =========================================
python --version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    python run_python_tests.py --export %RESULTS_DIR%\python_results_%TIMESTAMP%.json > %RESULTS_DIR%\python_output_%TIMESTAMP%.txt 2>&1
    set PYTHON_EXIT_CODE=%ERRORLEVEL%
    
    if %PYTHON_EXIT_CODE% equ 0 (
        echo ✓ Python tests completed successfully
    ) else (
        echo ✗ Python tests failed with exit code %PYTHON_EXIT_CODE%
    )
) else (
    echo ⚠ Python not found, skipping Python tests
    set PYTHON_EXIT_CODE=0
)
echo.

REM Generate summary report
echo [3/3] Generating Summary Report...
echo ==================================

echo Performance Test Summary > %RESULTS_DIR%\summary_%TIMESTAMP%.txt
echo ======================== >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
echo Test Date: %date% %time% >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
echo. >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt

echo C++ Tests: >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
if %CPP_EXIT_CODE% equ 0 (
    echo   Status: PASSED >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
) else (
    echo   Status: FAILED >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
)
echo   Results: cpp_results_%TIMESTAMP%.txt >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
echo. >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt

echo Python Tests: >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
if %PYTHON_EXIT_CODE% equ 0 (
    echo   Status: PASSED >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
) else (
    echo   Status: FAILED >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
)
echo   Results: python_results_%TIMESTAMP%.json >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
echo. >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt

REM Extract key metrics from C++ results
echo Key Performance Metrics: >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
echo ======================= >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt
findstr /C:"End-to-end latency:" %RESULTS_DIR%\cpp_results_%TIMESTAMP%.txt >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt 2>nul
findstr /C:"Data ingestion throughput:" %RESULTS_DIR%\cpp_results_%TIMESTAMP%.txt >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt 2>nul
findstr /C:"Concurrent connections:" %RESULTS_DIR%\cpp_results_%TIMESTAMP%.txt >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt 2>nul
findstr /C:"Data loss:" %RESULTS_DIR%\cpp_results_%TIMESTAMP%.txt >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt 2>nul
findstr /C:"Failover time:" %RESULTS_DIR%\cpp_results_%TIMESTAMP%.txt >> %RESULTS_DIR%\summary_%TIMESTAMP%.txt 2>nul

echo.
echo ========================================
echo Performance Test Suite Complete
echo ========================================

REM Calculate overall result
set OVERALL_RESULT=0
if %CPP_EXIT_CODE% neq 0 set OVERALL_RESULT=1
if %PYTHON_EXIT_CODE% neq 0 set OVERALL_RESULT=1

if %OVERALL_RESULT% equ 0 (
    echo ✓ All tests PASSED
    echo.
    echo Key Requirements Validation:
    echo - End-to-end latency ^< 50μs: Check cpp_results_%TIMESTAMP%.txt
    echo - Throughput ^> 1M msg/s: Check cpp_results_%TIMESTAMP%.txt  
    echo - 1000 concurrent connections: Check cpp_results_%TIMESTAMP%.txt
    echo - Zero data loss: Check cpp_results_%TIMESTAMP%.txt
    echo - Failover ^< 5s: Check cpp_results_%TIMESTAMP%.txt
) else (
    echo ✗ Some tests FAILED
    echo Please review the detailed results in %RESULTS_DIR%
)

echo.
echo Results saved to: %RESULTS_DIR%\summary_%TIMESTAMP%.txt
echo.

REM Open results directory
if exist "%RESULTS_DIR%" (
    echo Opening results directory...
    start "" "%RESULTS_DIR%"
)

pause
exit /b %OVERALL_RESULT%