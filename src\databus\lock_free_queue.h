#pragma once

#include <atomic>
#include <memory>
#include <array>
#include <cstddef>
#include <type_traits>
#include "data_types.h"

namespace financial_data {
namespace databus {

/**
 * @brief 高性能无锁环形队列
 * 
 * 基于共享内存的无锁队列实现，支持单生产者多消费者模式(SPMC)
 * 或多生产者单消费者模式(MPSC)，针对金融数据传输优化
 */
template<typename T, size_t Size>
class LockFreeQueue {
    static_assert((Size & (Size - 1)) == 0, "Size must be power of 2");
    static_assert(std::is_trivially_copyable_v<T>, "T must be trivially copyable");

private:
    struct alignas(64) Node {  // 缓存行对齐
        std::atomic<bool> ready{false};
        T data;
    };

    alignas(64) std::array<Node, Size> buffer_;
    alignas(64) std::atomic<size_t> write_pos_{0};
    alignas(64) std::atomic<size_t> read_pos_{0};
    
    static constexpr size_t MASK = Size - 1;

public:
    LockFreeQueue() = default;
    ~LockFreeQueue() = default;

    // 禁止拷贝和移动
    LockFreeQueue(const LockFreeQueue&) = delete;
    LockFreeQueue& operator=(const LockFreeQueue&) = delete;
    LockFreeQueue(LockFreeQueue&&) = delete;
    LockFreeQueue& operator=(LockFreeQueue&&) = delete;

    /**
     * @brief 尝试推入数据（非阻塞）
     * @param item 要推入的数据
     * @return true 成功推入，false 队列已满
     */
    bool TryPush(const T& item) noexcept {
        const size_t current_write = write_pos_.load(std::memory_order_relaxed);
        const size_t next_write = (current_write + 1) & MASK;
        
        // 检查队列是否已满
        if (next_write == read_pos_.load(std::memory_order_acquire)) {
            return false;
        }
        
        Node& node = buffer_[current_write];
        node.data = item;
        node.ready.store(true, std::memory_order_release);
        
        write_pos_.store(next_write, std::memory_order_release);
        return true;
    }

    /**
     * @brief 尝试推入数据（移动语义）
     */
    bool TryPush(T&& item) noexcept {
        const size_t current_write = write_pos_.load(std::memory_order_relaxed);
        const size_t next_write = (current_write + 1) & MASK;
        
        if (next_write == read_pos_.load(std::memory_order_acquire)) {
            return false;
        }
        
        Node& node = buffer_[current_write];
        node.data = std::move(item);
        node.ready.store(true, std::memory_order_release);
        
        write_pos_.store(next_write, std::memory_order_release);
        return true;
    }

    /**
     * @brief 尝试弹出数据（非阻塞）
     * @param item 输出参数，存储弹出的数据
     * @return true 成功弹出，false 队列为空
     */
    bool TryPop(T& item) noexcept {
        const size_t current_read = read_pos_.load(std::memory_order_relaxed);
        
        Node& node = buffer_[current_read];
        if (!node.ready.load(std::memory_order_acquire)) {
            return false;  // 队列为空或数据未准备好
        }
        
        item = std::move(node.data);
        node.ready.store(false, std::memory_order_release);
        
        read_pos_.store((current_read + 1) & MASK, std::memory_order_release);
        return true;
    }

    /**
     * @brief 检查队列是否为空
     */
    bool IsEmpty() const noexcept {
        const size_t current_read = read_pos_.load(std::memory_order_acquire);
        return !buffer_[current_read].ready.load(std::memory_order_acquire);
    }

    /**
     * @brief 检查队列是否已满
     */
    bool IsFull() const noexcept {
        const size_t current_write = write_pos_.load(std::memory_order_acquire);
        const size_t next_write = (current_write + 1) & MASK;
        return next_write == read_pos_.load(std::memory_order_acquire);
    }

    /**
     * @brief 获取队列大小（近似值）
     */
    size_t Size() const noexcept {
        const size_t write_pos = write_pos_.load(std::memory_order_acquire);
        const size_t read_pos = read_pos_.load(std::memory_order_acquire);
        return (write_pos - read_pos) & MASK;
    }

    /**
     * @brief 获取队列容量
     */
    static constexpr size_t Capacity() noexcept {
        return Size - 1;  // 环形队列实际容量比数组大小小1
    }

    /**
     * @brief 清空队列
     */
    void Clear() noexcept {
        T dummy;
        while (TryPop(dummy)) {
            // 清空所有元素
        }
    }
};

// 针对金融数据的特化队列类型
using TickQueue = LockFreeQueue<StandardTick, 65536>;           // 64K tick数据队列
using Level2Queue = LockFreeQueue<Level2Data, 32768>;          // 32K Level2数据队列
using BatchQueue = LockFreeQueue<MarketDataBatch, 4096>;       // 4K 批次数据队列
using WrapperQueue = LockFreeQueue<MarketDataWrapper, 131072>; // 128K 包装数据队列

/**
 * @brief 多生产者单消费者队列
 * 
 * 使用CAS操作支持多个生产者同时写入
 */
template<typename T, size_t Size>
class MPSCQueue {
    static_assert((Size & (Size - 1)) == 0, "Size must be power of 2");

private:
    struct alignas(64) Node {
        std::atomic<uint64_t> sequence{0};
        T data;
    };

    alignas(64) std::array<Node, Size> buffer_;
    alignas(64) std::atomic<uint64_t> enqueue_pos_{0};
    alignas(64) std::atomic<uint64_t> dequeue_pos_{0};
    
    static constexpr size_t MASK = Size - 1;

public:
    MPSCQueue() {
        // 初始化序列号
        for (size_t i = 0; i < Size; ++i) {
            buffer_[i].sequence.store(i, std::memory_order_relaxed);
        }
    }

    bool TryPush(const T& item) noexcept {
        uint64_t pos = enqueue_pos_.load(std::memory_order_relaxed);
        
        for (;;) {
            Node& node = buffer_[pos & MASK];
            uint64_t seq = node.sequence.load(std::memory_order_acquire);
            
            if (seq == pos) {
                // 尝试CAS更新位置
                if (enqueue_pos_.compare_exchange_weak(pos, pos + 1, std::memory_order_relaxed)) {
                    node.data = item;
                    node.sequence.store(pos + 1, std::memory_order_release);
                    return true;
                }
            } else if (seq < pos) {
                return false;  // 队列已满
            } else {
                pos = enqueue_pos_.load(std::memory_order_relaxed);
            }
        }
    }

    bool TryPop(T& item) noexcept {
        uint64_t pos = dequeue_pos_.load(std::memory_order_relaxed);
        Node& node = buffer_[pos & MASK];
        uint64_t seq = node.sequence.load(std::memory_order_acquire);
        
        if (seq == pos + 1) {
            item = std::move(node.data);
            node.sequence.store(pos + Size, std::memory_order_release);
            dequeue_pos_.store(pos + 1, std::memory_order_release);
            return true;
        }
        
        return false;  // 队列为空
    }

    bool IsEmpty() const noexcept {
        uint64_t pos = dequeue_pos_.load(std::memory_order_acquire);
        Node& node = buffer_[pos & MASK];
        return node.sequence.load(std::memory_order_acquire) != pos + 1;
    }

    size_t Size() const noexcept {
        uint64_t enqueue_pos = enqueue_pos_.load(std::memory_order_acquire);
        uint64_t dequeue_pos = dequeue_pos_.load(std::memory_order_acquire);
        return enqueue_pos - dequeue_pos;
    }
};

// MPSC队列特化类型
using MPSCTickQueue = MPSCQueue<StandardTick, 65536>;
using MPSCWrapperQueue = MPSCQueue<MarketDataWrapper, 131072>;

} // namespace databus
} // namespace financial_data