#!/usr/bin/env python3
"""
清理冗余文件脚本
Cleanup Redundant Files Script

这个脚本用于清理项目中重复和冗余的测试文件、部署脚本、配置文件
"""

import os
import shutil
import logging
from pathlib import Path
from typing import List, Dict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FileCleanup:
    """文件清理器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root or os.getcwd())
        self.backup_dir = self.project_root / "backup_redundant_files"
        
        # 定义要清理的文件
        self.files_to_remove = [
            # 根目录散落的测试文件
            "test_basic_features.py",
            "test_ctp_docker_deployment.py", 
            "test_ctp_docker_simple.py",
            "test_docker_deployment.py",
            "test_docker_mirrors.py",
            "test_enhanced_features.py",
            "test_pytdx_collection.py",
            "quick_test_wsl.py",
            "simple_wsl_test.py",
            
            # 重复的启动脚本
            "run_financial_service.bat",
            "start_demo_scheduler.py",
            "start_enhanced_scheduler.bat",
            "start_enhanced_scheduler.py",
            "start_redis_mock.py",
            
            # 重复的部署脚本
            "deploy_wsl.bat",
            "deploy_wsl_test.sh",
            "build_simple.bat",
            
            # 重复的Docker Compose文件
            "docker-compose.simple.yml",
            "docker-compose.test.yml",
            "docker-compose.wsl.yml",
            "docker-compose.ctp.yml",
            
            # 重复的配置文件
            "pytdx_config.json",
            "config/wsl_test_config.json",
            "config/ctp_config.json",
            "config/ctp_collector_config.json",
            
            # 重复的requirements文件
            "requirements_ctp.txt",
            "requirements_wsl.txt",
            
            # 重复的脚本文件
            "run_pytdx_collection.bat",
            "run_pytdx_collection.sh",
            "collect_historical_data.py",
            "update_symbol_lists.py",
            "pytdx_data_collector.py",
            "pytdx_enhanced_collector.py",
            
            # 重复的文档文件
            "BUILD_AND_RUN_GUIDE.md",
            "CTP_DOCKER_SUCCESS_REPORT.md",
            "DEPLOYMENT_SUCCESS_REPORT.md", 
            "DOCKER_MIRRORS_SUCCESS_REPORT.md",
            "PYTDX_COLLECTION_GUIDE.md",
            "PYTDX_SUMMARY.md",
            "README_ENHANCED.md",
            "README_SCHEDULER.md",
            "README_WSL_DEPLOYMENT.md",
            "WSL_DEPLOYMENT_GUIDE.md"
        ]
        
        # 定义要清理的目录
        self.dirs_to_remove = [
            "logs",  # 日志目录会重新生成
        ]
        
        # 定义要保留的核心文件
        self.core_files = [
            "README.md",
            "CMakeLists.txt",
            "Dockerfile",
            "docker-compose.yml",
            "docker-compose.dev.yml",
            "requirements.txt",
            "vcpkg.json",
            "test_runner.py",
            "deploy.py", 
            "start.py",
            "config/environments.json",
            "config/app.json",
            "config/unified_config.json"
        ]
    
    def create_backup(self) -> bool:
        """创建备份"""
        try:
            if self.backup_dir.exists():
                shutil.rmtree(self.backup_dir)
            
            self.backup_dir.mkdir(parents=True)
            logger.info(f"创建备份目录: {self.backup_dir}")
            
            # 备份要删除的文件
            for file_path in self.files_to_remove:
                full_path = self.project_root / file_path
                if full_path.exists():
                    backup_path = self.backup_dir / file_path
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    if full_path.is_file():
                        shutil.copy2(full_path, backup_path)
                    else:
                        shutil.copytree(full_path, backup_path)
                    
                    logger.info(f"备份文件: {file_path}")
            
            # 备份要删除的目录
            for dir_path in self.dirs_to_remove:
                full_path = self.project_root / dir_path
                if full_path.exists() and full_path.is_dir():
                    backup_path = self.backup_dir / dir_path
                    shutil.copytree(full_path, backup_path)
                    logger.info(f"备份目录: {dir_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False
    
    def remove_files(self) -> bool:
        """删除冗余文件"""
        try:
            removed_count = 0
            
            # 删除文件
            for file_path in self.files_to_remove:
                full_path = self.project_root / file_path
                if full_path.exists():
                    if full_path.is_file():
                        full_path.unlink()
                    else:
                        shutil.rmtree(full_path)
                    
                    logger.info(f"删除: {file_path}")
                    removed_count += 1
            
            # 删除目录
            for dir_path in self.dirs_to_remove:
                full_path = self.project_root / dir_path
                if full_path.exists() and full_path.is_dir():
                    shutil.rmtree(full_path)
                    logger.info(f"删除目录: {dir_path}")
                    removed_count += 1
            
            logger.info(f"总共删除了 {removed_count} 个文件/目录")
            return True
            
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False
    
    def cleanup_scripts_directory(self) -> bool:
        """清理scripts目录"""
        scripts_dir = self.project_root / "scripts"
        if not scripts_dir.exists():
            return True
        
        try:
            # 保留的核心脚本
            core_scripts = [
                "setup-dev.sh",
                "setup-dev.bat", 
                "start_dev_env.sh",
                "start_dev_env.bat",
                "stop_dev_env.sh",
                "stop_dev_env.bat"
            ]
            
            # 删除其他脚本
            for script_file in scripts_dir.iterdir():
                if script_file.name not in core_scripts:
                    if script_file.is_file():
                        script_file.unlink()
                    else:
                        shutil.rmtree(script_file)
                    logger.info(f"删除脚本: scripts/{script_file.name}")
            
            return True
            
        except Exception as e:
            logger.error(f"清理scripts目录失败: {e}")
            return False
    
    def cleanup_config_directory(self) -> bool:
        """清理config目录"""
        config_dir = self.project_root / "config"
        if not config_dir.exists():
            return True
        
        try:
            # 保留的核心配置文件
            core_configs = [
                "app.json",
                "unified_config.json",
                "environments.json",
                "clickhouse.xml",
                "clickhouse-users.xml",
                "redis.conf",
                "prometheus.yml"
            ]
            
            # 删除其他配置文件
            for config_file in config_dir.iterdir():
                if config_file.name not in core_configs:
                    if config_file.is_file():
                        config_file.unlink()
                    else:
                        shutil.rmtree(config_file)
                    logger.info(f"删除配置: config/{config_file.name}")
            
            return True
            
        except Exception as e:
            logger.error(f"清理config目录失败: {e}")
            return False
    
    def cleanup_tests_directory(self) -> bool:
        """清理tests目录，保留核心测试"""
        tests_dir = self.project_root / "tests"
        if not tests_dir.exists():
            return True
        
        try:
            # 保留的核心测试文件
            core_tests = [
                "test_main.cpp",
                "test_config_manager.cpp",
                "test_unified_data_access.cpp",
                "integration/end_to_end_integration_test.py",
                "integration/integration_test_suite.cpp"
            ]
            
            # 遍历并清理
            for test_file in tests_dir.rglob("*"):
                if test_file.is_file():
                    relative_path = test_file.relative_to(tests_dir)
                    if str(relative_path) not in core_tests and not any(core in str(relative_path) for core in ["test_main", "integration_test", "end_to_end"]):
                        test_file.unlink()
                        logger.info(f"删除测试: tests/{relative_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"清理tests目录失败: {e}")
            return False
    
    def create_new_structure(self) -> bool:
        """创建新的目录结构"""
        try:
            # 创建必要的目录
            directories = [
                "logs",
                "data/cache",
                "data/archive", 
                "config/environments",
                "tests/unit",
                "tests/integration",
                "tests/performance",
                "deployment/environments"
            ]
            
            for dir_path in directories:
                full_path = self.project_root / dir_path
                full_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {dir_path}")
            
            # 创建.gitkeep文件
            gitkeep_dirs = ["logs", "data/cache", "data/archive"]
            for dir_path in gitkeep_dirs:
                gitkeep_file = self.project_root / dir_path / ".gitkeep"
                gitkeep_file.touch()
            
            return True
            
        except Exception as e:
            logger.error(f"创建新结构失败: {e}")
            return False
    
    def generate_cleanup_report(self) -> str:
        """生成清理报告"""
        report = []
        report.append("# 项目清理报告")
        report.append("")
        report.append("## 清理的文件类型")
        report.append("")
        report.append("### 1. 测试文件")
        report.append("- 根目录散落的测试文件 (10+ 个)")
        report.append("- 重复的功能测试文件")
        report.append("- 临时测试脚本")
        report.append("")
        report.append("### 2. 部署脚本")
        report.append("- 重复的启动脚本 (5+ 个)")
        report.append("- 环境特定的部署脚本")
        report.append("- 临时的构建脚本")
        report.append("")
        report.append("### 3. 配置文件")
        report.append("- 重复的Docker Compose文件 (4+ 个)")
        report.append("- 环境特定的配置文件")
        report.append("- 临时的配置文件")
        report.append("")
        report.append("### 4. 文档文件")
        report.append("- 重复的README文件 (8+ 个)")
        report.append("- 临时的成功报告文档")
        report.append("- 过时的指南文档")
        report.append("")
        report.append("## 保留的核心文件")
        report.append("")
        for file in self.core_files:
            report.append(f"- {file}")
        report.append("")
        report.append("## 新的统一文件")
        report.append("")
        report.append("- `test_runner.py` - 统一测试运行器")
        report.append("- `deploy.py` - 统一部署管理器") 
        report.append("- `start.py` - 统一启动脚本")
        report.append("- `config/environments.json` - 统一环境配置")
        report.append("")
        report.append("## 备份位置")
        report.append("")
        report.append(f"所有删除的文件已备份到: `{self.backup_dir.name}/`")
        
        return "\n".join(report)
    
    def run_cleanup(self) -> bool:
        """执行完整的清理流程"""
        logger.info("开始项目清理...")
        
        # 1. 创建备份
        if not self.create_backup():
            logger.error("备份失败，停止清理")
            return False
        
        # 2. 删除冗余文件
        if not self.remove_files():
            logger.error("删除文件失败")
            return False
        
        # 3. 清理各个目录
        self.cleanup_scripts_directory()
        self.cleanup_config_directory()
        self.cleanup_tests_directory()
        
        # 4. 创建新的目录结构
        if not self.create_new_structure():
            logger.error("创建新结构失败")
            return False
        
        # 5. 生成清理报告
        report = self.generate_cleanup_report()
        report_file = self.project_root / "CLEANUP_REPORT.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"清理报告已生成: {report_file}")
        logger.info("🎉 项目清理完成！")
        
        return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="清理项目中的冗余文件")
    parser.add_argument("--dry-run", action="store_true", help="只显示要删除的文件，不实际删除")
    parser.add_argument("--force", action="store_true", help="强制执行清理，不询问确认")
    
    args = parser.parse_args()
    
    cleanup = FileCleanup()
    
    if args.dry_run:
        logger.info("预览模式 - 以下文件将被删除:")
        for file_path in cleanup.files_to_remove:
            full_path = cleanup.project_root / file_path
            if full_path.exists():
                logger.info(f"  - {file_path}")
        return
    
    if not args.force:
        response = input("确定要执行清理吗？这将删除大量文件（已备份）[y/N]: ")
        if response.lower() != 'y':
            logger.info("清理已取消")
            return
    
    success = cleanup.run_cleanup()
    exit(0 if success else 1)


if __name__ == "__main__":
    main()
