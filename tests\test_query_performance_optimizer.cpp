#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <chrono>
#include <thread>

#include "../src/storage/query_performance_optimizer.h"
#include "../src/storage/unified_data_access.h"

using namespace financial_data;
using namespace testing;

// Mock UnifiedDataAccessInterface for testing
class MockUnifiedDataAccessInterface : public UnifiedDataAccessInterface {
public:
    MockUnifiedDataAccessInterface() : UnifiedDataAccessInterface() {}
    
    MOCK_METHOD(std::future<QueryResponse>, QueryData, (const QueryRequest& request), (override));
};

class QueryPerformanceOptimizerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建mock数据访问接口
        mock_data_access_ = std::make_shared<MockUnifiedDataAccessInterface>();
        
        // 配置优化器
        config_.max_cache_size = 100;
        config_.cache_ttl = std::chrono::minutes(5);
        config_.enable_batch_optimization = true;
        config_.batch_size = 10;
        config_.enable_pagination = true;
        config_.default_page_size = 50;
        config_.enable_prefetching = true;
        config_.enable_query_coalescing = true;
        
        optimizer_ = std::make_unique<QueryPerformanceOptimizer>(config_);
        ASSERT_TRUE(optimizer_->Initialize(mock_data_access_));
    }
    
    void TearDown() override {
        optimizer_->Shutdown();
    }
    
    QueryRequest CreateTestRequest(const std::string& symbol, int64_t start_time, int64_t end_time) {
        QueryRequest request;
        request.symbol = symbol;
        request.exchange = "TEST";
        request.data_type = "tick";
        request.start_timestamp_ns = start_time;
        request.end_timestamp_ns = end_time;
        request.limit = 100;
        return request;
    }
    
    QueryResponse CreateTestResponse(const std::string& symbol, size_t tick_count) {
        QueryResponse response;
        response.storage_source = "test";
        
        int64_t base_time = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();
        
        for (size_t i = 0; i < tick_count; ++i) {
            StandardTick tick;
            tick.symbol = symbol;
            tick.exchange = "TEST";
            tick.timestamp_ns = base_time + i * 1000000; // 1ms间隔
            tick.last_price = 100.0 + i * 0.01;
            tick.volume = 1000;
            response.ticks.push_back(tick);
        }
        
        response.total_records = tick_count;
        return response;
    }

protected:
    std::shared_ptr<MockUnifiedDataAccessInterface> mock_data_access_;
    std::unique_ptr<QueryPerformanceOptimizer> optimizer_;
    QueryOptimizationConfig config_;
};

// 测试基本的优化查询
TEST_F(QueryPerformanceOptimizerTest, BasicOptimizedQueryTest) {
    std::string symbol = "TEST001";
    int64_t start_time = 1000000000;
    int64_t end_time = 2000000000;
    
    QueryRequest request = CreateTestRequest(symbol, start_time, end_time);
    QueryResponse expected_response = CreateTestResponse(symbol, 10);
    
    // 设置mock期望
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillOnce([expected_response](const QueryRequest&) {
            std::promise<QueryResponse> promise;
            promise.set_value(expected_response);
            return promise.get_future();
        });
    
    // 执行优化查询
    auto future = optimizer_->OptimizedQuery(request);
    auto response = future.get();
    
    EXPECT_EQ(response.ticks.size(), 10);
    EXPECT_EQ(response.ticks[0].symbol, symbol);
    EXPECT_EQ(response.storage_source, "test");
}

// 测试查询缓存
TEST_F(QueryPerformanceOptimizerTest, QueryCacheTest) {
    std::string symbol = "TEST001";
    int64_t start_time = 1000000000;
    int64_t end_time = 2000000000;
    
    QueryRequest request = CreateTestRequest(symbol, start_time, end_time);
    QueryResponse expected_response = CreateTestResponse(symbol, 5);
    
    // 第一次查询应该调用数据访问接口
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillOnce([expected_response](const QueryRequest&) {
            std::promise<QueryResponse> promise;
            promise.set_value(expected_response);
            return promise.get_future();
        });
    
    // 执行第一次查询
    auto future1 = optimizer_->OptimizedQuery(request);
    auto response1 = future1.get();
    
    EXPECT_EQ(response1.ticks.size(), 5);
    
    // 第二次查询应该从缓存返回，不调用数据访问接口
    auto future2 = optimizer_->OptimizedQuery(request);
    auto response2 = future2.get();
    
    EXPECT_EQ(response2.ticks.size(), 5);
    EXPECT_EQ(response2.ticks[0].symbol, symbol);
    
    // 验证缓存命中率
    EXPECT_GT(optimizer_->GetCacheHitRatio(), 0.0);
}

// 测试批量查询
TEST_F(QueryPerformanceOptimizerTest, BatchQueryTest) {
    std::vector<QueryRequest> requests;
    std::vector<QueryResponse> expected_responses;
    
    // 创建多个查询请求
    for (int i = 0; i < 5; ++i) {
        std::string symbol = "TEST" + std::to_string(i);
        QueryRequest request = CreateTestRequest(symbol, 1000000000, 2000000000);
        requests.push_back(request);
        
        QueryResponse response = CreateTestResponse(symbol, 3);
        expected_responses.push_back(response);
    }
    
    // 设置mock期望 - 每个查询都会被调用
    for (size_t i = 0; i < requests.size(); ++i) {
        EXPECT_CALL(*mock_data_access_, QueryData(_))
            .WillOnce([expected_responses, i](const QueryRequest&) {
                std::promise<QueryResponse> promise;
                promise.set_value(expected_responses[i]);
                return promise.get_future();
            });
    }
    
    // 执行批量查询
    auto future = optimizer_->BatchQuery(requests);
    auto responses = future.get();
    
    EXPECT_EQ(responses.size(), 5);
    for (size_t i = 0; i < responses.size(); ++i) {
        EXPECT_EQ(responses[i].ticks.size(), 3);
        EXPECT_EQ(responses[i].ticks[0].symbol, "TEST" + std::to_string(i));
    }
}

// 测试分页查询
TEST_F(QueryPerformanceOptimizerTest, PaginatedQueryTest) {
    std::string symbol = "TEST001";
    QueryRequest request = CreateTestRequest(symbol, 1000000000, 2000000000);
    request.limit = 20; // 请求20条记录
    
    // 创建包含更多数据的响应
    QueryResponse full_response = CreateTestResponse(symbol, 100);
    
    // 设置mock期望
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillOnce([full_response](const QueryRequest&) {
            std::promise<QueryResponse> promise;
            promise.set_value(full_response);
            return promise.get_future();
        });
    
    // 执行分页查询
    auto future = optimizer_->PaginatedQuery(request);
    auto response = future.get();
    
    EXPECT_LE(response.ticks.size(), config_.default_page_size);
    EXPECT_TRUE(response.has_more || response.ticks.size() <= config_.default_page_size);
    
    // 如果有更多数据，应该有下一页游标
    if (response.has_more) {
        EXPECT_FALSE(response.next_cursor.empty());
    }
}

// 测试分页游标创建和验证
TEST_F(QueryPerformanceOptimizerTest, PaginationCursorTest) {
    std::string symbol = "TEST001";
    QueryRequest request = CreateTestRequest(symbol, 1000000000, 2000000000);
    
    // 创建分页游标
    std::string cursor = optimizer_->CreatePaginationCursor(request);
    EXPECT_FALSE(cursor.empty());
    
    // 验证游标
    EXPECT_TRUE(optimizer_->ValidateCursor(cursor));
    
    // 验证无效游标
    EXPECT_FALSE(optimizer_->ValidateCursor("invalid_cursor"));
}

// 测试缓存管理
TEST_F(QueryPerformanceOptimizerTest, CacheManagementTest) {
    // 初始缓存应该为空
    EXPECT_EQ(optimizer_->GetCacheSize(), 0);
    
    std::string symbol = "TEST001";
    QueryRequest request = CreateTestRequest(symbol, 1000000000, 2000000000);
    QueryResponse expected_response = CreateTestResponse(symbol, 5);
    
    // 设置mock期望
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillOnce([expected_response](const QueryRequest&) {
            std::promise<QueryResponse> promise;
            promise.set_value(expected_response);
            return promise.get_future();
        });
    
    // 执行查询以填充缓存
    auto future = optimizer_->OptimizedQuery(request);
    auto response = future.get();
    
    EXPECT_GT(optimizer_->GetCacheSize(), 0);
    
    // 清空缓存
    optimizer_->InvalidateCache();
    EXPECT_EQ(optimizer_->GetCacheSize(), 0);
}

// 测试缓存失效
TEST_F(QueryPerformanceOptimizerTest, CacheInvalidationTest) {
    std::string symbol = "TEST001";
    QueryRequest request = CreateTestRequest(symbol, 1000000000, 2000000000);
    QueryResponse expected_response = CreateTestResponse(symbol, 5);
    
    // 设置mock期望 - 第一次查询
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillOnce([expected_response](const QueryRequest&) {
            std::promise<QueryResponse> promise;
            promise.set_value(expected_response);
            return promise.get_future();
        });
    
    // 执行查询
    auto future1 = optimizer_->OptimizedQuery(request);
    auto response1 = future1.get();
    
    EXPECT_GT(optimizer_->GetCacheSize(), 0);
    
    // 使特定模式的缓存失效
    optimizer_->InvalidateCache(symbol);
    
    // 设置mock期望 - 缓存失效后的查询
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillOnce([expected_response](const QueryRequest&) {
            std::promise<QueryResponse> promise;
            promise.set_value(expected_response);
            return promise.get_future();
        });
    
    // 再次执行查询，应该重新从数据源获取
    auto future2 = optimizer_->OptimizedQuery(request);
    auto response2 = future2.get();
    
    EXPECT_EQ(response2.ticks.size(), 5);
}

// 测试性能指标
TEST_F(QueryPerformanceOptimizerTest, PerformanceMetricsTest) {
    // 初始指标应该为0
    auto initial_metrics = optimizer_->GetMetrics();
    EXPECT_EQ(initial_metrics.total_queries.load(), 0);
    EXPECT_EQ(initial_metrics.cache_hits.load(), 0);
    EXPECT_EQ(initial_metrics.cache_misses.load(), 0);
    
    std::string symbol = "TEST001";
    QueryRequest request = CreateTestRequest(symbol, 1000000000, 2000000000);
    QueryResponse expected_response = CreateTestResponse(symbol, 5);
    
    // 设置mock期望
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillOnce([expected_response](const QueryRequest&) {
            std::promise<QueryResponse> promise;
            promise.set_value(expected_response);
            return promise.get_future();
        });
    
    // 执行查询
    auto future = optimizer_->OptimizedQuery(request);
    auto response = future.get();
    
    // 检查指标更新
    auto updated_metrics = optimizer_->GetMetrics();
    EXPECT_GT(updated_metrics.total_queries.load(), 0);
    EXPECT_GE(updated_metrics.avg_query_time_ms.load(), 0.0);
    
    // 重置指标
    optimizer_->ResetMetrics();
    auto reset_metrics = optimizer_->GetMetrics();
    EXPECT_EQ(reset_metrics.total_queries.load(), 0);
}

// 测试配置更新
TEST_F(QueryPerformanceOptimizerTest, ConfigUpdateTest) {
    QueryOptimizationConfig new_config = config_;
    new_config.max_cache_size = 200;
    new_config.cache_ttl = std::chrono::minutes(10);
    new_config.batch_size = 20;
    
    EXPECT_TRUE(optimizer_->UpdateConfig(new_config));
    
    auto current_config = optimizer_->GetConfig();
    EXPECT_EQ(current_config.max_cache_size, 200);
    EXPECT_EQ(current_config.cache_ttl, std::chrono::minutes(10));
    EXPECT_EQ(current_config.batch_size, 20);
}

// 测试慢查询检测
TEST_F(QueryPerformanceOptimizerTest, SlowQueryDetectionTest) {
    // 设置较低的慢查询阈值用于测试
    QueryOptimizationConfig slow_config = config_;
    slow_config.slow_query_threshold_ms = 1; // 1ms阈值
    
    auto slow_optimizer = std::make_unique<QueryPerformanceOptimizer>(slow_config);
    ASSERT_TRUE(slow_optimizer->Initialize(mock_data_access_));
    
    std::string symbol = "TEST001";
    QueryRequest request = CreateTestRequest(symbol, 1000000000, 2000000000);
    QueryResponse expected_response = CreateTestResponse(symbol, 5);
    
    // 设置mock期望，模拟慢查询
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillOnce([expected_response](const QueryRequest&) {
            // 模拟慢查询
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            std::promise<QueryResponse> promise;
            promise.set_value(expected_response);
            return promise.get_future();
        });
    
    // 执行查询
    auto future = slow_optimizer->OptimizedQuery(request);
    auto response = future.get();
    
    // 等待一段时间让指标更新
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 检查慢查询指标
    auto metrics = slow_optimizer->GetMetrics();
    EXPECT_GT(metrics.slow_queries.load(), 0);
    
    slow_optimizer->Shutdown();
}

// 测试工厂方法
TEST_F(QueryPerformanceOptimizerTest, FactoryMethodsTest) {
    // 测试默认工厂方法
    auto default_optimizer = QueryPerformanceOptimizerFactory::CreateDefault();
    EXPECT_TRUE(default_optimizer != nullptr);
    EXPECT_TRUE(default_optimizer->Initialize(mock_data_access_));
    default_optimizer->Shutdown();
    
    // 测试高吞吐量工厂方法
    auto high_throughput_optimizer = QueryPerformanceOptimizerFactory::CreateHighThroughput();
    EXPECT_TRUE(high_throughput_optimizer != nullptr);
    EXPECT_TRUE(high_throughput_optimizer->Initialize(mock_data_access_));
    
    auto ht_config = high_throughput_optimizer->GetConfig();
    EXPECT_EQ(ht_config.cache_strategy, CacheStrategy::ADAPTIVE);
    EXPECT_GT(ht_config.max_cache_size, config_.max_cache_size);
    EXPECT_TRUE(ht_config.enable_batch_optimization);
    
    high_throughput_optimizer->Shutdown();
    
    // 测试低延迟工厂方法
    auto low_latency_optimizer = QueryPerformanceOptimizerFactory::CreateLowLatency();
    EXPECT_TRUE(low_latency_optimizer != nullptr);
    EXPECT_TRUE(low_latency_optimizer->Initialize(mock_data_access_));
    
    auto ll_config = low_latency_optimizer->GetConfig();
    EXPECT_EQ(ll_config.cache_strategy, CacheStrategy::LRU);
    EXPECT_TRUE(ll_config.enable_prefetching);
    EXPECT_LT(ll_config.batch_timeout, config_.batch_timeout);
    
    low_latency_optimizer->Shutdown();
}

// 测试并发安全性
TEST_F(QueryPerformanceOptimizerTest, ConcurrentAccessTest) {
    const int num_threads = 5;
    const int queries_per_thread = 20;
    std::vector<std::thread> threads;
    
    std::string symbol = "TEST001";
    QueryResponse expected_response = CreateTestResponse(symbol, 3);
    
    // 设置mock期望 - 允许多次调用
    EXPECT_CALL(*mock_data_access_, QueryData(_))
        .WillRepeatedly([expected_response](const QueryRequest&) {
            std::promise<QueryResponse> promise;
            promise.set_value(expected_response);
            return promise.get_future();
        });
    
    // 启动多个线程同时执行查询
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, symbol, queries_per_thread, i]() {
            for (int j = 0; j < queries_per_thread; ++j) {
                QueryRequest request = CreateTestRequest(
                    symbol + std::to_string(i), 
                    1000000000 + j * 1000000, 
                    2000000000 + j * 1000000);
                
                auto future = optimizer_->OptimizedQuery(request);
                auto response = future.get();
                
                EXPECT_EQ(response.ticks.size(), 3);
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    // 验证指标
    auto metrics = optimizer_->GetMetrics();
    EXPECT_EQ(metrics.total_queries.load(), num_threads * queries_per_thread);
    EXPECT_GT(optimizer_->GetCacheSize(), 0);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}