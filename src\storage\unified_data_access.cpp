#include "unified_data_access.h"
#include "query_performance_optimizer.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <algorithm>
#include <functional>
#include <iomanip>
#include <sstream>

namespace financial_data {

UnifiedDataAccessInterface::UnifiedDataAccessInterface(const StorageLayerConfig& config)
    : config_(config) {
    InitializeLogger();
}

UnifiedDataAccessInterface::~UnifiedDataAccessInterface() {
    Shutdown();
}

void UnifiedDataAccessInterface::InitializeLogger() {
    logger_ = spdlog::get("unified_data_access");
    if (!logger_) {
        logger_ = spdlog::stdout_color_mt("unified_data_access");
        logger_->set_level(spdlog::level::info);
    }
}

bool UnifiedDataAccessInterface::Initialize(
    std::shared_ptr<RedisHotStorage> hot_storage,
    std::shared_ptr<ClickHouseStorage> warm_storage,
    std::shared_ptr<storage::ColdDataStorage> cold_storage) {
    
    // 创建默认的存储层选择器
    StorageSelectionConfig selector_config;
    selector_config.hot_storage_days = config_.hot_storage_days;
    selector_config.warm_storage_days = config_.warm_storage_days;
    auto layer_selector = std::make_shared<StorageLayerSelector>(selector_config);
    
    return Initialize(hot_storage, warm_storage, cold_storage, layer_selector, nullptr);
}

bool UnifiedDataAccessInterface::Initialize(
    std::shared_ptr<RedisHotStorage> hot_storage,
    std::shared_ptr<ClickHouseStorage> warm_storage,
    std::shared_ptr<storage::ColdDataStorage> cold_storage,
    std::shared_ptr<StorageLayerSelector> layer_selector) {
    
    return Initialize(hot_storage, warm_storage, cold_storage, layer_selector, nullptr);
}

bool UnifiedDataAccessInterface::Initialize(
    std::shared_ptr<RedisHotStorage> hot_storage,
    std::shared_ptr<ClickHouseStorage> warm_storage,
    std::shared_ptr<storage::ColdDataStorage> cold_storage,
    std::shared_ptr<StorageLayerSelector> layer_selector,
    std::shared_ptr<QueryPerformanceOptimizer> query_optimizer) {
    
    if (initialized_.load()) {
        logger_->warn("UnifiedDataAccessInterface already initialized");
        return true;
    }
    
    // 验证存储层
    if (!hot_storage || !warm_storage || !cold_storage || !layer_selector) {
        logger_->error("Invalid storage layer references provided");
        return false;
    }
    
    hot_storage_ = hot_storage;
    warm_storage_ = warm_storage;
    cold_storage_ = cold_storage;
    layer_selector_ = layer_selector;
    query_optimizer_ = query_optimizer;
    
    // 初始化存储层选择器
    if (!layer_selector_->Initialize()) {
        logger_->error("Failed to initialize storage layer selector");
        return false;
    }
    
    // 初始化查询优化器（如果提供）
    if (query_optimizer_) {
        if (!query_optimizer_->Initialize(shared_from_this())) {
            logger_->error("Failed to initialize query performance optimizer");
            return false;
        }
    }
    
    // 注册健康检查回调
    layer_selector_->RegisterHealthCheckCallback(StorageLayer::HOT, 
        [this](StorageLayer layer) -> bool {
            try {
                auto stats = hot_storage_->GetStats();
                return stats.active_connections > 0;
            } catch (...) {
                return false;
            }
        });
    
    layer_selector_->RegisterHealthCheckCallback(StorageLayer::WARM,
        [this](StorageLayer layer) -> bool {
            try {
                return warm_storage_->IsConnected();
            } catch (...) {
                return false;
            }
        });
    
    layer_selector_->RegisterHealthCheckCallback(StorageLayer::COLD,
        [this](StorageLayer layer) -> bool {
            // 简单检查冷存储是否可用
            return cold_storage_ != nullptr;
        });
    
    // 启动健康检查线程
    health_check_thread_ = std::thread([this]() {
        while (!shutdown_requested_.load()) {
            PerformHealthCheck();
            std::this_thread::sleep_for(config_.health_check_interval);
        }
    });
    
    initialized_.store(true);
    logger_->info("UnifiedDataAccessInterface initialized successfully");
    return true;
}

void UnifiedDataAccessInterface::Shutdown() {
    if (!initialized_.load()) {
        return;
    }
    
    shutdown_requested_.store(true);
    
    // 等待健康检查线程结束
    if (health_check_thread_.joinable()) {
        health_check_thread_.join();
    }
    
    // 清理缓存
    ClearCache();
    
    // 关闭存储层选择器
    if (layer_selector_) {
        layer_selector_->Shutdown();
    }
    
    // 关闭查询优化器
    if (query_optimizer_) {
        query_optimizer_->Shutdown();
    }
    
    initialized_.store(false);
    logger_->info("UnifiedDataAccessInterface shutdown completed");
}

std::future<QueryResponse> UnifiedDataAccessInterface::QueryData(const QueryRequest& request) {
    auto promise = std::make_shared<std::promise<QueryResponse>>();
    auto future = promise->get_future();
    
    if (!initialized_.load()) {
        QueryResponse error_response;
        error_response.storage_source = "error";
        promise->set_value(std::move(error_response));
        return future;
    }
    
    // 检查并发限制
    if (active_queries_.load() >= config_.max_concurrent_queries) {
        logger_->warn("Maximum concurrent queries reached: {}", config_.max_concurrent_queries);
        QueryResponse error_response;
        error_response.storage_source = "error";
        promise->set_value(std::move(error_response));
        return future;
    }
    
    active_queries_++;
    metrics_.total_queries++;
    
    // 异步执行查询
    std::thread([this, request, promise]() {
        auto start_time = std::chrono::steady_clock::now();
        
        try {
            // 检查缓存
            if (config_.enable_query_cache) {
                std::string query_hash = GenerateQueryHash(request);
                QueryResponse cached_response;
                if (GetFromCache(query_hash, cached_response)) {
                    metrics_.cache_hits++;
                    promise->set_value(std::move(cached_response));
                    active_queries_--;
                    return;
                }
                metrics_.cache_misses++;
            }
            
            // 使用智能存储层选择器确定存储层
            std::vector<StorageLayer> layers = layer_selector_->SelectStorageLayers(
                request.start_timestamp_ns, request.end_timestamp_ns);
            
            QueryResponse response;
            if (layers.size() == 1) {
                // 单层查询
                auto layer_start_time = std::chrono::steady_clock::now();
                bool query_success = false;
                
                switch (layers[0]) {
                    case StorageLayer::HOT:
                        response = QueryFromHotStorage(request).get();
                        metrics_.hot_storage_queries++;
                        query_success = !response.IsEmpty() && response.storage_source != "hot_error";
                        break;
                    case StorageLayer::WARM:
                        response = QueryFromWarmStorage(request).get();
                        metrics_.warm_storage_queries++;
                        query_success = !response.IsEmpty() && response.storage_source != "warm_error";
                        break;
                    case StorageLayer::COLD:
                        response = QueryFromColdStorage(request).get();
                        metrics_.cold_storage_queries++;
                        query_success = !response.IsEmpty() && response.storage_source != "cold_error";
                        break;
                    default:
                        break;
                }
                
                // 报告查询指标给存储层选择器
                auto layer_end_time = std::chrono::steady_clock::now();
                auto layer_query_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                    layer_end_time - layer_start_time);
                layer_selector_->UpdateLayerMetrics(layers[0], query_success, 
                                                   layer_query_time.count());
                
                // 如果查询失败，报告故障
                if (!query_success) {
                    layer_selector_->ReportLayerFailure(layers[0], 
                        "Query failed: " + response.storage_source);
                }
            } else {
                // 跨层查询
                response = QueryFromMultipleLayers(request, layers).get();
                metrics_.mixed_storage_queries++;
            }
            
            // 计算查询时间
            auto end_time = std::chrono::steady_clock::now();
            response.query_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                end_time - start_time);
            
            // 更新平均查询时间
            double current_avg = metrics_.avg_query_time_ms.load();
            double new_avg = (current_avg * (metrics_.total_queries - 1) + 
                             response.query_time.count()) / metrics_.total_queries;
            metrics_.avg_query_time_ms.store(new_avg);
            
            // 缓存结果
            if (config_.enable_query_cache && !response.IsEmpty()) {
                std::string query_hash = GenerateQueryHash(request);
                PutToCache(query_hash, response);
            }
            
            promise->set_value(std::move(response));
            
        } catch (const std::exception& e) {
            logger_->error("Query failed: {}", e.what());
            QueryResponse error_response;
            error_response.storage_source = "error";
            promise->set_value(std::move(error_response));
        }
        
        active_queries_--;
    }).detach();
    
    return future;
}

std::future<StandardTick> UnifiedDataAccessInterface::GetLatestTick(
    const std::string& symbol, const std::string& exchange) {
    
    auto promise = std::make_shared<std::promise<StandardTick>>();
    auto future = promise->get_future();
    
    if (!initialized_.load()) {
        promise->set_value(StandardTick{});
        return future;
    }
    
    // 异步执行
    std::thread([this, symbol, exchange, promise]() {
        try {
            // 优先从热存储获取最新数据
            StandardTick tick;
            if (hot_storage_->GetLatestTick(symbol, tick)) {
                if (exchange.empty() || tick.exchange == exchange) {
                    promise->set_value(std::move(tick));
                    return;
                }
            }
            
            // 回退到温存储
            QueryRequest request;
            request.symbol = symbol;
            request.exchange = exchange;
            request.limit = 1;
            request.start_timestamp_ns = GetCurrentTimestampNs() - DaysToNanoseconds(7);
            request.end_timestamp_ns = GetCurrentTimestampNs();
            
            auto response = QueryFromWarmStorage(request).get();
            if (!response.ticks.empty()) {
                promise->set_value(std::move(response.ticks[0]));
                return;
            }
            
            // 返回空数据
            promise->set_value(StandardTick{});
            
        } catch (const std::exception& e) {
            logger_->error("GetLatestTick failed for {}: {}", symbol, e.what());
            promise->set_value(StandardTick{});
        }
    }).detach();
    
    return future;
}

std::future<Level2Data> UnifiedDataAccessInterface::GetLatestLevel2(
    const std::string& symbol, const std::string& exchange) {
    
    auto promise = std::make_shared<std::promise<Level2Data>>();
    auto future = promise->get_future();
    
    if (!initialized_.load()) {
        promise->set_value(Level2Data{});
        return future;
    }
    
    // 异步执行
    std::thread([this, symbol, exchange, promise]() {
        try {
            // 优先从热存储获取最新数据
            Level2Data level2;
            if (hot_storage_->GetLatestLevel2(symbol, level2)) {
                if (exchange.empty() || level2.exchange == exchange) {
                    promise->set_value(std::move(level2));
                    return;
                }
            }
            
            // 返回空数据
            promise->set_value(Level2Data{});
            
        } catch (const std::exception& e) {
            logger_->error("GetLatestLevel2 failed for {}: {}", symbol, e.what());
            promise->set_value(Level2Data{});
        }
    }).detach();
    
    return future;
}

std::future<QueryResponse> UnifiedDataAccessInterface::OptimizedQuery(const QueryRequest& request) {
    if (query_optimizer_) {
        return query_optimizer_->OptimizedQuery(request);
    } else {
        // 回退到普通查询
        return QueryData(request);
    }
}

std::future<std::vector<QueryResponse>> UnifiedDataAccessInterface::BatchQuery(
    const std::vector<QueryRequest>& requests) {
    
    if (query_optimizer_) {
        return query_optimizer_->BatchQuery(requests);
    } else {
        // 回退到逐个查询
        auto promise = std::make_shared<std::promise<std::vector<QueryResponse>>>();
        auto future = promise->get_future();
        
        std::thread([this, requests, promise]() {
            std::vector<QueryResponse> responses;
            responses.reserve(requests.size());
            
            for (const auto& request : requests) {
                auto query_future = QueryData(request);
                responses.push_back(query_future.get());
            }
            
            promise->set_value(std::move(responses));
        }).detach();
        
        return future;
    }
}

std::future<QueryResponse> UnifiedDataAccessInterface::PaginatedQuery(
    const QueryRequest& request, const std::string& cursor) {
    
    if (query_optimizer_) {
        return query_optimizer_->PaginatedQuery(request, cursor);
    } else {
        // 简单的分页实现
        auto promise = std::make_shared<std::promise<QueryResponse>>();
        auto future = promise->get_future();
        
        std::thread([this, request, cursor, promise]() {
            QueryRequest paginated_request = request;
            
            // 简单的游标解析（实际实现中应该更复杂）
            if (!cursor.empty()) {
                try {
                    size_t offset = std::stoull(cursor);
                    paginated_request.limit += static_cast<int>(offset);
                } catch (...) {
                    // 无效游标，使用原始请求
                }
            }
            
            auto query_future = QueryData(paginated_request);
            auto response = query_future.get();
            
            // 简单的分页处理
            if (!cursor.empty() && !response.ticks.empty()) {
                try {
                    size_t offset = std::stoull(cursor);
                    if (offset < response.ticks.size()) {
                        std::vector<StandardTick> page_ticks(
                            response.ticks.begin() + offset, response.ticks.end());
                        response.ticks = std::move(page_ticks);
                    }
                } catch (...) {
                    // 游标解析失败，返回所有数据
                }
            }
            
            promise->set_value(std::move(response));
        }).detach();
        
        return future;
    }
}

std::string UnifiedDataAccessInterface::CreatePaginationCursor(const QueryRequest& request) {
    if (query_optimizer_) {
        return query_optimizer_->CreatePaginationCursor(request);
    } else {
        // 简单的游标实现
        return "0";
    }
}

std::future<std::unordered_map<std::string, StandardTick>> 
UnifiedDataAccessInterface::GetLatestTicks(
    const std::vector<std::string>& symbols, const std::string& exchange) {
    
    auto promise = std::make_shared<std::promise<std::unordered_map<std::string, StandardTick>>>();
    auto future = promise->get_future();
    
    if (!initialized_.load()) {
        promise->set_value(std::unordered_map<std::string, StandardTick>{});
        return future;
    }
    
    // 异步执行
    std::thread([this, symbols, exchange, promise]() {
        try {
            auto result = hot_storage_->GetLatestTicks(symbols);
            
            // 过滤交易所
            if (!exchange.empty()) {
                auto it = result.begin();
                while (it != result.end()) {
                    if (it->second.exchange != exchange) {
                        it = result.erase(it);
                    } else {
                        ++it;
                    }
                }
            }
            
            promise->set_value(std::move(result));
            
        } catch (const std::exception& e) {
            logger_->error("GetLatestTicks failed: {}", e.what());
            promise->set_value(std::unordered_map<std::string, StandardTick>{});
        }
    }).detach();
    
    return future;
}

StorageLayer UnifiedDataAccessInterface::DetermineStorageLayer(int64_t timestamp_ns) const {
    int64_t current_time = GetCurrentTimestampNs();
    int64_t hot_threshold = current_time - DaysToNanoseconds(config_.hot_storage_days);
    int64_t warm_threshold = current_time - DaysToNanoseconds(config_.warm_storage_days);
    
    if (timestamp_ns >= hot_threshold) {
        return StorageLayer::HOT;
    } else if (timestamp_ns >= warm_threshold) {
        return StorageLayer::WARM;
    } else {
        return StorageLayer::COLD;
    }
}

std::vector<StorageLayer> UnifiedDataAccessInterface::DetermineStorageLayers(
    int64_t start_timestamp_ns, int64_t end_timestamp_ns) const {
    
    std::vector<StorageLayer> layers;
    
    StorageLayer start_layer = DetermineStorageLayer(start_timestamp_ns);
    StorageLayer end_layer = DetermineStorageLayer(end_timestamp_ns);
    
    if (start_layer == end_layer) {
        layers.push_back(start_layer);
    } else {
        // 跨层查询，需要查询多个存储层
        int start_level = static_cast<int>(start_layer);
        int end_level = static_cast<int>(end_layer);
        
        for (int level = std::min(start_level, end_level); 
             level <= std::max(start_level, end_level); ++level) {
            layers.push_back(static_cast<StorageLayer>(level));
        }
    }
    
    return layers;
}

std::future<QueryResponse> UnifiedDataAccessInterface::QueryFromHotStorage(
    const QueryRequest& request) {
    
    auto promise = std::make_shared<std::promise<QueryResponse>>();
    auto future = promise->get_future();
    
    std::thread([this, request, promise]() {
        try {
            QueryResponse response;
            response.storage_source = "hot";
            
            QueryOptions options;
            options.start_time_ns = request.start_timestamp_ns;
            options.end_time_ns = request.end_timestamp_ns;
            options.limit = request.limit;
            options.cursor = request.cursor;
            options.include_level2 = request.include_level2;
            
            auto result = hot_storage_->QueryTicks(request.symbol, options);
            response.ticks = std::move(result.ticks);
            response.next_cursor = result.next_cursor;
            response.has_more = result.has_more;
            response.total_records = response.ticks.size();
            
            if (request.include_level2) {
                auto level2_result = hot_storage_->QueryLevel2(request.symbol, options);
                response.level2_data = std::move(level2_result.level2_data);
                response.total_records += response.level2_data.size();
            }
            
            promise->set_value(std::move(response));
            
        } catch (const std::exception& e) {
            logger_->error("Hot storage query failed: {}", e.what());
            QueryResponse error_response;
            error_response.storage_source = "hot_error";
            promise->set_value(std::move(error_response));
        }
    }).detach();
    
    return future;
}

std::future<QueryResponse> UnifiedDataAccessInterface::QueryFromWarmStorage(
    const QueryRequest& request) {
    
    auto promise = std::make_shared<std::promise<QueryResponse>>();
    auto future = promise->get_future();
    
    std::thread([this, request, promise]() {
        try {
            QueryResponse response;
            response.storage_source = "warm";
            
            auto result = warm_storage_->QueryTickData(
                request.symbol, request.exchange,
                request.start_timestamp_ns, request.end_timestamp_ns,
                request.limit, request.cursor);
            
            // 转换数据格式
            response.ticks.reserve(result.data.size());
            for (const auto& standardized_tick : result.data) {
                StandardTick tick;
                tick.timestamp_ns = standardized_tick.timestamp_ns;
                tick.symbol = standardized_tick.symbol;
                tick.exchange = standardized_tick.exchange;
                tick.last_price = standardized_tick.last_price;
                tick.volume = standardized_tick.volume;
                tick.turnover = standardized_tick.turnover;
                tick.open_interest = standardized_tick.open_interest;
                tick.sequence = standardized_tick.sequence;
                tick.trade_flag = standardized_tick.trade_flag;
                
                // 转换买卖盘数据
                for (size_t i = 0; i < std::min(standardized_tick.bid_prices.size(), size_t(5)); ++i) {
                    tick.bids[i].price = standardized_tick.bid_prices[i];
                    tick.bids[i].volume = standardized_tick.bid_volumes[i];
                }
                for (size_t i = 0; i < std::min(standardized_tick.ask_prices.size(), size_t(5)); ++i) {
                    tick.asks[i].price = standardized_tick.ask_prices[i];
                    tick.asks[i].volume = standardized_tick.ask_volumes[i];
                }
                
                response.ticks.push_back(std::move(tick));
            }
            
            response.next_cursor = result.next_cursor;
            response.has_more = result.has_more;
            response.total_records = result.total_rows;
            
            promise->set_value(std::move(response));
            
        } catch (const std::exception& e) {
            logger_->error("Warm storage query failed: {}", e.what());
            QueryResponse error_response;
            error_response.storage_source = "warm_error";
            promise->set_value(std::move(error_response));
        }
    }).detach();
    
    return future;
}

std::future<QueryResponse> UnifiedDataAccessInterface::QueryFromColdStorage(
    const QueryRequest& request) {
    
    auto promise = std::make_shared<std::promise<QueryResponse>>();
    auto future = promise->get_future();
    
    std::thread([this, request, promise]() {
        try {
            QueryResponse response;
            response.storage_source = "cold";
            
            // 转换时间戳为时间点
            auto start_time = std::chrono::system_clock::time_point(
                std::chrono::nanoseconds(request.start_timestamp_ns));
            auto end_time = std::chrono::system_clock::time_point(
                std::chrono::nanoseconds(request.end_timestamp_ns));
            
            auto batch_future = cold_storage_->RetrieveData(
                request.symbol, request.exchange, start_time, end_time);
            auto batch = batch_future.get();
            
            // 转换数据格式
            response.ticks.reserve(batch.size());
            for (size_t i = 0; i < batch.size() && response.ticks.size() < request.limit; ++i) {
                StandardTick tick;
                tick.timestamp_ns = batch.timestamps[i];
                tick.symbol = batch.symbols[i];
                tick.exchange = batch.exchanges[i];
                tick.last_price = batch.last_prices[i];
                tick.volume = batch.volumes[i];
                tick.turnover = batch.turnovers[i];
                tick.open_interest = batch.open_interests[i];
                tick.sequence = batch.sequences[i];
                
                // 转换买卖盘数据
                if (i < batch.bid_prices.size()) {
                    for (size_t j = 0; j < std::min(batch.bid_prices[i].size(), size_t(5)); ++j) {
                        tick.bids[j].price = batch.bid_prices[i][j];
                        tick.bids[j].volume = batch.bid_volumes[i][j];
                    }
                }
                if (i < batch.ask_prices.size()) {
                    for (size_t j = 0; j < std::min(batch.ask_prices[i].size(), size_t(5)); ++j) {
                        tick.asks[j].price = batch.ask_prices[i][j];
                        tick.asks[j].volume = batch.ask_volumes[i][j];
                    }
                }
                
                response.ticks.push_back(std::move(tick));
            }
            
            response.total_records = batch.size();
            response.has_more = batch.size() > request.limit;
            
            promise->set_value(std::move(response));
            
        } catch (const std::exception& e) {
            logger_->error("Cold storage query failed: {}", e.what());
            QueryResponse error_response;
            error_response.storage_source = "cold_error";
            promise->set_value(std::move(error_response));
        }
    }).detach();
    
    return future;
}

std::future<QueryResponse> UnifiedDataAccessInterface::QueryFromMultipleLayers(
    const QueryRequest& request, const std::vector<StorageLayer>& layers) {
    
    auto promise = std::make_shared<std::promise<QueryResponse>>();
    auto future = promise->get_future();
    
    std::thread([this, request, layers, promise]() {
        try {
            std::vector<std::future<QueryResponse>> layer_futures;
            
            // 为每个存储层创建查询任务
            for (StorageLayer layer : layers) {
                QueryRequest layer_request = request;
                
                // 调整时间范围以适应存储层
                int64_t current_time = GetCurrentTimestampNs();
                switch (layer) {
                    case StorageLayer::HOT: {
                        int64_t hot_threshold = current_time - DaysToNanoseconds(config_.hot_storage_days);
                        layer_request.start_timestamp_ns = std::max(request.start_timestamp_ns, hot_threshold);
                        layer_request.end_timestamp_ns = request.end_timestamp_ns;
                        layer_futures.push_back(QueryFromHotStorage(layer_request));
                        break;
                    }
                    case StorageLayer::WARM: {
                        int64_t hot_threshold = current_time - DaysToNanoseconds(config_.hot_storage_days);
                        int64_t warm_threshold = current_time - DaysToNanoseconds(config_.warm_storage_days);
                        layer_request.start_timestamp_ns = std::max(request.start_timestamp_ns, warm_threshold);
                        layer_request.end_timestamp_ns = std::min(request.end_timestamp_ns, hot_threshold);
                        layer_futures.push_back(QueryFromWarmStorage(layer_request));
                        break;
                    }
                    case StorageLayer::COLD: {
                        int64_t warm_threshold = current_time - DaysToNanoseconds(config_.warm_storage_days);
                        layer_request.start_timestamp_ns = request.start_timestamp_ns;
                        layer_request.end_timestamp_ns = std::min(request.end_timestamp_ns, warm_threshold);
                        layer_futures.push_back(QueryFromColdStorage(layer_request));
                        break;
                    }
                    default:
                        break;
                }
            }
            
            // 等待所有查询完成
            std::vector<QueryResponse> responses;
            for (auto& future : layer_futures) {
                responses.push_back(future.get());
            }
            
            // 合并响应
            QueryResponse merged_response = MergeResponses(responses);
            merged_response.storage_source = "mixed";
            
            promise->set_value(std::move(merged_response));
            
        } catch (const std::exception& e) {
            logger_->error("Multi-layer query failed: {}", e.what());
            QueryResponse error_response;
            error_response.storage_source = "mixed_error";
            promise->set_value(std::move(error_response));
        }
    }).detach();
    
    return future;
}

QueryResponse UnifiedDataAccessInterface::MergeResponses(
    const std::vector<QueryResponse>& responses) {
    
    QueryResponse merged;
    
    // 合并所有tick数据
    for (const auto& response : responses) {
        merged.ticks.insert(merged.ticks.end(), 
                           response.ticks.begin(), response.ticks.end());
        merged.level2_data.insert(merged.level2_data.end(),
                                 response.level2_data.begin(), response.level2_data.end());
        
        // 合并统计信息
        if (response.storage_source == "hot") {
            merged.stats.hot_storage_hits += response.ticks.size() + response.level2_data.size();
            merged.stats.hot_query_time += response.query_time;
        } else if (response.storage_source == "warm") {
            merged.stats.warm_storage_hits += response.ticks.size() + response.level2_data.size();
            merged.stats.warm_query_time += response.query_time;
        } else if (response.storage_source == "cold") {
            merged.stats.cold_storage_hits += response.ticks.size() + response.level2_data.size();
            merged.stats.cold_query_time += response.query_time;
        }
    }
    
    // 按时间戳排序
    SortResponseByTimestamp(merged);
    
    merged.total_records = merged.ticks.size() + merged.level2_data.size();
    
    return merged;
}

void UnifiedDataAccessInterface::SortResponseByTimestamp(QueryResponse& response) {
    // 对tick数据按时间戳排序
    std::sort(response.ticks.begin(), response.ticks.end(),
              [](const StandardTick& a, const StandardTick& b) {
                  return a.timestamp_ns < b.timestamp_ns;
              });
    
    // 对level2数据按时间戳排序
    std::sort(response.level2_data.begin(), response.level2_data.end(),
              [](const Level2Data& a, const Level2Data& b) {
                  return a.timestamp_ns < b.timestamp_ns;
              });
}

std::string UnifiedDataAccessInterface::GenerateQueryHash(const QueryRequest& request) const {
    std::ostringstream oss;
    oss << request.symbol << "|" << request.exchange << "|" << request.data_type << "|"
        << request.start_timestamp_ns << "|" << request.end_timestamp_ns << "|"
        << request.limit << "|" << request.cursor << "|" << request.include_level2;
    
    // 简单哈希
    std::hash<std::string> hasher;
    return std::to_string(hasher(oss.str()));
}

bool UnifiedDataAccessInterface::GetFromCache(const std::string& query_hash, 
                                             QueryResponse& response) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    auto it = query_cache_.find(query_hash);
    if (it != query_cache_.end()) {
        if (!it->second.IsExpired(config_.cache_ttl)) {
            response = it->second.response;
            return true;
        } else {
            // 删除过期缓存
            query_cache_.erase(it);
        }
    }
    
    return false;
}

void UnifiedDataAccessInterface::PutToCache(const std::string& query_hash, 
                                           const QueryResponse& response) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    // 检查缓存大小限制
    if (query_cache_.size() >= config_.cache_size) {
        // 删除最旧的缓存项
        auto oldest_it = query_cache_.begin();
        for (auto it = query_cache_.begin(); it != query_cache_.end(); ++it) {
            if (it->second.timestamp < oldest_it->second.timestamp) {
                oldest_it = it;
            }
        }
        query_cache_.erase(oldest_it);
    }
    
    CacheItem item;
    item.response = response;
    item.timestamp = std::chrono::steady_clock::now();
    item.query_hash = query_hash;
    
    query_cache_[query_hash] = std::move(item);
}

void UnifiedDataAccessInterface::PerformHealthCheck() {
    std::lock_guard<std::mutex> lock(health_mutex_);
    
    health_status_.last_check = std::chrono::system_clock::now();
    
    // 检查热存储
    try {
        auto stats = hot_storage_->GetStats();
        health_status_.hot_storage_healthy = (stats.active_connections > 0);
        health_status_.hot_stats.total_queries = stats.total_queries;
        health_status_.hot_stats.avg_response_time = 
            std::chrono::milliseconds(static_cast<int>(stats.avg_query_latency_us / 1000));
    } catch (const std::exception& e) {
        health_status_.hot_storage_healthy = false;
        logger_->warn("Hot storage health check failed: {}", e.what());
    }
    
    // 检查温存储
    try {
        health_status_.warm_storage_healthy = warm_storage_->IsConnected();
        auto metrics = warm_storage_->GetMetrics();
        health_status_.warm_stats.total_queries = metrics.total_queries;
        health_status_.warm_stats.failed_queries = metrics.failed_queries;
        health_status_.warm_stats.avg_response_time = metrics.avg_query_time;
        if (metrics.total_queries > 0) {
            health_status_.warm_stats.success_rate = 
                1.0 - (double(metrics.failed_queries) / metrics.total_queries);
        }
    } catch (const std::exception& e) {
        health_status_.warm_storage_healthy = false;
        logger_->warn("Warm storage health check failed: {}", e.what());
    }
    
    // 检查冷存储（简单检查）
    health_status_.cold_storage_healthy = (cold_storage_ != nullptr);
    
    // 更新整体健康状态
    health_status_.overall_healthy = 
        health_status_.hot_storage_healthy && 
        health_status_.warm_storage_healthy && 
        health_status_.cold_storage_healthy;
    
    if (!health_status_.overall_healthy) {
        health_status_.error_message = "One or more storage layers are unhealthy";
    } else {
        health_status_.error_message.clear();
    }
}

UnifiedDataAccessInterface::HealthStatus UnifiedDataAccessInterface::GetHealthStatus() const {
    std::lock_guard<std::mutex> lock(health_mutex_);
    return health_status_;
}

UnifiedDataAccessInterface::PerformanceMetrics UnifiedDataAccessInterface::GetMetrics() const {
    return metrics_;
}

void UnifiedDataAccessInterface::ResetMetrics() {
    metrics_.Reset();
}

void UnifiedDataAccessInterface::ClearCache() {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    query_cache_.clear();
}

size_t UnifiedDataAccessInterface::GetCacheSize() const {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    return query_cache_.size();
}

double UnifiedDataAccessInterface::GetCacheHitRate() const {
    uint64_t hits = metrics_.cache_hits.load();
    uint64_t misses = metrics_.cache_misses.load();
    uint64_t total = hits + misses;
    
    return total > 0 ? (double(hits) / total) : 0.0;
}

int64_t UnifiedDataAccessInterface::GetCurrentTimestampNs() const {
    auto now = std::chrono::high_resolution_clock::now();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(
        now.time_since_epoch()).count();
}

int64_t UnifiedDataAccessInterface::DaysToNanoseconds(int days) const {
    return static_cast<int64_t>(days) * 24 * 3600 * 1000000000LL;
}

// 工厂方法实现
std::unique_ptr<UnifiedDataAccessInterface> UnifiedDataAccessFactory::CreateDefault() {
    StorageLayerConfig config;
    return std::make_unique<UnifiedDataAccessInterface>(config);
}

std::unique_ptr<UnifiedDataAccessInterface> UnifiedDataAccessFactory::CreateHighPerformance() {
    StorageLayerConfig config;
    config.enable_query_cache = true;
    config.cache_size = 50000;
    config.cache_ttl = std::chrono::minutes(60);
    config.max_concurrent_queries = 200;
    return std::make_unique<UnifiedDataAccessInterface>(config);
}

std::unique_ptr<UnifiedDataAccessInterface> UnifiedDataAccessFactory::CreateLowLatency() {
    StorageLayerConfig config;
    config.enable_query_cache = true;
    config.cache_size = 100000;
    config.cache_ttl = std::chrono::minutes(15);
    config.max_concurrent_queries = 500;
    config.query_timeout = std::chrono::seconds(5);
    return std::make_unique<UnifiedDataAccessInterface>(config);
}

} // namespace financial_data