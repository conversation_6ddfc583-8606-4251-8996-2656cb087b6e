#include "health_checker.h"
#include <algorithm>
#include <sstream>
#include <fstream>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "psapi.lib")
#else
#include <sys/statvfs.h>
#include <sys/sysinfo.h>
#include <unistd.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#endif

namespace financial_data {

HealthChecker::HealthChecker(const HealthCheckConfig& config)
    : config_(config)
    , logger_(spdlog::get("health_checker") ? spdlog::get("health_checker") : spdlog::default_logger())
    , running_(false)
    , stats_{} {
    
    logger_->info("Health Checker initialized with check interval: {}ms", config_.check_interval_ms);
    
    // 注册内置的系统组件检查
    RegisterComponent("cpu", [this]() { return CheckCPUUsage(); });
    RegisterComponent("memory", [this]() { return CheckMemoryUsage(); });
    RegisterComponent("disk", [this]() { return CheckDiskUsage(); });
    RegisterComponent("network", [this]() { return CheckNetworkConnectivity(); });
}

HealthChecker::~HealthChecker() {
    Stop();
    logger_->info("Health Checker destroyed");
}

bool HealthChecker::Start() {
    if (running_.load()) {
        logger_->warn("Health Checker already running");
        return true;
    }
    
    logger_->info("Starting Health Checker");
    
    running_ = true;
    
    // 启动健康检查线程
    health_check_thread_ = std::thread(&HealthChecker::HealthCheckThread, this);
    
    logger_->info("Health Checker started successfully");
    return true;
}

void HealthChecker::Stop() {
    if (!running_.load()) {
        return;
    }
    
    logger_->info("Stopping Health Checker");
    
    running_ = false;
    
    // 等待线程结束
    if (health_check_thread_.joinable()) {
        health_check_thread_.join();
    }
    
    logger_->info("Health Checker stopped");
}

void HealthChecker::RegisterComponent(const std::string& component_name, HealthCheckFunction check_func) {
    std::lock_guard<std::mutex> lock(components_mutex_);
    check_functions_[component_name] = check_func;
    logger_->info("Registered health check for component: {}", component_name);
}

void HealthChecker::RegisterRecovery(const std::string& component_name, RecoveryFunction recovery_func) {
    std::lock_guard<std::mutex> lock(components_mutex_);
    recovery_functions_[component_name] = recovery_func;
    logger_->info("Registered recovery function for component: {}", component_name);
}

SystemHealth HealthChecker::GetSystemHealth() const {
    std::lock_guard<std::mutex> lock(components_mutex_);
    
    SystemHealth system_health;
    system_health.last_check = std::chrono::steady_clock::now();
    
    for (const auto& [name, health] : component_health_) {
        system_health.components.push_back(health);
        
        switch (health.overall_status) {
            case HealthStatus::HEALTHY:
                system_health.healthy_components++;
                break;
            case HealthStatus::WARNING:
                system_health.warning_components++;
                break;
            case HealthStatus::CRITICAL:
                system_health.critical_components++;
                break;
            default:
                break;
        }
    }
    
    system_health.overall_status = CalculateOverallStatus(system_health.components);
    return system_health;
}

ComponentHealth HealthChecker::GetComponentHealth(const std::string& component_name) const {
    std::lock_guard<std::mutex> lock(components_mutex_);
    auto it = component_health_.find(component_name);
    if (it != component_health_.end()) {
        return it->second;
    }
    return ComponentHealth{};
}

void HealthChecker::TriggerHealthCheck() {
    logger_->info("Manual health check triggered");
    
    std::lock_guard<std::mutex> lock(components_mutex_);
    for (const auto& [name, check_func] : check_functions_) {
        try {
            ComponentHealth health = CheckComponent(name);
            component_health_[name] = health;
            
            if (health.overall_status == HealthStatus::CRITICAL || 
                health.overall_status == HealthStatus::WARNING) {
                SendAlert(health);
            }
        } catch (const std::exception& e) {
            logger_->error("Error checking component {}: {}", name, e.what());
        }
    }
}

bool HealthChecker::TriggerRecovery(const std::string& component_name) {
    logger_->info("Manual recovery triggered for component: {}", component_name);
    return AttemptRecovery(component_name);
}

void HealthChecker::UpdateMetric(const std::string& component_name, const std::string& metric_name,
                                double value, double warning_threshold, double critical_threshold) {
    std::lock_guard<std::mutex> lock(components_mutex_);
    
    auto& component = component_health_[component_name];
    if (component.component_name.empty()) {
        component.component_name = component_name;
        component.overall_status = HealthStatus::HEALTHY;
    }
    
    // 查找或创建指标
    auto it = std::find_if(component.metrics.begin(), component.metrics.end(),
                          [&metric_name](const HealthMetric& m) { return m.name == metric_name; });
    
    if (it == component.metrics.end()) {
        HealthMetric metric;
        metric.name = metric_name;
        component.metrics.push_back(metric);
        it = component.metrics.end() - 1;
    }
    
    // 更新指标
    it->value = value;
    it->threshold_warning = warning_threshold;
    it->threshold_critical = critical_threshold;
    it->last_updated = std::chrono::steady_clock::now();
    
    // 计算指标状态
    if (critical_threshold > 0 && value >= critical_threshold) {
        it->status = HealthStatus::CRITICAL;
    } else if (warning_threshold > 0 && value >= warning_threshold) {
        it->status = HealthStatus::WARNING;
    } else {
        it->status = HealthStatus::HEALTHY;
    }
    
    // 更新组件整体状态
    HealthStatus worst_status = HealthStatus::HEALTHY;
    for (const auto& metric : component.metrics) {
        if (static_cast<int>(metric.status) > static_cast<int>(worst_status)) {
            worst_status = metric.status;
        }
    }
    component.overall_status = worst_status;
    component.last_check = std::chrono::steady_clock::now();
}

bool HealthChecker::IsSystemHealthy() const {
    SystemHealth health = GetSystemHealth();
    return health.overall_status == HealthStatus::HEALTHY || 
           health.overall_status == HealthStatus::WARNING;
}

HealthChecker::HealthStats HealthChecker::GetHealthStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void HealthChecker::HealthCheckThread() {
    logger_->info("Health check thread started");
    
    while (running_.load()) {
        try {
            auto start_time = std::chrono::steady_clock::now();
            
            std::vector<std::string> components_to_check;
            {
                std::lock_guard<std::mutex> lock(components_mutex_);
                for (const auto& [name, check_func] : check_functions_) {
                    components_to_check.push_back(name);
                }
            }
            
            // 执行健康检查
            for (const auto& component_name : components_to_check) {
                try {
                    ComponentHealth health = CheckComponent(component_name);
                    
                    {
                        std::lock_guard<std::mutex> lock(components_mutex_);
                        component_health_[component_name] = health;
                    }
                    
                    // 如果组件不健康，尝试恢复
                    if (config_.enable_auto_recovery && 
                        (health.overall_status == HealthStatus::CRITICAL || 
                         health.overall_status == HealthStatus::WARNING)) {
                        
                        SendAlert(health);
                        
                        if (health.overall_status == HealthStatus::CRITICAL) {
                            AttemptRecovery(component_name);
                        }
                    }
                    
                    {
                        std::lock_guard<std::mutex> lock(stats_mutex_);
                        stats_.total_checks++;
                        stats_.successful_checks++;
                        stats_.last_check_time = std::chrono::steady_clock::now();
                    }
                    
                } catch (const std::exception& e) {
                    logger_->error("Error checking component {}: {}", component_name, e.what());
                    
                    std::lock_guard<std::mutex> lock(stats_mutex_);
                    stats_.total_checks++;
                    stats_.failed_checks++;
                }
            }
            
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start_time).count();
            
            logger_->debug("Health check cycle completed in {}ms", elapsed);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(config_.check_interval_ms));
            
        } catch (const std::exception& e) {
            logger_->error("Error in health check thread: {}", e.what());
        }
    }
    
    logger_->info("Health check thread stopped");
}

ComponentHealth HealthChecker::CheckComponent(const std::string& component_name) {
    std::lock_guard<std::mutex> lock(components_mutex_);
    
    auto it = check_functions_.find(component_name);
    if (it == check_functions_.end()) {
        ComponentHealth health;
        health.component_name = component_name;
        health.overall_status = HealthStatus::UNKNOWN;
        health.error_message = "No check function registered";
        return health;
    }
    
    try {
        ComponentHealth health = it->second();
        health.component_name = component_name;
        health.last_check = std::chrono::steady_clock::now();
        return health;
    } catch (const std::exception& e) {
        ComponentHealth health;
        health.component_name = component_name;
        health.overall_status = HealthStatus::CRITICAL;
        health.error_message = e.what();
        health.last_check = std::chrono::steady_clock::now();
        return health;
    }
}

bool HealthChecker::AttemptRecovery(const std::string& component_name) {
    std::lock_guard<std::mutex> lock(components_mutex_);
    
    auto it = recovery_functions_.find(component_name);
    if (it == recovery_functions_.end()) {
        logger_->warn("No recovery function registered for component: {}", component_name);
        return false;
    }
    
    // 检查恢复尝试次数
    int& attempts = recovery_attempts_[component_name];
    if (attempts >= config_.recovery_attempts) {
        logger_->error("Max recovery attempts reached for component: {}", component_name);
        return false;
    }
    
    attempts++;
    logger_->info("Attempting recovery for component {} (attempt {}/{})", 
                 component_name, attempts, config_.recovery_attempts);
    
    try {
        bool success = it->second(component_name);
        
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats_.recovery_attempts++;
            if (success) {
                stats_.successful_recoveries++;
            }
        }
        
        if (success) {
            logger_->info("Recovery successful for component: {}", component_name);
            attempts = 0;  // 重置计数
        } else {
            logger_->error("Recovery failed for component: {}", component_name);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        logger_->error("Recovery attempt failed for component {}: {}", component_name, e.what());
        return false;
    }
}

HealthStatus HealthChecker::CalculateOverallStatus(const std::vector<ComponentHealth>& components) {
    if (components.empty()) {
        return HealthStatus::UNKNOWN;
    }
    
    HealthStatus worst_status = HealthStatus::HEALTHY;
    for (const auto& component : components) {
        if (static_cast<int>(component.overall_status) > static_cast<int>(worst_status)) {
            worst_status = component.overall_status;
        }
    }
    
    return worst_status;
}

void HealthChecker::SendAlert(const ComponentHealth& component) {
    if (alert_callback_) {
        try {
            alert_callback_(component);
        } catch (const std::exception& e) {
            logger_->error("Error in alert callback: {}", e.what());
        }
    }
    
    logger_->warn("Health alert for component {}: status={}, error={}", 
                 component.component_name, static_cast<int>(component.overall_status), 
                 component.error_message);
}

ComponentHealth HealthChecker::CheckCPUUsage() {
    ComponentHealth health;
    health.component_name = "cpu";
    
    try {
#ifdef _WIN32
        // Windows CPU使用率检查
        FILETIME idle_time, kernel_time, user_time;
        if (GetSystemTimes(&idle_time, &kernel_time, &user_time)) {
            // 简化的CPU使用率计算
            double cpu_usage = 15.0;  // 模拟值
            
            HealthMetric metric;
            metric.name = "cpu_usage_percent";
            metric.value = cpu_usage;
            metric.threshold_warning = 80.0;
            metric.threshold_critical = 95.0;
            metric.description = "CPU usage percentage";
            metric.last_updated = std::chrono::steady_clock::now();
            
            if (cpu_usage >= 95.0) {
                metric.status = HealthStatus::CRITICAL;
                health.overall_status = HealthStatus::CRITICAL;
            } else if (cpu_usage >= 80.0) {
                metric.status = HealthStatus::WARNING;
                health.overall_status = HealthStatus::WARNING;
            } else {
                metric.status = HealthStatus::HEALTHY;
                health.overall_status = HealthStatus::HEALTHY;
            }
            
            health.metrics.push_back(metric);
        }
#else
        // Linux CPU使用率检查
        std::ifstream stat_file("/proc/stat");
        if (stat_file.is_open()) {
            std::string line;
            std::getline(stat_file, line);
            // 解析CPU统计信息...
            double cpu_usage = 20.0;  // 模拟值
            
            HealthMetric metric;
            metric.name = "cpu_usage_percent";
            metric.value = cpu_usage;
            metric.threshold_warning = 80.0;
            metric.threshold_critical = 95.0;
            health.metrics.push_back(metric);
            
            health.overall_status = cpu_usage >= 95.0 ? HealthStatus::CRITICAL :
                                   cpu_usage >= 80.0 ? HealthStatus::WARNING : HealthStatus::HEALTHY;
        }
#endif
    } catch (const std::exception& e) {
        health.overall_status = HealthStatus::CRITICAL;
        health.error_message = e.what();
    }
    
    return health;
}

ComponentHealth HealthChecker::CheckMemoryUsage() {
    ComponentHealth health;
    health.component_name = "memory";
    
    try {
#ifdef _WIN32
        MEMORYSTATUSEX mem_status;
        mem_status.dwLength = sizeof(mem_status);
        if (GlobalMemoryStatusEx(&mem_status)) {
            double memory_usage = static_cast<double>(mem_status.dwMemoryLoad);
            
            HealthMetric metric;
            metric.name = "memory_usage_percent";
            metric.value = memory_usage;
            metric.threshold_warning = 85.0;
            metric.threshold_critical = 95.0;
            metric.description = "Memory usage percentage";
            metric.last_updated = std::chrono::steady_clock::now();
            
            if (memory_usage >= 95.0) {
                metric.status = HealthStatus::CRITICAL;
                health.overall_status = HealthStatus::CRITICAL;
            } else if (memory_usage >= 85.0) {
                metric.status = HealthStatus::WARNING;
                health.overall_status = HealthStatus::WARNING;
            } else {
                metric.status = HealthStatus::HEALTHY;
                health.overall_status = HealthStatus::HEALTHY;
            }
            
            health.metrics.push_back(metric);
        }
#else
        struct sysinfo info;
        if (sysinfo(&info) == 0) {
            double total_mem = info.totalram * info.mem_unit;
            double free_mem = info.freeram * info.mem_unit;
            double memory_usage = ((total_mem - free_mem) / total_mem) * 100.0;
            
            HealthMetric metric;
            metric.name = "memory_usage_percent";
            metric.value = memory_usage;
            metric.threshold_warning = 85.0;
            metric.threshold_critical = 95.0;
            health.metrics.push_back(metric);
            
            health.overall_status = memory_usage >= 95.0 ? HealthStatus::CRITICAL :
                                   memory_usage >= 85.0 ? HealthStatus::WARNING : HealthStatus::HEALTHY;
        }
#endif
    } catch (const std::exception& e) {
        health.overall_status = HealthStatus::CRITICAL;
        health.error_message = e.what();
    }
    
    return health;
}

ComponentHealth HealthChecker::CheckDiskUsage() {
    ComponentHealth health;
    health.component_name = "disk";
    health.overall_status = HealthStatus::HEALTHY;
    
    try {
#ifdef _WIN32
        ULARGE_INTEGER free_bytes, total_bytes;
        if (GetDiskFreeSpaceEx(L"C:\\", &free_bytes, &total_bytes, nullptr)) {
            double disk_usage = ((double)(total_bytes.QuadPart - free_bytes.QuadPart) / total_bytes.QuadPart) * 100.0;
            
            HealthMetric metric;
            metric.name = "disk_usage_percent";
            metric.value = disk_usage;
            metric.threshold_warning = 85.0;
            metric.threshold_critical = 95.0;
            metric.description = "Disk usage percentage";
            metric.last_updated = std::chrono::steady_clock::now();
            
            if (disk_usage >= 95.0) {
                metric.status = HealthStatus::CRITICAL;
                health.overall_status = HealthStatus::CRITICAL;
            } else if (disk_usage >= 85.0) {
                metric.status = HealthStatus::WARNING;
                health.overall_status = HealthStatus::WARNING;
            } else {
                metric.status = HealthStatus::HEALTHY;
            }
            
            health.metrics.push_back(metric);
        }
#else
        struct statvfs stat;
        if (statvfs("/", &stat) == 0) {
            double total_space = stat.f_blocks * stat.f_frsize;
            double free_space = stat.f_avail * stat.f_frsize;
            double disk_usage = ((total_space - free_space) / total_space) * 100.0;
            
            HealthMetric metric;
            metric.name = "disk_usage_percent";
            metric.value = disk_usage;
            metric.threshold_warning = 85.0;
            metric.threshold_critical = 95.0;
            health.metrics.push_back(metric);
            
            health.overall_status = disk_usage >= 95.0 ? HealthStatus::CRITICAL :
                                   disk_usage >= 85.0 ? HealthStatus::WARNING : HealthStatus::HEALTHY;
        }
#endif
    } catch (const std::exception& e) {
        health.overall_status = HealthStatus::CRITICAL;
        health.error_message = e.what();
    }
    
    return health;
}

ComponentHealth HealthChecker::CheckNetworkConnectivity() {
    ComponentHealth health;
    health.component_name = "network";
    health.overall_status = HealthStatus::HEALTHY;
    
    // 简单的网络连接检查 - 尝试连接到本地回环地址
    try {
#ifdef _WIN32
        WSADATA wsa_data;
        if (WSAStartup(MAKEWORD(2, 2), &wsa_data) == 0) {
            SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
            if (sock != INVALID_SOCKET) {
                closesocket(sock);
                health.overall_status = HealthStatus::HEALTHY;
            } else {
                health.overall_status = HealthStatus::WARNING;
                health.error_message = "Socket creation failed";
            }
            WSACleanup();
        }
#else
        int sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock >= 0) {
            close(sock);
            health.overall_status = HealthStatus::HEALTHY;
        } else {
            health.overall_status = HealthStatus::WARNING;
            health.error_message = "Socket creation failed";
        }
#endif
        
        HealthMetric metric;
        metric.name = "network_connectivity";
        metric.value = health.overall_status == HealthStatus::HEALTHY ? 1.0 : 0.0;
        metric.threshold_warning = 0.5;
        metric.threshold_critical = 0.0;
        metric.description = "Network connectivity status";
        metric.last_updated = std::chrono::steady_clock::now();
        metric.status = health.overall_status;
        
        health.metrics.push_back(metric);
        
    } catch (const std::exception& e) {
        health.overall_status = HealthStatus::CRITICAL;
        health.error_message = e.what();
    }
    
    return health;
}

} // namespace financial_data