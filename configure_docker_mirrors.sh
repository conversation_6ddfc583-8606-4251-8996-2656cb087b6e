#!/bin/bash
# Docker镜像源配置脚本
# 配置国内Docker Hub镜像加速器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 国内Docker镜像源列表
DOCKER_MIRRORS=(
    "https://docker.1ms.run"
    "https://docker.xuanyuan.me"
    "https://docker.1panel.live"
    "https://dockerproxy.net"
    "https://cr.laoyou.ip-ddns.com"
    "https://docker.kejilion.pro"
    "https://docker.m.daocloud.io"
)

print_header() {
    echo "========================================"
    echo "    Docker镜像源配置工具"
    echo "========================================"
    echo
}

# 检查Docker是否安装
check_docker() {
    print_info "检查Docker安装状态..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，正在安装..."
        install_docker
    else
        print_success "Docker已安装"
        docker --version
    fi
    
    # 检查Docker服务状态
    if ! sudo systemctl is-active --quiet docker; then
        print_info "启动Docker服务..."
        sudo systemctl start docker
        sudo systemctl enable docker
    fi
    
    print_success "Docker服务运行正常"
}

# 安装Docker
install_docker() {
    print_info "安装Docker..."
    
    # 更新包索引
    sudo apt-get update
    
    # 安装必要的包
    sudo apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo \
      "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
      $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 安装Docker Engine
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    print_success "Docker安装完成"
    print_warning "请重新登录以使docker组权限生效"
}

# 备份现有配置
backup_config() {
    print_info "备份现有Docker配置..."
    
    if [[ -f /etc/docker/daemon.json ]]; then
        sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
        print_success "已备份现有配置"
    else
        print_info "未找到现有配置文件"
    fi
}

# 配置Docker镜像源
configure_mirrors() {
    print_info "配置Docker镜像源..."
    
    # 创建Docker配置目录
    sudo mkdir -p /etc/docker
    
    # 生成镜像源配置
    cat > /tmp/daemon.json << EOF
{
  "registry-mirrors": [
$(printf '    "%s",\n' "${DOCKER_MIRRORS[@]}" | sed '$ s/,$//')
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "exec-opts": ["native.cgroupdriver=systemd"],
  "live-restore": true
}
EOF
    
    # 应用配置
    sudo mv /tmp/daemon.json /etc/docker/daemon.json
    
    print_success "镜像源配置完成"
    
    # 显示配置内容
    print_info "当前配置:"
    sudo cat /etc/docker/daemon.json | jq . 2>/dev/null || sudo cat /etc/docker/daemon.json
}

# 重启Docker服务
restart_docker() {
    print_info "重启Docker服务..."
    
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    # 等待服务启动
    sleep 3
    
    if sudo systemctl is-active --quiet docker; then
        print_success "Docker服务重启成功"
    else
        print_error "Docker服务重启失败"
        return 1
    fi
}

# 测试镜像源可用性
test_mirrors() {
    print_info "测试镜像源可用性..."
    echo
    
    # 测试镜像列表
    TEST_IMAGES=(
        "hello-world:latest"
        "alpine:latest"
        "redis:7-alpine"
    )
    
    for image in "${TEST_IMAGES[@]}"; do
        print_info "测试拉取镜像: $image"
        
        # 清理可能存在的镜像
        docker rmi "$image" 2>/dev/null || true
        
        # 测试拉取
        start_time=$(date +%s)
        if timeout 60 docker pull "$image" >/dev/null 2>&1; then
            end_time=$(date +%s)
            duration=$((end_time - start_time))
            print_success "✅ $image 拉取成功 (${duration}秒)"
        else
            print_error "❌ $image 拉取失败"
        fi
        
        echo
    done
}

# 测试单个镜像源
test_single_mirror() {
    local mirror_url="$1"
    print_info "测试镜像源: $mirror_url"
    
    # 临时配置单个镜像源
    cat > /tmp/test_daemon.json << EOF
{
  "registry-mirrors": ["$mirror_url"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF
    
    # 备份当前配置
    sudo cp /etc/docker/daemon.json /tmp/daemon.json.temp
    
    # 应用测试配置
    sudo mv /tmp/test_daemon.json /etc/docker/daemon.json
    sudo systemctl restart docker
    sleep 2
    
    # 测试拉取
    start_time=$(date +%s)
    if timeout 30 docker pull hello-world:latest >/dev/null 2>&1; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        print_success "✅ $mirror_url 可用 (${duration}秒)"
        echo "$mirror_url,可用,${duration}秒" >> /tmp/mirror_test_results.csv
    else
        print_error "❌ $mirror_url 不可用"
        echo "$mirror_url,不可用,超时" >> /tmp/mirror_test_results.csv
    fi
    
    # 恢复原配置
    sudo mv /tmp/daemon.json.temp /etc/docker/daemon.json
    docker rmi hello-world:latest 2>/dev/null || true
}

# 测试所有镜像源
test_all_mirrors() {
    print_info "逐个测试所有镜像源..."
    echo
    
    # 创建结果文件
    echo "镜像源,状态,响应时间" > /tmp/mirror_test_results.csv
    
    for mirror in "${DOCKER_MIRRORS[@]}"; do
        test_single_mirror "$mirror"
        sleep 1
    done
    
    # 恢复多镜像源配置
    configure_mirrors
    restart_docker
    
    echo
    print_info "测试结果汇总:"
    echo "----------------------------------------"
    cat /tmp/mirror_test_results.csv | column -t -s ','
    echo "----------------------------------------"
    
    # 统计可用镜像源
    available_count=$(grep "可用" /tmp/mirror_test_results.csv | wc -l)
    total_count=${#DOCKER_MIRRORS[@]}
    
    print_info "可用镜像源: $available_count/$total_count"
}

# 显示Docker信息
show_docker_info() {
    print_info "Docker系统信息:"
    echo
    
    # Docker版本
    docker --version
    
    # Docker信息
    echo "Registry Mirrors:"
    docker info 2>/dev/null | grep -A 10 "Registry Mirrors:" || echo "未配置镜像源"
    
    echo
    echo "Storage Driver:"
    docker info 2>/dev/null | grep "Storage Driver:" || echo "未知"
    
    echo
    echo "Docker Root Dir:"
    docker info 2>/dev/null | grep "Docker Root Dir:" || echo "未知"
}

# 清理测试镜像
cleanup_test_images() {
    print_info "清理测试镜像..."
    
    TEST_IMAGES=(
        "hello-world:latest"
        "alpine:latest"
        "redis:7-alpine"
    )
    
    for image in "${TEST_IMAGES[@]}"; do
        docker rmi "$image" 2>/dev/null || true
    done
    
    print_success "测试镜像清理完成"
}

# 主函数
main() {
    print_header
    
    case "${1:-configure}" in
        configure)
            check_docker
            backup_config
            configure_mirrors
            restart_docker
            test_mirrors
            show_docker_info
            cleanup_test_images
            ;;
        test)
            test_all_mirrors
            ;;
        info)
            show_docker_info
            ;;
        cleanup)
            cleanup_test_images
            ;;
        install)
            install_docker
            ;;
        *)
            echo "用法: $0 [configure|test|info|cleanup|install]"
            echo "  configure  - 配置镜像源并测试 (默认)"
            echo "  test       - 测试所有镜像源"
            echo "  info       - 显示Docker信息"
            echo "  cleanup    - 清理测试镜像"
            echo "  install    - 安装Docker"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"