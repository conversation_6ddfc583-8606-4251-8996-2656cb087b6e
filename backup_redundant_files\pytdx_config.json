{"collection": {"symbol_types": ["stock", "index", "futures", "fund", "bond"], "data_types": ["stock", "index"], "periods": ["daily", "60min", "30min", "15min", "5min"], "days_back": 30, "max_symbols_per_type": null, "request_interval": 0.05, "batch_size": 100}, "servers": {"hq_servers": [{"ip": "**************", "port": 7709, "name": "通达信主站"}, {"ip": "*************", "port": 7709, "name": "通达信备用1"}, {"ip": "*************", "port": 7709, "name": "通达信备用2"}, {"ip": "**************", "port": 7709, "name": "通达信备用3"}, {"ip": "*************", "port": 7709, "name": "通达信备用4"}], "exhq_servers": [{"ip": "*************", "port": 7722, "name": "扩展行情1"}, {"ip": "**********", "port": 7722, "name": "扩展行情2"}, {"ip": "**************", "port": 7722, "name": "扩展行情3"}, {"ip": "************", "port": 7722, "name": "扩展行情4"}]}, "storage": {"save_to_files": true, "save_to_database": true, "output_directory": "data/historical", "file_formats": ["json", "csv", "parquet"]}, "quality_control": {"enable_validation": true, "enable_deduplication": true, "min_price": 0.01, "max_price_change_ratio": 0.5, "min_volume": 0, "remove_invalid_data": true}, "logging": {"level": "INFO", "file": "logs/pytdx_collection.log", "console": true, "max_size": "100MB", "backup_count": 5}, "performance": {"max_concurrent_requests": 5, "connection_timeout": 30, "max_retries": 3, "retry_delay": 5, "heartbeat_interval": 60}}