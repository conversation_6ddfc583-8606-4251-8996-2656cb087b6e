#include "ThostFtdcMdApi.h"
#include <memory>
#include <thread>
#include <chrono>
#include <random>
#include <cstring>
#include <iostream>

// CTP API 模拟实现
// 实际使用时应该链接官方提供的库文件

class CThostFtdcMdApiImpl : public CThostFtdcMdApi {
private:
    CThostFtdcMdSpi* spi_;
    std::thread worker_thread_;
    bool running_;
    bool connected_;
    bool logged_in_;
    std::string trading_day_;
    std::vector<std::string> subscribed_instruments_;
    
public:
    CThostFtdcMdApiImpl() 
        : spi_(nullptr), running_(false), connected_(false), logged_in_(false) {
        // 设置交易日
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        char buffer[32];
        std::strftime(buffer, sizeof(buffer), "%Y%m%d", &tm);
        trading_day_ = buffer;
    }
    
    ~CThostFtdcMdApiImpl() {
        Release();
    }
    
    void Release() override {
        running_ = false;
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        connected_ = false;
        logged_in_ = false;
    }
    
    void Init() override {
        if (running_) return;
        
        running_ = true;
        worker_thread_ = std::thread([this]() {
            // 模拟连接过程
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            connected_ = true;
            if (spi_) {
                spi_->OnFrontConnected();
            }
            
            // 模拟行情数据推送
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_real_distribution<> price_dis(70000.0, 80000.0);
            std::uniform_int_distribution<> volume_dis(1, 100);
            
            while (running_ && connected_) {
                if (logged_in_ && !subscribed_instruments_.empty()) {
                    for (const auto& instrument : subscribed_instruments_) {
                        CThostFtdcDepthMarketDataField market_data = {};
                        
                        // 填充模拟数据
                        strcpy(market_data.TradingDay, trading_day_.c_str());
                        strcpy(market_data.InstrumentID, instrument.c_str());
                        strcpy(market_data.ExchangeID, "SHFE");
                        strcpy(market_data.UpdateTime, "09:30:00");
                        
                        market_data.LastPrice = price_dis(gen);
                        market_data.Volume = volume_dis(gen);
                        market_data.Turnover = market_data.LastPrice * market_data.Volume;
                        market_data.OpenInterest = 10000;
                        market_data.UpdateMillisec = 0;
                        
                        // 五档行情
                        market_data.BidPrice1 = market_data.LastPrice - 10;
                        market_data.BidVolume1 = volume_dis(gen);
                        market_data.AskPrice1 = market_data.LastPrice + 10;
                        market_data.AskVolume1 = volume_dis(gen);
                        
                        market_data.BidPrice2 = market_data.LastPrice - 20;
                        market_data.BidVolume2 = volume_dis(gen);
                        market_data.AskPrice2 = market_data.LastPrice + 20;
                        market_data.AskVolume2 = volume_dis(gen);
                        
                        market_data.BidPrice3 = market_data.LastPrice - 30;
                        market_data.BidVolume3 = volume_dis(gen);
                        market_data.AskPrice3 = market_data.LastPrice + 30;
                        market_data.AskVolume3 = volume_dis(gen);
                        
                        market_data.BidPrice4 = market_data.LastPrice - 40;
                        market_data.BidVolume4 = volume_dis(gen);
                        market_data.AskPrice4 = market_data.LastPrice + 40;
                        market_data.AskVolume4 = volume_dis(gen);
                        
                        market_data.BidPrice5 = market_data.LastPrice - 50;
                        market_data.BidVolume5 = volume_dis(gen);
                        market_data.AskPrice5 = market_data.LastPrice + 50;
                        market_data.AskVolume5 = volume_dis(gen);
                        
                        if (spi_) {
                            spi_->OnRtnDepthMarketData(&market_data);
                        }
                    }
                }
                
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        });
    }
    
    int Join() override {
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        return 0;
    }
    
    const char* GetTradingDay() override {
        return trading_day_.c_str();
    }
    
    void RegisterFront(char* pszFrontAddress) override {
        // 模拟注册前置机
        std::cout << "Registered front: " << pszFrontAddress << std::endl;
    }
    
    void RegisterNameServer(char* pszNsAddress) override {
        // 模拟注册名字服务器
    }
    
    void RegisterFensUserInfo(CThostFtdcFensUserInfoField* pFensUserInfo) override {
        // 模拟注册用户信息
    }
    
    void RegisterSpi(CThostFtdcMdSpi* pSpi) override {
        spi_ = pSpi;
    }
    
    int SubscribeMarketData(char* ppInstrumentID[], int nCount) override {
        if (!connected_ || !logged_in_) {
            return -1;
        }
        
        for (int i = 0; i < nCount; ++i) {
            std::string instrument = ppInstrumentID[i];
            subscribed_instruments_.push_back(instrument);
            
            // 模拟订阅响应
            if (spi_) {
                CThostFtdcSpecificInstrumentField specific_instrument = {};
                strcpy(specific_instrument.InstrumentID, instrument.c_str());
                
                CThostFtdcRspInfoField rsp_info = {};
                rsp_info.ErrorID = 0;
                strcpy(rsp_info.ErrorMsg, "Success");
                
                spi_->OnRspSubMarketData(&specific_instrument, &rsp_info, 1, true);
            }
        }
        
        return 0;
    }
    
    int UnSubscribeMarketData(char* ppInstrumentID[], int nCount) override {
        if (!connected_ || !logged_in_) {
            return -1;
        }
        
        for (int i = 0; i < nCount; ++i) {
            std::string instrument = ppInstrumentID[i];
            auto it = std::find(subscribed_instruments_.begin(), 
                               subscribed_instruments_.end(), instrument);
            if (it != subscribed_instruments_.end()) {
                subscribed_instruments_.erase(it);
            }
            
            // 模拟退订响应
            if (spi_) {
                CThostFtdcSpecificInstrumentField specific_instrument = {};
                strcpy(specific_instrument.InstrumentID, instrument.c_str());
                
                CThostFtdcRspInfoField rsp_info = {};
                rsp_info.ErrorID = 0;
                strcpy(rsp_info.ErrorMsg, "Success");
                
                spi_->OnRspUnSubMarketData(&specific_instrument, &rsp_info, 1, true);
            }
        }
        
        return 0;
    }
    
    int SubscribeForQuoteRsp(char* ppInstrumentID[], int nCount) override {
        return 0; // 模拟实现
    }
    
    int UnSubscribeForQuoteRsp(char* ppInstrumentID[], int nCount) override {
        return 0; // 模拟实现
    }
    
    int ReqUserLogin(CThostFtdcReqUserLoginField* pReqUserLoginField, int nRequestID) override {
        if (!connected_) {
            return -1;
        }
        
        // 模拟登录过程
        std::thread([this, nRequestID]() {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            
            logged_in_ = true;
            
            if (spi_) {
                CThostFtdcRspUserLoginField rsp_login = {};
                strcpy(rsp_login.TradingDay, trading_day_.c_str());
                strcpy(rsp_login.LoginTime, "09:00:00");
                strcpy(rsp_login.BrokerID, "9999");
                strcpy(rsp_login.UserID, "test");
                strcpy(rsp_login.SystemName, "CTP Mock System");
                rsp_login.FrontID = 1;
                rsp_login.SessionID = 1;
                strcpy(rsp_login.MaxOrderRef, "1");
                
                CThostFtdcRspInfoField rsp_info = {};
                rsp_info.ErrorID = 0;
                strcpy(rsp_info.ErrorMsg, "Success");
                
                spi_->OnRspUserLogin(&rsp_login, &rsp_info, nRequestID, true);
            }
        }).detach();
        
        return 0;
    }
    
    int ReqUserLogout(CThostFtdcUserLogoutField* pUserLogout, int nRequestID) override {
        logged_in_ = false;
        
        if (spi_) {
            CThostFtdcRspInfoField rsp_info = {};
            rsp_info.ErrorID = 0;
            strcpy(rsp_info.ErrorMsg, "Success");
            
            spi_->OnRspUserLogout(pUserLogout, &rsp_info, nRequestID, true);
        }
        
        return 0;
    }
};

// 静态方法实现
CThostFtdcMdApi* CThostFtdcMdApi::CreateFtdcMdApi(const char* pszFlowPath) {
    return new CThostFtdcMdApiImpl();
}

const char* CThostFtdcMdApi::GetApiVersion() {
    return "6.6.9_Mock";
}