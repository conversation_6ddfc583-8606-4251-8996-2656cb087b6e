#include "../src/monitoring/metrics_collector.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <random>

using namespace monitoring;

int main() {
    std::cout << "Financial Data Service Monitoring Demo" << std::endl;
    std::cout << "=====================================" << std::endl;
    
    // Create monitoring configuration
    MonitoringConfig config;
    config.prometheus_bind_address = "0.0.0.0:9090";
    config.latency_threshold_microseconds = 50.0;
    config.missing_data_timeout = std::chrono::seconds(5);
    config.cpu_threshold = 85.0;
    config.memory_threshold = 85.0;
    config.disk_threshold = 85.0;
    
    // Initialize metrics collector
    MetricsCollector metrics_collector(config);
    
    if (!metrics_collector.initialize()) {
        std::cerr << "Failed to initialize monitoring system" << std::endl;
        return 1;
    }
    
    if (!metrics_collector.start()) {
        std::cerr << "Failed to start monitoring system" << std::endl;
        return 1;
    }
    
    // Add additional alert channels (optional)
    // metrics_collector.addSlackAlerts("https://hooks.slack.com/services/...", "#alerts");
    // metrics_collector.addWebhookAlerts("https://your-webhook-url.com/alerts");
    
    std::cout << "Monitoring system started successfully!" << std::endl;
    std::cout << "Prometheus metrics available at: http://localhost:9090/metrics" << std::endl;
    std::cout << "Running demo for 60 seconds..." << std::endl;
    
    // Random number generator for demo data
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> latency_dist(10.0, 100.0); // 10-100 microseconds
    std::uniform_int_distribution<> sequence_dist(1, 1000000);
    std::uniform_int_distribution<> symbol_dist(0, 4);
    
    std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409", "AU2412", "AG2412"};
    std::unordered_map<std::string, uint64_t> last_sequences;
    
    // Initialize sequences
    for (const auto& symbol : symbols) {
        last_sequences[symbol] = sequence_dist(gen);
    }
    
    // Simulate data processing for 60 seconds
    auto start_time = std::chrono::steady_clock::now();
    auto demo_duration = std::chrono::seconds(60);
    
    while (std::chrono::steady_clock::now() - start_time < demo_duration) {
        // Simulate processing multiple symbols
        for (const auto& symbol : symbols) {
            // Simulate latency measurement
            auto measurement_id = metrics_collector.startLatencyMeasurement("data_processing", symbol);
            
            // Simulate some processing work
            std::this_thread::sleep_for(std::chrono::microseconds(static_cast<int>(latency_dist(gen))));
            
            metrics_collector.endLatencyMeasurement(measurement_id);
            
            // Simulate data message with sequence number
            last_sequences[symbol]++;
            
            // Occasionally simulate missing sequences (data loss)
            if (gen() % 1000 == 0) {
                last_sequences[symbol] += 5; // Skip 5 sequences to simulate data loss
                std::cout << "Simulated data loss for " << symbol << std::endl;
            }
            
            metrics_collector.recordDataMessage(symbol, last_sequences[symbol], "CTP", "tick");
            
            // Simulate end-to-end latency
            double end_to_end_latency = latency_dist(gen);
            
            // Occasionally simulate high latency to trigger alerts
            if (gen() % 500 == 0) {
                end_to_end_latency = 75.0; // Above 50μs threshold
                std::cout << "Simulated high latency: " << end_to_end_latency << "μs for " << symbol << std::endl;
            }
            
            metrics_collector.recordLatency("end_to_end", end_to_end_latency, symbol);
        }
        
        // Sleep between iterations
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // Print system health every 10 seconds
        static auto last_health_check = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        if (now - last_health_check >= std::chrono::seconds(10)) {
            auto health = metrics_collector.getSystemHealth();
            
            std::cout << "\n--- System Health Status ---" << std::endl;
            std::cout << "Average Latency: " << health.average_latency_microseconds << " μs" << std::endl;
            std::cout << "Max Latency: " << health.max_latency_microseconds << " μs" << std::endl;
            std::cout << "Latency Violations: " << health.latency_violations << std::endl;
            std::cout << "Messages Received: " << health.total_messages_received << std::endl;
            std::cout << "Messages Lost: " << health.total_messages_lost << std::endl;
            std::cout << "Data Loss Rate: " << (health.data_loss_rate * 100.0) << "%" << std::endl;
            std::cout << "CPU Usage: " << health.current_cpu_usage << "%" << std::endl;
            std::cout << "Memory Usage: " << health.current_memory_usage << "%" << std::endl;
            std::cout << "Alerts Sent: " << health.total_alerts_sent << std::endl;
            std::cout << "Alerts Failed: " << health.total_alerts_failed << std::endl;
            std::cout << "----------------------------\n" << std::endl;
            
            last_health_check = now;
        }
    }
    
    std::cout << "Demo completed. Final system health:" << std::endl;
    auto final_health = metrics_collector.getSystemHealth();
    
    std::cout << "=== Final Statistics ===" << std::endl;
    std::cout << "Total Messages Processed: " << final_health.total_messages_received << std::endl;
    std::cout << "Total Data Loss Events: " << final_health.total_messages_lost << std::endl;
    std::cout << "Overall Data Loss Rate: " << (final_health.data_loss_rate * 100.0) << "%" << std::endl;
    std::cout << "Total Latency Violations: " << final_health.latency_violations << std::endl;
    std::cout << "Total Alerts Sent: " << final_health.total_alerts_sent << std::endl;
    std::cout << "========================" << std::endl;
    
    // Stop monitoring system
    metrics_collector.stop();
    
    std::cout << "Monitoring demo completed successfully!" << std::endl;
    return 0;
}