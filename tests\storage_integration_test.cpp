#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <random>
#include "../src/storage/storage_manager.h"
#include "../src/data_models.h"

using namespace financial_data;

// 生成测试数据
StandardTick GenerateTestTick(const std::string& symbol, int sequence) {
    StandardTick tick;
    tick.SetCurrentTimestamp();
    tick.symbol = symbol;
    tick.exchange = "SHFE";
    tick.sequence = sequence;
    
    // 随机价格数据
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> price_dist(78000.0, 79000.0);
    std::uniform_int_distribution<> volume_dist(1, 100);
    
    tick.last_price = price_dist(gen);
    tick.volume = volume_dist(gen);
    tick.turnover = tick.last_price * tick.volume;
    tick.open_interest = 50000 + sequence;
    
    // 五档买卖盘
    for (int i = 0; i < 5; ++i) {
        tick.bids[i] = PriceLevel(tick.last_price - (i + 1) * 10, volume_dist(gen));
        tick.asks[i] = PriceLevel(tick.last_price + (i + 1) * 10, volume_dist(gen));
    }
    
    return tick;
}

int main() {
    std::cout << "=== Storage Integration Test ===" << std::endl;
    
    // 创建存储管理器
    StorageManagerConfig config;
    config.enable_redis = true;
    config.enable_clickhouse = true;
    config.worker_thread_count = 2;
    config.enable_write_batching = true;
    config.batch_size = 100;
    config.batch_timeout_ms = 1000;
    
    // Redis配置
    config.redis_config.host = "127.0.0.1";
    config.redis_config.port = 6379;
    config.redis_config.database = 0;
    config.redis_config.max_connections = 5;
    
    // ClickHouse配置
    config.clickhouse_config.host = "127.0.0.1";
    config.clickhouse_config.port = 9000;
    config.clickhouse_config.database = "market_data";
    config.clickhouse_config.username = "admin";
    config.clickhouse_config.password = "password123";
    
    auto storage_manager = std::make_unique<StorageManager>(config);
    
    // 初始化存储管理器
    std::cout << "Initializing storage manager..." << std::endl;
    if (!storage_manager->Initialize()) {
        std::cerr << "Failed to initialize storage manager" << std::endl;
        return 1;
    }
    
    std::cout << "Storage manager initialized successfully" << std::endl;
    
    // 测试数据写入
    std::cout << "\n=== Testing Data Storage ===" << std::endl;
    
    std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409"};
    int total_records = 1000;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 异步写入测试数据
    std::vector<std::future<bool>> futures;
    
    for (int i = 0; i < total_records; ++i) {
        std::string symbol = symbols[i % symbols.size()];
        auto tick = GenerateTestTick(symbol, i);
        
        // 异步存储
        futures.push_back(storage_manager->StoreTickAsync(tick));
        
        if (i % 100 == 0) {
            std::cout << "Generated " << i << " records..." << std::endl;
        }
    }
    
    // 等待所有写入完成
    int success_count = 0;
    int failed_count = 0;
    
    for (auto& future : futures) {
        if (future.get()) {
            success_count++;
        } else {
            failed_count++;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "\n=== Write Performance Results ===" << std::endl;
    std::cout << "Total records: " << total_records << std::endl;
    std::cout << "Successful writes: " << success_count << std::endl;
    std::cout << "Failed writes: " << failed_count << std::endl;
    std::cout << "Total time: " << duration.count() << " ms" << std::endl;
    std::cout << "Throughput: " << (total_records * 1000.0 / duration.count()) << " records/sec" << std::endl;
    
    // 等待批处理完成
    std::cout << "\nWaiting for batch processing to complete..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(3));
    
    // 测试数据查询
    std::cout << "\n=== Testing Data Query ===" << std::endl;
    
    for (const auto& symbol : symbols) {
        StandardTick latest_tick;
        if (storage_manager->GetLatestTick(symbol, latest_tick)) {
            std::cout << "Latest tick for " << symbol << ":" << std::endl;
            std::cout << "  Price: " << latest_tick.last_price << std::endl;
            std::cout << "  Volume: " << latest_tick.volume << std::endl;
            std::cout << "  Timestamp: " << latest_tick.timestamp_ns << std::endl;
        } else {
            std::cout << "No data found for " << symbol << std::endl;
        }
    }
    
    // 历史数据查询测试
    std::cout << "\n=== Testing Historical Query ===" << std::endl;
    
    auto now = std::chrono::system_clock::now();
    auto one_hour_ago = now - std::chrono::hours(1);
    
    int64_t start_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        one_hour_ago.time_since_epoch()).count();
    int64_t end_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        now.time_since_epoch()).count();
    
    auto historical_data = storage_manager->QueryTicks("CU2409", start_ns, end_ns, 10);
    
    std::cout << "Historical query returned " << historical_data.size() << " records" << std::endl;
    for (size_t i = 0; i < std::min(historical_data.size(), size_t(5)); ++i) {
        const auto& tick = historical_data[i];
        std::cout << "  Record " << i << ": price=" << tick.last_price 
                  << ", volume=" << tick.volume << std::endl;
    }
    
    // 显示统计信息
    std::cout << "\n=== Storage Statistics ===" << std::endl;
    auto stats = storage_manager->GetStatistics();
    
    std::cout << "Redis Statistics:" << std::endl;
    std::cout << "  Successful writes: " << stats.redis_writes_success.load() << std::endl;
    std::cout << "  Failed writes: " << stats.redis_writes_failed.load() << std::endl;
    std::cout << "  Average latency: " << stats.redis_avg_latency_ms.load() << " ms" << std::endl;
    
    std::cout << "ClickHouse Statistics:" << std::endl;
    std::cout << "  Successful writes: " << stats.clickhouse_writes_success.load() << std::endl;
    std::cout << "  Failed writes: " << stats.clickhouse_writes_failed.load() << std::endl;
    std::cout << "  Average latency: " << stats.clickhouse_avg_latency_ms.load() << " ms" << std::endl;
    
    std::cout << "Task Statistics:" << std::endl;
    std::cout << "  Pending tasks: " << stats.pending_tasks.load() << std::endl;
    std::cout << "  Completed tasks: " << stats.completed_tasks.load() << std::endl;
    std::cout << "  Failed tasks: " << stats.failed_tasks.load() << std::endl;
    
    // 健康检查
    std::cout << "\n=== Health Check ===" << std::endl;
    auto health = storage_manager->GetHealthStatus();
    std::cout << "Overall healthy: " << (health.overall_healthy ? "Yes" : "No") << std::endl;
    std::cout << "Redis healthy: " << (health.redis_healthy ? "Yes" : "No") << std::endl;
    std::cout << "ClickHouse healthy: " << (health.clickhouse_healthy ? "Yes" : "No") << std::endl;
    std::cout << "Queue healthy: " << (health.queue_healthy ? "Yes" : "No") << std::endl;
    
    if (!health.error_message.empty()) {
        std::cout << "Error message: " << health.error_message << std::endl;
    }
    
    // 关闭存储管理器
    std::cout << "\nShutting down storage manager..." << std::endl;
    storage_manager->Shutdown();
    
    std::cout << "Storage integration test completed!" << std::endl;
    return 0;
}