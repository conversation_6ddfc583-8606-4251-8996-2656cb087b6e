#!/usr/bin/env python3
"""
增强功能测试脚本
测试新增的代码表获取和数据更新功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

from src.collectors.pytdx_collector import PyTDXCollector, PyTDXConfig

async def test_symbol_lists():
    """测试代码表获取功能"""
    print("=" * 50)
    print("测试代码表获取功能")
    print("=" * 50)
    
    # 初始化采集器
    config = PyTDXConfig()
    collector = PyTDXCollector(config)
    
    try:
        # 初始化连接
        print("正在初始化PyTDX连接...")
        if not await collector.initialize():
            print("❌ 初始化失败")
            return
        
        print("✅ 初始化成功")
        
        # 测试获取所有代码表
        print("\n正在获取所有代码表...")
        symbol_lists = await collector.get_all_symbol_lists()
        
        print("\n📊 代码表统计:")
        total_symbols = 0
        for symbol_type, symbols in symbol_lists.items():
            count = len(symbols)
            total_symbols += count
            print(f"  {symbol_type}: {count} 个")
            
            # 显示前5个示例
            if symbols:
                print(f"    示例: {[s['code'] for s in symbols[:5]]}")
        
        print(f"\n总计: {total_symbols} 个标的")
        
        # 测试单独获取股票列表
        print("\n正在测试股票列表获取...")
        stock_list_sz = await collector.get_stock_list(0)  # 深圳
        stock_list_sh = await collector.get_stock_list(1)  # 上海
        
        print(f"深圳市场股票: {len(stock_list_sz)} 个")
        print(f"上海市场股票: {len(stock_list_sh)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await collector.close()

async def test_data_update():
    """测试数据更新功能"""
    print("\n" + "=" * 50)
    print("测试数据更新功能")
    print("=" * 50)
    
    # 初始化采集器
    config = PyTDXConfig()
    collector = PyTDXCollector(config)
    
    try:
        # 初始化连接
        print("正在初始化PyTDX连接...")
        if not await collector.initialize():
            print("❌ 初始化失败")
            return
        
        print("✅ 初始化成功")
        
        # 获取一些测试代码
        print("\n正在获取测试代码...")
        symbol_lists = await collector.get_all_symbol_lists(['stock'])
        
        if 'stock' not in symbol_lists or not symbol_lists['stock']:
            print("❌ 没有获取到股票代码")
            return
        
        # 取前5个股票进行测试
        test_symbols = [s['code'] for s in symbol_lists['stock'][:5]]
        print(f"测试代码: {test_symbols}")
        
        # 测试K线数据获取
        print("\n正在测试K线数据获取...")
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = '2024-01-01'
        
        for symbol in test_symbols[:2]:  # 只测试前2个
            try:
                print(f"\n测试 {symbol}:")
                
                # 日线数据
                k_data = await collector.get_k_data(
                    code=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    ktype='D'
                )
                
                if k_data is not None and len(k_data) > 0:
                    print(f"  日线数据: {len(k_data)} 条")
                else:
                    print(f"  日线数据: 无数据")
                
                # 60分钟数据
                k_data_60 = await collector.get_k_data(
                    code=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    ktype='60'
                )
                
                if k_data_60 is not None and len(k_data_60) > 0:
                    print(f"  60分钟数据: {len(k_data_60)} 条")
                else:
                    print(f"  60分钟数据: 无数据")
                
            except Exception as e:
                print(f"  ❌ 获取 {symbol} 数据失败: {e}")
        
        # 测试实时行情
        print(f"\n正在测试实时行情...")
        try:
            realtime_data = await collector.get_realtime_quotes(test_symbols[:3])
            if realtime_data:
                print(f"实时行情: {len(realtime_data)} 条")
                for quote in realtime_data[:2]:
                    print(f"  {quote}")
            else:
                print("实时行情: 无数据")
        except Exception as e:
            print(f"❌ 获取实时行情失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await collector.close()

async def main():
    """主函数"""
    print("PyTDX数据采集系统 - 增强功能测试")
    print("测试新增的代码表获取和数据更新功能")
    print()
    
    try:
        # 测试代码表获取
        success1 = await test_symbol_lists()
        
        # 测试数据更新
        success2 = await test_data_update()
        
        print("\n" + "=" * 50)
        print("测试结果汇总")
        print("=" * 50)
        print(f"代码表获取: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"数据更新: {'✅ 成功' if success2 else '❌ 失败'}")
        
        if success1 and success2:
            print("\n🎉 所有测试通过！增强功能工作正常。")
            print("\n可以运行 start_enhanced_scheduler.bat 启动完整服务")
        else:
            print("\n⚠️  部分测试失败，请检查网络连接和PyTDX服务器状态")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())