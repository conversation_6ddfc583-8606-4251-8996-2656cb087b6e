import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  message,
  Tabs,
  Row,
  Col,
  Divider,
  Alert,
  Space,
  Tag,
  Tooltip
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';

interface SystemConfig {
  category: string;
  key: string;
  value: any;
  description: string;
  requires_restart: boolean;
}

const SystemConfig: React.FC = () => {
  const [configs, setConfigs] = useState<SystemConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [form] = Form.useForm();
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    fetchConfigs();
  }, []);

  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/config');
      setConfigs(response.data);
      
      // 将配置转换为表单数据
      const formData: any = {};
      response.data.forEach((config: SystemConfig) => {
        formData[`${config.category}.${config.key}`] = config.value;
      });
      form.setFieldsValue(formData);
      setHasChanges(false);
    } catch (error) {
      message.error('获取系统配置失败');
      console.error('Error fetching configs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const formValues = form.getFieldsValue();
      const updatedConfigs: SystemConfig[] = [];
      
      // 将表单数据转换回配置格式
      Object.keys(formValues).forEach(key => {
        const [category, configKey] = key.split('.');
        const originalConfig = configs.find(c => c.category === category && c.key === configKey);
        if (originalConfig) {
          updatedConfigs.push({
            ...originalConfig,
            value: formValues[key]
          });
        }
      });
      
      await axios.put('/api/config', updatedConfigs);
      message.success('配置保存成功');
      setHasChanges(false);
      
      // 检查是否有需要重启的配置
      const needsRestart = updatedConfigs.some(config => config.requires_restart);
      if (needsRestart) {
        message.warning('部分配置需要重启系统后生效');
      }
    } catch (error) {
      message.error('保存配置失败');
      console.error('Error saving configs:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    fetchConfigs();
    message.info('配置已重置');
  };

  const handleFormChange = () => {
    setHasChanges(true);
  };

  const groupedConfigs = configs.reduce((groups, config) => {
    if (!groups[config.category]) {
      groups[config.category] = [];
    }
    groups[config.category].push(config);
    return groups;
  }, {} as Record<string, SystemConfig[]>);

  const categoryLabels: Record<string, string> = {
    data_collection: '数据采集',
    storage: '数据存储',
    monitoring: '系统监控',
    api: 'API接口',
    security: '安全配置',
    performance: '性能优化'
  };

  const renderConfigItem = (config: SystemConfig) => {
    const fieldName = `${config.category}.${config.key}`;
    
    let inputComponent;
    if (typeof config.value === 'boolean') {
      inputComponent = (
        <Switch
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      );
    } else if (typeof config.value === 'number') {
      inputComponent = (
        <InputNumber
          style={{ width: '100%' }}
          min={0}
        />
      );
    } else {
      inputComponent = <Input />;
    }

    return (
      <Form.Item
        key={fieldName}
        name={fieldName}
        label={
          <Space>
            <span>{config.key}</span>
            {config.requires_restart && (
              <Tooltip title="修改此配置需要重启系统">
                <Tag color="orange" icon={<ExclamationCircleOutlined />}>
                  需重启
                </Tag>
              </Tooltip>
            )}
          </Space>
        }
        help={config.description}
        style={{ marginBottom: 16 }}
      >
        {inputComponent}
      </Form.Item>
    );
  };

  const tabItems = Object.keys(groupedConfigs).map(category => ({
    key: category,
    label: categoryLabels[category] || category,
    children: (
      <div>
        {groupedConfigs[category].map(config => renderConfigItem(config))}
      </div>
    )
  }));

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h2 style={{ margin: 0 }}>
              <SettingOutlined style={{ marginRight: 8 }} />
              系统配置
            </h2>
            <p style={{ margin: '8px 0 0 0', color: '#666' }}>
              修改系统运行参数和配置选项
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
              disabled={loading || saving}
            >
              重置
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={saving}
              disabled={!hasChanges}
            >
              保存配置
            </Button>
          </Space>
        </div>

        {hasChanges && (
          <Alert
            message="配置已修改"
            description="您有未保存的配置更改，请点击"保存配置"按钮保存更改。"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
        >
          <Tabs
            items={tabItems}
            type="card"
            size="small"
          />
        </Form>
      </Card>

      <Card title="配置说明" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <h4>
              <InfoCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              数据采集配置
            </h4>
            <ul>
              <li><strong>max_connections:</strong> 最大并发连接数，影响系统并发处理能力</li>
              <li><strong>buffer_size:</strong> 数据缓冲区大小，影响内存使用和处理效率</li>
              <li><strong>timeout_seconds:</strong> 连接超时时间，影响网络连接稳定性</li>
            </ul>
          </Col>
          <Col span={12}>
            <h4>
              <InfoCircleOutlined style={{ marginRight: 8, color: '#52c41a' }} />
              存储配置
            </h4>
            <ul>
              <li><strong>hot_data_retention_days:</strong> 热数据保留天数，影响查询性能</li>
              <li><strong>warm_data_retention_years:</strong> 温数据保留年数，影响存储成本</li>
              <li><strong>compression_ratio_target:</strong> 目标压缩比，影响存储效率</li>
            </ul>
          </Col>
        </Row>
        
        <Divider />
        
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <h4>
              <InfoCircleOutlined style={{ marginRight: 8, color: '#faad14' }} />
              监控配置
            </h4>
            <ul>
              <li><strong>metrics_collection_interval:</strong> 指标收集间隔，影响监控精度</li>
              <li><strong>alert_check_interval:</strong> 告警检查间隔，影响告警响应速度</li>
            </ul>
          </Col>
          <Col span={12}>
            <h4>
              <InfoCircleOutlined style={{ marginRight: 8, color: '#722ed1' }} />
              API配置
            </h4>
            <ul>
              <li><strong>rate_limit_per_minute:</strong> API速率限制，防止系统过载</li>
              <li><strong>max_query_range_days:</strong> 最大查询时间范围，控制查询复杂度</li>
            </ul>
          </Col>
        </Row>

        <Alert
          message="重要提示"
          description="标记为"需重启"的配置项修改后需要重启相关服务才能生效。请在业务低峰期进行配置修改和系统重启。"
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      </Card>
    </div>
  );
};

export default SystemConfig;