#!/usr/bin/env python3
"""
集成测试运行器
协调运行Python和C++的端到端集成测试
"""

import os
import sys
import subprocess
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class IntegrationTestRunner:
    """集成测试运行器"""
    
    def __init__(self):
        self.project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
        self.test_results = {}
        self.start_time = None
        
    def run_all_tests(self) -> bool:
        """运行所有集成测试"""
        logger.info("Starting comprehensive integration test suite")
        self.start_time = time.time()
        
        # 1. 环境检查
        if not self._check_test_environment():
            logger.error("Test environment check failed")
            return False
        
        # 2. 运行Python集成测试
        python_success = self._run_python_tests()
        
        # 3. 运行C++集成测试
        cpp_success = self._run_cpp_tests()
        
        # 4. 生成综合报告
        self._generate_comprehensive_report(python_success, cpp_success)
        
        # 5. 清理测试环境
        self._cleanup_test_environment()
        
        overall_success = python_success and cpp_success
        
        total_time = time.time() - self.start_time
        logger.info(f"Integration test suite completed in {total_time:.2f}s - "
                   f"Success: {overall_success}")
        
        return overall_success
    
    def _check_test_environment(self) -> bool:
        """检查测试环境"""
        logger.info("Checking test environment...")
        
        # 检查必要的服务
        services_to_check = [
            ("Redis", "redis-cli", ["ping"]),
            ("ClickHouse", "clickhouse-client", ["--query", "SELECT 1"]),
        ]
        
        for service_name, command, args in services_to_check:
            try:
                result = subprocess.run(
                    [command] + args,
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode != 0:
                    logger.warning(f"{service_name} service check failed: {result.stderr}")
                    # 不强制要求所有服务都可用，允许使用mock
                else:
                    logger.info(f"{service_name} service is available")
                    
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                logger.warning(f"{service_name} service check failed: {e}")
        
        # 检查Python依赖
        try:
            import pandas
            import redis
            import requests
            logger.info("Python dependencies are available")
        except ImportError as e:
            logger.error(f"Missing Python dependency: {e}")
            return False
        
        # 检查测试数据目录
        test_data_dir = os.path.join(self.project_root, "tests", "data")
        os.makedirs(test_data_dir, exist_ok=True)
        
        logger.info("Test environment check completed")
        return True
    
    def _run_python_tests(self) -> bool:
        """运行Python集成测试"""
        logger.info("Running Python integration tests...")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env["PYTHONPATH"] = self.project_root
            env["TESTING"] = "1"
            
            # 运行Python测试
            python_test_file = os.path.join(
                self.project_root, 
                "tests", 
                "integration", 
                "end_to_end_integration_test.py"
            )
            
            result = subprocess.run(
                [sys.executable, python_test_file],
                cwd=self.project_root,
                env=env,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            # 记录结果
            self.test_results["python"] = {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
            if result.returncode == 0:
                logger.info("Python integration tests passed")
                return True
            else:
                logger.error(f"Python integration tests failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Python integration tests timed out")
            self.test_results["python"] = {
                "success": False,
                "error": "Test timeout"
            }
            return False
        except Exception as e:
            logger.error(f"Failed to run Python integration tests: {e}")
            self.test_results["python"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def _run_cpp_tests(self) -> bool:
        """运行C++集成测试"""
        logger.info("Running C++ integration tests...")
        
        try:
            # 检查是否有编译好的测试可执行文件
            cpp_test_executable = os.path.join(
                self.project_root,
                "build",
                "tests",
                "integration",
                "end_to_end_integration_test"
            )
            
            # 如果没有可执行文件，尝试编译
            if not os.path.exists(cpp_test_executable):
                logger.info("C++ test executable not found, attempting to build...")
                
                if not self._build_cpp_tests():
                    logger.warning("Failed to build C++ tests, skipping...")
                    self.test_results["cpp"] = {
                        "success": False,
                        "error": "Build failed",
                        "skipped": True
                    }
                    return True  # 不强制要求C++测试成功
            
            # 运行C++测试
            if os.path.exists(cpp_test_executable):
                result = subprocess.run(
                    [cpp_test_executable],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=600  # 10分钟超时
                )
                
                self.test_results["cpp"] = {
                    "success": result.returncode == 0,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "return_code": result.returncode
                }
                
                if result.returncode == 0:
                    logger.info("C++ integration tests passed")
                    return True
                else:
                    logger.error(f"C++ integration tests failed: {result.stderr}")
                    return False
            else:
                logger.warning("C++ test executable not available, skipping...")
                self.test_results["cpp"] = {
                    "success": False,
                    "error": "Executable not available",
                    "skipped": True
                }
                return True  # 不强制要求C++测试成功
                
        except subprocess.TimeoutExpired:
            logger.error("C++ integration tests timed out")
            self.test_results["cpp"] = {
                "success": False,
                "error": "Test timeout"
            }
            return False
        except Exception as e:
            logger.error(f"Failed to run C++ integration tests: {e}")
            self.test_results["cpp"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def _build_cpp_tests(self) -> bool:
        """构建C++测试"""
        try:
            build_dir = os.path.join(self.project_root, "build")
            os.makedirs(build_dir, exist_ok=True)
            
            # 运行CMake配置
            cmake_result = subprocess.run(
                ["cmake", ".."],
                cwd=build_dir,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if cmake_result.returncode != 0:
                logger.error(f"CMake configuration failed: {cmake_result.stderr}")
                return False
            
            # 构建测试
            build_result = subprocess.run(
                ["cmake", "--build", ".", "--target", "end_to_end_integration_test"],
                cwd=build_dir,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if build_result.returncode != 0:
                logger.error(f"C++ test build failed: {build_result.stderr}")
                return False
            
            logger.info("C++ tests built successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to build C++ tests: {e}")
            return False
    
    def _generate_comprehensive_report(self, python_success: bool, cpp_success: bool):
        """生成综合测试报告"""
        logger.info("Generating comprehensive test report...")
        
        total_time = time.time() - self.start_time
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_duration_seconds": total_time,
            "overall_success": python_success and cpp_success,
            "test_results": {
                "python": self.test_results.get("python", {}),
                "cpp": self.test_results.get("cpp", {})
            },
            "summary": {
                "python_tests": "PASSED" if python_success else "FAILED",
                "cpp_tests": "PASSED" if cpp_success else "FAILED/SKIPPED",
                "total_tests": 2,
                "passed_tests": sum([python_success, cpp_success]),
                "success_rate": (sum([python_success, cpp_success]) / 2) * 100
            },
            "environment": {
                "python_version": sys.version,
                "platform": sys.platform,
                "working_directory": self.project_root
            }
        }
        
        # 保存报告
        report_file = os.path.join(
            self.project_root,
            "tests",
            "integration",
            f"comprehensive_test_report_{int(time.time())}.json"
        )
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 生成简化的控制台报告
        self._print_console_report(report)
        
        logger.info(f"Comprehensive test report saved to: {report_file}")
    
    def _print_console_report(self, report: Dict):
        """打印控制台报告"""
        print("\n" + "="*80)
        print("INTEGRATION TEST SUMMARY")
        print("="*80)
        print(f"Timestamp: {report['timestamp']}")
        print(f"Duration: {report['total_duration_seconds']:.2f} seconds")
        print(f"Overall Result: {'PASSED' if report['overall_success'] else 'FAILED'}")
        print()
        
        print("Test Results:")
        print(f"  Python Tests: {report['summary']['python_tests']}")
        print(f"  C++ Tests: {report['summary']['cpp_tests']}")
        print()
        
        print(f"Success Rate: {report['summary']['success_rate']:.1f}% "
              f"({report['summary']['passed_tests']}/{report['summary']['total_tests']})")
        
        # 显示失败详情
        if not report['overall_success']:
            print("\nFailure Details:")
            for test_type, result in report['test_results'].items():
                if not result.get('success', False) and not result.get('skipped', False):
                    print(f"  {test_type.upper()} Test Failed:")
                    if 'error' in result:
                        print(f"    Error: {result['error']}")
                    if 'stderr' in result and result['stderr']:
                        print(f"    Details: {result['stderr'][:200]}...")
        
        print("="*80)
    
    def _cleanup_test_environment(self):
        """清理测试环境"""
        logger.info("Cleaning up test environment...")
        
        try:
            # 清理测试数据文件
            test_data_dir = os.path.join(self.project_root, "tests", "data")
            if os.path.exists(test_data_dir):
                for file in os.listdir(test_data_dir):
                    if file.startswith("test_"):
                        os.remove(os.path.join(test_data_dir, file))
            
            # 清理临时配置文件
            temp_config = os.path.join(self.project_root, "config", "test_config.json")
            if os.path.exists(temp_config):
                os.remove(temp_config)
            
            logger.info("Test environment cleanup completed")
            
        except Exception as e:
            logger.warning(f"Cleanup warning: {e}")


def main():
    """主函数"""
    runner = IntegrationTestRunner()
    
    try:
        success = runner.run_all_tests()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("Integration tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Integration test runner failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()