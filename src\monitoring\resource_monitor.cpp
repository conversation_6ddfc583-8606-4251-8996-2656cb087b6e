#include "resource_monitor.h"
#include "alert_manager.h"
#include "prometheus_metrics.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <fstream>

#ifdef _WIN32
#pragma comment(lib, "pdh.lib")
#pragma comment(lib, "psapi.lib")
#endif

namespace monitoring {

ResourceMonitor::ResourceMonitor(std::shared_ptr<AlertManager> alert_manager)
    : alert_manager_(alert_manager) {
#ifdef _WIN32
    pdh_initialized_ = false;
#else
    cpu_times_initialized_ = false;
#endif
    initializePlatformSpecific();
}

ResourceMonitor::~ResourceMonitor() {
    stop();
    cleanupPlatformSpecific();
}

bool ResourceMonitor::start() {
    if (running_.load()) {
        return true;
    }
    
    running_.store(true);
    monitoring_thread_ = std::thread(&ResourceMonitor::monitoringLoop, this);
    
    std::cout << "Resource monitor started with thresholds - CPU: " 
              << cpu_threshold_.load() << "%, Memory: " << memory_threshold_.load() 
              << "%, Disk: " << disk_threshold_.load() << "%" << std::endl;
    return true;
}

void ResourceMonitor::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    
    if (monitoring_thread_.joinable()) {
        monitoring_thread_.join();
    }
    
    std::cout << "Resource monitor stopped" << std::endl;
}

void ResourceMonitor::monitoringLoop() {
    while (running_.load()) {
        auto start_time = std::chrono::steady_clock::now();
        
        ResourceUsage usage = getCurrentUsage();
        processResourceUsage(usage);
        
        auto end_time = std::chrono::steady_clock::now();
        auto elapsed = end_time - start_time;
        auto sleep_time = monitoring_interval_ - elapsed;
        
        if (sleep_time > std::chrono::seconds(0)) {
            std::this_thread::sleep_for(sleep_time);
        }
    }
}

ResourceUsage ResourceMonitor::getCurrentUsage() {
    ResourceUsage usage;
    usage.timestamp = std::chrono::high_resolution_clock::now();
    
    usage.cpu_percentage = getCpuUsage();
    usage.memory_percentage = getMemoryUsage(usage.memory_used_bytes, usage.memory_total_bytes);
    usage.disk_percentage = getDiskUsage(usage.disk_used_bytes, usage.disk_total_bytes);
    usage.network_bytes_per_second = getNetworkBandwidth();
    
    return usage;
}

void ResourceMonitor::processResourceUsage(const ResourceUsage& usage) {
    // Update statistics
    updateStatistics(usage);
    
    // Update Prometheus metrics
    auto& metrics = PrometheusMetrics::getInstance();
    metrics.updateCpuUsage(usage.cpu_percentage);
    metrics.updateMemoryUsage(usage.memory_percentage);
    metrics.updateDiskUsage(usage.disk_percentage);
    metrics.updateNetworkBandwidth(usage.network_bytes_per_second);
    
    // Check thresholds
    checkThresholds(usage);
}

void ResourceMonitor::updateStatistics(const ResourceUsage& usage) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    sample_count_++;
    
    // Update average using incremental formula
    double alpha = 1.0 / sample_count_;
    average_usage_.cpu_percentage = (1.0 - alpha) * average_usage_.cpu_percentage + alpha * usage.cpu_percentage;
    average_usage_.memory_percentage = (1.0 - alpha) * average_usage_.memory_percentage + alpha * usage.memory_percentage;
    average_usage_.disk_percentage = (1.0 - alpha) * average_usage_.disk_percentage + alpha * usage.disk_percentage;
    average_usage_.network_bytes_per_second = (1.0 - alpha) * average_usage_.network_bytes_per_second + alpha * usage.network_bytes_per_second;
    
    // Update peak values
    peak_usage_.cpu_percentage = std::max(peak_usage_.cpu_percentage, usage.cpu_percentage);
    peak_usage_.memory_percentage = std::max(peak_usage_.memory_percentage, usage.memory_percentage);
    peak_usage_.disk_percentage = std::max(peak_usage_.disk_percentage, usage.disk_percentage);
    peak_usage_.network_bytes_per_second = std::max(peak_usage_.network_bytes_per_second, usage.network_bytes_per_second);
    peak_usage_.memory_used_bytes = std::max(peak_usage_.memory_used_bytes, usage.memory_used_bytes);
    peak_usage_.disk_used_bytes = std::max(peak_usage_.disk_used_bytes, usage.disk_used_bytes);
}

void ResourceMonitor::checkThresholds(const ResourceUsage& usage) {
    // Check CPU threshold
    if (usage.cpu_percentage > cpu_threshold_.load()) {
        sendResourceAlert("CPU", usage.cpu_percentage, cpu_threshold_.load());
    }
    
    // Check memory threshold
    if (usage.memory_percentage > memory_threshold_.load()) {
        sendResourceAlert("Memory", usage.memory_percentage, memory_threshold_.load());
    }
    
    // Check disk threshold
    if (usage.disk_percentage > disk_threshold_.load()) {
        sendResourceAlert("Disk", usage.disk_percentage, disk_threshold_.load());
    }
}

void ResourceMonitor::sendResourceAlert(const std::string& resource_type, double current_value, double threshold) {
    std::chrono::steady_clock::time_point* last_alert_time = nullptr;
    
    if (resource_type == "CPU") {
        last_alert_time = &last_cpu_alert_;
    } else if (resource_type == "Memory") {
        last_alert_time = &last_memory_alert_;
    } else if (resource_type == "Disk") {
        last_alert_time = &last_disk_alert_;
    }
    
    if (last_alert_time && !shouldSendAlert(*last_alert_time)) {
        return;
    }
    
    std::ostringstream message;
    message << std::fixed << std::setprecision(2);
    message << resource_type << " usage threshold exceeded!\n";
    message << "Current usage: " << current_value << "%\n";
    message << "Threshold: " << threshold << "%\n";
    message << "This may impact system performance and data processing capabilities.";
    
    std::string severity = (current_value > threshold + 10.0) ? "CRITICAL" : "WARNING";
    
    if (alert_manager_) {
        alert_manager_->sendAlert(
            "resource_usage",
            severity,
            message.str(),
            {
                {"resource_type", resource_type},
                {"current_value", std::to_string(current_value)},
                {"threshold", std::to_string(threshold)},
                {"severity", severity}
            }
        );
    }
    
    // Record alert metric
    auto& metrics = PrometheusMetrics::getInstance();
    metrics.incrementAlert("resource_usage", severity == "CRITICAL" ? "critical" : "warning");
    
    std::cout << "ALERT: " << message.str() << std::endl;
}

bool ResourceMonitor::shouldSendAlert(std::chrono::steady_clock::time_point& last_alert_time) {
    std::lock_guard<std::mutex> lock(alert_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    if (now - last_alert_time >= alert_cooldown_) {
        last_alert_time = now;
        return true;
    }
    
    return false;
}

void ResourceMonitor::initializePlatformSpecific() {
#ifdef _WIN32
    pdh_initialized_ = initializeWindowsCounters();
#else
    cpu_times_initialized_ = readCpuTimes(last_cpu_times_);
#endif
}

void ResourceMonitor::cleanupPlatformSpecific() {
#ifdef _WIN32
    cleanupWindowsCounters();
#endif
}

#ifdef _WIN32

bool ResourceMonitor::initializeWindowsCounters() {
    if (PdhOpenQuery(NULL, 0, &cpu_query_) != ERROR_SUCCESS) {
        return false;
    }
    
    if (PdhAddEnglishCounter(cpu_query_, L"\\Processor(_Total)\\% Processor Time", 0, &cpu_counter_) != ERROR_SUCCESS) {
        PdhCloseQuery(cpu_query_);
        return false;
    }
    
    // Collect initial sample
    PdhCollectQueryData(cpu_query_);
    
    return true;
}

void ResourceMonitor::cleanupWindowsCounters() {
    if (pdh_initialized_) {
        PdhCloseQuery(cpu_query_);
    }
}

double ResourceMonitor::getCpuUsage() {
    if (!pdh_initialized_) {
        return 0.0;
    }
    
    PDH_FMT_COUNTERVALUE counter_value;
    
    PdhCollectQueryData(cpu_query_);
    if (PdhGetFormattedCounterValue(cpu_counter_, PDH_FMT_DOUBLE, NULL, &counter_value) == ERROR_SUCCESS) {
        return counter_value.doubleValue;
    }
    
    return 0.0;
}

double ResourceMonitor::getMemoryUsage(uint64_t& used_bytes, uint64_t& total_bytes) {
    MEMORYSTATUSEX mem_status;
    mem_status.dwLength = sizeof(mem_status);
    
    if (GlobalMemoryStatusEx(&mem_status)) {
        total_bytes = mem_status.ullTotalPhys;
        used_bytes = total_bytes - mem_status.ullAvailPhys;
        return static_cast<double>(used_bytes) / static_cast<double>(total_bytes) * 100.0;
    }
    
    used_bytes = total_bytes = 0;
    return 0.0;
}

double ResourceMonitor::getDiskUsage(uint64_t& used_bytes, uint64_t& total_bytes) {
    ULARGE_INTEGER free_bytes, total_bytes_large;
    
    if (GetDiskFreeSpaceEx(L"C:\\", &free_bytes, &total_bytes_large, NULL)) {
        total_bytes = total_bytes_large.QuadPart;
        used_bytes = total_bytes - free_bytes.QuadPart;
        return static_cast<double>(used_bytes) / static_cast<double>(total_bytes) * 100.0;
    }
    
    used_bytes = total_bytes = 0;
    return 0.0;
}

#else // Linux implementation

bool ResourceMonitor::readCpuTimes(CpuTimes& times) {
    std::ifstream file("/proc/stat");
    if (!file.is_open()) {
        return false;
    }
    
    std::string line;
    if (std::getline(file, line) && line.substr(0, 3) == "cpu") {
        std::istringstream iss(line);
        std::string cpu_label;
        iss >> cpu_label >> times.user >> times.nice >> times.system >> times.idle 
            >> times.iowait >> times.irq >> times.softirq;
        return true;
    }
    
    return false;
}

double ResourceMonitor::calculateCpuUsage(const CpuTimes& current, const CpuTimes& previous) {
    uint64_t prev_idle = previous.idle + previous.iowait;
    uint64_t curr_idle = current.idle + current.iowait;
    
    uint64_t prev_non_idle = previous.user + previous.nice + previous.system + 
                            previous.irq + previous.softirq;
    uint64_t curr_non_idle = current.user + current.nice + current.system + 
                            current.irq + current.softirq;
    
    uint64_t prev_total = prev_idle + prev_non_idle;
    uint64_t curr_total = curr_idle + curr_non_idle;
    
    uint64_t total_diff = curr_total - prev_total;
    uint64_t idle_diff = curr_idle - prev_idle;
    
    if (total_diff == 0) {
        return 0.0;
    }
    
    return (static_cast<double>(total_diff - idle_diff) / static_cast<double>(total_diff)) * 100.0;
}

double ResourceMonitor::getCpuUsage() {
    if (!cpu_times_initialized_) {
        return 0.0;
    }
    
    CpuTimes current_times;
    if (!readCpuTimes(current_times)) {
        return 0.0;
    }
    
    double usage = calculateCpuUsage(current_times, last_cpu_times_);
    last_cpu_times_ = current_times;
    
    return usage;
}

double ResourceMonitor::getMemoryUsage(uint64_t& used_bytes, uint64_t& total_bytes) {
    std::ifstream file("/proc/meminfo");
    if (!file.is_open()) {
        used_bytes = total_bytes = 0;
        return 0.0;
    }
    
    std::string line;
    uint64_t mem_total = 0, mem_available = 0;
    
    while (std::getline(file, line)) {
        if (line.substr(0, 9) == "MemTotal:") {
            std::istringstream iss(line);
            std::string label, unit;
            iss >> label >> mem_total >> unit;
            mem_total *= 1024; // Convert from KB to bytes
        } else if (line.substr(0, 12) == "MemAvailable:") {
            std::istringstream iss(line);
            std::string label, unit;
            iss >> label >> mem_available >> unit;
            mem_available *= 1024; // Convert from KB to bytes
        }
    }
    
    if (mem_total > 0) {
        total_bytes = mem_total;
        used_bytes = mem_total - mem_available;
        return static_cast<double>(used_bytes) / static_cast<double>(total_bytes) * 100.0;
    }
    
    used_bytes = total_bytes = 0;
    return 0.0;
}

double ResourceMonitor::getDiskUsage(uint64_t& used_bytes, uint64_t& total_bytes) {
    struct statvfs stat;
    
    if (statvfs("/", &stat) == 0) {
        total_bytes = stat.f_blocks * stat.f_frsize;
        uint64_t free_bytes = stat.f_bavail * stat.f_frsize;
        used_bytes = total_bytes - free_bytes;
        
        if (total_bytes > 0) {
            return static_cast<double>(used_bytes) / static_cast<double>(total_bytes) * 100.0;
        }
    }
    
    used_bytes = total_bytes = 0;
    return 0.0;
}

#endif

double ResourceMonitor::getNetworkBandwidth() {
    // Simplified implementation - in a real system, you would track
    // network interface statistics over time to calculate bandwidth
    return 0.0;
}

ResourceUsage ResourceMonitor::getAverageUsage() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return average_usage_;
}

ResourceUsage ResourceMonitor::getPeakUsage() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return peak_usage_;
}

void ResourceMonitor::resetStatistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    average_usage_ = {};
    peak_usage_ = {};
    sample_count_ = 0;
}

} // namespace monitoring