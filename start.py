#!/usr/bin/env python3
"""
金融数据服务系统 - 统一启动脚本
Financial Data Service - Unified Startup Script

这是一个统一的启动脚本，可以快速启动不同环境的服务：

使用方法:
    python start.py                    # 启动开发环境（默认）
    python start.py --env dev          # 启动开发环境
    python start.py --env test         # 启动测试环境
    python start.py --env prod         # 启动生产环境
    python start.py --services redis   # 只启动Redis服务
    python start.py --quick            # 快速启动（最小服务集）
    python start.py --check            # 检查环境状态
    python start.py --stop             # 停止所有服务
"""

import os
import sys
import json
import time
import argparse
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ServiceManager:
    """服务管理器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root or os.getcwd())
        self.config_file = self.project_root / "config" / "environments.json"
        self.environments = self._load_environments()
        
        # 服务定义
        self.services = {
            "redis": {
                "name": "Redis",
                "check_cmd": ["redis-cli", "ping"],
                "start_cmd": ["docker", "run", "-d", "--name", "redis", "-p", "6379:6379", "redis:7-alpine"],
                "required": True
            },
            "clickhouse": {
                "name": "ClickHouse",
                "check_cmd": ["curl", "-s", "http://localhost:8123/ping"],
                "start_cmd": ["docker", "run", "-d", "--name", "clickhouse", "-p", "8123:8123", "-p", "9000:9000", "clickhouse/clickhouse-server:latest"],
                "required": True
            },
            "kafka": {
                "name": "Kafka",
                "check_cmd": ["docker", "ps", "--filter", "name=kafka", "--format", "{{.Names}}"],
                "start_cmd": ["docker-compose", "-f", "docker-compose.dev.yml", "up", "-d", "kafka"],
                "required": False
            },
            "financial-app": {
                "name": "Financial App",
                "check_cmd": ["curl", "-s", "http://localhost:8080/health"],
                "start_cmd": ["python", "src/main.py"],
                "required": True
            }
        }
    
    def _load_environments(self) -> Dict:
        """加载环境配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"环境配置文件不存在: {self.config_file}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"环境配置文件格式错误: {e}")
            return {}
    
    def check_service(self, service_name: str) -> bool:
        """检查服务状态"""
        service = self.services.get(service_name)
        if not service:
            return False
        
        try:
            result = subprocess.run(
                service["check_cmd"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def start_service(self, service_name: str) -> bool:
        """启动单个服务"""
        service = self.services.get(service_name)
        if not service:
            logger.error(f"未知服务: {service_name}")
            return False
        
        logger.info(f"启动 {service['name']}...")
        
        # 检查服务是否已经运行
        if self.check_service(service_name):
            logger.info(f"{service['name']} 已经在运行")
            return True
        
        try:
            # 启动服务
            if service_name == "financial-app":
                # 应用服务在前台运行
                subprocess.Popen(service["start_cmd"], cwd=self.project_root)
            else:
                # 基础设施服务在后台运行
                result = subprocess.run(
                    service["start_cmd"],
                    capture_output=True,
                    text=True,
                    cwd=self.project_root
                )
                if result.returncode != 0:
                    logger.error(f"启动 {service['name']} 失败: {result.stderr}")
                    return False
            
            # 等待服务启动
            for i in range(30):  # 最多等待30秒
                if self.check_service(service_name):
                    logger.info(f"✅ {service['name']} 启动成功")
                    return True
                time.sleep(1)
            
            logger.error(f"❌ {service['name']} 启动超时")
            return False
            
        except Exception as e:
            logger.error(f"启动 {service['name']} 时出错: {e}")
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """停止单个服务"""
        service = self.services.get(service_name)
        if not service:
            logger.error(f"未知服务: {service_name}")
            return False
        
        logger.info(f"停止 {service['name']}...")
        
        try:
            if service_name in ["redis", "clickhouse"]:
                # 停止Docker容器
                subprocess.run(["docker", "stop", service_name], capture_output=True)
                subprocess.run(["docker", "rm", service_name], capture_output=True)
            elif service_name == "kafka":
                # 停止Kafka相关容器
                subprocess.run(["docker-compose", "-f", "docker-compose.dev.yml", "stop", "kafka"], capture_output=True)
            elif service_name == "financial-app":
                # 停止应用进程
                subprocess.run(["pkill", "-f", "src/main.py"], capture_output=True)
            
            logger.info(f"✅ {service['name']} 已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止 {service['name']} 时出错: {e}")
            return False
    
    def start_environment(self, env: str, services: List[str] = None, quick: bool = False) -> bool:
        """启动指定环境"""
        logger.info(f"启动 {env} 环境...")
        
        # 检查环境配置
        if env not in self.environments:
            logger.error(f"未知环境: {env}")
            return False
        
        env_config = self.environments[env]
        logger.info(f"环境描述: {env_config.get('description', 'N/A')}")
        
        # 确定要启动的服务
        if services:
            services_to_start = services
        elif quick:
            services_to_start = ["redis", "financial-app"]  # 快速启动最小服务集
        else:
            # 根据环境配置确定服务
            features = env_config.get("features", {})
            services_to_start = ["redis", "clickhouse"]
            
            if features.get("ctp_collector") or features.get("pytdx_collector"):
                services_to_start.append("financial-app")
            
            if env == "testing":
                services_to_start.append("kafka")
        
        # 启动服务
        success = True
        for service_name in services_to_start:
            if not self.start_service(service_name):
                if self.services[service_name]["required"]:
                    logger.error(f"必需服务 {service_name} 启动失败，停止启动流程")
                    success = False
                    break
                else:
                    logger.warning(f"可选服务 {service_name} 启动失败，继续启动其他服务")
        
        if success:
            logger.info(f"🎉 {env} 环境启动成功！")
            self._show_service_info(env)
        else:
            logger.error(f"❌ {env} 环境启动失败")
        
        return success
    
    def stop_environment(self) -> bool:
        """停止所有服务"""
        logger.info("停止所有服务...")
        
        success = True
        for service_name in self.services.keys():
            if not self.stop_service(service_name):
                success = False
        
        # 额外清理
        try:
            subprocess.run(["docker-compose", "down"], capture_output=True, cwd=self.project_root)
        except:
            pass
        
        if success:
            logger.info("🛑 所有服务已停止")
        else:
            logger.warning("⚠️ 部分服务停止失败")
        
        return success
    
    def check_environment(self) -> Dict[str, bool]:
        """检查环境状态"""
        logger.info("检查环境状态...")
        
        status = {}
        for service_name, service in self.services.items():
            is_running = self.check_service(service_name)
            status[service_name] = is_running
            
            status_icon = "✅" if is_running else "❌"
            logger.info(f"{status_icon} {service['name']}: {'运行中' if is_running else '未运行'}")
        
        return status
    
    def _show_service_info(self, env: str):
        """显示服务信息"""
        logger.info("\n" + "="*50)
        logger.info("服务访问信息")
        logger.info("="*50)
        
        if self.check_service("redis"):
            logger.info("Redis: localhost:6379")
        
        if self.check_service("clickhouse"):
            logger.info("ClickHouse HTTP: http://localhost:8123")
            logger.info("ClickHouse TCP: localhost:9000")
        
        if self.check_service("financial-app"):
            logger.info("Financial App: http://localhost:8080")
            logger.info("Health Check: http://localhost:8080/health")
        
        if env == "testing" and self.check_service("kafka"):
            logger.info("Kafka: localhost:9092")
        
        logger.info("="*50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="金融数据服务系统统一启动脚本")
    
    parser.add_argument("--env", choices=["dev", "test", "prod"], default="dev",
                       help="环境类型 (默认: dev)")
    parser.add_argument("--services", nargs="+", 
                       choices=["redis", "clickhouse", "kafka", "financial-app"],
                       help="指定要启动的服务")
    parser.add_argument("--quick", action="store_true",
                       help="快速启动（最小服务集）")
    parser.add_argument("--check", action="store_true",
                       help="检查环境状态")
    parser.add_argument("--stop", action="store_true",
                       help="停止所有服务")
    
    args = parser.parse_args()
    
    # 创建服务管理器
    manager = ServiceManager()
    
    try:
        if args.stop:
            success = manager.stop_environment()
        elif args.check:
            status = manager.check_environment()
            success = all(status.values())
        else:
            success = manager.start_environment(
                env=args.env,
                services=args.services,
                quick=args.quick
            )
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("\n用户中断，正在停止服务...")
        manager.stop_environment()
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
