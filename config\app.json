{"server": {"host": "0.0.0.0", "port": 8080, "threads": 4}, "logging": {"level": "info", "file": "logs/financial_service.log"}, "redis": {"host": "127.0.0.1", "port": 6379, "database": 0, "password": "", "pool_size": 10}, "clickhouse": {"host": "127.0.0.1", "port": 9000, "database": "market_data", "username": "admin", "password": "password123"}, "storage_workers": 4, "enable_batching": true, "enable_migration": true, "performance": {"max_concurrent_clients": 1000}, "monitoring": {"prometheus_port": 9090, "health_check_interval": 30}}