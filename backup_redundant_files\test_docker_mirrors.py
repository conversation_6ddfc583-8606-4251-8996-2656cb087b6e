#!/usr/bin/env python3
"""
Docker镜像源测试脚本
测试国内Docker Hub镜像源的可用性和速度
"""

import asyncio
import subprocess
import time
import json
import sys
import os
from datetime import datetime

# 国内Docker镜像源列表
DOCKER_MIRRORS = [
    "https://docker.1ms.run",
    "https://docker.xuanyuan.me", 
    "https://docker.1panel.live",
    "https://dockerproxy.net",
    "https://cr.laoyou.ip-ddns.com",
    "https://docker.kejilion.pro",
    "https://docker.m.daocloud.io"
]

def print_header():
    print("=" * 60)
    print("    Docker镜像源测试工具")
    print("=" * 60)
    print()

def print_info(msg):
    print(f"[INFO] {msg}")

def print_success(msg):
    print(f"[SUCCESS] ✅ {msg}")

def print_error(msg):
    print(f"[ERROR] ❌ {msg}")

def print_warning(msg):
    print(f"[WARNING] ⚠️  {msg}")

def check_docker():
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print_success(f"Docker已安装: {result.stdout.strip()}")
            return True
        else:
            print_error("Docker未正确安装")
            return False
    except Exception as e:
        print_error(f"Docker检查失败: {e}")
        return False

def check_docker_service():
    """检查Docker服务状态"""
    try:
        result = subprocess.run(['docker', 'info'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success("Docker服务运行正常")
            return True
        else:
            print_error("Docker服务未运行")
            print_info("尝试启动Docker服务: sudo service docker start")
            return False
    except Exception as e:
        print_error(f"Docker服务检查失败: {e}")
        return False

def create_daemon_config(mirrors):
    """创建Docker daemon配置"""
    config = {
        "registry-mirrors": mirrors,
        "log-driver": "json-file",
        "log-opts": {
            "max-size": "100m",
            "max-file": "3"
        },
        "storage-driver": "overlay2",
        "exec-opts": ["native.cgroupdriver=systemd"],
        "live-restore": True
    }
    return json.dumps(config, indent=2)

def apply_docker_config(config_content):
    """应用Docker配置"""
    try:
        # 写入临时文件
        with open('/tmp/daemon.json', 'w') as f:
            f.write(config_content)
        
        # 复制到Docker配置目录
        subprocess.run(['sudo', 'mkdir', '-p', '/etc/docker'], check=True)
        subprocess.run(['sudo', 'cp', '/tmp/daemon.json', '/etc/docker/daemon.json'], check=True)
        
        # 重启Docker服务
        subprocess.run(['sudo', 'systemctl', 'daemon-reload'], check=True)
        subprocess.run(['sudo', 'systemctl', 'restart', 'docker'], check=True)
        
        # 等待服务启动
        time.sleep(3)
        
        return True
    except Exception as e:
        print_error(f"应用Docker配置失败: {e}")
        return False

def test_image_pull(image="hello-world:latest", timeout=30):
    """测试镜像拉取"""
    try:
        # 删除可能存在的镜像
        subprocess.run(['docker', 'rmi', image], 
                      capture_output=True, timeout=5)
        
        # 测试拉取
        start_time = time.time()
        result = subprocess.run(['docker', 'pull', image], 
                              capture_output=True, text=True, timeout=timeout)
        end_time = time.time()
        
        if result.returncode == 0:
            duration = end_time - start_time
            return True, duration
        else:
            return False, 0
            
    except subprocess.TimeoutExpired:
        return False, timeout
    except Exception as e:
        return False, 0

def test_single_mirror(mirror_url):
    """测试单个镜像源"""
    print_info(f"测试镜像源: {mirror_url}")
    
    # 创建单镜像源配置
    config = create_daemon_config([mirror_url])
    
    # 应用配置
    if not apply_docker_config(config):
        return {"url": mirror_url, "status": "配置失败", "time": 0}
    
    # 测试拉取
    success, duration = test_image_pull("hello-world:latest", 30)
    
    if success:
        print_success(f"✅ {mirror_url} 可用 ({duration:.1f}秒)")
        return {"url": mirror_url, "status": "可用", "time": duration}
    else:
        print_error(f"❌ {mirror_url} 不可用")
        return {"url": mirror_url, "status": "不可用", "time": 0}

def test_all_mirrors():
    """测试所有镜像源"""
    print_info("开始测试所有镜像源...")
    print()
    
    results = []
    
    for mirror in DOCKER_MIRRORS:
        result = test_single_mirror(mirror)
        results.append(result)
        
        # 清理测试镜像
        subprocess.run(['docker', 'rmi', 'hello-world:latest'], 
                      capture_output=True)
        
        time.sleep(1)  # 避免请求过快
    
    return results

def configure_best_mirrors(results):
    """配置最佳镜像源"""
    # 筛选可用的镜像源
    available_mirrors = [r for r in results if r["status"] == "可用"]
    
    if not available_mirrors:
        print_error("没有可用的镜像源")
        return False
    
    # 按速度排序
    available_mirrors.sort(key=lambda x: x["time"])
    
    # 选择前5个最快的镜像源
    best_mirrors = [m["url"] for m in available_mirrors[:5]]
    
    print_info(f"配置最佳镜像源 (共{len(best_mirrors)}个):")
    for i, mirror in enumerate(best_mirrors, 1):
        print(f"  {i}. {mirror}")
    
    # 创建配置
    config = create_daemon_config(best_mirrors)
    
    # 应用配置
    if apply_docker_config(config):
        print_success("最佳镜像源配置完成")
        return True
    else:
        print_error("镜像源配置失败")
        return False

def test_final_configuration():
    """测试最终配置"""
    print_info("测试最终配置...")
    
    test_images = [
        "hello-world:latest",
        "alpine:latest", 
        "redis:7-alpine"
    ]
    
    success_count = 0
    
    for image in test_images:
        print_info(f"测试拉取: {image}")
        success, duration = test_image_pull(image, 60)
        
        if success:
            print_success(f"✅ {image} 拉取成功 ({duration:.1f}秒)")
            success_count += 1
        else:
            print_error(f"❌ {image} 拉取失败")
    
    print()
    print_info(f"最终测试结果: {success_count}/{len(test_images)} 成功")
    
    return success_count == len(test_images)

def show_results_summary(results):
    """显示结果汇总"""
    print()
    print("=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    print(f"{'镜像源':<30} {'状态':<10} {'响应时间':<10}")
    print("-" * 60)
    
    available_count = 0
    total_time = 0
    
    for result in results:
        url = result["url"].replace("https://", "")
        status = result["status"]
        time_str = f"{result['time']:.1f}s" if result["time"] > 0 else "N/A"
        
        print(f"{url:<30} {status:<10} {time_str:<10}")
        
        if result["status"] == "可用":
            available_count += 1
            total_time += result["time"]
    
    print("-" * 60)
    print(f"可用镜像源: {available_count}/{len(results)}")
    
    if available_count > 0:
        avg_time = total_time / available_count
        print(f"平均响应时间: {avg_time:.1f}秒")
    
    print()

def cleanup_test_images():
    """清理测试镜像"""
    print_info("清理测试镜像...")
    
    test_images = [
        "hello-world:latest",
        "alpine:latest",
        "redis:7-alpine"
    ]
    
    for image in test_images:
        subprocess.run(['docker', 'rmi', image], 
                      capture_output=True)
    
    print_success("测试镜像清理完成")

def main():
    """主函数"""
    print_header()
    
    # 检查Docker环境
    if not check_docker():
        print_error("请先安装Docker")
        sys.exit(1)
    
    if not check_docker_service():
        print_error("请启动Docker服务: sudo service docker start")
        sys.exit(1)
    
    try:
        # 测试所有镜像源
        results = test_all_mirrors()
        
        # 显示结果汇总
        show_results_summary(results)
        
        # 配置最佳镜像源
        if configure_best_mirrors(results):
            # 测试最终配置
            if test_final_configuration():
                print_success("🎉 Docker镜像源配置完成！")
                print()
                print("✅ 推荐的使用方式:")
                print("  docker pull redis:7-alpine")
                print("  docker pull clickhouse/clickhouse-server:latest")
                print("  docker pull prom/prometheus:latest")
            else:
                print_warning("最终配置测试部分失败，但基本功能可用")
        else:
            print_error("镜像源配置失败")
        
        # 清理测试镜像
        cleanup_test_images()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        cleanup_test_images()
        sys.exit(1)
    except Exception as e:
        print_error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()