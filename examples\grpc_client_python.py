#!/usr/bin/env python3
"""
Python gRPC client example for Financial Data Service
Demonstrates streaming data consumption with flow control and error handling
"""

import grpc
import asyncio
import logging
import time
from typing import AsyncIterator, Optional
from concurrent.futures import ThreadPoolExecutor
import threading

# Import generated protobuf classes (would be generated from .proto files)
# import market_data_service_pb2
# import market_data_service_pb2_grpc

# Mock classes for demonstration (replace with actual generated classes)
class TickDataRequest:
    def __init__(self, symbols=None, exchange="", buffer_size=1000):
        self.symbols = symbols or []
        self.exchange = exchange
        self.buffer_size = buffer_size

class TickDataResponse:
    def __init__(self):
        self.ticks = []
        self.has_more = False
        self.metadata = None

class MarketDataServiceStub:
    def __init__(self, channel):
        self.channel = channel
    
    def StreamTickData(self, request):
        # Mock implementation
        pass

class GrpcClient:
    """High-performance gRPC client with load balancing and failover"""
    
    def __init__(self, server_addresses: list, max_retries: int = 3):
        self.server_addresses = server_addresses
        self.max_retries = max_retries
        self.current_server_index = 0
        self.channels = {}
        self.stubs = {}
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        self._initialize_connections()
    
    def _setup_logging(self):
        """Configure logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def _initialize_connections(self):
        """Initialize gRPC channels and stubs for all servers"""
        for i, address in enumerate(self.server_addresses):
            try:
                # Configure channel options for high performance
                options = [
                    ('grpc.keepalive_time_ms', 30000),
                    ('grpc.keepalive_timeout_ms', 5000),
                    ('grpc.keepalive_permit_without_calls', True),
                    ('grpc.http2.max_pings_without_data', 0),
                    ('grpc.http2.min_time_between_pings_ms', 10000),
                    ('grpc.http2.min_ping_interval_without_data_ms', 300000),
                    ('grpc.max_receive_message_length', 4 * 1024 * 1024),
                    ('grpc.max_send_message_length', 4 * 1024 * 1024),
                ]
                
                channel = grpc.insecure_channel(address, options=options)
                stub = MarketDataServiceStub(channel)
                
                self.channels[i] = channel
                self.stubs[i] = stub
                
                self.logger.info(f"Initialized connection to {address}")
                
            except Exception as e:
                self.logger.error(f"Failed to initialize connection to {address}: {e}")
    
    def _get_current_stub(self):
        """Get current active stub with failover logic"""
        for attempt in range(len(self.server_addresses)):
            try:
                stub = self.stubs[self.current_server_index]
                # Test connection health
                # health_request = HealthCheckRequest(service="MarketDataService")
                # health_response = stub.HealthCheck(health_request, timeout=5)
                return stub
            except Exception as e:
                self.logger.warning(f"Server {self.current_server_index} unavailable: {e}")
                self.current_server_index = (self.current_server_index + 1) % len(self.server_addresses)
        
        raise Exception("All servers unavailable")
    
    async def stream_tick_data(self, symbols: list, exchange: str, 
                              buffer_size: int = 1000) -> AsyncIterator[dict]:
        """Stream real-time tick data with flow control"""
        request = TickDataRequest(
            symbols=symbols,
            exchange=exchange,
            buffer_size=buffer_size
        )
        
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                stub = self._get_current_stub()
                
                self.logger.info(f"Starting tick data stream for symbols: {symbols}")
                
                # Create streaming call
                response_stream = stub.StreamTickData(request)
                
                async for response in response_stream:
                    # Process each tick data response
                    for tick in response.ticks:
                        yield {
                            'timestamp': tick.timestamp,
                            'symbol': tick.symbol,
                            'exchange': tick.exchange,
                            'last_price': tick.last_price,
                            'volume': tick.volume,
                            'turnover': tick.turnover,
                            'bids': [{'price': bid.price, 'volume': bid.volume} 
                                   for bid in tick.bids],
                            'asks': [{'price': ask.price, 'volume': ask.volume} 
                                   for ask in tick.asks],
                            'sequence': tick.sequence,
                            'server_timestamp': response.metadata.server_timestamp,
                            'processing_latency': response.metadata.processing_latency_us
                        }
                
                break  # Success, exit retry loop
                
            except grpc.RpcError as e:
                retry_count += 1
                self.logger.error(f"gRPC error (attempt {retry_count}): {e}")
                
                if e.code() == grpc.StatusCode.UNAVAILABLE:
                    # Server unavailable, try next server
                    self.current_server_index = (self.current_server_index + 1) % len(self.server_addresses)
                    await asyncio.sleep(1)  # Brief delay before retry
                else:
                    # Other errors, don't retry
                    break
            
            except Exception as e:
                retry_count += 1
                self.logger.error(f"Unexpected error (attempt {retry_count}): {e}")
                await asyncio.sleep(1)
        
        if retry_count >= self.max_retries:
            raise Exception(f"Failed to establish stream after {self.max_retries} retries")
    
    async def stream_kline_data(self, symbols: list, exchange: str, 
                               period: str, buffer_size: int = 1000) -> AsyncIterator[dict]:
        """Stream K-line data"""
        # Mock KlineDataRequest class
        class KlineDataRequest:
            def __init__(self, symbols, exchange, period, buffer_size):
                self.symbols = symbols
                self.exchange = exchange
                self.period = period
                self.buffer_size = buffer_size
        
        request = KlineDataRequest(symbols, exchange, period, buffer_size)
        
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                stub = self._get_current_stub()
                
                self.logger.info(f"Starting kline data stream for symbols: {symbols}, period: {period}")
                
                # Mock streaming call
                # response_stream = stub.StreamKlineData(request)
                
                # Mock response for demonstration
                for i in range(10):  # Mock 10 kline responses
                    yield {
                        'symbol': symbols[0] if symbols else 'MOCK',
                        'exchange': exchange,
                        'period': period,
                        'timestamp': int(time.time() * 1000000000),
                        'open': 50000.0 + i,
                        'high': 50100.0 + i,
                        'low': 49900.0 + i,
                        'close': 50050.0 + i,
                        'volume': 1000 + i * 10,
                        'turnover': 50000000.0 + i * 1000000
                    }
                    await asyncio.sleep(1)  # Mock delay
                
                break  # Success, exit retry loop
                
            except Exception as e:
                retry_count += 1
                self.logger.error(f"Kline stream error (attempt {retry_count}): {e}")
                if retry_count < self.max_retries:
                    await asyncio.sleep(1)
        
        if retry_count >= self.max_retries:
            raise Exception(f"Failed to establish kline stream after {self.max_retries} retries")
    
    async def stream_level2_data(self, symbols: list, exchange: str, 
                                depth: int = 10, buffer_size: int = 1000) -> AsyncIterator[dict]:
        """Stream Level-2 market depth data"""
        # Mock Level2DataRequest class
        class Level2DataRequest:
            def __init__(self, symbols, exchange, depth, buffer_size):
                self.symbols = symbols
                self.exchange = exchange
                self.depth = depth
                self.buffer_size = buffer_size
        
        request = Level2DataRequest(symbols, exchange, depth, buffer_size)
        
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                stub = self._get_current_stub()
                
                self.logger.info(f"Starting level2 data stream for symbols: {symbols}, depth: {depth}")
                
                # Mock streaming call
                # response_stream = stub.StreamLevel2Data(request)
                
                # Mock response for demonstration
                for i in range(100):  # Mock 100 level2 responses
                    bids = [{'price': 50000.0 - j, 'volume': 10 + j} for j in range(depth)]
                    asks = [{'price': 50001.0 + j, 'volume': 10 + j} for j in range(depth)]
                    
                    yield {
                        'timestamp': int(time.time() * 1000000000),
                        'symbol': symbols[0] if symbols else 'MOCK',
                        'exchange': exchange,
                        'bids': bids,
                        'asks': asks,
                        'sequence': i
                    }
                    await asyncio.sleep(0.1)  # Mock high frequency
                
                break  # Success, exit retry loop
                
            except Exception as e:
                retry_count += 1
                self.logger.error(f"Level2 stream error (attempt {retry_count}): {e}")
                if retry_count < self.max_retries:
                    await asyncio.sleep(1)
        
        if retry_count >= self.max_retries:
            raise Exception(f"Failed to establish level2 stream after {self.max_retries} retries")
    
    def close(self):
        """Close all gRPC channels"""
        for channel in self.channels.values():
            channel.close()
        self.logger.info("All gRPC channels closed")

class FlowControlledConsumer:
    """Consumer with built-in flow control and backpressure handling"""
    
    def __init__(self, client: GrpcClient, buffer_size: int = 1000):
        self.client = client
        self.buffer_size = buffer_size
        self.message_queue = asyncio.Queue(maxsize=buffer_size)
        self.processing_stats = {
            'messages_received': 0,
            'messages_processed': 0,
            'processing_errors': 0,
            'avg_latency_us': 0.0
        }
        self.logger = logging.getLogger(__name__)
    
    async def consume_tick_data(self, symbols: list, exchange: str, 
                               message_handler=None):
        """Consume tick data with flow control"""
        
        async def producer():
            """Producer coroutine that feeds the queue"""
            try:
                async for tick_data in self.client.stream_tick_data(
                    symbols, exchange, self.buffer_size):
                    await self.message_queue.put(tick_data)
                    self.processing_stats['messages_received'] += 1
            except Exception as e:
                self.logger.error(f"Producer error: {e}")
                await self.message_queue.put(None)  # Signal end
        
        async def consumer():
            """Consumer coroutine that processes messages"""
            while True:
                try:
                    # Get message from queue with timeout
                    tick_data = await asyncio.wait_for(
                        self.message_queue.get(), timeout=30.0)
                    
                    if tick_data is None:  # End signal
                        break
                    
                    # Process message
                    start_time = time.time()
                    
                    if message_handler:
                        await message_handler(tick_data)
                    else:
                        # Default processing
                        self.logger.info(f"Received tick: {tick_data['symbol']} @ {tick_data['last_price']}")
                    
                    # Update stats
                    processing_time = (time.time() - start_time) * 1000000  # microseconds
                    self.processing_stats['messages_processed'] += 1
                    self.processing_stats['avg_latency_us'] = (
                        (self.processing_stats['avg_latency_us'] * 
                         (self.processing_stats['messages_processed'] - 1) + processing_time) /
                        self.processing_stats['messages_processed']
                    )
                    
                    # Mark task as done
                    self.message_queue.task_done()
                    
                except asyncio.TimeoutError:
                    self.logger.warning("Consumer timeout, no messages received")
                    break
                except Exception as e:
                    self.logger.error(f"Consumer error: {e}")
                    self.processing_stats['processing_errors'] += 1
        
        # Run producer and consumer concurrently
        await asyncio.gather(producer(), consumer())
    
    def get_stats(self) -> dict:
        """Get processing statistics"""
        return self.processing_stats.copy()

async def main():
    """Example usage of the gRPC client"""
    
    # Configure multiple server addresses for load balancing
    server_addresses = [
        'localhost:50051',
        'localhost:50052',
        'localhost:50053'
    ]
    
    # Create client with failover support
    client = GrpcClient(server_addresses, max_retries=3)
    
    try:
        # Create flow-controlled consumer
        consumer = FlowControlledConsumer(client, buffer_size=1000)
        
        # Define custom message handler
        async def handle_tick_data(tick_data):
            """Custom tick data handler"""
            print(f"Processing tick: {tick_data['symbol']} @ {tick_data['last_price']} "
                  f"(latency: {tick_data['processing_latency']:.2f}μs)")
            
            # Add your custom processing logic here
            # e.g., store to database, trigger alerts, etc.
        
        # Start consuming tick data
        symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        exchange = 'binance'
        
        print(f"Starting to consume tick data for {symbols} from {exchange}")
        
        # Run consumer
        await consumer.consume_tick_data(symbols, exchange, handle_tick_data)
        
        # Print final statistics
        stats = consumer.get_stats()
        print(f"Processing completed. Stats: {stats}")
        
    except KeyboardInterrupt:
        print("Interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())