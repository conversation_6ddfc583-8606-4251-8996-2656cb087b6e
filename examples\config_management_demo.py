#!/usr/bin/env python3
"""
配置管理系统演示
演示如何使用统一配置管理框架
"""

import os
import sys
import time
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config.config_manager_python import (
    PythonConfigManager, ConfigChangeListener, ConfigChangeEvent,
    initialize_config, get_config, set_config, get_section
)
from config.python_validators import (
    PytdxConfigValidator, CollectionConfigValidator,
    StorageConfigValidator, MonitoringConfigValidator
)


class DemoConfigListener(ConfigChangeListener):
    """演示配置变更监听器"""
    
    def on_config_changed(self, event: ConfigChangeEvent):
        print(f"配置变更通知:")
        print(f"  类型: {event.type.value}")
        print(f"  节: {event.section}")
        print(f"  键: {event.key}")
        print(f"  旧值: {event.old_value}")
        print(f"  新值: {event.new_value}")
        print(f"  时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(event.timestamp))}")
        print("-" * 50)


def main():
    """主演示函数"""
    print("=== 配置管理系统演示 ===\n")
    
    # 1. 初始化配置管理器
    print("1. 初始化配置管理器")
    config_file = "config/unified_config.json"
    
    if not os.path.exists(config_file):
        print(f"配置文件不存在: {config_file}")
        return
    
    success = initialize_config(config_file)
    print(f"初始化结果: {'成功' if success else '失败'}")
    
    if not success:
        return
    
    config_manager = PythonConfigManager()
    
    # 2. 注册配置验证器
    print("\n2. 注册配置验证器")
    validators = {
        "collection": CollectionConfigValidator(),
        "storage": StorageConfigValidator(),
        "monitoring": MonitoringConfigValidator()
    }
    
    for section, validator in validators.items():
        config_manager.register_validator(section, validator)
        print(f"已注册验证器: {validator.get_validator_name()}")
    
    # 3. 验证配置
    print("\n3. 验证配置")
    validation_result = config_manager.validate_config()
    print(f"配置验证结果: {'通过' if validation_result.is_valid else '失败'}")
    
    if validation_result.errors:
        print("验证错误:")
        for error in validation_result.errors:
            print(f"  - {error}")
    
    if validation_result.warnings:
        print("验证警告:")
        for warning in validation_result.warnings:
            print(f"  - {warning}")
    
    # 4. 读取配置值
    print("\n4. 读取配置值")
    server_host = get_config("server.host", "localhost")
    server_port = get_config("server.port", 8080)
    redis_config = get_section("storage.hot_storage.config")
    
    print(f"服务器主机: {server_host}")
    print(f"服务器端口: {server_port}")
    print(f"Redis配置: {json.dumps(redis_config, indent=2, ensure_ascii=False)}")
    
    # 5. 注册配置变更监听器
    print("\n5. 注册配置变更监听器")
    listener = DemoConfigListener()
    config_manager.register_change_listener(listener)
    print("已注册配置变更监听器")
    
    # 6. 修改配置值
    print("\n6. 修改配置值")
    print("修改服务器端口从 8080 到 9090...")
    set_config("server.port", 9090)
    
    print("添加新的配置项...")
    set_config("demo.test_key", "test_value")
    
    # 7. 创建配置快照
    print("\n7. 创建配置快照")
    snapshot_id = config_manager.create_snapshot("演示快照")
    print(f"创建快照: {snapshot_id}")
    
    # 8. 继续修改配置
    print("\n8. 继续修改配置")
    set_config("server.port", 8888)
    set_config("demo.another_key", "another_value")
    
    print(f"当前服务器端口: {get_config('server.port')}")
    
    # 9. 恢复快照
    print("\n9. 恢复配置快照")
    restore_success = config_manager.restore_from_snapshot(snapshot_id)
    print(f"恢复快照结果: {'成功' if restore_success else '失败'}")
    print(f"恢复后服务器端口: {get_config('server.port')}")
    
    # 10. 查看版本历史
    print("\n10. 查看版本历史")
    history = config_manager.get_version_history()
    print(f"版本历史数量: {len(history)}")
    for version in history:
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(version['timestamp']))
        print(f"  版本: {version['version_id']}")
        print(f"  时间: {timestamp}")
        print(f"  描述: {version['description']}")
        print(f"  校验和: {version['checksum'][:16]}...")
        print()
    
    # 11. 启用热更新
    print("\n11. 启用热更新")
    config_manager.enable_hot_reload(True)
    config_manager.set_file_watch_interval(1.0)  # 1秒检查间隔
    print("已启用热更新，监控间隔: 1秒")
    print("您可以修改配置文件，系统会自动重新加载配置")
    
    # 12. 环境变量支持
    print("\n12. 环境变量支持")
    config_manager.enable_environment_variables(True)
    config_manager.set_environment_prefix("MARKET_DATA_")
    
    # 设置环境变量进行测试
    os.environ["TEST_VALUE"] = "from_environment"
    os.environ["MARKET_DATA_DEBUG"] = "true"
    
    # 添加包含环境变量的配置
    set_config("demo.env_test", "${TEST_VALUE}")
    set_config("demo.prefixed_env", "${DEBUG}")
    
    print(f"环境变量测试: {get_config('demo.env_test')}")
    print(f"前缀环境变量测试: {get_config('demo.prefixed_env')}")
    
    # 13. 配置统计信息
    print("\n13. 配置统计信息")
    stats = config_manager.get_statistics()
    print(f"总键数: {stats['total_keys']}")
    print(f"总节数: {stats['total_sections']}")
    print(f"变更次数: {stats['change_count']}")
    print(f"验证次数: {stats['validation_count']}")
    print(f"错误次数: {stats['error_count']}")
    
    last_loaded = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stats['last_loaded']))
    last_modified = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stats['last_modified']))
    print(f"最后加载时间: {last_loaded}")
    print(f"最后修改时间: {last_modified}")
    
    # 14. 导出配置
    print("\n14. 导出配置")
    export_file = "demo_exported_config.json"
    export_success = config_manager.export_to_file(export_file, True)
    print(f"导出配置到 {export_file}: {'成功' if export_success else '失败'}")
    
    if export_success and os.path.exists(export_file):
        file_size = os.path.getsize(export_file)
        print(f"导出文件大小: {file_size} 字节")
        
        # 清理导出文件
        os.remove(export_file)
        print("已清理导出文件")
    
    # 15. 清理
    print("\n15. 清理资源")
    config_manager.unregister_change_listener(listener)
    config_manager.enable_hot_reload(False)
    
    # 清理环境变量
    if "TEST_VALUE" in os.environ:
        del os.environ["TEST_VALUE"]
    if "MARKET_DATA_DEBUG" in os.environ:
        del os.environ["MARKET_DATA_DEBUG"]
    
    print("演示完成！")


if __name__ == "__main__":
    main()