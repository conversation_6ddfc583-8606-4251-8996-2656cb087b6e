"""
Data models for financial market data with pandas/numpy integration
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import json


@dataclass
class TickData:
    """Tick data model with pandas/numpy support"""
    timestamp: int  # nanoseconds
    symbol: str
    exchange: str
    last_price: float
    volume: int
    turnover: float
    open_interest: Optional[int] = None
    bid_prices: Optional[List[float]] = None
    bid_volumes: Optional[List[int]] = None
    ask_prices: Optional[List[float]] = None
    ask_volumes: Optional[List[int]] = None
    sequence: Optional[int] = None
    trade_flag: Optional[str] = None
    
    @property
    def datetime(self) -> datetime:
        """Convert timestamp to datetime"""
        return datetime.fromtimestamp(self.timestamp / 1_000_000_000)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        return asdict(self)
    
    def to_series(self) -> pd.Series:
        """Convert to pandas Series"""
        return pd.Series(self.to_dict())
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TickData':
        """Create from dictionary"""
        return cls(**data)
    
    @classmethod
    def from_protobuf(cls, pb_tick) -> 'TickData':
        """Create from protobuf message"""
        return cls(
            timestamp=pb_tick.timestamp,
            symbol=pb_tick.symbol,
            exchange=pb_tick.exchange,
            last_price=pb_tick.last_price,
            volume=pb_tick.volume,
            turnover=pb_tick.turnover,
            open_interest=pb_tick.open_interest if pb_tick.HasField('open_interest') else None,
            bid_prices=list(pb_tick.bid_prices) if pb_tick.bid_prices else None,
            bid_volumes=list(pb_tick.bid_volumes) if pb_tick.bid_volumes else None,
            ask_prices=list(pb_tick.ask_prices) if pb_tick.ask_prices else None,
            ask_volumes=list(pb_tick.ask_volumes) if pb_tick.ask_volumes else None,
            sequence=pb_tick.sequence if pb_tick.HasField('sequence') else None,
            trade_flag=pb_tick.trade_flag if pb_tick.trade_flag else None
        )


@dataclass
class KlineData:
    """K-line/candlestick data model"""
    timestamp: int  # nanoseconds
    symbol: str
    exchange: str
    period: str  # 1m, 5m, 1h, 1d, etc.
    open: float
    high: float
    low: float
    close: float
    volume: int
    turnover: float
    open_interest: Optional[int] = None
    
    @property
    def datetime(self) -> datetime:
        """Convert timestamp to datetime"""
        return datetime.fromtimestamp(self.timestamp / 1_000_000_000)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        return asdict(self)
    
    def to_series(self) -> pd.Series:
        """Convert to pandas Series"""
        return pd.Series(self.to_dict())
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'KlineData':
        """Create from dictionary"""
        return cls(**data)
    
    @classmethod
    def from_protobuf(cls, pb_kline) -> 'KlineData':
        """Create from protobuf message"""
        return cls(
            timestamp=pb_kline.timestamp,
            symbol=pb_kline.symbol,
            exchange=pb_kline.exchange,
            period=pb_kline.period,
            open=pb_kline.open,
            high=pb_kline.high,
            low=pb_kline.low,
            close=pb_kline.close,
            volume=pb_kline.volume,
            turnover=pb_kline.turnover,
            open_interest=pb_kline.open_interest if pb_kline.HasField('open_interest') else None
        )


@dataclass
class Level2Data:
    """Level 2 market depth data model"""
    timestamp: int  # nanoseconds
    symbol: str
    exchange: str
    bid_prices: List[float]
    bid_volumes: List[int]
    ask_prices: List[float]
    ask_volumes: List[int]
    sequence: Optional[int] = None
    
    @property
    def datetime(self) -> datetime:
        """Convert timestamp to datetime"""
        return datetime.fromtimestamp(self.timestamp / 1_000_000_000)
    
    @property
    def bid_depth(self) -> int:
        """Number of bid levels"""
        return len(self.bid_prices)
    
    @property
    def ask_depth(self) -> int:
        """Number of ask levels"""
        return len(self.ask_prices)
    
    @property
    def spread(self) -> float:
        """Best bid-ask spread"""
        if self.ask_prices and self.bid_prices:
            return self.ask_prices[0] - self.bid_prices[0]
        return 0.0
    
    @property
    def mid_price(self) -> float:
        """Mid price between best bid and ask"""
        if self.ask_prices and self.bid_prices:
            return (self.ask_prices[0] + self.bid_prices[0]) / 2.0
        return 0.0
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        return asdict(self)
    
    def to_series(self) -> pd.Series:
        """Convert to pandas Series"""
        return pd.Series(self.to_dict())
    
    def to_dataframe(self) -> pd.DataFrame:
        """Convert to pandas DataFrame with bid/ask levels"""
        max_depth = max(len(self.bid_prices), len(self.ask_prices))
        
        data = {
            'level': list(range(1, max_depth + 1)),
            'bid_price': self.bid_prices + [np.nan] * (max_depth - len(self.bid_prices)),
            'bid_volume': self.bid_volumes + [0] * (max_depth - len(self.bid_volumes)),
            'ask_price': self.ask_prices + [np.nan] * (max_depth - len(self.ask_prices)),
            'ask_volume': self.ask_volumes + [0] * (max_depth - len(self.ask_volumes))
        }
        
        return pd.DataFrame(data)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Level2Data':
        """Create from dictionary"""
        return cls(**data)
    
    @classmethod
    def from_protobuf(cls, pb_level2) -> 'Level2Data':
        """Create from protobuf message"""
        return cls(
            timestamp=pb_level2.timestamp,
            symbol=pb_level2.symbol,
            exchange=pb_level2.exchange,
            bid_prices=list(pb_level2.bid_prices),
            bid_volumes=list(pb_level2.bid_volumes),
            ask_prices=list(pb_level2.ask_prices),
            ask_volumes=list(pb_level2.ask_volumes),
            sequence=pb_level2.sequence if pb_level2.HasField('sequence') else None
        )


class DataFrameBuilder:
    """Helper class to build pandas DataFrames from market data"""
    
    @staticmethod
    def from_tick_list(ticks: List[TickData]) -> pd.DataFrame:
        """Create DataFrame from list of tick data"""
        if not ticks:
            return pd.DataFrame()
        
        data = [tick.to_dict() for tick in ticks]
        df = pd.DataFrame(data)
        
        # Convert timestamp to datetime index
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ns')
        df.set_index('datetime', inplace=True)
        
        return df
    
    @staticmethod
    def from_kline_list(klines: List[KlineData]) -> pd.DataFrame:
        """Create DataFrame from list of kline data"""
        if not klines:
            return pd.DataFrame()
        
        data = [kline.to_dict() for kline in klines]
        df = pd.DataFrame(data)
        
        # Convert timestamp to datetime index
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ns')
        df.set_index('datetime', inplace=True)
        
        return df
    
    @staticmethod
    def from_level2_list(level2_data: List[Level2Data]) -> pd.DataFrame:
        """Create DataFrame from list of level2 data"""
        if not level2_data:
            return pd.DataFrame()
        
        data = []
        for l2 in level2_data:
            base_data = {
                'timestamp': l2.timestamp,
                'datetime': l2.datetime,
                'symbol': l2.symbol,
                'exchange': l2.exchange,
                'spread': l2.spread,
                'mid_price': l2.mid_price
            }
            
            # Add bid/ask data
            for i, (bid_price, bid_vol) in enumerate(zip(l2.bid_prices, l2.bid_volumes)):
                base_data[f'bid_price_{i+1}'] = bid_price
                base_data[f'bid_volume_{i+1}'] = bid_vol
            
            for i, (ask_price, ask_vol) in enumerate(zip(l2.ask_prices, l2.ask_volumes)):
                base_data[f'ask_price_{i+1}'] = ask_price
                base_data[f'ask_volume_{i+1}'] = ask_vol
            
            data.append(base_data)
        
        df = pd.DataFrame(data)
        df.set_index('datetime', inplace=True)
        
        return df


class NumpyArrayBuilder:
    """Helper class to build numpy arrays from market data"""
    
    @staticmethod
    def from_tick_list(ticks: List[TickData], fields: List[str] = None) -> np.ndarray:
        """Create numpy array from list of tick data"""
        if not ticks:
            return np.array([])
        
        if fields is None:
            fields = ['timestamp', 'last_price', 'volume', 'turnover']
        
        data = []
        for tick in ticks:
            tick_dict = tick.to_dict()
            row = [tick_dict.get(field, 0) for field in fields]
            data.append(row)
        
        return np.array(data)
    
    @staticmethod
    def from_kline_list(klines: List[KlineData], fields: List[str] = None) -> np.ndarray:
        """Create numpy array from list of kline data"""
        if not klines:
            return np.array([])
        
        if fields is None:
            fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        
        data = []
        for kline in klines:
            kline_dict = kline.to_dict()
            row = [kline_dict.get(field, 0) for field in fields]
            data.append(row)
        
        return np.array(data)
    
    @staticmethod
    def ohlcv_array(klines: List[KlineData]) -> np.ndarray:
        """Create OHLCV numpy array optimized for technical analysis"""
        if not klines:
            return np.array([])
        
        data = []
        for kline in klines:
            data.append([kline.open, kline.high, kline.low, kline.close, kline.volume])
        
        return np.array(data)