/**
 * @file latency_test.cpp
 * @brief Latency testing implementation for financial data service
 */

#include "latency_test.h"
#include "test_utils.h"
#include <chrono>
#include <thread>
#include <vector>
#include <algorithm>
#include <numeric>
#include <iostream>
#include <future>
#include <random>

namespace performance_tests {

LatencyTest::LatencyTest() : test_utils_(std::make_unique<TestUtils>()) {}

LatencyTest::~LatencyTest() = default;

LatencyResult LatencyTest::TestEndToEndLatency() {
    std::cout << "    Testing end-to-end latency..." << std::endl;
    
    const uint32_t num_samples = 10000;
    const uint32_t warmup_samples = 1000;
    std::vector<double> latencies;
    latencies.reserve(num_samples);
    
    // Setup test environment
    auto mock_server = test_utils_->CreateMockMarketDataServer();
    auto test_client = test_utils_->CreateTestClient();
    
    // Warmup phase
    for (uint32_t i = 0; i < warmup_samples; ++i) {
        auto tick_data = test_utils_->GenerateTestTick();
        auto start_time = std::chrono::high_resolution_clock::now();
        
        mock_server->PublishTick(tick_data);
        auto received_tick = test_client->WaitForTick(std::chrono::milliseconds(100));
        
        auto end_time = std::chrono::high_resolution_clock::now();
        // Warmup, don't record
    }
    
    // Actual measurement phase
    for (uint32_t i = 0; i < num_samples; ++i) {
        auto tick_data = test_utils_->GenerateTestTick();
        
        // Record timestamp just before publishing
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Publish tick data
        mock_server->PublishTick(tick_data);
        
        // Wait for client to receive the data
        auto received_tick = test_client->WaitForTick(std::chrono::milliseconds(100));
        
        // Record timestamp when received
        auto end_time = std::chrono::high_resolution_clock::now();
        
        if (received_tick.has_value()) {
            auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                end_time - start_time).count();
            double latency_us = static_cast<double>(latency_ns) / 1000.0;
            latencies.push_back(latency_us);
        }
        
        // Small delay between samples to avoid overwhelming the system
        if (i % 100 == 0) {
            std::this_thread::sleep_for(std::chrono::microseconds(10));
        }
    }
    
    return CalculateLatencyStatistics(latencies);
}

LatencyResult LatencyTest::TestWebSocketLatency() {
    std::cout << "    Testing WebSocket latency..." << std::endl;
    
    const uint32_t num_samples = 5000;
    std::vector<double> latencies;
    latencies.reserve(num_samples);
    
    // Setup WebSocket test environment
    auto ws_server = test_utils_->CreateMockWebSocketServer();
    auto ws_client = test_utils_->CreateWebSocketTestClient();
    
    // Connect client
    ws_client->Connect("ws://localhost:8080/market-data");
    ws_client->Subscribe({"CU2409", "AL2409", "ZN2409"});
    
    // Measure WebSocket message latency
    for (uint32_t i = 0; i < num_samples; ++i) {
        auto tick_data = test_utils_->GenerateTestTick();
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Send via WebSocket
        ws_server->BroadcastTick(tick_data);
        
        // Wait for WebSocket message
        auto received_message = ws_client->WaitForMessage(std::chrono::milliseconds(50));
        
        auto end_time = std::chrono::high_resolution_clock::now();
        
        if (received_message.has_value()) {
            auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                end_time - start_time).count();
            double latency_us = static_cast<double>(latency_ns) / 1000.0;
            latencies.push_back(latency_us);
        }
    }
    
    return CalculateLatencyStatistics(latencies);
}

LatencyResult LatencyTest::TestGrpcLatency() {
    std::cout << "    Testing gRPC latency..." << std::endl;
    
    const uint32_t num_samples = 3000;
    std::vector<double> latencies;
    latencies.reserve(num_samples);
    
    // Setup gRPC test environment
    auto grpc_server = test_utils_->CreateMockGrpcServer();
    auto grpc_client = test_utils_->CreateGrpcTestClient();
    
    // Test streaming gRPC latency
    auto stream = grpc_client->OpenTickStream({"CU2409", "AL2409"});
    
    for (uint32_t i = 0; i < num_samples; ++i) {
        auto tick_data = test_utils_->GenerateTestTick();
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Send via gRPC stream
        grpc_server->StreamTick(tick_data);
        
        // Wait for gRPC response
        auto received_tick = stream->WaitForTick(std::chrono::milliseconds(100));
        
        auto end_time = std::chrono::high_resolution_clock::now();
        
        if (received_tick.has_value()) {
            auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                end_time - start_time).count();
            double latency_us = static_cast<double>(latency_ns) / 1000.0;
            latencies.push_back(latency_us);
        }
    }
    
    return CalculateLatencyStatistics(latencies);
}

LatencyResult LatencyTest::TestRestApiLatency() {
    std::cout << "    Testing REST API latency..." << std::endl;
    
    const uint32_t num_samples = 1000;
    std::vector<double> latencies;
    latencies.reserve(num_samples);
    
    // Setup REST API test environment
    auto api_server = test_utils_->CreateMockRestApiServer();
    auto http_client = test_utils_->CreateHttpTestClient();
    
    // Test various REST API endpoints
    std::vector<std::string> endpoints = {
        "/api/v1/ticks/latest/CU2409",
        "/api/v1/klines/CU2409?period=1m&limit=100",
        "/api/v1/depth/CU2409"
    };
    
    for (uint32_t i = 0; i < num_samples; ++i) {
        const auto& endpoint = endpoints[i % endpoints.size()];
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Make HTTP request
        auto response = http_client->Get(endpoint);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        
        if (response.status_code == 200) {
            auto latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                end_time - start_time).count();
            double latency_us = static_cast<double>(latency_ns) / 1000.0;
            latencies.push_back(latency_us);
        }
        
        // Rate limiting to avoid overwhelming the server
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    return CalculateLatencyStatistics(latencies);
}

LatencyResult LatencyTest::TestStorageLatency() {
    std::cout << "    Testing storage latency..." << std::endl;
    
    const uint32_t num_samples = 2000;
    std::vector<double> write_latencies;
    std::vector<double> read_latencies;
    write_latencies.reserve(num_samples);
    read_latencies.reserve(num_samples);
    
    // Setup storage test environment
    auto redis_client = test_utils_->CreateRedisTestClient();
    auto clickhouse_client = test_utils_->CreateClickHouseTestClient();
    
    // Test Redis write/read latency
    for (uint32_t i = 0; i < num_samples / 2; ++i) {
        auto tick_data = test_utils_->GenerateTestTick();
        
        // Test write latency
        auto write_start = std::chrono::high_resolution_clock::now();
        redis_client->StoreTick(tick_data);
        auto write_end = std::chrono::high_resolution_clock::now();
        
        auto write_latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            write_end - write_start).count();
        write_latencies.push_back(static_cast<double>(write_latency_ns) / 1000.0);
        
        // Test read latency
        auto read_start = std::chrono::high_resolution_clock::now();
        auto retrieved_tick = redis_client->GetLatestTick(tick_data.symbol);
        auto read_end = std::chrono::high_resolution_clock::now();
        
        if (retrieved_tick.has_value()) {
            auto read_latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                read_end - read_start).count();
            read_latencies.push_back(static_cast<double>(read_latency_ns) / 1000.0);
        }
    }
    
    // Test ClickHouse query latency
    for (uint32_t i = 0; i < num_samples / 2; ++i) {
        auto query_start = std::chrono::high_resolution_clock::now();
        
        auto historical_data = clickhouse_client->QueryHistoricalTicks(
            "CU2409", 
            std::chrono::system_clock::now() - std::chrono::hours(1),
            std::chrono::system_clock::now(),
            100
        );
        
        auto query_end = std::chrono::high_resolution_clock::now();
        
        if (!historical_data.empty()) {
            auto query_latency_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                query_end - query_start).count();
            read_latencies.push_back(static_cast<double>(query_latency_ns) / 1000.0);
        }
    }
    
    // Combine read and write latencies for overall storage latency
    std::vector<double> combined_latencies;
    combined_latencies.insert(combined_latencies.end(), write_latencies.begin(), write_latencies.end());
    combined_latencies.insert(combined_latencies.end(), read_latencies.begin(), read_latencies.end());
    
    return CalculateLatencyStatistics(combined_latencies);
}

LatencyResult LatencyTest::CalculateLatencyStatistics(const std::vector<double>& latencies) {
    if (latencies.empty()) {
        return LatencyResult{};
    }
    
    LatencyResult result;
    result.sample_count = latencies.size();
    
    // Sort for percentile calculations
    std::vector<double> sorted_latencies = latencies;
    std::sort(sorted_latencies.begin(), sorted_latencies.end());
    
    // Calculate mean
    result.mean_latency_us = std::accumulate(sorted_latencies.begin(), sorted_latencies.end(), 0.0) 
                           / sorted_latencies.size();
    
    // Calculate percentiles
    result.p50_latency_us = sorted_latencies[sorted_latencies.size() * 50 / 100];
    result.p95_latency_us = sorted_latencies[sorted_latencies.size() * 95 / 100];
    result.p99_latency_us = sorted_latencies[sorted_latencies.size() * 99 / 100];
    result.max_latency_us = sorted_latencies.back();
    
    return result;
}

} // namespace performance_tests