#pragma once

#include "cold_storage.hpp"
#include <string>
#include <vector>
#include <memory>
#include <future>
#include <chrono>
#include <functional>

namespace financial_data {
namespace storage {

// 查询条件结构
struct QueryCondition {
    std::vector<std::string> symbols;
    std::vector<std::string> exchanges;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    
    // 可选过滤条件
    std::optional<double> min_price;
    std::optional<double> max_price;
    std::optional<int64_t> min_volume;
    std::optional<int64_t> max_volume;
    
    // 分页参数
    size_t limit = 0;  // 0表示无限制
    size_t offset = 0;
    std::string cursor;  // 用于游标分页
    
    // 排序参数
    enum SortField {
        TIMESTAMP,
        SYMBOL,
        PRICE,
        VOLUME
    } sort_field = TIMESTAMP;
    
    enum SortOrder {
        ASC,
        DESC
    } sort_order = ASC;
};

// 查询结果结构
struct QueryResult {
    TickDataBatch data;
    size_t total_count = 0;
    bool has_more = false;
    std::string next_cursor;
    std::chrono::milliseconds query_time{0};
    std::string query_id;
    
    // 统计信息
    struct Stats {
        size_t files_scanned = 0;
        size_t records_scanned = 0;
        size_t records_filtered = 0;
        size_t bytes_read = 0;
        std::chrono::milliseconds io_time{0};
        std::chrono::milliseconds filter_time{0};
    } stats;
};

// 批量查询请求
struct BulkQueryRequest {
    std::string request_id;
    std::vector<QueryCondition> conditions;
    std::string output_format = "parquet";  // parquet, csv, json
    std::string compression = "zstd";       // zstd, gzip, none
    bool async_mode = true;
    std::string callback_url;  // 异步模式下的回调URL
    
    // 输出选项
    struct OutputOptions {
        bool include_metadata = true;
        bool split_by_symbol = false;
        bool split_by_date = false;
        size_t max_file_size = 1024 * 1024 * 1024; // 1GB
    } output_options;
};

// 批量查询结果
struct BulkQueryResult {
    std::string request_id;
    std::string status;  // pending, running, completed, failed
    double progress = 0.0;
    std::vector<std::string> output_files;
    std::string error_message;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point completed_at;
    
    struct Summary {
        size_t total_records = 0;
        size_t total_files = 0;
        size_t total_size = 0;
        std::chrono::milliseconds total_time{0};
    } summary;
};

// 归档接口
class ArchiveInterface {
public:
    explicit ArchiveInterface(std::shared_ptr<ColdDataStorage> cold_storage);
    ~ArchiveInterface();
    
    // 单次查询接口
    std::future<QueryResult> Query(const QueryCondition& condition);
    
    // 流式查询接口（适合大数据量查询）
    class QueryStream {
    public:
        virtual ~QueryStream() = default;
        virtual std::future<TickDataBatch> GetNextBatch() = 0;
        virtual bool HasMore() const = 0;
        virtual void Close() = 0;
        virtual QueryResult::Stats GetStats() const = 0;
    };
    
    std::unique_ptr<QueryStream> CreateQueryStream(const QueryCondition& condition);
    
    // 批量查询接口
    std::future<std::string> SubmitBulkQuery(const BulkQueryRequest& request);
    BulkQueryResult GetBulkQueryStatus(const std::string& request_id);
    bool CancelBulkQuery(const std::string& request_id);
    std::vector<BulkQueryResult> ListBulkQueries();
    
    // 数据导出接口
    std::future<std::string> ExportToFile(const QueryCondition& condition,
                                          const std::string& file_path,
                                          const std::string& format = "parquet");
    
    std::future<std::vector<std::string>> ExportToFiles(const QueryCondition& condition,
                                                        const std::string& output_dir,
                                                        const std::string& format = "parquet",
                                                        size_t max_file_size = 1024 * 1024 * 1024);
    
    // 数据统计接口
    struct DataStatistics {
        size_t total_records = 0;
        size_t total_files = 0;
        size_t total_size = 0;
        std::chrono::system_clock::time_point earliest_time;
        std::chrono::system_clock::time_point latest_time;
        std::vector<std::string> available_symbols;
        std::vector<std::string> available_exchanges;
        
        struct SymbolStats {
            std::string symbol;
            std::string exchange;
            size_t record_count = 0;
            size_t file_count = 0;
            std::chrono::system_clock::time_point first_time;
            std::chrono::system_clock::time_point last_time;
        };
        std::vector<SymbolStats> symbol_stats;
    };
    
    std::future<DataStatistics> GetDataStatistics(const QueryCondition& condition);
    
    // 数据预览接口
    std::future<TickDataBatch> PreviewData(const std::string& symbol,
                                          const std::string& exchange,
                                          size_t sample_size = 100);
    
    // 查询优化建议
    struct QueryOptimization {
        std::string suggestion;
        double estimated_time_seconds = 0.0;
        size_t estimated_io_bytes = 0;
        std::vector<std::string> recommended_indexes;
    };
    
    QueryOptimization AnalyzeQuery(const QueryCondition& condition);

private:
    std::shared_ptr<ColdDataStorage> cold_storage_;
    
    // 查询执行器
    class QueryExecutor;
    std::unique_ptr<QueryExecutor> query_executor_;
    
    // 批量查询管理器
    class BulkQueryManager;
    std::unique_ptr<BulkQueryManager> bulk_query_manager_;
    
    // 查询缓存
    class QueryCache;
    std::unique_ptr<QueryCache> query_cache_;
    
    // 查询优化器
    class QueryOptimizer;
    std::unique_ptr<QueryOptimizer> query_optimizer_;
};

// 查询流实现
class FileBasedQueryStream : public ArchiveInterface::QueryStream {
public:
    FileBasedQueryStream(std::shared_ptr<ColdDataStorage> cold_storage,
                        const QueryCondition& condition);
    ~FileBasedQueryStream() override;
    
    std::future<TickDataBatch> GetNextBatch() override;
    bool HasMore() const override;
    void Close() override;
    QueryResult::Stats GetStats() const override;

private:
    std::shared_ptr<ColdDataStorage> cold_storage_;
    QueryCondition condition_;
    std::vector<std::string> file_list_;
    size_t current_file_index_ = 0;
    bool closed_ = false;
    QueryResult::Stats stats_;
    
    void InitializeFileList();
    TickDataBatch ProcessFile(const std::string& file_path);
    bool MatchesCondition(const TickDataBatch& batch, size_t index);
};

// 数据压缩和格式转换工具
class DataConverter {
public:
    // 格式转换
    static bool ConvertToCSV(const TickDataBatch& batch, const std::string& file_path);
    static bool ConvertToJSON(const TickDataBatch& batch, const std::string& file_path);
    static bool ConvertToParquet(const TickDataBatch& batch, const std::string& file_path);
    
    // 数据压缩
    static bool CompressFile(const std::string& input_path, 
                           const std::string& output_path,
                           const std::string& compression = "zstd");
    
    static bool DecompressFile(const std::string& input_path,
                             const std::string& output_path,
                             const std::string& compression = "zstd");
    
    // 数据分割
    static std::vector<std::string> SplitFile(const std::string& input_path,
                                             const std::string& output_dir,
                                             size_t max_file_size);
    
    // 数据合并
    static bool MergeFiles(const std::vector<std::string>& input_paths,
                          const std::string& output_path);

private:
    static bool WriteCSVHeader(std::ofstream& file);
    static bool WriteCSVRecord(std::ofstream& file, const TickDataBatch& batch, size_t index);
    static bool WriteJSONRecord(std::ofstream& file, const TickDataBatch& batch, size_t index);
};

// 查询性能监控
class QueryPerformanceMonitor {
public:
    struct QueryMetrics {
        std::string query_id;
        std::chrono::system_clock::time_point start_time;
        std::chrono::system_clock::time_point end_time;
        std::chrono::milliseconds duration{0};
        size_t records_scanned = 0;
        size_t records_returned = 0;
        size_t bytes_read = 0;
        size_t files_accessed = 0;
        std::string query_pattern;
        bool cache_hit = false;
    };
    
    void RecordQuery(const QueryMetrics& metrics);
    std::vector<QueryMetrics> GetRecentQueries(size_t limit = 100);
    
    struct PerformanceStats {
        double average_query_time_ms = 0.0;
        double average_throughput_records_per_sec = 0.0;
        double cache_hit_rate = 0.0;
        size_t total_queries = 0;
        size_t slow_queries = 0;  // 查询时间超过阈值的数量
    };
    
    PerformanceStats GetPerformanceStats() const;
    
    // 慢查询分析
    std::vector<QueryMetrics> GetSlowQueries(std::chrono::milliseconds threshold = std::chrono::milliseconds(5000));
    
    // 查询模式分析
    std::map<std::string, size_t> GetQueryPatterns() const;

private:
    mutable std::mutex metrics_mutex_;
    std::vector<QueryMetrics> query_metrics_;
    static constexpr size_t MAX_METRICS = 10000;
    
    void CleanupOldMetrics();
};

} // namespace storage
} // namespace financial_data