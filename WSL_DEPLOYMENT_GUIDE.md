# WSL环境部署测试指南

## 概述

本指南帮助你在WSL (Windows Subsystem for Linux) 环境下部署和测试金融数据服务系统。

## 系统要求

- Windows 10/11 with WSL2
- Ubuntu 20.04+ 或其他Linux发行版
- Python 3.8+
- Docker (可选)
- 至少4GB可用内存

## 快速开始

### 1. 环境准备

在WSL终端中执行以下命令：

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y python3 python3-pip python3-venv build-essential curl wget git

# 安装Redis (用于数据缓存)
sudo apt install -y redis-server
sudo service redis-server start

# 安装Docker (可选，用于容器化部署)
sudo apt install -y docker.io docker-compose
sudo service docker start
sudo usermod -aG docker $USER
```

### 2. 项目设置

```bash
# 进入项目目录
cd /path/to/financial-data-service

# 给脚本添加执行权限
chmod +x deploy_wsl_test.sh
chmod +x quick_test_wsl.py

# 创建Python虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装Python依赖
pip install --upgrade pip
pip install -r requirements.txt
```

### 3. 快速测试

运行快速测试脚本验证环境：

```bash
python3 quick_test_wsl.py
```

这个脚本会检查：
- ✅ Python环境和模块
- ✅ Redis连接
- ✅ 文件系统权限
- ✅ 异步功能
- ✅ Docker服务 (如果安装)
- ✅ 网络连接

### 4. 完整部署

运行部署脚本：

```bash
# 完整部署
./deploy_wsl_test.sh

# 或者分步执行
./deploy_wsl_test.sh deploy
```

### 5. 服务管理

```bash
# 查看服务状态
./deploy_wsl_test.sh --status

# 停止服务
./deploy_wsl_test.sh --stop

# 重启服务
./deploy_wsl_test.sh --restart
```

## 部署选项

### 选项1: 标准部署 (推荐)

使用系统服务和虚拟环境：

```bash
./deploy_wsl_test.sh deploy
```

### 选项2: Docker部署

使用轻量级Docker配置：

```bash
# 启动基础服务
docker-compose -f docker-compose.wsl.yml up -d

# 查看服务状态
docker-compose -f docker-compose.wsl.yml ps
```

### 选项3: 手动部署

```bash
# 1. 激活虚拟环境
source venv/bin/activate

# 2. 启动Redis
sudo service redis-server start

# 3. 运行测试
python3 test_enhanced_features.py

# 4. 启动调度器
python3 start_enhanced_scheduler.py
```

## 功能测试

### 基础功能测试

```bash
# 激活虚拟环境
source venv/bin/activate

# 运行增强功能测试
python3 test_enhanced_features.py
```

### 数据采集测试

```bash
# 测试PyTDX数据采集
python3 -c "
import asyncio
import sys
sys.path.insert(0, '.')
from src.collectors.pytdx_collector import PyTDXCollector, PyTDXConfig

async def test():
    config = PyTDXConfig()
    collector = PyTDXCollector(config)
    if await collector.initialize():
        print('✅ PyTDX连接成功')
        await collector.close()
    else:
        print('❌ PyTDX连接失败')

asyncio.run(test())
"
```

### 调度器测试

```bash
# 启动增强版调度器
python3 start_enhanced_scheduler.py

# 或使用标准调度器
python3 scripts/start_scheduler.py --config config/scheduler_config.json
```

## 服务端口

| 服务 | 端口 | 用途 |
|------|------|------|
| Redis | 6379 | 数据缓存 |
| ClickHouse HTTP | 8123 | 数据库HTTP接口 |
| ClickHouse Native | 9000 | 数据库原生接口 |
| MinIO Console | 9001 | 对象存储管理 |
| MinIO API | 9002 | 对象存储API |
| Prometheus | 9090 | 监控指标 |

## 日志查看

```bash
# 查看调度器日志
tail -f logs/scheduler_service.log

# 查看系统日志
journalctl -f

# 查看Docker日志
docker-compose -f docker-compose.wsl.yml logs -f
```

## 故障排除

### 常见问题

1. **Python模块导入失败**
   ```bash
   # 重新安装依赖
   pip install --upgrade -r requirements.txt
   ```

2. **Redis连接失败**
   ```bash
   # 启动Redis服务
   sudo service redis-server start
   
   # 检查Redis状态
   redis-cli ping
   ```

3. **Docker权限问题**
   ```bash
   # 添加用户到docker组
   sudo usermod -aG docker $USER
   
   # 重新登录或执行
   newgrp docker
   ```

4. **端口占用**
   ```bash
   # 查看端口占用
   netstat -tlnp | grep :6379
   
   # 杀死占用进程
   sudo fuser -k 6379/tcp
   ```

### 性能优化

1. **WSL2内存限制**
   
   创建 `%USERPROFILE%\.wslconfig` 文件：
   ```ini
   [wsl2]
   memory=4GB
   processors=2
   ```

2. **Redis优化**
   ```bash
   # 编辑Redis配置
   sudo nano /etc/redis/redis.conf
   
   # 设置内存限制
   maxmemory 1gb
   maxmemory-policy allkeys-lru
   ```

## 开发建议

1. **使用虚拟环境**
   ```bash
   # 始终在虚拟环境中工作
   source venv/bin/activate
   ```

2. **代码热重载**
   ```bash
   # 使用watchdog监控文件变化
   pip install watchdog
   ```

3. **调试模式**
   ```bash
   # 设置调试日志级别
   export LOG_LEVEL=DEBUG
   python3 start_enhanced_scheduler.py
   ```

## 生产部署

对于生产环境，建议：

1. 使用systemd服务
2. 配置日志轮转
3. 设置监控告警
4. 使用负载均衡
5. 配置数据备份

详细的生产部署指南请参考 `deployment/install.sh`。

## 支持

如遇问题，请：

1. 查看日志文件
2. 运行 `python3 quick_test_wsl.py` 诊断
3. 检查系统资源使用情况
4. 提交Issue并附上错误日志

---

**注意**: 本指南专为WSL环境优化，在其他Linux环境中可能需要适当调整。