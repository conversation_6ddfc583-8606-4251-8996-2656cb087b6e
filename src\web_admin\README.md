# 金融数据服务系统 - Web管理界面

## 概述

Web管理界面是金融数据服务系统的管理控制台，提供系统监控、用户管理、告警配置、数据查询和系统配置等功能。

## 功能特性

### 1. 系统状态监控界面
- 实时性能指标显示（CPU、内存、磁盘使用率）
- 数据吞吐量和延迟监控
- 组件状态监控
- 实时图表展示

### 2. 用户管理和权限配置界面
- 用户创建、编辑、删除
- 角色权限管理（管理员、操作员、查看者）
- 用户状态管理
- 登录历史记录

### 3. 数据查询和导出工具界面
- 历史数据查询
- 多种数据格式支持（Tick、K线、Level2）
- 数据导出（JSON、CSV格式）
- 查询性能统计

### 4. 告警管理和通知配置界面
- 告警规则配置
- 多种通知渠道支持
- 告警状态管理
- 实时告警展示

### 5. 系统配置和参数调优界面
- 系统参数配置
- 分类配置管理
- 配置变更追踪
- 重启提醒功能

## 技术架构

### 前端技术栈
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全的JavaScript
- **Ant Design**: UI组件库
- **ECharts**: 数据可视化图表
- **Axios**: HTTP客户端
- **Day.js**: 日期时间处理

### 后端技术栈
- **FastAPI**: 现代Python Web框架
- **PostgreSQL**: 关系型数据库
- **Redis**: 缓存和实时数据存储
- **JWT**: 身份认证
- **Pydantic**: 数据验证

## 部署指南

### 环境要求
- Node.js 16+
- Python 3.8+
- PostgreSQL 12+
- Redis 6+

### 后端部署

1. 安装依赖
```bash
cd src/web_admin/backend
pip install -r requirements.txt
```

2. 初始化数据库
```bash
psql -U postgres -f database.sql
```

3. 配置环境变量
```bash
export JWT_SECRET_KEY="your-secret-key"
export DB_HOST="localhost"
export DB_PORT="5432"
export DB_USER="admin"
export DB_PASSWORD="password"
export DB_NAME="financial_data_admin"
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
```

4. 启动后端服务
```bash
python main.py
```

### 前端部署

1. 安装依赖
```bash
cd src/web_admin/frontend
npm install
```

2. 开发模式启动
```bash
npm start
```

3. 生产构建
```bash
npm run build
```

## 使用说明

### 登录系统
- 默认管理员账号: `admin`
- 默认密码: `admin123`

### 系统监控
1. 访问"系统概览"查看整体状态
2. 访问"系统监控"查看详细指标
3. 实时监控可以开启/关闭

### 用户管理
1. 只有管理员可以管理用户
2. 支持三种角色：管理员、操作员、查看者
3. 可以启用/禁用用户账号

### 告警配置
1. 支持多种监控指标
2. 可配置阈值和比较操作符
3. 支持多种通知渠道

### 数据查询
1. 选择合约代码和数据类型
2. 设置时间范围
3. 支持导出JSON和CSV格式

### 系统配置
1. 按类别组织配置项
2. 标记需要重启的配置
3. 支持配置重置功能

## API文档

后端API遵循RESTful设计原则，主要端点包括：

- `POST /api/auth/login` - 用户登录
- `GET /api/system/metrics` - 获取系统指标
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/alerts` - 获取告警配置
- `POST /api/alerts` - 创建告警配置
- `GET /api/data/query` - 查询历史数据
- `GET /api/config` - 获取系统配置
- `PUT /api/config` - 更新系统配置

## 安全特性

1. **身份认证**: JWT令牌认证
2. **权限控制**: 基于角色的访问控制
3. **数据加密**: TLS传输加密
4. **审计日志**: 完整的操作日志记录
5. **会话管理**: 自动过期和刷新

## 性能优化

1. **前端优化**:
   - 组件懒加载
   - 图表数据缓存
   - 防抖查询

2. **后端优化**:
   - 数据库连接池
   - Redis缓存
   - 异步处理

## 监控和告警

系统提供完整的监控和告警功能：

1. **系统指标监控**:
   - CPU、内存、磁盘使用率
   - 网络IO统计
   - 应用性能指标

2. **告警规则**:
   - 可配置的阈值告警
   - 多种通知渠道
   - 告警状态管理

3. **日志记录**:
   - 操作审计日志
   - 系统错误日志
   - 性能指标历史

## 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名密码
   - 确认用户状态为启用
   - 检查JWT配置

2. **数据查询慢**
   - 缩小查询时间范围
   - 检查数据库索引
   - 优化查询条件

3. **告警不生效**
   - 检查告警规则配置
   - 确认通知渠道设置
   - 查看系统日志

### 日志位置
- 应用日志: `/var/log/financial-data-admin/`
- 数据库日志: PostgreSQL日志目录
- Web服务器日志: Nginx/Apache日志目录

## 开发指南

### 添加新功能
1. 后端添加API端点
2. 前端创建对应组件
3. 更新路由配置
4. 添加权限检查

### 代码规范
- 使用TypeScript类型定义
- 遵循ESLint规则
- 组件使用函数式写法
- API使用async/await

## 更新日志

### v1.0.0 (2024-07-27)
- 初始版本发布
- 实现基础管理功能
- 支持系统监控和用户管理
- 提供数据查询和配置管理

## 许可证

本项目采用MIT许可证，详见LICENSE文件。