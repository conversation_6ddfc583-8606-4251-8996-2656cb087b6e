#include "financial_data_sdk.h"
#include <algorithm>
#include <random>
#include <thread>

namespace financial_data {
namespace sdk {

// Advanced error handler with exponential backoff and circuit breaker
class AdvancedErrorHandler {
public:
    explicit AdvancedErrorHandler(const ConnectionConfig& config) 
        : config_(config), circuit_breaker_state_(CircuitBreakerState::CLOSED) {
        ResetCircuitBreaker();
    }

    template<typename Func>
    auto ExecuteWithRetry(Func&& func) -> decltype(func()) {
        if (circuit_breaker_state_ == CircuitBreakerState::OPEN) {
            if (ShouldTryCircuitBreaker()) {
                circuit_breaker_state_ = CircuitBreakerState::HALF_OPEN;
            } else {
                throw std::runtime_error("Circuit breaker is open");
            }
        }

        int attempts = 0;
        std::chrono::milliseconds delay = config_.retry_interval;
        
        while (attempts < config_.max_retry_attempts) {
            try {
                auto result = func();
                
                // Success - reset circuit breaker if it was half-open
                if (circuit_breaker_state_ == CircuitBreakerState::HALF_OPEN) {
                    ResetCircuitBreaker();
                }
                
                return result;
                
            } catch (const std::exception& e) {
                attempts++;
                RecordFailure();
                
                ErrorCode error_code = ClassifyError(e);
                LogError(error_code, e.what(), attempts);
                
                if (attempts >= config_.max_retry_attempts) {
                    if (ShouldOpenCircuitBreaker()) {
                        OpenCircuitBreaker();
                    }
                    throw;
                }
                
                // Exponential backoff with jitter
                auto actual_delay = CalculateBackoffDelay(delay, attempts);
                std::this_thread::sleep_for(actual_delay);
                
                delay = std::min(delay * 2, std::chrono::milliseconds(30000)); // Max 30 seconds
            }
        }
        
        throw std::runtime_error("Max retry attempts exceeded");
    }

    void HandleError(const ErrorInfo& error) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        error_history_.push_back(error);
        
        // Keep only recent errors (last 1000)
        if (error_history_.size() > 1000) {
            error_history_.erase(error_history_.begin());
        }
        
        // Update error statistics
        UpdateErrorStats(error);
        
        if (error_callback_) {
            try {
                error_callback_(error);
            } catch (...) {
                // Ignore callback errors to prevent infinite loops
            }
        }
    }

    void SetErrorCallback(ErrorCallback callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        error_callback_ = callback;
    }

    // Get error statistics
    struct ErrorStatistics {
        uint64_t total_errors = 0;
        uint64_t connection_errors = 0;
        uint64_t timeout_errors = 0;
        uint64_t authentication_errors = 0;
        uint64_t server_errors = 0;
        std::chrono::system_clock::time_point last_error_time;
        double error_rate_per_minute = 0.0;
    };

    ErrorStatistics GetErrorStatistics() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return error_stats_;
    }

    void ResetErrorStatistics() {
        std::lock_guard<std::mutex> lock(mutex_);
        error_stats_ = ErrorStatistics{};
        error_history_.clear();
    }

    // Circuit breaker status
    enum class CircuitBreakerState {
        CLOSED,    // Normal operation
        OPEN,      // Failing fast
        HALF_OPEN  // Testing if service recovered
    };

    CircuitBreakerState GetCircuitBreakerState() const {
        return circuit_breaker_state_;
    }

private:
    ErrorCode ClassifyError(const std::exception& e) {
        std::string message = e.what();
        std::transform(message.begin(), message.end(), message.begin(), ::tolower);
        
        if (message.find("connection") != std::string::npos ||
            message.find("network") != std::string::npos) {
            return ErrorCode::CONNECTION_FAILED;
        } else if (message.find("timeout") != std::string::npos ||
                  message.find("deadline") != std::string::npos) {
            return ErrorCode::TIMEOUT;
        } else if (message.find("auth") != std::string::npos ||
                  message.find("unauthorized") != std::string::npos) {
            return ErrorCode::AUTHENTICATION_FAILED;
        } else if (message.find("server") != std::string::npos) {
            return ErrorCode::SERVER_ERROR;
        }
        
        return ErrorCode::UNKNOWN_ERROR;
    }

    void LogError(ErrorCode code, const std::string& message, int attempt) {
        // In a real implementation, this would log to a proper logging system
        // For now, we'll just store it in our error history
        HandleError(ErrorInfo(code, "Attempt " + std::to_string(attempt) + ": " + message));
    }

    std::chrono::milliseconds CalculateBackoffDelay(
        std::chrono::milliseconds base_delay, 
        int attempt) {
        
        // Exponential backoff: base_delay * 2^(attempt-1)
        auto exponential_delay = base_delay * (1 << (attempt - 1));
        
        // Add jitter (±25%)
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<> jitter_dist(0.75, 1.25);
        
        auto jittered_delay = std::chrono::duration_cast<std::chrono::milliseconds>(
            exponential_delay * jitter_dist(gen));
        
        return jittered_delay;
    }

    void UpdateErrorStats(const ErrorInfo& error) {
        error_stats_.total_errors++;
        error_stats_.last_error_time = error.timestamp;
        
        switch (error.code) {
            case ErrorCode::CONNECTION_FAILED:
            case ErrorCode::NETWORK_ERROR:
                error_stats_.connection_errors++;
                break;
            case ErrorCode::TIMEOUT:
                error_stats_.timeout_errors++;
                break;
            case ErrorCode::AUTHENTICATION_FAILED:
                error_stats_.authentication_errors++;
                break;
            case ErrorCode::SERVER_ERROR:
                error_stats_.server_errors++;
                break;
            default:
                break;
        }
        
        // Calculate error rate (errors per minute)
        auto now = std::chrono::system_clock::now();
        auto one_minute_ago = now - std::chrono::minutes(1);
        
        auto recent_errors = std::count_if(error_history_.begin(), error_history_.end(),
            [one_minute_ago](const ErrorInfo& err) {
                return err.timestamp >= one_minute_ago;
            });
        
        error_stats_.error_rate_per_minute = static_cast<double>(recent_errors);
    }

    // Circuit breaker implementation
    void RecordFailure() {
        std::lock_guard<std::mutex> lock(circuit_breaker_mutex_);
        failure_count_++;
        last_failure_time_ = std::chrono::steady_clock::now();
    }

    bool ShouldOpenCircuitBreaker() {
        std::lock_guard<std::mutex> lock(circuit_breaker_mutex_);
        
        // Open circuit breaker if failure rate is too high
        const int failure_threshold = 5;
        const auto time_window = std::chrono::minutes(1);
        
        auto now = std::chrono::steady_clock::now();
        if (failure_count_ >= failure_threshold && 
            (now - last_failure_time_) < time_window) {
            return true;
        }
        
        return false;
    }

    void OpenCircuitBreaker() {
        std::lock_guard<std::mutex> lock(circuit_breaker_mutex_);
        circuit_breaker_state_ = CircuitBreakerState::OPEN;
        circuit_breaker_open_time_ = std::chrono::steady_clock::now();
    }

    bool ShouldTryCircuitBreaker() {
        std::lock_guard<std::mutex> lock(circuit_breaker_mutex_);
        
        auto now = std::chrono::steady_clock::now();
        auto timeout = std::chrono::seconds(60); // Try again after 60 seconds
        
        return (now - circuit_breaker_open_time_) > timeout;
    }

    void ResetCircuitBreaker() {
        std::lock_guard<std::mutex> lock(circuit_breaker_mutex_);
        circuit_breaker_state_ = CircuitBreakerState::CLOSED;
        failure_count_ = 0;
    }

    ConnectionConfig config_;
    std::vector<ErrorInfo> error_history_;
    ErrorCallback error_callback_;
    mutable std::mutex mutex_;
    
    ErrorStatistics error_stats_;
    
    // Circuit breaker state
    std::atomic<CircuitBreakerState> circuit_breaker_state_;
    std::mutex circuit_breaker_mutex_;
    int failure_count_ = 0;
    std::chrono::steady_clock::time_point last_failure_time_;
    std::chrono::steady_clock::time_point circuit_breaker_open_time_;
};

} // namespace sdk
} // namespace financial_data