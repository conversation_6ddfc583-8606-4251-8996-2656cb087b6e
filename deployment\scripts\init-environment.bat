@echo off
REM Market Data Collection Enhancement - Environment Initialization Script (Windows)
REM This script initializes the deployment environment on Windows

setlocal enabledelayedexpansion

REM Configuration
set NAMESPACE=market-data
set CONFIG_DIR=%~dp0..\config
set KUBE_DIR=%~dp0..\kubernetes

REM Colors for output (Windows doesn't support colors in batch, but we'll use echo)
set "INFO_PREFIX=[INFO]"
set "WARN_PREFIX=[WARN]"
set "ERROR_PREFIX=[ERROR]"

REM Logging functions
:log_info
echo %INFO_PREFIX% %~1
goto :eof

:log_warn
echo %WARN_PREFIX% %~1
goto :eof

:log_error
echo %ERROR_PREFIX% %~1
goto :eof

REM Check prerequisites
:check_prerequisites
call :log_info "Checking prerequisites..."

REM Check if kubectl is installed
kubectl version --client >nul 2>&1
if errorlevel 1 (
    call :log_error "kubectl is not installed. Please install kubectl first."
    exit /b 1
)

REM Check if docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    call :log_error "docker is not installed. Please install docker first."
    exit /b 1
)

REM Check if helm is installed (optional)
helm version >nul 2>&1
if errorlevel 1 (
    call :log_warn "helm is not installed. Some features may not be available."
)

call :log_info "Prerequisites check completed."
goto :eof

REM Create namespace
:create_namespace
call :log_info "Creating namespace: %NAMESPACE%"
kubectl apply -f "%KUBE_DIR%\namespace.yaml"
goto :eof

REM Initialize storage
:init_storage
call :log_info "Initializing storage services..."

REM Apply storage services
kubectl apply -f "%KUBE_DIR%\storage-services.yaml"

REM Wait for storage services to be ready
call :log_info "Waiting for Redis to be ready..."
kubectl wait --for=condition=ready pod -l app=redis -n %NAMESPACE% --timeout=300s

call :log_info "Waiting for ClickHouse to be ready..."
kubectl wait --for=condition=ready pod -l app=clickhouse -n %NAMESPACE% --timeout=300s

call :log_info "Waiting for MinIO to be ready..."
kubectl wait --for=condition=ready pod -l app=minio -n %NAMESPACE% --timeout=300s

call :log_info "Storage services initialized successfully."
goto :eof

REM Initialize ClickHouse database
:init_clickhouse
call :log_info "Initializing ClickHouse database..."

REM Get ClickHouse pod name
for /f "tokens=*" %%i in ('kubectl get pods -n %NAMESPACE% -l app=clickhouse -o jsonpath="{.items[0].metadata.name}"') do set CLICKHOUSE_POD=%%i

REM Execute initialization SQL
kubectl exec -n %NAMESPACE% %CLICKHOUSE_POD% -- clickhouse-client --query "CREATE DATABASE IF NOT EXISTS market_data; CREATE TABLE IF NOT EXISTS market_data.standard_ticks (symbol String, timestamp_ns UInt64, price Float64, volume UInt64, bid_price Float64, ask_price Float64, bid_volume UInt64, ask_volume UInt64, data_source String, collection_timestamp_ns UInt64) ENGINE = MergeTree() PARTITION BY toYYYYMM(toDateTime(timestamp_ns / 1000000000)) ORDER BY (symbol, timestamp_ns); CREATE TABLE IF NOT EXISTS market_data.level2_data (symbol String, timestamp_ns UInt64, bid_prices Array(Float64), bid_volumes Array(UInt64), ask_prices Array(Float64), ask_volumes Array(UInt64), data_source String) ENGINE = MergeTree() PARTITION BY toYYYYMM(toDateTime(timestamp_ns / 1000000000)) ORDER BY (symbol, timestamp_ns);"

call :log_info "ClickHouse database initialized successfully."
goto :eof

REM Initialize MinIO buckets
:init_minio
call :log_info "Initializing MinIO buckets..."

REM Get MinIO pod name
for /f "tokens=*" %%i in ('kubectl get pods -n %NAMESPACE% -l app=minio -o jsonpath="{.items[0].metadata.name}"') do set MINIO_POD=%%i

REM Create buckets
kubectl exec -n %NAMESPACE% %MINIO_POD% -- mc alias set local http://localhost:9000 minioadmin minioadmin123
kubectl exec -n %NAMESPACE% %MINIO_POD% -- mc mb local/market-data-archive --ignore-existing
kubectl exec -n %NAMESPACE% %MINIO_POD% -- mc mb local/market-data-backup --ignore-existing

call :log_info "MinIO buckets initialized successfully."
goto :eof

REM Deploy application
:deploy_application
call :log_info "Deploying market data collector application..."

REM Apply ConfigMap
kubectl apply -f "%KUBE_DIR%\configmap.yaml"

REM Apply application deployment
kubectl apply -f "%KUBE_DIR%\market-data-collector-deployment.yaml"

REM Wait for deployment to be ready
call :log_info "Waiting for application deployment to be ready..."
kubectl wait --for=condition=available deployment/market-data-collector -n %NAMESPACE% --timeout=300s

call :log_info "Application deployed successfully."
goto :eof

REM Verify deployment
:verify_deployment
call :log_info "Verifying deployment..."

REM Check pod status
kubectl get pods -n %NAMESPACE%

REM Check services
kubectl get services -n %NAMESPACE%

REM Get application pod name
for /f "tokens=*" %%i in ('kubectl get pods -n %NAMESPACE% -l app=market-data-collector -o jsonpath="{.items[0].metadata.name}"') do set APP_POD=%%i

REM Check if application is responding
kubectl exec -n %NAMESPACE% %APP_POD% -- curl -f http://localhost:8080/health >nul 2>&1
if errorlevel 1 (
    call :log_warn "Application health check failed. Please check logs."
) else (
    call :log_info "Application health check passed."
)

call :log_info "Deployment verification completed."
goto :eof

REM Main execution
:main
call :log_info "Starting Market Data Collection Enhancement deployment..."

call :check_prerequisites
if errorlevel 1 exit /b 1

call :create_namespace
call :init_storage
call :init_clickhouse
call :init_minio
call :deploy_application
call :verify_deployment

call :log_info "Deployment completed successfully!"
call :log_info "You can access the application at:"
call :log_info "  - HTTP API: kubectl port-forward -n %NAMESPACE% service/market-data-collector-service 8080:8080"
call :log_info "  - gRPC API: kubectl port-forward -n %NAMESPACE% service/market-data-collector-service 8081:8081"
call :log_info "  - Metrics: kubectl port-forward -n %NAMESPACE% service/market-data-collector-service 9090:9090"
goto :eof

REM Handle script arguments
if "%1"=="check" (
    call :check_prerequisites
) else if "%1"=="storage" (
    call :create_namespace
    call :init_storage
    call :init_clickhouse
    call :init_minio
) else if "%1"=="app" (
    call :deploy_application
) else if "%1"=="verify" (
    call :verify_deployment
) else if "%1"=="" (
    call :main
) else (
    echo Usage: %0 [check^|storage^|app^|verify]
    echo   check   - Check prerequisites only
    echo   storage - Initialize storage services only
    echo   app     - Deploy application only
    echo   verify  - Verify deployment only
    echo   ^(empty^) - Run full deployment
    exit /b 1
)