#!/usr/bin/env python3
"""
Python gRPC客户端SDK示例
支持流式数据订阅、负载均衡和故障转移
"""

import grpc
import asyncio
import logging
import time
import threading
from typing import List, Dict, Optional, Callable, AsyncIterator
from concurrent.futures import Thread<PERSON>oolExecutor
import random
import json

# 导入生成的protobuf文件
import market_data_service_pb2
import market_data_service_pb2_grpc

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LoadBalancer:
    """客户端负载均衡器"""
    
    def __init__(self, servers: List[str]):
        self.servers = servers
        self.server_stats = {server: {'latency': 0.0, 'errors': 0, 'healthy': True} 
                           for server in servers}
        self.current_server_index = 0
        self.lock = threading.Lock()
    
    def get_best_server(self) -> str:
        """选择最佳服务器"""
        with self.lock:
            healthy_servers = [server for server, stats in self.server_stats.items() 
                             if stats['healthy']]
            
            if not healthy_servers:
                # 如果没有健康的服务器，重置所有服务器状态
                for stats in self.server_stats.values():
                    stats['healthy'] = True
                    stats['errors'] = 0
                healthy_servers = list(self.servers)
            
            # 选择延迟最低的服务器
            best_server = min(healthy_servers, 
                            key=lambda s: self.server_stats[s]['latency'])
            return best_server
    
    def report_error(self, server: str):
        """报告服务器错误"""
        with self.lock:
            if server in self.server_stats:
                self.server_stats[server]['errors'] += 1
                if self.server_stats[server]['errors'] > 3:
                    self.server_stats[server]['healthy'] = False
                    logger.warning(f"Server {server} marked as unhealthy")
    
    def update_latency(self, server: str, latency: float):
        """更新服务器延迟"""
        with self.lock:
            if server in self.server_stats:
                self.server_stats[server]['latency'] = latency


class StreamFlowController:
    """流量控制器"""
    
    def __init__(self, buffer_size: int = 1000):
        self.buffer_size = buffer_size
        self.pending_messages = 0
        self.backpressure_active = False
        self.lock = threading.Lock()
    
    def can_send(self) -> bool:
        """检查是否可以发送消息"""
        with self.lock:
            return not self.backpressure_active and self.pending_messages < self.buffer_size
    
    def on_message_sent(self):
        """消息发送时调用"""
        with self.lock:
            self.pending_messages += 1
            if self.pending_messages >= self.buffer_size * 0.8:
                self.backpressure_active = True
                logger.warning("Backpressure activated")
    
    def on_message_processed(self):
        """消息处理完成时调用"""
        with self.lock:
            if self.pending_messages > 0:
                self.pending_messages -= 1
            if self.pending_messages <= self.buffer_size * 0.5:
                self.backpressure_active = False


class FinancialDataClient:
    """金融数据gRPC客户端"""
    
    def __init__(self, servers: List[str], max_retries: int = 3):
        self.load_balancer = LoadBalancer(servers)
        self.max_retries = max_retries
        self.channels = {}
        self.stubs = {}
        self.flow_controller = StreamFlowController()
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # 初始化连接
        self._initialize_connections()
    
    def _initialize_connections(self):
        """初始化到所有服务器的连接"""
        for server in self.load_balancer.servers:
            try:
                channel = grpc.insecure_channel(server, options=[
                    ('grpc.keepalive_time_ms', 30000),
                    ('grpc.keepalive_timeout_ms', 5000),
                    ('grpc.keepalive_permit_without_calls', True),
                    ('grpc.max_receive_message_length', 4 * 1024 * 1024),
                    ('grpc.max_send_message_length', 4 * 1024 * 1024),
                ])
                stub = market_data_service_pb2_grpc.MarketDataServiceStub(channel)
                
                self.channels[server] = channel
                self.stubs[server] = stub
                logger.info(f"Connected to server: {server}")
                
            except Exception as e:
                logger.error(f"Failed to connect to server {server}: {e}")
                self.load_balancer.report_error(server)
    
    def _get_stub_with_retry(self):
        """获取可用的stub，支持重试和故障转移"""
        for attempt in range(self.max_retries):
            server = self.load_balancer.get_best_server()
            if server in self.stubs:
                return self.stubs[server], server
            
            # 如果连接不存在，尝试重新连接
            try:
                self._initialize_connections()
                if server in self.stubs:
                    return self.stubs[server], server
            except Exception as e:
                logger.error(f"Retry {attempt + 1} failed for server {server}: {e}")
                self.load_balancer.report_error(server)
                time.sleep(0.1 * (attempt + 1))  # 指数退避
        
        raise Exception("No healthy servers available")
    
    def stream_tick_data(self, symbols: List[str], exchange: str, 
                        callback: Callable, buffer_size: int = 1000):
        """订阅实时tick数据流"""
        def _stream_worker():
            stub, server = self._get_stub_with_retry()
            
            request = market_data_service_pb2.TickDataRequest(
                symbols=symbols,
                exchange=exchange,
                buffer_size=buffer_size
            )
            
            try:
                start_time = time.time()
                stream = stub.StreamTickData(request)
                
                for response in stream:
                    # 更新延迟统计
                    latency = time.time() - start_time
                    self.load_balancer.update_latency(server, latency * 1000)
                    
                    # 流量控制
                    if not self.flow_controller.can_send():
                        logger.warning("Flow control: dropping message")
                        continue
                    
                    self.flow_controller.on_message_sent()
                    
                    # 调用回调函数处理数据
                    try:
                        callback(response)
                        self.flow_controller.on_message_processed()
                    except Exception as e:
                        logger.error(f"Callback error: {e}")
                        self.flow_controller.on_message_processed()
                    
                    start_time = time.time()
                    
            except grpc.RpcError as e:
                logger.error(f"Stream error: {e}")
                self.load_balancer.report_error(server)
                raise
        
        # 在后台线程中运行流处理
        future = self.executor.submit(_stream_worker)
        return future
    
    def stream_kline_data(self, symbols: List[str], exchange: str, period: str,
                         callback: Callable, buffer_size: int = 1000):
        """订阅K线数据流"""
        def _stream_worker():
            stub, server = self._get_stub_with_retry()
            
            request = market_data_service_pb2.KlineDataRequest(
                symbols=symbols,
                exchange=exchange,
                period=period,
                buffer_size=buffer_size
            )
            
            try:
                start_time = time.time()
                stream = stub.StreamKlineData(request)
                
                for response in stream:
                    latency = time.time() - start_time
                    self.load_balancer.update_latency(server, latency * 1000)
                    
                    if not self.flow_controller.can_send():
                        logger.warning("Flow control: dropping kline message")
                        continue
                    
                    self.flow_controller.on_message_sent()
                    
                    try:
                        callback(response)
                        self.flow_controller.on_message_processed()
                    except Exception as e:
                        logger.error(f"Kline callback error: {e}")
                        self.flow_controller.on_message_processed()
                    
                    start_time = time.time()
                    
            except grpc.RpcError as e:
                logger.error(f"Kline stream error: {e}")
                self.load_balancer.report_error(server)
                raise
        
        future = self.executor.submit(_stream_worker)
        return future
    
    def stream_level2_data(self, symbols: List[str], exchange: str, depth: int,
                          callback: Callable, buffer_size: int = 1000):
        """订阅Level2深度数据流"""
        def _stream_worker():
            stub, server = self._get_stub_with_retry()
            
            request = market_data_service_pb2.Level2DataRequest(
                symbols=symbols,
                exchange=exchange,
                depth=depth,
                buffer_size=buffer_size
            )
            
            try:
                start_time = time.time()
                stream = stub.StreamLevel2Data(request)
                
                for response in stream:
                    latency = time.time() - start_time
                    self.load_balancer.update_latency(server, latency * 1000)
                    
                    if not self.flow_controller.can_send():
                        logger.warning("Flow control: dropping level2 message")
                        continue
                    
                    self.flow_controller.on_message_sent()
                    
                    try:
                        callback(response)
                        self.flow_controller.on_message_processed()
                    except Exception as e:
                        logger.error(f"Level2 callback error: {e}")
                        self.flow_controller.on_message_processed()
                    
                    start_time = time.time()
                    
            except grpc.RpcError as e:
                logger.error(f"Level2 stream error: {e}")
                self.load_balancer.report_error(server)
                raise
        
        future = self.executor.submit(_stream_worker)
        return future
    
    def get_historical_tick_data(self, symbol: str, exchange: str, 
                                start_timestamp: int, end_timestamp: int,
                                callback: Callable, limit: int = 1000):
        """获取历史tick数据"""
        def _history_worker():
            stub, server = self._get_stub_with_retry()
            
            request = market_data_service_pb2.HistoricalTickDataRequest(
                symbol=symbol,
                exchange=exchange,
                start_timestamp=start_timestamp,
                end_timestamp=end_timestamp,
                limit=limit
            )
            
            try:
                stream = stub.GetHistoricalTickData(request)
                
                for response in stream:
                    try:
                        callback(response)
                    except Exception as e:
                        logger.error(f"Historical data callback error: {e}")
                        
            except grpc.RpcError as e:
                logger.error(f"Historical data error: {e}")
                self.load_balancer.report_error(server)
                raise
        
        future = self.executor.submit(_history_worker)
        return future
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            stub, server = self._get_stub_with_retry()
            request = market_data_service_pb2.HealthCheckRequest(service="MarketDataService")
            response = stub.HealthCheck(request)
            return response.status == market_data_service_pb2.HealthCheckResponse.SERVING
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    def close(self):
        """关闭客户端连接"""
        for channel in self.channels.values():
            channel.close()
        self.executor.shutdown(wait=True)


# 使用示例
def main():
    """客户端使用示例"""
    
    # 服务器列表（支持负载均衡和故障转移）
    servers = [
        "localhost:50051",
        "localhost:50052",
        "localhost:50053"
    ]
    
    # 创建客户端
    client = FinancialDataClient(servers)
    
    # 定义数据处理回调函数
    def tick_data_handler(response):
        for tick in response.ticks:
            print(f"Tick: {tick.symbol} @ {tick.last_price} vol:{tick.volume}")
    
    def kline_data_handler(response):
        for kline in response.klines:
            print(f"Kline: {kline.symbol} {kline.period} O:{kline.open} H:{kline.high} L:{kline.low} C:{kline.close}")
    
    def level2_data_handler(response):
        for level2 in response.level2_data:
            print(f"Level2: {level2.symbol} bids:{len(level2.bids)} asks:{len(level2.asks)}")
    
    try:
        # 健康检查
        if client.health_check():
            print("Service is healthy")
        
        # 订阅实时tick数据
        tick_future = client.stream_tick_data(
            symbols=["AAPL", "GOOGL", "MSFT"],
            exchange="NASDAQ",
            callback=tick_data_handler,
            buffer_size=1000
        )
        
        # 订阅K线数据
        kline_future = client.stream_kline_data(
            symbols=["AAPL"],
            exchange="NASDAQ", 
            period="1m",
            callback=kline_data_handler,
            buffer_size=500
        )
        
        # 订阅Level2数据
        level2_future = client.stream_level2_data(
            symbols=["AAPL"],
            exchange="NASDAQ",
            depth=10,
            callback=level2_data_handler,
            buffer_size=200
        )
        
        # 获取历史数据
        historical_future = client.get_historical_tick_data(
            symbol="AAPL",
            exchange="NASDAQ",
            start_timestamp=int(time.time() - 3600) * 1000000,  # 1小时前
            end_timestamp=int(time.time()) * 1000000,  # 现在
            callback=tick_data_handler,
            limit=1000
        )
        
        # 等待一段时间让数据流运行
        print("Streaming data for 30 seconds...")
        time.sleep(30)
        
    except KeyboardInterrupt:
        print("Interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        client.close()
        print("Client closed")


if __name__ == "__main__":
    main()