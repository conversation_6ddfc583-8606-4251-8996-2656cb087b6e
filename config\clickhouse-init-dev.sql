-- ClickHouse 开发环境数据库初始化脚本
-- 简化配置，专注于基本功能

-- 创建主数据库
CREATE DATABASE IF NOT EXISTS market_data;

-- 创建元数据数据库
CREATE DATABASE IF NOT EXISTS metadata;

-- 使用市场数据数据库
USE market_data;

-- =====================================================
-- 期货 Tick 数据表 (简化版)
-- =====================================================
CREATE TABLE IF NOT EXISTS futures_tick (
    timestamp DateTime64(9),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    last_price Float64,
    volume UInt64,
    turnover Float64,
    open_interest UInt64,
    bid_price Float64,
    bid_volume UInt32,
    ask_price Float64,
    ask_volume UInt32,
    sequence UInt32
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
SETTINGS index_granularity = 8192;

-- =====================================================
-- 股票 Tick 数据表 (简化版)
-- =====================================================
CREATE TABLE IF NOT EXISTS stock_tick (
    timestamp DateTime64(9),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    last_price Float64,
    volume UInt64,
    turnover Float64,
    bid_price Float64,
    bid_volume UInt32,
    ask_price Float64,
    ask_volume UInt32,
    sequence UInt32,
    high_price Float64,
    low_price Float64,
    open_price Float64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
SETTINGS index_granularity = 8192;

-- =====================================================
-- K线数据表 (1分钟)
-- =====================================================
CREATE TABLE IF NOT EXISTS kline_1m (
    timestamp DateTime64(3),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    product_type LowCardinality(String),
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume UInt64,
    turnover Float64,
    trade_count UInt32
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
SETTINGS index_granularity = 8192;

-- =====================================================
-- K线数据表 (5分钟)
-- =====================================================
CREATE TABLE IF NOT EXISTS kline_5m (
    timestamp DateTime64(3),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    product_type LowCardinality(String),
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume UInt64,
    turnover Float64,
    trade_count UInt32
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
SETTINGS index_granularity = 8192;

-- =====================================================
-- K线数据表 (日线)
-- =====================================================
CREATE TABLE IF NOT EXISTS kline_1d (
    timestamp DateTime64(3),
    symbol LowCardinality(String),
    exchange LowCardinality(String),
    product_type LowCardinality(String),
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume UInt64,
    turnover Float64,
    trade_count UInt32
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp)
SETTINGS index_granularity = 8192;

-- =====================================================
-- 元数据表
-- =====================================================
USE metadata;

-- 合约信息表
CREATE TABLE IF NOT EXISTS instruments (
    symbol String,
    exchange LowCardinality(String),
    product_type Enum8('futures'=1, 'stock'=2, 'option'=3, 'forex'=4),
    underlying String,
    contract_size Float64,
    tick_size Float64,
    currency LowCardinality(String),
    created_at DateTime DEFAULT now(),
    updated_at DateTime DEFAULT now(),
    is_active UInt8 DEFAULT 1
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY (exchange, symbol)
SETTINGS index_granularity = 8192;

-- 交易日历表
CREATE TABLE IF NOT EXISTS trading_calendar (
    exchange LowCardinality(String),
    trading_date Date,
    is_trading_day UInt8,
    session_start DateTime,
    session_end DateTime
) ENGINE = MergeTree()
ORDER BY (exchange, trading_date)
SETTINGS index_granularity = 8192;

-- =====================================================
-- 插入一些测试数据
-- =====================================================
USE market_data;

-- 插入测试期货数据
INSERT INTO futures_tick VALUES
    (now() - INTERVAL 300 SECOND, 'IF2401', 'CFFEX', 3850.5, 100, 385050, 12345, 3850.0, 50, 3851.0, 60, 1),
    (now() - INTERVAL 240 SECOND, 'IF2401', 'CFFEX', 3851.0, 80, 308080, 12346, 3850.5, 40, 3851.5, 45, 2),
    (now() - INTERVAL 180 SECOND, 'IF2401', 'CFFEX', 3849.5, 120, 461940, 12347, 3849.0, 70, 3850.0, 55, 3);

-- 插入测试股票数据
INSERT INTO stock_tick VALUES
    (now() - INTERVAL 300 SECOND, '000001', 'SZSE', 12.50, 1000, 12500, 12.49, 500, 12.51, 600, 1, 12.60, 12.40, 12.45),
    (now() - INTERVAL 240 SECOND, '000001', 'SZSE', 12.52, 800, 10016, 12.51, 400, 12.53, 450, 2, 12.60, 12.40, 12.45),
    (now() - INTERVAL 180 SECOND, '000001', 'SZSE', 12.48, 1200, 14976, 12.47, 600, 12.49, 550, 3, 12.60, 12.40, 12.45);

-- 插入测试K线数据
INSERT INTO kline_1m VALUES
    (toStartOfMinute(now() - INTERVAL 300 SECOND), 'IF2401', 'CFFEX', 'futures', 3850.0, 3851.5, 3849.0, 3850.5, 300, 1155070, 3),
    (toStartOfMinute(now() - INTERVAL 240 SECOND), '000001', 'SZSE', 'stock', 12.45, 12.53, 12.47, 12.50, 3000, 37516, 3);

USE metadata;

-- 插入测试合约信息
INSERT INTO instruments VALUES
    ('IF2401', 'CFFEX', 'futures', 'CSI300', 300, 0.2, 'CNY', now(), now(), 1),
    ('000001', 'SZSE', 'stock', '', 1, 0.01, 'CNY', now(), now(), 1);

-- 插入测试交易日历
INSERT INTO trading_calendar VALUES
    ('CFFEX', today(), 1, toDateTime(today() + INTERVAL 9 HOUR), toDateTime(today() + INTERVAL 15 HOUR)),
    ('SZSE', today(), 1, toDateTime(today() + INTERVAL 9 HOUR + INTERVAL 30 MINUTE), toDateTime(today() + INTERVAL 15 HOUR));

-- 显示创建的表
SHOW TABLES FROM market_data;
SHOW TABLES FROM metadata;