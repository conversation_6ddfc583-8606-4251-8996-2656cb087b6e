#include "kafka_integration.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <cstring>

namespace financial_data {
namespace databus {

#ifdef KAFKA_ENABLED

// Kafka投递报告回调类
class DeliveryReportCb : public RdKafka::DeliveryReportCb {
private:
    KafkaProducer::DeliveryCallback callback_;

public:
    explicit DeliveryReportCb(KafkaProducer::DeliveryCallback callback)
        : callback_(std::move(callback)) {}

    void dr_cb(RdKafka::Message& message) override {
        if (callback_) {
            std::string error_str = message.err() ? message.errstr() : "";
            callback_(message.topic_name(), 
                     message.partition(), 
                     message.offset(), 
                     error_str);
        }
    }
};

// Kafka事件回调类
class EventCb : public RdKafka::EventCb {
public:
    void event_cb(RdKafka::Event& event) override {
        switch (event.type()) {
            case RdKafka::Event::EVENT_ERROR:
                std::cerr << "Kafka ERROR: " << RdKafka::err2str(event.err()) 
                         << " (" << event.str() << ")" << std::endl;
                break;
            case RdKafka::Event::EVENT_STATS:
                std::cout << "Kafka STATS: " << event.str() << std::endl;
                break;
            case RdKafka::Event::EVENT_LOG:
                std::cout << "Kafka LOG-" << event.severity() 
                         << "-" << event.fac() << ": " << event.str() << std::endl;
                break;
            default:
                std::cout << "Kafka EVENT " << event.type() 
                         << " (" << RdKafka::err2str(event.err()) << "): " 
                         << event.str() << std::endl;
                break;
        }
    }
};

#endif // KAFKA_ENABLED

// KafkaProducer实现
KafkaProducer::KafkaProducer(const KafkaConfig& config) 
    : config_(config) {
}

KafkaProducer::~KafkaProducer() {
    Shutdown();
}

bool KafkaProducer::Initialize() {
#ifdef KAFKA_ENABLED
    std::string error_str;
    
    // 创建配置对象
    conf_.reset(RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL));
    
    // 设置基本配置
    if (conf_->set("bootstrap.servers", config_.brokers, error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set bootstrap.servers: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("batch.size", std::to_string(config_.batch_size), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set batch.size: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("linger.ms", std::to_string(config_.linger_ms), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set linger.ms: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("buffer.memory", std::to_string(config_.buffer_memory), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set buffer.memory: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("compression.type", config_.compression_type, error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set compression.type: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("enable.idempotence", config_.enable_idempotence ? "true" : "false", error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set enable.idempotence: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("acks", std::to_string(config_.acks), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set acks: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("retries", std::to_string(config_.retries), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set retries: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("retry.backoff.ms", std::to_string(config_.retry_backoff_ms), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set retry.backoff.ms: " << error_str << std::endl;
        return false;
    }
    
    // 设置事件回调
    auto event_cb = std::make_unique<EventCb>();
    if (conf_->set("event_cb", event_cb.get(), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set event callback: " << error_str << std::endl;
        return false;
    }
    
    // 设置投递报告回调
    if (delivery_callback_) {
        auto dr_cb = std::make_unique<DeliveryReportCb>(delivery_callback_);
        if (conf_->set("dr_cb", dr_cb.get(), error_str) != RdKafka::Conf::CONF_OK) {
            std::cerr << "Failed to set delivery report callback: " << error_str << std::endl;
            return false;
        }
    }
    
    // 创建生产者
    producer_.reset(RdKafka::Producer::create(conf_.get(), error_str));
    if (!producer_) {
        std::cerr << "Failed to create producer: " << error_str << std::endl;
        return false;
    }
    
    // 启动轮询线程
    running_ = true;
    poll_thread_ = std::thread(&KafkaProducer::PollLoop, this);
    
    std::cout << "Kafka producer initialized successfully" << std::endl;
    return true;
#else
    std::cerr << "Kafka support not enabled" << std::endl;
    return false;
#endif
}

void KafkaProducer::Shutdown() {
    if (running_) {
        running_ = false;
        
#ifdef KAFKA_ENABLED
        if (producer_) {
            // 刷新所有待发送的消息
            producer_->flush(5000);
        }
#endif
        
        if (poll_thread_.joinable()) {
            poll_thread_.join();
        }
        
#ifdef KAFKA_ENABLED
        producer_.reset();
        conf_.reset();
#endif
    }
}

bool KafkaProducer::SendTick(const StandardTick& tick, const std::string& topic_suffix) {
#ifdef KAFKA_ENABLED
    if (!producer_ || !running_) {
        return false;
    }
    
    std::string topic = GetTopicName(topic_suffix);
    std::string payload = SerializeTick(tick);
    std::string key = tick.symbol;
    
    RdKafka::ErrorCode resp = producer_->produce(
        topic,
        RdKafka::Topic::PARTITION_UA,
        RdKafka::Producer::RK_MSG_COPY,
        const_cast<char*>(payload.c_str()),
        payload.size(),
        &key,
        nullptr
    );
    
    if (resp != RdKafka::ERR_NO_ERROR) {
        std::cerr << "Failed to produce tick message: " << RdKafka::err2str(resp) << std::endl;
        messages_failed_++;
        return false;
    }
    
    messages_sent_++;
    bytes_sent_ += payload.size();
    return true;
#else
    return false;
#endif
}

bool KafkaProducer::SendLevel2(const Level2Data& level2, const std::string& topic_suffix) {
#ifdef KAFKA_ENABLED
    if (!producer_ || !running_) {
        return false;
    }
    
    std::string topic = GetTopicName(topic_suffix);
    std::string payload = SerializeLevel2(level2);
    std::string key = level2.symbol;
    
    RdKafka::ErrorCode resp = producer_->produce(
        topic,
        RdKafka::Topic::PARTITION_UA,
        RdKafka::Producer::RK_MSG_COPY,
        const_cast<char*>(payload.c_str()),
        payload.size(),
        &key,
        nullptr
    );
    
    if (resp != RdKafka::ERR_NO_ERROR) {
        std::cerr << "Failed to produce level2 message: " << RdKafka::err2str(resp) << std::endl;
        messages_failed_++;
        return false;
    }
    
    messages_sent_++;
    bytes_sent_ += payload.size();
    return true;
#else
    return false;
#endif
}

bool KafkaProducer::SendBatch(const MarketDataBatch& batch, const std::string& topic_suffix) {
#ifdef KAFKA_ENABLED
    if (!producer_ || !running_) {
        return false;
    }
    
    std::string topic = GetTopicName(topic_suffix);
    std::string payload = SerializeBatch(batch);
    std::string key = "batch_" + std::to_string(batch.batch_sequence);
    
    RdKafka::ErrorCode resp = producer_->produce(
        topic,
        RdKafka::Topic::PARTITION_UA,
        RdKafka::Producer::RK_MSG_COPY,
        const_cast<char*>(payload.c_str()),
        payload.size(),
        &key,
        nullptr
    );
    
    if (resp != RdKafka::ERR_NO_ERROR) {
        std::cerr << "Failed to produce batch message: " << RdKafka::err2str(resp) << std::endl;
        messages_failed_++;
        return false;
    }
    
    messages_sent_++;
    bytes_sent_ += payload.size();
    return true;
#else
    return false;
#endif
}

bool KafkaProducer::SendMessage(const std::string& topic, 
                               const std::string& key,
                               const void* payload, 
                               size_t payload_size,
                               int32_t partition) {
#ifdef KAFKA_ENABLED
    if (!producer_ || !running_) {
        return false;
    }
    
    RdKafka::ErrorCode resp = producer_->produce(
        topic,
        partition,
        RdKafka::Producer::RK_MSG_COPY,
        const_cast<void*>(payload),
        payload_size,
        &key,
        nullptr
    );
    
    if (resp != RdKafka::ERR_NO_ERROR) {
        std::cerr << "Failed to produce message: " << RdKafka::err2str(resp) << std::endl;
        messages_failed_++;
        return false;
    }
    
    messages_sent_++;
    bytes_sent_ += payload_size;
    return true;
#else
    return false;
#endif
}

KafkaProducer::Statistics KafkaProducer::GetStatistics() const {
    static auto start_time = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time).count();
    
    Statistics stats;
    stats.messages_sent = messages_sent_.load();
    stats.messages_failed = messages_failed_.load();
    stats.bytes_sent = bytes_sent_.load();
    
    if (duration > 0) {
        stats.throughput_msg_per_sec = static_cast<double>(stats.messages_sent) / duration;
        stats.throughput_mb_per_sec = static_cast<double>(stats.bytes_sent) / (1024 * 1024 * duration);
    } else {
        stats.throughput_msg_per_sec = 0.0;
        stats.throughput_mb_per_sec = 0.0;
    }
    
    return stats;
}

void KafkaProducer::Flush(int timeout_ms) {
#ifdef KAFKA_ENABLED
    if (producer_) {
        producer_->flush(timeout_ms);
    }
#endif
}

void KafkaProducer::PollLoop() {
#ifdef KAFKA_ENABLED
    while (running_) {
        if (producer_) {
            producer_->poll(100);  // 100ms超时
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
#endif
}

std::string KafkaProducer::SerializeTick(const StandardTick& tick) {
    // 简单的JSON序列化（实际项目中应使用更高效的序列化方式如Protocol Buffers）
    std::ostringstream oss;
    oss << "{"
        << "\"timestamp_ns\":" << tick.timestamp_ns << ","
        << "\"symbol\":\"" << tick.symbol << "\","
        << "\"exchange\":\"" << tick.exchange << "\","
        << "\"last_price\":" << tick.last_price << ","
        << "\"volume\":" << tick.volume << ","
        << "\"turnover\":" << tick.turnover << ","
        << "\"open_interest\":" << tick.open_interest << ","
        << "\"sequence\":" << tick.sequence << ","
        << "\"trade_flag\":\"" << tick.trade_flag << "\""
        << "}";
    return oss.str();
}

std::string KafkaProducer::SerializeLevel2(const Level2Data& level2) {
    std::ostringstream oss;
    oss << "{"
        << "\"timestamp_ns\":" << level2.timestamp_ns << ","
        << "\"symbol\":\"" << level2.symbol << "\","
        << "\"exchange\":\"" << level2.exchange << "\","
        << "\"sequence\":" << level2.sequence << ","
        << "\"bids\":[";
    
    for (size_t i = 0; i < level2.bids.size(); ++i) {
        if (i > 0) oss << ",";
        oss << "{\"price\":" << level2.bids[i].price 
            << ",\"volume\":" << level2.bids[i].volume 
            << ",\"order_count\":" << level2.bids[i].order_count << "}";
    }
    
    oss << "],\"asks\":[";
    
    for (size_t i = 0; i < level2.asks.size(); ++i) {
        if (i > 0) oss << ",";
        oss << "{\"price\":" << level2.asks[i].price 
            << ",\"volume\":" << level2.asks[i].volume 
            << ",\"order_count\":" << level2.asks[i].order_count << "}";
    }
    
    oss << "]}";
    return oss.str();
}

std::string KafkaProducer::SerializeBatch(const MarketDataBatch& batch) {
    std::ostringstream oss;
    oss << "{"
        << "\"batch_timestamp_ns\":" << batch.batch_timestamp_ns << ","
        << "\"batch_sequence\":" << batch.batch_sequence << ","
        << "\"data_count\":" << batch.data.size()
        << "}";
    return oss.str();
}

std::string KafkaProducer::GetTopicName(const std::string& suffix) const {
    return config_.topic_prefix + "_" + suffix;
}

// KafkaConsumer实现
KafkaConsumer::KafkaConsumer(const KafkaConfig& config) 
    : config_(config) {
}

KafkaConsumer::~KafkaConsumer() {
    StopConsuming();
}

bool KafkaConsumer::Initialize() {
#ifdef KAFKA_ENABLED
    std::string error_str;
    
    // 创建配置对象
    conf_.reset(RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL));
    
    // 设置基本配置
    if (conf_->set("bootstrap.servers", config_.brokers, error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set bootstrap.servers: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("group.id", config_.group_id, error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set group.id: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("auto.offset.reset", config_.auto_offset_reset, error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set auto.offset.reset: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("enable.auto.commit", config_.enable_auto_commit ? "true" : "false", error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set enable.auto.commit: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("session.timeout.ms", std::to_string(config_.session_timeout_ms), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set session.timeout.ms: " << error_str << std::endl;
        return false;
    }
    
    if (conf_->set("heartbeat.interval.ms", std::to_string(config_.heartbeat_interval_ms), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set heartbeat.interval.ms: " << error_str << std::endl;
        return false;
    }
    
    // 设置事件回调
    auto event_cb = std::make_unique<EventCb>();
    if (conf_->set("event_cb", event_cb.get(), error_str) != RdKafka::Conf::CONF_OK) {
        std::cerr << "Failed to set event callback: " << error_str << std::endl;
        return false;
    }
    
    // 创建消费者
    consumer_.reset(RdKafka::KafkaConsumer::create(conf_.get(), error_str));
    if (!consumer_) {
        std::cerr << "Failed to create consumer: " << error_str << std::endl;
        return false;
    }
    
    std::cout << "Kafka consumer initialized successfully" << std::endl;
    return true;
#else
    std::cerr << "Kafka support not enabled" << std::endl;
    return false;
#endif
}

bool KafkaConsumer::Subscribe(const std::vector<std::string>& topics) {
#ifdef KAFKA_ENABLED
    if (!consumer_) {
        return false;
    }
    
    topics_ = topics;
    RdKafka::ErrorCode err = consumer_->subscribe(topics);
    if (err != RdKafka::ERR_NO_ERROR) {
        std::cerr << "Failed to subscribe to topics: " << RdKafka::err2str(err) << std::endl;
        return false;
    }
    
    std::cout << "Subscribed to " << topics.size() << " topics" << std::endl;
    return true;
#else
    return false;
#endif
}

bool KafkaConsumer::StartConsuming() {
    if (running_) {
        return true;
    }
    
    running_ = true;
    consume_thread_ = std::thread(&KafkaConsumer::ConsumeLoop, this);
    return true;
}

void KafkaConsumer::StopConsuming() {
    if (running_) {
        running_ = false;
        
        if (consume_thread_.joinable()) {
            consume_thread_.join();
        }
        
#ifdef KAFKA_ENABLED
        if (consumer_) {
            consumer_->close();
        }
        consumer_.reset();
        conf_.reset();
#endif
    }
}

bool KafkaConsumer::CommitOffset() {
#ifdef KAFKA_ENABLED
    if (!consumer_) {
        return false;
    }
    
    RdKafka::ErrorCode err = consumer_->commitSync();
    return err == RdKafka::ERR_NO_ERROR;
#else
    return false;
#endif
}

KafkaConsumer::Statistics KafkaConsumer::GetStatistics() const {
    static auto start_time = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time).count();
    
    Statistics stats;
    stats.messages_received = messages_received_.load();
    stats.messages_processed = messages_processed_.load();
    stats.bytes_received = bytes_received_.load();
    
    if (duration > 0) {
        stats.throughput_msg_per_sec = static_cast<double>(stats.messages_received) / duration;
        stats.throughput_mb_per_sec = static_cast<double>(stats.bytes_received) / (1024 * 1024 * duration);
    } else {
        stats.throughput_msg_per_sec = 0.0;
        stats.throughput_mb_per_sec = 0.0;
    }
    
    return stats;
}

void KafkaConsumer::ConsumeLoop() {
#ifdef KAFKA_ENABLED
    while (running_) {
        if (!consumer_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }
        
        std::unique_ptr<RdKafka::Message> msg(consumer_->consume(1000));
        
        if (!msg) {
            continue;
        }
        
        switch (msg->err()) {
            case RdKafka::ERR_NO_ERROR:
                // 成功接收消息
                messages_received_++;
                bytes_received_ += msg->len();
                
                if (message_callback_) {
                    std::string key = msg->key() ? *msg->key() : "";
                    message_callback_(msg->topic_name(),
                                    msg->partition(),
                                    msg->offset(),
                                    key,
                                    msg->payload(),
                                    msg->len());
                    messages_processed_++;
                }
                break;
                
            case RdKafka::ERR__PARTITION_EOF:
                // 分区结束，继续等待
                break;
                
            case RdKafka::ERR__TIMED_OUT:
                // 超时，继续等待
                break;
                
            default:
                std::cerr << "Consumer error: " << msg->errstr() << std::endl;
                break;
        }
    }
#endif
}

StandardTick KafkaConsumer::DeserializeTick(const void* payload, size_t size) {
    // 简单的反序列化实现（实际项目中应使用更高效的方式）
    StandardTick tick;
    // TODO: 实现JSON反序列化
    return tick;
}

Level2Data KafkaConsumer::DeserializeLevel2(const void* payload, size_t size) {
    Level2Data level2;
    // TODO: 实现JSON反序列化
    return level2;
}

MarketDataBatch KafkaConsumer::DeserializeBatch(const void* payload, size_t size) {
    MarketDataBatch batch;
    // TODO: 实现JSON反序列化
    return batch;
}

// KafkaIntegration实现
KafkaIntegration::KafkaIntegration(const KafkaConfig& config) 
    : config_(config) {
    tick_queue_ = std::make_unique<TickQueue>();
    level2_queue_ = std::make_unique<Level2Queue>();
    batch_queue_ = std::make_unique<BatchQueue>();
}

KafkaIntegration::~KafkaIntegration() {
    Shutdown();
}

bool KafkaIntegration::Initialize() {
    producer_ = std::make_unique<KafkaProducer>(config_);
    if (!producer_->Initialize()) {
        std::cerr << "Failed to initialize Kafka producer" << std::endl;
        return false;
    }
    
    std::cout << "Kafka integration initialized successfully" << std::endl;
    return true;
}

void KafkaIntegration::Shutdown() {
    StopWorkers();
    
    if (producer_) {
        producer_->Shutdown();
        producer_.reset();
    }
    
    consumers_.clear();
}

KafkaConsumer* KafkaIntegration::CreateConsumer(const std::string& consumer_id) {
    auto consumer = std::make_unique<KafkaConsumer>(config_);
    if (!consumer->Initialize()) {
        return nullptr;
    }
    
    KafkaConsumer* consumer_ptr = consumer.get();
    consumers_[consumer_id] = std::move(consumer);
    return consumer_ptr;
}

void KafkaIntegration::StartWorkers() {
    if (running_) {
        return;
    }
    
    running_ = true;
    
    // 启动工作线程
    worker_threads_.emplace_back(&KafkaIntegration::TickWorker, this);
    worker_threads_.emplace_back(&KafkaIntegration::Level2Worker, this);
    worker_threads_.emplace_back(&KafkaIntegration::BatchWorker, this);
}

void KafkaIntegration::StopWorkers() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    worker_threads_.clear();
}

bool KafkaIntegration::PushTick(const StandardTick& tick) {
    return tick_queue_->TryPush(tick);
}

bool KafkaIntegration::PushLevel2(const Level2Data& level2) {
    return level2_queue_->TryPush(level2);
}

bool KafkaIntegration::PushBatch(const MarketDataBatch& batch) {
    return batch_queue_->TryPush(batch);
}

void KafkaIntegration::TickWorker() {
    StandardTick tick;
    while (running_) {
        if (tick_queue_->TryPop(tick)) {
            if (producer_) {
                producer_->SendTick(tick);
            }
        } else {
            std::this_thread::sleep_for(std::chrono::microseconds(1));
        }
    }
}

void KafkaIntegration::Level2Worker() {
    Level2Data level2;
    while (running_) {
        if (level2_queue_->TryPop(level2)) {
            if (producer_) {
                producer_->SendLevel2(level2);
            }
        } else {
            std::this_thread::sleep_for(std::chrono::microseconds(1));
        }
    }
}

void KafkaIntegration::BatchWorker() {
    MarketDataBatch batch;
    while (running_) {
        if (batch_queue_->TryPop(batch)) {
            if (producer_) {
                producer_->SendBatch(batch);
            }
        } else {
            std::this_thread::sleep_for(std::chrono::microseconds(1));
        }
    }
}

} // namespace databus
} // namespace financial_data