# 集成测试CMake配置

cmake_minimum_required(VERSION 3.16)

# 查找必要的依赖
find_package(GTest REQUIRED)
find_package(GMock REQUIRED)
find_package(PkgConfig REQUIRED)

# 查找jsoncpp
pkg_check_modules(<PERSON><PERSON><PERSON><PERSON> jsoncpp)
if(NOT JSONCPP_FOUND)
    find_package(jsoncpp REQUIRED)
    set(JSONCPP_LIBRARIES jsoncpp_lib)
endif()

# 包含目录
include_directories(${CMAKE_SOURCE_DIR})
include_directories(${GTEST_INCLUDE_DIRS})
include_directories(${GMOCK_INCLUDE_DIRS})

# 端到端集成测试可执行文件
add_executable(end_to_end_integration_test
    end_to_end_integration_test.cpp
    ${CMAKE_SOURCE_DIR}/src/storage/unified_data_access.cpp
    ${CMAKE_SOURCE_DIR}/src/storage/storage_layer_selector.cpp
    ${CMAKE_SOURCE_DIR}/src/storage/query_performance_optimizer.cpp
    ${CMAKE_SOURCE_DIR}/src/config/config_manager.cpp
    ${CMAKE_SOURCE_DIR}/src/config/config_validators.cpp
    ${CMAKE_SOURCE_DIR}/src/data_models.cpp
)

# 链接库
target_link_libraries(end_to_end_integration_test
    ${GTEST_LIBRARIES}
    ${GMOCK_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    pthread
)

# 如果找到Redis客户端库，链接它
find_library(HIREDIS_LIB hiredis)
if(HIREDIS_LIB)
    target_link_libraries(end_to_end_integration_test ${HIREDIS_LIB})
    target_compile_definitions(end_to_end_integration_test PRIVATE HAVE_REDIS)
endif()

# 如果找到ClickHouse客户端库，链接它
find_library(CLICKHOUSE_CPP_LIB clickhouse-cpp-lib)
if(CLICKHOUSE_CPP_LIB)
    target_link_libraries(end_to_end_integration_test ${CLICKHOUSE_CPP_LIB})
    target_compile_definitions(end_to_end_integration_test PRIVATE HAVE_CLICKHOUSE)
endif()

# 编译选项
target_compile_options(end_to_end_integration_test PRIVATE
    -std=c++17
    -Wall
    -Wextra
    -O2
)

# 定义测试宏
target_compile_definitions(end_to_end_integration_test PRIVATE
    TESTING=1
    GTEST_HAS_PTHREAD=1
)

# 设置输出目录
set_target_properties(end_to_end_integration_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/integration
)

# 添加测试到CTest
enable_testing()
add_test(NAME EndToEndIntegrationTest COMMAND end_to_end_integration_test)

# 设置测试环境变量
set_tests_properties(EndToEndIntegrationTest PROPERTIES
    ENVIRONMENT "TESTING=1"
    TIMEOUT 600
)

# 创建测试数据目录
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/tests/data)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/tests/integration/reports)

# 复制测试配置文件
configure_file(
    ${CMAKE_SOURCE_DIR}/config/unified_config.json
    ${CMAKE_BINARY_DIR}/config/test_config.json
    COPYONLY
)

message(STATUS "Integration tests configured successfully")
# 性
能压力测试可执行文件
add_executable(performance_stress_test
    performance_stress_test.cpp
    ${CMAKE_SOURCE_DIR}/src/storage/unified_data_access.cpp
    ${CMAKE_SOURCE_DIR}/src/storage/storage_layer_selector.cpp
    ${CMAKE_SOURCE_DIR}/src/storage/query_performance_optimizer.cpp
    ${CMAKE_SOURCE_DIR}/src/config/config_manager.cpp
    ${CMAKE_SOURCE_DIR}/src/data_models.cpp
)

# 链接库
target_link_libraries(performance_stress_test
    ${GTEST_LIBRARIES}
    ${GMOCK_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    pthread
)

# 如果找到性能分析库，链接它们
find_library(BENCHMARK_LIB benchmark)
if(BENCHMARK_LIB)
    target_link_libraries(performance_stress_test ${BENCHMARK_LIB})
    target_compile_definitions(performance_stress_test PRIVATE HAVE_BENCHMARK)
endif()

# 编译选项
target_compile_options(performance_stress_test PRIVATE
    -std=c++17
    -Wall
    -Wextra
    -O2
)

# 定义测试宏
target_compile_definitions(performance_stress_test PRIVATE
    TESTING=1
    GTEST_HAS_PTHREAD=1
)

# 设置输出目录
set_target_properties(performance_stress_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/integration
)

# 添加性能测试到CTest
add_test(NAME PerformanceStressTest COMMAND performance_stress_test)

# 设置测试环境变量和超时
set_tests_properties(PerformanceStressTest PROPERTIES
    ENVIRONMENT "TESTING=1"
    TIMEOUT 1800  # 30分钟超时
)

# 数据一致性验证器可执行文件
add_executable(data_consistency_validator
    data_consistency_validator.cpp
    ${CMAKE_SOURCE_DIR}/src/storage/unified_data_access.cpp
    ${CMAKE_SOURCE_DIR}/src/storage/storage_layer_selector.cpp
    ${CMAKE_SOURCE_DIR}/src/config/config_manager.cpp
    ${CMAKE_SOURCE_DIR}/src/data_models.cpp
)

# 链接库
target_link_libraries(data_consistency_validator
    ${GTEST_LIBRARIES}
    ${GMOCK_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    pthread
)

# 编译选项
target_compile_options(data_consistency_validator PRIVATE
    -std=c++17
    -Wall
    -Wextra
    -O2
)

# 定义测试宏
target_compile_definitions(data_consistency_validator PRIVATE
    TESTING=1
    GTEST_HAS_PTHREAD=1
)

# 设置输出目录
set_target_properties(data_consistency_validator PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/integration
)

# 添加一致性测试到CTest
add_test(NAME DataConsistencyValidation COMMAND data_consistency_validator)

# 设置测试环境变量
set_tests_properties(DataConsistencyValidation PROPERTIES
    ENVIRONMENT "TESTING=1"
    TIMEOUT 900  # 15分钟超时
)

message(STATUS "Performance and consistency tests configured successfully")