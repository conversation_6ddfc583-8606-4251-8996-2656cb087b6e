# 开发环境使用指南

## 概述

本指南介绍如何使用简化的 Docker Compose 配置来搭建金融数据服务的开发测试环境。

## 环境特点

- **简化配置**: 专注于基本功能，不追求高性能
- **单实例部署**: 所有服务都使用单实例配置
- **资源优化**: 针对开发环境进行了内存和CPU限制
- **快速启动**: 减少了复杂的集群配置

## 服务组件

### 核心数据库服务
- **Redis**: 缓存和热存储 (端口: 6379)
- **ClickHouse**: 时序数据存储 (端口: 8123, 9000)

### 消息队列
- **Kafka**: 消息队列服务 (端口: 9092)
- **Zookeeper**: Kafka 协调服务 (端口: 2181)

### 对象存储
- **MinIO**: S3 兼容的对象存储 (端口: 9001, 9002)

### 监控服务 (可选)
- **Prometheus**: 监控数据收集 (端口: 9090)
- **Grafana**: 监控面板 (端口: 3000)

## 快速开始

### 1. 启动开发环境

**Windows:**
```cmd
scripts\start_dev_env.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/start_dev_env.sh
./scripts/start_dev_env.sh
```

### 2. 检查服务状态

**Windows:**
```cmd
scripts\check_dev_services.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/check_dev_services.sh
./scripts/check_dev_services.sh
```

### 3. 停止开发环境

**Windows:**
```cmd
scripts\stop_dev_env.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/stop_dev_env.sh
./scripts/stop_dev_env.sh
```

## 手动操作

### 启动基础服务
```bash
docker-compose -f docker-compose.dev.yml up -d
```

### 启动包含监控的完整服务
```bash
docker-compose -f docker-compose.dev.yml --profile monitoring up -d
```

### 查看服务日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.dev.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.dev.yml logs -f redis
docker-compose -f docker-compose.dev.yml logs -f clickhouse
docker-compose -f docker-compose.dev.yml logs -f kafka
```

### 停止并清理数据
```bash
docker-compose -f docker-compose.dev.yml down -v
```

## 服务访问

### Redis
```bash
# 使用 redis-cli 连接
redis-cli -h localhost -p 6379

# 或通过 Docker 容器
docker exec -it financial-redis-dev redis-cli
```

### ClickHouse
```bash
# HTTP 接口
curl "http://localhost:8123/?query=SHOW DATABASES"

# 客户端连接
clickhouse-client --host localhost --port 9000 --user admin --password password123

# 或通过 Docker 容器
docker exec -it financial-clickhouse-dev clickhouse-client
```

### Kafka
```bash
# 列出主题
docker exec financial-kafka-dev kafka-topics --bootstrap-server localhost:9092 --list

# 创建主题
docker exec financial-kafka-dev kafka-topics --bootstrap-server localhost:9092 --create --topic test-topic --partitions 1 --replication-factor 1

# 生产消息
docker exec -it financial-kafka-dev kafka-console-producer --bootstrap-server localhost:9092 --topic test-topic

# 消费消息
docker exec -it financial-kafka-dev kafka-console-consumer --bootstrap-server localhost:9092 --topic test-topic --from-beginning
```

### MinIO
- Web 控制台: http://localhost:9001
- 用户名: admin
- 密码: password123

## 数据库结构

### ClickHouse 数据库
- `market_data`: 市场数据存储
  - `futures_tick`: 期货 Tick 数据
  - `stock_tick`: 股票 Tick 数据
  - `kline_1m`: 1分钟 K线数据
  - `kline_5m`: 5分钟 K线数据
  - `kline_1d`: 日线数据

- `metadata`: 元数据存储
  - `instruments`: 合约信息
  - `trading_calendar`: 交易日历

## 测试数据

开发环境会自动插入一些测试数据，包括：
- 期货合约 IF2401 的 Tick 数据
- 股票 000001 的 Tick 数据
- 对应的 K线数据
- 合约信息和交易日历

## 性能配置

### 资源限制
- Redis: 最大内存 128MB
- ClickHouse: 内存限制 1GB
- Kafka: 内存限制 512MB
- Zookeeper: 内存限制 256MB
- MinIO: 内存限制 256MB

### 数据保留
- Kafka: 消息保留 24 小时
- ClickHouse: 无 TTL 配置（开发环境）
- Redis: LRU 淘汰策略

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口是否被占用: `netstat -an | findstr :6379`
   - 修改 docker-compose.dev.yml 中的端口映射

2. **内存不足**
   - 增加 Docker Desktop 的内存限制
   - 减少并发启动的服务数量

3. **ClickHouse 启动失败**
   - 检查初始化脚本语法: `config/clickhouse-init-dev.sql`
   - 查看容器日志: `docker logs financial-clickhouse-dev`

4. **Kafka 连接问题**
   - 确保 Zookeeper 先启动
   - 检查网络连接: `docker network ls`

### 日志查看
```bash
# 查看所有服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看特定服务的详细日志
docker-compose -f docker-compose.dev.yml logs --tail=100 -f [service_name]
```

## 开发建议

1. **数据持久化**: 开发环境数据会持久化到 Docker volumes
2. **配置修改**: 修改配置文件后需要重启对应服务
3. **性能测试**: 此配置不适合性能测试，请使用生产环境配置
4. **数据备份**: 重要数据请及时备份，避免容器删除时丢失

## 下一步

- 查看 [构建和运行指南](../BUILD_AND_RUN_GUIDE.md) 了解如何编译和运行应用程序
- 查看 [客户端使用指南](../clients/README.md) 了解如何使用各种语言的客户端
- 查看 [部署指南](../deployment/DEPLOYMENT_SUMMARY.md) 了解生产环境部署