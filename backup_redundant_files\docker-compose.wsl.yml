version: '3.3'

# WSL环境下的简化Docker Compose配置
# 专为测试和开发环境优化

services:
  # Redis - 内存数据库
  redis:
    image: redis:7-alpine
    container_name: financial-redis-wsl
    ports:
      - "6379:6379"
    volumes:
      - redis_data_wsl:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # ClickHouse - 时序数据库 (轻量配置)
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: financial-clickhouse-wsl
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - ./config/clickhouse-init-dev.sql:/docker-entrypoint-initdb.d/init.sql
      - clickhouse_data_wsl:/var/lib/clickhouse
    environment:
      CLICKHOUSE_DB: financial_data
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: password123
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # MinIO - 对象存储 (可选)
  minio:
    image: minio/minio:latest
    container_name: financial-minio-wsl
    ports:
      - "9001:9001"
      - "9002:9002"
    volumes:
      - minio_data_wsl:/data
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password123
    command: server /data --console-address ":9001" --address ":9002"
    restart: unless-stopped
    profiles:
      - storage
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: financial-prometheus-wsl
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data_wsl:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  redis_data_wsl:
    driver: local
  clickhouse_data_wsl:
    driver: local
  minio_data_wsl:
    driver: local
  prometheus_data_wsl:
    driver: local

networks:
  default:
    name: financial-wsl-network
    driver: bridge