@echo off
REM 金融数据服务 - 任务调度器启动脚本 (Windows)
REM Financial Data Service - Scheduler Startup Script (Windows)

setlocal enabledelayedexpansion

echo ========================================
echo 金融数据服务 - 任务调度器
echo Financial Data Service - Task Scheduler
echo ========================================
echo.

REM 设置项目根目录
set "PROJECT_ROOT=%~dp0.."
cd /d "%PROJECT_ROOT%"

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM 检查依赖包
echo 检查Python依赖包...
python -c "import asyncio, logging, json" >nul 2>&1
if errorlevel 1 (
    echo 错误: 缺少必要的Python包
    echo Error: Missing required Python packages
    echo 请运行: pip install -r requirements.txt
    echo Please run: pip install -r requirements.txt
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "config" mkdir config

REM 检查配置文件
set "CONFIG_FILE=config\scheduler_config.json"
if not exist "%CONFIG_FILE%" (
    echo 警告: 配置文件不存在: %CONFIG_FILE%
    echo Warning: Configuration file not found: %CONFIG_FILE%
    echo 将使用默认配置
    echo Will use default configuration
)

REM 解析命令行参数
set "LOG_LEVEL=INFO"
set "DAEMON_MODE=false"
set "SHOW_STATUS=false"
set "STOP_SERVICE=false"

:parse_args
if "%~1"=="" goto :start_service
if "%~1"=="--log-level" (
    set "LOG_LEVEL=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="-l" (
    set "LOG_LEVEL=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--config" (
    set "CONFIG_FILE=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="-c" (
    set "CONFIG_FILE=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--daemon" (
    set "DAEMON_MODE=true"
    shift
    goto :parse_args
)
if "%~1"=="-d" (
    set "DAEMON_MODE=true"
    shift
    goto :parse_args
)
if "%~1"=="--status" (
    set "SHOW_STATUS=true"
    shift
    goto :parse_args
)
if "%~1"=="--stop" (
    set "STOP_SERVICE=true"
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :show_help
)
if "%~1"=="-h" (
    goto :show_help
)
shift
goto :parse_args

:show_help
echo.
echo 用法: %~nx0 [选项]
echo Usage: %~nx0 [options]
echo.
echo 选项 Options:
echo   --config, -c FILE     配置文件路径 Configuration file path
echo   --log-level, -l LEVEL 日志级别 Log level (DEBUG, INFO, WARNING, ERROR)
echo   --daemon, -d          后台运行模式 Daemon mode (Windows不支持 Not supported on Windows)
echo   --status              显示服务状态 Show service status
echo   --stop                停止服务 Stop service
echo   --help, -h            显示帮助 Show help
echo.
echo 示例 Examples:
echo   %~nx0                           使用默认配置启动 Start with default config
echo   %~nx0 --config custom.json     使用自定义配置 Start with custom config
echo   %~nx0 --log-level DEBUG        设置日志级别 Set log level
echo   %~nx0 --status                  显示状态 Show status
echo   %~nx0 --stop                    停止服务 Stop service
echo.
pause
exit /b 0

:start_service
REM 检查服务状态
if "%SHOW_STATUS%"=="true" (
    echo 检查服务状态...
    python scripts\start_scheduler.py --status
    pause
    exit /b 0
)

REM 停止服务
if "%STOP_SERVICE%"=="true" (
    echo 停止服务...
    python scripts\start_scheduler.py --stop
    pause
    exit /b 0
)

REM 显示启动信息
echo 配置文件: %CONFIG_FILE%
echo 日志级别: %LOG_LEVEL%
echo.

REM Windows不支持真正的后台模式，但可以最小化窗口
if "%DAEMON_MODE%"=="true" (
    echo 注意: Windows系统不支持真正的后台模式
    echo Note: True daemon mode is not supported on Windows
    echo 将以最小化窗口模式启动...
    echo Starting with minimized window...
    echo.
    
    REM 创建VBS脚本来最小化窗口运行
    echo Set WshShell = CreateObject("WScript.Shell"^) > temp_run_minimized.vbs
    echo WshShell.Run "cmd /c python scripts\start_scheduler.py --config ""%CONFIG_FILE%"" --log-level %LOG_LEVEL%", 7, False >> temp_run_minimized.vbs
    
    cscript //nologo temp_run_minimized.vbs
    del temp_run_minimized.vbs
    
    echo 调度器服务已在后台启动
    echo Scheduler service started in background
    pause
    exit /b 0
)

REM 前台模式启动
echo 启动调度器服务...
echo Starting scheduler service...
echo.
echo 按 Ctrl+C 停止服务
echo Press Ctrl+C to stop the service
echo.

REM 启动Python调度器
python scripts\start_scheduler.py --config "%CONFIG_FILE%" --log-level %LOG_LEVEL%

REM 检查退出代码
if errorlevel 1 (
    echo.
    echo 服务启动失败，退出代码: %errorlevel%
    echo Service failed to start, exit code: %errorlevel%
    pause
    exit /b %errorlevel%
)

echo.
echo 调度器服务已停止
echo Scheduler service stopped
pause
exit /b 0