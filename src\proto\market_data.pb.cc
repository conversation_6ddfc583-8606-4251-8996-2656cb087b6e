// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: market_data.proto

#include "market_data.pb.h"

#include <algorithm>
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/extension_set.h"
#include "google/protobuf/wire_format_lite.h"
#include "google/protobuf/descriptor.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/reflection_ops.h"
#include "google/protobuf/wire_format.h"
#include "google/protobuf/generated_message_tctable_impl.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"
PROTOBUF_PRAGMA_INIT_SEG
namespace _pb = ::google::protobuf;
namespace _pbi = ::google::protobuf::internal;
namespace _fl = ::google::protobuf::internal::field_layout;
namespace financial_data {

inline constexpr ProtoTickData::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : symbol_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        trade_flag_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        timestamp_ns_{::int64_t{0}},
        last_price_{0},
        volume_{::uint64_t{0u}},
        turnover_{0},
        open_interest_{::uint64_t{0u}},
        sequence_{0u},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProtoTickData::ProtoTickData(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct ProtoTickDataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtoTickDataDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtoTickDataDefaultTypeInternal() {}
  union {
    ProtoTickData _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtoTickDataDefaultTypeInternal _ProtoTickData_default_instance_;

inline constexpr ProtoSubscriptionResponse::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : message_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        subscription_id_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        success_{false},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProtoSubscriptionResponse::ProtoSubscriptionResponse(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct ProtoSubscriptionResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtoSubscriptionResponseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtoSubscriptionResponseDefaultTypeInternal() {}
  union {
    ProtoSubscriptionResponse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtoSubscriptionResponseDefaultTypeInternal _ProtoSubscriptionResponse_default_instance_;

inline constexpr ProtoSubscriptionRequest::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : symbols_{},
        data_types_{},
        start_time_ns_{::int64_t{0}},
        include_history_{false},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProtoSubscriptionRequest::ProtoSubscriptionRequest(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct ProtoSubscriptionRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtoSubscriptionRequestDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtoSubscriptionRequestDefaultTypeInternal() {}
  union {
    ProtoSubscriptionRequest _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtoSubscriptionRequestDefaultTypeInternal _ProtoSubscriptionRequest_default_instance_;

inline constexpr ProtoPriceLevel::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : price_{0},
        volume_{0u},
        order_count_{0u},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProtoPriceLevel::ProtoPriceLevel(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct ProtoPriceLevelDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtoPriceLevelDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtoPriceLevelDefaultTypeInternal() {}
  union {
    ProtoPriceLevel _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtoPriceLevelDefaultTypeInternal _ProtoPriceLevel_default_instance_;

inline constexpr ProtoLevel2Data::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : bids_{},
        asks_{},
        symbol_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        timestamp_ns_{::int64_t{0}},
        sequence_{0u},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProtoLevel2Data::ProtoLevel2Data(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct ProtoLevel2DataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtoLevel2DataDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtoLevel2DataDefaultTypeInternal() {}
  union {
    ProtoLevel2Data _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtoLevel2DataDefaultTypeInternal _ProtoLevel2Data_default_instance_;

inline constexpr ProtoMarketData::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : source_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        receive_time_ns_{::int64_t{0}},
        data_type_{},
        _cached_size_{0},
        _oneof_case_{} {}

template <typename>
PROTOBUF_CONSTEXPR ProtoMarketData::ProtoMarketData(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct ProtoMarketDataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtoMarketDataDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtoMarketDataDefaultTypeInternal() {}
  union {
    ProtoMarketData _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtoMarketDataDefaultTypeInternal _ProtoMarketData_default_instance_;

inline constexpr ProtoMarketDataBatch::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : data_{},
        batch_timestamp_ns_{::int64_t{0}},
        batch_sequence_{0u},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProtoMarketDataBatch::ProtoMarketDataBatch(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct ProtoMarketDataBatchDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtoMarketDataBatchDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtoMarketDataBatchDefaultTypeInternal() {}
  union {
    ProtoMarketDataBatch _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtoMarketDataBatchDefaultTypeInternal _ProtoMarketDataBatch_default_instance_;
}  // namespace financial_data
static ::_pb::Metadata file_level_metadata_market_5fdata_2eproto[7];
static constexpr const ::_pb::EnumDescriptor**
    file_level_enum_descriptors_market_5fdata_2eproto = nullptr;
static constexpr const ::_pb::ServiceDescriptor**
    file_level_service_descriptors_market_5fdata_2eproto = nullptr;
const ::uint32_t TableStruct_market_5fdata_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(
    protodesc_cold) = {
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoPriceLevel, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoPriceLevel, _impl_.price_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoPriceLevel, _impl_.volume_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoPriceLevel, _impl_.order_count_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.timestamp_ns_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.symbol_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.last_price_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.volume_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.turnover_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.open_interest_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.sequence_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoTickData, _impl_.trade_flag_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoLevel2Data, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoLevel2Data, _impl_.timestamp_ns_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoLevel2Data, _impl_.symbol_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoLevel2Data, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoLevel2Data, _impl_.bids_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoLevel2Data, _impl_.asks_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoLevel2Data, _impl_.sequence_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketData, _internal_metadata_),
    ~0u,  // no _extensions_
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketData, _impl_._oneof_case_[0]),
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    ::_pbi::kInvalidFieldOffsetTag,
    ::_pbi::kInvalidFieldOffsetTag,
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketData, _impl_.receive_time_ns_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketData, _impl_.source_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketData, _impl_.data_type_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketDataBatch, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketDataBatch, _impl_.data_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketDataBatch, _impl_.batch_timestamp_ns_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketDataBatch, _impl_.batch_sequence_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionRequest, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionRequest, _impl_.symbols_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionRequest, _impl_.data_types_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionRequest, _impl_.include_history_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionRequest, _impl_.start_time_ns_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionResponse, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionResponse, _impl_.success_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionResponse, _impl_.message_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoSubscriptionResponse, _impl_.subscription_id_),
};

static const ::_pbi::MigrationSchema
    schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
        {0, -1, -1, sizeof(::financial_data::ProtoPriceLevel)},
        {11, -1, -1, sizeof(::financial_data::ProtoTickData)},
        {28, -1, -1, sizeof(::financial_data::ProtoLevel2Data)},
        {42, -1, -1, sizeof(::financial_data::ProtoMarketData)},
        {55, -1, -1, sizeof(::financial_data::ProtoMarketDataBatch)},
        {66, -1, -1, sizeof(::financial_data::ProtoSubscriptionRequest)},
        {78, -1, -1, sizeof(::financial_data::ProtoSubscriptionResponse)},
};

static const ::_pb::Message* const file_default_instances[] = {
    &::financial_data::_ProtoPriceLevel_default_instance_._instance,
    &::financial_data::_ProtoTickData_default_instance_._instance,
    &::financial_data::_ProtoLevel2Data_default_instance_._instance,
    &::financial_data::_ProtoMarketData_default_instance_._instance,
    &::financial_data::_ProtoMarketDataBatch_default_instance_._instance,
    &::financial_data::_ProtoSubscriptionRequest_default_instance_._instance,
    &::financial_data::_ProtoSubscriptionResponse_default_instance_._instance,
};
const char descriptor_table_protodef_market_5fdata_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
    "\n\021market_data.proto\022\016financial_data\"E\n\017P"
    "rotoPriceLevel\022\r\n\005price\030\001 \001(\001\022\016\n\006volume\030"
    "\002 \001(\r\022\023\n\013order_count\030\003 \001(\r\"\272\001\n\rProtoTick"
    "Data\022\024\n\014timestamp_ns\030\001 \001(\003\022\016\n\006symbol\030\002 \001"
    "(\t\022\020\n\010exchange\030\003 \001(\t\022\022\n\nlast_price\030\004 \001(\001"
    "\022\016\n\006volume\030\005 \001(\004\022\020\n\010turnover\030\006 \001(\001\022\025\n\rop"
    "en_interest\030\007 \001(\004\022\020\n\010sequence\030\010 \001(\r\022\022\n\nt"
    "rade_flag\030\t \001(\t\"\271\001\n\017ProtoLevel2Data\022\024\n\014t"
    "imestamp_ns\030\001 \001(\003\022\016\n\006symbol\030\002 \001(\t\022\020\n\010exc"
    "hange\030\003 \001(\t\022-\n\004bids\030\004 \003(\0132\037.financial_da"
    "ta.ProtoPriceLevel\022-\n\004asks\030\005 \003(\0132\037.finan"
    "cial_data.ProtoPriceLevel\022\020\n\010sequence\030\006 "
    "\001(\r\"\251\001\n\017ProtoMarketData\022-\n\004tick\030\001 \001(\0132\035."
    "financial_data.ProtoTickDataH\000\0221\n\006level2"
    "\030\002 \001(\0132\037.financial_data.ProtoLevel2DataH"
    "\000\022\027\n\017receive_time_ns\030\003 \001(\003\022\016\n\006source\030\004 \001"
    "(\tB\013\n\tdata_type\"y\n\024ProtoMarketDataBatch\022"
    "-\n\004data\030\001 \003(\0132\037.financial_data.ProtoMark"
    "etData\022\032\n\022batch_timestamp_ns\030\002 \001(\003\022\026\n\016ba"
    "tch_sequence\030\003 \001(\r\"o\n\030ProtoSubscriptionR"
    "equest\022\017\n\007symbols\030\001 \003(\t\022\022\n\ndata_types\030\002 "
    "\003(\t\022\027\n\017include_history\030\003 \001(\010\022\025\n\rstart_ti"
    "me_ns\030\004 \001(\003\"V\n\031ProtoSubscriptionResponse"
    "\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\027\n\017su"
    "bscription_id\030\003 \001(\tB\005H\001\370\001\001b\006proto3"
};
static ::absl::once_flag descriptor_table_market_5fdata_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_market_5fdata_2eproto = {
    false,
    false,
    994,
    descriptor_table_protodef_market_5fdata_2eproto,
    "market_data.proto",
    &descriptor_table_market_5fdata_2eproto_once,
    nullptr,
    0,
    7,
    schemas,
    file_default_instances,
    TableStruct_market_5fdata_2eproto::offsets,
    file_level_metadata_market_5fdata_2eproto,
    file_level_enum_descriptors_market_5fdata_2eproto,
    file_level_service_descriptors_market_5fdata_2eproto,
};

// This function exists to be marked as weak.
// It can significantly speed up compilation by breaking up LLVM's SCC
// in the .pb.cc translation units. Large translation units see a
// reduction of more than 35% of walltime for optimized builds. Without
// the weak attribute all the messages in the file, including all the
// vtables and everything they use become part of the same SCC through
// a cycle like:
// GetMetadata -> descriptor table -> default instances ->
//   vtables -> GetMetadata
// By adding a weak function here we break the connection from the
// individual vtables back into the descriptor table.
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_market_5fdata_2eproto_getter() {
  return &descriptor_table_market_5fdata_2eproto;
}
// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2
static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_market_5fdata_2eproto(&descriptor_table_market_5fdata_2eproto);
namespace financial_data {
// ===================================================================

class ProtoPriceLevel::_Internal {
 public:
};

ProtoPriceLevel::ProtoPriceLevel(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.ProtoPriceLevel)
}
ProtoPriceLevel::ProtoPriceLevel(
    ::google::protobuf::Arena* arena, const ProtoPriceLevel& from)
    : ProtoPriceLevel(arena) {
  MergeFrom(from);
}
inline PROTOBUF_NDEBUG_INLINE ProtoPriceLevel::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void ProtoPriceLevel::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, price_),
           0,
           offsetof(Impl_, order_count_) -
               offsetof(Impl_, price_) +
               sizeof(Impl_::order_count_));
}
ProtoPriceLevel::~ProtoPriceLevel() {
  // @@protoc_insertion_point(destructor:financial_data.ProtoPriceLevel)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void ProtoPriceLevel::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void ProtoPriceLevel::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.ProtoPriceLevel)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.price_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.order_count_) -
      reinterpret_cast<char*>(&_impl_.price_)) + sizeof(_impl_.order_count_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* ProtoPriceLevel::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 0, 0, 2> ProtoPriceLevel::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_ProtoPriceLevel_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // double price = 1;
    {::_pbi::TcParser::FastF64S1,
     {9, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoPriceLevel, _impl_.price_)}},
    // uint32 volume = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ProtoPriceLevel, _impl_.volume_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoPriceLevel, _impl_.volume_)}},
    // uint32 order_count = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ProtoPriceLevel, _impl_.order_count_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoPriceLevel, _impl_.order_count_)}},
  }}, {{
    65535, 65535
  }}, {{
    // double price = 1;
    {PROTOBUF_FIELD_OFFSET(ProtoPriceLevel, _impl_.price_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // uint32 volume = 2;
    {PROTOBUF_FIELD_OFFSET(ProtoPriceLevel, _impl_.volume_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt32)},
    // uint32 order_count = 3;
    {PROTOBUF_FIELD_OFFSET(ProtoPriceLevel, _impl_.order_count_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt32)},
  }},
  // no aux_entries
  {{
  }},
};

::uint8_t* ProtoPriceLevel::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.ProtoPriceLevel)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // double price = 1;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_price = this->_internal_price();
  ::uint64_t raw_price;
  memcpy(&raw_price, &tmp_price, sizeof(tmp_price));
  if (raw_price != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        1, this->_internal_price(), target);
  }

  // uint32 volume = 2;
  if (this->_internal_volume() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
        2, this->_internal_volume(), target);
  }

  // uint32 order_count = 3;
  if (this->_internal_order_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
        3, this->_internal_order_count(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.ProtoPriceLevel)
  return target;
}

::size_t ProtoPriceLevel::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.ProtoPriceLevel)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double price = 1;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_price = this->_internal_price();
  ::uint64_t raw_price;
  memcpy(&raw_price, &tmp_price, sizeof(tmp_price));
  if (raw_price != 0) {
    total_size += 9;
  }

  // uint32 volume = 2;
  if (this->_internal_volume() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
        this->_internal_volume());
  }

  // uint32 order_count = 3;
  if (this->_internal_order_count() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
        this->_internal_order_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData ProtoPriceLevel::_class_data_ = {
    ProtoPriceLevel::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* ProtoPriceLevel::GetClassData() const {
  return &_class_data_;
}

void ProtoPriceLevel::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<ProtoPriceLevel*>(&to_msg);
  auto& from = static_cast<const ProtoPriceLevel&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.ProtoPriceLevel)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_price = from._internal_price();
  ::uint64_t raw_price;
  memcpy(&raw_price, &tmp_price, sizeof(tmp_price));
  if (raw_price != 0) {
    _this->_internal_set_price(from._internal_price());
  }
  if (from._internal_volume() != 0) {
    _this->_internal_set_volume(from._internal_volume());
  }
  if (from._internal_order_count() != 0) {
    _this->_internal_set_order_count(from._internal_order_count());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProtoPriceLevel::CopyFrom(const ProtoPriceLevel& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.ProtoPriceLevel)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool ProtoPriceLevel::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* ProtoPriceLevel::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void ProtoPriceLevel::InternalSwap(ProtoPriceLevel* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProtoPriceLevel, _impl_.order_count_)
      + sizeof(ProtoPriceLevel::_impl_.order_count_)
      - PROTOBUF_FIELD_OFFSET(ProtoPriceLevel, _impl_.price_)>(
          reinterpret_cast<char*>(&_impl_.price_),
          reinterpret_cast<char*>(&other->_impl_.price_));
}

::google::protobuf::Metadata ProtoPriceLevel::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_2eproto_getter, &descriptor_table_market_5fdata_2eproto_once,
      file_level_metadata_market_5fdata_2eproto[0]);
}
// ===================================================================

class ProtoTickData::_Internal {
 public:
};

ProtoTickData::ProtoTickData(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.ProtoTickData)
}
inline PROTOBUF_NDEBUG_INLINE ProtoTickData::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : symbol_(arena, from.symbol_),
        exchange_(arena, from.exchange_),
        trade_flag_(arena, from.trade_flag_),
        _cached_size_{0} {}

ProtoTickData::ProtoTickData(
    ::google::protobuf::Arena* arena,
    const ProtoTickData& from)
    : ::google::protobuf::Message(arena) {
  ProtoTickData* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_ns_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, timestamp_ns_),
           offsetof(Impl_, sequence_) -
               offsetof(Impl_, timestamp_ns_) +
               sizeof(Impl_::sequence_));

  // @@protoc_insertion_point(copy_constructor:financial_data.ProtoTickData)
}
inline PROTOBUF_NDEBUG_INLINE ProtoTickData::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : symbol_(arena),
        exchange_(arena),
        trade_flag_(arena),
        _cached_size_{0} {}

inline void ProtoTickData::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_ns_),
           0,
           offsetof(Impl_, sequence_) -
               offsetof(Impl_, timestamp_ns_) +
               sizeof(Impl_::sequence_));
}
ProtoTickData::~ProtoTickData() {
  // @@protoc_insertion_point(destructor:financial_data.ProtoTickData)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void ProtoTickData::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.symbol_.Destroy();
  _impl_.exchange_.Destroy();
  _impl_.trade_flag_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void ProtoTickData::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.ProtoTickData)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.symbol_.ClearToEmpty();
  _impl_.exchange_.ClearToEmpty();
  _impl_.trade_flag_.ClearToEmpty();
  ::memset(&_impl_.timestamp_ns_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.sequence_) -
      reinterpret_cast<char*>(&_impl_.timestamp_ns_)) + sizeof(_impl_.sequence_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* ProtoTickData::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<4, 9, 0, 69, 2> ProtoTickData::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    9, 120,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294966784,  // skipmap
    offsetof(decltype(_table_), field_entries),
    9,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_ProtoTickData_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // int64 timestamp_ns = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ProtoTickData, _impl_.timestamp_ns_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.timestamp_ns_)}},
    // string symbol = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.symbol_)}},
    // string exchange = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.exchange_)}},
    // double last_price = 4;
    {::_pbi::TcParser::FastF64S1,
     {33, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.last_price_)}},
    // uint64 volume = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ProtoTickData, _impl_.volume_), 63>(),
     {40, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.volume_)}},
    // double turnover = 6;
    {::_pbi::TcParser::FastF64S1,
     {49, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.turnover_)}},
    // uint64 open_interest = 7;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ProtoTickData, _impl_.open_interest_), 63>(),
     {56, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.open_interest_)}},
    // uint32 sequence = 8;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ProtoTickData, _impl_.sequence_), 63>(),
     {64, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.sequence_)}},
    // string trade_flag = 9;
    {::_pbi::TcParser::FastUS1,
     {74, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.trade_flag_)}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 timestamp_ns = 1;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.timestamp_ns_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // string symbol = 2;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.symbol_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string exchange = 3;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // double last_price = 4;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.last_price_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // uint64 volume = 5;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.volume_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt64)},
    // double turnover = 6;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.turnover_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // uint64 open_interest = 7;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.open_interest_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt64)},
    // uint32 sequence = 8;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.sequence_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt32)},
    // string trade_flag = 9;
    {PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.trade_flag_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\34\0\6\10\0\0\0\0\0\12\0\0\0\0\0\0"
    "financial_data.ProtoTickData"
    "symbol"
    "exchange"
    "trade_flag"
  }},
};

::uint8_t* ProtoTickData::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.ProtoTickData)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // int64 timestamp_ns = 1;
  if (this->_internal_timestamp_ns() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<1>(
            stream, this->_internal_timestamp_ns(), target);
  }

  // string symbol = 2;
  if (!this->_internal_symbol().empty()) {
    const std::string& _s = this->_internal_symbol();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoTickData.symbol");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // string exchange = 3;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoTickData.exchange");
    target = stream->WriteStringMaybeAliased(3, _s, target);
  }

  // double last_price = 4;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_last_price = this->_internal_last_price();
  ::uint64_t raw_last_price;
  memcpy(&raw_last_price, &tmp_last_price, sizeof(tmp_last_price));
  if (raw_last_price != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        4, this->_internal_last_price(), target);
  }

  // uint64 volume = 5;
  if (this->_internal_volume() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
        5, this->_internal_volume(), target);
  }

  // double turnover = 6;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = this->_internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        6, this->_internal_turnover(), target);
  }

  // uint64 open_interest = 7;
  if (this->_internal_open_interest() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
        7, this->_internal_open_interest(), target);
  }

  // uint32 sequence = 8;
  if (this->_internal_sequence() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
        8, this->_internal_sequence(), target);
  }

  // string trade_flag = 9;
  if (!this->_internal_trade_flag().empty()) {
    const std::string& _s = this->_internal_trade_flag();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoTickData.trade_flag");
    target = stream->WriteStringMaybeAliased(9, _s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.ProtoTickData)
  return target;
}

::size_t ProtoTickData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.ProtoTickData)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string symbol = 2;
  if (!this->_internal_symbol().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_symbol());
  }

  // string exchange = 3;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // string trade_flag = 9;
  if (!this->_internal_trade_flag().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_trade_flag());
  }

  // int64 timestamp_ns = 1;
  if (this->_internal_timestamp_ns() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_timestamp_ns());
  }

  // double last_price = 4;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_last_price = this->_internal_last_price();
  ::uint64_t raw_last_price;
  memcpy(&raw_last_price, &tmp_last_price, sizeof(tmp_last_price));
  if (raw_last_price != 0) {
    total_size += 9;
  }

  // uint64 volume = 5;
  if (this->_internal_volume() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
        this->_internal_volume());
  }

  // double turnover = 6;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = this->_internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    total_size += 9;
  }

  // uint64 open_interest = 7;
  if (this->_internal_open_interest() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
        this->_internal_open_interest());
  }

  // uint32 sequence = 8;
  if (this->_internal_sequence() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
        this->_internal_sequence());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData ProtoTickData::_class_data_ = {
    ProtoTickData::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* ProtoTickData::GetClassData() const {
  return &_class_data_;
}

void ProtoTickData::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<ProtoTickData*>(&to_msg);
  auto& from = static_cast<const ProtoTickData&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.ProtoTickData)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_symbol().empty()) {
    _this->_internal_set_symbol(from._internal_symbol());
  }
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (!from._internal_trade_flag().empty()) {
    _this->_internal_set_trade_flag(from._internal_trade_flag());
  }
  if (from._internal_timestamp_ns() != 0) {
    _this->_internal_set_timestamp_ns(from._internal_timestamp_ns());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_last_price = from._internal_last_price();
  ::uint64_t raw_last_price;
  memcpy(&raw_last_price, &tmp_last_price, sizeof(tmp_last_price));
  if (raw_last_price != 0) {
    _this->_internal_set_last_price(from._internal_last_price());
  }
  if (from._internal_volume() != 0) {
    _this->_internal_set_volume(from._internal_volume());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = from._internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    _this->_internal_set_turnover(from._internal_turnover());
  }
  if (from._internal_open_interest() != 0) {
    _this->_internal_set_open_interest(from._internal_open_interest());
  }
  if (from._internal_sequence() != 0) {
    _this->_internal_set_sequence(from._internal_sequence());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProtoTickData::CopyFrom(const ProtoTickData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.ProtoTickData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool ProtoTickData::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* ProtoTickData::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void ProtoTickData::InternalSwap(ProtoTickData* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.symbol_, &other->_impl_.symbol_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.trade_flag_, &other->_impl_.trade_flag_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.sequence_)
      + sizeof(ProtoTickData::_impl_.sequence_)
      - PROTOBUF_FIELD_OFFSET(ProtoTickData, _impl_.timestamp_ns_)>(
          reinterpret_cast<char*>(&_impl_.timestamp_ns_),
          reinterpret_cast<char*>(&other->_impl_.timestamp_ns_));
}

::google::protobuf::Metadata ProtoTickData::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_2eproto_getter, &descriptor_table_market_5fdata_2eproto_once,
      file_level_metadata_market_5fdata_2eproto[1]);
}
// ===================================================================

class ProtoLevel2Data::_Internal {
 public:
};

ProtoLevel2Data::ProtoLevel2Data(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.ProtoLevel2Data)
}
inline PROTOBUF_NDEBUG_INLINE ProtoLevel2Data::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : bids_{visibility, arena, from.bids_},
        asks_{visibility, arena, from.asks_},
        symbol_(arena, from.symbol_),
        exchange_(arena, from.exchange_),
        _cached_size_{0} {}

ProtoLevel2Data::ProtoLevel2Data(
    ::google::protobuf::Arena* arena,
    const ProtoLevel2Data& from)
    : ::google::protobuf::Message(arena) {
  ProtoLevel2Data* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_ns_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, timestamp_ns_),
           offsetof(Impl_, sequence_) -
               offsetof(Impl_, timestamp_ns_) +
               sizeof(Impl_::sequence_));

  // @@protoc_insertion_point(copy_constructor:financial_data.ProtoLevel2Data)
}
inline PROTOBUF_NDEBUG_INLINE ProtoLevel2Data::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : bids_{visibility, arena},
        asks_{visibility, arena},
        symbol_(arena),
        exchange_(arena),
        _cached_size_{0} {}

inline void ProtoLevel2Data::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_ns_),
           0,
           offsetof(Impl_, sequence_) -
               offsetof(Impl_, timestamp_ns_) +
               sizeof(Impl_::sequence_));
}
ProtoLevel2Data::~ProtoLevel2Data() {
  // @@protoc_insertion_point(destructor:financial_data.ProtoLevel2Data)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void ProtoLevel2Data::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.symbol_.Destroy();
  _impl_.exchange_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void ProtoLevel2Data::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.ProtoLevel2Data)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.bids_.Clear();
  _impl_.asks_.Clear();
  _impl_.symbol_.ClearToEmpty();
  _impl_.exchange_.ClearToEmpty();
  ::memset(&_impl_.timestamp_ns_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.sequence_) -
      reinterpret_cast<char*>(&_impl_.timestamp_ns_)) + sizeof(_impl_.sequence_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* ProtoLevel2Data::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 6, 2, 53, 2> ProtoLevel2Data::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    6, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967232,  // skipmap
    offsetof(decltype(_table_), field_entries),
    6,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_ProtoLevel2Data_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // int64 timestamp_ns = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ProtoLevel2Data, _impl_.timestamp_ns_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.timestamp_ns_)}},
    // string symbol = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.symbol_)}},
    // string exchange = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.exchange_)}},
    // repeated .financial_data.ProtoPriceLevel bids = 4;
    {::_pbi::TcParser::FastMtR1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.bids_)}},
    // repeated .financial_data.ProtoPriceLevel asks = 5;
    {::_pbi::TcParser::FastMtR1,
     {42, 63, 1, PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.asks_)}},
    // uint32 sequence = 6;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ProtoLevel2Data, _impl_.sequence_), 63>(),
     {48, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.sequence_)}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 timestamp_ns = 1;
    {PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.timestamp_ns_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // string symbol = 2;
    {PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.symbol_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string exchange = 3;
    {PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // repeated .financial_data.ProtoPriceLevel bids = 4;
    {PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.bids_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated .financial_data.ProtoPriceLevel asks = 5;
    {PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.asks_), 0, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // uint32 sequence = 6;
    {PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.sequence_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt32)},
  }}, {{
    {::_pbi::TcParser::GetTable<::financial_data::ProtoPriceLevel>()},
    {::_pbi::TcParser::GetTable<::financial_data::ProtoPriceLevel>()},
  }}, {{
    "\36\0\6\10\0\0\0\0"
    "financial_data.ProtoLevel2Data"
    "symbol"
    "exchange"
  }},
};

::uint8_t* ProtoLevel2Data::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.ProtoLevel2Data)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // int64 timestamp_ns = 1;
  if (this->_internal_timestamp_ns() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<1>(
            stream, this->_internal_timestamp_ns(), target);
  }

  // string symbol = 2;
  if (!this->_internal_symbol().empty()) {
    const std::string& _s = this->_internal_symbol();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoLevel2Data.symbol");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // string exchange = 3;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoLevel2Data.exchange");
    target = stream->WriteStringMaybeAliased(3, _s, target);
  }

  // repeated .financial_data.ProtoPriceLevel bids = 4;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_bids_size()); i < n; i++) {
    const auto& repfield = this->_internal_bids().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(4, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .financial_data.ProtoPriceLevel asks = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_asks_size()); i < n; i++) {
    const auto& repfield = this->_internal_asks().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // uint32 sequence = 6;
  if (this->_internal_sequence() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
        6, this->_internal_sequence(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.ProtoLevel2Data)
  return target;
}

::size_t ProtoLevel2Data::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.ProtoLevel2Data)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .financial_data.ProtoPriceLevel bids = 4;
  total_size += 1UL * this->_internal_bids_size();
  for (const auto& msg : this->_internal_bids()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // repeated .financial_data.ProtoPriceLevel asks = 5;
  total_size += 1UL * this->_internal_asks_size();
  for (const auto& msg : this->_internal_asks()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // string symbol = 2;
  if (!this->_internal_symbol().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_symbol());
  }

  // string exchange = 3;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // int64 timestamp_ns = 1;
  if (this->_internal_timestamp_ns() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_timestamp_ns());
  }

  // uint32 sequence = 6;
  if (this->_internal_sequence() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
        this->_internal_sequence());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData ProtoLevel2Data::_class_data_ = {
    ProtoLevel2Data::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* ProtoLevel2Data::GetClassData() const {
  return &_class_data_;
}

void ProtoLevel2Data::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<ProtoLevel2Data*>(&to_msg);
  auto& from = static_cast<const ProtoLevel2Data&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.ProtoLevel2Data)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_bids()->MergeFrom(
      from._internal_bids());
  _this->_internal_mutable_asks()->MergeFrom(
      from._internal_asks());
  if (!from._internal_symbol().empty()) {
    _this->_internal_set_symbol(from._internal_symbol());
  }
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (from._internal_timestamp_ns() != 0) {
    _this->_internal_set_timestamp_ns(from._internal_timestamp_ns());
  }
  if (from._internal_sequence() != 0) {
    _this->_internal_set_sequence(from._internal_sequence());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProtoLevel2Data::CopyFrom(const ProtoLevel2Data& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.ProtoLevel2Data)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool ProtoLevel2Data::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* ProtoLevel2Data::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void ProtoLevel2Data::InternalSwap(ProtoLevel2Data* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.bids_.InternalSwap(&other->_impl_.bids_);
  _impl_.asks_.InternalSwap(&other->_impl_.asks_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.symbol_, &other->_impl_.symbol_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.sequence_)
      + sizeof(ProtoLevel2Data::_impl_.sequence_)
      - PROTOBUF_FIELD_OFFSET(ProtoLevel2Data, _impl_.timestamp_ns_)>(
          reinterpret_cast<char*>(&_impl_.timestamp_ns_),
          reinterpret_cast<char*>(&other->_impl_.timestamp_ns_));
}

::google::protobuf::Metadata ProtoLevel2Data::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_2eproto_getter, &descriptor_table_market_5fdata_2eproto_once,
      file_level_metadata_market_5fdata_2eproto[2]);
}
// ===================================================================

class ProtoMarketData::_Internal {
 public:
  static constexpr ::int32_t kOneofCaseOffset =
    PROTOBUF_FIELD_OFFSET(::financial_data::ProtoMarketData, _impl_._oneof_case_);
  static const ::financial_data::ProtoTickData& tick(const ProtoMarketData* msg);
  static const ::financial_data::ProtoLevel2Data& level2(const ProtoMarketData* msg);
};

const ::financial_data::ProtoTickData& ProtoMarketData::_Internal::tick(const ProtoMarketData* msg) {
  return *msg->_impl_.data_type_.tick_;
}
const ::financial_data::ProtoLevel2Data& ProtoMarketData::_Internal::level2(const ProtoMarketData* msg) {
  return *msg->_impl_.data_type_.level2_;
}
void ProtoMarketData::set_allocated_tick(::financial_data::ProtoTickData* tick) {
  ::google::protobuf::Arena* message_arena = GetArena();
  clear_data_type();
  if (tick) {
    ::google::protobuf::Arena* submessage_arena = tick->GetArena();
    if (message_arena != submessage_arena) {
      tick = ::google::protobuf::internal::GetOwnedMessage(message_arena, tick, submessage_arena);
    }
    set_has_tick();
    _impl_.data_type_.tick_ = tick;
  }
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoMarketData.tick)
}
void ProtoMarketData::set_allocated_level2(::financial_data::ProtoLevel2Data* level2) {
  ::google::protobuf::Arena* message_arena = GetArena();
  clear_data_type();
  if (level2) {
    ::google::protobuf::Arena* submessage_arena = level2->GetArena();
    if (message_arena != submessage_arena) {
      level2 = ::google::protobuf::internal::GetOwnedMessage(message_arena, level2, submessage_arena);
    }
    set_has_level2();
    _impl_.data_type_.level2_ = level2;
  }
  // @@protoc_insertion_point(field_set_allocated:financial_data.ProtoMarketData.level2)
}
ProtoMarketData::ProtoMarketData(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.ProtoMarketData)
}
inline PROTOBUF_NDEBUG_INLINE ProtoMarketData::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : source_(arena, from.source_),
        data_type_{},
        _cached_size_{0},
        _oneof_case_{from._oneof_case_[0]} {}

ProtoMarketData::ProtoMarketData(
    ::google::protobuf::Arena* arena,
    const ProtoMarketData& from)
    : ::google::protobuf::Message(arena) {
  ProtoMarketData* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  _impl_.receive_time_ns_ = from._impl_.receive_time_ns_;
  switch (data_type_case()) {
    case DATA_TYPE_NOT_SET:
      break;
      case kTick:
        _impl_.data_type_.tick_ = CreateMaybeMessage<::financial_data::ProtoTickData>(arena, *from._impl_.data_type_.tick_);
        break;
      case kLevel2:
        _impl_.data_type_.level2_ = CreateMaybeMessage<::financial_data::ProtoLevel2Data>(arena, *from._impl_.data_type_.level2_);
        break;
  }

  // @@protoc_insertion_point(copy_constructor:financial_data.ProtoMarketData)
}
inline PROTOBUF_NDEBUG_INLINE ProtoMarketData::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : source_(arena),
        data_type_{},
        _cached_size_{0},
        _oneof_case_{} {}

inline void ProtoMarketData::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.receive_time_ns_ = {};
}
ProtoMarketData::~ProtoMarketData() {
  // @@protoc_insertion_point(destructor:financial_data.ProtoMarketData)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void ProtoMarketData::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.source_.Destroy();
  if (has_data_type()) {
    clear_data_type();
  }
  _impl_.~Impl_();
}

void ProtoMarketData::clear_data_type() {
// @@protoc_insertion_point(one_of_clear_start:financial_data.ProtoMarketData)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  switch (data_type_case()) {
    case kTick: {
      if (GetArena() == nullptr) {
        delete _impl_.data_type_.tick_;
      }
      break;
    }
    case kLevel2: {
      if (GetArena() == nullptr) {
        delete _impl_.data_type_.level2_;
      }
      break;
    }
    case DATA_TYPE_NOT_SET: {
      break;
    }
  }
  _impl_._oneof_case_[0] = DATA_TYPE_NOT_SET;
}


PROTOBUF_NOINLINE void ProtoMarketData::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.ProtoMarketData)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.source_.ClearToEmpty();
  _impl_.receive_time_ns_ = ::int64_t{0};
  clear_data_type();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* ProtoMarketData::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 4, 2, 45, 2> ProtoMarketData::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    4, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_ProtoMarketData_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // string source = 4;
    {::_pbi::TcParser::FastUS1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoMarketData, _impl_.source_)}},
    // int64 receive_time_ns = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ProtoMarketData, _impl_.receive_time_ns_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoMarketData, _impl_.receive_time_ns_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .financial_data.ProtoTickData tick = 1;
    {PROTOBUF_FIELD_OFFSET(ProtoMarketData, _impl_.data_type_.tick_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kMessage | ::_fl::kTvTable)},
    // .financial_data.ProtoLevel2Data level2 = 2;
    {PROTOBUF_FIELD_OFFSET(ProtoMarketData, _impl_.data_type_.level2_), _Internal::kOneofCaseOffset + 0, 1,
    (0 | ::_fl::kFcOneof | ::_fl::kMessage | ::_fl::kTvTable)},
    // int64 receive_time_ns = 3;
    {PROTOBUF_FIELD_OFFSET(ProtoMarketData, _impl_.receive_time_ns_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // string source = 4;
    {PROTOBUF_FIELD_OFFSET(ProtoMarketData, _impl_.source_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }}, {{
    {::_pbi::TcParser::GetTable<::financial_data::ProtoTickData>()},
    {::_pbi::TcParser::GetTable<::financial_data::ProtoLevel2Data>()},
  }}, {{
    "\36\0\0\0\6\0\0\0"
    "financial_data.ProtoMarketData"
    "source"
  }},
};

::uint8_t* ProtoMarketData::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.ProtoMarketData)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  switch (data_type_case()) {
    case kTick: {
      target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
          1, _Internal::tick(this),
          _Internal::tick(this).GetCachedSize(), target, stream);
      break;
    }
    case kLevel2: {
      target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
          2, _Internal::level2(this),
          _Internal::level2(this).GetCachedSize(), target, stream);
      break;
    }
    default:
      break;
  }
  // int64 receive_time_ns = 3;
  if (this->_internal_receive_time_ns() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<3>(
            stream, this->_internal_receive_time_ns(), target);
  }

  // string source = 4;
  if (!this->_internal_source().empty()) {
    const std::string& _s = this->_internal_source();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoMarketData.source");
    target = stream->WriteStringMaybeAliased(4, _s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.ProtoMarketData)
  return target;
}

::size_t ProtoMarketData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.ProtoMarketData)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string source = 4;
  if (!this->_internal_source().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_source());
  }

  // int64 receive_time_ns = 3;
  if (this->_internal_receive_time_ns() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_receive_time_ns());
  }

  switch (data_type_case()) {
    // .financial_data.ProtoTickData tick = 1;
    case kTick: {
      total_size +=
          1 + ::google::protobuf::internal::WireFormatLite::MessageSize(*_impl_.data_type_.tick_);
      break;
    }
    // .financial_data.ProtoLevel2Data level2 = 2;
    case kLevel2: {
      total_size +=
          1 + ::google::protobuf::internal::WireFormatLite::MessageSize(*_impl_.data_type_.level2_);
      break;
    }
    case DATA_TYPE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData ProtoMarketData::_class_data_ = {
    ProtoMarketData::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* ProtoMarketData::GetClassData() const {
  return &_class_data_;
}

void ProtoMarketData::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<ProtoMarketData*>(&to_msg);
  auto& from = static_cast<const ProtoMarketData&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.ProtoMarketData)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_source().empty()) {
    _this->_internal_set_source(from._internal_source());
  }
  if (from._internal_receive_time_ns() != 0) {
    _this->_internal_set_receive_time_ns(from._internal_receive_time_ns());
  }
  switch (from.data_type_case()) {
    case kTick: {
      _this->_internal_mutable_tick()->::financial_data::ProtoTickData::MergeFrom(
          from._internal_tick());
      break;
    }
    case kLevel2: {
      _this->_internal_mutable_level2()->::financial_data::ProtoLevel2Data::MergeFrom(
          from._internal_level2());
      break;
    }
    case DATA_TYPE_NOT_SET: {
      break;
    }
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProtoMarketData::CopyFrom(const ProtoMarketData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.ProtoMarketData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool ProtoMarketData::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* ProtoMarketData::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void ProtoMarketData::InternalSwap(ProtoMarketData* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.source_, &other->_impl_.source_, arena);
        swap(_impl_.receive_time_ns_, other->_impl_.receive_time_ns_);
  swap(_impl_.data_type_, other->_impl_.data_type_);
  swap(_impl_._oneof_case_[0], other->_impl_._oneof_case_[0]);
}

::google::protobuf::Metadata ProtoMarketData::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_2eproto_getter, &descriptor_table_market_5fdata_2eproto_once,
      file_level_metadata_market_5fdata_2eproto[3]);
}
// ===================================================================

class ProtoMarketDataBatch::_Internal {
 public:
};

ProtoMarketDataBatch::ProtoMarketDataBatch(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.ProtoMarketDataBatch)
}
inline PROTOBUF_NDEBUG_INLINE ProtoMarketDataBatch::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : data_{visibility, arena, from.data_},
        _cached_size_{0} {}

ProtoMarketDataBatch::ProtoMarketDataBatch(
    ::google::protobuf::Arena* arena,
    const ProtoMarketDataBatch& from)
    : ::google::protobuf::Message(arena) {
  ProtoMarketDataBatch* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, batch_timestamp_ns_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, batch_timestamp_ns_),
           offsetof(Impl_, batch_sequence_) -
               offsetof(Impl_, batch_timestamp_ns_) +
               sizeof(Impl_::batch_sequence_));

  // @@protoc_insertion_point(copy_constructor:financial_data.ProtoMarketDataBatch)
}
inline PROTOBUF_NDEBUG_INLINE ProtoMarketDataBatch::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : data_{visibility, arena},
        _cached_size_{0} {}

inline void ProtoMarketDataBatch::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, batch_timestamp_ns_),
           0,
           offsetof(Impl_, batch_sequence_) -
               offsetof(Impl_, batch_timestamp_ns_) +
               sizeof(Impl_::batch_sequence_));
}
ProtoMarketDataBatch::~ProtoMarketDataBatch() {
  // @@protoc_insertion_point(destructor:financial_data.ProtoMarketDataBatch)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void ProtoMarketDataBatch::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void ProtoMarketDataBatch::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.ProtoMarketDataBatch)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.data_.Clear();
  ::memset(&_impl_.batch_timestamp_ns_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.batch_sequence_) -
      reinterpret_cast<char*>(&_impl_.batch_timestamp_ns_)) + sizeof(_impl_.batch_sequence_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* ProtoMarketDataBatch::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 1, 0, 2> ProtoMarketDataBatch::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_ProtoMarketDataBatch_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // repeated .financial_data.ProtoMarketData data = 1;
    {::_pbi::TcParser::FastMtR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoMarketDataBatch, _impl_.data_)}},
    // int64 batch_timestamp_ns = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ProtoMarketDataBatch, _impl_.batch_timestamp_ns_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoMarketDataBatch, _impl_.batch_timestamp_ns_)}},
    // uint32 batch_sequence = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ProtoMarketDataBatch, _impl_.batch_sequence_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoMarketDataBatch, _impl_.batch_sequence_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated .financial_data.ProtoMarketData data = 1;
    {PROTOBUF_FIELD_OFFSET(ProtoMarketDataBatch, _impl_.data_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // int64 batch_timestamp_ns = 2;
    {PROTOBUF_FIELD_OFFSET(ProtoMarketDataBatch, _impl_.batch_timestamp_ns_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // uint32 batch_sequence = 3;
    {PROTOBUF_FIELD_OFFSET(ProtoMarketDataBatch, _impl_.batch_sequence_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt32)},
  }}, {{
    {::_pbi::TcParser::GetTable<::financial_data::ProtoMarketData>()},
  }}, {{
  }},
};

::uint8_t* ProtoMarketDataBatch::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.ProtoMarketDataBatch)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // repeated .financial_data.ProtoMarketData data = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_data_size()); i < n; i++) {
    const auto& repfield = this->_internal_data().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // int64 batch_timestamp_ns = 2;
  if (this->_internal_batch_timestamp_ns() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<2>(
            stream, this->_internal_batch_timestamp_ns(), target);
  }

  // uint32 batch_sequence = 3;
  if (this->_internal_batch_sequence() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
        3, this->_internal_batch_sequence(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.ProtoMarketDataBatch)
  return target;
}

::size_t ProtoMarketDataBatch::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.ProtoMarketDataBatch)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .financial_data.ProtoMarketData data = 1;
  total_size += 1UL * this->_internal_data_size();
  for (const auto& msg : this->_internal_data()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // int64 batch_timestamp_ns = 2;
  if (this->_internal_batch_timestamp_ns() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_batch_timestamp_ns());
  }

  // uint32 batch_sequence = 3;
  if (this->_internal_batch_sequence() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
        this->_internal_batch_sequence());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData ProtoMarketDataBatch::_class_data_ = {
    ProtoMarketDataBatch::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* ProtoMarketDataBatch::GetClassData() const {
  return &_class_data_;
}

void ProtoMarketDataBatch::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<ProtoMarketDataBatch*>(&to_msg);
  auto& from = static_cast<const ProtoMarketDataBatch&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.ProtoMarketDataBatch)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_data()->MergeFrom(
      from._internal_data());
  if (from._internal_batch_timestamp_ns() != 0) {
    _this->_internal_set_batch_timestamp_ns(from._internal_batch_timestamp_ns());
  }
  if (from._internal_batch_sequence() != 0) {
    _this->_internal_set_batch_sequence(from._internal_batch_sequence());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProtoMarketDataBatch::CopyFrom(const ProtoMarketDataBatch& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.ProtoMarketDataBatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool ProtoMarketDataBatch::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* ProtoMarketDataBatch::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void ProtoMarketDataBatch::InternalSwap(ProtoMarketDataBatch* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.data_.InternalSwap(&other->_impl_.data_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProtoMarketDataBatch, _impl_.batch_sequence_)
      + sizeof(ProtoMarketDataBatch::_impl_.batch_sequence_)
      - PROTOBUF_FIELD_OFFSET(ProtoMarketDataBatch, _impl_.batch_timestamp_ns_)>(
          reinterpret_cast<char*>(&_impl_.batch_timestamp_ns_),
          reinterpret_cast<char*>(&other->_impl_.batch_timestamp_ns_));
}

::google::protobuf::Metadata ProtoMarketDataBatch::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_2eproto_getter, &descriptor_table_market_5fdata_2eproto_once,
      file_level_metadata_market_5fdata_2eproto[4]);
}
// ===================================================================

class ProtoSubscriptionRequest::_Internal {
 public:
};

ProtoSubscriptionRequest::ProtoSubscriptionRequest(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.ProtoSubscriptionRequest)
}
inline PROTOBUF_NDEBUG_INLINE ProtoSubscriptionRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : symbols_{visibility, arena, from.symbols_},
        data_types_{visibility, arena, from.data_types_},
        _cached_size_{0} {}

ProtoSubscriptionRequest::ProtoSubscriptionRequest(
    ::google::protobuf::Arena* arena,
    const ProtoSubscriptionRequest& from)
    : ::google::protobuf::Message(arena) {
  ProtoSubscriptionRequest* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, start_time_ns_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, start_time_ns_),
           offsetof(Impl_, include_history_) -
               offsetof(Impl_, start_time_ns_) +
               sizeof(Impl_::include_history_));

  // @@protoc_insertion_point(copy_constructor:financial_data.ProtoSubscriptionRequest)
}
inline PROTOBUF_NDEBUG_INLINE ProtoSubscriptionRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : symbols_{visibility, arena},
        data_types_{visibility, arena},
        _cached_size_{0} {}

inline void ProtoSubscriptionRequest::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, start_time_ns_),
           0,
           offsetof(Impl_, include_history_) -
               offsetof(Impl_, start_time_ns_) +
               sizeof(Impl_::include_history_));
}
ProtoSubscriptionRequest::~ProtoSubscriptionRequest() {
  // @@protoc_insertion_point(destructor:financial_data.ProtoSubscriptionRequest)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void ProtoSubscriptionRequest::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void ProtoSubscriptionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.ProtoSubscriptionRequest)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.symbols_.Clear();
  _impl_.data_types_.Clear();
  ::memset(&_impl_.start_time_ns_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.include_history_) -
      reinterpret_cast<char*>(&_impl_.start_time_ns_)) + sizeof(_impl_.include_history_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* ProtoSubscriptionRequest::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 0, 65, 2> ProtoSubscriptionRequest::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_ProtoSubscriptionRequest_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // int64 start_time_ns = 4;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ProtoSubscriptionRequest, _impl_.start_time_ns_), 63>(),
     {32, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.start_time_ns_)}},
    // repeated string symbols = 1;
    {::_pbi::TcParser::FastUR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.symbols_)}},
    // repeated string data_types = 2;
    {::_pbi::TcParser::FastUR1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.data_types_)}},
    // bool include_history = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<bool, offsetof(ProtoSubscriptionRequest, _impl_.include_history_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.include_history_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated string symbols = 1;
    {PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.symbols_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kUtf8String | ::_fl::kRepSString)},
    // repeated string data_types = 2;
    {PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.data_types_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kUtf8String | ::_fl::kRepSString)},
    // bool include_history = 3;
    {PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.include_history_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBool)},
    // int64 start_time_ns = 4;
    {PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.start_time_ns_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
  }},
  // no aux_entries
  {{
    "\47\7\12\0\0\0\0\0"
    "financial_data.ProtoSubscriptionRequest"
    "symbols"
    "data_types"
  }},
};

::uint8_t* ProtoSubscriptionRequest::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.ProtoSubscriptionRequest)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // repeated string symbols = 1;
  for (int i = 0, n = this->_internal_symbols_size(); i < n; ++i) {
    const auto& s = this->_internal_symbols().Get(i);
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        s.data(), static_cast<int>(s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoSubscriptionRequest.symbols");
    target = stream->WriteString(1, s, target);
  }

  // repeated string data_types = 2;
  for (int i = 0, n = this->_internal_data_types_size(); i < n; ++i) {
    const auto& s = this->_internal_data_types().Get(i);
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        s.data(), static_cast<int>(s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoSubscriptionRequest.data_types");
    target = stream->WriteString(2, s, target);
  }

  // bool include_history = 3;
  if (this->_internal_include_history() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(
        3, this->_internal_include_history(), target);
  }

  // int64 start_time_ns = 4;
  if (this->_internal_start_time_ns() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<4>(
            stream, this->_internal_start_time_ns(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.ProtoSubscriptionRequest)
  return target;
}

::size_t ProtoSubscriptionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.ProtoSubscriptionRequest)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string symbols = 1;
  total_size += 1 * ::google::protobuf::internal::FromIntSize(_internal_symbols().size());
  for (int i = 0, n = _internal_symbols().size(); i < n; ++i) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
        _internal_symbols().Get(i));
  }
  // repeated string data_types = 2;
  total_size += 1 * ::google::protobuf::internal::FromIntSize(_internal_data_types().size());
  for (int i = 0, n = _internal_data_types().size(); i < n; ++i) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
        _internal_data_types().Get(i));
  }
  // int64 start_time_ns = 4;
  if (this->_internal_start_time_ns() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_start_time_ns());
  }

  // bool include_history = 3;
  if (this->_internal_include_history() != 0) {
    total_size += 2;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData ProtoSubscriptionRequest::_class_data_ = {
    ProtoSubscriptionRequest::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* ProtoSubscriptionRequest::GetClassData() const {
  return &_class_data_;
}

void ProtoSubscriptionRequest::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<ProtoSubscriptionRequest*>(&to_msg);
  auto& from = static_cast<const ProtoSubscriptionRequest&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.ProtoSubscriptionRequest)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_symbols()->MergeFrom(from._internal_symbols());
  _this->_internal_mutable_data_types()->MergeFrom(from._internal_data_types());
  if (from._internal_start_time_ns() != 0) {
    _this->_internal_set_start_time_ns(from._internal_start_time_ns());
  }
  if (from._internal_include_history() != 0) {
    _this->_internal_set_include_history(from._internal_include_history());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProtoSubscriptionRequest::CopyFrom(const ProtoSubscriptionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.ProtoSubscriptionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool ProtoSubscriptionRequest::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* ProtoSubscriptionRequest::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void ProtoSubscriptionRequest::InternalSwap(ProtoSubscriptionRequest* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.symbols_.InternalSwap(&other->_impl_.symbols_);
  _impl_.data_types_.InternalSwap(&other->_impl_.data_types_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.include_history_)
      + sizeof(ProtoSubscriptionRequest::_impl_.include_history_)
      - PROTOBUF_FIELD_OFFSET(ProtoSubscriptionRequest, _impl_.start_time_ns_)>(
          reinterpret_cast<char*>(&_impl_.start_time_ns_),
          reinterpret_cast<char*>(&other->_impl_.start_time_ns_));
}

::google::protobuf::Metadata ProtoSubscriptionRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_2eproto_getter, &descriptor_table_market_5fdata_2eproto_once,
      file_level_metadata_market_5fdata_2eproto[5]);
}
// ===================================================================

class ProtoSubscriptionResponse::_Internal {
 public:
};

ProtoSubscriptionResponse::ProtoSubscriptionResponse(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.ProtoSubscriptionResponse)
}
inline PROTOBUF_NDEBUG_INLINE ProtoSubscriptionResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : message_(arena, from.message_),
        subscription_id_(arena, from.subscription_id_),
        _cached_size_{0} {}

ProtoSubscriptionResponse::ProtoSubscriptionResponse(
    ::google::protobuf::Arena* arena,
    const ProtoSubscriptionResponse& from)
    : ::google::protobuf::Message(arena) {
  ProtoSubscriptionResponse* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  _impl_.success_ = from._impl_.success_;

  // @@protoc_insertion_point(copy_constructor:financial_data.ProtoSubscriptionResponse)
}
inline PROTOBUF_NDEBUG_INLINE ProtoSubscriptionResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : message_(arena),
        subscription_id_(arena),
        _cached_size_{0} {}

inline void ProtoSubscriptionResponse::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.success_ = {};
}
ProtoSubscriptionResponse::~ProtoSubscriptionResponse() {
  // @@protoc_insertion_point(destructor:financial_data.ProtoSubscriptionResponse)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void ProtoSubscriptionResponse::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.message_.Destroy();
  _impl_.subscription_id_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void ProtoSubscriptionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.ProtoSubscriptionResponse)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.message_.ClearToEmpty();
  _impl_.subscription_id_.ClearToEmpty();
  _impl_.success_ = false;
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* ProtoSubscriptionResponse::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 0, 71, 2> ProtoSubscriptionResponse::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_ProtoSubscriptionResponse_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // bool success = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<bool, offsetof(ProtoSubscriptionResponse, _impl_.success_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoSubscriptionResponse, _impl_.success_)}},
    // string message = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoSubscriptionResponse, _impl_.message_)}},
    // string subscription_id = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(ProtoSubscriptionResponse, _impl_.subscription_id_)}},
  }}, {{
    65535, 65535
  }}, {{
    // bool success = 1;
    {PROTOBUF_FIELD_OFFSET(ProtoSubscriptionResponse, _impl_.success_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBool)},
    // string message = 2;
    {PROTOBUF_FIELD_OFFSET(ProtoSubscriptionResponse, _impl_.message_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string subscription_id = 3;
    {PROTOBUF_FIELD_OFFSET(ProtoSubscriptionResponse, _impl_.subscription_id_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\50\0\7\17\0\0\0\0"
    "financial_data.ProtoSubscriptionResponse"
    "message"
    "subscription_id"
  }},
};

::uint8_t* ProtoSubscriptionResponse::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.ProtoSubscriptionResponse)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(
        1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    const std::string& _s = this->_internal_message();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoSubscriptionResponse.message");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // string subscription_id = 3;
  if (!this->_internal_subscription_id().empty()) {
    const std::string& _s = this->_internal_subscription_id();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ProtoSubscriptionResponse.subscription_id");
    target = stream->WriteStringMaybeAliased(3, _s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.ProtoSubscriptionResponse)
  return target;
}

::size_t ProtoSubscriptionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.ProtoSubscriptionResponse)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_message());
  }

  // string subscription_id = 3;
  if (!this->_internal_subscription_id().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_subscription_id());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 2;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData ProtoSubscriptionResponse::_class_data_ = {
    ProtoSubscriptionResponse::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* ProtoSubscriptionResponse::GetClassData() const {
  return &_class_data_;
}

void ProtoSubscriptionResponse::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<ProtoSubscriptionResponse*>(&to_msg);
  auto& from = static_cast<const ProtoSubscriptionResponse&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.ProtoSubscriptionResponse)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _this->_internal_set_message(from._internal_message());
  }
  if (!from._internal_subscription_id().empty()) {
    _this->_internal_set_subscription_id(from._internal_subscription_id());
  }
  if (from._internal_success() != 0) {
    _this->_internal_set_success(from._internal_success());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProtoSubscriptionResponse::CopyFrom(const ProtoSubscriptionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.ProtoSubscriptionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool ProtoSubscriptionResponse::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* ProtoSubscriptionResponse::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void ProtoSubscriptionResponse::InternalSwap(ProtoSubscriptionResponse* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.message_, &other->_impl_.message_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.subscription_id_, &other->_impl_.subscription_id_, arena);
        swap(_impl_.success_, other->_impl_.success_);
}

::google::protobuf::Metadata ProtoSubscriptionResponse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_2eproto_getter, &descriptor_table_market_5fdata_2eproto_once,
      file_level_metadata_market_5fdata_2eproto[6]);
}
// @@protoc_insertion_point(namespace_scope)
}  // namespace financial_data
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google
// @@protoc_insertion_point(global_scope)
#include "google/protobuf/port_undef.inc"
