# Redis Cluster configuration for high availability hot data storage

# Basic settings
port 7000
bind 127.0.0.1
protected-mode no

# Cluster settings
cluster-enabled yes
cluster-config-file nodes-7000.conf
cluster-node-timeout 15000
cluster-announce-ip 127.0.0.1
cluster-announce-port 7000
cluster-announce-bus-port 17000
cluster-require-full-coverage no
cluster-allow-reads-when-down yes

# Memory configuration for hot data (7 days)
maxmemory 4gb
maxmemory-policy allkeys-lru
maxmemory-samples 10

# Persistence configuration - optimized for performance
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error no

# Performance optimizations
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Hot data specific settings
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Enable keyspace notifications for expiration events
notify-keyspace-events Ex

# Logging
loglevel notice
logfile "/var/log/redis/redis-7000.log"

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Cluster specific optimizations
cluster-require-full-coverage no
cluster-allow-reads-when-down yes