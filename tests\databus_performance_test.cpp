#include <gtest/gtest.h>
#include <chrono>
#include <thread>
#include <atomic>
#include <vector>
#include <iostream>
#include <random>

#include "../src/databus/data_bus.h"
#include "../src/databus/lock_free_queue.h"
#include "../src/proto/data_types.h"

using namespace financial_data;
using namespace financial_data::databus;

class DataBusPerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建高性能配置的数据总线
        data_bus_ = DataBusFactory::CreateHighPerformance();
        
        // 禁用Kafka以专注于内存处理性能
        auto config = data_bus_->GetConfig();
        config.enable_kafka = false;
        data_bus_->UpdateConfig(config);
        
        ASSERT_TRUE(data_bus_->Start());
    }
    
    void TearDown() override {
        if (data_bus_) {
            data_bus_->Stop();
        }
    }
    
    StandardTick CreateTestTick(uint32_t sequence = 0) {
        StandardTick tick;
        tick.timestamp_ns = StandardTick::GetCurrentTimestampNs();
        tick.symbol = "CU2409";
        tick.exchange = "SHFE";
        tick.last_price = 78560.0 + (sequence % 100) * 10.0;
        tick.volume = 1000 + sequence;
        tick.turnover = tick.last_price * tick.volume;
        tick.sequence = sequence;
        tick.trade_flag = "buy_open";
        
        // 设置买卖盘数据
        for (int i = 0; i < 5; ++i) {
            tick.bids[i] = PriceLevel(tick.last_price - (i + 1) * 10.0, 100 + i * 10, i + 1);
            tick.asks[i] = PriceLevel(tick.last_price + (i + 1) * 10.0, 100 + i * 10, i + 1);
        }
        
        return tick;
    }
    
    std::unique_ptr<DataBus> data_bus_;
};

/**
 * @brief 测试无锁队列的基本性能
 */
TEST_F(DataBusPerformanceTest, LockFreeQueueBasicPerformance) {
    const size_t test_count = 1000000;  // 100万条数据
    TickQueue queue;
    
    // 生产者性能测试
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < test_count; ++i) {
        StandardTick tick = CreateTestTick(i);
        while (!queue.TryPush(tick)) {
            std::this_thread::yield();
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double throughput = static_cast<double>(test_count) / duration.count() * 1000000.0;
    
    std::cout << "LockFreeQueue Push Performance:" << std::endl;
    std::cout << "  Messages: " << test_count << std::endl;
    std::cout << "  Duration: " << duration.count() << " microseconds" << std::endl;
    std::cout << "  Throughput: " << throughput << " messages/second" << std::endl;
    
    // 验证吞吐量达到100万条/秒
    EXPECT_GE(throughput, 1000000.0) << "Push throughput should be at least 1M messages/second";
    
    // 消费者性能测试
    start_time = std::chrono::high_resolution_clock::now();
    
    StandardTick tick;
    size_t consumed = 0;
    while (consumed < test_count) {
        if (queue.TryPop(tick)) {
            consumed++;
        } else {
            std::this_thread::yield();
        }
    }
    
    end_time = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    throughput = static_cast<double>(test_count) / duration.count() * 1000000.0;
    
    std::cout << "LockFreeQueue Pop Performance:" << std::endl;
    std::cout << "  Messages: " << test_count << std::endl;
    std::cout << "  Duration: " << duration.count() << " microseconds" << std::endl;
    std::cout << "  Throughput: " << throughput << " messages/second" << std::endl;
    
    EXPECT_GE(throughput, 1000000.0) << "Pop throughput should be at least 1M messages/second";
}

/**
 * @brief 测试SPSC（单生产者单消费者）队列性能
 */
TEST_F(DataBusPerformanceTest, SPSCQueuePerformance) {
    const size_t test_count = 1000000;
    TickQueue queue;
    
    std::atomic<bool> start_flag{false};
    std::atomic<size_t> produced{0};
    std::atomic<size_t> consumed{0};
    
    // 生产者线程
    std::thread producer([&]() {
        while (!start_flag.load()) {
            std::this_thread::yield();
        }
        
        for (size_t i = 0; i < test_count; ++i) {
            StandardTick tick = CreateTestTick(i);
            while (!queue.TryPush(tick)) {
                std::this_thread::yield();
            }
            produced.fetch_add(1);
        }
    });
    
    // 消费者线程
    std::thread consumer([&]() {
        while (!start_flag.load()) {
            std::this_thread::yield();
        }
        
        StandardTick tick;
        size_t count = 0;
        while (count < test_count) {
            if (queue.TryPop(tick)) {
                count++;
                consumed.fetch_add(1);
            } else {
                std::this_thread::yield();
            }
        }
    });
    
    // 开始测试
    auto start_time = std::chrono::high_resolution_clock::now();
    start_flag.store(true);
    
    producer.join();
    consumer.join();
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double throughput = static_cast<double>(test_count) / duration.count() * 1000000.0;
    
    std::cout << "SPSC Queue Performance:" << std::endl;
    std::cout << "  Messages: " << test_count << std::endl;
    std::cout << "  Duration: " << duration.count() << " microseconds" << std::endl;
    std::cout << "  Throughput: " << throughput << " messages/second" << std::endl;
    std::cout << "  Produced: " << produced.load() << ", Consumed: " << consumed.load() << std::endl;
    
    EXPECT_EQ(produced.load(), test_count);
    EXPECT_EQ(consumed.load(), test_count);
    EXPECT_GE(throughput, 1000000.0) << "SPSC throughput should be at least 1M messages/second";
}

/**
 * @brief 测试MPSC（多生产者单消费者）队列性能
 */
TEST_F(DataBusPerformanceTest, MPSCQueuePerformance) {
    const size_t producer_count = 4;
    const size_t test_count_per_producer = 250000;  // 每个生产者25万条，总共100万条
    const size_t total_expected = producer_count * test_count_per_producer;
    
    MPSCTickQueue queue;
    
    std::atomic<bool> start_flag{false};
    std::atomic<size_t> total_produced{0};
    std::atomic<size_t> total_consumed{0};
    
    std::vector<std::thread> producers;
    producers.reserve(producer_count);
    
    // 创建生产者线程
    for (size_t p = 0; p < producer_count; ++p) {
        producers.emplace_back([&, p]() {
            while (!start_flag.load()) {
                std::this_thread::yield();
            }
            
            for (size_t i = 0; i < test_count_per_producer; ++i) {
                StandardTick tick = CreateTestTick(p * test_count_per_producer + i);
                while (!queue.TryPush(tick)) {
                    std::this_thread::yield();
                }
                total_produced.fetch_add(1);
            }
        });
    }
    
    // 消费者线程
    std::thread consumer([&]() {
        while (!start_flag.load()) {
            std::this_thread::yield();
        }
        
        StandardTick tick;
        size_t count = 0;
        while (count < total_expected) {
            if (queue.TryPop(tick)) {
                count++;
                total_consumed.fetch_add(1);
            } else {
                std::this_thread::yield();
            }
        }
    });
    
    // 开始测试
    auto start_time = std::chrono::high_resolution_clock::now();
    start_flag.store(true);
    
    for (auto& producer : producers) {
        producer.join();
    }
    consumer.join();
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double throughput = static_cast<double>(total_expected) / duration.count() * 1000000.0;
    
    std::cout << "MPSC Queue Performance:" << std::endl;
    std::cout << "  Producers: " << producer_count << std::endl;
    std::cout << "  Messages per Producer: " << test_count_per_producer << std::endl;
    std::cout << "  Total Messages: " << total_expected << std::endl;
    std::cout << "  Duration: " << duration.count() << " microseconds" << std::endl;
    std::cout << "  Throughput: " << throughput << " messages/second" << std::endl;
    std::cout << "  Produced: " << total_produced.load() << ", Consumed: " << total_consumed.load() << std::endl;
    
    EXPECT_EQ(total_produced.load(), total_expected);
    EXPECT_EQ(total_consumed.load(), total_expected);
    EXPECT_GE(throughput, 1000000.0) << "MPSC throughput should be at least 1M messages/second";
}

/**
 * @brief 测试数据总线端到端性能
 */
TEST_F(DataBusPerformanceTest, DataBusEndToEndPerformance) {
    const size_t test_count = 1000000;
    std::atomic<size_t> messages_received{0};
    std::atomic<size_t> total_latency_ns{0};
    std::atomic<uint64_t> max_latency_ns{0};
    
    // 注册测试客户端
    std::string client_id = "performance_test_client";
    bool client_registered = data_bus_->RegisterClient(
        client_id, 
        "test",
        [&](const MarketDataWrapper& data) -> bool {
            auto receive_time = StandardTick::GetCurrentTimestampNs();
            uint64_t latency = receive_time - data.receive_time_ns;
            
            messages_received.fetch_add(1);
            total_latency_ns.fetch_add(latency);
            
            uint64_t current_max = max_latency_ns.load();
            while (latency > current_max && 
                   !max_latency_ns.compare_exchange_weak(current_max, latency)) {
                // CAS循环
            }
            
            return true;
        }
    );
    
    ASSERT_TRUE(client_registered);
    
    // 订阅测试合约
    ASSERT_TRUE(data_bus_->Subscribe(client_id, {"CU2409"}, {"SHFE"}));
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 开始性能测试
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < test_count; ++i) {
        StandardTick tick = CreateTestTick(i);
        
        // 记录发送时间
        tick.SetCurrentTimestamp();
        
        while (!data_bus_->PushTick(tick)) {
            std::this_thread::yield();
        }
        
        // 每10万条数据检查一次进度
        if (i % 100000 == 0 && i > 0) {
            std::cout << "Sent " << i << " messages..." << std::endl;
        }
    }
    
    // 等待所有消息处理完成
    auto timeout = std::chrono::steady_clock::now() + std::chrono::seconds(30);
    while (messages_received.load() < test_count && 
           std::chrono::steady_clock::now() < timeout) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double throughput = static_cast<double>(test_count) / duration.count() * 1000000.0;
    double avg_latency_ns = messages_received.load() > 0 ? 
                           static_cast<double>(total_latency_ns.load()) / messages_received.load() : 0.0;
    
    std::cout << "DataBus End-to-End Performance:" << std::endl;
    std::cout << "  Messages Sent: " << test_count << std::endl;
    std::cout << "  Messages Received: " << messages_received.load() << std::endl;
    std::cout << "  Duration: " << duration.count() << " microseconds" << std::endl;
    std::cout << "  Throughput: " << throughput << " messages/second" << std::endl;
    std::cout << "  Average Latency: " << avg_latency_ns / 1000.0 << " microseconds" << std::endl;
    std::cout << "  Max Latency: " << max_latency_ns.load() / 1000.0 << " microseconds" << std::endl;
    
    // 获取系统统计信息
    auto stats = data_bus_->GetStatistics();
    std::cout << "  Processing Rate: " << stats.GetProcessingRate() * 100.0 << "%" << std::endl;
    std::cout << "  Drop Rate: " << stats.GetDropRate() * 100.0 << "%" << std::endl;
    
    // 验证性能指标
    EXPECT_EQ(messages_received.load(), test_count) << "All messages should be received";
    EXPECT_GE(throughput, 1000000.0) << "Throughput should be at least 1M messages/second";
    EXPECT_LE(avg_latency_ns, 50000.0) << "Average latency should be less than 50 microseconds";  // 50微秒
    EXPECT_LE(max_latency_ns.load(), 1000000.0) << "Max latency should be less than 1 millisecond";
    
    // 清理
    data_bus_->UnregisterClient(client_id);
}

/**
 * @brief 测试背压控制下的性能
 */
TEST_F(DataBusPerformanceTest, BackpressurePerformance) {
    const size_t test_count = 2000000;  // 200万条数据，触发背压
    std::atomic<size_t> messages_sent{0};
    std::atomic<size_t> messages_dropped{0};
    
    // 注册一个慢速客户端来触发背压
    std::string client_id = "slow_client";
    bool client_registered = data_bus_->RegisterClient(
        client_id,
        "test",
        [&](const MarketDataWrapper& data) -> bool {
            // 模拟慢速处理
            std::this_thread::sleep_for(std::chrono::microseconds(10));
            return true;
        }
    );
    
    ASSERT_TRUE(client_registered);
    ASSERT_TRUE(data_bus_->Subscribe(client_id, {"CU2409"}, {"SHFE"}));
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < test_count; ++i) {
        StandardTick tick = CreateTestTick(i);
        
        if (data_bus_->PushTick(tick)) {
            messages_sent.fetch_add(1);
        } else {
            messages_dropped.fetch_add(1);
        }
        
        if (i % 100000 == 0 && i > 0) {
            auto bp_stats = data_bus_->GetBackpressureStatistics();
            std::cout << "Progress: " << i << ", Backpressure State: " 
                      << static_cast<int>(bp_stats.current_state) << std::endl;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double throughput = static_cast<double>(messages_sent.load()) / duration.count() * 1000000.0;
    
    std::cout << "Backpressure Performance Test:" << std::endl;
    std::cout << "  Total Messages: " << test_count << std::endl;
    std::cout << "  Messages Sent: " << messages_sent.load() << std::endl;
    std::cout << "  Messages Dropped: " << messages_dropped.load() << std::endl;
    std::cout << "  Duration: " << duration.count() << " microseconds" << std::endl;
    std::cout << "  Throughput: " << throughput << " messages/second" << std::endl;
    
    auto bp_stats = data_bus_->GetBackpressureStatistics();
    std::cout << "  Final Backpressure State: " << static_cast<int>(bp_stats.current_state) << std::endl;
    std::cout << "  Drop Rate: " << bp_stats.GetDropRate() * 100.0 << "%" << std::endl;
    
    // 验证背压控制有效
    EXPECT_GT(messages_dropped.load(), 0) << "Backpressure should cause some messages to be dropped";
    EXPECT_GT(bp_stats.dropped_messages.load(), 0) << "Backpressure controller should record dropped messages";
    
    data_bus_->UnregisterClient(client_id);
}

/**
 * @brief 测试多客户端并发性能
 */
TEST_F(DataBusPerformanceTest, MultiClientConcurrentPerformance) {
    const size_t client_count = 100;
    const size_t test_count = 1000000;
    
    std::vector<std::string> client_ids;
    std::atomic<size_t> total_messages_received{0};
    
    // 注册多个客户端
    for (size_t i = 0; i < client_count; ++i) {
        std::string client_id = "client_" + std::to_string(i);
        client_ids.push_back(client_id);
        
        bool registered = data_bus_->RegisterClient(
            client_id,
            "test",
            [&](const MarketDataWrapper& data) -> bool {
                total_messages_received.fetch_add(1);
                return true;
            }
        );
        
        ASSERT_TRUE(registered);
        ASSERT_TRUE(data_bus_->Subscribe(client_id, {"CU2409"}, {"SHFE"}));
    }
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 发送测试数据
    for (size_t i = 0; i < test_count; ++i) {
        StandardTick tick = CreateTestTick(i);
        
        while (!data_bus_->PushTick(tick)) {
            std::this_thread::yield();
        }
    }
    
    // 等待所有消息处理完成
    size_t expected_total = test_count * client_count;
    auto timeout = std::chrono::steady_clock::now() + std::chrono::seconds(60);
    
    while (total_messages_received.load() < expected_total && 
           std::chrono::steady_clock::now() < timeout) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double throughput = static_cast<double>(test_count) / duration.count() * 1000000.0;
    
    std::cout << "Multi-Client Concurrent Performance:" << std::endl;
    std::cout << "  Clients: " << client_count << std::endl;
    std::cout << "  Messages Sent: " << test_count << std::endl;
    std::cout << "  Expected Total Received: " << expected_total << std::endl;
    std::cout << "  Actual Total Received: " << total_messages_received.load() << std::endl;
    std::cout << "  Duration: " << duration.count() << " microseconds" << std::endl;
    std::cout << "  Input Throughput: " << throughput << " messages/second" << std::endl;
    
    // 验证所有客户端都收到了消息
    EXPECT_EQ(total_messages_received.load(), expected_total) 
        << "All clients should receive all messages";
    EXPECT_GE(throughput, 1000000.0) 
        << "Input throughput should be at least 1M messages/second";
    
    // 清理客户端
    for (const auto& client_id : client_ids) {
        data_bus_->UnregisterClient(client_id);
    }
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}