# Monitoring module CMakeLists.txt

# Find required packages
find_package(PkgConfig REQUIRED)
pkg_check_modules(PROMETHEUS_CPP REQUIRED prometheus-cpp)

# Create monitoring library
add_library(monitoring
    prometheus_metrics.cpp
    latency_monitor.cpp
    data_integrity_checker.cpp
    resource_monitor.cpp
    alert_manager.cpp
    metrics_collector.cpp
)

target_include_directories(monitoring PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${PROMETHEUS_CPP_INCLUDE_DIRS}
)

target_link_libraries(monitoring
    ${PROMETHEUS_CPP_LIBRARIES}
    pthread
)

target_compile_options(monitoring PRIVATE ${PROMETHEUS_CPP_CFLAGS_OTHER})