"""
Synchronous Financial Data Client with pandas/numpy support
"""

import grpc
import time
import logging
import threading
from typing import List, Dict, Optional, Callable, Union
from concurrent.futures import ThreadPoolExecutor, Future
import pandas as pd
import numpy as np

from .data_models import TickData, KlineData, Level2Data, DataFrameBuilder, NumpyArrayBuilder
from .cache import DataCache
from .utils import DataConverter
from .exceptions import ConnectionError, DataError, TimeoutError

# Import protobuf (placeholder - would be generated from .proto files)
# import market_data_service_pb2
# import market_data_service_pb2_grpc

logger = logging.getLogger(__name__)


class LoadBalancer:
    """Client-side load balancer for multiple servers"""
    
    def __init__(self, servers: List[str]):
        self.servers = servers
        self.server_stats = {
            server: {'latency': 0.0, 'errors': 0, 'healthy': True} 
            for server in servers
        }
        self.lock = threading.Lock()
    
    def get_best_server(self) -> str:
        """Select the best available server"""
        with self.lock:
            healthy_servers = [
                server for server, stats in self.server_stats.items() 
                if stats['healthy']
            ]
            
            if not healthy_servers:
                # Reset all servers if none are healthy
                for stats in self.server_stats.values():
                    stats['healthy'] = True
                    stats['errors'] = 0
                healthy_servers = list(self.servers)
            
            # Select server with lowest latency
            best_server = min(
                healthy_servers, 
                key=lambda s: self.server_stats[s]['latency']
            )
            return best_server
    
    def report_error(self, server: str):
        """Report server error"""
        with self.lock:
            if server in self.server_stats:
                self.server_stats[server]['errors'] += 1
                if self.server_stats[server]['errors'] > 3:
                    self.server_stats[server]['healthy'] = False
                    logger.warning(f"Server {server} marked as unhealthy")
    
    def update_latency(self, server: str, latency: float):
        """Update server latency statistics"""
        with self.lock:
            if server in self.server_stats:
                # Exponential moving average
                current = self.server_stats[server]['latency']
                self.server_stats[server]['latency'] = 0.7 * current + 0.3 * latency


class FinancialDataClient:
    """
    Synchronous Financial Data Client with pandas/numpy support
    
    Features:
    - Pandas DataFrame and numpy array support
    - Data caching and batch operations
    - Load balancing and failover
    - Technical indicators integration
    """
    
    def __init__(self, 
                 servers: List[str],
                 max_retries: int = 3,
                 cache_size: int = 10000,
                 enable_cache: bool = True):
        """
        Initialize the financial data client
        
        Args:
            servers: List of server addresses
            max_retries: Maximum retry attempts
            cache_size: Cache size for data storage
            enable_cache: Enable/disable data caching
        """
        self.load_balancer = LoadBalancer(servers)
        self.max_retries = max_retries
        self.channels = {}
        self.stubs = {}
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # Data processing components
        self.cache = DataCache(cache_size) if enable_cache else None
        self.converter = DataConverter()
        
        # Initialize connections
        self._initialize_connections()
    
    def _initialize_connections(self):
        """Initialize connections to all servers"""
        for server in self.load_balancer.servers:
            try:
                channel = grpc.insecure_channel(server, options=[
                    ('grpc.keepalive_time_ms', 30000),
                    ('grpc.keepalive_timeout_ms', 5000),
                    ('grpc.keepalive_permit_without_calls', True),
                    ('grpc.max_receive_message_length', 4 * 1024 * 1024),
                    ('grpc.max_send_message_length', 4 * 1024 * 1024),
                ])
                # stub = market_data_service_pb2_grpc.MarketDataServiceStub(channel)
                
                self.channels[server] = channel
                # self.stubs[server] = stub
                logger.info(f"Connected to server: {server}")
                
            except Exception as e:
                logger.error(f"Failed to connect to server {server}: {e}")
                self.load_balancer.report_error(server)
    
    def _get_stub_with_retry(self):
        """Get available stub with retry and failover"""
        for attempt in range(self.max_retries):
            server = self.load_balancer.get_best_server()
            if server in self.stubs:
                return self.stubs[server], server
            
            try:
                self._initialize_connections()
                if server in self.stubs:
                    return self.stubs[server], server
            except Exception as e:
                logger.error(f"Retry {attempt + 1} failed for server {server}: {e}")
                self.load_balancer.report_error(server)
                time.sleep(0.1 * (attempt + 1))
        
        raise ConnectionError("No healthy servers available")
    
    def get_tick_data(self, 
                     symbol: str, 
                     exchange: str = None,
                     start_time: Union[int, str, pd.Timestamp] = None,
                     end_time: Union[int, str, pd.Timestamp] = None,
                     limit: int = 1000,
                     as_dataframe: bool = True) -> Union[pd.DataFrame, List[TickData], np.ndarray]:
        """
        Get historical tick data
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            start_time: Start timestamp (various formats supported)
            end_time: End timestamp (various formats supported)
            limit: Maximum number of records
            as_dataframe: Return as pandas DataFrame
            
        Returns:
            DataFrame, list of TickData objects, or numpy array
        """
        # Convert timestamps
        start_ts = self.converter.to_nanoseconds(start_time) if start_time else None
        end_ts = self.converter.to_nanoseconds(end_time) if end_time else None
        
        # Check cache first
        cache_key = f"tick:{symbol}:{exchange}:{start_ts}:{end_ts}:{limit}"
        if self.cache:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                return cached_data
        
        try:
            stub, server = self._get_stub_with_retry()
            
            # Create request (placeholder)
            # request = market_data_service_pb2.TickDataRequest(
            #     symbol=symbol,
            #     exchange=exchange or "",
            #     start_timestamp=start_ts or 0,
            #     end_timestamp=end_ts or int(time.time() * 1_000_000_000),
            #     limit=limit
            # )
            
            start_time_req = time.time()
            # response = stub.GetTickData(request)
            latency = (time.time() - start_time_req) * 1000
            self.load_balancer.update_latency(server, latency)
            
            # Convert response to TickData objects (placeholder)
            ticks = []
            # for pb_tick in response.ticks:
            #     ticks.append(TickData.from_protobuf(pb_tick))
            
            # Cache the result
            if self.cache:
                self.cache.put(cache_key, ticks)
            
            # Return in requested format
            if as_dataframe:
                result = DataFrameBuilder.from_tick_list(ticks)
            else:
                result = ticks
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get tick data: {e}")
            raise DataError(f"Failed to get tick data: {e}")
    
    def get_kline_data(self,
                      symbol: str,
                      period: str = "1m",
                      exchange: str = None,
                      start_time: Union[int, str, pd.Timestamp] = None,
                      end_time: Union[int, str, pd.Timestamp] = None,
                      limit: int = 1000,
                      as_dataframe: bool = True) -> Union[pd.DataFrame, List[KlineData], np.ndarray]:
        """
        Get historical K-line data
        
        Args:
            symbol: Trading symbol
            period: Time period (1m, 5m, 1h, 1d, etc.)
            exchange: Exchange name
            start_time: Start timestamp
            end_time: End timestamp
            limit: Maximum number of records
            as_dataframe: Return as pandas DataFrame
            
        Returns:
            DataFrame, list of KlineData objects, or numpy array
        """
        start_ts = self.converter.to_nanoseconds(start_time) if start_time else None
        end_ts = self.converter.to_nanoseconds(end_time) if end_time else None
        
        cache_key = f"kline:{symbol}:{period}:{exchange}:{start_ts}:{end_ts}:{limit}"
        if self.cache:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                return cached_data
        
        try:
            stub, server = self._get_stub_with_retry()
            
            # Placeholder for actual implementation
            klines = []
            
            if self.cache:
                self.cache.put(cache_key, klines)
            
            if as_dataframe:
                result = DataFrameBuilder.from_kline_list(klines)
            else:
                result = klines
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get kline data: {e}")
            raise DataError(f"Failed to get kline data: {e}")
    
    def get_level2_data(self,
                       symbol: str,
                       exchange: str = None,
                       depth: int = 10,
                       start_time: Union[int, str, pd.Timestamp] = None,
                       end_time: Union[int, str, pd.Timestamp] = None,
                       limit: int = 1000,
                       as_dataframe: bool = True) -> Union[pd.DataFrame, List[Level2Data]]:
        """
        Get Level 2 market depth data
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            depth: Market depth levels
            start_time: Start timestamp
            end_time: End timestamp
            limit: Maximum number of records
            as_dataframe: Return as pandas DataFrame
            
        Returns:
            DataFrame or list of Level2Data objects
        """
        start_ts = self.converter.to_nanoseconds(start_time) if start_time else None
        end_ts = self.converter.to_nanoseconds(end_time) if end_time else None
        
        cache_key = f"level2:{symbol}:{exchange}:{depth}:{start_ts}:{end_ts}:{limit}"
        if self.cache:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                return cached_data
        
        try:
            stub, server = self._get_stub_with_retry()
            
            # Placeholder for actual implementation
            level2_data = []
            
            if self.cache:
                self.cache.put(cache_key, level2_data)
            
            if as_dataframe:
                result = DataFrameBuilder.from_level2_list(level2_data)
            else:
                result = level2_data
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get level2 data: {e}")
            raise DataError(f"Failed to get level2 data: {e}")
    
    def stream_tick_data(self,
                        symbols: List[str],
                        exchange: str = None,
                        callback: Callable[[TickData], None] = None,
                        buffer_size: int = 1000) -> Future:
        """
        Stream real-time tick data
        
        Args:
            symbols: List of trading symbols
            exchange: Exchange name
            callback: Callback function for each tick
            buffer_size: Stream buffer size
            
        Returns:
            Future object for the streaming task
        """
        def _stream_worker():
            stub, server = self._get_stub_with_retry()
            
            try:
                # Placeholder for streaming implementation
                # stream = stub.StreamTickData(request)
                # for response in stream:
                #     for pb_tick in response.ticks:
                #         tick = TickData.from_protobuf(pb_tick)
                #         if callback:
                #             callback(tick)
                pass
                
            except Exception as e:
                logger.error(f"Stream error: {e}")
                self.load_balancer.report_error(server)
                raise
        
        return self.executor.submit(_stream_worker)
    
    def get_batch_data(self,
                      requests: List[Dict],
                      as_dataframe: bool = True,
                      parallel: bool = True) -> Dict[str, Union[pd.DataFrame, List]]:
        """
        Get multiple data requests in batch
        
        Args:
            requests: List of data requests
            as_dataframe: Return as pandas DataFrames
            parallel: Execute requests in parallel
            
        Returns:
            Dictionary of results keyed by request identifier
        """
        if parallel:
            futures = {}
            for i, req in enumerate(requests):
                req_type = req.get('type', 'tick')
                if req_type == 'tick':
                    future = self.executor.submit(
                        self.get_tick_data,
                        req['symbol'],
                        req.get('exchange'),
                        req.get('start_time'),
                        req.get('end_time'),
                        req.get('limit', 1000),
                        as_dataframe
                    )
                elif req_type == 'kline':
                    future = self.executor.submit(
                        self.get_kline_data,
                        req['symbol'],
                        req.get('period', '1m'),
                        req.get('exchange'),
                        req.get('start_time'),
                        req.get('end_time'),
                        req.get('limit', 1000),
                        as_dataframe
                    )
                futures[req.get('id', f'req_{i}')] = future
            
            # Collect results
            results = {}
            for req_id, future in futures.items():
                try:
                    results[req_id] = future.result(timeout=30)
                except Exception as e:
                    logger.error(f"Batch request {req_id} failed: {e}")
                    results[req_id] = None
            
            return results
        else:
            # Sequential execution
            results = {}
            for i, req in enumerate(requests):
                req_id = req.get('id', f'req_{i}')
                try:
                    req_type = req.get('type', 'tick')
                    if req_type == 'tick':
                        results[req_id] = self.get_tick_data(
                            req['symbol'],
                            req.get('exchange'),
                            req.get('start_time'),
                            req.get('end_time'),
                            req.get('limit', 1000),
                            as_dataframe
                        )
                    elif req_type == 'kline':
                        results[req_id] = self.get_kline_data(
                            req['symbol'],
                            req.get('period', '1m'),
                            req.get('exchange'),
                            req.get('start_time'),
                            req.get('end_time'),
                            req.get('limit', 1000),
                            as_dataframe
                        )
                except Exception as e:
                    logger.error(f"Batch request {req_id} failed: {e}")
                    results[req_id] = None
            
            return results
    
    def health_check(self) -> bool:
        """Check service health"""
        try:
            stub, server = self._get_stub_with_retry()
            # request = market_data_service_pb2.HealthCheckRequest(service="MarketDataService")
            # response = stub.HealthCheck(request)
            # return response.status == market_data_service_pb2.HealthCheckResponse.SERVING
            return True  # Placeholder
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    def get_server_stats(self) -> Dict:
        """Get server statistics"""
        return dict(self.load_balancer.server_stats)
    
    def clear_cache(self):
        """Clear data cache"""
        if self.cache:
            self.cache.clear()
    
    def close(self):
        """Close client connections"""
        for channel in self.channels.values():
            channel.close()
        self.executor.shutdown(wait=True)
        logger.info("Client closed")