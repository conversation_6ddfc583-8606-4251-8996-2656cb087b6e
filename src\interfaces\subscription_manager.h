#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <atomic>
#include <functional>
#include <spdlog/spdlog.h>
#include "websocket_types.h"
#include "data_types.h"

namespace financial_data {
namespace interfaces {

/**
 * @brief 订阅过滤器
 */
class SubscriptionFilter {
public:
    virtual ~SubscriptionFilter() = default;
    virtual bool ShouldSend(const MarketDataWrapper& data, const ClientConnection& client) const = 0;
    virtual std::string GetFilterType() const = 0;
};

/**
 * @brief 价格范围过滤器
 */
class PriceRangeFilter : public SubscriptionFilter {
private:
    double min_price_;
    double max_price_;
    
public:
    PriceRangeFilter(double min_price, double max_price) 
        : min_price_(min_price), max_price_(max_price) {}
    
    bool ShouldSend(const MarketDataWrapper& data, const ClientConnection& client) const override {
        if (data.type == MarketDataWrapper::DataType::TICK) {
            double price = data.tick_data.last_price;
            return price >= min_price_ && price <= max_price_;
        }
        return true;  // Level2数据不过滤
    }
    
    std::string GetFilterType() const override {
        return "price_range";
    }
};

/**
 * @brief 成交量过滤器
 */
class VolumeFilter : public SubscriptionFilter {
private:
    uint64_t min_volume_;
    
public:
    explicit VolumeFilter(uint64_t min_volume) : min_volume_(min_volume) {}
    
    bool ShouldSend(const MarketDataWrapper& data, const ClientConnection& client) const override {
        if (data.type == MarketDataWrapper::DataType::TICK) {
            return data.tick_data.volume >= min_volume_;
        }
        return true;
    }
    
    std::string GetFilterType() const override {
        return "volume";
    }
};

/**
 * @brief 订阅管理器
 * 
 * 负责管理客户端订阅、数据过滤和路由
 */
class SubscriptionManager {
private:
    // 客户端连接映射
    std::unordered_map<std::string, std::shared_ptr<ClientConnection>> clients_;
    mutable std::shared_mutex clients_mutex_;
    
    // 订阅映射：symbol -> client_ids
    std::unordered_map<std::string, std::unordered_set<std::string>> symbol_subscriptions_;
    mutable std::shared_mutex symbol_subscriptions_mutex_;
    
    // 交易所订阅映射：exchange -> client_ids
    std::unordered_map<std::string, std::unordered_set<std::string>> exchange_subscriptions_;
    mutable std::shared_mutex exchange_subscriptions_mutex_;
    
    // 数据类型订阅映射：data_type -> client_ids
    std::unordered_map<std::string, std::unordered_set<std::string>> datatype_subscriptions_;
    mutable std::shared_mutex datatype_subscriptions_mutex_;
    
    // 客户端过滤器
    std::unordered_map<std::string, std::vector<std::unique_ptr<SubscriptionFilter>>> client_filters_;
    mutable std::shared_mutex filters_mutex_;
    
    // 统计信息
    std::atomic<uint64_t> total_subscriptions_{0};
    std::atomic<uint64_t> active_subscriptions_{0};
    std::atomic<uint64_t> filtered_messages_{0};
    std::atomic<uint64_t> routed_messages_{0};
    
    std::shared_ptr<spdlog::logger> logger_;

public:
    SubscriptionManager();
    ~SubscriptionManager() = default;

    /**
     * @brief 添加客户端连接
     */
    bool AddClient(const std::string& client_id, std::shared_ptr<ClientConnection> client);

    /**
     * @brief 移除客户端连接
     */
    bool RemoveClient(const std::string& client_id);

    /**
     * @brief 获取客户端连接
     */
    std::shared_ptr<ClientConnection> GetClient(const std::string& client_id) const;

    /**
     * @brief 获取所有活跃客户端
     */
    std::vector<std::string> GetActiveClients() const;

    /**
     * @brief 处理订阅请求
     */
    SubscriptionResponse ProcessSubscription(const SubscriptionRequest& request);

    /**
     * @brief 处理取消订阅请求
     */
    SubscriptionResponse ProcessUnsubscription(const SubscriptionRequest& request);

    /**
     * @brief 获取订阅某个合约的客户端列表
     */
    std::vector<std::string> GetSubscribedClients(const std::string& symbol, 
                                                 const std::string& exchange = "") const;

    /**
     * @brief 获取订阅某种数据类型的客户端列表
     */
    std::vector<std::string> GetDataTypeSubscribers(const std::string& data_type) const;

    /**
     * @brief 检查客户端是否订阅了某个合约
     */
    bool IsClientSubscribed(const std::string& client_id, 
                           const std::string& symbol, 
                           const std::string& exchange = "") const;

    /**
     * @brief 路由市场数据到订阅的客户端
     */
    std::vector<std::string> RouteMarketData(const MarketDataWrapper& data) const;

    /**
     * @brief 应用过滤器检查是否应该发送数据
     */
    bool ShouldSendToClient(const std::string& client_id, const MarketDataWrapper& data) const;

    /**
     * @brief 添加客户端过滤器
     */
    bool AddClientFilter(const std::string& client_id, std::unique_ptr<SubscriptionFilter> filter);

    /**
     * @brief 移除客户端过滤器
     */
    bool RemoveClientFilter(const std::string& client_id, const std::string& filter_type);

    /**
     * @brief 清除客户端所有过滤器
     */
    void ClearClientFilters(const std::string& client_id);

    /**
     * @brief 获取客户端订阅信息
     */
    struct ClientSubscriptionInfo {
        std::unordered_set<std::string> symbols;
        std::unordered_set<std::string> exchanges;
        std::unordered_set<std::string> data_types;
        std::vector<std::string> filter_types;
        uint64_t subscription_count;
    };
    
    ClientSubscriptionInfo GetClientSubscriptionInfo(const std::string& client_id) const;

    /**
     * @brief 获取订阅统计信息
     */
    struct SubscriptionStatistics {
        uint64_t total_clients;
        uint64_t active_clients;
        uint64_t total_subscriptions;
        uint64_t active_subscriptions;
        uint64_t filtered_messages;
        uint64_t routed_messages;
        std::unordered_map<std::string, uint64_t> symbol_subscription_counts;
        std::unordered_map<std::string, uint64_t> exchange_subscription_counts;
        std::unordered_map<std::string, uint64_t> datatype_subscription_counts;
    };
    
    SubscriptionStatistics GetStatistics() const;

    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

    /**
     * @brief 清理过期的客户端连接
     */
    size_t CleanupExpiredClients(std::chrono::milliseconds timeout);

    /**
     * @brief 获取订阅摘要
     */
    std::string GetSubscriptionSummary() const;

    /**
     * @brief 验证订阅请求
     */
    bool ValidateSubscriptionRequest(const SubscriptionRequest& request, std::string& error_message) const;

    /**
     * @brief 创建过滤器
     */
    std::unique_ptr<SubscriptionFilter> CreateFilter(const std::string& filter_type, 
                                                    const std::unordered_map<std::string, std::string>& params) const;

private:
    /**
     * @brief 添加符号订阅
     */
    void AddSymbolSubscription(const std::string& symbol, const std::string& client_id);

    /**
     * @brief 移除符号订阅
     */
    void RemoveSymbolSubscription(const std::string& symbol, const std::string& client_id);

    /**
     * @brief 添加交易所订阅
     */
    void AddExchangeSubscription(const std::string& exchange, const std::string& client_id);

    /**
     * @brief 移除交易所订阅
     */
    void RemoveExchangeSubscription(const std::string& exchange, const std::string& client_id);

    /**
     * @brief 添加数据类型订阅
     */
    void AddDataTypeSubscription(const std::string& data_type, const std::string& client_id);

    /**
     * @brief 移除数据类型订阅
     */
    void RemoveDataTypeSubscription(const std::string& data_type, const std::string& client_id);

    /**
     * @brief 生成订阅ID
     */
    std::string GenerateSubscriptionId(const std::string& client_id, const SubscriptionRequest& request) const;

    /**
     * @brief 记录订阅操作
     */
    void LogSubscriptionOperation(const std::string& operation, 
                                 const std::string& client_id, 
                                 const SubscriptionRequest& request) const;
};

} // namespace interfaces
} // namespace financial_data