#include "tls_manager.h"
#include <iostream>
#include <fstream>
#include <sstream>

namespace financial_data {
namespace security {

TLSManager::TLSManager(const TLSConfig& config)
    : config_(config), server_ctx_(nullptr), client_ctx_(nullptr), initialized_(false) {
}

TLSManager::~TLSManager() {
    Cleanup();
}

bool TLSManager::Initialize() {
    if (initialized_) {
        return true;
    }
    
    // 初始化OpenSSL
    SSL_load_error_strings();
    SSL_library_init();
    OpenSSL_add_all_algorithms();
    
    // 创建服务器上下文
    server_ctx_ = CreateServerContext();
    if (!server_ctx_) {
        std::cerr << "Failed to create server SSL context" << std::endl;
        return false;
    }
    
    // 创建客户端上下文
    client_ctx_ = CreateClientContext();
    if (!client_ctx_) {
        std::cerr << "Failed to create client SSL context" << std::endl;
        return false;
    }
    
    initialized_ = true;
    return true;
}

SSL_CTX* TLSManager::CreateServerContext() {
    const SSL_METHOD* method = TLS_server_method();
    SSL_CTX* ctx = SSL_CTX_new(method);
    
    if (!ctx) {
        LogSSLError("SSL_CTX_new");
        return nullptr;
    }
    
    // 设置最小TLS版本为1.3
    SSL_CTX_set_min_proto_version(ctx, TLS1_3_VERSION);
    SSL_CTX_set_max_proto_version(ctx, TLS1_3_VERSION);
    
    // 配置密码套件
    if (!ConfigureCipherSuites(ctx)) {
        SSL_CTX_free(ctx);
        return nullptr;
    }
    
    // 加载证书和私钥
    if (!LoadCertificates(ctx)) {
        SSL_CTX_free(ctx);
        return nullptr;
    }
    
    // 设置客户端证书验证
    if (config_.require_client_cert) {
        SSL_CTX_set_verify(ctx, SSL_VERIFY_PEER | SSL_VERIFY_FAIL_IF_NO_PEER_CERT, VerifyCallback);
    }
    
    return ctx;
}

SSL_CTX* TLSManager::CreateClientContext() {
    const SSL_METHOD* method = TLS_client_method();
    SSL_CTX* ctx = SSL_CTX_new(method);
    
    if (!ctx) {
        LogSSLError("SSL_CTX_new");
        return nullptr;
    }
    
    // 设置最小TLS版本为1.3
    SSL_CTX_set_min_proto_version(ctx, TLS1_3_VERSION);
    SSL_CTX_set_max_proto_version(ctx, TLS1_3_VERSION);
    
    // 加载CA证书
    if (!config_.ca_file.empty()) {
        if (SSL_CTX_load_verify_locations(ctx, config_.ca_file.c_str(), nullptr) != 1) {
            LogSSLError("SSL_CTX_load_verify_locations");
            SSL_CTX_free(ctx);
            return nullptr;
        }
    }
    
    // 设置服务器证书验证
    SSL_CTX_set_verify(ctx, SSL_VERIFY_PEER, VerifyCallback);
    
    return ctx;
}

bool TLSManager::LoadCertificates(SSL_CTX* ctx) {
    // 加载证书文件
    if (SSL_CTX_use_certificate_file(ctx, config_.cert_file.c_str(), SSL_FILETYPE_PEM) <= 0) {
        LogSSLError("SSL_CTX_use_certificate_file");
        return false;
    }
    
    // 加载私钥文件
    if (SSL_CTX_use_PrivateKey_file(ctx, config_.key_file.c_str(), SSL_FILETYPE_PEM) <= 0) {
        LogSSLError("SSL_CTX_use_PrivateKey_file");
        return false;
    }
    
    // 验证私钥和证书匹配
    if (!SSL_CTX_check_private_key(ctx)) {
        std::cerr << "Private key does not match certificate" << std::endl;
        return false;
    }
    
    return true;
}

bool TLSManager::ConfigureCipherSuites(SSL_CTX* ctx) {
    // TLS 1.3推荐的密码套件
    const char* tls13_ciphers = "TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256";
    
    if (SSL_CTX_set_ciphersuites(ctx, tls13_ciphers) != 1) {
        LogSSLError("SSL_CTX_set_ciphersuites");
        return false;
    }
    
    // 设置安全选项
    SSL_CTX_set_options(ctx, SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_TLSv1 | SSL_OP_NO_TLSv1_1 | SSL_OP_NO_TLSv1_2);
    SSL_CTX_set_options(ctx, SSL_OP_CIPHER_SERVER_PREFERENCE);
    SSL_CTX_set_options(ctx, SSL_OP_NO_COMPRESSION);
    
    return true;
}

bool TLSManager::VerifyCertificate(SSL* ssl) {
    X509* cert = SSL_get_peer_certificate(ssl);
    if (!cert) {
        std::cerr << "No peer certificate" << std::endl;
        return false;
    }
    
    long verify_result = SSL_get_verify_result(ssl);
    X509_free(cert);
    
    if (verify_result != X509_V_OK) {
        std::cerr << "Certificate verification failed: " << X509_verify_cert_error_string(verify_result) << std::endl;
        return false;
    }
    
    return true;
}

std::string TLSManager::GetConnectionInfo(SSL* ssl) {
    std::ostringstream info;
    
    info << "TLS Version: " << SSL_get_version(ssl) << std::endl;
    info << "Cipher: " << SSL_get_cipher(ssl) << std::endl;
    
    X509* cert = SSL_get_peer_certificate(ssl);
    if (cert) {
        char* subject = X509_NAME_oneline(X509_get_subject_name(cert), nullptr, 0);
        char* issuer = X509_NAME_oneline(X509_get_issuer_name(cert), nullptr, 0);
        
        info << "Subject: " << (subject ? subject : "Unknown") << std::endl;
        info << "Issuer: " << (issuer ? issuer : "Unknown") << std::endl;
        
        OPENSSL_free(subject);
        OPENSSL_free(issuer);
        X509_free(cert);
    }
    
    return info.str();
}

bool TLSManager::IsTLS13(SSL* ssl) {
    return SSL_version(ssl) == TLS1_3_VERSION;
}

int TLSManager::VerifyCallback(int preverify_ok, X509_STORE_CTX* ctx) {
    if (!preverify_ok) {
        int error = X509_STORE_CTX_get_error(ctx);
        std::cerr << "Certificate verification error: " << X509_verify_cert_error_string(error) << std::endl;
    }
    return preverify_ok;
}

void TLSManager::LogSSLError(const std::string& operation) {
    unsigned long error = ERR_get_error();
    char error_string[256];
    ERR_error_string_n(error, error_string, sizeof(error_string));
    std::cerr << "SSL Error in " << operation << ": " << error_string << std::endl;
}

void TLSManager::Cleanup() {
    if (server_ctx_) {
        SSL_CTX_free(server_ctx_);
        server_ctx_ = nullptr;
    }
    
    if (client_ctx_) {
        SSL_CTX_free(client_ctx_);
        client_ctx_ = nullptr;
    }
    
    EVP_cleanup();
    ERR_free_strings();
    initialized_ = false;
}

} // namespace security
} // namespace financial_data