#include "storage_strategy_validator.h"
#include <regex>
#include <algorithm>
#include <sstream>

namespace config {

// 静态成员定义
const std::unordered_set<std::string> StorageStrategyValidator::SUPPORTED_DATA_TYPES = {
    "tick", "kline", "level2", "fundamental", "news", "announcement"
};

const std::unordered_set<std::string> StorageStrategyValidator::SUPPORTED_STORAGE_LAYERS = {
    "hot", "warm", "cold"
};

const std::unordered_set<std::string> StorageStrategyValidator::SUPPORTED_SELECTION_STRATEGIES = {
    "time_based", "performance_based", "load_balanced", "failover_only"
};

StorageStrategyValidator::StorageStrategyValidator() = default;

ValidationResult StorageStrategyValidator::Validate(const nlohmann::json& config) const {
    ValidationResult result;
    
    // 检查是否存在storage配置
    if (!config.contains("storage")) {
        result.AddError("Missing 'storage' configuration section");
        return result;
    }
    
    const auto& storage_config = config["storage"];
    
    // 检查是否存在strategy配置
    if (!storage_config.contains("strategy")) {
        result.AddWarning("Missing 'strategy' configuration in storage section, using defaults");
        return result;
    }
    
    const auto& strategy_config = storage_config["strategy"];
    
    // 验证基本策略配置
    ValidateBasicStrategy(strategy_config, result);
    
    // 验证阈值配置
    if (strategy_config.contains("thresholds")) {
        ValidateThresholds(strategy_config["thresholds"], result);
    }
    
    // 验证数据类型配置
    if (strategy_config.contains("data_type_configs")) {
        ValidateDataTypeConfigs(strategy_config["data_type_configs"], result);
    }
    
    // 验证迁移策略配置
    if (strategy_config.contains("migration_policies")) {
        ValidateMigrationPolicies(strategy_config["migration_policies"], result);
    }
    
    // 验证时间配置的一致性
    ValidateTimeConsistency(strategy_config, result);
    
    return result;
}

std::string StorageStrategyValidator::GetValidatorName() const {
    return "StorageStrategyValidator";
}

void StorageStrategyValidator::ValidateBasicStrategy(const nlohmann::json& strategy_config, 
                                                   ValidationResult& result) const {
    // 验证选择策略
    if (strategy_config.contains("selection_strategy")) {
        if (!strategy_config["selection_strategy"].is_string()) {
            result.AddError("selection_strategy must be a string");
        } else {
            std::string strategy = strategy_config["selection_strategy"];
            if (!ValidateSelectionStrategy(strategy)) {
                result.AddError("Invalid selection_strategy: " + strategy + 
                               ". Supported strategies: time_based, performance_based, load_balanced, failover_only");
            }
        }
    }
    
    // 验证布尔配置
    std::vector<std::string> bool_configs = {
        "enable_automatic_failover", "enable_load_balancing"
    };
    
    for (const auto& config_name : bool_configs) {
        if (strategy_config.contains(config_name)) {
            if (!strategy_config[config_name].is_boolean()) {
                result.AddError(config_name + " must be a boolean value");
            }
        }
    }
    
    // 验证数值配置
    std::vector<std::pair<std::string, std::pair<double, double>>> numeric_configs = {
        {"health_check_interval_seconds", {1.0, 3600.0}},
        {"health_check_timeout_seconds", {1.0, 300.0}},
        {"max_consecutive_failures", {1.0, 100.0}},
        {"failover_cooldown_seconds", {1.0, 7200.0}},
        {"max_failover_attempts", {1.0, 10.0}},
        {"load_balance_threshold", {0.1, 1.0}}
    };
    
    for (const auto& [config_name, range] : numeric_configs) {
        if (strategy_config.contains(config_name)) {
            if (!strategy_config[config_name].is_number()) {
                result.AddError(config_name + " must be a numeric value");
            } else {
                double value = strategy_config[config_name];
                if (!ValidateNumericRange(value, range.first, range.second)) {
                    result.AddError(config_name + " must be between " + 
                                   std::to_string(range.first) + " and " + 
                                   std::to_string(range.second));
                }
            }
        }
    }
}

void StorageStrategyValidator::ValidateThresholds(const nlohmann::json& thresholds_config, 
                                                ValidationResult& result) const {
    // 验证存储天数阈值
    std::vector<std::pair<std::string, std::pair<double, double>>> day_configs = {
        {"hot_storage_days", {1.0, 365.0}},
        {"warm_storage_days", {1.0, 3650.0}}
    };
    
    for (const auto& [config_name, range] : day_configs) {
        if (thresholds_config.contains(config_name)) {
            if (!thresholds_config[config_name].is_number()) {
                result.AddError("thresholds." + config_name + " must be a numeric value");
            } else {
                double value = thresholds_config[config_name];
                if (!ValidateNumericRange(value, range.first, range.second)) {
                    result.AddError("thresholds." + config_name + " must be between " + 
                                   std::to_string(range.first) + " and " + 
                                   std::to_string(range.second) + " days");
                }
            }
        }
    }
    
    // 验证性能阈值
    std::vector<std::pair<std::string, std::pair<double, double>>> perf_configs = {
        {"max_response_time_ms", {1.0, 60000.0}},
        {"min_success_rate", {0.0, 1.0}},
        {"health_threshold_success_rate", {0.0, 1.0}},
        {"degraded_threshold_success_rate", {0.0, 1.0}}
    };
    
    for (const auto& [config_name, range] : perf_configs) {
        if (thresholds_config.contains(config_name)) {
            if (!thresholds_config[config_name].is_number()) {
                result.AddError("thresholds." + config_name + " must be a numeric value");
            } else {
                double value = thresholds_config[config_name];
                if (!ValidateNumericRange(value, range.first, range.second)) {
                    result.AddError("thresholds." + config_name + " must be between " + 
                                   std::to_string(range.first) + " and " + 
                                   std::to_string(range.second));
                }
            }
        }
    }
    
    // 验证阈值逻辑一致性
    if (thresholds_config.contains("hot_storage_days") && 
        thresholds_config.contains("warm_storage_days")) {
        double hot_days = thresholds_config["hot_storage_days"];
        double warm_days = thresholds_config["warm_storage_days"];
        
        if (hot_days >= warm_days) {
            result.AddError("hot_storage_days must be less than warm_storage_days");
        }
    }
    
    if (thresholds_config.contains("health_threshold_success_rate") && 
        thresholds_config.contains("degraded_threshold_success_rate")) {
        double health_rate = thresholds_config["health_threshold_success_rate"];
        double degraded_rate = thresholds_config["degraded_threshold_success_rate"];
        
        if (health_rate <= degraded_rate) {
            result.AddError("health_threshold_success_rate must be greater than degraded_threshold_success_rate");
        }
    }
}

void StorageStrategyValidator::ValidateDataTypeConfigs(const nlohmann::json& data_type_configs, 
                                                     ValidationResult& result) const {
    if (!data_type_configs.is_object()) {
        result.AddError("data_type_configs must be an object");
        return;
    }
    
    for (const auto& [data_type, config] : data_type_configs.items()) {
        // 验证数据类型是否支持
        if (SUPPORTED_DATA_TYPES.find(data_type) == SUPPORTED_DATA_TYPES.end()) {
            result.AddWarning("Unknown data type: " + data_type + 
                             ". Consider adding it to supported data types if needed");
        }
        
        // 验证单个数据类型配置
        ValidateDataTypeConfig(data_type, config, result);
    }
}

void StorageStrategyValidator::ValidateDataTypeConfig(const std::string& data_type,
                                                    const nlohmann::json& config,
                                                    ValidationResult& result) const {
    std::string prefix = "data_type_configs." + data_type + ".";
    
    // 验证必需字段
    std::vector<std::string> required_fields = {
        "hot_storage_days", "warm_storage_days", "priority_storage"
    };
    
    for (const auto& field : required_fields) {
        if (!config.contains(field)) {
            result.AddError(prefix + field + " is required");
        }
    }
    
    // 验证存储天数
    std::vector<std::pair<std::string, std::pair<double, double>>> day_configs = {
        {"hot_storage_days", {1.0, 365.0}},
        {"warm_storage_days", {1.0, 3650.0}}
    };
    
    for (const auto& [field_name, range] : day_configs) {
        if (config.contains(field_name)) {
            if (!config[field_name].is_number()) {
                result.AddError(prefix + field_name + " must be a numeric value");
            } else {
                double value = config[field_name];
                if (!ValidateNumericRange(value, range.first, range.second)) {
                    result.AddError(prefix + field_name + " must be between " + 
                                   std::to_string(range.first) + " and " + 
                                   std::to_string(range.second) + " days");
                }
            }
        }
    }
    
    // 验证优先存储层
    if (config.contains("priority_storage")) {
        if (!config["priority_storage"].is_string()) {
            result.AddError(prefix + "priority_storage must be a string");
        } else {
            std::string priority = config["priority_storage"];
            if (!ValidateStorageLayer(priority)) {
                result.AddError(prefix + "priority_storage must be one of: hot, warm, cold");
            }
        }
    }
    
    // 验证可选字段
    if (config.contains("compression_enabled") && !config["compression_enabled"].is_boolean()) {
        result.AddError(prefix + "compression_enabled must be a boolean value");
    }
    
    if (config.contains("batch_size")) {
        if (!config["batch_size"].is_number_integer() || config["batch_size"] <= 0) {
            result.AddError(prefix + "batch_size must be a positive integer");
        }
    }
    
    if (config.contains("max_response_time_ms")) {
        if (!config["max_response_time_ms"].is_number()) {
            result.AddError(prefix + "max_response_time_ms must be a numeric value");
        } else {
            double value = config["max_response_time_ms"];
            if (!ValidateNumericRange(value, 1.0, 60000.0)) {
                result.AddError(prefix + "max_response_time_ms must be between 1 and 60000 ms");
            }
        }
    }
    
    // 验证时间配置一致性
    if (config.contains("hot_storage_days") && config.contains("warm_storage_days")) {
        double hot_days = config["hot_storage_days"];
        double warm_days = config["warm_storage_days"];
        
        if (hot_days >= warm_days) {
            result.AddError(prefix + "hot_storage_days must be less than warm_storage_days");
        }
    }
}

void StorageStrategyValidator::ValidateMigrationPolicies(const nlohmann::json& migration_policies, 
                                                       ValidationResult& result) const {
    if (!migration_policies.is_object()) {
        result.AddError("migration_policies must be an object");
        return;
    }
    
    for (const auto& [data_type, policy] : migration_policies.items()) {
        // 验证数据类型是否支持
        if (SUPPORTED_DATA_TYPES.find(data_type) == SUPPORTED_DATA_TYPES.end()) {
            result.AddWarning("Unknown data type in migration policy: " + data_type);
        }
        
        // 验证单个迁移策略配置
        ValidateMigrationPolicy(data_type, policy, result);
    }
}

void StorageStrategyValidator::ValidateMigrationPolicy(const std::string& data_type,
                                                     const nlohmann::json& policy,
                                                     ValidationResult& result) const {
    std::string prefix = "migration_policies." + data_type + ".";
    
    // 验证必需字段
    std::vector<std::string> required_fields = {
        "hot_to_warm_hours", "warm_to_cold_days", "auto_migration"
    };
    
    for (const auto& field : required_fields) {
        if (!policy.contains(field)) {
            result.AddError(prefix + field + " is required");
        }
    }
    
    // 验证时间配置
    if (policy.contains("hot_to_warm_hours")) {
        if (!policy["hot_to_warm_hours"].is_number()) {
            result.AddError(prefix + "hot_to_warm_hours must be a numeric value");
        } else {
            double value = policy["hot_to_warm_hours"];
            if (!ValidateNumericRange(value, 1.0, 8760.0)) { // 1小时到1年
                result.AddError(prefix + "hot_to_warm_hours must be between 1 and 8760 hours");
            }
        }
    }
    
    if (policy.contains("warm_to_cold_days")) {
        if (!policy["warm_to_cold_days"].is_number()) {
            result.AddError(prefix + "warm_to_cold_days must be a numeric value");
        } else {
            double value = policy["warm_to_cold_days"];
            if (!ValidateNumericRange(value, 1.0, 3650.0)) { // 1天到10年
                result.AddError(prefix + "warm_to_cold_days must be between 1 and 3650 days");
            }
        }
    }
    
    // 验证布尔配置
    if (policy.contains("auto_migration") && !policy["auto_migration"].is_boolean()) {
        result.AddError(prefix + "auto_migration must be a boolean value");
    }
    
    // 验证批量大小
    if (policy.contains("migration_batch_size")) {
        if (!policy["migration_batch_size"].is_number_integer() || 
            policy["migration_batch_size"] <= 0) {
            result.AddError(prefix + "migration_batch_size must be a positive integer");
        }
    }
    
    // 验证cron表达式
    if (policy.contains("migration_schedule")) {
        if (!policy["migration_schedule"].is_string()) {
            result.AddError(prefix + "migration_schedule must be a string");
        } else {
            std::string cron_expr = policy["migration_schedule"];
            if (!ValidateCronExpression(cron_expr)) {
                result.AddError(prefix + "migration_schedule contains invalid cron expression: " + cron_expr);
            }
        }
    }
    
    // 验证时间配置逻辑一致性
    if (policy.contains("hot_to_warm_hours") && policy.contains("warm_to_cold_days")) {
        double hot_to_warm_hours = policy["hot_to_warm_hours"];
        double warm_to_cold_days = policy["warm_to_cold_days"];
        
        // 转换为相同单位进行比较（都转换为小时）
        double warm_to_cold_hours = warm_to_cold_days * 24.0;
        
        if (hot_to_warm_hours >= warm_to_cold_hours) {
            result.AddError(prefix + "hot_to_warm_hours must be less than warm_to_cold_days (converted to hours)");
        }
    }
}

bool StorageStrategyValidator::ValidateCronExpression(const std::string& cron_expr) const {
    // 分割cron表达式
    std::istringstream iss(cron_expr);
    std::vector<std::string> parts;
    std::string part;
    
    while (iss >> part) {
        parts.push_back(part);
    }
    
    if (parts.size() != 5) {
        return false;
    }
    
    // 验证每个部分: 分, 时, 日, 月, 周
    std::vector<std::pair<int, int>> ranges = {{0, 59}, {0, 23}, {1, 31}, {1, 12}, {0, 6}};
    
    for (size_t i = 0; i < parts.size(); ++i) {
        if (!ValidateCronField(parts[i], ranges[i].first, ranges[i].second)) {
            return false;
        }
    }
    
    return true;
}

bool StorageStrategyValidator::ValidateCronField(const std::string& field, int min_val, int max_val) const {
    if (field == "*") {
        return true;
    }
    
    // 处理步长 (*/n)
    if (field.substr(0, 2) == "*/") {
        try {
            int step = std::stoi(field.substr(2));
            return step > 0 && step <= max_val;
        } catch (const std::exception&) {
            return false;
        }
    }
    
    // 处理范围和列表
    std::istringstream iss(field);
    std::string part;
    
    while (std::getline(iss, part, ',')) {
        size_t dash_pos = part.find('-');
        if (dash_pos != std::string::npos) {
            // 范围 (n-m)
            try {
                int start = std::stoi(part.substr(0, dash_pos));
                int end = std::stoi(part.substr(dash_pos + 1));
                if (!(min_val <= start && start <= max_val && 
                      min_val <= end && end <= max_val && start <= end)) {
                    return false;
                }
            } catch (const std::exception&) {
                return false;
            }
        } else {
            // 单个数值
            try {
                int value = std::stoi(part);
                if (!(min_val <= value && value <= max_val)) {
                    return false;
                }
            } catch (const std::exception&) {
                return false;
            }
        }
    }
    
    return true;
}

bool StorageStrategyValidator::ValidateStorageLayer(const std::string& layer) const {
    return SUPPORTED_STORAGE_LAYERS.find(layer) != SUPPORTED_STORAGE_LAYERS.end();
}

bool StorageStrategyValidator::ValidateSelectionStrategy(const std::string& strategy) const {
    return SUPPORTED_SELECTION_STRATEGIES.find(strategy) != SUPPORTED_SELECTION_STRATEGIES.end();
}

bool StorageStrategyValidator::ValidateNumericRange(double value, double min_val, double max_val) const {
    return value >= min_val && value <= max_val;
}

void StorageStrategyValidator::ValidateTimeConsistency(const nlohmann::json& config, 
                                                     ValidationResult& result) const {
    // 验证全局阈值与数据类型配置的一致性
    if (!config.contains("thresholds") || !config.contains("data_type_configs")) {
        return;
    }
    
    const auto& thresholds = config["thresholds"];
    const auto& data_type_configs = config["data_type_configs"];
    
    if (thresholds.contains("hot_storage_days") && thresholds.contains("warm_storage_days")) {
        double global_hot_days = thresholds["hot_storage_days"];
        double global_warm_days = thresholds["warm_storage_days"];
        
        // 检查每个数据类型的配置是否与全局配置冲突
        for (const auto& [data_type, type_config] : data_type_configs.items()) {
            if (type_config.contains("hot_storage_days") && 
                type_config.contains("warm_storage_days")) {
                double type_hot_days = type_config["hot_storage_days"];
                double type_warm_days = type_config["warm_storage_days"];
                
                // 警告：数据类型配置与全局配置差异较大
                if (std::abs(type_hot_days - global_hot_days) > global_hot_days * 0.5) {
                    result.AddWarning("Data type '" + data_type + "' hot_storage_days (" + 
                                     std::to_string(type_hot_days) + 
                                     ") differs significantly from global setting (" + 
                                     std::to_string(global_hot_days) + ")");
                }
                
                if (std::abs(type_warm_days - global_warm_days) > global_warm_days * 0.5) {
                    result.AddWarning("Data type '" + data_type + "' warm_storage_days (" + 
                                     std::to_string(type_warm_days) + 
                                     ") differs significantly from global setting (" + 
                                     std::to_string(global_warm_days) + ")");
                }
            }
        }
    }
    
    // 验证迁移策略与存储配置的一致性
    if (config.contains("migration_policies")) {
        const auto& migration_policies = config["migration_policies"];
        
        for (const auto& [data_type, policy] : migration_policies.items()) {
            if (data_type_configs.contains(data_type)) {
                const auto& type_config = data_type_configs[data_type];
                
                if (policy.contains("hot_to_warm_hours") && 
                    type_config.contains("hot_storage_days")) {
                    double migration_hours = policy["hot_to_warm_hours"];
                    double storage_days = type_config["hot_storage_days"];
                    double storage_hours = storage_days * 24.0;
                    
                    // 迁移时间应该与存储时间相匹配或略小
                    if (migration_hours > storage_hours * 1.2) {
                        result.AddWarning("Migration policy for '" + data_type + 
                                         "' hot_to_warm_hours (" + std::to_string(migration_hours) + 
                                         ") is much larger than hot_storage_days (" + 
                                         std::to_string(storage_days) + " days)");
                    }
                }
            }
        }
    }
}

} // namespace config