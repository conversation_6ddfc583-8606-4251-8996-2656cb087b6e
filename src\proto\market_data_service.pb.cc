// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: market_data_service.proto

#include "market_data_service.pb.h"

#include <algorithm>
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/extension_set.h"
#include "google/protobuf/wire_format_lite.h"
#include "google/protobuf/descriptor.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/reflection_ops.h"
#include "google/protobuf/wire_format.h"
#include "google/protobuf/generated_message_tctable_impl.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"
PROTOBUF_PRAGMA_INIT_SEG
namespace _pb = ::google::protobuf;
namespace _pbi = ::google::protobuf::internal;
namespace _fl = ::google::protobuf::internal::field_layout;
namespace financial_data {

inline constexpr TickDataRequest::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : symbols_{},
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        include_level2_{false},
        buffer_size_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR TickDataRequest::TickDataRequest(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct TickDataRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR TickDataRequestDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~TickDataRequestDefaultTypeInternal() {}
  union {
    TickDataRequest _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 TickDataRequestDefaultTypeInternal _TickDataRequest_default_instance_;

inline constexpr ResponseMetadata::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : server_id_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        server_timestamp_{::int64_t{0}},
        processing_latency_us_{0},
        sequence_number_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ResponseMetadata::ResponseMetadata(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct ResponseMetadataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ResponseMetadataDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ResponseMetadataDefaultTypeInternal() {}
  union {
    ResponseMetadata _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ResponseMetadataDefaultTypeInternal _ResponseMetadata_default_instance_;

inline constexpr PriceLevel::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : price_{0},
        volume_{0},
        order_count_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR PriceLevel::PriceLevel(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct PriceLevelDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PriceLevelDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~PriceLevelDefaultTypeInternal() {}
  union {
    PriceLevel _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PriceLevelDefaultTypeInternal _PriceLevel_default_instance_;

inline constexpr Level2DataRequest::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : symbols_{},
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        depth_{0},
        buffer_size_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR Level2DataRequest::Level2DataRequest(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct Level2DataRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Level2DataRequestDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~Level2DataRequestDefaultTypeInternal() {}
  union {
    Level2DataRequest _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Level2DataRequestDefaultTypeInternal _Level2DataRequest_default_instance_;

inline constexpr KlineDataRequest::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : symbols_{},
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        period_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        start_timestamp_{::int64_t{0}},
        end_timestamp_{::int64_t{0}},
        buffer_size_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR KlineDataRequest::KlineDataRequest(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct KlineDataRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR KlineDataRequestDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~KlineDataRequestDefaultTypeInternal() {}
  union {
    KlineDataRequest _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 KlineDataRequestDefaultTypeInternal _KlineDataRequest_default_instance_;

inline constexpr KlineData::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : symbol_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        period_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        timestamp_{::int64_t{0}},
        open_{0},
        high_{0},
        low_{0},
        close_{0},
        volume_{::int64_t{0}},
        turnover_{0},
        open_interest_{::int64_t{0}},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR KlineData::KlineData(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct KlineDataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR KlineDataDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~KlineDataDefaultTypeInternal() {}
  union {
    KlineData _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 KlineDataDefaultTypeInternal _KlineData_default_instance_;

inline constexpr HistoricalTickDataRequest::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : symbol_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        cursor_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        start_timestamp_{::int64_t{0}},
        end_timestamp_{::int64_t{0}},
        limit_{0},
        buffer_size_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR HistoricalTickDataRequest::HistoricalTickDataRequest(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct HistoricalTickDataRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR HistoricalTickDataRequestDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~HistoricalTickDataRequestDefaultTypeInternal() {}
  union {
    HistoricalTickDataRequest _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 HistoricalTickDataRequestDefaultTypeInternal _HistoricalTickDataRequest_default_instance_;

inline constexpr HealthCheckResponse::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : message_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        status_{static_cast< ::financial_data::HealthCheckResponse_ServingStatus >(0)},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR HealthCheckResponse::HealthCheckResponse(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct HealthCheckResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR HealthCheckResponseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~HealthCheckResponseDefaultTypeInternal() {}
  union {
    HealthCheckResponse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 HealthCheckResponseDefaultTypeInternal _HealthCheckResponse_default_instance_;

inline constexpr HealthCheckRequest::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : service_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR HealthCheckRequest::HealthCheckRequest(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct HealthCheckRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR HealthCheckRequestDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~HealthCheckRequestDefaultTypeInternal() {}
  union {
    HealthCheckRequest _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 HealthCheckRequestDefaultTypeInternal _HealthCheckRequest_default_instance_;

inline constexpr TickData::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : bids_{},
        asks_{},
        symbol_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        trade_flag_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        timestamp_{::int64_t{0}},
        last_price_{0},
        volume_{::int64_t{0}},
        turnover_{0},
        open_interest_{::int64_t{0}},
        sequence_{0u},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR TickData::TickData(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct TickDataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR TickDataDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~TickDataDefaultTypeInternal() {}
  union {
    TickData _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 TickDataDefaultTypeInternal _TickData_default_instance_;

inline constexpr Level2Data::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : bids_{},
        asks_{},
        symbol_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        exchange_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        timestamp_{::int64_t{0}},
        sequence_{0u},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR Level2Data::Level2Data(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct Level2DataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Level2DataDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~Level2DataDefaultTypeInternal() {}
  union {
    Level2Data _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Level2DataDefaultTypeInternal _Level2Data_default_instance_;

inline constexpr KlineDataResponse::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        klines_{},
        metadata_{nullptr},
        has_more_{false} {}

template <typename>
PROTOBUF_CONSTEXPR KlineDataResponse::KlineDataResponse(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct KlineDataResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR KlineDataResponseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~KlineDataResponseDefaultTypeInternal() {}
  union {
    KlineDataResponse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 KlineDataResponseDefaultTypeInternal _KlineDataResponse_default_instance_;

inline constexpr TickDataResponse::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        ticks_{},
        next_cursor_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        metadata_{nullptr},
        has_more_{false} {}

template <typename>
PROTOBUF_CONSTEXPR TickDataResponse::TickDataResponse(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct TickDataResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR TickDataResponseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~TickDataResponseDefaultTypeInternal() {}
  union {
    TickDataResponse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 TickDataResponseDefaultTypeInternal _TickDataResponse_default_instance_;

inline constexpr Level2DataResponse::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        level2_data_{},
        metadata_{nullptr} {}

template <typename>
PROTOBUF_CONSTEXPR Level2DataResponse::Level2DataResponse(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct Level2DataResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Level2DataResponseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~Level2DataResponseDefaultTypeInternal() {}
  union {
    Level2DataResponse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Level2DataResponseDefaultTypeInternal _Level2DataResponse_default_instance_;
}  // namespace financial_data
static ::_pb::Metadata file_level_metadata_market_5fdata_5fservice_2eproto[14];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_market_5fdata_5fservice_2eproto[1];
static constexpr const ::_pb::ServiceDescriptor**
    file_level_service_descriptors_market_5fdata_5fservice_2eproto = nullptr;
const ::uint32_t TableStruct_market_5fdata_5fservice_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(
    protodesc_cold) = {
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataRequest, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataRequest, _impl_.symbols_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataRequest, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataRequest, _impl_.include_level2_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataRequest, _impl_.buffer_size_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::HistoricalTickDataRequest, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::HistoricalTickDataRequest, _impl_.symbol_),
    PROTOBUF_FIELD_OFFSET(::financial_data::HistoricalTickDataRequest, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::HistoricalTickDataRequest, _impl_.start_timestamp_),
    PROTOBUF_FIELD_OFFSET(::financial_data::HistoricalTickDataRequest, _impl_.end_timestamp_),
    PROTOBUF_FIELD_OFFSET(::financial_data::HistoricalTickDataRequest, _impl_.limit_),
    PROTOBUF_FIELD_OFFSET(::financial_data::HistoricalTickDataRequest, _impl_.cursor_),
    PROTOBUF_FIELD_OFFSET(::financial_data::HistoricalTickDataRequest, _impl_.buffer_size_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataRequest, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataRequest, _impl_.symbols_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataRequest, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataRequest, _impl_.period_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataRequest, _impl_.start_timestamp_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataRequest, _impl_.end_timestamp_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataRequest, _impl_.buffer_size_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataRequest, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataRequest, _impl_.symbols_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataRequest, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataRequest, _impl_.depth_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataRequest, _impl_.buffer_size_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::HealthCheckRequest, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::HealthCheckRequest, _impl_.service_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataResponse, _impl_._has_bits_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataResponse, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataResponse, _impl_.ticks_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataResponse, _impl_.has_more_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataResponse, _impl_.next_cursor_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickDataResponse, _impl_.metadata_),
    ~0u,
    ~0u,
    ~0u,
    0,
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataResponse, _impl_._has_bits_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataResponse, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataResponse, _impl_.klines_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataResponse, _impl_.has_more_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineDataResponse, _impl_.metadata_),
    ~0u,
    ~0u,
    0,
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataResponse, _impl_._has_bits_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataResponse, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataResponse, _impl_.level2_data_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2DataResponse, _impl_.metadata_),
    ~0u,
    0,
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::HealthCheckResponse, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::HealthCheckResponse, _impl_.status_),
    PROTOBUF_FIELD_OFFSET(::financial_data::HealthCheckResponse, _impl_.message_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.timestamp_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.symbol_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.last_price_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.volume_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.turnover_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.open_interest_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.bids_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.asks_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.sequence_),
    PROTOBUF_FIELD_OFFSET(::financial_data::TickData, _impl_.trade_flag_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.symbol_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.period_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.timestamp_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.open_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.high_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.low_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.close_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.volume_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.turnover_),
    PROTOBUF_FIELD_OFFSET(::financial_data::KlineData, _impl_.open_interest_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2Data, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2Data, _impl_.timestamp_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2Data, _impl_.symbol_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2Data, _impl_.exchange_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2Data, _impl_.bids_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2Data, _impl_.asks_),
    PROTOBUF_FIELD_OFFSET(::financial_data::Level2Data, _impl_.sequence_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::PriceLevel, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::PriceLevel, _impl_.price_),
    PROTOBUF_FIELD_OFFSET(::financial_data::PriceLevel, _impl_.volume_),
    PROTOBUF_FIELD_OFFSET(::financial_data::PriceLevel, _impl_.order_count_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::financial_data::ResponseMetadata, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::financial_data::ResponseMetadata, _impl_.server_timestamp_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ResponseMetadata, _impl_.server_id_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ResponseMetadata, _impl_.sequence_number_),
    PROTOBUF_FIELD_OFFSET(::financial_data::ResponseMetadata, _impl_.processing_latency_us_),
};

static const ::_pbi::MigrationSchema
    schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
        {0, -1, -1, sizeof(::financial_data::TickDataRequest)},
        {12, -1, -1, sizeof(::financial_data::HistoricalTickDataRequest)},
        {27, -1, -1, sizeof(::financial_data::KlineDataRequest)},
        {41, -1, -1, sizeof(::financial_data::Level2DataRequest)},
        {53, -1, -1, sizeof(::financial_data::HealthCheckRequest)},
        {62, 74, -1, sizeof(::financial_data::TickDataResponse)},
        {78, 89, -1, sizeof(::financial_data::KlineDataResponse)},
        {92, 102, -1, sizeof(::financial_data::Level2DataResponse)},
        {104, -1, -1, sizeof(::financial_data::HealthCheckResponse)},
        {114, -1, -1, sizeof(::financial_data::TickData)},
        {133, -1, -1, sizeof(::financial_data::KlineData)},
        {152, -1, -1, sizeof(::financial_data::Level2Data)},
        {166, -1, -1, sizeof(::financial_data::PriceLevel)},
        {177, -1, -1, sizeof(::financial_data::ResponseMetadata)},
};

static const ::_pb::Message* const file_default_instances[] = {
    &::financial_data::_TickDataRequest_default_instance_._instance,
    &::financial_data::_HistoricalTickDataRequest_default_instance_._instance,
    &::financial_data::_KlineDataRequest_default_instance_._instance,
    &::financial_data::_Level2DataRequest_default_instance_._instance,
    &::financial_data::_HealthCheckRequest_default_instance_._instance,
    &::financial_data::_TickDataResponse_default_instance_._instance,
    &::financial_data::_KlineDataResponse_default_instance_._instance,
    &::financial_data::_Level2DataResponse_default_instance_._instance,
    &::financial_data::_HealthCheckResponse_default_instance_._instance,
    &::financial_data::_TickData_default_instance_._instance,
    &::financial_data::_KlineData_default_instance_._instance,
    &::financial_data::_Level2Data_default_instance_._instance,
    &::financial_data::_PriceLevel_default_instance_._instance,
    &::financial_data::_ResponseMetadata_default_instance_._instance,
};
const char descriptor_table_protodef_market_5fdata_5fservice_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
    "\n\031market_data_service.proto\022\016financial_d"
    "ata\"a\n\017TickDataRequest\022\017\n\007symbols\030\001 \003(\t\022"
    "\020\n\010exchange\030\002 \001(\t\022\026\n\016include_level2\030\003 \001("
    "\010\022\023\n\013buffer_size\030\004 \001(\005\"\241\001\n\031HistoricalTic"
    "kDataRequest\022\016\n\006symbol\030\001 \001(\t\022\020\n\010exchange"
    "\030\002 \001(\t\022\027\n\017start_timestamp\030\003 \001(\003\022\025\n\rend_t"
    "imestamp\030\004 \001(\003\022\r\n\005limit\030\005 \001(\005\022\016\n\006cursor\030"
    "\006 \001(\t\022\023\n\013buffer_size\030\007 \001(\005\"\212\001\n\020KlineData"
    "Request\022\017\n\007symbols\030\001 \003(\t\022\020\n\010exchange\030\002 \001"
    "(\t\022\016\n\006period\030\003 \001(\t\022\027\n\017start_timestamp\030\004 "
    "\001(\003\022\025\n\rend_timestamp\030\005 \001(\003\022\023\n\013buffer_siz"
    "e\030\006 \001(\005\"Z\n\021Level2DataRequest\022\017\n\007symbols\030"
    "\001 \003(\t\022\020\n\010exchange\030\002 \001(\t\022\r\n\005depth\030\003 \001(\005\022\023"
    "\n\013buffer_size\030\004 \001(\005\"%\n\022HealthCheckReques"
    "t\022\017\n\007service\030\001 \001(\t\"\226\001\n\020TickDataResponse\022"
    "\'\n\005ticks\030\001 \003(\0132\030.financial_data.TickData"
    "\022\020\n\010has_more\030\002 \001(\010\022\023\n\013next_cursor\030\003 \001(\t\022"
    "2\n\010metadata\030\004 \001(\0132 .financial_data.Respo"
    "nseMetadata\"\204\001\n\021KlineDataResponse\022)\n\006kli"
    "nes\030\001 \003(\0132\031.financial_data.KlineData\022\020\n\010"
    "has_more\030\002 \001(\010\0222\n\010metadata\030\003 \001(\0132 .finan"
    "cial_data.ResponseMetadata\"y\n\022Level2Data"
    "Response\022/\n\013level2_data\030\001 \003(\0132\032.financia"
    "l_data.Level2Data\0222\n\010metadata\030\002 \001(\0132 .fi"
    "nancial_data.ResponseMetadata\"\272\001\n\023Health"
    "CheckResponse\022A\n\006status\030\001 \001(\01621.financia"
    "l_data.HealthCheckResponse.ServingStatus"
    "\022\017\n\007message\030\002 \001(\t\"O\n\rServingStatus\022\013\n\007UN"
    "KNOWN\020\000\022\013\n\007SERVING\020\001\022\017\n\013NOT_SERVING\020\002\022\023\n"
    "\017SERVICE_UNKNOWN\020\003\"\206\002\n\010TickData\022\021\n\ttimes"
    "tamp\030\001 \001(\003\022\016\n\006symbol\030\002 \001(\t\022\020\n\010exchange\030\003"
    " \001(\t\022\022\n\nlast_price\030\004 \001(\001\022\016\n\006volume\030\005 \001(\003"
    "\022\020\n\010turnover\030\006 \001(\001\022\025\n\ropen_interest\030\007 \001("
    "\003\022(\n\004bids\030\010 \003(\0132\032.financial_data.PriceLe"
    "vel\022(\n\004asks\030\t \003(\0132\032.financial_data.Price"
    "Level\022\020\n\010sequence\030\n \001(\r\022\022\n\ntrade_flag\030\013 "
    "\001(\t\"\301\001\n\tKlineData\022\016\n\006symbol\030\001 \001(\t\022\020\n\010exc"
    "hange\030\002 \001(\t\022\016\n\006period\030\003 \001(\t\022\021\n\ttimestamp"
    "\030\004 \001(\003\022\014\n\004open\030\005 \001(\001\022\014\n\004high\030\006 \001(\001\022\013\n\003lo"
    "w\030\007 \001(\001\022\r\n\005close\030\010 \001(\001\022\016\n\006volume\030\t \001(\003\022\020"
    "\n\010turnover\030\n \001(\001\022\025\n\ropen_interest\030\013 \001(\003\""
    "\247\001\n\nLevel2Data\022\021\n\ttimestamp\030\001 \001(\003\022\016\n\006sym"
    "bol\030\002 \001(\t\022\020\n\010exchange\030\003 \001(\t\022(\n\004bids\030\004 \003("
    "\0132\032.financial_data.PriceLevel\022(\n\004asks\030\005 "
    "\003(\0132\032.financial_data.PriceLevel\022\020\n\010seque"
    "nce\030\006 \001(\r\"@\n\nPriceLevel\022\r\n\005price\030\001 \001(\001\022\016"
    "\n\006volume\030\002 \001(\005\022\023\n\013order_count\030\003 \001(\005\"w\n\020R"
    "esponseMetadata\022\030\n\020server_timestamp\030\001 \001("
    "\003\022\021\n\tserver_id\030\002 \001(\t\022\027\n\017sequence_number\030"
    "\003 \001(\005\022\035\n\025processing_latency_us\030\004 \001(\0012\341\003\n"
    "\021MarketDataService\022U\n\016StreamTickData\022\037.f"
    "inancial_data.TickDataRequest\032 .financia"
    "l_data.TickDataResponse0\001\022f\n\025GetHistoric"
    "alTickData\022).financial_data.HistoricalTi"
    "ckDataRequest\032 .financial_data.TickDataR"
    "esponse0\001\022X\n\017StreamKlineData\022 .financial"
    "_data.KlineDataRequest\032!.financial_data."
    "KlineDataResponse0\001\022[\n\020StreamLevel2Data\022"
    "!.financial_data.Level2DataRequest\032\".fin"
    "ancial_data.Level2DataResponse0\001\022V\n\013Heal"
    "thCheck\022\".financial_data.HealthCheckRequ"
    "est\032#.financial_data.HealthCheckResponse"
    "B\003\200\001\000b\006proto3"
};
static ::absl::once_flag descriptor_table_market_5fdata_5fservice_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_market_5fdata_5fservice_2eproto = {
    false,
    false,
    2493,
    descriptor_table_protodef_market_5fdata_5fservice_2eproto,
    "market_data_service.proto",
    &descriptor_table_market_5fdata_5fservice_2eproto_once,
    nullptr,
    0,
    14,
    schemas,
    file_default_instances,
    TableStruct_market_5fdata_5fservice_2eproto::offsets,
    file_level_metadata_market_5fdata_5fservice_2eproto,
    file_level_enum_descriptors_market_5fdata_5fservice_2eproto,
    file_level_service_descriptors_market_5fdata_5fservice_2eproto,
};

// This function exists to be marked as weak.
// It can significantly speed up compilation by breaking up LLVM's SCC
// in the .pb.cc translation units. Large translation units see a
// reduction of more than 35% of walltime for optimized builds. Without
// the weak attribute all the messages in the file, including all the
// vtables and everything they use become part of the same SCC through
// a cycle like:
// GetMetadata -> descriptor table -> default instances ->
//   vtables -> GetMetadata
// By adding a weak function here we break the connection from the
// individual vtables back into the descriptor table.
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_market_5fdata_5fservice_2eproto_getter() {
  return &descriptor_table_market_5fdata_5fservice_2eproto;
}
// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2
static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_market_5fdata_5fservice_2eproto(&descriptor_table_market_5fdata_5fservice_2eproto);
namespace financial_data {
const ::google::protobuf::EnumDescriptor* HealthCheckResponse_ServingStatus_descriptor() {
  ::google::protobuf::internal::AssignDescriptors(&descriptor_table_market_5fdata_5fservice_2eproto);
  return file_level_enum_descriptors_market_5fdata_5fservice_2eproto[0];
}
PROTOBUF_CONSTINIT const uint32_t HealthCheckResponse_ServingStatus_internal_data_[] = {
    262144u, 0u, };
bool HealthCheckResponse_ServingStatus_IsValid(int value) {
  return 0 <= value && value <= 3;
}
#if (__cplusplus < 201703) && \
  (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

constexpr HealthCheckResponse_ServingStatus HealthCheckResponse::UNKNOWN;
constexpr HealthCheckResponse_ServingStatus HealthCheckResponse::SERVING;
constexpr HealthCheckResponse_ServingStatus HealthCheckResponse::NOT_SERVING;
constexpr HealthCheckResponse_ServingStatus HealthCheckResponse::SERVICE_UNKNOWN;
constexpr HealthCheckResponse_ServingStatus HealthCheckResponse::ServingStatus_MIN;
constexpr HealthCheckResponse_ServingStatus HealthCheckResponse::ServingStatus_MAX;
constexpr int HealthCheckResponse::ServingStatus_ARRAYSIZE;

#endif  // (__cplusplus < 201703) &&
        // (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
// ===================================================================

class TickDataRequest::_Internal {
 public:
};

TickDataRequest::TickDataRequest(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.TickDataRequest)
}
inline PROTOBUF_NDEBUG_INLINE TickDataRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : symbols_{visibility, arena, from.symbols_},
        exchange_(arena, from.exchange_),
        _cached_size_{0} {}

TickDataRequest::TickDataRequest(
    ::google::protobuf::Arena* arena,
    const TickDataRequest& from)
    : ::google::protobuf::Message(arena) {
  TickDataRequest* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, include_level2_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, include_level2_),
           offsetof(Impl_, buffer_size_) -
               offsetof(Impl_, include_level2_) +
               sizeof(Impl_::buffer_size_));

  // @@protoc_insertion_point(copy_constructor:financial_data.TickDataRequest)
}
inline PROTOBUF_NDEBUG_INLINE TickDataRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : symbols_{visibility, arena},
        exchange_(arena),
        _cached_size_{0} {}

inline void TickDataRequest::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, include_level2_),
           0,
           offsetof(Impl_, buffer_size_) -
               offsetof(Impl_, include_level2_) +
               sizeof(Impl_::buffer_size_));
}
TickDataRequest::~TickDataRequest() {
  // @@protoc_insertion_point(destructor:financial_data.TickDataRequest)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void TickDataRequest::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.exchange_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void TickDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.TickDataRequest)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.symbols_.Clear();
  _impl_.exchange_.ClearToEmpty();
  ::memset(&_impl_.include_level2_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.buffer_size_) -
      reinterpret_cast<char*>(&_impl_.include_level2_)) + sizeof(_impl_.buffer_size_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* TickDataRequest::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 0, 54, 2> TickDataRequest::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_TickDataRequest_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // int32 buffer_size = 4;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(TickDataRequest, _impl_.buffer_size_), 63>(),
     {32, 63, 0, PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.buffer_size_)}},
    // repeated string symbols = 1;
    {::_pbi::TcParser::FastUR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.symbols_)}},
    // string exchange = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.exchange_)}},
    // bool include_level2 = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<bool, offsetof(TickDataRequest, _impl_.include_level2_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.include_level2_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated string symbols = 1;
    {PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.symbols_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kUtf8String | ::_fl::kRepSString)},
    // string exchange = 2;
    {PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // bool include_level2 = 3;
    {PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.include_level2_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBool)},
    // int32 buffer_size = 4;
    {PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.buffer_size_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
    "\36\7\10\0\0\0\0\0"
    "financial_data.TickDataRequest"
    "symbols"
    "exchange"
  }},
};

::uint8_t* TickDataRequest::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.TickDataRequest)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // repeated string symbols = 1;
  for (int i = 0, n = this->_internal_symbols_size(); i < n; ++i) {
    const auto& s = this->_internal_symbols().Get(i);
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        s.data(), static_cast<int>(s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.TickDataRequest.symbols");
    target = stream->WriteString(1, s, target);
  }

  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.TickDataRequest.exchange");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // bool include_level2 = 3;
  if (this->_internal_include_level2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(
        3, this->_internal_include_level2(), target);
  }

  // int32 buffer_size = 4;
  if (this->_internal_buffer_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<4>(
            stream, this->_internal_buffer_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.TickDataRequest)
  return target;
}

::size_t TickDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.TickDataRequest)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string symbols = 1;
  total_size += 1 * ::google::protobuf::internal::FromIntSize(_internal_symbols().size());
  for (int i = 0, n = _internal_symbols().size(); i < n; ++i) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
        _internal_symbols().Get(i));
  }
  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // bool include_level2 = 3;
  if (this->_internal_include_level2() != 0) {
    total_size += 2;
  }

  // int32 buffer_size = 4;
  if (this->_internal_buffer_size() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_buffer_size());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData TickDataRequest::_class_data_ = {
    TickDataRequest::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* TickDataRequest::GetClassData() const {
  return &_class_data_;
}

void TickDataRequest::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<TickDataRequest*>(&to_msg);
  auto& from = static_cast<const TickDataRequest&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.TickDataRequest)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_symbols()->MergeFrom(from._internal_symbols());
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (from._internal_include_level2() != 0) {
    _this->_internal_set_include_level2(from._internal_include_level2());
  }
  if (from._internal_buffer_size() != 0) {
    _this->_internal_set_buffer_size(from._internal_buffer_size());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void TickDataRequest::CopyFrom(const TickDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.TickDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool TickDataRequest::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* TickDataRequest::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void TickDataRequest::InternalSwap(TickDataRequest* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.symbols_.InternalSwap(&other->_impl_.symbols_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.buffer_size_)
      + sizeof(TickDataRequest::_impl_.buffer_size_)
      - PROTOBUF_FIELD_OFFSET(TickDataRequest, _impl_.include_level2_)>(
          reinterpret_cast<char*>(&_impl_.include_level2_),
          reinterpret_cast<char*>(&other->_impl_.include_level2_));
}

::google::protobuf::Metadata TickDataRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[0]);
}
// ===================================================================

class HistoricalTickDataRequest::_Internal {
 public:
};

HistoricalTickDataRequest::HistoricalTickDataRequest(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.HistoricalTickDataRequest)
}
inline PROTOBUF_NDEBUG_INLINE HistoricalTickDataRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : symbol_(arena, from.symbol_),
        exchange_(arena, from.exchange_),
        cursor_(arena, from.cursor_),
        _cached_size_{0} {}

HistoricalTickDataRequest::HistoricalTickDataRequest(
    ::google::protobuf::Arena* arena,
    const HistoricalTickDataRequest& from)
    : ::google::protobuf::Message(arena) {
  HistoricalTickDataRequest* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, start_timestamp_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, start_timestamp_),
           offsetof(Impl_, buffer_size_) -
               offsetof(Impl_, start_timestamp_) +
               sizeof(Impl_::buffer_size_));

  // @@protoc_insertion_point(copy_constructor:financial_data.HistoricalTickDataRequest)
}
inline PROTOBUF_NDEBUG_INLINE HistoricalTickDataRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : symbol_(arena),
        exchange_(arena),
        cursor_(arena),
        _cached_size_{0} {}

inline void HistoricalTickDataRequest::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, start_timestamp_),
           0,
           offsetof(Impl_, buffer_size_) -
               offsetof(Impl_, start_timestamp_) +
               sizeof(Impl_::buffer_size_));
}
HistoricalTickDataRequest::~HistoricalTickDataRequest() {
  // @@protoc_insertion_point(destructor:financial_data.HistoricalTickDataRequest)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void HistoricalTickDataRequest::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.symbol_.Destroy();
  _impl_.exchange_.Destroy();
  _impl_.cursor_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void HistoricalTickDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.HistoricalTickDataRequest)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.symbol_.ClearToEmpty();
  _impl_.exchange_.ClearToEmpty();
  _impl_.cursor_.ClearToEmpty();
  ::memset(&_impl_.start_timestamp_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.buffer_size_) -
      reinterpret_cast<char*>(&_impl_.start_timestamp_)) + sizeof(_impl_.buffer_size_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* HistoricalTickDataRequest::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 7, 0, 69, 2> HistoricalTickDataRequest::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    7, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967168,  // skipmap
    offsetof(decltype(_table_), field_entries),
    7,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_HistoricalTickDataRequest_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // string symbol = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.symbol_)}},
    // string exchange = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.exchange_)}},
    // int64 start_timestamp = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(HistoricalTickDataRequest, _impl_.start_timestamp_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.start_timestamp_)}},
    // int64 end_timestamp = 4;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(HistoricalTickDataRequest, _impl_.end_timestamp_), 63>(),
     {32, 63, 0, PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.end_timestamp_)}},
    // int32 limit = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(HistoricalTickDataRequest, _impl_.limit_), 63>(),
     {40, 63, 0, PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.limit_)}},
    // string cursor = 6;
    {::_pbi::TcParser::FastUS1,
     {50, 63, 0, PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.cursor_)}},
    // int32 buffer_size = 7;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(HistoricalTickDataRequest, _impl_.buffer_size_), 63>(),
     {56, 63, 0, PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.buffer_size_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string symbol = 1;
    {PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.symbol_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string exchange = 2;
    {PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int64 start_timestamp = 3;
    {PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.start_timestamp_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // int64 end_timestamp = 4;
    {PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.end_timestamp_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // int32 limit = 5;
    {PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.limit_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // string cursor = 6;
    {PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.cursor_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int32 buffer_size = 7;
    {PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.buffer_size_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
    "\50\6\10\0\0\0\6\0"
    "financial_data.HistoricalTickDataRequest"
    "symbol"
    "exchange"
    "cursor"
  }},
};

::uint8_t* HistoricalTickDataRequest::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.HistoricalTickDataRequest)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // string symbol = 1;
  if (!this->_internal_symbol().empty()) {
    const std::string& _s = this->_internal_symbol();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.HistoricalTickDataRequest.symbol");
    target = stream->WriteStringMaybeAliased(1, _s, target);
  }

  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.HistoricalTickDataRequest.exchange");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // int64 start_timestamp = 3;
  if (this->_internal_start_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<3>(
            stream, this->_internal_start_timestamp(), target);
  }

  // int64 end_timestamp = 4;
  if (this->_internal_end_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<4>(
            stream, this->_internal_end_timestamp(), target);
  }

  // int32 limit = 5;
  if (this->_internal_limit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<5>(
            stream, this->_internal_limit(), target);
  }

  // string cursor = 6;
  if (!this->_internal_cursor().empty()) {
    const std::string& _s = this->_internal_cursor();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.HistoricalTickDataRequest.cursor");
    target = stream->WriteStringMaybeAliased(6, _s, target);
  }

  // int32 buffer_size = 7;
  if (this->_internal_buffer_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<7>(
            stream, this->_internal_buffer_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.HistoricalTickDataRequest)
  return target;
}

::size_t HistoricalTickDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.HistoricalTickDataRequest)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string symbol = 1;
  if (!this->_internal_symbol().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_symbol());
  }

  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // string cursor = 6;
  if (!this->_internal_cursor().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_cursor());
  }

  // int64 start_timestamp = 3;
  if (this->_internal_start_timestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_start_timestamp());
  }

  // int64 end_timestamp = 4;
  if (this->_internal_end_timestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_end_timestamp());
  }

  // int32 limit = 5;
  if (this->_internal_limit() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_limit());
  }

  // int32 buffer_size = 7;
  if (this->_internal_buffer_size() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_buffer_size());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData HistoricalTickDataRequest::_class_data_ = {
    HistoricalTickDataRequest::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* HistoricalTickDataRequest::GetClassData() const {
  return &_class_data_;
}

void HistoricalTickDataRequest::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<HistoricalTickDataRequest*>(&to_msg);
  auto& from = static_cast<const HistoricalTickDataRequest&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.HistoricalTickDataRequest)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_symbol().empty()) {
    _this->_internal_set_symbol(from._internal_symbol());
  }
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (!from._internal_cursor().empty()) {
    _this->_internal_set_cursor(from._internal_cursor());
  }
  if (from._internal_start_timestamp() != 0) {
    _this->_internal_set_start_timestamp(from._internal_start_timestamp());
  }
  if (from._internal_end_timestamp() != 0) {
    _this->_internal_set_end_timestamp(from._internal_end_timestamp());
  }
  if (from._internal_limit() != 0) {
    _this->_internal_set_limit(from._internal_limit());
  }
  if (from._internal_buffer_size() != 0) {
    _this->_internal_set_buffer_size(from._internal_buffer_size());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void HistoricalTickDataRequest::CopyFrom(const HistoricalTickDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.HistoricalTickDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool HistoricalTickDataRequest::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* HistoricalTickDataRequest::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void HistoricalTickDataRequest::InternalSwap(HistoricalTickDataRequest* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.symbol_, &other->_impl_.symbol_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.cursor_, &other->_impl_.cursor_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.buffer_size_)
      + sizeof(HistoricalTickDataRequest::_impl_.buffer_size_)
      - PROTOBUF_FIELD_OFFSET(HistoricalTickDataRequest, _impl_.start_timestamp_)>(
          reinterpret_cast<char*>(&_impl_.start_timestamp_),
          reinterpret_cast<char*>(&other->_impl_.start_timestamp_));
}

::google::protobuf::Metadata HistoricalTickDataRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[1]);
}
// ===================================================================

class KlineDataRequest::_Internal {
 public:
};

KlineDataRequest::KlineDataRequest(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.KlineDataRequest)
}
inline PROTOBUF_NDEBUG_INLINE KlineDataRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : symbols_{visibility, arena, from.symbols_},
        exchange_(arena, from.exchange_),
        period_(arena, from.period_),
        _cached_size_{0} {}

KlineDataRequest::KlineDataRequest(
    ::google::protobuf::Arena* arena,
    const KlineDataRequest& from)
    : ::google::protobuf::Message(arena) {
  KlineDataRequest* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, start_timestamp_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, start_timestamp_),
           offsetof(Impl_, buffer_size_) -
               offsetof(Impl_, start_timestamp_) +
               sizeof(Impl_::buffer_size_));

  // @@protoc_insertion_point(copy_constructor:financial_data.KlineDataRequest)
}
inline PROTOBUF_NDEBUG_INLINE KlineDataRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : symbols_{visibility, arena},
        exchange_(arena),
        period_(arena),
        _cached_size_{0} {}

inline void KlineDataRequest::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, start_timestamp_),
           0,
           offsetof(Impl_, buffer_size_) -
               offsetof(Impl_, start_timestamp_) +
               sizeof(Impl_::buffer_size_));
}
KlineDataRequest::~KlineDataRequest() {
  // @@protoc_insertion_point(destructor:financial_data.KlineDataRequest)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void KlineDataRequest::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.exchange_.Destroy();
  _impl_.period_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void KlineDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.KlineDataRequest)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.symbols_.Clear();
  _impl_.exchange_.ClearToEmpty();
  _impl_.period_.ClearToEmpty();
  ::memset(&_impl_.start_timestamp_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.buffer_size_) -
      reinterpret_cast<char*>(&_impl_.start_timestamp_)) + sizeof(_impl_.buffer_size_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* KlineDataRequest::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 6, 0, 61, 2> KlineDataRequest::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    6, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967232,  // skipmap
    offsetof(decltype(_table_), field_entries),
    6,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_KlineDataRequest_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // repeated string symbols = 1;
    {::_pbi::TcParser::FastUR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.symbols_)}},
    // string exchange = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.exchange_)}},
    // string period = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.period_)}},
    // int64 start_timestamp = 4;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(KlineDataRequest, _impl_.start_timestamp_), 63>(),
     {32, 63, 0, PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.start_timestamp_)}},
    // int64 end_timestamp = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(KlineDataRequest, _impl_.end_timestamp_), 63>(),
     {40, 63, 0, PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.end_timestamp_)}},
    // int32 buffer_size = 6;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(KlineDataRequest, _impl_.buffer_size_), 63>(),
     {48, 63, 0, PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.buffer_size_)}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated string symbols = 1;
    {PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.symbols_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kUtf8String | ::_fl::kRepSString)},
    // string exchange = 2;
    {PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string period = 3;
    {PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.period_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int64 start_timestamp = 4;
    {PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.start_timestamp_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // int64 end_timestamp = 5;
    {PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.end_timestamp_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // int32 buffer_size = 6;
    {PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.buffer_size_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
    "\37\7\10\6\0\0\0\0"
    "financial_data.KlineDataRequest"
    "symbols"
    "exchange"
    "period"
  }},
};

::uint8_t* KlineDataRequest::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.KlineDataRequest)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // repeated string symbols = 1;
  for (int i = 0, n = this->_internal_symbols_size(); i < n; ++i) {
    const auto& s = this->_internal_symbols().Get(i);
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        s.data(), static_cast<int>(s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.KlineDataRequest.symbols");
    target = stream->WriteString(1, s, target);
  }

  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.KlineDataRequest.exchange");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // string period = 3;
  if (!this->_internal_period().empty()) {
    const std::string& _s = this->_internal_period();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.KlineDataRequest.period");
    target = stream->WriteStringMaybeAliased(3, _s, target);
  }

  // int64 start_timestamp = 4;
  if (this->_internal_start_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<4>(
            stream, this->_internal_start_timestamp(), target);
  }

  // int64 end_timestamp = 5;
  if (this->_internal_end_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<5>(
            stream, this->_internal_end_timestamp(), target);
  }

  // int32 buffer_size = 6;
  if (this->_internal_buffer_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<6>(
            stream, this->_internal_buffer_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.KlineDataRequest)
  return target;
}

::size_t KlineDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.KlineDataRequest)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string symbols = 1;
  total_size += 1 * ::google::protobuf::internal::FromIntSize(_internal_symbols().size());
  for (int i = 0, n = _internal_symbols().size(); i < n; ++i) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
        _internal_symbols().Get(i));
  }
  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // string period = 3;
  if (!this->_internal_period().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_period());
  }

  // int64 start_timestamp = 4;
  if (this->_internal_start_timestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_start_timestamp());
  }

  // int64 end_timestamp = 5;
  if (this->_internal_end_timestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_end_timestamp());
  }

  // int32 buffer_size = 6;
  if (this->_internal_buffer_size() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_buffer_size());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData KlineDataRequest::_class_data_ = {
    KlineDataRequest::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* KlineDataRequest::GetClassData() const {
  return &_class_data_;
}

void KlineDataRequest::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<KlineDataRequest*>(&to_msg);
  auto& from = static_cast<const KlineDataRequest&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.KlineDataRequest)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_symbols()->MergeFrom(from._internal_symbols());
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (!from._internal_period().empty()) {
    _this->_internal_set_period(from._internal_period());
  }
  if (from._internal_start_timestamp() != 0) {
    _this->_internal_set_start_timestamp(from._internal_start_timestamp());
  }
  if (from._internal_end_timestamp() != 0) {
    _this->_internal_set_end_timestamp(from._internal_end_timestamp());
  }
  if (from._internal_buffer_size() != 0) {
    _this->_internal_set_buffer_size(from._internal_buffer_size());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void KlineDataRequest::CopyFrom(const KlineDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.KlineDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool KlineDataRequest::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* KlineDataRequest::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void KlineDataRequest::InternalSwap(KlineDataRequest* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.symbols_.InternalSwap(&other->_impl_.symbols_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.period_, &other->_impl_.period_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.buffer_size_)
      + sizeof(KlineDataRequest::_impl_.buffer_size_)
      - PROTOBUF_FIELD_OFFSET(KlineDataRequest, _impl_.start_timestamp_)>(
          reinterpret_cast<char*>(&_impl_.start_timestamp_),
          reinterpret_cast<char*>(&other->_impl_.start_timestamp_));
}

::google::protobuf::Metadata KlineDataRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[2]);
}
// ===================================================================

class Level2DataRequest::_Internal {
 public:
};

Level2DataRequest::Level2DataRequest(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.Level2DataRequest)
}
inline PROTOBUF_NDEBUG_INLINE Level2DataRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : symbols_{visibility, arena, from.symbols_},
        exchange_(arena, from.exchange_),
        _cached_size_{0} {}

Level2DataRequest::Level2DataRequest(
    ::google::protobuf::Arena* arena,
    const Level2DataRequest& from)
    : ::google::protobuf::Message(arena) {
  Level2DataRequest* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, depth_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, depth_),
           offsetof(Impl_, buffer_size_) -
               offsetof(Impl_, depth_) +
               sizeof(Impl_::buffer_size_));

  // @@protoc_insertion_point(copy_constructor:financial_data.Level2DataRequest)
}
inline PROTOBUF_NDEBUG_INLINE Level2DataRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : symbols_{visibility, arena},
        exchange_(arena),
        _cached_size_{0} {}

inline void Level2DataRequest::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, depth_),
           0,
           offsetof(Impl_, buffer_size_) -
               offsetof(Impl_, depth_) +
               sizeof(Impl_::buffer_size_));
}
Level2DataRequest::~Level2DataRequest() {
  // @@protoc_insertion_point(destructor:financial_data.Level2DataRequest)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Level2DataRequest::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.exchange_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void Level2DataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.Level2DataRequest)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.symbols_.Clear();
  _impl_.exchange_.ClearToEmpty();
  ::memset(&_impl_.depth_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.buffer_size_) -
      reinterpret_cast<char*>(&_impl_.depth_)) + sizeof(_impl_.buffer_size_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* Level2DataRequest::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 0, 56, 2> Level2DataRequest::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_Level2DataRequest_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // int32 buffer_size = 4;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(Level2DataRequest, _impl_.buffer_size_), 63>(),
     {32, 63, 0, PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.buffer_size_)}},
    // repeated string symbols = 1;
    {::_pbi::TcParser::FastUR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.symbols_)}},
    // string exchange = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.exchange_)}},
    // int32 depth = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(Level2DataRequest, _impl_.depth_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.depth_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated string symbols = 1;
    {PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.symbols_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kUtf8String | ::_fl::kRepSString)},
    // string exchange = 2;
    {PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int32 depth = 3;
    {PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.depth_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // int32 buffer_size = 4;
    {PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.buffer_size_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
    "\40\7\10\0\0\0\0\0"
    "financial_data.Level2DataRequest"
    "symbols"
    "exchange"
  }},
};

::uint8_t* Level2DataRequest::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.Level2DataRequest)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // repeated string symbols = 1;
  for (int i = 0, n = this->_internal_symbols_size(); i < n; ++i) {
    const auto& s = this->_internal_symbols().Get(i);
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        s.data(), static_cast<int>(s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.Level2DataRequest.symbols");
    target = stream->WriteString(1, s, target);
  }

  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.Level2DataRequest.exchange");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // int32 depth = 3;
  if (this->_internal_depth() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<3>(
            stream, this->_internal_depth(), target);
  }

  // int32 buffer_size = 4;
  if (this->_internal_buffer_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<4>(
            stream, this->_internal_buffer_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.Level2DataRequest)
  return target;
}

::size_t Level2DataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.Level2DataRequest)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string symbols = 1;
  total_size += 1 * ::google::protobuf::internal::FromIntSize(_internal_symbols().size());
  for (int i = 0, n = _internal_symbols().size(); i < n; ++i) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
        _internal_symbols().Get(i));
  }
  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // int32 depth = 3;
  if (this->_internal_depth() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_depth());
  }

  // int32 buffer_size = 4;
  if (this->_internal_buffer_size() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_buffer_size());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData Level2DataRequest::_class_data_ = {
    Level2DataRequest::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* Level2DataRequest::GetClassData() const {
  return &_class_data_;
}

void Level2DataRequest::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<Level2DataRequest*>(&to_msg);
  auto& from = static_cast<const Level2DataRequest&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.Level2DataRequest)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_symbols()->MergeFrom(from._internal_symbols());
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (from._internal_depth() != 0) {
    _this->_internal_set_depth(from._internal_depth());
  }
  if (from._internal_buffer_size() != 0) {
    _this->_internal_set_buffer_size(from._internal_buffer_size());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Level2DataRequest::CopyFrom(const Level2DataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.Level2DataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool Level2DataRequest::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* Level2DataRequest::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void Level2DataRequest::InternalSwap(Level2DataRequest* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.symbols_.InternalSwap(&other->_impl_.symbols_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.buffer_size_)
      + sizeof(Level2DataRequest::_impl_.buffer_size_)
      - PROTOBUF_FIELD_OFFSET(Level2DataRequest, _impl_.depth_)>(
          reinterpret_cast<char*>(&_impl_.depth_),
          reinterpret_cast<char*>(&other->_impl_.depth_));
}

::google::protobuf::Metadata Level2DataRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[3]);
}
// ===================================================================

class HealthCheckRequest::_Internal {
 public:
};

HealthCheckRequest::HealthCheckRequest(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.HealthCheckRequest)
}
inline PROTOBUF_NDEBUG_INLINE HealthCheckRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : service_(arena, from.service_),
        _cached_size_{0} {}

HealthCheckRequest::HealthCheckRequest(
    ::google::protobuf::Arena* arena,
    const HealthCheckRequest& from)
    : ::google::protobuf::Message(arena) {
  HealthCheckRequest* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);

  // @@protoc_insertion_point(copy_constructor:financial_data.HealthCheckRequest)
}
inline PROTOBUF_NDEBUG_INLINE HealthCheckRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : service_(arena),
        _cached_size_{0} {}

inline void HealthCheckRequest::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
HealthCheckRequest::~HealthCheckRequest() {
  // @@protoc_insertion_point(destructor:financial_data.HealthCheckRequest)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void HealthCheckRequest::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.service_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void HealthCheckRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.HealthCheckRequest)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.service_.ClearToEmpty();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* HealthCheckRequest::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 0, 49, 2> HealthCheckRequest::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    **********,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_HealthCheckRequest_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // string service = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(HealthCheckRequest, _impl_.service_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string service = 1;
    {PROTOBUF_FIELD_OFFSET(HealthCheckRequest, _impl_.service_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\41\7\0\0\0\0\0\0"
    "financial_data.HealthCheckRequest"
    "service"
  }},
};

::uint8_t* HealthCheckRequest::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.HealthCheckRequest)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // string service = 1;
  if (!this->_internal_service().empty()) {
    const std::string& _s = this->_internal_service();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.HealthCheckRequest.service");
    target = stream->WriteStringMaybeAliased(1, _s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.HealthCheckRequest)
  return target;
}

::size_t HealthCheckRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.HealthCheckRequest)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string service = 1;
  if (!this->_internal_service().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_service());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData HealthCheckRequest::_class_data_ = {
    HealthCheckRequest::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* HealthCheckRequest::GetClassData() const {
  return &_class_data_;
}

void HealthCheckRequest::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<HealthCheckRequest*>(&to_msg);
  auto& from = static_cast<const HealthCheckRequest&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.HealthCheckRequest)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_service().empty()) {
    _this->_internal_set_service(from._internal_service());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void HealthCheckRequest::CopyFrom(const HealthCheckRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.HealthCheckRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool HealthCheckRequest::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* HealthCheckRequest::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void HealthCheckRequest::InternalSwap(HealthCheckRequest* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.service_, &other->_impl_.service_, arena);
}

::google::protobuf::Metadata HealthCheckRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[4]);
}
// ===================================================================

class TickDataResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<TickDataResponse>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
    8 * PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_._has_bits_);
  static const ::financial_data::ResponseMetadata& metadata(const TickDataResponse* msg);
  static void set_has_metadata(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::financial_data::ResponseMetadata& TickDataResponse::_Internal::metadata(const TickDataResponse* msg) {
  return *msg->_impl_.metadata_;
}
TickDataResponse::TickDataResponse(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.TickDataResponse)
}
inline PROTOBUF_NDEBUG_INLINE TickDataResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        ticks_{visibility, arena, from.ticks_},
        next_cursor_(arena, from.next_cursor_) {}

TickDataResponse::TickDataResponse(
    ::google::protobuf::Arena* arena,
    const TickDataResponse& from)
    : ::google::protobuf::Message(arena) {
  TickDataResponse* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.metadata_ = (cached_has_bits & 0x00000001u)
                ? CreateMaybeMessage<::financial_data::ResponseMetadata>(arena, *from._impl_.metadata_)
                : nullptr;
  _impl_.has_more_ = from._impl_.has_more_;

  // @@protoc_insertion_point(copy_constructor:financial_data.TickDataResponse)
}
inline PROTOBUF_NDEBUG_INLINE TickDataResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        ticks_{visibility, arena},
        next_cursor_(arena) {}

inline void TickDataResponse::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, metadata_),
           0,
           offsetof(Impl_, has_more_) -
               offsetof(Impl_, metadata_) +
               sizeof(Impl_::has_more_));
}
TickDataResponse::~TickDataResponse() {
  // @@protoc_insertion_point(destructor:financial_data.TickDataResponse)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void TickDataResponse::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.next_cursor_.Destroy();
  delete _impl_.metadata_;
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void TickDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.TickDataResponse)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.ticks_.Clear();
  _impl_.next_cursor_.ClearToEmpty();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.metadata_ != nullptr);
    _impl_.metadata_->Clear();
  }
  _impl_.has_more_ = false;
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* TickDataResponse::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 2, 51, 2> TickDataResponse::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_._has_bits_),
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_TickDataResponse_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // .financial_data.ResponseMetadata metadata = 4;
    {::_pbi::TcParser::FastMtS1,
     {34, 0, 1, PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.metadata_)}},
    // repeated .financial_data.TickData ticks = 1;
    {::_pbi::TcParser::FastMtR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.ticks_)}},
    // bool has_more = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<bool, offsetof(TickDataResponse, _impl_.has_more_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.has_more_)}},
    // string next_cursor = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.next_cursor_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated .financial_data.TickData ticks = 1;
    {PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.ticks_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // bool has_more = 2;
    {PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.has_more_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBool)},
    // string next_cursor = 3;
    {PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.next_cursor_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // .financial_data.ResponseMetadata metadata = 4;
    {PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.metadata_), _Internal::kHasBitsOffset + 0, 1,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::financial_data::TickData>()},
    {::_pbi::TcParser::GetTable<::financial_data::ResponseMetadata>()},
  }}, {{
    "\37\0\0\13\0\0\0\0"
    "financial_data.TickDataResponse"
    "next_cursor"
  }},
};

::uint8_t* TickDataResponse::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.TickDataResponse)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // repeated .financial_data.TickData ticks = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_ticks_size()); i < n; i++) {
    const auto& repfield = this->_internal_ticks().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // bool has_more = 2;
  if (this->_internal_has_more() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(
        2, this->_internal_has_more(), target);
  }

  // string next_cursor = 3;
  if (!this->_internal_next_cursor().empty()) {
    const std::string& _s = this->_internal_next_cursor();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.TickDataResponse.next_cursor");
    target = stream->WriteStringMaybeAliased(3, _s, target);
  }

  cached_has_bits = _impl_._has_bits_[0];
  // .financial_data.ResponseMetadata metadata = 4;
  if (cached_has_bits & 0x00000001u) {
    target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
        4, _Internal::metadata(this),
        _Internal::metadata(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.TickDataResponse)
  return target;
}

::size_t TickDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.TickDataResponse)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .financial_data.TickData ticks = 1;
  total_size += 1UL * this->_internal_ticks_size();
  for (const auto& msg : this->_internal_ticks()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // string next_cursor = 3;
  if (!this->_internal_next_cursor().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_next_cursor());
  }

  // .financial_data.ResponseMetadata metadata = 4;
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size +=
        1 + ::google::protobuf::internal::WireFormatLite::MessageSize(*_impl_.metadata_);
  }

  // bool has_more = 2;
  if (this->_internal_has_more() != 0) {
    total_size += 2;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData TickDataResponse::_class_data_ = {
    TickDataResponse::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* TickDataResponse::GetClassData() const {
  return &_class_data_;
}

void TickDataResponse::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<TickDataResponse*>(&to_msg);
  auto& from = static_cast<const TickDataResponse&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.TickDataResponse)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_ticks()->MergeFrom(
      from._internal_ticks());
  if (!from._internal_next_cursor().empty()) {
    _this->_internal_set_next_cursor(from._internal_next_cursor());
  }
  if ((from._impl_._has_bits_[0] & 0x00000001u) != 0) {
    _this->_internal_mutable_metadata()->::financial_data::ResponseMetadata::MergeFrom(
        from._internal_metadata());
  }
  if (from._internal_has_more() != 0) {
    _this->_internal_set_has_more(from._internal_has_more());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void TickDataResponse::CopyFrom(const TickDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.TickDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool TickDataResponse::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* TickDataResponse::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void TickDataResponse::InternalSwap(TickDataResponse* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.ticks_.InternalSwap(&other->_impl_.ticks_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.next_cursor_, &other->_impl_.next_cursor_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.has_more_)
      + sizeof(TickDataResponse::_impl_.has_more_)
      - PROTOBUF_FIELD_OFFSET(TickDataResponse, _impl_.metadata_)>(
          reinterpret_cast<char*>(&_impl_.metadata_),
          reinterpret_cast<char*>(&other->_impl_.metadata_));
}

::google::protobuf::Metadata TickDataResponse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[5]);
}
// ===================================================================

class KlineDataResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<KlineDataResponse>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
    8 * PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_._has_bits_);
  static const ::financial_data::ResponseMetadata& metadata(const KlineDataResponse* msg);
  static void set_has_metadata(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::financial_data::ResponseMetadata& KlineDataResponse::_Internal::metadata(const KlineDataResponse* msg) {
  return *msg->_impl_.metadata_;
}
KlineDataResponse::KlineDataResponse(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.KlineDataResponse)
}
inline PROTOBUF_NDEBUG_INLINE KlineDataResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        klines_{visibility, arena, from.klines_} {}

KlineDataResponse::KlineDataResponse(
    ::google::protobuf::Arena* arena,
    const KlineDataResponse& from)
    : ::google::protobuf::Message(arena) {
  KlineDataResponse* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.metadata_ = (cached_has_bits & 0x00000001u)
                ? CreateMaybeMessage<::financial_data::ResponseMetadata>(arena, *from._impl_.metadata_)
                : nullptr;
  _impl_.has_more_ = from._impl_.has_more_;

  // @@protoc_insertion_point(copy_constructor:financial_data.KlineDataResponse)
}
inline PROTOBUF_NDEBUG_INLINE KlineDataResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        klines_{visibility, arena} {}

inline void KlineDataResponse::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, metadata_),
           0,
           offsetof(Impl_, has_more_) -
               offsetof(Impl_, metadata_) +
               sizeof(Impl_::has_more_));
}
KlineDataResponse::~KlineDataResponse() {
  // @@protoc_insertion_point(destructor:financial_data.KlineDataResponse)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void KlineDataResponse::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  delete _impl_.metadata_;
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void KlineDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.KlineDataResponse)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.klines_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.metadata_ != nullptr);
    _impl_.metadata_->Clear();
  }
  _impl_.has_more_ = false;
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* KlineDataResponse::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 2, 0, 2> KlineDataResponse::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_._has_bits_),
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_KlineDataResponse_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // repeated .financial_data.KlineData klines = 1;
    {::_pbi::TcParser::FastMtR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_.klines_)}},
    // bool has_more = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<bool, offsetof(KlineDataResponse, _impl_.has_more_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_.has_more_)}},
    // .financial_data.ResponseMetadata metadata = 3;
    {::_pbi::TcParser::FastMtS1,
     {26, 0, 1, PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_.metadata_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated .financial_data.KlineData klines = 1;
    {PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_.klines_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // bool has_more = 2;
    {PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_.has_more_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBool)},
    // .financial_data.ResponseMetadata metadata = 3;
    {PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_.metadata_), _Internal::kHasBitsOffset + 0, 1,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::financial_data::KlineData>()},
    {::_pbi::TcParser::GetTable<::financial_data::ResponseMetadata>()},
  }}, {{
  }},
};

::uint8_t* KlineDataResponse::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.KlineDataResponse)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // repeated .financial_data.KlineData klines = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_klines_size()); i < n; i++) {
    const auto& repfield = this->_internal_klines().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // bool has_more = 2;
  if (this->_internal_has_more() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(
        2, this->_internal_has_more(), target);
  }

  cached_has_bits = _impl_._has_bits_[0];
  // .financial_data.ResponseMetadata metadata = 3;
  if (cached_has_bits & 0x00000001u) {
    target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
        3, _Internal::metadata(this),
        _Internal::metadata(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.KlineDataResponse)
  return target;
}

::size_t KlineDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.KlineDataResponse)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .financial_data.KlineData klines = 1;
  total_size += 1UL * this->_internal_klines_size();
  for (const auto& msg : this->_internal_klines()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // .financial_data.ResponseMetadata metadata = 3;
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size +=
        1 + ::google::protobuf::internal::WireFormatLite::MessageSize(*_impl_.metadata_);
  }

  // bool has_more = 2;
  if (this->_internal_has_more() != 0) {
    total_size += 2;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData KlineDataResponse::_class_data_ = {
    KlineDataResponse::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* KlineDataResponse::GetClassData() const {
  return &_class_data_;
}

void KlineDataResponse::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<KlineDataResponse*>(&to_msg);
  auto& from = static_cast<const KlineDataResponse&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.KlineDataResponse)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_klines()->MergeFrom(
      from._internal_klines());
  if ((from._impl_._has_bits_[0] & 0x00000001u) != 0) {
    _this->_internal_mutable_metadata()->::financial_data::ResponseMetadata::MergeFrom(
        from._internal_metadata());
  }
  if (from._internal_has_more() != 0) {
    _this->_internal_set_has_more(from._internal_has_more());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void KlineDataResponse::CopyFrom(const KlineDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.KlineDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool KlineDataResponse::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* KlineDataResponse::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void KlineDataResponse::InternalSwap(KlineDataResponse* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.klines_.InternalSwap(&other->_impl_.klines_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_.has_more_)
      + sizeof(KlineDataResponse::_impl_.has_more_)
      - PROTOBUF_FIELD_OFFSET(KlineDataResponse, _impl_.metadata_)>(
          reinterpret_cast<char*>(&_impl_.metadata_),
          reinterpret_cast<char*>(&other->_impl_.metadata_));
}

::google::protobuf::Metadata KlineDataResponse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[6]);
}
// ===================================================================

class Level2DataResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<Level2DataResponse>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
    8 * PROTOBUF_FIELD_OFFSET(Level2DataResponse, _impl_._has_bits_);
  static const ::financial_data::ResponseMetadata& metadata(const Level2DataResponse* msg);
  static void set_has_metadata(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::financial_data::ResponseMetadata& Level2DataResponse::_Internal::metadata(const Level2DataResponse* msg) {
  return *msg->_impl_.metadata_;
}
Level2DataResponse::Level2DataResponse(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.Level2DataResponse)
}
inline PROTOBUF_NDEBUG_INLINE Level2DataResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        level2_data_{visibility, arena, from.level2_data_} {}

Level2DataResponse::Level2DataResponse(
    ::google::protobuf::Arena* arena,
    const Level2DataResponse& from)
    : ::google::protobuf::Message(arena) {
  Level2DataResponse* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.metadata_ = (cached_has_bits & 0x00000001u)
                ? CreateMaybeMessage<::financial_data::ResponseMetadata>(arena, *from._impl_.metadata_)
                : nullptr;

  // @@protoc_insertion_point(copy_constructor:financial_data.Level2DataResponse)
}
inline PROTOBUF_NDEBUG_INLINE Level2DataResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        level2_data_{visibility, arena} {}

inline void Level2DataResponse::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.metadata_ = {};
}
Level2DataResponse::~Level2DataResponse() {
  // @@protoc_insertion_point(destructor:financial_data.Level2DataResponse)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Level2DataResponse::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  delete _impl_.metadata_;
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void Level2DataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.Level2DataResponse)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.level2_data_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.metadata_ != nullptr);
    _impl_.metadata_->Clear();
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* Level2DataResponse::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 2, 0, 2> Level2DataResponse::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(Level2DataResponse, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    **********,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_Level2DataResponse_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // .financial_data.ResponseMetadata metadata = 2;
    {::_pbi::TcParser::FastMtS1,
     {18, 0, 1, PROTOBUF_FIELD_OFFSET(Level2DataResponse, _impl_.metadata_)}},
    // repeated .financial_data.Level2Data level2_data = 1;
    {::_pbi::TcParser::FastMtR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(Level2DataResponse, _impl_.level2_data_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated .financial_data.Level2Data level2_data = 1;
    {PROTOBUF_FIELD_OFFSET(Level2DataResponse, _impl_.level2_data_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // .financial_data.ResponseMetadata metadata = 2;
    {PROTOBUF_FIELD_OFFSET(Level2DataResponse, _impl_.metadata_), _Internal::kHasBitsOffset + 0, 1,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::financial_data::Level2Data>()},
    {::_pbi::TcParser::GetTable<::financial_data::ResponseMetadata>()},
  }}, {{
  }},
};

::uint8_t* Level2DataResponse::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.Level2DataResponse)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // repeated .financial_data.Level2Data level2_data = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_level2_data_size()); i < n; i++) {
    const auto& repfield = this->_internal_level2_data().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  cached_has_bits = _impl_._has_bits_[0];
  // .financial_data.ResponseMetadata metadata = 2;
  if (cached_has_bits & 0x00000001u) {
    target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
        2, _Internal::metadata(this),
        _Internal::metadata(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.Level2DataResponse)
  return target;
}

::size_t Level2DataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.Level2DataResponse)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .financial_data.Level2Data level2_data = 1;
  total_size += 1UL * this->_internal_level2_data_size();
  for (const auto& msg : this->_internal_level2_data()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // .financial_data.ResponseMetadata metadata = 2;
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size +=
        1 + ::google::protobuf::internal::WireFormatLite::MessageSize(*_impl_.metadata_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData Level2DataResponse::_class_data_ = {
    Level2DataResponse::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* Level2DataResponse::GetClassData() const {
  return &_class_data_;
}

void Level2DataResponse::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<Level2DataResponse*>(&to_msg);
  auto& from = static_cast<const Level2DataResponse&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.Level2DataResponse)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_level2_data()->MergeFrom(
      from._internal_level2_data());
  if ((from._impl_._has_bits_[0] & 0x00000001u) != 0) {
    _this->_internal_mutable_metadata()->::financial_data::ResponseMetadata::MergeFrom(
        from._internal_metadata());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Level2DataResponse::CopyFrom(const Level2DataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.Level2DataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool Level2DataResponse::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* Level2DataResponse::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void Level2DataResponse::InternalSwap(Level2DataResponse* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.level2_data_.InternalSwap(&other->_impl_.level2_data_);
  swap(_impl_.metadata_, other->_impl_.metadata_);
}

::google::protobuf::Metadata Level2DataResponse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[7]);
}
// ===================================================================

class HealthCheckResponse::_Internal {
 public:
};

HealthCheckResponse::HealthCheckResponse(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.HealthCheckResponse)
}
inline PROTOBUF_NDEBUG_INLINE HealthCheckResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : message_(arena, from.message_),
        _cached_size_{0} {}

HealthCheckResponse::HealthCheckResponse(
    ::google::protobuf::Arena* arena,
    const HealthCheckResponse& from)
    : ::google::protobuf::Message(arena) {
  HealthCheckResponse* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  _impl_.status_ = from._impl_.status_;

  // @@protoc_insertion_point(copy_constructor:financial_data.HealthCheckResponse)
}
inline PROTOBUF_NDEBUG_INLINE HealthCheckResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : message_(arena),
        _cached_size_{0} {}

inline void HealthCheckResponse::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.status_ = {};
}
HealthCheckResponse::~HealthCheckResponse() {
  // @@protoc_insertion_point(destructor:financial_data.HealthCheckResponse)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void HealthCheckResponse::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.message_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void HealthCheckResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.HealthCheckResponse)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.message_.ClearToEmpty();
  _impl_.status_ = 0;
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* HealthCheckResponse::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 50, 2> HealthCheckResponse::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    **********,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_HealthCheckResponse_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // string message = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(HealthCheckResponse, _impl_.message_)}},
    // .financial_data.HealthCheckResponse.ServingStatus status = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(HealthCheckResponse, _impl_.status_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(HealthCheckResponse, _impl_.status_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .financial_data.HealthCheckResponse.ServingStatus status = 1;
    {PROTOBUF_FIELD_OFFSET(HealthCheckResponse, _impl_.status_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kOpenEnum)},
    // string message = 2;
    {PROTOBUF_FIELD_OFFSET(HealthCheckResponse, _impl_.message_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\42\0\7\0\0\0\0\0"
    "financial_data.HealthCheckResponse"
    "message"
  }},
};

::uint8_t* HealthCheckResponse::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.HealthCheckResponse)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // .financial_data.HealthCheckResponse.ServingStatus status = 1;
  if (this->_internal_status() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
        1, this->_internal_status(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    const std::string& _s = this->_internal_message();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.HealthCheckResponse.message");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.HealthCheckResponse)
  return target;
}

::size_t HealthCheckResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.HealthCheckResponse)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_message());
  }

  // .financial_data.HealthCheckResponse.ServingStatus status = 1;
  if (this->_internal_status() != 0) {
    total_size += 1 +
                  ::_pbi::WireFormatLite::EnumSize(this->_internal_status());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData HealthCheckResponse::_class_data_ = {
    HealthCheckResponse::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* HealthCheckResponse::GetClassData() const {
  return &_class_data_;
}

void HealthCheckResponse::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<HealthCheckResponse*>(&to_msg);
  auto& from = static_cast<const HealthCheckResponse&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.HealthCheckResponse)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _this->_internal_set_message(from._internal_message());
  }
  if (from._internal_status() != 0) {
    _this->_internal_set_status(from._internal_status());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void HealthCheckResponse::CopyFrom(const HealthCheckResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.HealthCheckResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool HealthCheckResponse::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* HealthCheckResponse::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void HealthCheckResponse::InternalSwap(HealthCheckResponse* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.message_, &other->_impl_.message_, arena);
  swap(_impl_.status_, other->_impl_.status_);
}

::google::protobuf::Metadata HealthCheckResponse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[8]);
}
// ===================================================================

class TickData::_Internal {
 public:
};

TickData::TickData(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.TickData)
}
inline PROTOBUF_NDEBUG_INLINE TickData::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : bids_{visibility, arena, from.bids_},
        asks_{visibility, arena, from.asks_},
        symbol_(arena, from.symbol_),
        exchange_(arena, from.exchange_),
        trade_flag_(arena, from.trade_flag_),
        _cached_size_{0} {}

TickData::TickData(
    ::google::protobuf::Arena* arena,
    const TickData& from)
    : ::google::protobuf::Message(arena) {
  TickData* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, timestamp_),
           offsetof(Impl_, sequence_) -
               offsetof(Impl_, timestamp_) +
               sizeof(Impl_::sequence_));

  // @@protoc_insertion_point(copy_constructor:financial_data.TickData)
}
inline PROTOBUF_NDEBUG_INLINE TickData::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : bids_{visibility, arena},
        asks_{visibility, arena},
        symbol_(arena),
        exchange_(arena),
        trade_flag_(arena),
        _cached_size_{0} {}

inline void TickData::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_),
           0,
           offsetof(Impl_, sequence_) -
               offsetof(Impl_, timestamp_) +
               sizeof(Impl_::sequence_));
}
TickData::~TickData() {
  // @@protoc_insertion_point(destructor:financial_data.TickData)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void TickData::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.symbol_.Destroy();
  _impl_.exchange_.Destroy();
  _impl_.trade_flag_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void TickData::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.TickData)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.bids_.Clear();
  _impl_.asks_.Clear();
  _impl_.symbol_.ClearToEmpty();
  _impl_.exchange_.ClearToEmpty();
  _impl_.trade_flag_.ClearToEmpty();
  ::memset(&_impl_.timestamp_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.sequence_) -
      reinterpret_cast<char*>(&_impl_.timestamp_)) + sizeof(_impl_.sequence_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* TickData::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<4, 11, 2, 64, 2> TickData::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    11, 120,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294965248,  // skipmap
    offsetof(decltype(_table_), field_entries),
    11,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_TickData_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // int64 timestamp = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(TickData, _impl_.timestamp_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.timestamp_)}},
    // string symbol = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.symbol_)}},
    // string exchange = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.exchange_)}},
    // double last_price = 4;
    {::_pbi::TcParser::FastF64S1,
     {33, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.last_price_)}},
    // int64 volume = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(TickData, _impl_.volume_), 63>(),
     {40, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.volume_)}},
    // double turnover = 6;
    {::_pbi::TcParser::FastF64S1,
     {49, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.turnover_)}},
    // int64 open_interest = 7;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(TickData, _impl_.open_interest_), 63>(),
     {56, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.open_interest_)}},
    // repeated .financial_data.PriceLevel bids = 8;
    {::_pbi::TcParser::FastMtR1,
     {66, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.bids_)}},
    // repeated .financial_data.PriceLevel asks = 9;
    {::_pbi::TcParser::FastMtR1,
     {74, 63, 1, PROTOBUF_FIELD_OFFSET(TickData, _impl_.asks_)}},
    // uint32 sequence = 10;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(TickData, _impl_.sequence_), 63>(),
     {80, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.sequence_)}},
    // string trade_flag = 11;
    {::_pbi::TcParser::FastUS1,
     {90, 63, 0, PROTOBUF_FIELD_OFFSET(TickData, _impl_.trade_flag_)}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 timestamp = 1;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.timestamp_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // string symbol = 2;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.symbol_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string exchange = 3;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // double last_price = 4;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.last_price_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // int64 volume = 5;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.volume_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // double turnover = 6;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.turnover_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // int64 open_interest = 7;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.open_interest_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // repeated .financial_data.PriceLevel bids = 8;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.bids_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated .financial_data.PriceLevel asks = 9;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.asks_), 0, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // uint32 sequence = 10;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.sequence_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt32)},
    // string trade_flag = 11;
    {PROTOBUF_FIELD_OFFSET(TickData, _impl_.trade_flag_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }}, {{
    {::_pbi::TcParser::GetTable<::financial_data::PriceLevel>()},
    {::_pbi::TcParser::GetTable<::financial_data::PriceLevel>()},
  }}, {{
    "\27\0\6\10\0\0\0\0\0\0\0\12\0\0\0\0"
    "financial_data.TickData"
    "symbol"
    "exchange"
    "trade_flag"
  }},
};

::uint8_t* TickData::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.TickData)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // int64 timestamp = 1;
  if (this->_internal_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<1>(
            stream, this->_internal_timestamp(), target);
  }

  // string symbol = 2;
  if (!this->_internal_symbol().empty()) {
    const std::string& _s = this->_internal_symbol();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.TickData.symbol");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // string exchange = 3;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.TickData.exchange");
    target = stream->WriteStringMaybeAliased(3, _s, target);
  }

  // double last_price = 4;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_last_price = this->_internal_last_price();
  ::uint64_t raw_last_price;
  memcpy(&raw_last_price, &tmp_last_price, sizeof(tmp_last_price));
  if (raw_last_price != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        4, this->_internal_last_price(), target);
  }

  // int64 volume = 5;
  if (this->_internal_volume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<5>(
            stream, this->_internal_volume(), target);
  }

  // double turnover = 6;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = this->_internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        6, this->_internal_turnover(), target);
  }

  // int64 open_interest = 7;
  if (this->_internal_open_interest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<7>(
            stream, this->_internal_open_interest(), target);
  }

  // repeated .financial_data.PriceLevel bids = 8;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_bids_size()); i < n; i++) {
    const auto& repfield = this->_internal_bids().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(8, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .financial_data.PriceLevel asks = 9;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_asks_size()); i < n; i++) {
    const auto& repfield = this->_internal_asks().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(9, repfield, repfield.GetCachedSize(), target, stream);
  }

  // uint32 sequence = 10;
  if (this->_internal_sequence() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
        10, this->_internal_sequence(), target);
  }

  // string trade_flag = 11;
  if (!this->_internal_trade_flag().empty()) {
    const std::string& _s = this->_internal_trade_flag();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.TickData.trade_flag");
    target = stream->WriteStringMaybeAliased(11, _s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.TickData)
  return target;
}

::size_t TickData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.TickData)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .financial_data.PriceLevel bids = 8;
  total_size += 1UL * this->_internal_bids_size();
  for (const auto& msg : this->_internal_bids()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // repeated .financial_data.PriceLevel asks = 9;
  total_size += 1UL * this->_internal_asks_size();
  for (const auto& msg : this->_internal_asks()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // string symbol = 2;
  if (!this->_internal_symbol().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_symbol());
  }

  // string exchange = 3;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // string trade_flag = 11;
  if (!this->_internal_trade_flag().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_trade_flag());
  }

  // int64 timestamp = 1;
  if (this->_internal_timestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_timestamp());
  }

  // double last_price = 4;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_last_price = this->_internal_last_price();
  ::uint64_t raw_last_price;
  memcpy(&raw_last_price, &tmp_last_price, sizeof(tmp_last_price));
  if (raw_last_price != 0) {
    total_size += 9;
  }

  // int64 volume = 5;
  if (this->_internal_volume() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_volume());
  }

  // double turnover = 6;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = this->_internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    total_size += 9;
  }

  // int64 open_interest = 7;
  if (this->_internal_open_interest() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_open_interest());
  }

  // uint32 sequence = 10;
  if (this->_internal_sequence() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
        this->_internal_sequence());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData TickData::_class_data_ = {
    TickData::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* TickData::GetClassData() const {
  return &_class_data_;
}

void TickData::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<TickData*>(&to_msg);
  auto& from = static_cast<const TickData&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.TickData)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_bids()->MergeFrom(
      from._internal_bids());
  _this->_internal_mutable_asks()->MergeFrom(
      from._internal_asks());
  if (!from._internal_symbol().empty()) {
    _this->_internal_set_symbol(from._internal_symbol());
  }
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (!from._internal_trade_flag().empty()) {
    _this->_internal_set_trade_flag(from._internal_trade_flag());
  }
  if (from._internal_timestamp() != 0) {
    _this->_internal_set_timestamp(from._internal_timestamp());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_last_price = from._internal_last_price();
  ::uint64_t raw_last_price;
  memcpy(&raw_last_price, &tmp_last_price, sizeof(tmp_last_price));
  if (raw_last_price != 0) {
    _this->_internal_set_last_price(from._internal_last_price());
  }
  if (from._internal_volume() != 0) {
    _this->_internal_set_volume(from._internal_volume());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = from._internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    _this->_internal_set_turnover(from._internal_turnover());
  }
  if (from._internal_open_interest() != 0) {
    _this->_internal_set_open_interest(from._internal_open_interest());
  }
  if (from._internal_sequence() != 0) {
    _this->_internal_set_sequence(from._internal_sequence());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void TickData::CopyFrom(const TickData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.TickData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool TickData::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* TickData::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void TickData::InternalSwap(TickData* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.bids_.InternalSwap(&other->_impl_.bids_);
  _impl_.asks_.InternalSwap(&other->_impl_.asks_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.symbol_, &other->_impl_.symbol_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.trade_flag_, &other->_impl_.trade_flag_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TickData, _impl_.sequence_)
      + sizeof(TickData::_impl_.sequence_)
      - PROTOBUF_FIELD_OFFSET(TickData, _impl_.timestamp_)>(
          reinterpret_cast<char*>(&_impl_.timestamp_),
          reinterpret_cast<char*>(&other->_impl_.timestamp_));
}

::google::protobuf::Metadata TickData::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[9]);
}
// ===================================================================

class KlineData::_Internal {
 public:
};

KlineData::KlineData(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.KlineData)
}
inline PROTOBUF_NDEBUG_INLINE KlineData::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : symbol_(arena, from.symbol_),
        exchange_(arena, from.exchange_),
        period_(arena, from.period_),
        _cached_size_{0} {}

KlineData::KlineData(
    ::google::protobuf::Arena* arena,
    const KlineData& from)
    : ::google::protobuf::Message(arena) {
  KlineData* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, timestamp_),
           offsetof(Impl_, open_interest_) -
               offsetof(Impl_, timestamp_) +
               sizeof(Impl_::open_interest_));

  // @@protoc_insertion_point(copy_constructor:financial_data.KlineData)
}
inline PROTOBUF_NDEBUG_INLINE KlineData::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : symbol_(arena),
        exchange_(arena),
        period_(arena),
        _cached_size_{0} {}

inline void KlineData::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_),
           0,
           offsetof(Impl_, open_interest_) -
               offsetof(Impl_, timestamp_) +
               sizeof(Impl_::open_interest_));
}
KlineData::~KlineData() {
  // @@protoc_insertion_point(destructor:financial_data.KlineData)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void KlineData::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.symbol_.Destroy();
  _impl_.exchange_.Destroy();
  _impl_.period_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void KlineData::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.KlineData)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.symbol_.ClearToEmpty();
  _impl_.exchange_.ClearToEmpty();
  _impl_.period_.ClearToEmpty();
  ::memset(&_impl_.timestamp_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.open_interest_) -
      reinterpret_cast<char*>(&_impl_.timestamp_)) + sizeof(_impl_.open_interest_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* KlineData::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<4, 11, 0, 61, 2> KlineData::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    11, 120,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294965248,  // skipmap
    offsetof(decltype(_table_), field_entries),
    11,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_KlineData_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // string symbol = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.symbol_)}},
    // string exchange = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.exchange_)}},
    // string period = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.period_)}},
    // int64 timestamp = 4;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(KlineData, _impl_.timestamp_), 63>(),
     {32, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.timestamp_)}},
    // double open = 5;
    {::_pbi::TcParser::FastF64S1,
     {41, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.open_)}},
    // double high = 6;
    {::_pbi::TcParser::FastF64S1,
     {49, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.high_)}},
    // double low = 7;
    {::_pbi::TcParser::FastF64S1,
     {57, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.low_)}},
    // double close = 8;
    {::_pbi::TcParser::FastF64S1,
     {65, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.close_)}},
    // int64 volume = 9;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(KlineData, _impl_.volume_), 63>(),
     {72, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.volume_)}},
    // double turnover = 10;
    {::_pbi::TcParser::FastF64S1,
     {81, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.turnover_)}},
    // int64 open_interest = 11;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(KlineData, _impl_.open_interest_), 63>(),
     {88, 63, 0, PROTOBUF_FIELD_OFFSET(KlineData, _impl_.open_interest_)}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // string symbol = 1;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.symbol_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string exchange = 2;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string period = 3;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.period_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int64 timestamp = 4;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.timestamp_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // double open = 5;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.open_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // double high = 6;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.high_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // double low = 7;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.low_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // double close = 8;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.close_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // int64 volume = 9;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.volume_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // double turnover = 10;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.turnover_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // int64 open_interest = 11;
    {PROTOBUF_FIELD_OFFSET(KlineData, _impl_.open_interest_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
  }},
  // no aux_entries
  {{
    "\30\6\10\6\0\0\0\0\0\0\0\0\0\0\0\0"
    "financial_data.KlineData"
    "symbol"
    "exchange"
    "period"
  }},
};

::uint8_t* KlineData::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.KlineData)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // string symbol = 1;
  if (!this->_internal_symbol().empty()) {
    const std::string& _s = this->_internal_symbol();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.KlineData.symbol");
    target = stream->WriteStringMaybeAliased(1, _s, target);
  }

  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.KlineData.exchange");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // string period = 3;
  if (!this->_internal_period().empty()) {
    const std::string& _s = this->_internal_period();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.KlineData.period");
    target = stream->WriteStringMaybeAliased(3, _s, target);
  }

  // int64 timestamp = 4;
  if (this->_internal_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<4>(
            stream, this->_internal_timestamp(), target);
  }

  // double open = 5;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_open = this->_internal_open();
  ::uint64_t raw_open;
  memcpy(&raw_open, &tmp_open, sizeof(tmp_open));
  if (raw_open != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        5, this->_internal_open(), target);
  }

  // double high = 6;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_high = this->_internal_high();
  ::uint64_t raw_high;
  memcpy(&raw_high, &tmp_high, sizeof(tmp_high));
  if (raw_high != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        6, this->_internal_high(), target);
  }

  // double low = 7;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_low = this->_internal_low();
  ::uint64_t raw_low;
  memcpy(&raw_low, &tmp_low, sizeof(tmp_low));
  if (raw_low != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        7, this->_internal_low(), target);
  }

  // double close = 8;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_close = this->_internal_close();
  ::uint64_t raw_close;
  memcpy(&raw_close, &tmp_close, sizeof(tmp_close));
  if (raw_close != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        8, this->_internal_close(), target);
  }

  // int64 volume = 9;
  if (this->_internal_volume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<9>(
            stream, this->_internal_volume(), target);
  }

  // double turnover = 10;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = this->_internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        10, this->_internal_turnover(), target);
  }

  // int64 open_interest = 11;
  if (this->_internal_open_interest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<11>(
            stream, this->_internal_open_interest(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.KlineData)
  return target;
}

::size_t KlineData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.KlineData)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string symbol = 1;
  if (!this->_internal_symbol().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_symbol());
  }

  // string exchange = 2;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // string period = 3;
  if (!this->_internal_period().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_period());
  }

  // int64 timestamp = 4;
  if (this->_internal_timestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_timestamp());
  }

  // double open = 5;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_open = this->_internal_open();
  ::uint64_t raw_open;
  memcpy(&raw_open, &tmp_open, sizeof(tmp_open));
  if (raw_open != 0) {
    total_size += 9;
  }

  // double high = 6;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_high = this->_internal_high();
  ::uint64_t raw_high;
  memcpy(&raw_high, &tmp_high, sizeof(tmp_high));
  if (raw_high != 0) {
    total_size += 9;
  }

  // double low = 7;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_low = this->_internal_low();
  ::uint64_t raw_low;
  memcpy(&raw_low, &tmp_low, sizeof(tmp_low));
  if (raw_low != 0) {
    total_size += 9;
  }

  // double close = 8;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_close = this->_internal_close();
  ::uint64_t raw_close;
  memcpy(&raw_close, &tmp_close, sizeof(tmp_close));
  if (raw_close != 0) {
    total_size += 9;
  }

  // int64 volume = 9;
  if (this->_internal_volume() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_volume());
  }

  // double turnover = 10;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = this->_internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    total_size += 9;
  }

  // int64 open_interest = 11;
  if (this->_internal_open_interest() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_open_interest());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData KlineData::_class_data_ = {
    KlineData::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* KlineData::GetClassData() const {
  return &_class_data_;
}

void KlineData::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<KlineData*>(&to_msg);
  auto& from = static_cast<const KlineData&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.KlineData)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_symbol().empty()) {
    _this->_internal_set_symbol(from._internal_symbol());
  }
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (!from._internal_period().empty()) {
    _this->_internal_set_period(from._internal_period());
  }
  if (from._internal_timestamp() != 0) {
    _this->_internal_set_timestamp(from._internal_timestamp());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_open = from._internal_open();
  ::uint64_t raw_open;
  memcpy(&raw_open, &tmp_open, sizeof(tmp_open));
  if (raw_open != 0) {
    _this->_internal_set_open(from._internal_open());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_high = from._internal_high();
  ::uint64_t raw_high;
  memcpy(&raw_high, &tmp_high, sizeof(tmp_high));
  if (raw_high != 0) {
    _this->_internal_set_high(from._internal_high());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_low = from._internal_low();
  ::uint64_t raw_low;
  memcpy(&raw_low, &tmp_low, sizeof(tmp_low));
  if (raw_low != 0) {
    _this->_internal_set_low(from._internal_low());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_close = from._internal_close();
  ::uint64_t raw_close;
  memcpy(&raw_close, &tmp_close, sizeof(tmp_close));
  if (raw_close != 0) {
    _this->_internal_set_close(from._internal_close());
  }
  if (from._internal_volume() != 0) {
    _this->_internal_set_volume(from._internal_volume());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_turnover = from._internal_turnover();
  ::uint64_t raw_turnover;
  memcpy(&raw_turnover, &tmp_turnover, sizeof(tmp_turnover));
  if (raw_turnover != 0) {
    _this->_internal_set_turnover(from._internal_turnover());
  }
  if (from._internal_open_interest() != 0) {
    _this->_internal_set_open_interest(from._internal_open_interest());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void KlineData::CopyFrom(const KlineData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.KlineData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool KlineData::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* KlineData::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void KlineData::InternalSwap(KlineData* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.symbol_, &other->_impl_.symbol_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.period_, &other->_impl_.period_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(KlineData, _impl_.open_interest_)
      + sizeof(KlineData::_impl_.open_interest_)
      - PROTOBUF_FIELD_OFFSET(KlineData, _impl_.timestamp_)>(
          reinterpret_cast<char*>(&_impl_.timestamp_),
          reinterpret_cast<char*>(&other->_impl_.timestamp_));
}

::google::protobuf::Metadata KlineData::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[10]);
}
// ===================================================================

class Level2Data::_Internal {
 public:
};

Level2Data::Level2Data(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.Level2Data)
}
inline PROTOBUF_NDEBUG_INLINE Level2Data::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : bids_{visibility, arena, from.bids_},
        asks_{visibility, arena, from.asks_},
        symbol_(arena, from.symbol_),
        exchange_(arena, from.exchange_),
        _cached_size_{0} {}

Level2Data::Level2Data(
    ::google::protobuf::Arena* arena,
    const Level2Data& from)
    : ::google::protobuf::Message(arena) {
  Level2Data* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, timestamp_),
           offsetof(Impl_, sequence_) -
               offsetof(Impl_, timestamp_) +
               sizeof(Impl_::sequence_));

  // @@protoc_insertion_point(copy_constructor:financial_data.Level2Data)
}
inline PROTOBUF_NDEBUG_INLINE Level2Data::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : bids_{visibility, arena},
        asks_{visibility, arena},
        symbol_(arena),
        exchange_(arena),
        _cached_size_{0} {}

inline void Level2Data::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, timestamp_),
           0,
           offsetof(Impl_, sequence_) -
               offsetof(Impl_, timestamp_) +
               sizeof(Impl_::sequence_));
}
Level2Data::~Level2Data() {
  // @@protoc_insertion_point(destructor:financial_data.Level2Data)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Level2Data::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.symbol_.Destroy();
  _impl_.exchange_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void Level2Data::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.Level2Data)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.bids_.Clear();
  _impl_.asks_.Clear();
  _impl_.symbol_.ClearToEmpty();
  _impl_.exchange_.ClearToEmpty();
  ::memset(&_impl_.timestamp_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.sequence_) -
      reinterpret_cast<char*>(&_impl_.timestamp_)) + sizeof(_impl_.sequence_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* Level2Data::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 6, 2, 48, 2> Level2Data::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    6, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967232,  // skipmap
    offsetof(decltype(_table_), field_entries),
    6,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_Level2Data_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // int64 timestamp = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(Level2Data, _impl_.timestamp_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.timestamp_)}},
    // string symbol = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.symbol_)}},
    // string exchange = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.exchange_)}},
    // repeated .financial_data.PriceLevel bids = 4;
    {::_pbi::TcParser::FastMtR1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.bids_)}},
    // repeated .financial_data.PriceLevel asks = 5;
    {::_pbi::TcParser::FastMtR1,
     {42, 63, 1, PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.asks_)}},
    // uint32 sequence = 6;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(Level2Data, _impl_.sequence_), 63>(),
     {48, 63, 0, PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.sequence_)}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 timestamp = 1;
    {PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.timestamp_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // string symbol = 2;
    {PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.symbol_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string exchange = 3;
    {PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.exchange_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // repeated .financial_data.PriceLevel bids = 4;
    {PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.bids_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated .financial_data.PriceLevel asks = 5;
    {PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.asks_), 0, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // uint32 sequence = 6;
    {PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.sequence_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt32)},
  }}, {{
    {::_pbi::TcParser::GetTable<::financial_data::PriceLevel>()},
    {::_pbi::TcParser::GetTable<::financial_data::PriceLevel>()},
  }}, {{
    "\31\0\6\10\0\0\0\0"
    "financial_data.Level2Data"
    "symbol"
    "exchange"
  }},
};

::uint8_t* Level2Data::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.Level2Data)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // int64 timestamp = 1;
  if (this->_internal_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<1>(
            stream, this->_internal_timestamp(), target);
  }

  // string symbol = 2;
  if (!this->_internal_symbol().empty()) {
    const std::string& _s = this->_internal_symbol();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.Level2Data.symbol");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // string exchange = 3;
  if (!this->_internal_exchange().empty()) {
    const std::string& _s = this->_internal_exchange();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.Level2Data.exchange");
    target = stream->WriteStringMaybeAliased(3, _s, target);
  }

  // repeated .financial_data.PriceLevel bids = 4;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_bids_size()); i < n; i++) {
    const auto& repfield = this->_internal_bids().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(4, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .financial_data.PriceLevel asks = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_asks_size()); i < n; i++) {
    const auto& repfield = this->_internal_asks().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // uint32 sequence = 6;
  if (this->_internal_sequence() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
        6, this->_internal_sequence(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.Level2Data)
  return target;
}

::size_t Level2Data::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.Level2Data)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .financial_data.PriceLevel bids = 4;
  total_size += 1UL * this->_internal_bids_size();
  for (const auto& msg : this->_internal_bids()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // repeated .financial_data.PriceLevel asks = 5;
  total_size += 1UL * this->_internal_asks_size();
  for (const auto& msg : this->_internal_asks()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // string symbol = 2;
  if (!this->_internal_symbol().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_symbol());
  }

  // string exchange = 3;
  if (!this->_internal_exchange().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_exchange());
  }

  // int64 timestamp = 1;
  if (this->_internal_timestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_timestamp());
  }

  // uint32 sequence = 6;
  if (this->_internal_sequence() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
        this->_internal_sequence());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData Level2Data::_class_data_ = {
    Level2Data::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* Level2Data::GetClassData() const {
  return &_class_data_;
}

void Level2Data::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<Level2Data*>(&to_msg);
  auto& from = static_cast<const Level2Data&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.Level2Data)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_bids()->MergeFrom(
      from._internal_bids());
  _this->_internal_mutable_asks()->MergeFrom(
      from._internal_asks());
  if (!from._internal_symbol().empty()) {
    _this->_internal_set_symbol(from._internal_symbol());
  }
  if (!from._internal_exchange().empty()) {
    _this->_internal_set_exchange(from._internal_exchange());
  }
  if (from._internal_timestamp() != 0) {
    _this->_internal_set_timestamp(from._internal_timestamp());
  }
  if (from._internal_sequence() != 0) {
    _this->_internal_set_sequence(from._internal_sequence());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Level2Data::CopyFrom(const Level2Data& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.Level2Data)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool Level2Data::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* Level2Data::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void Level2Data::InternalSwap(Level2Data* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.bids_.InternalSwap(&other->_impl_.bids_);
  _impl_.asks_.InternalSwap(&other->_impl_.asks_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.symbol_, &other->_impl_.symbol_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.exchange_, &other->_impl_.exchange_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.sequence_)
      + sizeof(Level2Data::_impl_.sequence_)
      - PROTOBUF_FIELD_OFFSET(Level2Data, _impl_.timestamp_)>(
          reinterpret_cast<char*>(&_impl_.timestamp_),
          reinterpret_cast<char*>(&other->_impl_.timestamp_));
}

::google::protobuf::Metadata Level2Data::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[11]);
}
// ===================================================================

class PriceLevel::_Internal {
 public:
};

PriceLevel::PriceLevel(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.PriceLevel)
}
PriceLevel::PriceLevel(
    ::google::protobuf::Arena* arena, const PriceLevel& from)
    : PriceLevel(arena) {
  MergeFrom(from);
}
inline PROTOBUF_NDEBUG_INLINE PriceLevel::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void PriceLevel::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, price_),
           0,
           offsetof(Impl_, order_count_) -
               offsetof(Impl_, price_) +
               sizeof(Impl_::order_count_));
}
PriceLevel::~PriceLevel() {
  // @@protoc_insertion_point(destructor:financial_data.PriceLevel)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void PriceLevel::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void PriceLevel::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.PriceLevel)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.price_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.order_count_) -
      reinterpret_cast<char*>(&_impl_.price_)) + sizeof(_impl_.order_count_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* PriceLevel::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 0, 0, 2> PriceLevel::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_PriceLevel_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // double price = 1;
    {::_pbi::TcParser::FastF64S1,
     {9, 63, 0, PROTOBUF_FIELD_OFFSET(PriceLevel, _impl_.price_)}},
    // int32 volume = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(PriceLevel, _impl_.volume_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(PriceLevel, _impl_.volume_)}},
    // int32 order_count = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(PriceLevel, _impl_.order_count_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(PriceLevel, _impl_.order_count_)}},
  }}, {{
    65535, 65535
  }}, {{
    // double price = 1;
    {PROTOBUF_FIELD_OFFSET(PriceLevel, _impl_.price_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // int32 volume = 2;
    {PROTOBUF_FIELD_OFFSET(PriceLevel, _impl_.volume_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // int32 order_count = 3;
    {PROTOBUF_FIELD_OFFSET(PriceLevel, _impl_.order_count_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
  }},
};

::uint8_t* PriceLevel::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.PriceLevel)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // double price = 1;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_price = this->_internal_price();
  ::uint64_t raw_price;
  memcpy(&raw_price, &tmp_price, sizeof(tmp_price));
  if (raw_price != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        1, this->_internal_price(), target);
  }

  // int32 volume = 2;
  if (this->_internal_volume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<2>(
            stream, this->_internal_volume(), target);
  }

  // int32 order_count = 3;
  if (this->_internal_order_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<3>(
            stream, this->_internal_order_count(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.PriceLevel)
  return target;
}

::size_t PriceLevel::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.PriceLevel)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double price = 1;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_price = this->_internal_price();
  ::uint64_t raw_price;
  memcpy(&raw_price, &tmp_price, sizeof(tmp_price));
  if (raw_price != 0) {
    total_size += 9;
  }

  // int32 volume = 2;
  if (this->_internal_volume() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_volume());
  }

  // int32 order_count = 3;
  if (this->_internal_order_count() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_order_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData PriceLevel::_class_data_ = {
    PriceLevel::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* PriceLevel::GetClassData() const {
  return &_class_data_;
}

void PriceLevel::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<PriceLevel*>(&to_msg);
  auto& from = static_cast<const PriceLevel&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.PriceLevel)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_price = from._internal_price();
  ::uint64_t raw_price;
  memcpy(&raw_price, &tmp_price, sizeof(tmp_price));
  if (raw_price != 0) {
    _this->_internal_set_price(from._internal_price());
  }
  if (from._internal_volume() != 0) {
    _this->_internal_set_volume(from._internal_volume());
  }
  if (from._internal_order_count() != 0) {
    _this->_internal_set_order_count(from._internal_order_count());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void PriceLevel::CopyFrom(const PriceLevel& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.PriceLevel)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool PriceLevel::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* PriceLevel::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void PriceLevel::InternalSwap(PriceLevel* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PriceLevel, _impl_.order_count_)
      + sizeof(PriceLevel::_impl_.order_count_)
      - PROTOBUF_FIELD_OFFSET(PriceLevel, _impl_.price_)>(
          reinterpret_cast<char*>(&_impl_.price_),
          reinterpret_cast<char*>(&other->_impl_.price_));
}

::google::protobuf::Metadata PriceLevel::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[12]);
}
// ===================================================================

class ResponseMetadata::_Internal {
 public:
};

ResponseMetadata::ResponseMetadata(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:financial_data.ResponseMetadata)
}
inline PROTOBUF_NDEBUG_INLINE ResponseMetadata::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from)
      : server_id_(arena, from.server_id_),
        _cached_size_{0} {}

ResponseMetadata::ResponseMetadata(
    ::google::protobuf::Arena* arena,
    const ResponseMetadata& from)
    : ::google::protobuf::Message(arena) {
  ResponseMetadata* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, server_timestamp_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, server_timestamp_),
           offsetof(Impl_, sequence_number_) -
               offsetof(Impl_, server_timestamp_) +
               sizeof(Impl_::sequence_number_));

  // @@protoc_insertion_point(copy_constructor:financial_data.ResponseMetadata)
}
inline PROTOBUF_NDEBUG_INLINE ResponseMetadata::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : server_id_(arena),
        _cached_size_{0} {}

inline void ResponseMetadata::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, server_timestamp_),
           0,
           offsetof(Impl_, sequence_number_) -
               offsetof(Impl_, server_timestamp_) +
               sizeof(Impl_::sequence_number_));
}
ResponseMetadata::~ResponseMetadata() {
  // @@protoc_insertion_point(destructor:financial_data.ResponseMetadata)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void ResponseMetadata::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.server_id_.Destroy();
  _impl_.~Impl_();
}

PROTOBUF_NOINLINE void ResponseMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:financial_data.ResponseMetadata)
  PROTOBUF_TSAN_WRITE(&_impl_._tsan_detect_race);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.server_id_.ClearToEmpty();
  ::memset(&_impl_.server_timestamp_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.sequence_number_) -
      reinterpret_cast<char*>(&_impl_.server_timestamp_)) + sizeof(_impl_.sequence_number_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* ResponseMetadata::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 0, 49, 2> ResponseMetadata::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_ResponseMetadata_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // double processing_latency_us = 4;
    {::_pbi::TcParser::FastF64S1,
     {33, 63, 0, PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.processing_latency_us_)}},
    // int64 server_timestamp = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ResponseMetadata, _impl_.server_timestamp_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.server_timestamp_)}},
    // string server_id = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.server_id_)}},
    // int32 sequence_number = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ResponseMetadata, _impl_.sequence_number_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.sequence_number_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 server_timestamp = 1;
    {PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.server_timestamp_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // string server_id = 2;
    {PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.server_id_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int32 sequence_number = 3;
    {PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.sequence_number_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // double processing_latency_us = 4;
    {PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.processing_latency_us_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
  }},
  // no aux_entries
  {{
    "\37\0\11\0\0\0\0\0"
    "financial_data.ResponseMetadata"
    "server_id"
  }},
};

::uint8_t* ResponseMetadata::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:financial_data.ResponseMetadata)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // int64 server_timestamp = 1;
  if (this->_internal_server_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt64ToArrayWithField<1>(
            stream, this->_internal_server_timestamp(), target);
  }

  // string server_id = 2;
  if (!this->_internal_server_id().empty()) {
    const std::string& _s = this->_internal_server_id();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "financial_data.ResponseMetadata.server_id");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // int32 sequence_number = 3;
  if (this->_internal_sequence_number() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<3>(
            stream, this->_internal_sequence_number(), target);
  }

  // double processing_latency_us = 4;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_processing_latency_us = this->_internal_processing_latency_us();
  ::uint64_t raw_processing_latency_us;
  memcpy(&raw_processing_latency_us, &tmp_processing_latency_us, sizeof(tmp_processing_latency_us));
  if (raw_processing_latency_us != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(
        4, this->_internal_processing_latency_us(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:financial_data.ResponseMetadata)
  return target;
}

::size_t ResponseMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:financial_data.ResponseMetadata)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string server_id = 2;
  if (!this->_internal_server_id().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_server_id());
  }

  // int64 server_timestamp = 1;
  if (this->_internal_server_timestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
        this->_internal_server_timestamp());
  }

  // double processing_latency_us = 4;
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_processing_latency_us = this->_internal_processing_latency_us();
  ::uint64_t raw_processing_latency_us;
  memcpy(&raw_processing_latency_us, &tmp_processing_latency_us, sizeof(tmp_processing_latency_us));
  if (raw_processing_latency_us != 0) {
    total_size += 9;
  }

  // int32 sequence_number = 3;
  if (this->_internal_sequence_number() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_sequence_number());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData ResponseMetadata::_class_data_ = {
    ResponseMetadata::MergeImpl,
    nullptr,  // OnDemandRegisterArenaDtor
};
const ::google::protobuf::Message::ClassData* ResponseMetadata::GetClassData() const {
  return &_class_data_;
}

void ResponseMetadata::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<ResponseMetadata*>(&to_msg);
  auto& from = static_cast<const ResponseMetadata&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:financial_data.ResponseMetadata)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_server_id().empty()) {
    _this->_internal_set_server_id(from._internal_server_id());
  }
  if (from._internal_server_timestamp() != 0) {
    _this->_internal_set_server_timestamp(from._internal_server_timestamp());
  }
  static_assert(sizeof(::uint64_t) == sizeof(double),
                "Code assumes ::uint64_t and double are the same size.");
  double tmp_processing_latency_us = from._internal_processing_latency_us();
  ::uint64_t raw_processing_latency_us;
  memcpy(&raw_processing_latency_us, &tmp_processing_latency_us, sizeof(tmp_processing_latency_us));
  if (raw_processing_latency_us != 0) {
    _this->_internal_set_processing_latency_us(from._internal_processing_latency_us());
  }
  if (from._internal_sequence_number() != 0) {
    _this->_internal_set_sequence_number(from._internal_sequence_number());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ResponseMetadata::CopyFrom(const ResponseMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:financial_data.ResponseMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool ResponseMetadata::IsInitialized() const {
  return true;
}

::_pbi::CachedSize* ResponseMetadata::AccessCachedSize() const {
  return &_impl_._cached_size_;
}
void ResponseMetadata::InternalSwap(ResponseMetadata* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.server_id_, &other->_impl_.server_id_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.sequence_number_)
      + sizeof(ResponseMetadata::_impl_.sequence_number_)
      - PROTOBUF_FIELD_OFFSET(ResponseMetadata, _impl_.server_timestamp_)>(
          reinterpret_cast<char*>(&_impl_.server_timestamp_),
          reinterpret_cast<char*>(&other->_impl_.server_timestamp_));
}

::google::protobuf::Metadata ResponseMetadata::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_market_5fdata_5fservice_2eproto_getter, &descriptor_table_market_5fdata_5fservice_2eproto_once,
      file_level_metadata_market_5fdata_5fservice_2eproto[13]);
}
// @@protoc_insertion_point(namespace_scope)
}  // namespace financial_data
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google
// @@protoc_insertion_point(global_scope)
#include "google/protobuf/port_undef.inc"
