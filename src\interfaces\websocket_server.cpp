#include "websocket_server.h"
#include <fstream>
#include <chrono>

namespace financial_data {
namespace interfaces {

WebSocketServer::WebSocketServer(const WebSocketConfig& config) 
    : config_(config), logger_(spdlog::get("websocket_server")) {
    if (!logger_) {
        logger_ = spdlog::default_logger();
    }
    
    logger_->info("WebSocketServer created with config: host={}, port={}, max_connections={}",
                 config_.host, config_.port, config_.max_connections);
}

WebSocketServer::~WebSocketServer() {
    Stop();
}

bool WebSocketServer::Initialize() {
    try {
        // 创建WebSocket处理器
        handler_ = std::make_unique<WebSocketHandler>(config_);
        
        if (!handler_->Initialize()) {
            logger_->error("Failed to initialize WebSocket handler");
            return false;
        }
        
        logger_->info("WebSocketServer initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize WebSocketServer: {}", e.what());
        return false;
    }
}

bool WebSocketServer::Start() {
    if (running_.load()) {
        logger_->warn("WebSocketServer is already running");
        return false;
    }
    
    if (!handler_) {
        logger_->error("WebSocket handler not initialized");
        return false;
    }
    
    try {
        running_ = true;
        
        // 注册数据总线回调
        if (data_bus_) {
            RegisterDataBusCallback();
        }
        
        // 启动服务器线程
        server_thread_ = std::thread(&WebSocketServer::ServerLoop, this);
        
        logger_->info("WebSocketServer started successfully");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to start WebSocketServer: {}", e.what());
        running_ = false;
        return false;
    }
}

void WebSocketServer::Stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    
    // 取消注册数据总线回调
    if (data_bus_) {
        UnregisterDataBusCallback();
    }
    
    // 停止处理器
    if (handler_) {
        handler_->Stop();
    }
    
    // 等待服务器线程结束
    if (server_thread_.joinable()) {
        server_thread_.join();
    }
    
    logger_->info("WebSocketServer stopped");
}

void WebSocketServer::SetDataBus(std::shared_ptr<databus::DataBus> data_bus) {
    data_bus_ = data_bus;
    
    if (running_.load() && data_bus_) {
        RegisterDataBusCallback();
    }
}

std::shared_ptr<SubscriptionManager> WebSocketServer::GetSubscriptionManager() const {
    return handler_ ? handler_->GetSubscriptionManager() : nullptr;
}

std::shared_ptr<HeartbeatManager> WebSocketServer::GetHeartbeatManager() const {
    return handler_ ? handler_->GetHeartbeatManager() : nullptr;
}

size_t WebSocketServer::GetConnectionCount() const {
    return handler_ ? handler_->GetConnectionCount() : 0;
}

size_t WebSocketServer::GetActiveConnectionCount() const {
    // 获取活跃连接数统计
    return handler_ ? handler_->GetActiveConnectionCount() : 0;
}

void WebSocketServer::BroadcastMessage(const std::string& message) {
    if (handler_) {
        handler_->BroadcastMessage(message);
    }
}

bool WebSocketServer::SendMessageToClient(const std::string& client_id, const std::string& message) {
    return handler_ ? handler_->SendMessageToClient(client_id, message) : false;
}

bool WebSocketServer::DisconnectClient(const std::string& client_id, const std::string& reason) {
    return handler_ ? handler_->DisconnectClient(client_id, reason) : false;
}

bool WebSocketServer::UpdateConfig(const WebSocketConfig& config) {
    config_ = config;
    
    if (handler_) {
        return handler_->UpdateConfig(config);
    }
    
    return true;
}

WebSocketStatistics WebSocketServer::GetStatistics() const {
    return handler_ ? handler_->GetStatistics() : WebSocketStatistics{};
}

void WebSocketServer::ResetStatistics() {
    if (handler_) {
        handler_->ResetStatistics();
    }
}

std::string WebSocketServer::GetStatisticsSummary() const {
    return handler_ ? handler_->GetStatisticsSummary() : "WebSocket handler not initialized";
}

WebSocketHandler::HealthStatus WebSocketServer::GetHealthStatus() const {
    return handler_ ? handler_->GetHealthStatus() : WebSocketHandler::HealthStatus{};
}

WebSocketServer::PerformanceTestResult WebSocketServer::RunPerformanceTest(size_t num_clients, 
                                                                          size_t messages_per_client,
                                                                          uint32_t test_duration_ms) {
    PerformanceTestResult result;
    result.success = false;
    
    if (!running_.load()) {
        result.error_message = "Server is not running";
        return result;
    }
    
    try {
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // 记录初始统计
        auto initial_stats = GetStatistics();
        
        // 生成测试数据
        std::vector<StandardTick> test_ticks;
        test_ticks.reserve(messages_per_client);
        
        for (size_t i = 0; i < messages_per_client; ++i) {
            StandardTick tick;
            tick.timestamp_ns = StandardTick::GetCurrentTimestampNs();
            tick.symbol = "TEST" + std::to_string(i % 10);
            tick.exchange = "TEST";
            tick.last_price = 100.0 + (i % 100);
            tick.volume = 1000 + i;
            tick.sequence = static_cast<uint32_t>(i);
            test_ticks.push_back(tick);
        }
        
        // 模拟发送消息
        auto message_start = std::chrono::high_resolution_clock::now();
        
        for (const auto& tick : test_ticks) {
            MarketDataWrapper wrapper(tick);
            if (handler_) {
                handler_->HandleMarketData(wrapper);
            }
        }
        
        auto message_end = std::chrono::high_resolution_clock::now();
        
        // 等待消息处理完成
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        auto end_time = std::chrono::high_resolution_clock::now();
        
        // 计算结果
        result.test_duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        auto final_stats = GetStatistics();
        result.total_messages_sent = final_stats.total_messages_sent.load() - initial_stats.total_messages_sent.load();
        result.total_clients_connected = GetActiveConnectionCount();
        
        if (result.total_messages_sent > 0) {
            auto message_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                message_end - message_start).count();
            result.throughput_msg_per_sec = static_cast<double>(result.total_messages_sent) / (message_duration / 1000.0);
        }
        
        result.avg_latency_ms = final_stats.avg_message_latency_ns.load() / 1000000.0;
        result.max_latency_ms = final_stats.max_message_latency_ns.load() / 1000000.0;
        
        result.success = true;
        
        logger_->info("Performance test completed: {} messages sent to {} clients in {}ms, "
                     "throughput: {:.2f} msg/s, avg latency: {:.3f}ms",
                     result.total_messages_sent, result.total_clients_connected, 
                     result.test_duration_ms, result.throughput_msg_per_sec, result.avg_latency_ms);
        
    } catch (const std::exception& e) {
        result.error_message = "Performance test failed: " + std::string(e.what());
        logger_->error("Performance test failed: {}", e.what());
    }
    
    return result;
}

void WebSocketServer::ServerLoop() {
    logger_->info("WebSocket server loop started");
    
    try {
        if (handler_) {
            handler_->Start();  // 这会阻塞直到服务器停止
        }
    } catch (const std::exception& e) {
        logger_->error("Error in server loop: {}", e.what());
    }
    
    logger_->info("WebSocket server loop stopped");
}

bool WebSocketServer::HandleDataBusMessage(const MarketDataWrapper& data) {
    if (handler_) {
        handler_->HandleMarketData(data);
        return true;
    }
    return false;
}

void WebSocketServer::RegisterDataBusCallback() {
    if (data_bus_) {
        // 注册为数据总线的客户端
        std::string client_id = "websocket_server";
        data_bus_->RegisterClient(client_id, "websocket", 
            [this](const MarketDataWrapper& data) {
                return HandleDataBusMessage(data);
            });
        
        logger_->info("Registered WebSocket server as data bus client");
    }
}

void WebSocketServer::UnregisterDataBusCallback() {
    if (data_bus_) {
        std::string client_id = "websocket_server";
        data_bus_->UnregisterClient(client_id);
        
        logger_->info("Unregistered WebSocket server from data bus");
    }
}

// WebSocketServerFactory实现
std::unique_ptr<WebSocketServer> WebSocketServerFactory::CreateDefault(uint16_t port) {
    WebSocketConfig config;
    config.port = port;
    return std::make_unique<WebSocketServer>(config);
}

std::unique_ptr<WebSocketServer> WebSocketServerFactory::CreateHighPerformance(uint16_t port) {
    WebSocketConfig config;
    config.port = port;
    config.max_connections = 2000;
    config.thread_pool_size = 8;
    config.enable_compression = true;
    config.enable_batching = true;
    config.batch_size = 200;
    config.batch_timeout_ms = 5;
    config.send_buffer_size = 128 * 1024;
    config.receive_buffer_size = 128 * 1024;
    config.enable_tcp_nodelay = true;
    
    return std::make_unique<WebSocketServer>(config);
}

std::unique_ptr<WebSocketServer> WebSocketServerFactory::CreateLowLatency(uint16_t port) {
    WebSocketConfig config;
    config.port = port;
    config.max_connections = 500;
    config.thread_pool_size = 4;
    config.enable_compression = false;  // 禁用压缩以减少延迟
    config.enable_batching = false;     // 禁用批处理以减少延迟
    config.send_buffer_size = 32 * 1024;
    config.receive_buffer_size = 32 * 1024;
    config.enable_tcp_nodelay = true;
    config.heartbeat_interval_ms = 10000;  // 更频繁的心跳
    
    return std::make_unique<WebSocketServer>(config);
}

std::unique_ptr<WebSocketServer> WebSocketServerFactory::CreateFromConfig(const std::string& config_file) {
    try {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            throw std::runtime_error("Cannot open config file: " + config_file);
        }
        
        nlohmann::json config_json;
        file >> config_json;
        
        WebSocketConfig config;
        
        // 解析配置
        if (config_json.contains("host")) {
            config.host = config_json["host"];
        }
        if (config_json.contains("port")) {
            config.port = config_json["port"];
        }
        if (config_json.contains("max_connections")) {
            config.max_connections = config_json["max_connections"];
        }
        if (config_json.contains("thread_pool_size")) {
            config.thread_pool_size = config_json["thread_pool_size"];
        }
        if (config_json.contains("enable_compression")) {
            config.enable_compression = config_json["enable_compression"];
        }
        if (config_json.contains("enable_batching")) {
            config.enable_batching = config_json["enable_batching"];
        }
        if (config_json.contains("batch_size")) {
            config.batch_size = config_json["batch_size"];
        }
        if (config_json.contains("batch_timeout_ms")) {
            config.batch_timeout_ms = config_json["batch_timeout_ms"];
        }
        if (config_json.contains("heartbeat_interval_ms")) {
            config.heartbeat_interval_ms = config_json["heartbeat_interval_ms"];
        }
        if (config_json.contains("heartbeat_timeout_ms")) {
            config.heartbeat_timeout_ms = config_json["heartbeat_timeout_ms"];
        }
        
        return std::make_unique<WebSocketServer>(config);
        
    } catch (const std::exception& e) {
        throw std::runtime_error("Failed to create WebSocket server from config file: " + std::string(e.what()));
    }
}

std::unique_ptr<WebSocketServer> WebSocketServerFactory::CreateFromConfig(const WebSocketConfig& config) {
    return std::make_unique<WebSocketServer>(config);
}

} // namespace interfaces
} // namespace financial_data