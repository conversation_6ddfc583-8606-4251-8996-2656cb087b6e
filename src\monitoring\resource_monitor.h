#pragma once

#include <atomic>
#include <thread>
#include <chrono>
#include <memory>
#include <mutex>
#include <string>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#include <pdh.h>
#else
#include <sys/resource.h>
#include <sys/statvfs.h>
#include <unistd.h>
#endif

namespace monitoring {

class AlertManager;

struct ResourceUsage {
    double cpu_percentage;
    double memory_percentage;
    double disk_percentage;
    double network_bytes_per_second;
    uint64_t memory_used_bytes;
    uint64_t memory_total_bytes;
    uint64_t disk_used_bytes;
    uint64_t disk_total_bytes;
    std::chrono::high_resolution_clock::time_point timestamp;
};

class ResourceMonitor {
public:
    explicit ResourceMonitor(std::shared_ptr<AlertManager> alert_manager);
    ~ResourceMonitor();
    
    // Start/stop monitoring
    bool start();
    void stop();
    
    // Configuration
    void setMonitoringInterval(std::chrono::seconds interval) {
        monitoring_interval_ = interval;
    }
    void setCpuThreshold(double threshold) {
        cpu_threshold_ = threshold;
    }
    void setMemoryThreshold(double threshold) {
        memory_threshold_ = threshold;
    }
    void setDiskThreshold(double threshold) {
        disk_threshold_ = threshold;
    }
    void setAlertCooldown(std::chrono::seconds cooldown) {
        alert_cooldown_ = cooldown;
    }
    
    // Get current resource usage
    ResourceUsage getCurrentUsage();
    
    // Statistics
    ResourceUsage getAverageUsage() const;
    ResourceUsage getPeakUsage() const;
    void resetStatistics();
    
private:
    std::shared_ptr<AlertManager> alert_manager_;
    
    // Configuration
    std::chrono::seconds monitoring_interval_{5}; // 5 seconds
    std::atomic<double> cpu_threshold_{85.0}; // 85%
    std::atomic<double> memory_threshold_{85.0}; // 85%
    std::atomic<double> disk_threshold_{85.0}; // 85%
    std::chrono::seconds alert_cooldown_{300}; // 5 minutes between alerts
    
    // Threading
    std::atomic<bool> running_{false};
    std::thread monitoring_thread_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    ResourceUsage average_usage_{};
    ResourceUsage peak_usage_{};
    uint64_t sample_count_{0};
    
    // Alert state
    std::chrono::steady_clock::time_point last_cpu_alert_;
    std::chrono::steady_clock::time_point last_memory_alert_;
    std::chrono::steady_clock::time_point last_disk_alert_;
    std::mutex alert_mutex_;
    
#ifdef _WIN32
    // Windows-specific handles
    PDH_HQUERY cpu_query_;
    PDH_HCOUNTER cpu_counter_;
    bool pdh_initialized_;
#else
    // Linux-specific state
    struct CpuTimes {
        uint64_t user, nice, system, idle, iowait, irq, softirq;
    };
    CpuTimes last_cpu_times_;
    bool cpu_times_initialized_;
#endif
    
    // Monitoring methods
    void monitoringLoop();
    void processResourceUsage(const ResourceUsage& usage);
    void updateStatistics(const ResourceUsage& usage);
    void checkThresholds(const ResourceUsage& usage);
    
    // Platform-specific resource collection
    double getCpuUsage();
    double getMemoryUsage(uint64_t& used_bytes, uint64_t& total_bytes);
    double getDiskUsage(uint64_t& used_bytes, uint64_t& total_bytes);
    double getNetworkBandwidth();
    
    // Alert methods
    void sendResourceAlert(const std::string& resource_type, double current_value, double threshold);
    bool shouldSendAlert(std::chrono::steady_clock::time_point& last_alert_time);
    
    // Utility methods
    void initializePlatformSpecific();
    void cleanupPlatformSpecific();
    
#ifdef _WIN32
    bool initializeWindowsCounters();
    void cleanupWindowsCounters();
#else
    bool readCpuTimes(CpuTimes& times);
    double calculateCpuUsage(const CpuTimes& current, const CpuTimes& previous);
#endif
};

} // namespace monitoring