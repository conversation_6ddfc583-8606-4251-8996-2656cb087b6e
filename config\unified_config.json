{"version": "1.0.0", "environment": "development", "server": {"host": "0.0.0.0", "port": 8080, "threads": 4, "max_connections": 1000, "keep_alive_timeout": 60, "request_timeout": 30}, "logging": {"level": "info", "file": "logs/financial_service.log", "max_file_size_mb": 100, "max_files": 10, "console": true, "format": "[%Y-%m-%d %H:%M:%S] [%l] %v"}, "collection": {"pytdx": {"enabled": true, "servers": [{"host": "**************", "port": 7709}, {"host": "************", "port": 7709}], "batch_size": 1000, "concurrent_requests": 5, "timeout_seconds": 30, "retry_attempts": 3, "retry_delay_seconds": 5, "archive_enabled": true, "archive_batch_size": 5000, "symbols": ["all"], "data_types": ["tick", "kline", "level2"]}, "ctp": {"enabled": true, "config_path": "config/ctp_config.json", "failover_timeout": 30, "reconnect_interval": 5, "max_reconnect_attempts": 10, "heartbeat_interval": 30}, "coordination": {"priority_source": "ctp", "overlap_tolerance_seconds": 300, "enable_data_merge": true, "conflict_resolution": "timestamp_priority", "data_validation": true}}, "storage": {"hot_storage": {"type": "redis", "retention_days": 7, "config": {"host": "127.0.0.1", "port": 6379, "database": 0, "password": "", "pool_size": 10, "timeout_ms": 5000, "max_memory": "2gb", "eviction_policy": "allkeys-lru"}}, "warm_storage": {"type": "clickhouse", "retention_days": 730, "config": {"host": "127.0.0.1", "port": 9000, "database": "market_data", "username": "admin", "password": "${CLICKHOUSE_PASSWORD}", "max_connections": 20, "connection_timeout": 10, "query_timeout": 60, "compression": true, "partition_by": "toYYYYMM(timestamp)"}}, "cold_storage": {"type": "s3", "config": {"bucket": "market-data-archive", "region": "us-east-1", "access_key": "${AWS_ACCESS_KEY}", "secret_key": "${AWS_SECRET_KEY}", "endpoint": "", "compression": "gzip", "encryption": "AES256"}}, "migration": {"enabled": true, "batch_size": 10000, "parallel_workers": 4, "check_interval_hours": 1, "hot_to_warm_threshold_hours": 168, "warm_to_cold_threshold_days": 730}, "strategy": {"selection_strategy": "time_based", "enable_automatic_failover": true, "enable_load_balancing": false, "health_check_interval_seconds": 30, "health_check_timeout_seconds": 5, "max_consecutive_failures": 3, "failover_cooldown_seconds": 60, "max_failover_attempts": 2, "load_balance_threshold": 0.8, "thresholds": {"hot_storage_days": 7, "warm_storage_days": 730, "max_response_time_ms": 1000.0, "min_success_rate": 0.9, "health_threshold_success_rate": 0.95, "degraded_threshold_success_rate": 0.8}, "data_type_configs": {"tick": {"hot_storage_days": 7, "warm_storage_days": 365, "priority_storage": "hot", "compression_enabled": false, "batch_size": 1000, "max_response_time_ms": 100.0}, "kline": {"hot_storage_days": 30, "warm_storage_days": 1095, "priority_storage": "warm", "compression_enabled": true, "batch_size": 5000, "max_response_time_ms": 500.0}, "level2": {"hot_storage_days": 3, "warm_storage_days": 180, "priority_storage": "hot", "compression_enabled": false, "batch_size": 500, "max_response_time_ms": 50.0}, "fundamental": {"hot_storage_days": 90, "warm_storage_days": 2190, "priority_storage": "warm", "compression_enabled": true, "batch_size": 10000, "max_response_time_ms": 2000.0}}, "migration_policies": {"tick": {"hot_to_warm_hours": 168, "warm_to_cold_days": 365, "auto_migration": true, "migration_batch_size": 50000, "migration_schedule": "0 2 * * *"}, "kline": {"hot_to_warm_hours": 720, "warm_to_cold_days": 1095, "auto_migration": true, "migration_batch_size": 100000, "migration_schedule": "0 3 * * *"}, "level2": {"hot_to_warm_hours": 72, "warm_to_cold_days": 180, "auto_migration": true, "migration_batch_size": 25000, "migration_schedule": "0 1 * * *"}, "fundamental": {"hot_to_warm_hours": 2160, "warm_to_cold_days": 2190, "auto_migration": true, "migration_batch_size": 200000, "migration_schedule": "0 4 * * 0"}}}}, "scheduling": {"historical_update": {"enabled": true, "cron": "0 2 * * *", "symbols": ["all"], "lookback_days": 1, "max_parallel_tasks": 5, "timeout_minutes": 60}, "data_migration": {"enabled": true, "cron": "0 3 * * *", "batch_size": 10000, "max_parallel_workers": 4}, "data_cleanup": {"enabled": true, "cron": "0 4 * * 0", "retention_policy": {"hot_storage_days": 7, "warm_storage_days": 730, "cold_storage_years": 10}}, "health_check": {"enabled": true, "cron": "*/5 * * * *", "timeout_seconds": 30, "alert_on_failure": true}}, "monitoring": {"enable_metrics": true, "prometheus": {"bind_address": "0.0.0.0:9090", "metrics_path": "/metrics", "scrape_interval": "15s"}, "alert_thresholds": {"data_delay_seconds": 60, "error_rate_percent": 5.0, "cpu_threshold_percent": 85.0, "memory_threshold_percent": 85.0, "disk_threshold_percent": 90.0, "connection_failure_rate": 10.0}, "alerting": {"enabled": true, "channels": {"console": {"enabled": true, "level": "warn"}, "email": {"enabled": false, "smtp_server": "smtp.example.com", "port": 587, "username": "${EMAIL_USERNAME}", "password": "${EMAIL_PASSWORD}", "recipients": ["<EMAIL>"], "subject_prefix": "[MarketData Alert]"}, "webhook": {"enabled": false, "url": "${WEBHOOK_URL}", "timeout_seconds": 10, "retry_attempts": 3}}, "rate_limiting": {"max_alerts_per_minute": 10, "cooldown_minutes": 5}}, "health_check": {"interval_seconds": 30, "timeout_seconds": 10, "endpoints": ["/health", "/metrics", "/status"]}}, "performance": {"max_concurrent_clients": 1000, "worker_threads": 8, "io_threads": 4, "buffer_size": 65536, "batch_timeout_ms": 100, "enable_compression": true, "compression_level": 6, "memory_pool": {"initial_size_mb": 100, "max_size_mb": 1000, "growth_factor": 1.5}, "cache": {"enabled": true, "max_size_mb": 500, "ttl_seconds": 300, "cleanup_interval_seconds": 60}}, "security": {"enable_authentication": false, "enable_encryption": false, "api_keys": {"enabled": false, "header_name": "X-API-Key", "keys": []}, "rate_limiting": {"enabled": true, "requests_per_minute": 1000, "burst_size": 100, "cleanup_interval_seconds": 60}, "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST"], "allowed_headers": ["Content-Type", "Authorization"]}}, "data_quality": {"validation": {"enabled": true, "strict_mode": false, "price_range_check": true, "volume_range_check": true, "timestamp_check": true, "duplicate_check": true}, "repair": {"enabled": true, "auto_repair": false, "missing_data_interpolation": true, "outlier_detection": true, "outlier_threshold": 3.0}, "reporting": {"enabled": true, "report_interval_hours": 24, "quality_threshold": 95.0, "alert_on_low_quality": true}}, "features": {"hot_reload": true, "version_management": true, "environment_variables": true, "config_validation": true, "backup_on_change": true, "audit_logging": true}}