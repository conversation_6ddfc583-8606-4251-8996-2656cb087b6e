#pragma once

#include "cold_storage.hpp"
#include <chrono>
#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <thread>
#include <future>

namespace financial_data {
namespace storage {

struct MigrationPolicy {
    int warm_to_cold_days = 730;  // 2年后迁移到冷存储
    int retention_years = 10;     // 保留10年
    std::string cron_schedule = "0 2 * * *";  // 每天凌晨2点执行
    bool enable_s3_backup = true;
    bool enable_compression = true;
    int compression_level = 9;
    size_t batch_size = 100000;
};

struct MigrationTask {
    std::string task_id;
    std::string symbol;
    std::string exchange;
    std::chrono::system_clock::time_point start_date;
    std::chrono::system_clock::time_point end_date;
    std::string source_storage;  // "warm" or "hot"
    std::string target_storage;  // "cold"
    size_t estimated_records;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point scheduled_at;
    
    enum Status {
        PENDING,
        RUNNING,
        COMPLETED,
        FAILED,
        CANCELLED
    } status = PENDING;
    
    std::string error_message;
    double progress = 0.0;
    size_t processed_records = 0;
};

struct MigrationStats {
    size_t total_tasks = 0;
    size_t completed_tasks = 0;
    size_t failed_tasks = 0;
    size_t pending_tasks = 0;
    size_t running_tasks = 0;
    
    size_t total_migrated_records = 0;
    size_t total_migrated_size = 0;
    double average_compression_ratio = 0.0;
    
    std::chrono::system_clock::time_point last_migration_time;
    std::chrono::milliseconds average_migration_time{0};
};

class LifecycleManager {
public:
    explicit LifecycleManager(std::shared_ptr<ColdDataStorage> cold_storage);
    ~LifecycleManager();
    
    // 初始化生命周期管理器
    bool Initialize(const MigrationPolicy& policy);
    
    // 启动/停止自动迁移
    void StartAutomaticMigration();
    void StopAutomaticMigration();
    bool IsRunning() const { return running_; }
    
    // 手动触发迁移
    std::future<bool> TriggerMigration();
    std::future<bool> TriggerMigration(const std::string& symbol, const std::string& exchange);
    
    // 任务管理
    std::string ScheduleMigrationTask(const std::string& symbol,
                                     const std::string& exchange,
                                     const std::chrono::system_clock::time_point& start_date,
                                     const std::chrono::system_clock::time_point& end_date);
    
    bool CancelMigrationTask(const std::string& task_id);
    MigrationTask GetMigrationTask(const std::string& task_id);
    std::vector<MigrationTask> GetMigrationTasks(MigrationTask::Status status = MigrationTask::PENDING);
    
    // 统计信息
    MigrationStats GetMigrationStats() const;
    
    // 策略管理
    void UpdateMigrationPolicy(const MigrationPolicy& policy);
    MigrationPolicy GetMigrationPolicy() const { return policy_; }
    
    // 数据清理
    std::future<bool> CleanupExpiredData();
    std::future<size_t> GetExpiredDataSize();
    
    // 存储空间优化
    std::future<bool> OptimizeStorage();
    std::future<bool> DefragmentStorage();
    
    // 健康检查
    struct HealthStatus {
        bool is_healthy = true;
        std::string status_message;
        size_t pending_tasks = 0;
        size_t failed_tasks = 0;
        std::chrono::system_clock::time_point last_check_time;
        double storage_utilization = 0.0;
        bool s3_backup_healthy = true;
    };
    
    HealthStatus CheckHealth();

private:
    std::shared_ptr<ColdDataStorage> cold_storage_;
    MigrationPolicy policy_;
    std::atomic<bool> running_{false};
    std::thread migration_thread_;
    
    // 任务管理
    mutable std::mutex tasks_mutex_;
    std::vector<MigrationTask> migration_tasks_;
    std::atomic<size_t> task_counter_{0};
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    MigrationStats stats_;
    
    // 工作线程
    void MigrationWorker();
    void ProcessPendingTasks();
    bool ExecuteMigrationTask(MigrationTask& task);
    
    // 数据查询接口（需要连接ClickHouse）
    struct DataRange {
        std::string symbol;
        std::string exchange;
        std::chrono::system_clock::time_point start_time;
        std::chrono::system_clock::time_point end_time;
        size_t record_count;
    };
    
    std::vector<DataRange> FindDataToMigrate();
    std::vector<DataRange> FindExpiredData();
    
    // 任务持久化
    bool SaveTaskState();
    bool LoadTaskState();
    
    // 通知系统
    void NotifyTaskCompleted(const MigrationTask& task);
    void NotifyTaskFailed(const MigrationTask& task);
    
    // Cron调度器
    class CronScheduler;
    std::unique_ptr<CronScheduler> scheduler_;
    
    // 性能监控
    void UpdateStats(const MigrationTask& task);
    void LogMigrationMetrics(const MigrationTask& task, 
                           std::chrono::milliseconds duration);
};

// 存储空间分析器
class StorageAnalyzer {
public:
    explicit StorageAnalyzer(std::shared_ptr<ColdDataStorage> cold_storage);
    
    struct StorageReport {
        struct LayerInfo {
            std::string layer_name;
            size_t total_size = 0;
            size_t file_count = 0;
            size_t record_count = 0;
            double utilization = 0.0;
            std::chrono::system_clock::time_point oldest_data;
            std::chrono::system_clock::time_point newest_data;
        };
        
        LayerInfo hot_storage;
        LayerInfo warm_storage;
        LayerInfo cold_storage;
        
        size_t total_size = 0;
        double compression_ratio = 0.0;
        size_t duplicate_data_size = 0;
        
        std::vector<std::string> recommendations;
        std::chrono::system_clock::time_point generated_at;
    };
    
    std::future<StorageReport> GenerateReport();
    std::future<std::vector<std::string>> FindDuplicateData();
    std::future<size_t> EstimateMigrationSize(int days_threshold);
    
private:
    std::shared_ptr<ColdDataStorage> cold_storage_;
    
    StorageReport::LayerInfo AnalyzeLayer(const std::string& layer_name);
    std::vector<std::string> GenerateRecommendations(const StorageReport& report);
};

// 数据完整性验证器
class DataIntegrityValidator {
public:
    explicit DataIntegrityValidator(std::shared_ptr<ColdDataStorage> cold_storage);
    
    struct ValidationResult {
        bool is_valid = true;
        std::string file_path;
        std::string error_message;
        std::string expected_checksum;
        std::string actual_checksum;
        size_t expected_records = 0;
        size_t actual_records = 0;
        std::chrono::system_clock::time_point validated_at;
    };
    
    std::future<ValidationResult> ValidateFile(const std::string& file_path);
    std::future<std::vector<ValidationResult>> ValidateAllFiles();
    std::future<bool> RepairCorruptedFile(const std::string& file_path);
    
private:
    std::shared_ptr<ColdDataStorage> cold_storage_;
    
    bool ValidateChecksum(const std::string& file_path, const std::string& expected_checksum);
    bool ValidateRecordCount(const std::string& file_path, size_t expected_count);
    bool ValidateDataConsistency(const std::string& file_path);
};

} // namespace storage
} // namespace financial_data