# Financial Data SDK for C++

A high-performance C++ SDK for accessing real-time and historical financial market data with microsecond-level latency.

## Features

- **Ultra-Low Latency**: Additional latency overhead < 5 microseconds
- **High Throughput**: Supports 1M+ messages per second
- **Dual API**: Both synchronous and asynchronous interfaces
- **Connection Management**: Advanced connection pooling and automatic reconnection
- **Error Handling**: Comprehensive error handling with circuit breaker pattern
- **Resource Optimization**: Memory-efficient design with connection pooling
- **Multi-Protocol Support**: gRPC streaming and REST APIs
- **Thread-Safe**: Full thread safety for concurrent operations

## Requirements

- C++17 or later
- CMake 3.16+
- gRPC 1.30+
- Protocol Buffers 3.12+
- vcpkg (recommended for dependency management)

## Installation

### Using vcpkg (Recommended)

```bash
# Install dependencies
vcpkg install grpc protobuf

# Build the SDK
mkdir build && cd build
cmake .. -DCMAKE_TOOLCHAIN_FILE=[vcpkg root]/scripts/buildsystems/vcpkg.cmake
cmake --build . --config Release
```

### Manual Installation

```bash
# Install gRPC and protobuf manually
# Then build
mkdir build && cd build
cmake ..
make -j$(nproc)
```

## Quick Start

### Basic Usage

```cpp
#include "financial_data_sdk.h"
using namespace financial_data::sdk;

int main() {
    // Configure connection
    ConnectionConfig config;
    config.server_address = "localhost:50051";
    config.enable_compression = true;
    
    // Create SDK instance
    FinancialDataSDK sdk(config);
    
    // Connect to server
    if (!sdk.Connect()) {
        std::cerr << "Failed to connect" << std::endl;
        return 1;
    }
    
    // Get latest market data
    std::vector<std::string> symbols = {"CU2409", "AL2409"};
    auto ticks = sdk.GetLatestTicks(symbols, "SHFE");
    
    for (const auto& tick : ticks) {
        std::cout << "Symbol: " << tick.symbol 
                  << ", Price: " << tick.last_price 
                  << ", Volume: " << tick.volume << std::endl;
    }
    
    sdk.Disconnect();
    return 0;
}
```

### Streaming Data

```cpp
#include "financial_data_sdk.h"
using namespace financial_data::sdk;

int main() {
    FinancialDataSDK sdk;
    
    if (!sdk.Connect()) {
        return 1;
    }
    
    // Set up streaming callback
    auto callback = [](const StandardTick& tick) {
        std::cout << "Received: " << tick.symbol 
                  << " @ " << tick.last_price << std::endl;
    };
    
    // Configure subscription
    SubscriptionConfig config;
    config.symbols = {"CU2409", "AL2409", "ZN2409"};
    config.exchange = "SHFE";
    config.include_level2 = true;
    
    // Start streaming
    sdk.SubscribeTickData(config, callback);
    
    // Keep running
    std::this_thread::sleep_for(std::chrono::minutes(5));
    
    sdk.UnsubscribeAll();
    sdk.Disconnect();
    return 0;
}
```

### Asynchronous Operations

```cpp
#include "financial_data_sdk.h"
using namespace financial_data::sdk;

int main() {
    FinancialDataSDK sdk;
    sdk.Connect();
    
    // Start multiple async requests
    std::vector<std::future<std::vector<StandardTick>>> futures;
    
    std::vector<std::string> symbols = {"CU2409", "AL2409", "ZN2409"};
    for (const auto& symbol : symbols) {
        auto future = sdk.GetLatestTicksAsync({symbol}, "SHFE");
        futures.push_back(std::move(future));
    }
    
    // Wait for all results
    for (auto& future : futures) {
        try {
            auto ticks = future.get();
            std::cout << "Received " << ticks.size() << " ticks" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Error: " << e.what() << std::endl;
        }
    }
    
    sdk.Disconnect();
    return 0;
}
```

## API Reference

### Core Classes

#### FinancialDataSDK

Main SDK interface for accessing financial data.

**Constructor:**
```cpp
explicit FinancialDataSDK(const ConnectionConfig& config = ConnectionConfig{});
```

**Connection Management:**
```cpp
bool Connect();                          // Connect to server
void Disconnect();                       // Disconnect from server
bool IsConnected() const;               // Check connection status
ConnectionStatus GetConnectionStatus() const;
```

**Synchronous API:**
```cpp
std::vector<StandardTick> GetLatestTicks(
    const std::vector<std::string>& symbols, 
    const std::string& exchange = "");

std::vector<StandardTick> GetHistoricalTicks(
    const std::string& symbol,
    const std::string& exchange,
    int64_t start_timestamp,
    int64_t end_timestamp,
    int limit = 1000);

std::vector<Level2Data> GetLevel2Data(
    const std::vector<std::string>& symbols,
    const std::string& exchange = "",
    int depth = 5);
```

**Asynchronous API:**
```cpp
std::future<std::vector<StandardTick>> GetLatestTicksAsync(...);
std::future<std::vector<StandardTick>> GetHistoricalTicksAsync(...);
```

**Streaming API:**
```cpp
bool SubscribeTickData(const SubscriptionConfig& config, 
                      TickDataCallback callback);
bool SubscribeLevel2Data(const SubscriptionConfig& config,
                        Level2DataCallback callback);
bool UnsubscribeTickData(const std::vector<std::string>& symbols);
bool UnsubscribeLevel2Data(const std::vector<std::string>& symbols);
void UnsubscribeAll();
```

**Callbacks:**
```cpp
void SetErrorCallback(ErrorCallback callback);
void SetConnectionStatusCallback(ConnectionStatusCallback callback);
```

**Monitoring:**
```cpp
Statistics GetStatistics() const;
void ResetStatistics();
bool HealthCheck();
```

### Configuration Classes

#### ConnectionConfig

```cpp
struct ConnectionConfig {
    std::string server_address = "localhost:50051";
    std::string auth_token;
    std::chrono::milliseconds connect_timeout{5000};
    std::chrono::milliseconds request_timeout{10000};
    int max_retry_attempts = 3;
    std::chrono::milliseconds retry_interval{1000};
    bool enable_compression = true;
    bool enable_keepalive = true;
    std::chrono::seconds keepalive_time{30};
    std::chrono::seconds keepalive_timeout{5};
    int max_receive_message_size = 64 * 1024 * 1024;
    int max_send_message_size = 16 * 1024 * 1024;
};
```

#### SubscriptionConfig

```cpp
struct SubscriptionConfig {
    std::vector<std::string> symbols;
    std::string exchange;
    bool include_level2 = false;
    int buffer_size = 1000;
    std::chrono::milliseconds heartbeat_interval{10000};
};
```

### Data Structures

#### StandardTick

```cpp
struct StandardTick {
    int64_t timestamp_ns;           // Nanosecond timestamp
    std::string symbol;             // Contract symbol
    std::string exchange;           // Exchange code
    double last_price;              // Last trade price
    uint64_t volume;                // Trade volume
    double turnover;                // Trade turnover
    uint64_t open_interest;         // Open interest
    uint32_t sequence;              // Sequence number
    std::string trade_flag;         // Trade flag
    std::array<PriceLevel, 5> bids; // Bid levels
    std::array<PriceLevel, 5> asks; // Ask levels
    
    bool IsValid() const;
    void SetCurrentTimestamp();
    static int64_t GetCurrentTimestampNs();
};
```

#### Level2Data

```cpp
struct Level2Data {
    int64_t timestamp_ns;
    std::string symbol;
    std::string exchange;
    std::vector<PriceLevel> bids;   // Up to 10 levels
    std::vector<PriceLevel> asks;   // Up to 10 levels
    uint32_t sequence;
    
    bool IsValid() const;
    void SetCurrentTimestamp();
};
```

#### PriceLevel

```cpp
struct PriceLevel {
    double price;
    uint32_t volume;
    uint32_t order_count;
    
    PriceLevel() = default;
    PriceLevel(double p, uint32_t v, uint32_t oc = 0);
};
```

### Error Handling

#### ErrorCode

```cpp
enum class ErrorCode {
    SUCCESS = 0,
    CONNECTION_FAILED,
    AUTHENTICATION_FAILED,
    SUBSCRIPTION_FAILED,
    TIMEOUT,
    INVALID_PARAMETER,
    NETWORK_ERROR,
    SERVER_ERROR,
    UNKNOWN_ERROR
};
```

#### ErrorInfo

```cpp
struct ErrorInfo {
    ErrorCode code;
    std::string message;
    std::chrono::system_clock::time_point timestamp;
};
```

### Callback Types

```cpp
using TickDataCallback = std::function<void(const StandardTick&)>;
using Level2DataCallback = std::function<void(const Level2Data&)>;
using ErrorCallback = std::function<void(const ErrorInfo&)>;
using ConnectionStatusCallback = std::function<void(bool connected)>;
```

## Performance Optimization

### Connection Pooling

The SDK automatically manages a pool of gRPC connections for optimal performance:

```cpp
ConnectionConfig config;
config.max_receive_message_size = 64 * 1024 * 1024;  // 64MB
config.max_send_message_size = 16 * 1024 * 1024;     // 16MB
config.enable_keepalive = true;
config.keepalive_time = std::chrono::seconds(30);
```

### Memory Management

- Zero-copy operations where possible
- Pre-allocated buffers for high-frequency data
- Efficient data structures with minimal allocations

### Latency Optimization

- Disable compression for ultra-low latency: `config.enable_compression = false`
- Use dedicated threads for streaming operations
- Minimize callback processing time

## Error Handling and Resilience

### Automatic Retry

```cpp
ConnectionConfig config;
config.max_retry_attempts = 3;
config.retry_interval = std::chrono::milliseconds(1000);
```

### Circuit Breaker

The SDK includes a circuit breaker pattern to prevent cascading failures:

- **Closed**: Normal operation
- **Open**: Failing fast after threshold breaches
- **Half-Open**: Testing service recovery

### Error Callbacks

```cpp
sdk.SetErrorCallback([](const ErrorInfo& error) {
    std::cerr << "Error [" << static_cast<int>(error.code) << "]: " 
              << error.message << std::endl;
    
    // Handle specific error types
    switch (error.code) {
        case ErrorCode::CONNECTION_FAILED:
            // Implement reconnection logic
            break;
        case ErrorCode::TIMEOUT:
            // Handle timeout
            break;
        // ... other cases
    }
});
```

## Monitoring and Statistics

### Performance Metrics

```cpp
auto stats = sdk.GetStatistics();
std::cout << "Messages received: " << stats.messages_received << std::endl;
std::cout << "Average latency: " << stats.avg_latency.count() << " μs" << std::endl;
std::cout << "Max latency: " << stats.max_latency.count() << " μs" << std::endl;
std::cout << "Connection count: " << stats.connection_count << std::endl;
```

### Health Monitoring

```cpp
// Check server health
if (sdk.HealthCheck()) {
    std::cout << "Server is healthy" << std::endl;
} else {
    std::cout << "Server health check failed" << std::endl;
}

// Monitor connection status
sdk.SetConnectionStatusCallback([](bool connected) {
    std::cout << "Connection status: " 
              << (connected ? "Connected" : "Disconnected") << std::endl;
});
```

## Examples

See the `examples/` directory for complete working examples:

- `basic_usage.cpp` - Basic synchronous operations
- `streaming_example.cpp` - Real-time data streaming
- `async_example.cpp` - Asynchronous operations
- `performance_benchmark.cpp` - Performance testing

## Testing

### Build and Run Tests

```bash
cd build
make unit_tests integration_tests performance_tests
./unit_tests
./integration_tests
./performance_tests
```

### Performance Requirements

The SDK is designed to meet the following performance requirements:

- **Latency**: Additional overhead < 5 microseconds
- **Throughput**: Support 1M+ messages per second
- **Connections**: Handle 1000+ concurrent connections
- **Reliability**: 99.99% uptime with automatic failover

## Best Practices

### Connection Management

```cpp
// Use connection pooling for high-frequency operations
ConnectionConfig config;
config.enable_keepalive = true;
config.keepalive_time = std::chrono::seconds(30);

// Set appropriate timeouts
config.connect_timeout = std::chrono::milliseconds(5000);
config.request_timeout = std::chrono::milliseconds(10000);
```

### Memory Optimization

```cpp
// Pre-allocate containers for known data sizes
std::vector<StandardTick> ticks;
ticks.reserve(1000);  // Reserve space for expected data

// Use move semantics where possible
auto future = sdk.GetLatestTicksAsync(std::move(symbols), exchange);
```

### Error Handling

```cpp
// Always handle exceptions in production code
try {
    auto ticks = sdk.GetLatestTicks(symbols, "SHFE");
    // Process ticks...
} catch (const std::exception& e) {
    // Log error and implement fallback logic
    std::cerr << "Failed to get ticks: " << e.what() << std::endl;
}
```

### Threading

```cpp
// Use dedicated threads for streaming callbacks
std::thread processing_thread([&sdk]() {
    // Set up streaming with minimal callback processing
    auto callback = [](const StandardTick& tick) {
        // Minimal processing in callback
        tick_queue.push(tick);  // Thread-safe queue
    };
    
    sdk.SubscribeTickData(config, callback);
});
```

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check server address and port
   - Verify network connectivity
   - Increase connection timeout

2. **High Latency**
   - Disable compression for ultra-low latency
   - Check network conditions
   - Optimize callback processing

3. **Memory Usage**
   - Monitor statistics regularly
   - Use appropriate buffer sizes
   - Implement proper cleanup

### Debug Mode

```cpp
// Enable debug logging (if implemented)
ConnectionConfig config;
config.debug_mode = true;
config.log_level = LogLevel::DEBUG;
```

## License

This SDK is part of the Financial Data Service System. See the main project license for details.

## Support

For technical support and questions:
- Check the examples and tests for usage patterns
- Review the API documentation
- Contact the development team for enterprise support