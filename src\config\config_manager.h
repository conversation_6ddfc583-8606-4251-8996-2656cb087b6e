#pragma once

#include <string>
#include <memory>
#include <functional>
#include <map>
#include <vector>
#include <mutex>
#include <chrono>
#include <atomic>
#include <thread>
#include <nlohmann/json.hpp>

namespace config {

// 配置变更事件类型
enum class ConfigChangeType {
    ADDED,
    MODIFIED,
    DELETED,
    RELOADED
};

// 配置变更事件
struct ConfigChangeEvent {
    ConfigChangeType type;
    std::string section;
    std::string key;
    nlohmann::json old_value;
    nlohmann::json new_value;
    std::chrono::system_clock::time_point timestamp;
};

// 配置验证结果
struct ValidationResult {
    bool is_valid = true;
    std::vector<std::string> errors;
    std::vector<std::string> warnings;
    
    void AddError(const std::string& error) {
        is_valid = false;
        errors.push_back(error);
    }
    
    void AddWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
};

// 配置版本信息
struct ConfigVersion {
    std::string version_id;
    std::chrono::system_clock::time_point timestamp;
    std::string description;
    std::string checksum;
    nlohmann::json config_snapshot;
};

// 配置变更监听器接口
class ConfigChangeListener {
public:
    virtual ~ConfigChangeListener() = default;
    virtual void OnConfigChanged(const ConfigChangeEvent& event) = 0;
};

// 配置验证器接口
class ConfigValidator {
public:
    virtual ~ConfigValidator() = default;
    virtual ValidationResult Validate(const nlohmann::json& config) const = 0;
    virtual std::string GetValidatorName() const = 0;
};

// 统一配置管理器
class ConfigManager {
public:
    static ConfigManager& Instance();
    
    // 初始化和生命周期管理
    bool Initialize(const std::string& config_file_path);
    void Shutdown();
    
    // 配置加载和保存
    bool LoadConfig();
    bool SaveConfig();
    bool LoadFromFile(const std::string& file_path);
    bool SaveToFile(const std::string& file_path);
    
    // 配置访问接口
    template<typename T>
    T GetValue(const std::string& key, const T& default_value = T{}) const;
    
    template<typename T>
    bool SetValue(const std::string& key, const T& value);
    
    bool HasKey(const std::string& key) const;
    bool RemoveKey(const std::string& key);
    
    // 配置节操作
    nlohmann::json GetSection(const std::string& section) const;
    bool SetSection(const std::string& section, const nlohmann::json& config);
    bool RemoveSection(const std::string& section);
    std::vector<std::string> GetSectionNames() const;
    
    // 配置验证
    ValidationResult ValidateConfig() const;
    ValidationResult ValidateSection(const std::string& section) const;
    bool RegisterValidator(const std::string& section, 
                          std::shared_ptr<ConfigValidator> validator);
    void UnregisterValidator(const std::string& section);
    
    // 热更新功能
    void EnableHotReload(bool enable = true);
    bool IsHotReloadEnabled() const;
    void SetFileWatchInterval(std::chrono::milliseconds interval);
    
    // 变更监听
    void RegisterChangeListener(std::shared_ptr<ConfigChangeListener> listener);
    void UnregisterChangeListener(std::shared_ptr<ConfigChangeListener> listener);
    
    // 版本管理
    std::string CreateSnapshot(const std::string& description = "");
    bool RestoreFromSnapshot(const std::string& version_id);
    std::vector<ConfigVersion> GetVersionHistory() const;
    bool DeleteSnapshot(const std::string& version_id);
    void SetMaxVersionHistory(size_t max_versions);
    
    // 配置合并
    bool MergeConfig(const nlohmann::json& other_config, bool overwrite = true);
    bool MergeFromFile(const std::string& file_path, bool overwrite = true);
    
    // 环境变量支持
    void EnableEnvironmentVariables(bool enable = true);
    void SetEnvironmentPrefix(const std::string& prefix);
    
    // 配置导出
    std::string ExportToString(bool pretty_print = true) const;
    bool ExportToFile(const std::string& file_path, bool pretty_print = true) const;
    
    // 统计信息
    struct Statistics {
        size_t total_keys;
        size_t total_sections;
        std::chrono::system_clock::time_point last_loaded;
        std::chrono::system_clock::time_point last_modified;
        size_t change_count;
        size_t validation_count;
        size_t error_count;
    };
    
    Statistics GetStatistics() const;
    
private:
    ConfigManager() = default;
    ~ConfigManager() = default;
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    // 内部方法
    void NotifyListeners(const ConfigChangeEvent& event);
    void StartFileWatcher();
    void StopFileWatcher();
    void FileWatcherLoop();
    std::string CalculateChecksum(const nlohmann::json& config) const;
    std::string GenerateVersionId() const;
    void CleanupOldVersions();
    std::string ResolveEnvironmentVariables(const std::string& value) const;
    nlohmann::json ProcessEnvironmentVariables(const nlohmann::json& config) const;
    
    // 成员变量
    mutable std::shared_mutex config_mutex_;
    nlohmann::json config_;
    std::string config_file_path_;
    
    // 验证器
    std::map<std::string, std::shared_ptr<ConfigValidator>> validators_;
    mutable std::mutex validators_mutex_;
    
    // 监听器
    std::vector<std::shared_ptr<ConfigChangeListener>> listeners_;
    mutable std::mutex listeners_mutex_;
    
    // 热更新
    std::atomic<bool> hot_reload_enabled_{false};
    std::atomic<bool> file_watcher_running_{false};
    std::thread file_watcher_thread_;
    std::chrono::milliseconds watch_interval_{1000};
    std::chrono::system_clock::time_point last_file_time_;
    
    // 版本管理
    std::vector<ConfigVersion> version_history_;
    mutable std::mutex version_mutex_;
    size_t max_version_history_{10};
    
    // 环境变量支持
    std::atomic<bool> env_vars_enabled_{false};
    std::string env_prefix_{"MARKET_DATA_"};
    
    // 统计信息
    mutable Statistics stats_{};
    mutable std::mutex stats_mutex_;
};

// 模板方法实现
template<typename T>
T ConfigManager::GetValue(const std::string& key, const T& default_value) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    
    try {
        // 支持嵌套键访问，如 "redis.host"
        nlohmann::json current = config_;
        std::istringstream iss(key);
        std::string token;
        
        while (std::getline(iss, token, '.')) {
            if (current.contains(token)) {
                current = current[token];
            } else {
                return default_value;
            }
        }
        
        if constexpr (std::is_same_v<T, std::string>) {
            std::string value = current.get<std::string>();
            return ResolveEnvironmentVariables(value);
        } else {
            return current.get<T>();
        }
    } catch (const std::exception&) {
        return default_value;
    }
}

template<typename T>
bool ConfigManager::SetValue(const std::string& key, const T& value) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    try {
        nlohmann::json old_value;
        nlohmann::json* current = &config_;
        std::istringstream iss(key);
        std::string token;
        std::vector<std::string> path_tokens;
        
        // 解析路径
        while (std::getline(iss, token, '.')) {
            path_tokens.push_back(token);
        }
        
        // 导航到父节点
        for (size_t i = 0; i < path_tokens.size() - 1; ++i) {
            if (!current->contains(path_tokens[i])) {
                (*current)[path_tokens[i]] = nlohmann::json::object();
            }
            current = &(*current)[path_tokens[i]];
        }
        
        // 保存旧值
        const std::string& final_key = path_tokens.back();
        if (current->contains(final_key)) {
            old_value = (*current)[final_key];
        }
        
        // 设置新值
        (*current)[final_key] = value;
        
        // 更新统计信息
        {
            std::lock_guard<std::mutex> stats_lock(stats_mutex_);
            stats_.change_count++;
            stats_.last_modified = std::chrono::system_clock::now();
        }
        
        // 通知监听器
        ConfigChangeEvent event;
        event.type = old_value.is_null() ? ConfigChangeType::ADDED : ConfigChangeType::MODIFIED;
        event.key = key;
        event.old_value = old_value;
        event.new_value = value;
        event.timestamp = std::chrono::system_clock::now();
        
        lock.unlock();
        NotifyListeners(event);
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

} // namespace config