#!/usr/bin/env python3
"""
Python performance tests for financial data service
Complements the C++ performance test suite
"""

import asyncio
import time
import statistics
import json
import sys
import argparse
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
import queue
import random
import websockets
import requests
import grpc
from datetime import datetime, timedelta

@dataclass
class PythonTestResult:
    test_name: str
    success: bool
    duration_seconds: float
    metrics: Dict[str, Any]
    error_message: Optional[str] = None

class PythonPerformanceTests:
    """Python-based performance tests for additional validation"""
    
    def __init__(self):
        self.results: List[PythonTestResult] = []
        self.websocket_url = "ws://localhost:8080/market-data"
        self.rest_api_base = "http://localhost:8000/api/v1"
        self.grpc_endpoint = "localhost:50051"
    
    async def run_all_tests(self) -> List[PythonTestResult]:
        """Run all Python performance tests"""
        print("=== Python Performance Test Suite ===")
        
        # Run WebSocket stress test
        await self.test_websocket_stress()
        
        # Run REST API load test
        await self.test_rest_api_load()
        
        # Run concurrent connection test
        await self.test_concurrent_connections()
        
        # Run data integrity validation
        await self.test_data_integrity_validation()
        
        # Run memory leak detection
        await self.test_memory_leak_detection()
        
        return self.results
    
    async def test_websocket_stress(self):
        """Test WebSocket under stress conditions"""
        print("  Running WebSocket stress test...")
        
        start_time = time.time()
        num_clients = 100
        test_duration = 30  # seconds
        
        try:
            # Create multiple WebSocket connections
            clients = []
            message_counts = []
            
            async def websocket_client(client_id: int):
                message_count = 0
                try:
                    async with websockets.connect(self.websocket_url) as websocket:
                        # Subscribe to symbols
                        subscribe_msg = {
                            "action": "subscribe",
                            "symbols": ["CU2409", "AL2409", "ZN2409"]
                        }
                        await websocket.send(json.dumps(subscribe_msg))
                        
                        # Receive messages for test duration
                        end_time = time.time() + test_duration
                        while time.time() < end_time:
                            try:
                                message = await asyncio.wait_for(
                                    websocket.recv(), timeout=1.0
                                )
                                message_count += 1
                            except asyncio.TimeoutError:
                                continue
                            except websockets.exceptions.ConnectionClosed:
                                break
                        
                        return message_count
                        
                except Exception as e:
                    print(f"    Client {client_id} error: {e}")
                    return 0
            
            # Run clients concurrently
            tasks = [websocket_client(i) for i in range(num_clients)]
            message_counts = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out exceptions and calculate metrics
            valid_counts = [count for count in message_counts if isinstance(count, int)]
            
            total_messages = sum(valid_counts)
            avg_messages_per_client = statistics.mean(valid_counts) if valid_counts else 0
            successful_clients = len(valid_counts)
            
            duration = time.time() - start_time
            
            metrics = {
                "total_clients": num_clients,
                "successful_clients": successful_clients,
                "success_rate": successful_clients / num_clients,
                "total_messages_received": total_messages,
                "avg_messages_per_client": avg_messages_per_client,
                "messages_per_second": total_messages / test_duration,
                "test_duration": test_duration
            }
            
            success = successful_clients >= num_clients * 0.95  # 95% success rate
            
            self.results.append(PythonTestResult(
                test_name="WebSocket Stress Test",
                success=success,
                duration_seconds=duration,
                metrics=metrics
            ))
            
            print(f"    Successful clients: {successful_clients}/{num_clients}")
            print(f"    Total messages: {total_messages}")
            print(f"    Messages/sec: {total_messages / test_duration:.0f}")
            
        except Exception as e:
            self.results.append(PythonTestResult(
                test_name="WebSocket Stress Test",
                success=False,
                duration_seconds=time.time() - start_time,
                metrics={},
                error_message=str(e)
            ))
    
    async def test_rest_api_load(self):
        """Test REST API under load"""
        print("  Running REST API load test...")
        
        start_time = time.time()
        num_threads = 20
        requests_per_thread = 100
        
        try:
            endpoints = [
                "/ticks/latest/CU2409",
                "/klines/CU2409?period=1m&limit=100",
                "/depth/CU2409",
                "/ticks/CU2409?start=1721446200&end=1721449800&limit=50"
            ]
            
            response_times = []
            successful_requests = 0
            failed_requests = 0
            
            def make_requests(thread_id: int):
                nonlocal successful_requests, failed_requests
                thread_response_times = []
                
                for i in range(requests_per_thread):
                    endpoint = random.choice(endpoints)
                    url = f"{self.rest_api_base}{endpoint}"
                    
                    req_start = time.time()
                    try:
                        response = requests.get(url, timeout=5.0)
                        req_end = time.time()
                        
                        response_time = (req_end - req_start) * 1000  # Convert to ms
                        thread_response_times.append(response_time)
                        
                        if response.status_code == 200:
                            successful_requests += 1
                        else:
                            failed_requests += 1
                            
                    except Exception as e:
                        failed_requests += 1
                        print(f"    Request failed: {e}")
                
                return thread_response_times
            
            # Run requests in parallel
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(make_requests, i) for i in range(num_threads)]
                
                for future in as_completed(futures):
                    thread_times = future.result()
                    response_times.extend(thread_times)
            
            duration = time.time() - start_time
            total_requests = successful_requests + failed_requests
            
            metrics = {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "success_rate": successful_requests / total_requests if total_requests > 0 else 0,
                "requests_per_second": total_requests / duration,
                "avg_response_time_ms": statistics.mean(response_times) if response_times else 0,
                "p95_response_time_ms": statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else 0,
                "p99_response_time_ms": statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else 0
            }
            
            success = (successful_requests / total_requests) >= 0.95 if total_requests > 0 else False
            
            self.results.append(PythonTestResult(
                test_name="REST API Load Test",
                success=success,
                duration_seconds=duration,
                metrics=metrics
            ))
            
            print(f"    Successful requests: {successful_requests}/{total_requests}")
            print(f"    Requests/sec: {total_requests / duration:.0f}")
            print(f"    Avg response time: {metrics['avg_response_time_ms']:.1f}ms")
            
        except Exception as e:
            self.results.append(PythonTestResult(
                test_name="REST API Load Test",
                success=False,
                duration_seconds=time.time() - start_time,
                metrics={},
                error_message=str(e)
            ))
    
    async def test_concurrent_connections(self):
        """Test system behavior with many concurrent connections"""
        print("  Running concurrent connections test...")
        
        start_time = time.time()
        target_connections = 500
        
        try:
            connection_results = []
            
            async def test_connection(conn_id: int):
                try:
                    # Test WebSocket connection
                    async with websockets.connect(self.websocket_url) as websocket:
                        await websocket.send(json.dumps({
                            "action": "subscribe",
                            "symbols": ["CU2409"]
                        }))
                        
                        # Receive a few messages
                        for _ in range(5):
                            try:
                                await asyncio.wait_for(websocket.recv(), timeout=2.0)
                            except asyncio.TimeoutError:
                                break
                        
                        return True
                        
                except Exception:
                    return False
            
            # Create connections in batches to avoid overwhelming the system
            batch_size = 50
            successful_connections = 0
            
            for batch_start in range(0, target_connections, batch_size):
                batch_end = min(batch_start + batch_size, target_connections)
                batch_tasks = [
                    test_connection(i) for i in range(batch_start, batch_end)
                ]
                
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                successful_connections += sum(1 for result in batch_results if result is True)
                
                # Small delay between batches
                await asyncio.sleep(0.1)
            
            duration = time.time() - start_time
            success_rate = successful_connections / target_connections
            
            metrics = {
                "target_connections": target_connections,
                "successful_connections": successful_connections,
                "success_rate": success_rate,
                "connection_time_seconds": duration
            }
            
            success = success_rate >= 0.90  # 90% success rate for concurrent connections
            
            self.results.append(PythonTestResult(
                test_name="Concurrent Connections Test",
                success=success,
                duration_seconds=duration,
                metrics=metrics
            ))
            
            print(f"    Successful connections: {successful_connections}/{target_connections}")
            print(f"    Success rate: {success_rate * 100:.1f}%")
            
        except Exception as e:
            self.results.append(PythonTestResult(
                test_name="Concurrent Connections Test",
                success=False,
                duration_seconds=time.time() - start_time,
                metrics={},
                error_message=str(e)
            ))
    
    async def test_data_integrity_validation(self):
        """Validate data integrity across different interfaces"""
        print("  Running data integrity validation...")
        
        start_time = time.time()
        
        try:
            # Get data from different sources and compare
            symbol = "CU2409"
            
            # Get latest tick from REST API
            rest_response = requests.get(f"{self.rest_api_base}/ticks/latest/{symbol}")
            rest_data = rest_response.json() if rest_response.status_code == 200 else None
            
            # Get data via WebSocket
            websocket_data = None
            try:
                async with websockets.connect(self.websocket_url) as websocket:
                    await websocket.send(json.dumps({
                        "action": "subscribe",
                        "symbols": [symbol]
                    }))
                    
                    # Wait for a message
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    websocket_data = json.loads(message)
                    
            except (asyncio.TimeoutError, websockets.exceptions.ConnectionClosed):
                pass
            
            # Compare data consistency
            data_consistent = False
            if rest_data and websocket_data:
                # Check if key fields match (allowing for small time differences)
                price_match = abs(rest_data.get('last_price', 0) - websocket_data.get('last_price', 0)) < 0.01
                symbol_match = rest_data.get('symbol') == websocket_data.get('symbol')
                data_consistent = price_match and symbol_match
            
            duration = time.time() - start_time
            
            metrics = {
                "rest_data_available": rest_data is not None,
                "websocket_data_available": websocket_data is not None,
                "data_consistent": data_consistent,
                "rest_data": rest_data,
                "websocket_data": websocket_data
            }
            
            success = data_consistent or (rest_data is None and websocket_data is None)
            
            self.results.append(PythonTestResult(
                test_name="Data Integrity Validation",
                success=success,
                duration_seconds=duration,
                metrics=metrics
            ))
            
            print(f"    Data consistency: {'✓' if data_consistent else '✗'}")
            
        except Exception as e:
            self.results.append(PythonTestResult(
                test_name="Data Integrity Validation",
                success=False,
                duration_seconds=time.time() - start_time,
                metrics={},
                error_message=str(e)
            ))
    
    async def test_memory_leak_detection(self):
        """Test for memory leaks during sustained operation"""
        print("  Running memory leak detection...")
        
        start_time = time.time()
        test_duration = 60  # 1 minute test
        
        try:
            import psutil
            import os
            
            # Monitor system memory during sustained load
            memory_samples = []
            
            async def memory_monitor():
                process = psutil.Process(os.getpid())
                end_time = time.time() + test_duration
                
                while time.time() < end_time:
                    memory_info = process.memory_info()
                    memory_samples.append(memory_info.rss / 1024 / 1024)  # MB
                    await asyncio.sleep(1)
            
            async def sustained_load():
                # Create sustained load with WebSocket connections
                connections = []
                try:
                    for i in range(20):  # 20 concurrent connections
                        async with websockets.connect(self.websocket_url) as websocket:
                            await websocket.send(json.dumps({
                                "action": "subscribe",
                                "symbols": ["CU2409", "AL2409", "ZN2409"]
                            }))
                            connections.append(websocket)
                            
                            # Keep receiving messages
                            end_time = time.time() + test_duration
                            while time.time() < end_time:
                                try:
                                    await asyncio.wait_for(websocket.recv(), timeout=1.0)
                                except (asyncio.TimeoutError, websockets.exceptions.ConnectionClosed):
                                    break
                                    
                except Exception:
                    pass  # Ignore connection errors for this test
            
            # Run memory monitoring and load generation concurrently
            await asyncio.gather(
                memory_monitor(),
                sustained_load(),
                return_exceptions=True
            )
            
            duration = time.time() - start_time
            
            # Analyze memory usage
            if len(memory_samples) >= 2:
                initial_memory = statistics.mean(memory_samples[:5])
                final_memory = statistics.mean(memory_samples[-5:])
                memory_growth = final_memory - initial_memory
                max_memory = max(memory_samples)
                
                # Consider it a leak if memory grows by more than 50MB
                memory_leak_detected = memory_growth > 50.0
            else:
                initial_memory = final_memory = max_memory = memory_growth = 0
                memory_leak_detected = False
            
            metrics = {
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "max_memory_mb": max_memory,
                "memory_growth_mb": memory_growth,
                "memory_leak_detected": memory_leak_detected,
                "sample_count": len(memory_samples)
            }
            
            success = not memory_leak_detected
            
            self.results.append(PythonTestResult(
                test_name="Memory Leak Detection",
                success=success,
                duration_seconds=duration,
                metrics=metrics
            ))
            
            print(f"    Memory growth: {memory_growth:.1f} MB")
            print(f"    Memory leak detected: {'Yes' if memory_leak_detected else 'No'}")
            
        except ImportError:
            print("    Skipping memory leak test (psutil not available)")
            self.results.append(PythonTestResult(
                test_name="Memory Leak Detection",
                success=True,  # Skip test
                duration_seconds=time.time() - start_time,
                metrics={"skipped": True, "reason": "psutil not available"}
            ))
        except Exception as e:
            self.results.append(PythonTestResult(
                test_name="Memory Leak Detection",
                success=False,
                duration_seconds=time.time() - start_time,
                metrics={},
                error_message=str(e)
            ))
    
    def print_summary(self):
        """Print test results summary"""
        print("\n=== Python Performance Test Results ===")
        
        passed_tests = sum(1 for result in self.results if result.success)
        total_tests = len(self.results)
        
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success rate: {passed_tests / total_tests * 100:.1f}%")
        
        print("\nDetailed Results:")
        for result in self.results:
            status = "✓ PASS" if result.success else "✗ FAIL"
            print(f"  {result.test_name}: {status} ({result.duration_seconds:.1f}s)")
            
            if not result.success and result.error_message:
                print(f"    Error: {result.error_message}")
        
        return passed_tests == total_tests
    
    def export_results(self, filename: str):
        """Export results to JSON file"""
        results_data = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": len(self.results),
            "passed_tests": sum(1 for r in self.results if r.success),
            "results": [
                {
                    "test_name": r.test_name,
                    "success": r.success,
                    "duration_seconds": r.duration_seconds,
                    "metrics": r.metrics,
                    "error_message": r.error_message
                }
                for r in self.results
            ]
        }
        
        with open(filename, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        print(f"Results exported to {filename}")

async def main():
    parser = argparse.ArgumentParser(description="Python Performance Tests for Financial Data Service")
    parser.add_argument("--export", help="Export results to JSON file")
    parser.add_argument("--websocket-url", default="ws://localhost:8080/market-data", 
                       help="WebSocket server URL")
    parser.add_argument("--rest-api-base", default="http://localhost:8000/api/v1",
                       help="REST API base URL")
    
    args = parser.parse_args()
    
    # Create test runner
    test_runner = PythonPerformanceTests()
    test_runner.websocket_url = args.websocket_url
    test_runner.rest_api_base = args.rest_api_base
    
    # Run tests
    results = await test_runner.run_all_tests()
    
    # Print summary
    all_passed = test_runner.print_summary()
    
    # Export results if requested
    if args.export:
        test_runner.export_results(args.export)
    
    # Exit with appropriate code
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    asyncio.run(main())