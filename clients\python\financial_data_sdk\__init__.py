"""
Financial Data Service Python SDK

A comprehensive Python SDK for accessing real-time and historical financial market data
with support for pandas/numpy data formats, async operations, and technical indicators.
"""

from .client import FinancialDataClient
from .async_client import AsyncFinancialDataClient
from .data_models import TickData, KlineData, Level2Data
from .indicators import TechnicalIndicators
from .cache import DataCache
from .utils import DataConverter

__version__ = "1.0.0"
__author__ = "Financial Data Service Team"

__all__ = [
    "FinancialDataClient",
    "AsyncFinancialDataClient", 
    "TickData",
    "KlineData",
    "Level2Data",
    "TechnicalIndicators",
    "DataCache",
    "DataConverter"
]