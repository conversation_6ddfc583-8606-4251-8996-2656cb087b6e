#pragma once

#include <memory>
#include <string>
#include <vector>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <functional>
#include <unordered_map>
#include <spdlog/spdlog.h>

namespace financial_data {

enum class NodeRole {
    PRIMARY,
    SECONDARY,
    UNKNOWN
};

enum class NodeStatus {
    HEALTHY,
    UNHEALTHY,
    DISCONNECTED,
    UNKNOWN
};

struct NodeInfo {
    std::string node_id;
    std::string address;
    int port;
    NodeRole role;
    NodeStatus status;
    std::chrono::steady_clock::time_point last_heartbeat;
    int priority;
    
    NodeInfo() : role(NodeRole::UNKNOWN), status(NodeStatus::UNKNOWN), priority(0) {}
};

struct FailoverConfig {
    int heartbeat_interval_ms = 1000;      // 心跳间隔
    int heartbeat_timeout_ms = 5000;       // 心跳超时
    int failover_timeout_ms = 5000;        // 故障转移超时
    int max_failover_attempts = 3;         // 最大故障转移尝试次数
    bool enable_auto_failback = true;      // 启用自动故障恢复
    int failback_delay_ms = 30000;         // 故障恢复延迟
    std::string cluster_name = "financial-data-cluster";
    std::string local_node_id;
    std::vector<std::string> peer_nodes;   // 对等节点列表
};

class FailoverManager {
public:
    using FailoverCallback = std::function<void(NodeRole old_role, NodeRole new_role)>;
    using HealthCheckCallback = std::function<bool()>;
    using DataSyncCallback = std::function<bool(const std::string& peer_node)>;

    explicit FailoverManager(const FailoverConfig& config);
    ~FailoverManager();

    // 启动故障转移管理器
    bool Start();
    
    // 停止故障转移管理器
    void Stop();
    
    // 获取当前节点角色
    NodeRole GetCurrentRole() const { return current_role_.load(); }
    
    // 获取当前节点状态
    NodeStatus GetCurrentStatus() const { return current_status_.load(); }
    
    // 获取集群状态
    std::vector<NodeInfo> GetClusterStatus() const;
    
    // 手动触发故障转移
    bool TriggerFailover(const std::string& reason = "Manual failover");
    
    // 设置回调函数
    void SetFailoverCallback(FailoverCallback callback) { failover_callback_ = callback; }
    void SetHealthCheckCallback(HealthCheckCallback callback) { health_check_callback_ = callback; }
    void SetDataSyncCallback(DataSyncCallback callback) { data_sync_callback_ = callback; }
    
    // 更新节点健康状态
    void UpdateNodeHealth(const std::string& node_id, NodeStatus status);
    
    // 获取主节点信息
    NodeInfo GetPrimaryNode() const;
    
    // 检查是否为主节点
    bool IsPrimary() const { return current_role_.load() == NodeRole::PRIMARY; }
    
    // 检查是否为备节点
    bool IsSecondary() const { return current_role_.load() == NodeRole::SECONDARY; }

private:
    // 心跳线程
    void HeartbeatThread();
    
    // 故障检测线程
    void FailureDetectionThread();
    
    // 执行故障转移
    bool ExecuteFailover(const std::string& reason);
    
    // 执行故障恢复
    bool ExecuteFailback();
    
    // 发送心跳
    void SendHeartbeat();
    
    // 处理接收到的心跳
    void HandleHeartbeat(const std::string& node_id, const NodeInfo& info);
    
    // 检查节点健康状态
    bool CheckNodeHealth(const std::string& node_id);
    
    // 选举新的主节点
    std::string ElectNewPrimary();
    
    // 同步数据到对等节点
    bool SyncDataToPeers();
    
    // 通知角色变更
    void NotifyRoleChange(NodeRole old_role, NodeRole new_role);

private:
    FailoverConfig config_;
    std::shared_ptr<spdlog::logger> logger_;
    
    std::atomic<NodeRole> current_role_;
    std::atomic<NodeStatus> current_status_;
    std::atomic<bool> running_;
    std::atomic<bool> failover_in_progress_;
    
    mutable std::mutex nodes_mutex_;
    std::unordered_map<std::string, NodeInfo> cluster_nodes_;
    
    std::thread heartbeat_thread_;
    std::thread failure_detection_thread_;
    
    FailoverCallback failover_callback_;
    HealthCheckCallback health_check_callback_;
    DataSyncCallback data_sync_callback_;
    
    std::chrono::steady_clock::time_point last_failover_time_;
    int failover_attempts_;
};

} // namespace financial_data