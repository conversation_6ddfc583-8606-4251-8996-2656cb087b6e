#include "financial_data_sdk.h"
#include "market_data_service.grpc.pb.h"
#include "market_data_service.pb.h"

#include <grpcpp/grpcpp.h>
#include <grpcpp/create_channel.h>
#include <grpcpp/security/credentials.h>
#include <grpcpp/support/channel_arguments.h>

#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>
#include <chrono>
#include <algorithm>

namespace financial_data {
namespace sdk {

// Connection pool for managing gRPC channels
class ConnectionPool {
public:
    explicit ConnectionPool(const ConnectionConfig& config) : config_(config) {
        CreateChannels();
    }

    std::shared_ptr<grpc::Channel> GetChannel() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (channels_.empty()) {
            CreateChannels();
        }
        
        auto channel = channels_.front();
        channels_.pop();
        channels_.push(channel); // Round-robin
        return channel;
    }

    bool IsHealthy() {
        auto channel = GetChannel();
        return channel && channel->GetState(false) == GRPC_CHANNEL_READY;
    }

private:
    void CreateChannels() {
        grpc::ChannelArguments args;
        args.SetMaxReceiveMessageSize(config_.max_receive_message_size);
        args.SetMaxSendMessageSize(config_.max_send_message_size);
        
        if (config_.enable_keepalive) {
            args.SetInt(GRPC_ARG_KEEPALIVE_TIME_MS, 
                       static_cast<int>(config_.keepalive_time.count() * 1000));
            args.SetInt(GRPC_ARG_KEEPALIVE_TIMEOUT_MS,
                       static_cast<int>(config_.keepalive_timeout.count() * 1000));
            args.SetInt(GRPC_ARG_KEEPALIVE_PERMIT_WITHOUT_CALLS, 1);
        }

        if (config_.enable_compression) {
            args.SetCompressionAlgorithm(GRPC_COMPRESS_GZIP);
        }

        auto credentials = grpc::InsecureChannelCredentials();
        if (!config_.auth_token.empty()) {
            // Add authentication if token is provided
            credentials = grpc::SslCredentials(grpc::SslCredentialsOptions());
        }

        // Create multiple channels for load balancing
        const int num_channels = 4;
        for (int i = 0; i < num_channels; ++i) {
            auto channel = grpc::CreateChannel(config_.server_address, credentials, args);
            channels_.push(channel);
        }
    }

    ConnectionConfig config_;
    std::queue<std::shared_ptr<grpc::Channel>> channels_;
    std::mutex mutex_;
};

// Async client for streaming operations
class AsyncClient {
public:
    explicit AsyncClient(std::shared_ptr<ConnectionPool> pool) 
        : pool_(pool), running_(false) {}

    ~AsyncClient() {
        Stop();
    }

    void Start() {
        if (running_.exchange(true)) {
            return; // Already running
        }
        
        worker_thread_ = std::thread(&AsyncClient::ProcessEvents, this);
    }

    void Stop() {
        if (!running_.exchange(false)) {
            return; // Already stopped
        }
        
        completion_queue_.Shutdown();
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
    }

    template<typename Request, typename Response>
    void StartStream(const Request& request,
                    std::function<void(const Response&)> callback,
                    std::function<void(const grpc::Status&)> error_callback) {
        auto channel = pool_->GetChannel();
        auto stub = MarketDataService::NewStub(channel);
        
        auto context = std::make_unique<grpc::ClientContext>();
        context->set_deadline(std::chrono::system_clock::now() + 
                             std::chrono::hours(24)); // Long-lived stream
        
        // This is a simplified example - actual implementation would need
        // proper template specialization for different stream types
        // For brevity, showing the concept
    }

private:
    void ProcessEvents() {
        void* tag;
        bool ok;
        
        while (completion_queue_.Next(&tag, &ok)) {
            if (!ok) {
                // Handle error
                continue;
            }
            
            // Process the event based on tag
            // Implementation would handle different event types
        }
    }

    std::shared_ptr<ConnectionPool> pool_;
    grpc::CompletionQueue completion_queue_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
};

// Error handler with retry logic
class ErrorHandler {
public:
    explicit ErrorHandler(const ConnectionConfig& config) : config_(config) {}

    template<typename Func>
    auto ExecuteWithRetry(Func&& func) -> decltype(func()) {
        int attempts = 0;
        while (attempts < config_.max_retry_attempts) {
            try {
                return func();
            } catch (const std::exception& e) {
                attempts++;
                if (attempts >= config_.max_retry_attempts) {
                    throw;
                }
                std::this_thread::sleep_for(config_.retry_interval);
            }
        }
        throw std::runtime_error("Max retry attempts exceeded");
    }

    void HandleError(const ErrorInfo& error) {
        std::lock_guard<std::mutex> lock(mutex_);
        error_history_.push_back(error);
        
        // Keep only recent errors
        if (error_history_.size() > 100) {
            error_history_.erase(error_history_.begin());
        }
        
        if (error_callback_) {
            error_callback_(error);
        }
    }

    void SetErrorCallback(ErrorCallback callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        error_callback_ = callback;
    }

private:
    ConnectionConfig config_;
    std::vector<ErrorInfo> error_history_;
    ErrorCallback error_callback_;
    std::mutex mutex_;
};

// Main implementation class
class FinancialDataSDK::Impl {
public:
    explicit Impl(const ConnectionConfig& config)
        : config_(config)
        , connection_pool_(std::make_shared<ConnectionPool>(config))
        , async_client_(std::make_shared<AsyncClient>(connection_pool_))
        , error_handler_(std::make_unique<ErrorHandler>(config))
        , connection_status_(ConnectionStatus::DISCONNECTED) {
        
        ResetStatistics();
    }

    ~Impl() {
        Disconnect();
    }

    bool Connect() {
        if (connection_status_ == ConnectionStatus::CONNECTED) {
            return true;
        }

        connection_status_ = ConnectionStatus::CONNECTING;
        
        try {
            // Test connection with health check
            auto channel = connection_pool_->GetChannel();
            auto stub = MarketDataService::NewStub(channel);
            
            grpc::ClientContext context;
            context.set_deadline(std::chrono::system_clock::now() + config_.connect_timeout);
            
            HealthCheckRequest request;
            request.set_service("MarketDataService");
            HealthCheckResponse response;
            
            auto status = stub->HealthCheck(&context, request, &response);
            
            if (status.ok() && response.status() == HealthCheckResponse::SERVING) {
                connection_status_ = ConnectionStatus::CONNECTED;
                async_client_->Start();
                statistics_.connection_count++;
                
                if (connection_status_callback_) {
                    connection_status_callback_(true);
                }
                return true;
            } else {
                connection_status_ = ConnectionStatus::FAILED;
                error_handler_->HandleError(ErrorInfo(ErrorCode::CONNECTION_FAILED, 
                                                    "Health check failed: " + status.error_message()));
                return false;
            }
        } catch (const std::exception& e) {
            connection_status_ = ConnectionStatus::FAILED;
            error_handler_->HandleError(ErrorInfo(ErrorCode::CONNECTION_FAILED, e.what()));
            return false;
        }
    }

    void Disconnect() {
        if (connection_status_ == ConnectionStatus::DISCONNECTED) {
            return;
        }

        connection_status_ = ConnectionStatus::DISCONNECTED;
        async_client_->Stop();
        
        if (connection_status_callback_) {
            connection_status_callback_(false);
        }
    }

    bool IsConnected() const {
        return connection_status_ == ConnectionStatus::CONNECTED;
    }

    ConnectionStatus GetConnectionStatus() const {
        return connection_status_;
    }

    std::vector<StandardTick> GetLatestTicks(const std::vector<std::string>& symbols, 
                                           const std::string& exchange) {
        if (!IsConnected()) {
            throw std::runtime_error("Not connected to server");
        }

        return error_handler_->ExecuteWithRetry([&]() {
            auto channel = connection_pool_->GetChannel();
            auto stub = MarketDataService::NewStub(channel);
            
            grpc::ClientContext context;
            context.set_deadline(std::chrono::system_clock::now() + config_.request_timeout);
            
            TickDataRequest request;
            for (const auto& symbol : symbols) {
                request.add_symbols(symbol);
            }
            request.set_exchange(exchange);
            request.set_buffer_size(static_cast<int32_t>(symbols.size()));
            
            std::vector<StandardTick> results;
            auto reader = stub->StreamTickData(&context, request);
            
            TickDataResponse response;
            utils::LatencyMeasurer latency_measurer;
            latency_measurer.Start();
            
            while (reader->Read(&response)) {
                for (const auto& tick_proto : response.ticks()) {
                    StandardTick tick;
                    ConvertFromProto(tick_proto, tick);
                    results.push_back(tick);
                }
                
                if (!response.has_more()) {
                    break;
                }
            }
            
            auto latency = latency_measurer.Stop();
            UpdateLatencyStats(latency);
            
            auto status = reader->Finish();
            if (!status.ok()) {
                throw std::runtime_error("gRPC error: " + status.error_message());
            }
            
            statistics_.messages_received += results.size();
            return results;
        });
    }

    std::vector<StandardTick> GetHistoricalTicks(const std::string& symbol,
                                               const std::string& exchange,
                                               int64_t start_timestamp,
                                               int64_t end_timestamp,
                                               int limit) {
        if (!IsConnected()) {
            throw std::runtime_error("Not connected to server");
        }

        return error_handler_->ExecuteWithRetry([&]() {
            auto channel = connection_pool_->GetChannel();
            auto stub = MarketDataService::NewStub(channel);
            
            grpc::ClientContext context;
            context.set_deadline(std::chrono::system_clock::now() + config_.request_timeout);
            
            HistoricalTickDataRequest request;
            request.set_symbol(symbol);
            request.set_exchange(exchange);
            request.set_start_timestamp(start_timestamp);
            request.set_end_timestamp(end_timestamp);
            request.set_limit(limit);
            
            std::vector<StandardTick> results;
            auto reader = stub->GetHistoricalTickData(&context, request);
            
            TickDataResponse response;
            utils::LatencyMeasurer latency_measurer;
            latency_measurer.Start();
            
            while (reader->Read(&response)) {
                for (const auto& tick_proto : response.ticks()) {
                    StandardTick tick;
                    ConvertFromProto(tick_proto, tick);
                    results.push_back(tick);
                }
                
                if (!response.has_more()) {
                    break;
                }
            }
            
            auto latency = latency_measurer.Stop();
            UpdateLatencyStats(latency);
            
            auto status = reader->Finish();
            if (!status.ok()) {
                throw std::runtime_error("gRPC error: " + status.error_message());
            }
            
            statistics_.messages_received += results.size();
            return results;
        });
    }

    bool SubscribeTickData(const SubscriptionConfig& config, TickDataCallback callback) {
        if (!IsConnected()) {
            return false;
        }

        // Store subscription for management
        std::lock_guard<std::mutex> lock(subscription_mutex_);
        for (const auto& symbol : config.symbols) {
            tick_subscriptions_[symbol] = callback;
        }

        // Start streaming in background
        std::thread([this, config, callback]() {
            try {
                auto channel = connection_pool_->GetChannel();
                auto stub = MarketDataService::NewStub(channel);
                
                grpc::ClientContext context;
                
                TickDataRequest request;
                for (const auto& symbol : config.symbols) {
                    request.add_symbols(symbol);
                }
                request.set_exchange(config.exchange);
                request.set_include_level2(config.include_level2);
                request.set_buffer_size(config.buffer_size);
                
                auto reader = stub->StreamTickData(&context, request);
                TickDataResponse response;
                
                while (reader->Read(&response)) {
                    for (const auto& tick_proto : response.ticks()) {
                        StandardTick tick;
                        ConvertFromProto(tick_proto, tick);
                        callback(tick);
                        statistics_.messages_received++;
                    }
                }
                
                auto status = reader->Finish();
                if (!status.ok()) {
                    error_handler_->HandleError(ErrorInfo(ErrorCode::NETWORK_ERROR, 
                                                        "Stream error: " + status.error_message()));
                }
            } catch (const std::exception& e) {
                error_handler_->HandleError(ErrorInfo(ErrorCode::UNKNOWN_ERROR, e.what()));
            }
        }).detach();

        return true;
    }

    void SetErrorCallback(ErrorCallback callback) {
        error_handler_->SetErrorCallback(callback);
    }

    void SetConnectionStatusCallback(ConnectionStatusCallback callback) {
        connection_status_callback_ = callback;
    }

    Statistics GetStatistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return statistics_;
    }

    void ResetStatistics() {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        statistics_ = Statistics{};
        statistics_.last_message_time = std::chrono::system_clock::now();
    }

    bool HealthCheck() {
        try {
            auto channel = connection_pool_->GetChannel();
            auto stub = MarketDataService::NewStub(channel);
            
            grpc::ClientContext context;
            context.set_deadline(std::chrono::system_clock::now() + 
                               std::chrono::seconds(5));
            
            HealthCheckRequest request;
            request.set_service("MarketDataService");
            HealthCheckResponse response;
            
            auto status = stub->HealthCheck(&context, request, &response);
            return status.ok() && response.status() == HealthCheckResponse::SERVING;
        } catch (...) {
            return false;
        }
    }

private:
    void ConvertFromProto(const TickData& proto, StandardTick& tick) {
        tick.timestamp_ns = proto.timestamp();
        tick.symbol = proto.symbol();
        tick.exchange = proto.exchange();
        tick.last_price = proto.last_price();
        tick.volume = proto.volume();
        tick.turnover = proto.turnover();
        tick.open_interest = proto.open_interest();
        tick.sequence = proto.sequence();
        tick.trade_flag = proto.trade_flag();
        
        // Convert bid/ask levels
        size_t bid_count = std::min(static_cast<size_t>(proto.bids_size()), tick.bids.size());
        for (size_t i = 0; i < bid_count; ++i) {
            const auto& bid_proto = proto.bids(static_cast<int>(i));
            tick.bids[i] = PriceLevel(bid_proto.price(), 
                                    static_cast<uint32_t>(bid_proto.volume()),
                                    static_cast<uint32_t>(bid_proto.order_count()));
        }
        
        size_t ask_count = std::min(static_cast<size_t>(proto.asks_size()), tick.asks.size());
        for (size_t i = 0; i < ask_count; ++i) {
            const auto& ask_proto = proto.asks(static_cast<int>(i));
            tick.asks[i] = PriceLevel(ask_proto.price(),
                                    static_cast<uint32_t>(ask_proto.volume()),
                                    static_cast<uint32_t>(ask_proto.order_count()));
        }
    }

    void UpdateLatencyStats(std::chrono::microseconds latency) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        
        if (latency > statistics_.max_latency) {
            statistics_.max_latency = latency;
        }
        
        // Simple moving average for avg_latency
        static constexpr int WINDOW_SIZE = 100;
        static int count = 0;
        static std::chrono::microseconds sum{0};
        
        sum += latency;
        count++;
        
        if (count >= WINDOW_SIZE) {
            statistics_.avg_latency = sum / count;
            sum = std::chrono::microseconds{0};
            count = 0;
        }
    }

    ConnectionConfig config_;
    std::shared_ptr<ConnectionPool> connection_pool_;
    std::shared_ptr<AsyncClient> async_client_;
    std::unique_ptr<ErrorHandler> error_handler_;
    
    std::atomic<ConnectionStatus> connection_status_;
    ConnectionStatusCallback connection_status_callback_;
    
    // Subscription management
    std::unordered_map<std::string, TickDataCallback> tick_subscriptions_;
    std::unordered_map<std::string, Level2DataCallback> level2_subscriptions_;
    std::mutex subscription_mutex_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    Statistics statistics_;
};

// FinancialDataSDK implementation
FinancialDataSDK::FinancialDataSDK(const ConnectionConfig& config)
    : pImpl_(std::make_unique<Impl>(config)) {}

FinancialDataSDK::~FinancialDataSDK() = default;

bool FinancialDataSDK::Connect() {
    return pImpl_->Connect();
}

void FinancialDataSDK::Disconnect() {
    pImpl_->Disconnect();
}

bool FinancialDataSDK::IsConnected() const {
    return pImpl_->IsConnected();
}

ConnectionStatus FinancialDataSDK::GetConnectionStatus() const {
    return pImpl_->GetConnectionStatus();
}

std::vector<StandardTick> FinancialDataSDK::GetLatestTicks(
    const std::vector<std::string>& symbols, 
    const std::string& exchange) {
    return pImpl_->GetLatestTicks(symbols, exchange);
}

std::vector<StandardTick> FinancialDataSDK::GetHistoricalTicks(
    const std::string& symbol,
    const std::string& exchange,
    int64_t start_timestamp,
    int64_t end_timestamp,
    int limit) {
    return pImpl_->GetHistoricalTicks(symbol, exchange, start_timestamp, end_timestamp, limit);
}

std::future<std::vector<StandardTick>> FinancialDataSDK::GetLatestTicksAsync(
    const std::vector<std::string>& symbols, 
    const std::string& exchange) {
    return std::async(std::launch::async, [this, symbols, exchange]() {
        return pImpl_->GetLatestTicks(symbols, exchange);
    });
}

std::future<std::vector<StandardTick>> FinancialDataSDK::GetHistoricalTicksAsync(
    const std::string& symbol,
    const std::string& exchange,
    int64_t start_timestamp,
    int64_t end_timestamp,
    int limit) {
    return std::async(std::launch::async, [this, symbol, exchange, start_timestamp, end_timestamp, limit]() {
        return pImpl_->GetHistoricalTicks(symbol, exchange, start_timestamp, end_timestamp, limit);
    });
}

bool FinancialDataSDK::SubscribeTickData(const SubscriptionConfig& config, 
                                       TickDataCallback callback) {
    return pImpl_->SubscribeTickData(config, callback);
}

void FinancialDataSDK::SetErrorCallback(ErrorCallback callback) {
    pImpl_->SetErrorCallback(callback);
}

void FinancialDataSDK::SetConnectionStatusCallback(ConnectionStatusCallback callback) {
    pImpl_->SetConnectionStatusCallback(callback);
}

FinancialDataSDK::Statistics FinancialDataSDK::GetStatistics() const {
    return pImpl_->GetStatistics();
}

void FinancialDataSDK::ResetStatistics() {
    pImpl_->ResetStatistics();
}

std::vector<Level2Data> FinancialDataSDK::GetLevel2Data(const std::vector<std::string>& symbols,
                                        const std::string& exchange,
                                        int depth) {
    // Implementation would be similar to GetLatestTicks but for Level2Data
    // For brevity, returning empty vector in this example
    return std::vector<Level2Data>();
}

bool FinancialDataSDK::SubscribeLevel2Data(const SubscriptionConfig& config,
                                          Level2DataCallback callback) {
    // Implementation would be similar to SubscribeTickData but for Level2Data
    // For brevity, returning false in this example
    return false;
}

bool FinancialDataSDK::UnsubscribeTickData(const std::vector<std::string>& symbols) {
    // Implementation would remove specific symbols from subscriptions
    return true;
}

bool FinancialDataSDK::UnsubscribeLevel2Data(const std::vector<std::string>& symbols) {
    // Implementation would remove specific symbols from Level2 subscriptions
    return true;
}

void FinancialDataSDK::UnsubscribeAll() {
    // Implementation would clear all subscriptions
}

bool FinancialDataSDK::HealthCheck() {
    return pImpl_->HealthCheck();
}

// Utility functions implementation
namespace utils {

int64_t SystemTimeToNanoseconds(const std::chrono::system_clock::time_point& tp) {
    return std::chrono::duration_cast<std::chrono::nanoseconds>(
        tp.time_since_epoch()).count();
}

std::chrono::system_clock::time_point NanosecondsToSystemTime(int64_t ns) {
    return std::chrono::system_clock::time_point(std::chrono::nanoseconds(ns));
}

bool ValidateSymbol(const std::string& symbol) {
    return !symbol.empty() && symbol.length() <= 32;
}

bool ValidateExchange(const std::string& exchange) {
    return !exchange.empty() && exchange.length() <= 16;
}

bool ValidateTimestamp(int64_t timestamp_ns) {
    auto now = std::chrono::high_resolution_clock::now();
    auto now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
        now.time_since_epoch()).count();
    
    // Allow timestamps within reasonable range (past 10 years to future 1 year)
    int64_t min_timestamp = now_ns - (10LL * 365 * 24 * 3600 * 1000000000LL);
    int64_t max_timestamp = now_ns + (365LL * 24 * 3600 * 1000000000LL);
    
    return timestamp_ns >= min_timestamp && timestamp_ns <= max_timestamp;
}

LatencyMeasurer::LatencyMeasurer() = default;

void LatencyMeasurer::Start() {
    start_time_ = std::chrono::high_resolution_clock::now();
}

std::chrono::microseconds LatencyMeasurer::Stop() {
    auto end_time = std::chrono::high_resolution_clock::now();
    last_latency_ = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - start_time_);
    return last_latency_;
}

std::chrono::microseconds LatencyMeasurer::GetLastLatency() const {
    return last_latency_;
}

} // namespace utils

} // namespace sdk
} // namespace financial_data