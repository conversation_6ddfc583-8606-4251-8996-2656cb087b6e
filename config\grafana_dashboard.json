{"dashboard": {"id": null, "title": "Financial Data Service Monitoring", "tags": ["financial", "monitoring", "real-time"], "timezone": "browser", "panels": [{"id": 1, "title": "End-to-End Latency", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, financial_data_end_to_end_latency_microseconds_bucket)", "legendFormat": "95th Percentile"}, {"expr": "histogram_quantile(0.99, financial_data_end_to_end_latency_microseconds_bucket)", "legendFormat": "99th Percentile"}], "fieldConfig": {"defaults": {"unit": "µs", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 30}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Data Loss Rate", "type": "stat", "targets": [{"expr": "rate(financial_data_loss_total[5m]) / rate(financial_data_received_total[5m]) * 100", "legendFormat": "Loss Rate %"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "System Resource Usage", "type": "timeseries", "targets": [{"expr": "system_cpu_usage_percent", "legendFormat": "CPU Usage %"}, {"expr": "system_memory_usage_percent", "legendFormat": "Memory Usage %"}, {"expr": "system_disk_usage_percent", "legendFormat": "Disk Usage %"}], "fieldConfig": {"defaults": {"unit": "percent", "max": 100, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 85}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Message Throughput", "type": "timeseries", "targets": [{"expr": "rate(financial_data_received_total[1m])", "legendFormat": "Messages/sec - {{symbol}}"}], "fieldConfig": {"defaults": {"unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Active Connections", "type": "timeseries", "targets": [{"expr": "financial_data_connections_active", "legendFormat": "Active Connections"}], "fieldConfig": {"defaults": {"unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Queue Sizes", "type": "timeseries", "targets": [{"expr": "financial_data_queue_size", "legendFormat": "{{queue}} Queue Size"}], "fieldConfig": {"defaults": {"unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 7, "title": "<PERSON><PERSON>", "type": "timeseries", "targets": [{"expr": "rate(financial_data_alerts_total[5m])", "legendFormat": "{{type}} - {{severity}}"}], "fieldConfig": {"defaults": {"unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 8, "title": "Latency Heatmap", "type": "heatmap", "targets": [{"expr": "rate(financial_data_end_to_end_latency_microseconds_bucket[5m])", "legendFormat": "{{le}}"}], "fieldConfig": {"defaults": {"unit": "µs"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}, {"id": 9, "title": "Data Integrity by Symbol", "type": "table", "targets": [{"expr": "financial_data_received_total", "legendFormat": "{{symbol}}", "format": "table"}, {"expr": "financial_data_loss_total", "legendFormat": "{{symbol}}", "format": "table"}], "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Value #A": "Received", "Value #B": "Lost", "symbol": "Symbol"}}}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 27, "version": 1}}